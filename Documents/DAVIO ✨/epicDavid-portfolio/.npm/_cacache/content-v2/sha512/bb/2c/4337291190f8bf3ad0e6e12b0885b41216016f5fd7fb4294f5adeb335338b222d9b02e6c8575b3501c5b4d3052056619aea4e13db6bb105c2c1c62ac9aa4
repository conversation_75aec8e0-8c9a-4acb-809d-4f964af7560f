{"_id": "core-js-compat", "_rev": "150-836c70589c3fb490d137daf41d6a3e07", "name": "core-js-compat", "dist-tags": {"beta": "3.0.0-beta.20", "latest": "3.43.0"}, "versions": {"3.0.0-beta.6": {"name": "core-js-compat", "version": "3.0.0-beta.6", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "705aa7f7fa75dd043d657b6007312b677a052a16", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-g4XsZrXhU5U+9VKFclI2Mf7tgAPQToFADHhMJAlKcfAiTsOrJ/WGVMGbyfagg8ElrWO+6UrrokEuPFzIIsiZag==", "signatures": [{"sig": "MEUCIFbTpMPuZXk0fdNRsLFPCQCH40yN1Du23FXUNF0TFEFuAiEA92PNqMBxO6zOY21AiaL7OLYwp/SPXBj1JXojjOOner0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIVK3CRA9TVsSAnZWagAAJXsP/0VNPiYcmVsiEqTEIcd5\nyyUbtzbQvqFAMXlQHlW3fpMEVf34IBr7Dl4re3PE0CTxWbuK0zonFNVQvKQ8\nJzJbWZdEKGl8bUzdJThwHf3fyjTEdxVB3Wskw84b/PyqcSZisqDNOOo1Ls8H\n0w5aaCTEIpVG129AlCs0tmakywi6wujzf6YQfuH+aaE9iq1TD4HNh+6atOKE\n3S/ezjp0naGI3VN7uAxEmvuPp73OI5UJInCn+dTxt6Bj5UI27Z140h6ImclS\nNnwwpZcqvLJUOb/DaTV4CgXNMcWf7hfWDhUvhPWRM8MMFk9avFMreeVfTjc5\nKle5ZMm142CoMsDr5GWyZr3FNleaR6R2/55YC/Iv6TEN88EE9Ggn7Q+B8eBi\nXdC0euNNucT6siTG6tkpQ8lqnA0w+vctV5oZVsUHwoommy4HJ1mOGcxmH7em\nrpGp3raFoKdONn8E8g+EPasW15iw9auXRTpRyyG5Z5I47sp59JHS9sjNX7xB\nJs4wzp9GCFpYIW9n3OXoclUuOeP8su2WdLZ2fVNs+7esFIJJWMTs3Nm481xU\nnA6FK3oG8aXv5jAc8D7/n8K+ow3xj/VOz0fBO5VMa2j+tF+OJu5a7mAVs2yQ\n7ERysPXpdKjueuGcxRFW3X8ohh1RGStGBguhLUw6DvMmvEEP9YfBqFFvJIQc\nCl4q\r\n=GD9b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.3.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.6_1545687734806_0.48632858662035483", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.7": {"name": "core-js-compat", "version": "3.0.0-beta.7", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a380d6b929f024cd3f9753b3cdf4ffd5d56490ca", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-+xky1ZSNV9/pDOu+mGSB/Zea0pZcfGoSn+RdUIT5zCQIFJ2o5NRzMoHrT3WpchzSBwuhI8WmeJuVni5xz63ABA==", "signatures": [{"sig": "MEYCIQD07LGJbJrpph5fAFeqyl2w7X8XS6hdvYx7GSoFaZMhwgIhAK+Ue8PAx1PeCwGAPWtKBXtizitddnmb9Q7f1ZZry9BQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJCI1CRA9TVsSAnZWagAAO/MP+gOpo+KoZpOqDe5iG5/I\nAzktTpmKgvs+armQD6y5+TL/wsEE2yMgHY9jSuHiS+P8ooKDDPWlnXL9AcBL\nfVaMaK37t3fZx82ATzI2QXaXBq0+U34CaDWXZHPcNgFueOSakpRp5C0+7+gS\nxmeFYeLpP8wtY5RpSSt9TmiNwpRKLQMgIICjvswYrMXRTIkmGKWZim4uOdTP\n2fNoV5c9ZCNrEIcxcveeWPXI/CTae2fJ/rkjOa6GVYBsnor5Mg4498ljyi62\n1Hxq0BjqSPWK4KDeJGmQuISfGp4CgEO2KsiUEbHmSZSDtGeNTsfS+VMI8C3K\nsh7ZdDMNeAuaZShmTuE33jcfujUWtSlQLbrzvfPIqJBf2q4yvMHwsi7tAmaa\nO6YsMCY1/6vmfXKhX4qNrwSosk97Y2zHlmZzJSE/5Z+7cKELGS7rKPiaIre3\n2lv1Sdd/zpclak6nJhXbNNQ0n7hwW45BEdS08+jo4nWHTUoUc2AAPTjULiEs\nSaFdPUpwIz3DmBKXkE8ei2g0L3auMoW/6jHvdxxMXqhvncAyO5EoWyTQHiNQ\nPtU+b9pTiqWtMox9zJUX1B3jWlk1njodM8+2HF8/N1VD+QGKRT4F0jXRYosB\nCoEObqGHI5uwgDJfCUZKpfz0KQ7eIfB2x+NEGv7fXxVMirUYOsfPlqlQhIzd\nvU1M\r\n=x2rN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.3.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.7_1545871923895_0.1531782625684741", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.8": {"name": "core-js-compat", "version": "3.0.0-beta.8", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "1ea69cdc4fdd32d01b7970ec37dcef9ddec78229", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.8.tgz", "fileCount": 4, "integrity": "sha512-WNZWhxq/kG5S83ISkIniLUqGU/7CGeo8nRWItV/huglRGM2+8ZB0e7n+2weHRFeJaFEJvocr+t4Tbw4HJGEfhQ==", "signatures": [{"sig": "MEUCIBmW8jyx3+0ofzisCOCSwn6B7Cp1iCchhepjt8ppdNViAiEAyibaDysMqYwsOX3puHmxuITijM7XZ9yQOd2/IvhqEwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNvYSCRA9TVsSAnZWagAAO/cP/0mwxwxgdBzDdIES1M0c\n+HwEraK6IYMFXtjmZj/lpx4Dy30rV/LdaxJCsaLyleF2OHODpACO2fQWTsMS\nrdY9hkoE44yd5+LhK3WCG2/+5cDpmzbo0cnWQ0ObLDr8DBB3AYY5t6R7/SoA\nUxhVyngpgjD5Oy3Of11nSJVJHVaWikUGsgk35Arfyi9g/7SChgtR0F+ZsFHq\nMTOEBHiSvHZJgoB7WmY9GKTpufAKotiFNVB/UrHRI5poxp9Gcm8CLzezIojP\nclpi6BVnJNWMq1R2i3VboaCHvdWAs2xRmknLO4NHYNmLg4uvjBgdXyQg4YXH\ntg+5el2062z5H4NTjNsKJy2kK37h1/0WCqJ5IRfwAWbsxGlY8d9jnl/BW2dh\nlu7uS6cwskKy+Yjx6mQaj2jACSkDj8Faea9qiD4cx1W3z3JnpATLfw/HFxFQ\nYii8+Mmb5tkPlDpMhUJL0AlZunhO2cVcSl2u2MRyzbSGpq/AWjUwb4ykG1qq\nTLVhfJKG4n96YZ69RIlztTR9m5LMMYEDK9lbSO7WCULG3F6lDUbgTez8/GOV\nPcAZ2PWcd5fkFWdt207UiDDpy9m6kjvTzTpXowbCOG7tHR1Zy5vlh5Xb7a4q\n5/jREkdK6xS6HS6rjHa/sZ/P62uOnzfrdQqUw/3RLU6xDUCzm4AMuY5nKmFY\nqYXp\r\n=SpLW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.3.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.8_1547105809669_0.44593051247422255", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.9": {"name": "core-js-compat", "version": "3.0.0-beta.9", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "cfec65d16ce1df9441d0850ad36c466ddd10d770", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.9.tgz", "fileCount": 4, "integrity": "sha512-kgTmY+ewqC1bB6wXiswrzB3CJvrnCRQoo85kbS37LvZ6XSn3Hz5vSQVzZuWS+LjrBudz6woKOAcAGyZB/BETQQ==", "signatures": [{"sig": "MEQCIGrWx/WWkivvAOJoDgvPHaSRcwbrT7R9Lq6N+uOf3FePAiBhHtudc8N8RPFjMa5kcoVcZ+WrWjX7S16axz8DX9GLtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQFaOCRA9TVsSAnZWagAA1sAP/RH4nX5uepnxUCnIJDKW\nFhKRIpmc9xrBhhTb7wqd3sHrtvTBh9yZS0bBx9wKdd+ILjj2dPmKRENZu10A\nK55r84ZRRZW9vpM2Knjof7QpjG7f9eEFt6ziPP0+f8GvqpLE20ZMwToXNEmm\njKLQxqvDBtbMRnGYxIjhoFRO7kEmUPixaH8uT7icIPHaSGHgW1+jf4FsSJ+V\nIYM3y9SxyJxRng5Vm4fEXXgGyJlC2Ds7oYk/23v4pZQH1uxLiw5K3Zt4ONBA\niXXWhUe2Jnk+EFCL/ukYgJZzDMVY6dclzB8dgG0fD4pJMciZUtrecN1inkiv\n0kzv3nnXeSUxP6rRNde31KG9pLXh12LaWlKM4gPSygYFeNd6rGL/vLlTer1i\ntAKHMOpoatba8j7CY/f06Cgle6la0KuV3JQtXcA9hY8tIYaUpfUa/oQr5/2e\n2qcdxEGBW5onLL4f01dJHQ7sxM4LqytcKgFY+kBi+9G5ZBPFWWfhu9+Q9mXK\nwnZ4z2EynzLEOUKUkfvZ8WhEo+Ia3sVpmvpjhnQq9K2wDkiLiCjJg0IUYwQM\nGxw6fh4uBqsAOU2r8pNu95NdTkx6wwo/SSFefNZkdlWguO1RlznnPh4dpWwd\nIqV8MdQCyc26CC/Ot2FJetFSuA6MR0i2tf+99R+pWAyLkL5C85XlpiQ+IEjN\n1TeD\r\n=yzfx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.9_1547720333534_0.48808802998538825", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.10": {"name": "core-js-compat", "version": "3.0.0-beta.10", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "54a1d451242d723ce18651e55e01089b5149d13b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.10.tgz", "fileCount": 4, "integrity": "sha512-UWA71SqEDbJvslX7THuMs6kQk9ZDmTgYqSAmqZ/+PkGppcRZyNXjrL0L6afcNU18xfmOBW/8uLqz7Drb54Sm1Q==", "signatures": [{"sig": "MEQCIH4DQ4AZmgirCxmMsqQ+jiqPiGITGtdGnZ/0us9bDwC4AiAD+sT9qWc2kx8fxVnxx28tc8sK47DmHlW0F6k5Fw1jzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRnThCRA9TVsSAnZWagAA5ZwP/0otZR3gMHa9tPSoSRkS\nqPihsD85rloC8SoIe9U+WL2dF7Pj8tttHC34zyWaxP8E3a/BJTPKPe5kehDP\nn3d8AKnjuBmCHfY5EpV+xaBqB5vAur36diEDWOSmHffFLdbRgzHZqE8a6yV3\nF9koAv4oGH4l6JZgROy+sfoeWHHB89XyZOeZL/11b+Xx52mtwb7Me9amzmBK\naxLthu/xJfmd0U7jQZsBOT4H5Jp7JWfNVZOVChHvmHQO0Cz3HhNhO9yIegVa\nxN4jqJoG2W/r+BHqp9uA/CdUXqaJcUdsIssz3v9jV9K1UMsrXgcAKgG5Sfsa\nYlcIR1px6pFYwFD3fTucbKiVyr2wcmy1GZjISZkyPnTqw3TDI+a4HeBi6cA8\nhH37VNzqyLEPBxPOkFj9KvkTjuOLk7ldWY9UzTWbrz31FAWRdzMThhwWhtgv\ntzWdufxF9Af3xXUpLwlvjftBABr6uJQtUNBsAqziqb5qQV2qZI+JusUDOVS0\ntb1bdT699AVAGzgN98Mo2kGWnjyepvJ7KKtlccDj9CmrvzVE/x0Aqe89FTYz\nfRNDVN/p9iBKR+nl89tSE5+xkEEpugsH3198WxQZB1mhqbHFoMViqfDwvjvW\nSg8pSzR1VTuD5yQGOUoYihwCoUB1rzAAu6YxHYNcF/ssM0op6frLG6dUgDt3\nRsGf\r\n=w1mE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.6.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"electron-to-chromium": "^1.3.103"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.10_1548121312355_0.16101251856211296", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.11": {"name": "core-js-compat", "version": "3.0.0-beta.11", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fd8c13fda3d48357075640ad9297eb480380700c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.11.tgz", "fileCount": 4, "integrity": "sha512-g9UG5O/FeYXQkdyF0IuqCqDGurTjRpt6KbwBvaisDYJntL/n94UDmcj9FL2P2pt9bqOjtJLBFieWxkoDeshW2Q==", "signatures": [{"sig": "MEYCIQDieWs39JwTuxFDNQE9ivdJB5FyEXK/NaRlHE7ifjQiKAIhAN2L2QO9vcGsEpVwXopNm3S2LDP4LE2ZC6MFUFO9VUCU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTj6nCRA9TVsSAnZWagAAV0wP/R7xecXgRLO5mDFWsKz/\nZbNGI2vXLqHF5JhSP3h41kTmTkTIygaYIumzQc/yr584nCaLEhUOhyTC46eU\nKbbrywpL3V+pmnabCJIZb1RifX/kHNJw9rUYpv9Lm93IFCJzDMm4s8uAih0e\nrVMOlAx2r6ouKX4eRYzAjB++Y7DCWMyRE5qgdvLW3j8R/5gV8iw5MjWn3bVT\nXwG4Rr2t8lODHhd21ZeuSDG2z4/+dLRIMV+8KtFGb7zy6Qts4G1bddJg601v\nzWlG+580/htmMrzOZb2pJoydSQ0s52vqoP1OQwx4bEbXOb8rnxEYDHsdixct\nynH3ZRVkNKc3FxRmbWAYQvfaXHBGF6lxldTsM4lXKPaOJvYZNugsOaezH2XA\nh5LNPFfNsz20LJMB0FB1iwQodCODi+0P4y+JsTkpLavjgmdN+dsAFtd9P8eY\nnihoRMS6W06h4zQFhxfsJziG2oOWdR82vW6dSxcuXlY66Gl4aUa6LwTPp2PT\nEVpI/3JNRxsezAxz3J1xMXohAPJJgOnHd2FROQqBiuklUpcHrXAqt0Fn5uF+\nGaXrDFOsyufsZLqHiSIPHJD5LikRWt9P8iilASb59D5QSOZFIzOvn6cJGDgD\nHRogZzV1mBhTbwXTgVO2qNShweLIgWwXaNfbJMJwEJjUXW55ItW5POdQkCYF\n5l6L\r\n=QuC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"electron-to-chromium": "^1.3.108"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.11_1548631718564_0.2729189493063182", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.12": {"name": "core-js-compat", "version": "3.0.0-beta.12", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b693c4287280c0136345d3835bbe967b2bff4bfc", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.12.tgz", "fileCount": 4, "integrity": "sha512-zEKU5HnV2Wo3dplsWeSfTxzeosa4w1pDqaImT9hnFo+L0wTCF8I0sXJ/wTssgPhpDyhtqlvZ1QZ+vj8l1zfhAQ==", "signatures": [{"sig": "MEUCIHivapznlDO2mqIKTzFMj82ZeK5fX884x7qtFZRrkLiqAiEA1Zy/52V2/t1PegYrPjc+xoaP3ZfKR7MvMUZ7lMJ/4jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVQUWCRA9TVsSAnZWagAAI38P/1deHBOFwPzJTFnvypT9\nNmSnJBV71ckSp7p85J2hibWUc6NDxu8FD7yRRT1qBLZwaok8VY7dR7V5wizR\ny2JQ+2DQG6jGWl1It7VcsoVku36O1zNL7svmhamyIdyT6BiceeJRHHvZPfRE\nkklurGP6MZ7bm+lXVDKWKrxtxoMp3LYH3hE+TwMyMFUB9AeuvQKJUS+rMBRV\nDeTIowU9wET2aCjQcwlYfr/YTMjSXFGuKwgSOoPsrc3spCpMoVCbgPOjcF9G\nL/kR6NU8eXDPY+d7TmFcFF2IneSIHC2p/MUCm1pvzAtoEXFhidsCZPuH0toT\nCkJyQ/qMnEMSXsUeb0HKVCoiqN8Id6LtZV4GNaOlB/pc7E+JfPy5oCHh/Y/m\nkSGSyxvV7QF/Q6GAXblxSZN/uvrhdtEJ4PqxPnBvOVVDqSrLfuv8uluBph0O\n6QVf0NW0TQyxAQqSjizWiA1hiBevLH5Ia7LlaPPr7acZ0sO/IDDuIwz+ZKT7\nDO/FTfYMPi+YcTbdVIZzkoh0pM9NoH8YGa+M7WyO2gALqc9h4YO7aDYTBFx4\nHNglEAdaa6P7kt0US1XedenXE2i+fzSyhWXYLEnBx8aIWGbuum88Nk6SOB+a\n/uAtnFwL8CJcTj3S1Z6tQarhv8bfCT4MbL8PRO7F+0pyeqEBIv9WUjZgjD3F\nNwfC\r\n=b2PS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"electron-to-chromium": "^1.3.111"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.12_1549075734421_0.9212454914015986", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.13": {"name": "core-js-compat", "version": "3.0.0-beta.13", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e5dfb726376ead6f379a1a56e33ab8059af1b436", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.13.tgz", "fileCount": 4, "integrity": "sha512-4XstosACkR8wtyA8hnSCkmgMFVMEDXw6ilN5bO0f3ZHNo540DgX0Jj9tcmIMWM64rFhVH9jzZLi6m1qc8Mm5+w==", "signatures": [{"sig": "MEUCIQCvu5/tWrFYuruawXO8tlmzUgjtIbQzEDTD5nso4mZMmQIgLdNPhb1NzTy8kBsZ6EMP2vI30go8Oa5XPQwJVhw9yPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWA9hCRA9TVsSAnZWagAANJ4QAIQMdPpRjyJze8RxmreR\nPX+q96yPnv1Gg4XBwFm/oII16/glAILrUW8SVvyHwKPE7zmFcHjUXcmH5q19\nCJ3Jj0FQiorHYxVaUb0vRTExR84tAl5JOZIODHFKNneeGdqWi5FflWtRo6aB\nngeaghacbMDXchM5fR4wzhX5mCKFGTAL8N2cc5kd3jKHTupe3PdKXns+sU3T\n2j/AwBaLq2y7hIodD2d21DaLWSW92C0csd+5giq9MR9YgLsvJ910fyVZnbrO\naLwaySBdTbDV90Jc/oQQUOx9a9bVwnARN/KLjiKyDx97cRpWTon8AoBFPBTT\neNdg8BfNtMVAWLlmDdpsU2FxaB20NU49KAeq5CayQxzS9NVk4z3lPYXDPGdL\n1S0AYi0VeJkAzLalWEMxP+R+XR06iKhGxBZ6nCAks8uJne3y3R3NkEOdl0Bg\njgwam8LigefRpzJF+EYJpP1sUqm3nVeUufAwLhdNxKeKgvrQnN2CRFe4jVoK\nwKE5YQYTzx9cXis44QIDOeWhKjQObaJ4gAVnH/6C5VP0928Mo/CDAV9Fdlx0\nSnnAnf0sxNU4P+gO+T5VJoOoQND7NTJAeVzCkVDKpL4JJuVy3/QIMGdBb+Zo\nF8AwI9QZssPyF1oi8CyohwFHu0Y4pEALvBS1+DI/TC7QF2YxxR17NEVELO+T\ngJyO\r\n=Z771\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"electron-to-chromium": "^1.3.111"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.13_1549274976565_0.7369195069928163", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.14": {"name": "core-js-compat", "version": "3.0.0-beta.14", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7df0a62ead3ac8172cb67d699b371f749568765a", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.14.tgz", "fileCount": 4, "integrity": "sha512-Wa91HgOcLMgh5ZQp+yw786H/vMc+3n4057PvgBxkrjU1+/SBB72s3FoNGXItn1mw02G5LFfwMWj5+RG5HvEkWw==", "signatures": [{"sig": "MEQCIF8PfF1GFHlAco/0rfHw4C35kYaAIxjdbh26okDFVyMcAiAdZe8erPMjO8t+FTVV8pGmdQCc24x84/E5/0qOAvEsLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcW+HaCRA9TVsSAnZWagAAdhkQAIqMFYYaeu67FM4PAmb0\nFcmQuHAVXBfyCUZvwEGy0JUhzefAwcutFfm1FUhGfYv6Z+toOjEtlzjSf8R0\noooFB1zeRHTHjoUkE4PmcLemnwt1e5vUA9BmLsPJYfu/Q2ijDyhMfstkvUBL\ndVLzryOJlobiovCVQYSNTmPAHc8wBaXqXF1jvqyeuSHatYvJFl5KLn79fo2H\njnto+rEkK0u8D3dFZriA94aiDTVegbz9nwDyVZsbRk6pg5GV5jZ9c3exo/hq\nyRGdKr0ftLnzw+P3dFpYHgq7F9vkRrwYu79YLLKMli+NpmXlivonxpkFxYH+\n69Lt2ZtLa9ndcPFzCkoRjWmokSKNSzOn3kB9LUnPzR/LY1/InCb6DjFCL5BH\nPWIEhGhJauqeqS59noV5l4IrgncGW6Ehl2wfYvNfFZ50vONMNd8oQgiEG6TN\nfMn9BkOXPS/epeGGGnHuQ/vJo9iW3EsaRtenFZju1A1/mUPRHyh3MJcrRG9m\nfmdzYpWx+zqEbNP/R+Xp/CRe+wFLEo6pZr6VZcDFqhmncNVsGdZnLoXdrj4D\nR+im7XepsAf1JKLFIT0Je6on18GyFLwWQmjd9MOBe2imo4zgw2GHZPwTMQJR\nqarnw0rcE+Yf8yBMz0jFRKMJ95fYXyXhH9vy9VeQ+qkb81/BpGveVv6OixlH\nG6au\r\n=rC+8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"electron-to-chromium": "^1.3.113"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.14_1549525465562_0.9868777286974912", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.15": {"name": "core-js-compat", "version": "3.0.0-beta.15", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "f4f4ca56ab09e19376560257331e27ea4cbc8328", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.15.tgz", "fileCount": 5, "integrity": "sha512-4mopxabPB/SCr5ilGVoJviHiPhBK8mE5K/RcNcQQS/DqOPvXuPmeA7Hw+jh9mrJD0o0qujgE25HcxfNZefhIqA==", "signatures": [{"sig": "MEYCIQDemhfyhld2e7VYcLK90iIwpHHh7RhI4SedAud+8aGt7AIhANjwsXyU68rUHHBN6KeUrA+Lgmk1ETY2iOJhnXDjMN7d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcX0L3CRA9TVsSAnZWagAADnQP/jOr8D6JN0QTbeAYKfAB\nniSy+PUmx/r9GUGGx6BcNVqUcL/uOGs1ntlTFWGLgrssjJi4EqC9jutM15Rq\nxjiu1F2OUEU7NEPdRREoHtuhf+21m5OZW/zheHdq1+GqRc5jryE8xiOdATD1\nIlfZsUR2PwEtnl/pCeAUxTE6UNLVeysOki9o0YJwgIseue83m4nAr7+HWVcL\nYJpgWhLpQEQOrBSC3mlZOoPWlaoGbqtFbD8pecyrjdi5JaFzA+RZvdTI7udw\nQwlkG+deD4Ih8bQGaBYQ9KICOEurGsjN+HcGyhFJeP1L960vgM+tJ2n1+o+d\nrinf0wj2kE3dMMZ6c3EO8PCuL9EEOaVbekallac0bpAE59dB4C7U4bulHG1I\n3vO0+DPc8TGC5oo/wHqE8pm2QSvgjUYr0K1rAVAL1VOXlJztuQg2TtKi2IeQ\nQyh9zHFCc+YKqXO9HojjVS7HV9iDd3rVRx/Z6uF4ulBDKWePYzQGnzJbSmcw\nXzGCwyp2wd7a8HuH0ge+YJtqpxNhFJ4F9ZKW5WzjHqk+GQ4xK093vqTNElWI\nn5tvE2Az0Bvr+Qub2zWprFOgX8uzgTGvtbpgenzW6I0j8W25PQ3kzqH3ZsLO\nsjJJOG/BBnSRlI+ZeykWL+mqLnoR0kNXwjxLRmw8TmhZaL0KY79qwsRNy7gD\nMlYl\r\n=EQWr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.113"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.15_1549746935219_0.8487878051411413", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.16": {"name": "core-js-compat", "version": "3.0.0-beta.16", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3e43e590148f61970e5f3ec362bf53339c2760bf", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.16.tgz", "fileCount": 7, "integrity": "sha512-+BqDEwMHx6k4dYqHEkMiUTH8eK4yPByfC8zSfUtw1E2r3WdX5Yl+E0SRVHa/33QTknVq7vNtEVGY1GHqx0+k5g==", "signatures": [{"sig": "MEUCIH6Mse8sviJZxQaNZavDvzK7lcXnAAsed4gkdnhPTafcAiEAyIa9SsskeOB9UM68FvmIf2hspAporWPw0qNsi8qXSGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaxekCRA9TVsSAnZWagAAuDoP/ixI5Y5HB5qs3L4ONiv3\nPhg03N13h9WuL2QMpUod7UeJ1qR4DUB2+Dk6yrZs+c+ZYI/QgUlGvqqx6aaK\nO7r3CnsodngfC12n3vM2c6aap/Qtepa8OvlPEI0NAKKtv3Lm2DqAIgc6Rocr\nihd6yRCdvfBeygozaYVotLFyLeVI+TYedBTzUFshV8WKb7C4CaEoD8+byO0+\nu9ynw42xoPCBkiuvyVJwIE64kECbrBIHuVNGolLECmQ8CindC8UqUgM2eRVR\nS+kZdtPnYffJpLaHlSraUyYYLRxqFpYik1X39WBncicACXxODPBa9017wQq5\nQ5e0vgzqWXIOLVclYwt2CQbCwz24Ued1xemc4+jITKoWyDO3y7ZkpWY7L/EZ\n4c0xFL739a8lbDv0osmGM+PBd9DTBBNjWlwyafRj6BqNs5JjYV1686TEQO1w\nxGKO4lNQf5CgtEv8pHk18T8ojgYaAwi30LbZKefyVED5NOctEbpUyDvEFHv7\nraEFMMCrCPXnhBjyhVnlUrNSZwkRzK6VDXGb3EXhMAO+QcnTwmNLayJqkSHi\n6Uob5bH4utUAQyqSml60vkCXu5NwdyBgSxhiwfb9E8Arh4bRuUf1OcxilAIg\nztS3FNQYaMJGT83B29OXJWlO3kSMNCbMwJ7cXSsapj5ZQ+bGPQsHTjbX2B54\nHA7p\r\n=IDzw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "browserslist": "^4.4.1", "core-js-pure": "3.0.0-beta.16"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.113"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.16_1550522276204_0.051518059761104906", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.17": {"name": "core-js-compat", "version": "3.0.0-beta.17", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "cc4d8b11a360649d107a2826adff03b6dbbabf95", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.17.tgz", "fileCount": 7, "integrity": "sha512-+hfKDdZ4/hwwAYrjKOA9VLlMby1QlgiQW392VVfblVzivPKESWYth3XVqobjEFhaEOrAjiOJHJb43EOpYTLlMw==", "signatures": [{"sig": "MEUCIGpKFyeOzP/YaEEHDw7kOzz0zPucox9W2M1LsiOI81AZAiEA583pVXrLt8zpbDoFR6TkJX2wuxim7BtDGRB2J4jcYMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchCzKCRA9TVsSAnZWagAAup4P/1sTu00kOj4X5IevaRlp\n1P0xJM0MJN1yw7FMYdPcwAQUUbKHlpBOuqZKGsrK1jO8GIZAJ/8hiJQByGh9\nzQTkh0Afe04u+YV1iQgyrGwD37nsKlZ7ccTs0uJo8i6Fp/UUPjEGiwjEFBmR\nNNhp5FPxiO0AIfNrtL4dcaMn2q/dOPlVXZ6/f0oAB0SV0Cgr861ukQuLlmFz\nozNLhTldjC1XyRjyxVudTWtxwcAsI+MAI7E15MRrQdtyA3wC+YwAGUuifJEB\nipm5H0FQJHneumOTpZvua8Ncz8IXyrnVJ+Y7fslOMTgKwcjC9Rx2Slun8jfN\nM0k5GthH3KX9kc/53i9hFmXiXbRMo8XlGLk2WFHg9FxIYcvQZ04smmkIzN2M\niWNzuU8NLWbSZYAxuX8cY5EmXm/B586nAO8ScM9HdN+O5kIQr6MMPmHkv3IV\n0nrK5uHuzdOre0t2DUv53tT3HsTpjDrPAJ1RAlb70gdMhDdn5PiUoiJvcB1Y\nBfmAGPRW2KHwGOQaLeiPBbJl1UoEEbLJY/dm81/PMEFCvcqKvcqvcf0LJXmO\nPUCvZ7ieSapkXMLKzL1+SyYhcnKXUiaqF5kva/0VJS5yyB9pgCXgHbUaA2Is\npAXDC4RH2LatgwCQPSnH7qLz0pJmUti4n29eDqHsrI1XF4AFrxcNOlljKnsk\n0/CA\r\n=tqVC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "core-js": "3.0.0-beta.17", "browserslist": "^4.4.2", "core-js-pure": "3.0.0-beta.17"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.113"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.17_1552166089077_0.2872866836434633", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.18": {"name": "core-js-compat", "version": "3.0.0-beta.18", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "78aeaff5ab7395e6c83a6c2d65998106084e7691", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.18.tgz", "fileCount": 7, "integrity": "sha512-lGDWwQsHcjRTuLuXxpDlNC2F8/IS9mYvSJKR9E8389fikTWejul/nNvwbSWeXDQyNNdEmZ7rRKA8N0RQqQiCdg==", "signatures": [{"sig": "MEYCIQChdKd4v92ml1CI+MTO9xvi80EiK8l9G3G2IgAEbbNljAIhAMiv/izBWAbuqxVxDZpnhQFVYtfU25MAC3uwvqkzAXJw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchDtlCRA9TVsSAnZWagAAoGMP/AsZwN33Gos+OTE94mIg\nBx0eMxrErnqlPA2YuYiPUbg7SFG8N9y9mkNYeUnRiKT4sgZyHWaShuAOXDFQ\napvXgVRRcR4QNBivqg1y8HdsRC3CYJpa/0aP6lANXbRer/fv56tpEXhaJ+wP\nSWNE4V+vxiGgNiHrwOgLxMSA0xmvSUYCz2egMTaisJ1W49VRb4ruNbLVnm6H\nIfHBx+ZgsKHGfBJuba9Ji2mXceqpW7LCSdqSzjY5iSeOWQilGfsiHimNSQ+f\n39J5L<PERSON><PERSON>+edcZDrXdBY3JHUE0GR8U2Xvywf5SXF9Vjj+SUO58oMpvkfcFc4B\n5y4UDxh33axF51W1FCY7NUWv2RcGkCZhZlJ/xtrrT25/hS09IuN2wfEESrRQ\nZAp7UxtrW7DJwKKex5j/YBWoAstxbx/PFcCXi4nXojt5tnZ0Ek72gGVaGuk+\nNuYHd4CMxM5NSLeyXW2Hl5LYxrJZJ78IFMMrtr5hgdokwLSZgzEkKNysI+a1\njLhTuMxCmUqtS9mBUiNSdDHgMdNfwN3MNs72BnA8BvpaMSyOMwdf9nOY0I2a\nfyjB2Y10NU1/0RAmoQXusKnPHVuoHIScuVCV2tU6olAHESuQNPhu4PTfPWbq\ngEUIpDmXGHaXaL29GTeMVMROqwN6iRcCi1oqz8iuJSWECFlmeDbYQm1qUXTW\nQeGz\r\n=GqMR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "core-js": "3.0.0-beta.18", "browserslist": "^4.4.2", "core-js-pure": "3.0.0-beta.18"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.113"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.18_1552169829249_0.1850800339756149", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.19": {"name": "core-js-compat", "version": "3.0.0-beta.19", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fa09f857062d9d1c9a94bac3cfcf4fdd56bfc7b4", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.19.tgz", "fileCount": 7, "integrity": "sha512-/+MbA4i4kx7SznFon7MLpCACnvkKGNAIZsUeGv8+f/SQIRy8/BZYhPM7gqBtmej3uoPW5wPmZKrdOqye3WsMlg==", "signatures": [{"sig": "MEQCIE6EzJyBXqmDnyHeJNE+XVDlwCl7Vm3fjLxnxxB6MpsMAiAHzjV13ueDrM1WBManWGHYLnzEfY2PR9wMCwwct1q6Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcir1ZCRA9TVsSAnZWagAAS4EP/1cNlWQQ1SihHJ5yIj/k\nuzxFoDjt2X96OmtylElqQCNyp8RMrDyQeRMLjzWg2zjjlXe5f6tUHWHWLoPk\nhke9J79DZfd+U55MTmgfHFNqiZ1SzyfR9nW35kVif4VP/WWp2GA6zjtLLBU4\npQf7TOcjhpAldJn/YZUHg5xlgs8LEertE7DwJnG/z8Yna3EX7FocZCsos1OE\nkJprevFRSlskusjZOutQCUqoypuycbNpDekadT169LOvIg1kw742vYGuGBNq\ny0JQdsgSbDgHXy3OxLs9wq6vjdQWzOMAhHOX2kSgln9WKHJFLwQnhVTnadqF\nmgmvEXlUN3BGvpBbL1MVRAKBU5B7wWeCgwFoJ0Da4Jk2bw184M6gORHDui8z\nC+34y7kv87pGdmmyZjpstImiYxBN0+AirC26n4whotT/k9+lXTrg5kAiR+iT\nbCX5xUqGgMAiYWR9Zbkj9m5dY8L04EPPGi68SsaUi7z62uqFESMCwT5MHxjA\no7nOMka1e2HesWJpRbv9iFBXwEssJl6IQbVdm79ej78MzosB+DFM6w9YcLWY\nRZwiKHia/jnjB4gvlHTEXgDShG/wJpkwWaRSH7fLuVIy0sB8Hx6VzUfKCXUN\nsUIFsAgtWGyX9wUwEL7oeFAczrcrXdJ6cuss3E6bUz3KY/C7r/LtXYVoo8lH\nxzAU\r\n=cOoY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "core-js": "3.0.0-beta.19", "browserslist": "^4.4.2", "core-js-pure": "3.0.0-beta.19"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.116"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.19_1552596312548_0.35358813661519783", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.20": {"name": "core-js-compat", "version": "3.0.0-beta.20", "license": "MIT", "_id": "core-js-compat@3.0.0-beta.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "eaadd63391464b4558e6713e73f3a5741722faf8", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0-beta.20.tgz", "fileCount": 7, "integrity": "sha512-lS7V31nrWeWMLfzxc1xsIGDQ01nNRwz0iO4pcxOQKVHLeg+u6hjS+HikGTbMekHgXbrmIL6AoBdjn4ftWHEVKg==", "signatures": [{"sig": "MEYCIQDx8XRSwZ670+Euw4s4BCr31isQpvgjyF007xmlLY/UeQIhANxzx8z7BTLdYEGnXk+qcBDto5sCifOc3QXxzs8GRvCz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjqcpCRA9TVsSAnZWagAAHroQAKFVY8H96NHbZ20MXRQM\n5Guknk1gBghH+2rSSQ8bNpvdEhqL0NXR8eV3Bv+xwCY9kS5AVvnZhhdds1pv\nN1fLWLd1SvuVuloKJTQrTBf4+XA+AXdet3NSB8c7jsV4U6Qs1GpQigGGykmg\nhLURQ2JscB/2xeVGVfKY4zVuUA6wzp9ECP3/ZYVge+ibhYQnUTgwRfPiwpeY\n2wfkBSF3JvdQ9xbqntW9YoGoQDMSOGBVpvPTcfT5fExr20HPTBKi0RPzzl2i\nWfL7+HkiD7wuhCYK0z7wpNnZo3B0HeqDYIwi0/aMqUd5GE1wUAvIsfg1561A\noinNDi1qfZeDuDG8L6EFuE7kmydFbzKEvy6r+9IavINFva/pr+J8S//35PNH\nNT2TwwHjmadAF8vQq8NbQHs41Apn8pTP6lVnIl+CbeRKM3n7f8GCjQ3Ml6pZ\neXITcTd9uCyHsxWC2nwJlL5tOICisEOYnoukz+P5YuLpYoG3hPYw+8AeH60o\nF8NcNgoVeZiVN0xjQ1tJVVQTq2efuwUqsGH2zdPmRgArbDuV5W2x0SeygmOK\nSLvJnidwkJN5iPNVHzzFdPPDs0s1+qLvvtYC3GkzR3xfSOXug6/homAZt9K5\nQGZ04LR6Dm+8/QbxawbLBZsdVQxKpuapKSSgtmsq5t2qkHnFcjyE8yYBcaLk\nchuX\r\n=ljRZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "core-js": "3.0.0-beta.20", "browserslist": "^4.5.1", "core-js-pure": "3.0.0-beta.20"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.116"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0-beta.20_1552852777159_0.9229699399894047", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "core-js-compat", "version": "3.0.0", "license": "MIT", "_id": "core-js-compat@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "cd9810b8000742535a4a43773866185e310bd4f7", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-W/Ppz34uUme3LmXWjMgFlYyGnbo1hd9JvA0LNQ4EmieqVjg2GPYbj3H6tcdP2QGPGWdRKUqZVbVKLNIFVs/HiA==", "signatures": [{"sig": "MEQCIGh1SHNv4VZ6TaeLjjlTGrD66QPtv5AsOg30X9iplzs+AiAY58pgYqPdca50ugZhynxjj0hnJlSp3LybHIDYNEM79Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckUCDCRA9TVsSAnZWagAAO/gP/A7lidIRGmTbpv7aydjY\nciQc7aRIPkDwXgs3+T5CiQGkAlHtmXYWcVY1ZY95vZYW5Zj0B0W5oBDPgiKM\nk+NI6J608J8HmLUjjYx2j/fOlJaUg1zFLs5/ykDyqTfRu0VfpB+tw++TbWuh\nit7Qs7sAvHkryeorCROU04KGyEZLc0JEvAwXobo9m1aRzFwr8+PuEUYStW9l\nVap4U2A+MK5ktx5VyplOAExo3opI/m9ubHKwNWSDkiyUiY5E8HLAap0XeNgl\nMxebSomNS/Scz/7lckEDvHFmV/HDdLt9ggCEWsT/y8dLs4xpVR3vYm/zGzKf\nGDFv5Azmal++HhEDXsm3U+sloW694fZrOmgSx+uSSrBEetGR4rmc06D9a+LY\nXiT/BYpT5+d5pQxiswlXR60rljZQTeBMtbBVAUv2XmDOl+kHiCzuQ8H9Mfk4\nydJTK0d84bh01fTDL0Te4DWlpf7dfgXLAc9JPn1Jy7Ji16yPsmpdC/EwXFzh\nv9pFCVziw+7pnaxPV0Q4nEC5mqWWYW6Iugq1fm0+USh8GIhLTZ0c19JdfnZu\nXsrKQyP8dS+W5AWT+rlww7VKoz1dZbDyylSflInYkB84Na65bg3CBLHIR8xb\n6c7bjmRAMMG1yoq3VYakn/2rHKziAM1Nyh9Anr9r0petCSgPXIVLwdS2pkE6\ne9SB\r\n=Ezh7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^5.6.0", "core-js": "3.0.0", "browserslist": "^4.5.1", "core-js-pure": "3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.116"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.0_1553023107168_0.6057407518228435", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "core-js-compat", "version": "3.0.1", "license": "MIT", "_id": "core-js-compat@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "bff73ba31ca8687431b9c88f78d3362646fb76f0", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-2pC3e+Ht/1/gD7Sim/sqzvRplMiRnFQVlPpDVaHtY9l7zZP7knamr3VRD6NyGfHd84MrDC0tAM9ulNxYMW0T3g==", "signatures": [{"sig": "MEUCIQCJOzb09KOEG6j4RGERHSwQAd9MCv8B9U0qUukUbBLJjwIgFTMCSqCM+rYMuzC/L1ot51dxxzPLBf3GHVUguaF27Rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqFM1CRA9TVsSAnZWagAAq6sP/1aOYU4j7wmf9LsBlylH\nnc6CoMea3ifCNvImxLZaIxb1htGUAbeK0GlV630mytBn/8dTTSLeHli2Fv9Z\nez8TQbkfc3P7D9UM4J9otSxSfc003tW8VISx++Iql5boNA8Thb4wBVJ9JtGN\neu7zoRuwyCN0O0K+6s+2DZbFykoCwIarbgO/ouAdUQgyijhdgeCzRJyoL7nf\nb7bshLgV/DdC/bxuoTaOpFWdSujXwRyToq9KlQxNW0hKCc1MLi7SrN/fiHVm\n0ULAw8LInpmq3ra2C1zkqHlrv+64AdnCZDb4XLM8MMJqWdgJcfdlcN4uWVRq\noGaRzDXtYlmUbmcqybat47uHcNqwOk9cMc3mwIp7ydyzMJTpA9u7YabC6gta\njk7gqZuyRSZvUnJw3UTuNq/5Lyq2vXFnPUYsa0gPVjPK7bKunGh80aEgm097\nPOb7RmKEHnbZTQ4Dv/nuvaCGRrw+Is5J2KEysXZ8ELokM902+VbYah3x5nxu\nFFRTlEHJVCplWkLzqsI+wT/NNvoCJkdId9jFt1B7H2a9KDJfry4TdZYhO9he\nb8MkmkaXruLo3O2sLo0prXSP7XeCyPTRC6AGhSBenOWS5SmNBnMlr5rgOpNW\nk5at6bxNM+Nqt/RkVRJotH7d/xZ/5UtOztp8HSDRXw0GWv0c8mqHrvy063ab\nMl75\r\n=AP0c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"semver": "^6.0.0", "core-js": "3.0.1", "browserslist": "^4.5.4", "core-js-pure": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.3", "detective": "^5.2.0", "electron-to-chromium": "^1.3.124"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.0.1_1554535220385_0.34622612918491735", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "core-js-compat", "version": "3.1.0", "license": "MIT", "_id": "core-js-compat@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "340492a001d81100812e0f6e874ee6e21639ae92", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.0.tgz", "fileCount": 8, "integrity": "sha512-v8M7YAMacMOJ4T4Z9QSud3dFYASMDvK9d2RWBHRSJlO4nGboLQVtFdbDmgzxfM7XrvcvO56L0sHcqGjuk/4wTQ==", "signatures": [{"sig": "MEQCIEyrhdsMk7iskyC7tkzfvCU47+sFOFQ5dOReal/7lk+iAiALoINYogfAFoQfLaU6wtmGcObtoV2G2SsX4Uyt67BvUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4a4DCRA9TVsSAnZWagAAdA8P/3BQnwu6V34dJwX3nBW6\ngROLHKDAOnSd8ISaIQDtK7Lc8o5l9YzoMHPEsI5a2rmr/dP9FShEB87TpB/B\nwB+RaaP3DHyblBk8hvKTeOGK9p4J2YHVKF0uapYmrn/XYsvKN+rySJKty8dD\nNvTL1Up00Ycwe+2pvCul3IAvL+TEcKtROoyt1oRhBCncR3vlaumk2wTyaCHV\nqGWRJBTZdMwRdZXG8NZlRn82z4gDXx9+TwT7GbzEBKqXET/exYHA5bxyudps\nQ0x5Fgn4xMseBnKS9Vxs57FH4+47+eKIvTxdmgyJirohGwFgFFPgmk7mbfam\nme2r3MlqiOpLnfbOkyAmY8DyefUID+c8ut9qztCa9ZfE5bGYKJ89krkHOiWt\nHHYp7+JOJEQWTQdmv5Y+G/AIbUOXqGaz0IWswCURfbtlP9Ro3ixDfnUue0Ki\ndr24aXlbl19khNZ2WM2DHhj4cXKznUttusljBUe8NSItK4yAkn0EJp88oj+r\niSSY2dc94DprsU1wTD+JF6+sZaxnsXnDfzZSXAbtwU4cLNvGijv3xzqaUjW5\n4pFitz/b3hgAYHuXCa0noVIh3vZyTklinWKKaqZ3+mal64CeP0Fk33c79frS\nFNGs3VZhixjq2EbzywloS0gQog32M5s7DRzos7/U8A3O0XD49o5d3m4E5M9P\ncXZm\r\n=lM4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"semver": "^6.0.0", "browserslist": "^4.6.0", "core-js-pure": "3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.135"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.1.0_1558294018780_0.05255055736656189", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "core-js-compat", "version": "3.1.1", "license": "MIT", "_id": "core-js-compat@3.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a1ae7edd5b791066d6eaa969c1b62986b844fd42", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.1.tgz", "fileCount": 8, "integrity": "sha512-RH3kv8NFovFRMdeTEUgu8nMquEWKEVttOY3JFi8mN75sg72zygPqWbpWNTUw4JYEU43562tlzhdWsWqXmdFAnQ==", "signatures": [{"sig": "MEUCIFFfHcP7knpb0j3hw7sEAZ/y+eadY6f5gKkqJYeHnTvbAiEAukqi39tGmwCqA+R2YdizjDRk85AibvkS6ZkjT7aiqik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4xEcCRA9TVsSAnZWagAANicP/jcOY/mFwDQXRa3EK1yH\nZZWEAghuAgBnbz3ncGtBviYlpQeedTE2IX6M2tvdrAB5Rev0bfJLkAw0w+Hi\njnhDzk8daVuqDtZhLmr2TlrJRZCXswTb4QT+9StiXzh1f3aoUrL29W5QXsZM\nbHEgkstlDp5gF1ZXa8NtVjOBRnqlCgrRuh9UnDEk4KMEOfJNXz6xRnSl/d3C\ncr7i5STwnr3zO4noSJSWicuETaktwgf/yscUYPqePLZ1EI7MpGrIg2Itt7Zf\nfx9f2ojozJZmuVz1yWMi9VKjzBMfCxun/P4CB4BY2PE09dwDoYNSfgxkvxBA\nA7sEHE79dz9QRNiBuze8EvXBEpI9Id6r92dXnHViQFD7YMKQF6hLhwTQkjqX\nbEcBdAOjDbgN4g3K584EvxYSuaDGGT0+UhEc8gwqY7At83G8azfW6zrT2NZw\n9jeTUk0zuiclTDmp4ucx1puVWV78AR8nL2J8gJPvCUucGlf93FDmOZ86wbPf\noSWbJV1Q5YnocCJqKnOVzMrqQmYa4R1CQQKoo3ivGcqn78H0Dcl8RD27l8rm\nQB4eN0JXkYB7xp8B8f+DSJRZ5fpz3iWCMa0jyivEmhOFKSUzwZOQKd4uM2lc\nHMq/J56xXkNi4lOOhsQBV+k0wmjGNhe/HsixBOwGbkWSwSW2ilpSD/M8mSji\n2UQZ\r\n=PQct\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"semver": "^6.0.0", "browserslist": "^4.6.0", "core-js-pure": "3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.135"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.1.1_1558384923843_0.40419731377439794", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "core-js-compat", "version": "3.1.2", "license": "MIT", "_id": "core-js-compat@3.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "c29ab9722517094b98622175e2218c3b7398176d", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.2.tgz", "fileCount": 8, "integrity": "sha512-X0Ch5f6itrHxhg5HSJucX6nNLNAGr+jq+biBh6nPGc3YAWz2a8p/ZIZY8cUkDzSRNG54omAuu3hoEF8qZbu/6Q==", "signatures": [{"sig": "MEYCIQCVszNZufEIyivfEf0fJm/CStJO3q/oJ8k545/UcJ7eHAIhAIs3imN9rMZjB3P4qT/Imf6aW2eYVZjzt/rbtXhfjfGb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5HtyCRA9TVsSAnZWagAA0eYP/jVTvMgs+odgFXNbRqMP\nKJkjSHceDrkO/wSllzMzRSWN9qwL0rK8jav8ZERIHH603oiC7/DoYSe50DVZ\nENA5NqKK/qkjshMwi6xlkIgmOX4TEk5KEjpsR0LecerjJj4pcTZVcCt3rQ4q\nw/7g4NAWWnagf4bhmmz+YcRi9sG4ywlky1GKPE4QLL0wWhQBBEomBicYkGs1\neUPTK4T8wOP+Sr7kCz8jbwIZKgw6/+T+ac6QC2YizISJB51Aj3/g3acX+WGR\nCXmtCBvS15GN5Txpn6eQVacgNo4sy+tKgF8QftIGvo7IzPxdoJaNSveYM0J5\nX3optvFrxC8uRfqcvH9QmVqENyOMBIx/ckaiahkrV6uVbuuZ/hYUvYtLI4Qj\nmF4M92/hm7Wv/vSJqEuKMeI2EZmOFdoli7K0fWTfOCCZzN9Jwgxtznw+BGt1\nJouVhWgG9Hno6eEsIGTf7r4VS3PL2QkBHkl8PE/0r+/+FTavegmGfNGeJFlE\nFviaFN21OcwqU1hf1WDJsFjKUzvwy1itJ29WchW958s2LUW02I6ABV0+f0bP\nGQvvmbEAt7UKF3VN0iYHzCtEDvyxOcUYsQML2RXLpAI9zgZrvPvKLG4jwaEi\n6MHyO7zjp5hgCZDwLNGwO2yhUtorZ+VAnXGG8K0ygrUkzRC+rcHmrEF9+DWX\n4qZb\r\n=25Jx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"semver": "^6.0.0", "browserslist": "^4.6.0", "core-js-pure": "3.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.135"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.1.2_1558477681418_0.9131659415254603", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "core-js-compat", "version": "3.1.3", "license": "MIT", "_id": "core-js-compat@3.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "0cc3ba4c7f62928c2837e1cffbe8dc78b4f1ae14", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.3.tgz", "fileCount": 8, "integrity": "sha512-EP018pVhgwsKHz3YoN1hTq49aRe+h017Kjz0NQz3nXV0cCRMvH3fLQl+vEPGr4r4J5sk4sU3tUC7U1aqTCeJeA==", "signatures": [{"sig": "MEUCIHvf216MNMYNZo9/LOHLb5uj0f6Kykd7kMD7fvEz9+glAiEA1z/ENRaWF8kPMhB+jiVgYQ3lg/heTMW8mgJnE+T6rAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6+DMCRA9TVsSAnZWagAAo48P/jR/kUdd+JP28aKlXCKi\n7z5iJ5Wwb+n0BVsHRrQr+uG3seHuu19FeuxPbkgZas6H/AHpB9qnum6V3EFL\nMiBj7kEwPyFT+T4s/t5pAVo7HoQYcwo4sAdaxIwml6GQ3wqEIBuQmJ8DcE/S\nZBjKbB8m9MZBUwrqjD7Yq+S5JYAms2lpiKLT7PES8gvXDga5BZyrzGW2xibU\n2HfHOZPsg7oJ/iGHli+bd3oNV0g/aCRw8+fLu5GgLARvYF9f5JAw3eGG6oe6\nFfzxLC0TzxRtwGaGUp2lZjBwOo1QBxHRuWfoHspMgRRraBkHa+nd2xwjF5KM\n76G1XC7Ecr/AAxWPQFtxsD694g1L6jPLR6UxPb9QQi2E5T/w3vXmupTbieFh\n0i+sMfNHYLhYQl2lIVNoXtJTAWI8tjX41X3IWMSnkcjpc/jLUusOHyAf6KU/\nDl+/a6U+BtSSqA60AzaBcmbyXlAuRuRXpUG9czcQuKP9Qlh6fjSj3A4RrlHx\nn+9tDKGC/9b6Q5aLMGFZNwAIYUJ+AlbdrenF0j+7b5/iZ6flER5HIk/G7zBR\n1pPLYMA0q0Y8YKQoEyAGkrgKCGhPBvZ960DgLC+5lxgvAAOmDbKpEszV8Luo\nIhARjDDP4NWLiY5l1JCex3WPtTAc3hpMMHH2vHySCuxeTysxSBTwgmxoOJa+\nI4qw\r\n=AVRc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"semver": "^6.1.0", "browserslist": "^4.6.0", "core-js-pure": "3.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.137"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.1.3_1558962379898_0.023241886897012787", "host": "s3://npm-registry-packages"}}, "3.1.4": {"name": "core-js-compat", "version": "3.1.4", "license": "MIT", "_id": "core-js-compat@3.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e4d0c40fbd01e65b1d457980fe4112d4358a7408", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.1.4.tgz", "fileCount": 8, "integrity": "sha512-Z5zbO9f1d0YrJdoaQhphVAnKPimX92D6z8lCGphH89MNRxlL1prI9ExJPqVwP0/kgkQCv8c4GJGT8X16yUncOg==", "signatures": [{"sig": "MEQCIFjEOCDu0C8y8CdtDmDU8dC8+r1lkfrlr7WalNIpo1qZAiAw0RxTGQgQEKb1eoF4jI8hvwh5hnLnxtLznrlggCLLTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBN0pCRA9TVsSAnZWagAAFx0P/3eIA2loMNZbaWUNlxZT\njrvhkD2iIfuYoYU1jIMdEjixl4l61GkMhG0kSmuHdrq4YJyY8KjORO4VvCC2\n2+6O6+AGeDWXkGVC3qfcggcydSOV+/oyOVdY2eIBFXWSF/6keMtki5f9wJ/j\nJY+KLiz9v6zsjIA4EDeA2NgKL60svPr3ekzyuNqDo592sgl7qND1VFeASmlv\n+6jJtVNB0bY1ZKqg/Za6qerjDmYlMpnL13xymIEDwnPohvMwiQb2Vt/OQdxs\ncB3yEoCHoJJ2mC+SnHFw/2yNs8KWZlWiD3o2KTz71Zz9Oag1cceDoae30rqO\nh06GariRkVMw+6Zch99IvZYRtKmtW3E9bN+t7hAw3+TerFDtvb/9FBOVzeH9\nZH7adPyn1CcVmJSaDFVwU+XFZX+Kxs/Rhbq3Obbk/dKac1aUcDN+DaXVW3JX\nkK+vOHrbcaD4suWcZ8AFCF95/p5b/kF6eqFuOdle54Bzhbp69HlieA0sIMrq\nbVxggsDFykggfPY8RH/y6Snkp0Dn70bBJzeFhYtxXbnviHnx1emOdYYC7uH5\nHNtVXnHgapBipKv+XVlH9VvBzZmZ9S/Z8MNwrEJUB5Q3w4b3+TAi/iiKuUll\nlfWEEg5zaO0QyAwvRw+soTYWRGMRlZUMZwt6ADPR/lqyunDGyTOafpZRapCs\nAy+S\r\n=gC8A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.1.1", "browserslist": "^4.6.2", "core-js-pure": "3.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.161"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.1.4_1560599848377_0.4911272908968589", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "core-js-compat", "version": "3.2.0", "license": "MIT", "_id": "core-js-compat@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d7fcc4d695d66b069437bd9d9f411274ceb196d3", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.2.0.tgz", "fileCount": 8, "integrity": "sha512-vQve8j3tGPPqIkrsK442hbVwDIGXcngLPnislJaCNJIG1aBWPD0IqRhpxEwjtUOYpzLap8xA7CoGlJBT1ZkcDA==", "signatures": [{"sig": "MEUCIQCZJmf75JWYcsdww7XdeT/Z54aio9lQT0xV3CxmBZahzQIgbBia5v7MyMAkIx9r67nquQomIfGy6C/v1HuqTmh5hBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTJy4CRA9TVsSAnZWagAAErkP/0Pxymd8EUMAGBQ5udc0\nze0KJMAC5wkz51c+iwTaK7CZSSH+NDjxTmxq1xwXJVQqt8B/SqYKasPZYit6\nLAQ2OjQQT5H2ApcYqT//BHnr4Lbt5OA/yspgR/lfk9MvYID5BylK2/1/oPNG\n1+uBBX2yqCF2X3VKYZ90KAJfix2FlKVkRwZEa2IWS5llrVOtlJj0QoEU945N\np5FQlOyPQdRL8YNgIPtPcky83p8haI6VmgZiHH8x75Xm1buZV1ydXRG4zrUE\n0svJK+e9DFmNWpLpxuwCuOJgLmMyu7JC7Z/rHTaWlLSBDx03+BFLEhr5NGhe\nmB2s3cto8PB3TMu9Zq1HnK6NQdyEzoAnHJZHiHikBOrRqteT9Zzq0SNDRt4B\nH99JbW+CpEo8lwuaL6EZPx0gWC0lMjMsScOQ8CJW4VA0c/SxKXovwSJJvxQd\nht+nFBMCQ8BuybFmP3tz5mI09sYWe1Mby7dfMAWhkmjYL8R7FTibjWBi1Mug\nt3duS7i11+bRdzYNoClyYwKEmxP0nQSQTRfvKsfaCuObBOV8iALX/jv+Qd2b\n0e+F6eYUGFOFBr9sybLbOXsIRa8S3X4IDvfoQCHZRUSPrfa7Xm02/2f4mZBI\nHY+CUPjbvlsVkeLF/xW90R2FZMjpP1mQWR3lj9VNU47XJMz5ahLUVuS9k+q2\nOTu4\r\n=hCYp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.6.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.220"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.2.0_1565301944153_0.4613150367771026", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "core-js-compat", "version": "3.2.1", "license": "MIT", "_id": "core-js-compat@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "0cbdbc2e386e8e00d3b85dc81c848effec5b8150", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.2.1.tgz", "fileCount": 8, "integrity": "sha512-MwPZle5CF9dEaMYdDeWm73ao/IflDH+FjeJCWEADcEgFSE9TLimFKwJsfmkwzI8eC0Aj0mgvMDjeQjrElkz4/A==", "signatures": [{"sig": "MEYCIQD5+ynCBxncrOMvuJ7bZQQmgMJ3jITm0BTifoBnAHe25gIhAMNd3W1SkW7+7nbbjvK2JrNW06ob0M4kowCC+O7+Aha6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUVq0CRA9TVsSAnZWagAAU1sP+gNSjlQJlh4oStGvLsxW\nKKHMo2+qgkOs3OfupUTrm8FpXvQSRSHhT6a59gS9vkKH3mNlTjvN+Sq4kewj\nym3/jQrBc8854NVaHqfg5be00dle/TnSq/fkcLYG4AAtD/1WRRqB6WdXfSoq\nags8S+D2zwuHRrnlKrVpQx+2fxpzS1QAIg/jlhZjRqwjT+nbj9SlczS/z+iN\n2IfJP6q6RtD93aE5Dh4ACnAmpyT4CDzqWaZ/LbiLbanLud3ctEgsDtO/mEl1\n5lohU1/G/zCZloRY5aoAWPTinDa2lRQjNTeidgj6X8kbigjCxHwyOh3OKdbA\n1DPvRWQ0NsvPAgRahhm2BvSwH26iX3TII3jPUCO+Sev/eLcxuyO6HULdBeJH\ne3BNJD6Onsi6IEY56CX9CVzJ1SgDkOF5ry2Q282QdLYQVYEso8hytBp2/WR7\n5Xs1syb5TLc8iIUSyb8ttpxj50613n5DLw2eEtQ5rOI/tAfovOvcmqGAjBS6\nCqLVgwID4WZD+nTyNjzQOaA/ccq7phJoEtXcjn+/MTmbcHiQ7kru1bUYT0ZP\nX1WQm2/Sv1F0ZNN+Opl89ot4PR2qO8Fde+sc2rLZekK5AQXPePRQwdw2o7Zf\nys6jyl/AiEe/KilxYHse7vhk8EqvM9mvVBRdFWYah9GawTV0UKVsB+wvZMrr\n52Yy\r\n=1fmT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.6.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.224"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.2.1_1565612723504_0.855011541306907", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "core-js-compat", "version": "3.3.1", "license": "MIT", "_id": "core-js-compat@3.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7a22cb813bdf673b9929b90da5cbf6b3f5a72f70", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.1.tgz", "fileCount": 8, "integrity": "sha512-R5gisVHlU8ZXrr05qjER3SJ8pp8cYxhWQ0doIfuGhLqtHf9b3eLzzr1ZEfI7MX9JJFwVT+HuyToULupnHl0Ghw==", "signatures": [{"sig": "MEUCIQCBv2ZGQjT98eAkJ0pxMCCCL2Bvdc+vW+oIlZPfjgCf4gIgeivoS8gSq3tM7X07KFjYi2t7GhP5amLRu04LTcxlA8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoz2BCRA9TVsSAnZWagAAPsoP+QHwtdHBByR/RwhEQCd/\n6YAFCrMe9ZOLUTqpEXmarwkjbahUA295ZaVdE5VIoVH6ZXgHkNlRDRiC+D3p\nPdPtLoxCYYUfEmwi1PHjXlVOqZx8jX4WTDKcdwV5Ai0dKsIKs0vOPK2yoZK4\n4+u9/GYMvE27vyns754JckGrFJn1rJo3Us6i+RpLyGepbkAw2noUh7a+FjR/\nOAghbIlZ1vocNh69yxu4caNfJHfd56xEKtYmsoOvpuMIwuGMJq26FNj8vG4B\nCZq/T5OqeKC6EuOfPJcpmvwRAuOzbDQV/Oai6CpKGMYRkL3t45rdyBDKTmxw\nIJMlV3e04OCKBcFMHSUhVsIFT/nO7iHbxlAhQzT+CzlHftrAl06GvsQIn0Cy\nEmf2TLXAlFcLNAc1SPvRdx8TRijOkrxbE5A2AvyBthfOmFR0E4VbN2Aj+x3+\nXaBFg4lVb94U4yK907HAZo+7pjl7cug/N9LRhTUBqMOP5EvP5jzKWimuxG/P\nfHNk/ih2sIdIMCDCIkbBern6ibyewCLP1YrjFkvcFS6BiBr5j1Pfnr80ZdG2\naPBCrSX+bNg12aKpGl4LH9XqF3QnUPQchhoF9iTNgSP3i4IhiGgjRTwCFMQJ\nTppK8cgyPbioVJf/w5BgKKIu0iQX4aVWVM0Ab09r93uUkjMQde9gF6e/cKAU\nIThb\r\n=8EXg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.282"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.1_1570979200583_0.24115446629798232", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "core-js-compat", "version": "3.3.2", "license": "MIT", "_id": "core-js-compat@3.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "1096c989c1b929ede06b5b6b4768dc4439078c03", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.2.tgz", "fileCount": 8, "integrity": "sha512-gfiK4QnNXhnnHVOIZst2XHdFfdMTPxtR0EGs0TdILMlGIft+087oH6/Sw2xTTIjpWXC9vEwsJA8VG3XTGcmO5g==", "signatures": [{"sig": "MEUCIQCX+1iIGsx5e+MQDc890Ra2fNrajaUmi6r/AxAavrdHEwIgObTAsKbtHWGOZ7wLC45HKR02VrKAzwnr03RSCsrPnwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpHsdCRA9TVsSAnZWagAAUjEP/ieaK4US+1rDbXxGl5vn\nHlTM4UI9Fp/x2IT2nrR2kBWp/fjXJGTJphSY+GlnO0miHIfxbLfXgqOWW7VI\nxZ0XykCpwZFATbYPr784JsPn3C8goLtjyjRFowdxbSJroM3QeuFpdycLAAoI\nNIIk9qmIHLQ6V1a1rw4T1PkFMY3BOfJOymrFbTgVXfhbHAf+FJkWW3BQ5S1p\nRtEgjGmReUpW71oZ43XZXy97YlyVvuE47weop9L7k3e+guzSej/L69M+Yiwx\nQaRDGo1tKSXV/eS81j1KRFdH1+JLfiMp/yPJ6EJlt+xB+hvlPNAD7fINWju8\nl4Nd7cI/vX6M9A/HQRByjcYQMjPqW8fCRIhTgdWB5/7/UgCFLnWZm+lB5yCK\nnIP6ITtzJYgK4za2YjftERa4ZQK4y4z4f78c9SjDfkkTaq7hWcpKdjeJ9Gai\nH4jehGBya8xGYAs8TH3QGl6iQ3fexvYJni+SRtXs3dQQgLxwKaG/CqwPhCuj\nqB4k1mxEonW6TZmMz0uQ3XbwRMKect43TdlPCgOpLvbwwZqY22Z9VMM12JU+\nrGMTszvelcH27klevfv9if3RBXWbZK/FyLwMzDb6fQtzLPxLBPQVGjvC6nfM\nFKTsa+0pVCNR1Sn2QI6l9ODUwNlI25Qq5PvkRW4y//REuLdFuC/bMkydJy+2\nEWUT\r\n=Wx5x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.282"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.2_1571060509189_0.28446647916580625", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "core-js-compat", "version": "3.3.3", "license": "MIT", "_id": "core-js-compat@3.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "82642808cf484a35292b2f8e83ef9376884e760f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.3.tgz", "fileCount": 8, "integrity": "sha512-GNZkENsx5pMnS7Inwv7ZO/s3B68a9WU5kIjxqrD/tkNR8mtfXJRk8fAKRlbvWZSGPc59/TkiOBDYl5Cb65pTVA==", "signatures": [{"sig": "MEQCIAbanhGJPffDh1T+nr3Ll//tCcVVgmSkQNAJ7tnrTclOAiBRLM+Hizi/aPY0bHAlqGbpaWyavTdUEPV44uFeoqvuzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdreXvCRA9TVsSAnZWagAAmFkP/RFbsPAGylCoS+IBy2tH\n1ZDwwX5o2TsTnDWPAFqH/M/tGTPCC3t5N/6t0s0hPr6o0pn2B3wGdd5tiGcR\ngo6GLjqRNgVHA/czVABYHSr1uvMPwcYz8sZJRxA4mytiZjUxfrhwF2QrrMkg\n4ERwDijhEH6B30vpVd3pdOzwEd2Neugk3Zc/ahUNq/Ic1w6MWoxBxRx4uA25\ntzYtxYL2dW7U8glP+N1lKfDzpmp6JgO3IgPzzZUdDIqrhZnu6STOc0CCsMkN\ndOtYQgMV5QPpJqXjP1mnYo3k+hVseZSH5HWkbqBi0jLij1yX2ZuLBbpSJXK5\ng2IwooFX92KAZeWBTwGDo2cqRH/3eDk6hnK3CO7m7voHsfRjpQplBgg2YQeC\npFetpsyvvCE7/wU0FjnXhv8ZyaacNojBJW0rw7rRTf5hB7ILPPQz0n6arjvM\n8Bcu0Gc2VfeKyZBKWb0V4mb5c1kdqtzEJ1fMaPbs4Sf03fS3AwibkOGoLBfB\nMQ0fSrogaTnW9u6RU+Cykc3U+uT0WouFDBsiF7pKpWO1s3Rr2TFqqpNf5JJR\nXC0zaxCdYfxMafd09JHoLIxlTHE8R1o2JxdXZ9cEHCKUV9gEpE4JsSX2NQM6\nLOisXfyBuiJIEcKX6zvNyaXbv5oFFrsO4Xk7cEl5BGj3WOqWj3aNQURv7yXB\nuJgo\r\n=URB4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.4", "detective": "^5.2.0", "electron-to-chromium": "^1.3.289"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.3_1571677679267_0.9615217341916282", "host": "s3://npm-registry-packages"}}, "3.3.4": {"name": "core-js-compat", "version": "3.3.4", "license": "MIT", "_id": "core-js-compat@3.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a151c6cd754edbfe6a4a2a66b9382df2ae74fbcd", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.4.tgz", "fileCount": 8, "integrity": "sha512-7OK3/LPP8R3Ovasf3GilEOp+o1w0ZKJ75FMou2RDfTwIV69G5RkKCGFnqgBv/ZhR6xo9GCzlfVALyHmydbE7DA==", "signatures": [{"sig": "MEYCIQD1uHVuqcciRYRmTYebqjd96H87SmEMIdj+9NB0oYWmhwIhAJ9xm0f5vKE0hMY4hqIfSD7HQdMPBMDXgQ5BfQiKNTJt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsteLCRA9TVsSAnZWagAAazkP/0weDFAb5B5tLh8y+Hmt\nBQnMC1x2I6GO3tObzxd2MJ422N/PMDj6Vh+oEJyg1HMoa8xsloqUkrrDhQYK\nAfrNDf1B6ms8C5ybnqS9eVwURShiBVUpAyOMRejsvy6V24dvnBm73JMPe3W0\nxhp5eNYbymJ/XZ0K0UY1dp+L23STOVBcv0Fd9RfmHX+PaSGcXtSTEPKTO3xm\nGOOCISIOlMdKvq5coEk8bBTIE67HGUGv9g/CFnaBumP0wEVg9iOurtA+F8g9\nPgvL3+sJ4bcwXtvAfDl97pZf04rXwRyY6gQocSKJsmFp+v772hMPgSUOl0K3\nHi5uLyU3gCdSVNXQGxnVmoTK+xa3JdkniywDAxXyAbpDIvffBNM2YzCCXDy9\nZHDg+iKCN4kBvx3FuRIQWcCV3/3bsV6abln2hfozNK0Nr1lku32HiWhqSbvi\nVeVZqGkxeXmuq4P9RdvgqC2CFnsYmAY+vTgJ1GtwnwaUrcm7me/mvaSTCsXi\njIWb6UmpoxfCi/bzbix13rzsfkZrwqN6uIH3nZQxTxtUIi9rzWW9+29qnEoR\nhChZ4hSyvsfiWqf04Pa9Hg5jBltIYM/7o8Hc4SRmjAPQ6upLhhjR94ZNSqZI\nxTyOm5E95PcSMBM7bLBO6I8+/dAmql5rX8b5KNndUf/bZbF2d7rABOF5eVSR\n3ecw\r\n=rhzj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.5", "detective": "^5.2.0", "electron-to-chromium": "^1.3.296"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.4_1572001675399_0.5682737658455992", "host": "s3://npm-registry-packages"}}, "3.3.5": {"name": "core-js-compat", "version": "3.3.5", "license": "MIT", "_id": "core-js-compat@3.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7abf70778b73dc74aa99d4075aefcd99b76f2c3a", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.5.tgz", "fileCount": 8, "integrity": "sha512-44ZORuapx0MUht0MUk0p9lcQPh7n/LDXehimTmjCs0CYblpKZcqVd5w0OQDUDq5OQjEbazWObHDQJWvvHYPNTg==", "signatures": [{"sig": "MEQCIHKwEh3GjsFqG2uFOiP1nU2U9YNoqmmNyyHgBj7eqXILAiBgb3NyCdnFH+WCAPFwyTmfSmL9HR8MihWkruVF2uDYKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdtyXhCRA9TVsSAnZWagAAyk4P/0J4wU1LjXPQhdvtgyMr\n+bI7qOYhtvMOefkQGRa5h0l4oyLHpFHZeLTmh8I8ReSUwvvnEUuaDAdKQHpM\nYvuByzU/F6DL32Ui7ecJOCAlkwpIO4i3+nnUbLDmQTdOsYZcgB43KTrwKGcP\nlEkLHV75YFanesEh5q575pz6HpYkVskhtci15nEkhHp4Nn3VRai7lXltQlrG\nyFkoHzzauT6el4CtMCce2Mr1BCxGVXACMCxJmo1ytmgcl+S0puX5VOnMsirO\nu3M7U91T5O9hzpuEc3wL0b9EbHfe5Ypsg/Z80wNrxfuO2mbzXV18jpo4QX01\nIRMd1MfltGl//AFxnrJWDb9Mfux11pJFMPV25xsaRWtx2K8+y4TMTCAehrmM\nVoehYHlv/TB6qgSr0vByVifpNgm3ePq96gmEpt5fqPkMRZvg/L/r9sfp9ouS\nuW3qrR6V9C3/FR2mfie8Hwz3DtqGMModwGlSmhnQJiAf2mdprRrGdLDRBJif\nqIZamXQ/bJ2qKc7vH8YtuK63sdxyoBCZ2A0aHKlMoOVDScvSI6K8Nmx0UkpN\nieIUU7UvkhEjUmA5DcFp7UNZBbQKjuImKDrgJwVry9JA571gZiHlTXg5Omev\nlsRjVezLfcgOpDVMrq+li3wIbVWcCTH28AzcMypRAzlA6lSfKc2YdKfmHOKk\nP3Z3\r\n=23Dz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.5", "detective": "^5.2.0", "electron-to-chromium": "^1.3.296"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.5_1572283873434_0.6345930257796037", "host": "s3://npm-registry-packages"}}, "3.3.6": {"name": "core-js-compat", "version": "3.3.6", "license": "MIT", "_id": "core-js-compat@3.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "70c30dbeb582626efe9ecd6f49daa9ff4aeb136c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.3.6.tgz", "fileCount": 8, "integrity": "sha512-YnwZG/+0/f7Pf6Lr3jxtVAFjtGBW9lsLYcqrxhYJai1GfvrP8DEyEpnNzj/FRQfIkOOfk1j5tTBvPBLWVVJm4A==", "signatures": [{"sig": "MEUCIBVOpvIako/A3XmCbMiW0wsmYsoXFXjHZ/LJO6HuoaRvAiEAza/oKqVg61FYisPmnan1Xl4vKJPXo5yTMqblKlTuBGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduxUlCRA9TVsSAnZWagAAicAP/0kmbx0uOc+BiPrqs6vn\nCKlrjrNVQ3KQeIw2Js7m0Q+hi5qRg7V76M4KkqP5zEmIFTa4G72pwab2v9d3\nC3RXGl+p2/IO9eSf0HvvRGpF0XeJRML9OOMuMUDZ5gaIavEJlx796lTKRxjC\njYPoZH3ZJ3xzkPBB2B/BCruzHzAIRdOjzhokbal3pr1VjzYNJ76dTKL3Ct4/\nr+Z1FhBkFWNuU7n5SLj9aAwJD9Nqknj8Phl3pj1fMqb5u6pqiMNzn/CqZxkY\njF/uEZey5P8sGODoOtCpSm3aDjYVBp/f9aAc7e4S4oEUJa9R0qPI8bh6THxu\nAmQ25IMxiGnVrGlhWu5zdcl7EXJXCkSadZFRT3prUhiN7EcdnTJW/oytAlME\nHOkL9iRi7NCS71+kNisgjloyAszmp6L34BwkJ5bMxODzO4iLoWHXywhpuj7C\n1EKnvfC/yrge6os9Doxw5OyRMfwOk55t1NCW5UKdxrXoocr+6v77PsMXQo4n\naIbO1NRGFTXOBfU1oCBWIkJAOPLpQfg3OjfRJYnCb/nyA8pPAdwsse4akUDQ\n+oy6KuKf/Ky6fZOmsXCDgYfmotFKEdxkbdVauOBtlzFfCgMTS72Y3PJMQQLl\nTCsvlyKdY8lnpxwFVZRKnLaa21kdSbFdMzzXA46jEzzpFHVXe4Ktyf+z5TfR\njmV6\r\n=ES4H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.5", "detective": "^5.2.0", "electron-to-chromium": "^1.3.296"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.3.6_1572541733460_0.5293211560837934", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "core-js-compat", "version": "3.4.0", "license": "MIT", "_id": "core-js-compat@3.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "2a47c51d3dc026d290018cacd987495f68a47c75", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.0.tgz", "fileCount": 8, "integrity": "sha512-pgQUcgT2+v9/yxHgMynYjNj7nmxLRXv3UC39rjCjDwpe63ev2rioQTju1PKLYUBbPCQQvZNWvQC8tBJd65q11g==", "signatures": [{"sig": "MEQCIHcFJqHRGy0ObdXoU3Po6fIkpqAtdnVkmx2mPtLA9UQAAiBkduVWKZhunbRVkcihGJVBG7KqK0CqjWA1hYTlw5gBBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwz8dCRA9TVsSAnZWagAAkLcP/1wiKIPuD9rVV+Eux7Rp\nItF/yqsS+WAplOyOe6rwougH37TY9eoBP0vIb8xnk/BtiPxr3IWMRl7qbCs7\nG1j74Ht2YtZqG8+ipSKLsNbGtxZqIQbJNm+V7wxL2cjMT5p+P4No+XxSjQEY\n4XUDSiZN8sXAUAVoZGcKLXDDAeZlCChcPq8x3g4Mkv+bMKPCHymsOg1RfQT1\nuA4OWRZ86bbDythUMUDBZaGVP10jV7S6WNtEEN/DYmXK+KyFJqf5WC7uGNZ2\nNpxpUXtAznB3/uSo20O9oZBGTU8sPSa+u0Mlyc8ossoHgxcktByDDVXLgcg4\n7/fvQQ6OFBc+JNaOB8uS5UqkxqjI+5VbW3PuuDd3uOoUT9NR/CzinSch2rL1\n8nuGiSikD07TAVrQ00g/lS/g78zEOjI8y4lIDkYtGYqWTNlWfiMgZrlyuigQ\naEsVVqxBa7+Klkrje5nk1+n+3nwNzx4FgcLb/tjOImiypLUVE/ySXeBqrFMZ\nsxiRp0NrnYjQOOTe/vsioukvHuxredl/Wyr7iQ50VC906Qs74pZQij1L1Xe6\nnMcvGdfBY8olqGAudTvH/pQGDDCV3woflf+erw3eW/TPBr3VY7vCH1hwHk85\nevdXF2sUajb5++8y2vWoIHs8VMzwEKp2ZHJ5QCyKF8gpcm5sD1aiyLUcnU/v\n80/s\r\n=tCHX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.5", "detective": "^5.2.0", "electron-to-chromium": "^1.3.303"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.0_1573076764609_0.6766987412055063", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "core-js-compat", "version": "3.4.1", "license": "MIT", "_id": "core-js-compat@3.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e12c5a3ef9fcb50fd9d9a32805bfe674f9139246", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.1.tgz", "fileCount": 8, "integrity": "sha512-YdeJI26gLc0CQJ9asLE5obEgBz2I0+CIgnoTbS2T0d5IPQw/OCgCIFR527RmpduxjrB3gSEHoGOCTq9sigOyfw==", "signatures": [{"sig": "MEUCIHPYEKxXlFZkzagsA5wTD//2iy25nBBUPvfPLdbBCkKUAiEA/rwqWJil2zxykcnH+qf4CPNSMwGpwEs2QXylxBTnRNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdykHdCRA9TVsSAnZWagAAAoAP/Ak3UrJBHYfA9UQ/Upyx\n3ahkIqWX7b2dLmfQC0foFJHsocQOe1Yf9FvtUcMEW/247WBQXGe5P2uAf6TU\n33hwvfloMwbSuR64VSp1ryFixLWOuZhwuOWb2mFOXmmuzb0beluycUqAJfTd\nr9hmdwDpUyeK0RWE7f33t6WVyk4+xFVZ5kKB8Bpyvp78Oa8fb8MacVg3Rzfb\nI66UXQcTJgtnE8jqai6r3MEBJxI3/ub6YZI3yUFvVi7Zl+H0eSonWDXAnHB3\nrqheMFq9SJ29A4mSmHCpQ5ScvR4xpEgqV/myfu1luryAXNlxPqfBOSshIcjn\nIivvJmTCSvVFK9885hjP0Y9IchxMZrupmff9PTEQW32iqMzVXGsWQEo3Q8Nq\n9ek+e1GdqTBZ5U6lQpLOCmTWngANqLFy7o+OpEHALyXyn1Ud1pDD6xfSqQgQ\nvBO5Yj3X4tHtGzpOYeExiyME5HsAoEaa1T++YosfS11dHTf1BFAYHE9qX3gF\nF/vMMyF3x3ziz+VljFKX3/g06LTD3WjjAC0Y3MNgFJ+6Vz40z0zw1bx6C556\n6JhrAKOjNT8IRDTB8ghWUAsmcR84+UWI683Ip2SvXn41d/5i2zDrhdJ4mLFH\nZILqeLTUUUW1a1BLxWk63LNtz5uxPHK0p6JHA5jv2J+8hXyjbpD2Fud74XKD\nKg/G\r\n=CSS3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.306"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.1_1573536220696_0.9749807500224095", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "core-js-compat", "version": "3.4.2", "license": "MIT", "_id": "core-js-compat@3.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "652fa7c54652b7f6586a893e37001df55ea2ac37", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.2.tgz", "fileCount": 8, "integrity": "sha512-W0Aj+LM3EAxxjD0Kp2o4be8UlnxIZHNupBv2znqrheR4aY2nOn91794k/xoSp+SxqqriiZpTsSwBtZr60cbkwQ==", "signatures": [{"sig": "MEUCIQCy43npHfpujW8CFXb0UY3fa1dcYEapq6gpn9JD6XrCVwIgKctx6J0GVw9sNkttsCDlrvDw8kgOgpyCJreOAj53EyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1t9kCRA9TVsSAnZWagAA8NQP/1jLp5N9WbyJW6zr+v3i\nsxqI9zVItsILaPjQ6sw7UDUzBSH1fvH8k01r51RxAfMV8ir0MQlEDoFJ8s8t\nQ4mZzOliYr5u/397vV8KxD3zyI2Nv6IOBjO6yCs6TIKLr4sygv3Ko7tJO5CM\nVDPF8QCFeqEoSPIj06CiAn7YWbZmfarfCeLImjAsiP+KXFxVx7qovdH00Mmb\n2P87wtxg4Ox2ZyGQNPe5ILpp8C4d30+GUVQVIzVErxKbXDiTqO3F9KXQ04t2\n/6jBr3faTtZZCvkXQTbW946dLRb/rjzFeA/jMNKPwYDG/EgxmMbBft25/v4n\n0IeV3IEJMEzLo1vza3LGlqVOsUCqudDG07nJqa8jUhv9XbR9VYqr1KEyh7al\n5GtvL6swKTuojbBoVMQ41NgIPCMCJk5MQRIVlchqZkVgU1Soi5pLy7p/Qay6\n7MS8VdQXhJHvArvSlyFbuSOnMku6d9HdIp93i4itO7vle/8yqs4dOb1lr6gY\n1h3m5jREOC6/iB+tbBungCY7wNl+XJoIY8nJFAGCxquEn7cHbexZUPG+X7dd\nQjx31PVMVNCTKWbIV/kR6I0KyBLdr4f01y3OxxSVCN7knfK+foaWQTWCQvwQ\n2ZpNuuhOpsjbPeWv5TOivNeMeuSuPx6NzUrz7YbX2ttjiVuMRdx1Nr/2GOTy\npu/A\r\n=l3MG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.308"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.2_1574362980348_0.017499264780989288", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "core-js-compat", "version": "3.4.3", "license": "MIT", "_id": "core-js-compat@3.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "54f2c5cd77b8b5acc5048904cc795722c9d590d1", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.3.tgz", "fileCount": 8, "integrity": "sha512-HgW7pxjAdq6EMxT5uOhbTfjgZo8fvWn0D6QXVlWs3z1Eerux7tRrJ4gcgnZNn6S0OMPHlClJuIEPp0hnow44Sg==", "signatures": [{"sig": "MEYCIQDvcmbJnWQ+Uxg6MAnCFCTbu2C4Z1eP+Fpz1s4zFOgXoAIhAIr10366uY7Bq2vyql4Xq7kVeW9InZ2NpzlI1XFmK9mu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3TDICRA9TVsSAnZWagAA9gcQAIRxDOGCuDZYNq/9b34A\nezoo0v2KOBiCt3bjMbg6bXRlIJ/cToCRnyxpSKTYqWudHl6NTFmbbGH56lrX\nfMbNQsN36R2GHGLrKDpPxvm8vrUHvZ3soafy/cdskyq7Aj+r6Vp20SYO/Aid\nR7xzflbdmYFSiBcdSWU6rQSca0UCTTyE0/03CxZcxs4In2jP7pMgfpCUu6gQ\nJ7zpZ/QsHcTBBFUJyo+r+CUqK8TEFA+J+Rt4ruyndpBIg43nPtymrON53DyI\nu1tpc65ylRWwEo+Dg/itQkgTy2aDNzAesNZ6eLHc3HacZBvEAoIOTRyLONZE\nkmuaNLtBnOWFR1SP5RM5hPNxHax1tl0STGBnTrijOxNjtnUKK0bb2UumCdpz\nm78p7fg+Rf3NBf0n+J7SQj8yWqi+4EULNTE0qg5g8OuOFUGGXVM70CXNgO0u\nw6NqjJf61Ys23lH4pFs0B+iLJN21Xm6gofGBhwmMKL/r82h0vftLsBA9Kf9x\n7AvgWGVDPi9AYYngEAfSYmODC5RjpqrvNq5He2t1ahzzoaL11T1PRVQ8rjGA\nXg2cAjxZHrOm29o2ldNo/CWxweGxVDGZdjtt8DK2cnGZBksba+BsyCV+pzc3\nM2dWiu+wRzkSRk0+7unl0+IHXu7/eejz3H+GjtHuipcj7JMIbNRnzqCIJCCj\niBSO\r\n=OFgb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.314"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.3_1574777031387_0.9774016556666627", "host": "s3://npm-registry-packages"}}, "3.4.4": {"name": "core-js-compat", "version": "3.4.4", "license": "MIT", "_id": "core-js-compat@3.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d0aa01b1d626e129455d51aa94c27127c564ba2b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.4.tgz", "fileCount": 8, "integrity": "sha512-hBbxJ6sOkaFSvwAXjJ/t/1usGIPK5gTbtqV8+T+u1pZBkgQ3lDQgAguN1g01PJ0b3eyQxgRIEgxqGoviE1QtIQ==", "signatures": [{"sig": "MEUCIQC/PEr2yZ5nJWczIrDEX8Ppka2tDcywAF8kefBvQEmtpwIgYRwueIPgEHr0j7fXi8gHWBgMUWXrsRgQoiFqJ/Z/MsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3hV3CRA9TVsSAnZWagAA3McP/R12Ms8aUc3xmcbhedxK\n9AL4AJL+1102Amk7a3rjRDvKwNRX8n0jtZPy7jaabI1BtgLi5mEZnIXGR2vv\nuvA6/EFZ3wL2tBWPvhHYmGLJgZ2clSe4cSPcmQ7BpJ+UDJMELjU1e2gQ+h9y\neMTGKQ0NROHKXmTUxd9ZjZ32i+MWz+JnWL51fAqIY1RNgbKykv5oPXbtHWTC\ntQrTU+Ui3vHQ5DbdOot9MabULYEA94+jTBfPvqXSPPsO7hziQ7e4SQnAHc7R\nXW+z3Uqo39J4A06uIHC+akd1TzlR6jAo9t0RIF6jIMrsuEgMyEd4xep6SA81\nHdOs5ohHIfXhPO6oCU2Qxz3bwiZz7R0z0kL/gDjCFEEwTeWIleVdw6zgka7B\n06sV7XGSsnqfkB7M0lPBkNEr7EiqkonKzcUy0phqVRgn2o2pCCRrQovwOqg2\nlBn2QEaE/tdoU1+T7uvGK6nlKJ61BGq4UQ1X2wRvvkBqqHdxhv+vfEsKmroO\nSktAc5195rbkFBEp41qqbQl6b4HzINivR9zTKFp3Gho/LVvWers65VGdH8HM\nDB0WN03Of9E7C0fIb1dF7e5ZnB7BvnVu1oxKndkxJ2qNhr7NleZUX8ArjOR2\ncxLRulHy0OEFp8pFMO1IkOI8V/i8ChuIEaZmnnAwjiHXPOVlcaVpLAUozxqn\nCgNq\r\n=XaDe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.314"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.4_1574835575239_0.8105406665240302", "host": "s3://npm-registry-packages"}}, "3.4.5": {"name": "core-js-compat", "version": "3.4.5", "license": "MIT", "_id": "core-js-compat@3.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "f072059c0b98ad490eacac082296cfe241af1b58", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.5.tgz", "fileCount": 8, "integrity": "sha512-rYVvzvKJDKoefdAC+q6VP63vp5hMmeVONCi9pVUbU1qRrtVrmAk/nPhnRg+i+XFd775m1hpG2Yd5RY3X45ccuw==", "signatures": [{"sig": "MEYCIQC17TK0JhThLNlhyU6SMmZEf/mbcZG74hCQexiGRg2ZnAIhALfPQCwvaTsVvAELsVm0zDmiROuvyyutxElpuQOjhOKN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3vOvCRA9TVsSAnZWagAA1tMP/1UHOn2XwPfoCSbkKC9j\nhGBqoWGBeqFGp9ThgzT9Ox347G4mC7ufW29zcwS8n+gHC6rR3FV27oWpM7Wy\n0IRDCz4GiEaEKEVERKntZwIeOQ9Wmd7RgaTu3s1buC25+B/Yiwb2DrcF+0Dr\nbM63qlFQsUhm91uiRtQd/k4FtfNADm5qcLcUijxNZcZzp3zG+B/Lae+W9KQM\nQP6lvLsBSXLf9Nh5cB+QY2LCutqyqgYS7gq2SUPSwX3E6Iq5Wlx6IxB4yLDW\n5BrWo8Ik2B9tb2S8SYYUZFHN9w2HiGfBeR3HXV0RUKlh3iIL98sfyTQWa77o\njo8amQAUBxNQV63iODhz2IMS2BqJf/B9Gj+yrDbkE82usb0IPGlL+5wGHU5/\n8cWpkd2yAAyeM77Np3nocdZxa+HCnHjpXoqlqs6qCO09tXPmCLVeLB2PrV13\nMWljuDph+0rGIin7+R3mf6LrOwgTHtz9E4bUVyOi0GvvY4flvyVWJlrGbE5D\nwUa8mgED9NjhT6S/w5on02MgD498DyuYYeKW+fYY/F1jRVcItVI6zW4lWRlf\nET4CwoF/a9e/ZoC2lqHZSHrS5wbVpgFzGR2mgJwkdm7LNypHHH6LlszXm5Af\nO2JL5d3kZxH5sAWa2dqLdL5/kPy+JuUPIwB/w+Arb43NTBMcf9rp8TTtoA8j\nbn3z\r\n=B+lU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.7.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.314"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.5_1574892463199_0.773469987728731", "host": "s3://npm-registry-packages"}}, "3.4.7": {"name": "core-js-compat", "version": "3.4.7", "license": "MIT", "_id": "core-js-compat@3.4.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "39f8080b1d92a524d6d90505c42b9c5c1eb90611", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.7.tgz", "fileCount": 9, "integrity": "sha512-57+mgz/P/xsGdjwQYkwtBZR3LuISaxD1dEwVDtbk8xJMqAmwqaxLOvnNT7kdJ7jYE/NjNptyzXi+IQFMi/2fCw==", "signatures": [{"sig": "MEQCIBF0zqlZuwAKs5J6GZKkgqjcwZT9+2hthek4wlBjUs2cAiAIIjI58V0/Dh2LGajNUnacRsWr0yqgsWln+Ap7Y4qFew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5VDFCRA9TVsSAnZWagAACAIQAICgpdCZiABzZOZDWw8H\nBYUxRCdCSpw4UHHoqkbycPfzKxGp1Q7jDSAuaOu1ZkQDYHi20Nr3FcuBnum+\nRMk/6p11MsyvtPcbnzjPH4XNm9LgOKyzFqXf20oXEre3nMeFAFz3p8DRIcdT\niesluk073fIOouCENAnWNUF/MDvdaREtagdsZGYgAf7ILbfiAe3+EZQg6dh/\n5FmE18zdbR4/G+6iEe8+OASsKr+AnqRgldO8GSjHGiS854Z1uHh6dLJOWWYF\nUP4cXXJm0b7hbs+HhZCNfpXTTEfVuqxs9zcz8IFJP+/c+GPOC5/6p8Dnfsyq\n4vPiU2NFyLFRfwcpjYeFdA001Q5wIhob4CZWV8XQxJmjMWJqhPuhCsT29/UY\nxBaITOYs8Dbdb1pCCp2jgPBgji4BBjxXnMszMKg0hCDBHhzNd3UPSN0eMgFK\n1HAjb8knAHxxxM6xoVqbW+3/hd+M9wSp/d6ntBxgTCRurZmzhNhMbcRD9JiZ\nYUqzUAdGuvrvyeif3bM+DWBqAhQLhRnXe/tzRK0m5iRM3Sb9XlHwL1hOsXwn\nCm/Puz1mtIZPEjlJ8vVIKSi6uwZ0ywYtI16xleB1KNPf2+ALp8D9dB1+gn+a\nMjLTLyglTZ/neSy1pCZvtl7o3BJlIB1Uf7ziSk97qTMMqPNEdo5cqcwQBsDw\nlahF\r\n=2j2l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.321"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.7_1575309509381_0.780216213498659", "host": "s3://npm-registry-packages"}}, "3.4.8": {"name": "core-js-compat", "version": "3.4.8", "license": "MIT", "_id": "core-js-compat@3.4.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "f72e6a4ed76437ea710928f44615f926a81607d5", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.4.8.tgz", "fileCount": 11, "integrity": "sha512-l3WTmnXHV2Sfu5VuD7EHE2w7y+K68+kULKt5RJg8ZJk3YhHF1qLD4O8v8AmNq+8vbOwnPFFDvds25/AoEvMqlQ==", "signatures": [{"sig": "MEYCIQCF/fJjTzHOJnUaS4vm61Zjx2yzaisFisJcgXs3Wy7JjAIhAOpS9EUwVAE98NB7V588OKb0VvHoKESLijv+fvO9TMCX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 294411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7WfACRA9TVsSAnZWagAAAQwP/jAeUzkDzuau8qThHYYn\ngvyo4uU0xiNI1XuJinFjZholfZzPLJEco3C4N0lvcRNO163BCL9qj0jpLO8H\nyYubb3IQ/iaQmmSywvf+DGNgXgXndxLkIyFAs5sEAB+QGVGs3dJForCCctzl\nPH+YGJDzAG2Udun8kTWyifZmaWZ79c64FkoZyvf7i66pmnzOd92PBfqj2T4H\n6Qm4Vz+AaeKuqxakQ2bRvMag7Nk0azM/1OUIVTCI4BB5ozq+O6u4n5kiMkNQ\nNDmwyc6ccHgQlcp5EWIQoLl9DuLrNQY1qJdLCbez8hLuU71Jw8UZJb8659q4\n3TIItlj8L8/jJ2WomNUgbUKIP7n1Ml8tp4FxVrVUhV9IUi0q1VxKxKz5L4r9\nuFYGxiubJMm1uekbuoZyIAV9I0t+BrZ78CricnStadCPAai8TxETBLU0a3+C\nEGVARyJ+NG4u1ZirMxaJt0pWGKuyFzj4+vAKCUWdqgYQyTHmb6r+KmeyLB9M\nTAxuLZ1ZmMAuitGvGfNaQ1Zd4B2IPM1DY5mBKlfXcS7+/4XuF1lcrXEH17rA\nCWVVVetPiAw3iKXnk4o+2JjFtym7aWHlPuojIEY9fEbFiuOOVL7/7RKKV3fG\nc07a4efX/Ki4kpor9KBJWYjAqOSEszOF7e69AJ5SG9kfQw9Ru/fvcGF9IiUf\nTB4C\r\n=dObC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.8.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.322"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.4.8_1575839679737_0.857226362927773", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "core-js-compat", "version": "3.5.0", "license": "MIT", "_id": "core-js-compat@3.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "5a11a619a9e9dd2dcf1c742b2060bc4a2143e5b6", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.5.0.tgz", "fileCount": 11, "integrity": "sha512-E7iJB72svRjJTnm9HDvujzNVMCm3ZcDYEedkJ/sDTNsy/0yooCd9Cg7GSzE7b4e0LfIkjijdB1tqg0pGwxWeWg==", "signatures": [{"sig": "MEUCIBuUKlS9PDZXyfb5CS9lSmEqWHK4tyYg6Tt+3QCnqiHoAiEA6ieEYU2vcMnguMeKLl63nKQ09kTHyXGHZd2sKxyZnN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 296286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8ZELCRA9TVsSAnZWagAAM/YP/Amx3wdjXHzB7/xqt/Kj\nsDFGX6Ms2LH/DdikBtfz/c0vSo7Eam5pnLsgsj0/0gH6MEV56eEogHIv4LW8\nJSHaBS0f1VjcZJRRmHfviSvn224mayvQam1AG7M7lDRDk8eGYFW0AeYxmqAP\nGbtw9SQFH8V+OlqYS3+dUCy1rg/vQ0SPg7OXAJoCacnKXPozo4bjB5JTkDZG\nuk+d1hGk2xNNT3AyVmsUeIXUzWjCHh4PvUNl1ibnUJQwMJO2tunYQk9+pqLD\n67EYhM/J2VYVqOfutzplr/CAV4OKwQKXcxTQSn0wXvaCpY8mOEy37MxNORZ9\nFx0FQ5r+uj9Nv/IXkmZFjAxtGIJ+outuAM+GPJZXyY/jOqZst0jH3+isZsfj\nFcVNbnhc3sMFC1XoSwL7DZGi/8N3DK0xSab8Dt//t/7ezgQDjPbgGL0H9aB1\ngyiVO7Fj0c5YpMgMujJb2jG5TyJ2m2/J9Lx8DZgu7Q+E1ynUGkyrBpSwjRQL\nsAxFx85f5JNgJjtzKl1T8oWLsleONicHqwj8/TgvuXEM/cySF1WreTGDXRAM\n7t4Nij1TpjBIFnHKcZZM1to0wEeGIYcIrMaDCjSjogjr6xisPeGHnWH7hTPc\nCJDWm86yN/yg/y9KbIkA1D6sl5oauntVTmOD+hamWIrS7+eCs5+98nnKUXID\nPM9e\r\n=+NDl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"semver": "^6.3.0", "browserslist": "^4.8.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.322"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.5.0_1576112394724_0.5941835299389768", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "core-js-compat", "version": "3.6.0", "license": "MIT", "_id": "core-js-compat@3.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "4eb6cb69d03d99159ed7c860cd5fcf7d23a62ea9", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.0.tgz", "fileCount": 13, "integrity": "sha512-Z3eCNjGgoYluH89Jt4wVkfYsc/VdLrA2/woX5lm0isO/pCT+P+Y+o65bOuEnjDJLthdwTBxbCVzptTXtc18fJg==", "signatures": [{"sig": "MEUCICPeEtCicgvfGEMAIPUP/W1zFYJvrzWmw4LnfmbJHk3+AiEAu3CiWygyqJpo87oiaqjeZDJGnfmJsVvyqTl7EfVacKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+rZQCRA9TVsSAnZWagAAGGYP/2galtAf7W0koTJ4pReR\nung6XOX/jVQnOHOvRAGTANkkhdUPJDhUfYZL8Uh79/qku0eSBeWVpW40iNCk\nNklw/NSCJu9YPHS/tikGuBTAZlhUhlVrtUfhD9Vl69/zoesYfyAn/uHetstd\nGYXWw/Co1KTNlkXuzBgxuMRZrkzhGB/QcGswlnbPlatozyOZrqmppnTHAxPP\nzM++T3EanCgjTTJiAuJRxexWEJSCQ4DjzIf3+lwLLfHLiWRFIMLtKFK9VC8E\n6QuWX1yqHXEyrKMrToXexXFXAhZ7A52tIVa6l6PIVBrWgICpbkkrtz/RaJmK\nDa2LWIdaSkJxx4CTr9H2x4U53V2UJjag8X2SAgtf6VGq7s57jvJO2p5J5/rA\nHLTULzV6P4i1KW+eEG+TatsuAM960Y/rHPUz+iY303dilcEGOjQPahWPeMY0\n4t6Ct3Y65U2VBorWrFoeqGCb77/WEqqneSGD3V5wog9w79rdImZs6SVBeis3\nk/5oK/avA3KmqnpzQAtHaRRy0EWKeBBjsZTBIsxDvtxz4a6uZXsEC5rl8XBO\n5LiiMeztavUi5BKX8UEnvhuzDiUd9T0cqEdQZIgf6js7Nc5XRSvtdrrxmZKZ\nuPJ+YN6W1SYgnN/3dRx746QE3vrVCccBn9VavJtRGdyXZWmRt07n8ahNzmRG\nAyyS\r\n=o104\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.322"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.0_1576711759869_0.9929416424945838", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "core-js-compat", "version": "3.6.1", "license": "MIT", "_id": "core-js-compat@3.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "39638c935c83c93a793abb628b252ec43e85783a", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.1.tgz", "fileCount": 13, "integrity": "sha512-2Tl1EuxZo94QS2VeH28Ebf5g3xbPZG/hj/N5HDDy4XMP/ImR0JIer/nggQRiMN91Q54JVkGbytf42wO29oXVHg==", "signatures": [{"sig": "MEUCIQC/mPqnRx5xbDr9RdZZWv2/Hl8sE+xEDadlGaAWRBwzIwIgFfy7oEZHv25K+fqCwieByf0IL0sGHZvBP4k8j6GNOZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 298977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAvxTCRA9TVsSAnZWagAAAc8P+wZpwQ629jU9bnPPkSpo\nOv5TRZgrJw1trkSvqP6hcGz4RMntBzDPHzqr20XYRQcDDgtz7DAYjt8RzObQ\neGrj8x0GP4kx34ssd5iOAaf5MrO2udIy7CjdWgzi8GC9j2TwLL3GuFusCdKA\nZva/6bXM/vPVWMI9t7syN1WA+tCdbK0PGRcOZHRZu8KiQ72BUc6uT7zT7yaA\n4OXhFb2GNKIXOdOEnuwRYwRaim7ohsg+Fa/+q2tEkhlWy6/tof10Ds7aaU/z\ns751naf1gTR6HaL7rupYkIVyelK7fI00S4lXW/DlKifdRIwdwaAsUpam6bMh\nniwtm8f9yZd079AiI+pXTN5iOM5MNj0/WF464EYJQJkTESdQsG72s/vBusxF\nDcjKOdqPBo3CF1l6iq/ELXkmgwI764DEWnX0wQdYNDKJdaFPCV1SBdHFv0UE\n7SBrv0vYKJcf8QrsvXh8awjP/gKdabpQRpNxK/XxvHowdtYEJKfABJ6jXGgp\nS2sf8yFwxkXc13WnpsdqRQPveoS7zR/oxsbv6nLXR+g8+pVenaS0kGKtwjJS\nPkjlvKxtz46rx8h+LQepHgMNbMc53LBd2igPQVwnrerda+vWZ/w3LBBHJAEh\nFeaYXH2vPXh8DzgRdYl4LzpV/Gjl6CbupcZeOTPM0uZo95e5p5TNoszls2Dj\nF8FU\r\n=qaz3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.2"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.322"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.1_1577253970648_0.2977187989523076", "host": "s3://npm-registry-packages"}}, "3.6.2": {"name": "core-js-compat", "version": "3.6.2", "license": "MIT", "_id": "core-js-compat@3.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "314ca8b84d5e71c27c19f1ecda966501b1cf1f10", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.2.tgz", "fileCount": 13, "integrity": "sha512-+G28dzfYGtAM+XGvB1C5AS1ZPKfQ47HLhcdeIQdZgQnJVdp7/D0m+W/TErwhgsX6CujRUk/LebB6dCrKrtJrvQ==", "signatures": [{"sig": "MEYCIQCcP3GF1A5lfdYvoSVHslMNipj8Tb2/96nF4ftJhK5NMgIhAPgOy0gWiaMemRHnDBb+UZXkjbQxoUiVktLs7FjL7wUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeE3ASCRA9TVsSAnZWagAANBQQAJ0as6Ueqiet2GEWAGbf\nsDeBSOTb5QqouDFu4JKho8wNwFh0J0dm1m2XT+G3BxbmaJh76WcjtBhXV34U\nmOvPE4o2tMCc61ck50xfxZ5jpWqzJYqU0Xdeh6nPAFvWkn8LG9iHgj4UxpQ+\nw+a68OcA0uPdt0jDmX8odOlUoLZ+VCDho+b+gWgYMq1rJuez/FxOnh35/fLe\nWWUTCoHPk3dCIT0i0GpTTGWfe1juOlg7Ht0lQ8QVCMzRZhYaHq3hf+kyOTST\nSKgyYVBxr9VDXCq5J497c5ZjHBSk1iP59lJGYbNbh1R17TNfD+IMLn21kB+z\nJ3B/kw/DK2jeNCUli7Dac2Wx8s5L+phNe7GKvx6OghEQelM7lKVAOYQomD0D\nqBcyOEjtAaYb+rG7TiYYFGRMaQj1LBnEj9suXrCueqOxOQHm8A66E/2OVyUE\nmgu/ieiVOmHisAuoOp/y7vmcTBddtMzLJZTByhXw3ajLmx6JE4di0tz2mLVO\nGbmrfxCWLP6Kxr9+4AHSvqFMuvNSz0VV573YqbaAP+AlBjfacnQ1rUfcq5Ao\ndUGXH46muAhcqNvcJ2Vib+iv9PB4fjvOGwOjfPCiHK8slaylWJeGEVrvQL6v\nAFM705G8ejUq1hr3twMyxFuMIVWB1yBykWmAI6EhrmE4dZwZIFXF3rv/bvwH\nrXgg\r\n=PMHG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.327"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.2_1578332177781_0.3768001884212371", "host": "s3://npm-registry-packages"}}, "3.6.3": {"name": "core-js-compat", "version": "3.6.3", "license": "MIT", "_id": "core-js-compat@3.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "41e281ca771209d5f2eb63ce34f96037d0928538", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.3.tgz", "fileCount": 13, "integrity": "sha512-Y3YNGU3bU1yrnzVodop23ghArbKv4IqkZg9MMOWv/h7KT6NRk1/SzHhWDDlubg2+tlcUzAqgj1/GyeJ9fUKMeg==", "signatures": [{"sig": "MEUCIGesEdK/0gKqwUmGFael4NHD72KPUkCAiJu9Z5OMGHs+AiEA5VYP7odu25Gs06DdzkSTId1xfdM6CiNcWq1t2wR7a0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGQeACRA9TVsSAnZWagAAqUkQAIsVGwgp1FYW8gkCBAlI\nF+1W+SrpGOgfIRoGb4xYyLNRLW5/K2K1UTITaFb5JPwpR/GGvVVhD5HXK9Mj\nSpn0CGIQfmmBF7wdz9rYJW/rV+KpmTl+Ue3yFT5YSQDjPyxMDh/57v8dleTV\nmde/ZxV6F/DJUGTjGrisIKcMrdN6LM8wW3XWTWLVRpPM+bP8Y7ZjHwC3xLaH\nk3duDkV2uvFm8P9zI6r1gDxKW92HIupRyab+my/+feXNFLAB4bSie6DkBMuu\n4AuHV6ERQG5KKAzVD0TVeyGPu1narOSTIwnJjhH1ZZhMdHTLfAtNrgNUpJYn\nr5O+3IIAZbLPmCW4Adac6XcuCMiOQ22wvf+s0zpinqhow0W5/LEH1FJPY68F\nCeS3beq8bgdHShmIgeApB5rtgYKhzjxiCORhYARaODIjos4MCSo8DlIqi4Ma\n/bCXlj9TqktulfYPKURZqcIqEFc3PL+2rPeTXU5DSO8Te3ucdSJHxCVDuLP2\npKN3wX5y8oFdp55HncCKyyrjHG/8JkvUUMhw0/sgXiGEwmyXSEiipmo6R6hI\n8Z2CUltpmHHbKuTnKOtN89t0aD+BZ8NjlU3CMW8jRo6JP9oN1Fhsq4I3uZ5K\nxNpMx/rLYJ98gp5/g/AjTaQF4SnDbprQ9CgAVFHiUddWEmMHd6VyvpvnJ5nf\nuBpv\r\n=lihs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.327"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.3_1578698623979_0.5822535802529769", "host": "s3://npm-registry-packages"}}, "3.6.4": {"name": "core-js-compat", "version": "3.6.4", "license": "MIT", "_id": "core-js-compat@3.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "938476569ebb6cda80d339bcf199fae4f16fff17", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.4.tgz", "fileCount": 13, "integrity": "sha512-zAa3IZPvsJ0slViBQ2z+vgyyTuhd3MFn1rBQjZSKVEgB0UMYhUkCj9jJUVPgGTGqWvsBVmfnruXgTcNyTlEiSA==", "signatures": [{"sig": "MEQCIBShLX7GzHAs6lBLhsbOA7KtKYnxqmfccsjhRo0Mr3uIAiAi49Pp96J/QET1sxIjv3A2PvNFw72Bqg9MxV+VVM4hTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHPj4CRA9TVsSAnZWagAAZ/0P/2RsZFUlFlIVIYJqPRXt\nezUwq8PUvuqrbwgb0RUZicAWpQrBeYtRHuDe5U8QkMLKQjOiq84NC8aQgESN\n3fs3AzVK/uxB0o7704gcA9DZ9vIDzLFyfB4+q8acuMNb+v7Lk4Ws0Syo9ctg\n2j5qgwec0WwWS68pmVg1APHHCZKFyqZdh8WborEqDHiXT6e94AsAuNocYLIO\nlK96bz+HStgaLuxkSZuQQtwbqGXadBap3yGq/Ajgko1GAdJVBtPCMzMdCo8B\nb2mG/repFvBxhmciP9ui9Q3xnYd78voOtktsyD09zKt1ohhUjF6cCDZwy5Ma\nclbTq0mta2UeAm4RJTAU3+9mgwuwYsb7kcMIQ5oUyfZd5krZ+ijzZFs+MGy7\npB6ekuiR/7RtncjHDk5JYcz1qGAj/9+5ti8XXhz53QzbGoQqGWjbN4wV45BP\nIyfnkPj6c2A+DI6U3u3Nipwsd7oQlA9OKddiEM62L0H2RKGaK4VRbT57AjTC\n1V3lY5vOpeWGLRBhzSmEQjfU7kwVkk1O4lk2Ab47wLfe2d37m9S/flFmVzb6\n4Qd4cAIq026RgxTfK+gRyXhBjNFtPzFx7S8Xlfy7d0T0gTHJ19jmJHm9AKpb\nICXNqXuY88y8fSDnkt29N3goRJwuk3VPWb8EBfK+E07uaArlYUGVMvNPjcC8\niafR\r\n=YGgS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "core-js compat", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.332"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.4_1578957048539_0.7577059456344721", "host": "s3://npm-registry-packages"}}, "3.6.5": {"name": "core-js-compat", "version": "3.6.5", "license": "MIT", "_id": "core-js-compat@3.6.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "2a51d9a4e25dfd6e690251aa81f99e3c05481f1c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.6.5.tgz", "fileCount": 13, "integrity": "sha512-7ItTKOhOZbznhXAQ2g/slGg1PJV5zDO/WdkTwi7UEOJmkvsE32PWvx6mKtDjiMpjnR2CNf6BAD6sSxIlv7ptng==", "signatures": [{"sig": "MEYCIQCe4c4BiPhVYB/RbaU1P/bSYjWsTzu7NWlwnGyagj/UzQIhAJS2LZdbjmfPHU9qiWIJhqCm8qtMFDPkZY3r1ouRPFUu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 299586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekJvNCRA9TVsSAnZWagAAz+IP/04kZrGXSe7Q2G2nIq+R\nj6LI7C1OKz1CK9HHvMoHuZHCZSsP26OKxU5Bi4bD6cYZMdyihRnYWS/+8bXP\n4GEqQ5caWopKZgRtEuJrwNd69Wm8unJXGCgNaVFvWclvY5MR+ifQB6+fMkvM\nH6K3N7h/NvNGKN5TneDpd+RpjVJtkyM2pmN7IXIlEWPH8mlEJzzKn4ctS3dx\nr16QAgRHGsKHv+VgA/FmlVvnK3MjBWHa7hXVYi1Fv0nGahNJn4CqFBeGtOb0\nineXwW0vI6tbuqS6aB0pYjV54JTDJm1sIHBibV+Mz2HfhixO2Mf50aVhTyac\nj7SBSVVIVuZN3V1GYBJzF4LW+B5+3YB3dZ/v7BCBtXIQpIsptqXLTuB1Dicz\nBKiPaqEyaNE4o4GUTScmZoaOOAWbHox0f7TMPPIU5ZiKrps62cHxl1VXegJK\nj2T552s7fsE75vfsXlR46XusypuBtJZfLIDq5VJrOaqddsU1BSyJmgytrW28\ntIN9dJGyac/sYyWxLy3uZH2AVyaWTzYfgpRN/3qh2EbJeFL5NoJgUieiDLSr\nqwoDs8kVAJOt3xsINRSl/sAhFv10nzprOb8rMiYLgS2mwpn2VzmTJKD9RYvO\nKhP4AQQ6r/ABzhmEwKQTiy0nsKuGgn/dlErM86kt2MG57DueGHSu0VmEdVuh\n5YeC\r\n=T1Aj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"semver": "7.0.0", "browserslist": "^4.8.5"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.332"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.6.5_1586535372829_0.6257533398408435", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "core-js-compat", "version": "3.7.0", "license": "MIT", "_id": "core-js-compat@3.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "8479c5d3d672d83f1f5ab94cf353e57113e065ed", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.7.0.tgz", "fileCount": 13, "integrity": "sha512-V8yBI3+ZLDVomoWICO6kq/CD28Y4r1M7CWeO4AGpMdMfseu8bkSubBmUPySMGKRTS+su4XQ07zUkAsiu9FCWTg==", "signatures": [{"sig": "MEUCIFge/rmZ0YrI7tI6Z42NGCjEkFZrE62YjzXUtzYeZu/2AiEA2SZ+UkQnyduhHJLTGIri2VXuw0M3AvtRqRmabaL2qcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305566, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpWgCCRA9TVsSAnZWagAA7SoQAIQKN5TgN9xzyaPMcX5t\nl6pJ0ghcizI0F/caaj+B/4lxRyojHk/NXcsTv4JC7JlZZRExnY55/5CNepFt\nn+4zbf+W+CCCfkPe4jvi2Yy9gANVihp93Zn+LGqjXW7AIJLeMAhqnYcBY+Q8\nAz3b8VR9+y5HBfnPsgHKmQjsC+G08wUMyG8Y0YexqdSuEAqtnbz8Ov3/PAkc\nXIkz91+zx4qlhbvdkPr7VY73tV0v3ypl6VnwROvE6jqCno+5CtrjfQ6W/tGJ\nrx+3yFBp4HCC7Es4vsbarkQcnpRCL1gAlphFvVcr17yp+gDZwp1IpYps/IjW\nFUtxVCNgjZLrH066PH4M0us8VfunDD1H9R+3YsmAlw/CwC+3AL+YFD+XUKwl\nokScj4Xrv9yUIysL51YSo8hdTtBi47JgyffHwqSTqb9a/yJRMcrzqFDciD/3\n4EkZk9zZbLJ2oIFiegs2V/lZaAIkC9lXLcFFJvkxw/XbPtk+PRwAnXeKhpK1\nSlXAOUoBHx5EBaZDg7D1Ae27wlsDHbUta2jmO+QdK5T7ClsPadfnULdb0f43\nuPFQxsgI06p7avGwGe58c6rK19KmeBiCiDHFMPOQMkmTyetJSdIGl+kEsEvp\nFh/uoH1RsNO7oBRWwrtNKOshUdkf5njLntemuh51DXY/NBmH5UJONNmbMWLE\nZ8Hr\r\n=R2pV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"semver": "7.0.0", "browserslist": "^4.14.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.589"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.7.0_1604675586041_0.8940709077830244", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "core-js-compat", "version": "3.8.0", "license": "MIT", "_id": "core-js-compat@3.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3248c6826f4006793bd637db608bca6e4cd688b1", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.8.0.tgz", "fileCount": 13, "integrity": "sha512-o9QKelQSxQMYWHXc/Gc4L8bx/4F7TTraE5rhuN8I7mKBt5dBIUpXpIR3omv70ebr8ST5R3PqbDQr+ZI3+Tt1FQ==", "signatures": [{"sig": "MEUCIQD5H+Obd25+hgua9AoO7dFcBq2BYwLn+AACKIPL6ppbAgIgbjZ8++evCJaB4D+v9/V0pGDEkBrs4wL9w4ZaeC9acqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvqB/CRA9TVsSAnZWagAAqfUP/15d0G+bd698nzuomG3N\nsQLs5gpYkRNuxV0twgpEhR1MybHQL2ka2d3lltfS6jSVH2aCmj5YyjanMdv0\ntl5nephytxcegxcWUB4NSndr3pQiMA/k5MzqlyNm7gaILYHA3zQ43DUZmEiv\niEr5khjYlSK4pJyX+9ooiadfR5tm5lxY6Slh2MEM2dEuF1FetVICLjvlURX6\nxfB5p49MX/Oc79Oy+tV6v+XO/8Cks/yiXaxIppKJOpF4I+RO2hQvEq86hmy9\nBIWxxVD1e/vsp2vwMQp3EkqCt5UdkZeWwcVYe4rgK9LZsqzUyNd+KCqN8+Iz\nu7sN7w3U4Son5Ph3aG2MRn5rQjMwXMJ+7f4uLjP5X+H5udC32f4OsIyh3gPw\n/2EAR/OU8lYmO3YPzxfvTtPeuGqkAjZ76IWNib7N/9fZRWJxQNL+7i5Lzoo1\nFBEkkMqjNly3uSkiK6eP8EKeDH5wjnLqRTT8tHQTTabKoQGFd7A1IRLR5fEc\nTv56GRgSbhznYFyp7E5QOkn+BkjNCKpeBO+B4wO3+TrRZkGj7DA3NJGkaeVc\nH2/4Ee/BWgtc3qlp0uwZrpMhyEKytiLrS1Kic559b5rRToKfnT/EIyrhPXWP\nA4vxXuj7mVAceSKLImd6SSF88/URew6YfO4EP0zdlXlb4ABmp1axpqrjNST1\nOsyk\r\n=N0pp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.14.7"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.607"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.8.0_1606328446932_0.38879095518521045", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "core-js-compat", "version": "3.8.1", "license": "MIT", "_id": "core-js-compat@3.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "8d1ddd341d660ba6194cbe0ce60f4c794c87a36e", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.8.1.tgz", "fileCount": 13, "integrity": "sha512-a16TLmy9NVD1rkjUGbwuyWkiDoN0FDpAwrfLONvHFQx0D9k7J9y0srwMT8QP/Z6HE3MIFaVynEeYwZwPX1o5RQ==", "signatures": [{"sig": "MEUCIAyLlQIhm38Wnd1oFXYQ5ckd64G6Iao1n4PjX6EzNZIHAiEAlnJ3eYv9joy9PeFh05eRAb0/VzN1IRivOkyeYilChyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzDJXCRA9TVsSAnZWagAA+68P/izSngmTobYpoh17tG7a\nh6Pmu6L0S4X7Q8ynnJ2eVZ5scHil1dqpG0+b1ZTVLaQS0eWzyftmxboy5B0y\nQxQZ/C/SnwnSXR97/pIFbwWzJ9g6ORr/a67F5W4n/tCKKhAb4/VcwFOQ6cLe\nEHgMBrgDfsyNvrQ9XMascY23+zoG4ZMLqAfbNZMpB4TQm7aTgYJB7E4qJpR7\ntG5TYnWBSUGusklqv+y+AQjD6HuVgvuhERPSjIDdNw8v8Q5Yb1L6S9yQY0AY\nmMIIGGaSPSGHvyZbEcB4XSNXqwei7KbDhPgDKgVUrE4Js9iZF4tY3Wnooo0L\nAGGHVvGeojzGa+gxshPIjvnt1Wugiv4u7vl3HIVk1DmMhXliQTYd8wSCUR77\nMs3POwCNGG+lwyjlPlDpMTulx4qKrCEMiF2GXEu6rSc28yQAZTkzpKbTtk67\npRUp/1kri5PZLcVgrMlSnLYWEf5VlChrnlfb2qwwJ2ct3yllzO6rh5EUO9Ow\ngxSf/cVb5B5s7fbsdDmujs4wzV6cuQoD1+3pQYRxWonNK08b97SNbZNNHAtn\nGW7ypaDAVDz4b8gMsw7HyVKrh9QEpCNjB81Zvg3yZlw3lbcre1BOFTgJSPBt\nDi67rzhTCgcW8jcwj0rPMXiU36mXjq6ZjYX+3+1euQqrGCTYXBRSBWThNDU9\nxv69\r\n=a62/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.15.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.616"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.8.1_1607217751087_0.23986768561290694", "host": "s3://npm-registry-packages"}}, "3.8.2": {"name": "core-js-compat", "version": "3.8.2", "license": "MIT", "_id": "core-js-compat@3.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3717f51f6c3d2ebba8cbf27619b57160029d1d4c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.8.2.tgz", "fileCount": 13, "integrity": "sha512-LO8uL9lOIyRRrQmZxHZFl1RV+ZbcsAkFWTktn5SmH40WgLtSNYN4m4W2v9ONT147PxBY/XrRhrWq8TlvObyUjQ==", "signatures": [{"sig": "MEUCIQC8MoSNG3u7A9+rsit0jZ3ctjwwD/1lNZRh1FniAem3RAIgLKRbwzzAKbQMDGQrSJ+l+/XYE63Hh6e/+PcZdRC0WwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8dPXCRA9TVsSAnZWagAArT4QAJLOR1G7u3Fdd9dZVXbT\nXQjm01C+WK64+sj1/ubFutTRYTmGVDauOLVknCJ31uzRJzeXPmh0qCpNhxlP\nFK9lcplsxyBFVPUgEluUpG1hX/Cj4A3BvHBCWYmUuCJi+/pqzkrkNHcSE7IP\nIB+0O3qiZR/lfcPAqrTEt4g5QdDnpEBYLE5SuzSpPQVJK586kcvxWRDWQCNF\nQ84VMupIeP4w/pkR4uO0ly+WHuxGSbZFfk4nvfzGqDNBi11swkF8F9oxi4mK\nXrKbyIWMjvTbDlxzGyDCkGe4SplFElWvEXJT1fWNfLFaa6+ugidjy+O95POD\n0LasWMQxuTtQyLtQzwCLYDkDfpmPVUp/iCM0HY16ebgXm5iMhjTlPyurfUUt\n7tJasvjMk2Q1d+ckNPcMzH9ZVK/YrTogLrOPeAOp0XKPi39nouZQMHBl3Zsu\n/7qjFfanBkoaeBcB+SFmOvAVK3z+TuOYAGBNHc3FahhxnY189zImdHngLmNL\nOffbcFNDrwr89CYwuRm87s/M4OP/CgSOcyCudJhrfwEg1PwSnIkPHTZigA5j\nhPvvQlcg6hlaVzP5CovOuFE1cNy0GmIho3afhQ2eFCNkMQm3UMwUVVBl2ATi\ntAi+5idvOqS+zVUfPjlHn4BX2oBgkbqF0FTA5C9b+wvx/Itn52MdqQAhxxXn\nCjT0\r\n=Lxss\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.0"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.633"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.8.2_1609683927099_0.8847545646201116", "host": "s3://npm-registry-packages"}}, "3.8.3": {"name": "core-js-compat", "version": "3.8.3", "license": "MIT", "_id": "core-js-compat@3.8.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "9123fb6b9cad30f0651332dc77deba48ef9b0b3f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.8.3.tgz", "fileCount": 13, "integrity": "sha512-1sCb0wBXnBIL16pfFG1Gkvei6UzvKyTNYpiC41yrdjEv0UoJoq9E/abTMzyYJ6JpTkAj15dLjbqifIzEBDVvog==", "signatures": [{"sig": "MEUCIQC0h5YCiOOSNs0OTYpekJWBu4mVa6Z/8dyUDJLhYScnKQIgGcHHUyCrIspcqARwPLUzY/qdDL8ktQed3WW7ZlFzjvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBoVVCRA9TVsSAnZWagAAaP4P/RWjJ/A+IrxGj//4R2iO\nCWOy04KvIxcdVcYKMsvCRnfYIjzcOzA2V+RGjbeaKpbr2dWf9Qptvm7QYoCc\nrjPr9WtYL4Yn0+LstSPsWImvdqD0xH6lY4i5zQwFqjUZrz2FwEwbVXbr718Y\nmw/UQdkeLCgYH2iEdBv3VjDJ6GIxBuj4OY/HetEtVu9FGzNL+huVqujPX5YQ\nnnFAI6udl/Onn6nyNDKU8Hfkg1psn5QrsfyBr5Va7tWUGctISoXm1EIOCGur\nUxNL/A1UQlYcVk4ydOOfCQOgzbNJxCKOyzDtk8IJ81olFxyvuXUBWh9Ib9PK\nxHQrrJ67tnM0VZmUbhrSxtGnMoqdCxUmzln7tf9VDd+fo7DOhRXZHT6+pSnR\nuMb+Bcx4ACtmHcJhEIcOBBK99df6ruqrFjwcxZwHJf5pKO+g/3jj6Rud+JL5\nZGKnACm9MvLpaB/9nRiGZ6RUMhHhooeS44l3FeuyTpQQTrY6K3bW4rLzvXkM\nJl5HqfVLwpOsepnqUKWQErZt3rHFqVEUCDKrsCxqIZgPxsdqdmqpP1qmuNND\nmq9oOFoISsNCJUR4WIKx3I/YMZsg5bXseZjjY16/moWTRzlcth3m/Bj/snXV\nlpqwvltYVnd8JQJ6J1lY0r0fBwwfBoYinjfAYq9anSQninhX0QKR9Pqv6A89\nXdPm\r\n=IJFc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "a88734f1d7d8c1b5bb797e1b8ece2ec1961111c6", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v15.2.0+x64 (win32)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.1"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.641"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.8.3_1611040085462_0.7595577668565987", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "core-js-compat", "version": "3.9.0", "license": "MIT", "_id": "core-js-compat@3.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "29da39385f16b71e1915565aa0385c4e0963ad56", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.9.0.tgz", "fileCount": 13, "integrity": "sha512-YK6fwFjCOKWwGnjFUR3c544YsnA/7DoLL0ysncuOJ4pwbriAtOpvM2bygdlcXbvQCQZ7bBU9CL4t7tGl7ETRpQ==", "signatures": [{"sig": "MEYCIQDhEG9PMGn+g+ShUMbxefKmNGBpXTEZxuf4KNNVb49nNQIhAORbIY08Fnfh0eN+93fPx5fD0OJrzQh8uheq/S6YSlhv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLqHQCRA9TVsSAnZWagAAlYkP/0aoPRRCdkOGH0xiT7uI\nQA2YTpMxdgz2LnTelWneZh5XWHnakVBkTpUTOBeFZ/7jhPEJpd2xzxib2Cxe\nGWCe44aN8UdOqjen+FaR1o27eDqMiw14IzklI9bVUnysTMKmjllrCK86Azfd\n+0D3ZbMSpyK/XUxDcy4q1dkZKZk9TCIhtUf5Bqk7A9xZpGJ83b4WTYwYk3w5\nBjFH3OPSDhiMKd06+ZLyqltZMg0rXnM4rFgofztxxacjmsjSgvaimygZZvYC\nLv2d6au7M6TtB4Bro12Hcyf1GqMT92Mj9qs0ab8CnCgwmlwvCR/Pl/r8fIDn\nir4W9uGO8G7XPANJ57M8BMS+8ZxHrtags9yxb/ZXQauFeP+S+v6Raq827K97\n2g5O3qeOOADKmZnndW5reiPz9Hd4cYvV814c/mJdNso19bNVNlsPbuZGsKrJ\n2WRrW7cVCRcvzpdI7xNjxGJA+hmJo8m7VNqamxJbzSQ2irvrhjJ8gOxdXgIG\nKm9FmbuoYn0QogO5dlvS5616C6PQNSuHnb+aGxu3LB+6AAoF4zqflYqquA68\nGowbUfMmIsLqdYYN1x1iT6M9OhL8s4osZwuznZqRREYcwcU5jPSxYpjtwhfy\n6mUV887Bc6dr2d0wVwIh8Yv32rw0Iui8ve95YNuzJnVU7oLiNVojWEpAJHCf\n2JPa\r\n=EWoq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "37970c09678489b5663bd051342c2055f6982f37", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v15.8.0+x64 (win32)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.667"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.9.0_1613668814163_0.4966588989299763", "host": "s3://npm-registry-packages"}}, "3.9.1": {"name": "core-js-compat", "version": "3.9.1", "license": "MIT", "_id": "core-js-compat@3.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "4e572acfe90aff69d76d8c37759d21a5c59bb455", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.9.1.tgz", "fileCount": 13, "integrity": "sha512-jXAirMQxrkbiiLsCx9bQPJFA6llDadKMpYrBJQJ3/c4/vsPP/fAf29h24tviRlvwUL6AmY5CHLu2GvjuYviQqA==", "signatures": [{"sig": "MEUCIC3P/y2LiH+dxjpWWq6og8TUX5ES9ndwDRmbhxsqY1GSAiEAmx/MpEN3ueVImHh2xuDT9NmQTE8d+Kg16hE9Z3GxapI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgO+IECRA9TVsSAnZWagAAQF4P/2rDM4iD3Ml5iVR/LZ8y\nUfogbfTQGbVv6ahC8f2XIEUWrZ0crKD0VRtpJsG47530tSNMqtup/0LAkoPH\n5aUAJ452QNi1LaIMdIoagkxjYx2NnXTiSKYweO+N0BkGMy0JkKCJjBY39voM\nA2YUVpK2EKRIgiSReXfoGeVtywLonnCozoQCmi/xI5wYc4ZTPTfXyvWUx4j1\nnJGwrvd3O5bqdSdUEx+N0ecOKU1LFyPZZYow4ZOhC66zymruOMJV/M4sA0Sm\nP3AsUW0l6I2Mwwq3+5qpblWmkPfkVOW2iyY4rjTdCTC+fvnzcsETqm/cPPCd\nalBm4GnETm2bRYn1MjXm4EY+iJY1SYvhDVVilb8TkjUGyuXALSDQGkn57upR\n3HUhDMGTCub+Z9dtKlNAs/MKzLaNmx45IzXFcx/vLgZlNAlqTd5I14h/tCv6\nsZt6tsXkotw6HmYpVKNYGrpm7r/x8+mYnJh817qAeNUKA8iCyTjEsieghJfJ\nLBgDmwkwuu3/4T57MZNAPfdBwgRc/0mqa672d0iZkvh6ThzlV1EeaTFUw/Co\nduNtpe8+LPmCJcnPK/L3CqZwbS7wNP26b4Z+iIGngsslc0wW/f+3yNCxgQHW\nUPLFr2RjiNwfvwwO+Lm/XHi7kje1ns8IklSVNlMchkjh6h7c/Bol6mAHJ7ZV\nxAA1\r\n=T2AO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "50073b34ee7373eaa8c9cbbcc7313c5236a2f83e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/3.22.1/node@v15.8.0+x64 (win32)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "detective": "^5.2.0", "electron-to-chromium": "^1.3.675"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.9.1_1614537219789_0.18111943940200814", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "core-js-compat", "version": "3.10.0", "license": "MIT", "_id": "core-js-compat@3.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3600dc72869673c110215ee7a005a8609dea0fe1", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.10.0.tgz", "fileCount": 13, "integrity": "sha512-9yVewub2MXNYyGvuLnMHcN1k9RkvB7/ofktpeKTIaASyB88YYqGzUnu0ywMMhJrDHOMiTjSHWGzR+i7Wb9Z1kQ==", "signatures": [{"sig": "MEQCIDUkmeFApVgP4+TXUkA0fXI+0vohzOpQSAk7po/fzTIxAiBZH9602LWn4SOWslABTisOlNFfuGEG7zE6TyMcRsQe0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZC4YCRA9TVsSAnZWagAAITsP/RYIP8cjvLv1e0+nFDJ1\n+PNzzopVAiR5Jil2d0Y9NfirdHgqUZKxdZ9Sp/2YH6tSKKX9TJnW0s1D89oY\nH9G/f3ZaopH3cd3q5tzd7l4C8etskVeIk+CDp734dwCk6wp022Vl0b26vf/Y\ny1xkdEvBy0LyAuYgVOQ7YRZQE4X4RYQZAYkBbXjDb/V9GOzNF/z/SwyRrBLL\nYvt0+emZ2r9qa29Tl2bcmaFZ+0jKAB7meP204ma5VcQ8LauGPSxPoJ5uE4Ee\nOzsiUfD9YXkkl2/5tsjbAKNnsfQT50yVWmq2V0PO5sQB/t7NgjZ+pp++mZmr\nGGmMTDtZnVWY0F50VIrHZ58LbgJK5Vl4hF5/wdXtwYrmChiP/4+5oxv0cHoR\n35ZJGP1cTFaNBtOIJH1gCUd78rxjWn06JxX7MkT/wg+A/k0RKhtiqitcHTva\nvVNCKiny5z4aSR3X7mg2k+A19iwzIXWxfe0JpwLTzpkNxa+jAFU1SIuARcUj\njA706uFI5MKEyCR1IO060UgEZ3YV+n/Mil3cZe09SBKKG5wCXBs3fNngYv/1\n7Nz6rFyYVrZkcWpykUftAiLzOzx7/FrWlREpMAgiXO1yu518yYeFZTPDymU8\nB+5egIuT0IB8nOYayS6oaVcbyKEH5LaBnSnWwoyDN41T1ZfDpbxyDgG0wj0v\nLCT8\r\n=L9ti\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "bb9b7f58458914e54cb4dbc130374a4c0487fca5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v15.12.0+x64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.12.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.0", "detective": "^5.2.0", "electron-to-chromium": "^1.3.702"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.10.0_1617178136430_0.11044964759114717", "host": "s3://npm-registry-packages"}}, "3.10.1": {"name": "core-js-compat", "version": "3.10.1", "license": "MIT", "_id": "core-js-compat@3.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "62183a3a77ceeffcc420d907a3e6fc67d9b27f1c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.10.1.tgz", "fileCount": 13, "integrity": "sha512-ZHQTdTPkqvw2CeHiZC970NNJcnwzT6YIueDMASKt+p3WbZsLXOcoD392SkcWhkC0wBBHhlfhqGKKsNCQUozYtg==", "signatures": [{"sig": "MEQCIF5g7GWsu7zTjLZAiEqjE11GN7sqjZo2tdwShS2nEOiBAiBBbRIzII3NAYFgesiak4KmmMz0tX5ezjaA3Zvny3zg6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbgJKCRA9TVsSAnZWagAATFIP/iZm2m7mRILwFrPf+dFM\nsBZLGBlNRpXkYfieQRfMsY51FtGbCtHGejP4zwFQ8/0mKS/vgOJnRuK/AqRK\n0ob1e70WMEUVeRTcTHs9nqFFjQQ334BZmUR86kNGNBLyZ9Z+1ptOGCLgHtZl\n7tYBxqO9VSXvWM821uKS1R0RJ9l12YxH50/2mKaVXb4oEMhMXdre4XH/TWPV\nN7ZdkCoQvP+VVnxsStSrm26ZvGosJ1RD3Psw8zJvQ2+eTtLLYfpZmfI7c4V+\nJSKP7Q+plot3HzLwFd4zi5HSR+STXn3sw3wg+rZEoPCGU3PLXPxWiLpatPVa\nZlwW5B2z81Vn95xYZWGMxt2M2jnrTeYoHZsfBtcjkrnlExwc6u2E9EsHTIRQ\niU8Y6BiuQVtNLZksdaI69EGD5B16aBk9PICjYRAaYWsxH8U8xG8TGFOdI71m\nw95p0z2JruTlVfJDGSESHD6QhnDpMVvzy5gf5GJ+c6A//r0BhjHpSJGC6wCE\nE4+FyW+E/EKdYGpttH2ALwdvKEk5M055uPibvbQ8OPBfVXlveVBsBmiz8upZ\nFJCpsgLetYL+4UrfyNKkXP2wgcJOiY60ydhvo1v3egM1JQz7QnK4/0MqoYD0\ntLYqEL3cz6JPi2m3z3F2aLVV9EMhyzd+WhcYO/8vavOr9b9dUlEJxBFBRQxq\nqpoO\r\n=/Rr6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "dfa44ca9098d22d057f3a516940a0f5e35617830", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v15.12.0+x64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.12.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.3"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.0", "detective": "^5.2.0", "electron-to-chromium": "^1.3.709"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.10.1_1617822282186_0.08792880042673046", "host": "s3://npm-registry-packages"}}, "3.10.2": {"name": "core-js-compat", "version": "3.10.2", "license": "MIT", "_id": "core-js-compat@3.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "0a675b4e1cde599616322a72c8886bcf696f3ec3", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.10.2.tgz", "fileCount": 13, "integrity": "sha512-IGHnpuaM1N++gLSPI1F1wu3WXICPxSyj/Q++clcwsIOnUVp5uKUIPl/+6h0TQ112KU3fMiSxqJuM+OrCyKj5+A==", "signatures": [{"sig": "MEQCIHSPW5AmbhZOSKig8A8RPgI73tqR30U+MC3/2O+tOnBOAiAxT+Zz+1toxwObpR6h8qnSv0D5l+hhzFqfmsDT63N8Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfbfoCRA9TVsSAnZWagAAU3EP/jYlM1YzZPycNhjMFkG3\nY7cvitmtVlh8UkHwUFxpTUqhQiTlXR29/aVc43Ql+cn4OwOx+CqX4RiG7weC\nvM/TfcZjQz2vmKdmQ7IKJQ/JlIhkOH3jDK0G6Wnlio/+HDm9Fmmvvp7esjRs\nzXFFATEm//0nMJ8h2IZuAZDqarO2+tVyE9JaF0bFMEpd9OdqsBGSTb5trJqi\nQnQclHJHSw/coP5N1KmVlH57Ap0yupKqXx/+vp7SK8DCsZle+bHuPdPnx901\ny/k0VtRVcVxqdb4e4frpaFZjAqlPTByo8q2PhsEVDEPXrmsXdlwfcXJOmE7o\namKlWVXHc4CDGAmy+y5sy7SqjtPnTI//BXqjmh4YRr1ftKXZcEJOZO6LMoqe\n2wUBv/4bFo4Ucu+trtKYzeLPWvstC1lmz9rbMDOGAVZj4/3xejs4kZOBO+Pb\nXgC5PukdXIetcuxVLv87s4As66iiSEDB0dNfgcg/TTRvKFzCmwWG5pfos2mE\n2PE7mwiz4x0atdOLboxpek7xL0iwd9QLf/erQNTPH0FdTEUyuDdFrKUoBbY3\nddW7roUdm4BODjW4nEfSpOnlypFJY6kRDa55OMqDDpdhfKrh8nsdvutXtMEq\nz6cSUw3ZSpJjnmXNsId/uUxSBVu5K+sQ1rQ47R9UDP6uSD9DZMC2A3P0amBE\nbyS7\r\n=8hKf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "871050326809b302c67e4fb4e0e4007a2db7b8e8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v15.12.0+x64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "15.12.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.4"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.0", "detective": "^5.2.0", "electron-to-chromium": "^1.3.717"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.10.2_1618851816119_0.5603820187524282", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "core-js-compat", "version": "3.11.0", "license": "MIT", "_id": "core-js-compat@3.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "635683f43480a0b41e3f6be3b1c648dadb8b4390", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.11.0.tgz", "fileCount": 13, "integrity": "sha512-3wsN9YZJohOSDCjVB0GequOyHax8zFiogSX3XWLE28M1Ew7dTU57tgHjIylSBKSIouwmLBp3g61sKMz/q3xEGA==", "signatures": [{"sig": "MEYCIQDXSpX58nnkL0SVfjjs6ke6j4l/M7o6oLptHvbcU3aTqwIhAJSWRmulgVl3KiM64nLYMlBclrAnbr+j2TATmWzTJMod", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggVLgCRA9TVsSAnZWagAAFGoQAJMaWTppuBrRl0wMpD0C\nt2+kZ3zWT1sDH+ta/+n0xsLfP0KVvXyY/sTgikTOd0wjuusyFz4pr77RcScn\nNUZgwGm19wi+RwIUWaD0oWlm9ZOE9qj9rN2vOKkeXEVH9Bp3sXVfhFRQHScs\ndijgmHGsSacmFlMJtlwuRXjEhieZUFVXlwTXVfQGfbIP30E86SGRHS7A5LEz\nJvdqmqC5oKSgGNFu+6VC2pbCmXCMqWv2FqgTTM/fxtNuO3qtZhR6wP8k4eqs\ndnuUN6Cp/nqpXqHTfEyC4OHYvYqTDZtyKk/B07xXPFilOgsPhxyvJBloOZnj\nwMwY1qHi9D4+vmeQVLEecWRWOXVb6o06rdVM1IOVqGwOaInFoQ0/Vr3DQBnC\nxB/WtxdQR6lLGztxzeABgXIwdipuuK3mh+Vb8uWCNC4s/rIrbiSiOsooBkA/\nt4/ify7JyHNz3+sC3nEGi8dOYA1qEE9i/dXopeU5C/9xkY0ELgLbeJHV1zLM\n1ukW95dFx4i4vWE8+PebfYz0dChr02nxnX79IDnroW1Fc/fD9FUEm3W0Q50p\nTEiRyYVGO5HH2z6HK01vw9bviRf74tYo2V7zCRNUjOIlbRDel2EX34cig9ey\n8Nom2C9IhgwKzPDz05o35TdSKEe+2Ta7vA+Uo0zHCsNHospT19rJf6uGWzin\nWBUc\r\n=tB6d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "e94a771bfe1c88f1c37c4fa05505e82e84739493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.4"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.719"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.11.0_1619088096445_0.4880355988827165", "host": "s3://npm-registry-packages"}}, "3.11.1": {"name": "core-js-compat", "version": "3.11.1", "license": "MIT", "_id": "core-js-compat@3.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "57a91e9b02d3bb8cf37f82eceaf44a3d646fa614", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.11.1.tgz", "fileCount": 13, "integrity": "sha512-aZ0e4tmlG/aOBHj92/TuOuZwp6jFvn1WNabU5VOVixzhu5t5Ao+JZkQOPlgNXu6ynwLrwJxklT4Gw1G1VGEh+g==", "signatures": [{"sig": "MEQCIEqI0K/4ZTCMnfsAYfNKaycQiAUdYoS6lXBGAILKgLbXAiAA5o1m76eVeOYjztzR5+gab7e1Z8g9FZPpzK/MYiGciw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiW4jCRA9TVsSAnZWagAAzLEP/RfYOHCl1YNY07tzckX2\nBgcwjqeunPT/L8PSVJYM+oPYcPqcr9XGtnNZ9j7xrbbFD77k+p0+BJ/gerHt\nQA2AIGK72DPP/IkztEjpU1ASwqgKmSAWNAQCFkqMckR9C8mgQbHOORvsAG5f\n/vVzs0Y2zpzd2xGGwDDEn873n/WagrEvhxXph0jXaHy3UzK/q4ZwHGH3MXGj\nx+mIyXVuLIoTSocnaZfPy+v8AYwGmU1IZFJdCf9v4hJcJkRgsF6QKTixmekI\nRu4/0BVqQzfgvrfLCQPYUgmPINisQazv3MvYrGVeuaWeREtS/L9ZxlqEw344\nQnaIRLXIAGNjntSJg6PkEEcMsxsU34F8+rSAuAAHCdq0H4Ak9KFdTlRzV/bV\nSqqEiucB7w10AqdXDAUN4r+9kYE9xEca+eWooh9YtkCedQVfDxJJTnuftlTQ\n2+L8t4siVqiI2xeZV51jePIUFeVbOcrhYae6ekPHtoRGXatVeMTTYNihUZV4\nZJpqaWk6Mhx6RyQpvojo5/aWAx9TJnYTEnnMJ2jyKFB6lsB4J4mQwDs+G4Am\nJ804ajx8tjqFBX7mJThPA7pcFSQTA7YPM8c2TMtkXP5FTK4arNzn7vCVD+qt\nKu6uYLVGxW80+DV76nHZfnAViyp93riJDe52UFQjSyA3s5tpawZADfuovR/W\nNszl\r\n=rlu5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "1e9c4fbb22c7954d50a4db09d40d5c7648bead88", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.5"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.722"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.11.1_1619619362957_0.04119155445546263", "host": "s3://npm-registry-packages"}}, "3.11.2": {"name": "core-js-compat", "version": "3.11.2", "license": "MIT", "_id": "core-js-compat@3.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "5048e367851cfd2c6c0cb81310757b4da296e385", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.11.2.tgz", "fileCount": 13, "integrity": "sha512-gYhNwu7AJjecNtRrIfyoBabQ3ZG+llfPmg9BifIX8yxIpDyfNLRM73zIjINSm6z3dMdI1nwNC9C7uiy4pIC6cw==", "signatures": [{"sig": "MEQCIGflWAy3h4E4NfajXqcpbF1AS95DO2DrHkvvzGOKsLtrAiBFmLe88CXmpG1ZbidIxiRzUBhLNGFCpBMvQ7JL9UjeUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj9RlCRA9TVsSAnZWagAA/FoP/3hMh5B6ck2t/dyscx++\nmxIaXzZuisAJEsZ+bzU2xu/ORcmnnXvqk6CsvnVbrhJCF1bu+bG/ROPIlA6g\nYQoB/amwvsclnHSfYWmqQnokCVO5Aw6l2StkVsUhMwzAg0TR2YDY0rDUe9hp\nn0lafJnn8KsN8C0IMD4jYjM/NhQ51CuVNiCIE1SUCmvjfLANJEwCc8QE++NE\nV+576dr3E1elmSFSCetayhJm5ZrxEN3Me7v8v+yC1i+sRSbdpFsweucivIIK\nqKRg1SCg+wxoTt2L3APEHgUNr7ojm2JlBVPwdyY70dIeAfq6QYEX4Iv0NGXF\nHxi+9U6jmr90Hr+t2lVRiHWU8lzp1LrrMroNXZPDblai3uU6Vtbq9XavRL+7\n3nmkUbk+jY70aArDmL5VzxQy2Poxx8Q1r5UKmgymqwy0HBPE6hinEa2aGsR/\nRUbnNncT05h2fZiUCyMyAPhSIJDSkpGsSRQsAo0ieTHqgnL1YgmraQfNwAWp\n+mQwu16rG3y8EpyZWXp5oz1A0aDgYGQ9maeRIAAM6MfesbJHepRX5SsllAzS\nOPiInW8OolkiFMTJxcthyMmgYhNx13w8yN6nPjimYWP9ui3T3K4x4ZrVvEr/\nnKut/6beY6WnSsd+FXFPXYDL4MPEPkt4LBxhpMBakcdpACFQBZpd7sjLYFVi\nnh5X\r\n=o5we\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "040f7836ad467dae5a0b849b02442a3ed2607b18", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.725"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.11.2_1620038757118_0.9257490224518163", "host": "s3://npm-registry-packages"}}, "3.11.3": {"name": "core-js-compat", "version": "3.11.3", "license": "MIT", "_id": "core-js-compat@3.11.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "031b4b4b92fe7066c4c56da6cf6b540c22849389", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.11.3.tgz", "fileCount": 13, "integrity": "sha512-oNjHN/qUHOA0dPv+v5prqHfeSvIEJrk3hYVoaUK4MNzL9U433uu0MN+pImcdntV8o9pDq0r1v+9lTfKPjjbX/A==", "signatures": [{"sig": "MEQCIFg6gpY4WAYHOv0UBiQLwQXN9lgSRWcA2mHQYBVwFAtOAiBsv0qi1Mfo1Swv+lqz9qLXs2AbmdQSk7cHe3ejSPfyWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkqIaCRA9TVsSAnZWagAAWHAP/iMfo1thpDIVlpdmnmYQ\n6pSseZkMzHQrN+Uf5bTp6yXi27YASpWenEFc1qL27egiUjOBn1Eox0PV238O\nOU5zTUaV6zrbsPdMQpJVBJdd0B65RS26NhPVUE2HKTpFCPpqTM/R5kf6fLrZ\nkhIfD1Yha5sVB9GJhCMg8lD6fTZRqI7rhpY705jCrhGSXzE3KQpvxdYdG2/r\nnHUIfi41+FTfvCesEfVoIb9MPhf8019pEGBxWzn5m8eYgEopagtvRJqwdV/Y\njduaJC9EUsShAhb8wbOiuifdUAJ1nwvtBdk00/56Akc9tCkb+BTOC2vTk+gi\nzGuZtlXgEmLbaumPkJT5grXvNyJ9AK8iiZ9A6SNGUOWMDzfNK/CbqcdlfOcW\nRHmQI5a9Y19Q1LY7h3ofLIxcZHDiEWKQcyIo1L2eaHPtnC742+SpNWIaWaCJ\nOSel1o/jhFuBsMTHaz12ClnkYbf9Reop+38aaGeLjekDrsPbrbQ7Y4XPCGBE\nigG4No3UbHjCeBEaSZkTFpSEKi/8fXAsRVCbd/wMohYglq3eCKQKgQGTAQJK\nyV9nHm4atNrzXt9eAA4fUV0Ihr0mkfgKWYaj6fwUqfsOn9i7Cvy2vlEBr92F\nOjYKKoRIkZe/J0lrGXJgEPYqO31mzbV40KVE070TT29kDcbUBdKEYgCPWF7w\nbMiH\r\n=aqvX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "bfe345ad3db933bdf1d5fd934c57959f83d30a91", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.726"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.11.3_1620222490367_0.6764313661842436", "host": "s3://npm-registry-packages"}}, "3.12.0": {"name": "core-js-compat", "version": "3.12.0", "license": "MIT", "_id": "core-js-compat@3.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a031e51fe411085e33cb629bfee2acaa53bc309a", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.12.0.tgz", "fileCount": 13, "integrity": "sha512-vvaN8EOvYBEjrr+MN3vCKrMNc/xdYZI+Rt/uPMROi4T5Hj8Fz6TiPQm2mrB9aZoQVW1lCFHYmMrv99aUct9mkg==", "signatures": [{"sig": "MEQCIDs09E+dV1rmD84ZdzdAjwOdE8eOJIT6sKwHFAUi9PuVAiBYpSOOMtqJYWlm1AhqJx5xuK8oq3dsEQQ1kZAcN9DAVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgk7GfCRA9TVsSAnZWagAA7WoQAJ6+IILoZPnJrMfJC3B7\n6cGCLonvuoVO9iUHGY7JZaLRYZbBJ7CG/Jwv58j6iwP3CC9xV5NpvBKnQIzn\nJS3Tvv/qxc+YMNhC415cDAeQRbIBl7y9J0us38MRmcoNkP+/4D56VMKg4Qzo\nRPz3IYPjFm++wFUca7Q+rGmtNIxZSeaLRRYfpjBkcZqht3MEGNPxMvUUH9Dm\nZYeo70/Fms0QH0BPnLLakvLYbUbHIJB4P5MO8mGKs3e4N7+00tB6bD9k9nAe\nk2k2N7fgFesXfqNL0CkJbExwmnzykWuEtONqicy08amoSXa7cROKINZyKbST\nA8PUOmmOx/mUX9YQExfYzIrj/R85wc46O2g3F51WMGatQzKQnIRDXKWlwe8H\nimkjWwPJqJ0b2DQCXPMgBQ1JseA+t4gxwSqZ28AdzNJxUKIylswUyYx2DoEA\neDCWQzJI2yaT1aPnD94bRQvWBE4y6ELy/iAJX5OXmAXfDX6bY2UvVIivebd1\nNXlMKV6Q3EtgigeDsenO75YeNSD/+z2CRmx2sVNgGOiFX7o9qQYI8BvSTDWS\nU+F9tTTEV7B9XZ0NhMLJxMV5LayxjLBIf5AliCprrP6bUY8anMUHBmYWo0Jb\nLGpBTHocDYkZjY3PbwWhKPii/D4W3WA3tkJOZKl8G9aJBvGNLaTABoq23x16\nSuIu\r\n=khcw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "1aadb7b20d500abb53b6d721b75974571dd54b26", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.727"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.12.0_1620291998773_0.06589472638176397", "host": "s3://npm-registry-packages"}}, "3.12.1": {"name": "core-js-compat", "version": "3.12.1", "license": "MIT", "_id": "core-js-compat@3.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "2c302c4708505fa7072b0adb5156d26f7801a18b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.12.1.tgz", "fileCount": 13, "integrity": "sha512-i6h5qODpw6EsHAoIdQhKoZdWn+dGBF3dSS8m5tif36RlWvW3A6+yu2S16QHUo3CrkzrnEskMAt9f8FxmY9fhWQ==", "signatures": [{"sig": "MEUCIFr8Uz1TngNOvRq6/tzZgIaUxxAxbbP80bSmVADYTUQwAiEAwAsxtzZ19UcskH9+I/bzz7DrYgs+8s+3KnRchY5MiVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglvzQCRA9TVsSAnZWagAASRMP/jAAEhBUvCjw50hVZS+x\nA1P1CiskWXu9IvAMaRoD9mOV42sT4+dB10WObhE6OysX9XkNKlxJw77j9yji\n9nJoaZTUu3/NCqtXFXh+RBJ54rAq+blHfEeef5nnk1FvRT9OKXnOT6lU0ffD\nPPpc8JzQngzHPIhWKCipFFG4FPsTIMi1c3KZa+d67MPHSUHfBs/7LWyONl/O\nTrRuqeeQJX5wQztFIrpHcYGfF/pS0vwVboXFBHupHLUxWNe10eBf7OKTtfy2\nsVl8USm4cbcZuVtL4RV8QQyWGDw6QfBq4TzfTA7ElRcxlRplKgi3+28Tlsrt\ngYbsblvNEapLJb2kBpjyUyeMZNSvW1CTUB9DL56PZtKVB97c8dp4UZs6i16l\nH6m+MjEHOXV5APL+QAra64ITCdveNGeu32J+1LVY/RR9BHXpvGr2rSN3N7zj\nQF9tMRvJSmgBW4nKgGJ955DcvWeenAFn10yobK/JP9NXf39n105igfmqH5ED\nTqcju5lXgljs4EUC8Xt4G8yYt+lPs822xnCa5GiWEIlcBJn6iv/cmiKDOfzF\nvh0+xAVF1lkP5z151cXiddCWPt1Y9Vay9dwfXrpobLS1TlF9JgAW2NIv7w1b\ny9+hdZUeL34mcPMDgPwmaAWRPtmOZTgJuwuo0i5Jx5ML46ZlqEcsFHI3ahYd\nXG/b\r\n=aL9N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "8752e6940ac87e35a05c7a0df53cdc781bb73c8d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.1", "detective": "^5.2.0", "electron-to-chromium": "^1.3.727"}, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.12.1_1620507856387_0.3105993908185367", "host": "s3://npm-registry-packages"}}, "3.13.0": {"name": "core-js-compat", "version": "3.13.0", "license": "MIT", "_id": "core-js-compat@3.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a88f5fa81d8e9b15d7f98abc4447a4dfca2a358f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.13.0.tgz", "fileCount": 13, "integrity": "sha512-jhbI2zpVskgfDC9mGRaDo1gagd0E0i/kYW0+WvibL/rafEHKAHO653hEXIxJHqRlRLITluXtRH3AGTL5qJmifQ==", "signatures": [{"sig": "MEUCIQDuPdoQ6voMkVnZq5/uS03hNz+SNzU0vpEquo1lo86KegIgYA2CZQ6yQvTRHqII0+GZ7ZogbSuc1zrk54sRBadl62c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrUvfCRA9TVsSAnZWagAAw58P/3Bk4KsxrDTAOVa8p/jG\njhebF2SDnA7BWmjW7hY7cisVWoidCSSYab4G71P4SB4UG4XVcGL3+H/leA/I\nDdjzvS0zy8ijiUhAVuNZYZ/ZN4Rododtnqi2Wg+gpXlIl36pcptP/zYyySsI\n6nn4Uw9pQTtkgp010wj8EaJEMpFq99LlC3NW4iMn3EJp0nc7HsY3f6r2lU1R\n/jAIRKP/hMer4kD83IuOlxqKlLYjrcN0SLohmkb1aRWyarmf50KczdBbvuGG\nwxB+9yCRgMxQqyeg8eqV+W+WWfr/uAW3VVpkvhEXh5vdk9lnEiOr9/dYxEsk\neVZj23lCfnawvOCv6Lg4/mBzzRxqda0lUdfiCgKbfHN7vXScUeRW7AzCqYZW\nUpkjXEFP5uLXGzTMpk8wSO4wwApz/UeNdc2dYt6eY7E59d40gtWD5mcZAXPD\nODlAwYUP9OMj81/F/3aGV+YRhyvUgd10/aBn4YkRmc0Re9YETqf2obl0EZA/\nf/2oOfEvbCtVzv/q6NKgP3juKg06jw+iGw+83qinjNgXclBql2Fx9J1XDC/S\nW+TnZqvdbd/fIhRGQ2vYwv0v7WDXCwKvmUicArXPWthGsd/2ayTisG0QoccU\njNjnsd8/aI2YZjxQFdBo2SPyUoBj51DrSrghSJCjWZC+E6wC+HGvS72h8Vlc\n7IPP\r\n=vPRc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "f4307bc98ea7e1ca10b1c6e27847f9faad4a76d3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.13.0_1621969887466_0.5429868502781456", "host": "s3://npm-registry-packages"}}, "3.13.1": {"name": "core-js-compat", "version": "3.13.1", "license": "MIT", "_id": "core-js-compat@3.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "05444caa8f153be0c67db03cf8adb8ec0964e58e", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.13.1.tgz", "fileCount": 13, "integrity": "sha512-mdrcxc0WznfRd8ZicEZh1qVeJ2mu6bwQFh8YVUK48friy/FOwFV5EJj9/dlh+nMQ74YusdVfBFDuomKgUspxWQ==", "signatures": [{"sig": "MEYCIQDpvNbHly/RcW9BVIMY4RXz2PthzmWX5j83TIWRg0xhXwIhAKVf0uUVsskx6OMGoNf8UwlEyRdFrBkTwiIVigtl0th8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsgLBCRA9TVsSAnZWagAAP3oP/jc2rMy5zxd3qMJw1M3u\n56SaqB0pcw7T/iXLxzN4ou4WkkOTyoKV4DKkNV2jjvjTgejObzMwDeMb63mi\ncSh+GeXmd+nRDLKJaw7hYFhHmy/6udWopGne/4LdTYeAVf3HKbL8MmmiRK71\n0pkx+0ZJEZvC43xR7Yf2Bskby/lYJEIYmbz4zrloaIGdTdd2o53xhN20xuEd\n95vDpMcsIwdp2A+wEDEbJ87/seqhH5KWx1dkNk3MZI3zSMXywo17vhTBElm0\ntwXQdZmrvbJqMeAet1OCwk9IABbk7nnhgLQspP46lN/OAE0a34eCBVULZ1Ww\nlQ/VoViwrurNcykcyFg30yATkv8vwfUd3C5X7Ai97q3lYdzhEi8AeIaw5GFB\nq96ilTO1gnhnkb6KgfEfGNhKJgFECwuo0PDerJRZAZtao/PK5A98Dc500eqD\nAuIRxN6Gviv5dSLtvoYEgcYgQYe6lcnMHJlUJc9EJ2VjoV3CgdViPgP2DFwf\n28ONsIm2G2qIwpFnFgc4rRzJAJaM7Lfk3KNuiBXu/y48Doh+KJDf7EgbOEj4\nQukMKgfKIZY9Dol0pIHx5x9C9Kq6CY2+X06JG6XrqtQqe3Gs8doK3CZ8xsCO\nQDDFazdz9cuzJSnad53RTIRYGpp5zk5y2lRskTk79DcT05Ucg4VQRBLKly2/\nK3oj\r\n=8CJO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "a05c21cbf99ccb39b75746f3f65cbb91ef80d697", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.13.1_1622278848584_0.16050760182169266", "host": "s3://npm-registry-packages"}}, "3.14.0": {"name": "core-js-compat", "version": "3.14.0", "license": "MIT", "_id": "core-js-compat@3.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b574dabf29184681d5b16357bd33d104df3d29a5", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.14.0.tgz", "fileCount": 13, "integrity": "sha512-R4NS2eupxtiJU+VwgkF9WTpnSfZW4pogwKHd8bclWU2sp93Pr5S1uYJI84cMOubJRou7bcfL0vmwtLslWN5p3A==", "signatures": [{"sig": "MEUCICsrMv06kUBTsgqwuM+TSfUz7E3KlPbIQa1CqV0MlbPxAiEAlxe8ZcNsDai8Pyw5yeV+2rnwThotjXCEYdAeKrQdb68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguyyqCRA9TVsSAnZWagAAyhQP/3hUfF7phRX0pnT9fHIn\nC5uc9SDHGvWdrnbu/ff8eUbEbOXFleh8OVoej3wyzw9YiD1dUV13X9f9+vs/\n66KW1r/shJu8E8+5YaKMdGMq4NHjR8xdJc2+nFWJb2F/qaHck/TPeOJxOeAl\noY2tMLkxKdm//2KpTXhRRf+v2fNyrZhNCkkIrYAzLUUgj8f81+sCGmz66Afq\nkCpjaJor5eSJV1jEcjcSnmb2FeF3GLg46cIVo8oJzi4FE2YkmZg1ckBEnuE7\n9mVNRTgmOcaoWjyuuQgZNiQKD1ap8W/iZJmYN0R8UaLG1RHOdJyobQrzPOkP\n9XK8j6UI5W9f3rAwrC1tE9gkG3l62mT+zCq0yc+Gu3YlQjDdwfjGQebFGWa1\nLM0to8+mz+oDWwKtQlvuCfUrvZJja43OGwasvEplb5ochbm9sTCVGLmv5cVJ\n8kIpdMQsjj97vMlw0ttJ2lNISOwFcr+UuqgPak8qQCdN5tTRE5k8yOd88EJh\nHn4dsMXVGUEMdRPfNN2WE/votLW6Aw0wTi87Cs7OVONhdBJam0lMJhXXCiUL\ntgBCHUBw/18cZlR/wp/CO7vFVjXwi+5y1hrI2c+4R41G0+AykZ/pyVuw+uUb\ndeeLua6DnivSoEtQo3gR7kYnXR+ElRYrSGyzlYxy8dujVkHRCCjAmTZI99Ko\nW/N8\r\n=19A/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "e386f3de7760ee2910d07efb9d35029aa5dda93b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.14.0_1622879402779_0.43650005681313364", "host": "s3://npm-registry-packages"}}, "3.15.0": {"name": "core-js-compat", "version": "3.15.0", "license": "MIT", "_id": "core-js-compat@3.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e14a371123db9d1c5b41206d3f420643d238b8fa", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.15.0.tgz", "fileCount": 13, "integrity": "sha512-8X6lWsG+s7IfOKzV93a7fRYfWRZobOfjw5V5rrq43Vh/W+V6qYxl7Akalsvgab4PFT/4L/pjQbdBUEM36NXKrw==", "signatures": [{"sig": "MEYCIQDInKciXLdSrVBTziJ1wfUKUi1j/6ZL6mJK3kLFB96EBQIhAIs5EfKsYuwEBThErLyMyai6TNsh6YPKtqUxYtN3DSsQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz3vdCRA9TVsSAnZWagAA+ksQAJS1V41A81mud3D+Y9rG\n7xzZ5HFNcmeCcAvgyakPtXexJPsxi9PFxUGFtlhybwO4UAAETMJgcqGa3bku\n2sZ7SYrdGEKujq/Can4aLf0XOmHkIy0hJ8cPJWcvXOMUrOmkDgkOvT1rC1zp\nusQQXZqUAtGDPS7/6GLzHBFJWc8N5yKMKHSKRZVNgMg2cLssWO7PDAQKHTAc\ni20Phani0mD1KHkQLCAFdNSI21++q8t3LnGY3sdc96hjyt2aZsS8aNt9a7Qv\nK4+9wswHA27Dr+863VytJR8i+bWUR/3rrVTRnfqmUGrmDKraO15yaPGo+gkw\nKi7NvX62gXmvhKfiMPjgp+vP8rbPiLe5huR0oKuaoXal4YyPAfBkTmkrYgeG\nuaHa1k96Nr/KC9QtKPeTHZT78vVLO/1z8uFfYG16a+sqRP8Grdi/0P/VM9Ug\noi+uNFkRYbBTHA/5dFlBWWH5w+i+BXvxvDy9gZR/DaJg5VxRIJj9UiGMx7oG\nTLb78T/8Ya5uL+oz1y8AVVTUYvRkfZeKxHJsSrZE/mPMzCNLtjBYyOA3pa2X\nArvT0EeVrRbwNXqCYG+7OA7YvQ4ron24F8e8K3NObfPJokUtgFJ5dlQRJ3wL\nM/lIuHjBBAYWD5lMNfj3GB9AmkCmv1KL8EAn7BUdykMPmh006btBOoPVP6BQ\nqysA\r\n=B/Uo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4f7f304e7472127efc4749323c56819312fb327f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.15.0_1624210397064_0.9098548985392125", "host": "s3://npm-registry-packages"}}, "3.15.1": {"name": "core-js-compat", "version": "3.15.1", "license": "MIT", "_id": "core-js-compat@3.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "1afe233716d37ee021956ef097594071b2b585a7", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.15.1.tgz", "fileCount": 13, "integrity": "sha512-xGhzYMX6y7oEGQGAJmP2TmtBLvR4nZmRGEcFa3ubHOq5YEp51gGN9AovVa0AoujGZIq+Wm6dISiYyGNfdflYww==", "signatures": [{"sig": "MEUCIQDBIjDtC45XNJHE6poxf8ahYe3UcKHA7PsJ41fthbjZtwIgLSjOu7FRJNyShHjjqao98Q+nTWm2CsaCI1K6/td8Q5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0iywCRA9TVsSAnZWagAAzQIQAJOEZDqPqUn4UZz9AeQn\nD9/akHqdWpjxweAXB6lvak4j5s2xzCqV3++Fc6/N7hJ/a/g43+3aoKvivXIJ\nGamnmU6F9b4smt+g6vfnxT+q/AMnAmW+3cs8vWJdTTuq/czxgDM2zSZgxwt0\nm1fubpVjFvrPHLcbROEmMGKiv6O3O110YS7dpxERUg43O88SpRmmgg1IZrDQ\noO3QR6lztfXZJehclmJNNr3/G5JGnQNWk07wXkkcHW0LSBvfiQyDIZJgSchk\ngYz8ywOncDa9t8j9tDzBAf4JZH0bAE4+aXYprNSVG8sWJEr74sh2Zl6ED6W5\nuVukseyAeGeLNa7b0Ha4UoWhjnpDRWWf8EkDU0NfxovhIpSC6ZCtRIAMeS+2\nzNXSyuWlILdmZFl6aup/WIIX92kIoIUYqKwGpk8l2EQDEFtfFBKjBejRGCEs\nHdOZvYS+80cRxtEMeSwp+Uaa0C/9Za1eyF0rTkKx9ZDVEhcWcyA3RnHWpgcR\nDrcj11ihBoiVk0HXJoaUbvM9nISZf8EZnACS4JHP4UmQA/ZH2wPsscEuWAPw\n9jvJ37qL0Hh//MAOL50X5TJAVh38O5uItq9/4G1uuAaNulDf8KuVx5ajKyBA\n9JuVrogWexMiscVKoTDFF0WusoDTCwQ9yw8dLMCEEOAG8gVhLxakj7BK5HEF\nJmvl\r\n=6ZwM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d7409d106383f252ab25215a287d9b8160785918", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.15.1_1624386735444_0.9066901377952798", "host": "s3://npm-registry-packages"}}, "3.15.2": {"name": "core-js-compat", "version": "3.15.2", "license": "MIT", "_id": "core-js-compat@3.15.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "47272fbb479880de14b4e6081f71f3492f5bd3cb", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.15.2.tgz", "fileCount": 13, "integrity": "sha512-Wp+BJVvwopjI+A1EFqm2dwUmWYXrvucmtIB2LgXn/Rb+gWPKYxtmb4GKHGKG/KGF1eK9jfjzT38DITbTOCX/SQ==", "signatures": [{"sig": "MEYCIQD2+X67isztXVf/DvHaIV/nK77ROWa9HAPqKFgbwhLl9QIhAMAfFXCgVML6LPfEjEwrw0ItW5BnVaYnvDkZ3/QXSn41", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2wNoCRA9TVsSAnZWagAATFkP/1QqgM41pNJUKzcrtakD\nXR+4IeGF220x8YD+j5HKHkI6zqjooRMaA0ROWJvAfGXbqVuhwSL4jAUHLLvU\nKlEnDX5rquM7Dj33VyTbdbVgZ3N3E1U4fzVLFwzvejLV4gMxkayA4FnU72mr\n+t5anOCDUBR9pdeB83F/gMmJ6HnbiHpRftT9YAEP09ufE0tPGxVPOQNHdbDP\nNWbrEjYaYFKzrcduWXDSGQ+satA7mfnDxzrLwebJVO/9rfqUy5fglmAObrQc\nFo8b2msYrb+Ede9f3/mk+CUOxvDoSkMK23iE6mkiF+Kg8b1YChD1mqMKVuAl\nhCxy1UMNEhxfRSbPDuN9YTQjbCWqxsAdegQVantn8E9gV22dsiRnQLf9UoS6\nUCqC1rU7xcpUUPtj/Ckag/3Dz7a4uY9SyemRee6r5m274pe79SWM6wq9qGrI\nWl1bKZnphNdMjrDWfl5V4gD01P3QLETGGayA4jh2zBKwczprFzqDmNvJz7+S\n1slRf5Eyu/rHtA+k8W79myJ9PQo2O1FdkbyT8SCajm3cjdJp8ux5ee/j2Tgu\nAL+cnga+Crp9/whYV0DfT9HelZbTIHhu9d77dhWcqudu5ELhrWLE3FbsXULZ\nETMC/ZycxMtF5N2omZljoCe6PNW9ux13qVXkt+JGVTyPR0TON22WGQ3VjIuY\nI84P\r\n=VsMn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "ea6ee013f5960f859d90e4136e91b035560ba72e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.15.2_1624965991925_0.18749325294371588", "host": "s3://npm-registry-packages"}}, "3.16.0": {"name": "core-js-compat", "version": "3.16.0", "license": "MIT", "_id": "core-js-compat@3.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fced4a0a534e7e02f7e084bff66c701f8281805f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.16.0.tgz", "fileCount": 13, "integrity": "sha512-5D9sPHCdewoUK7pSUPfTF7ZhLh8k9/CoJXWUEo+F1dZT5Z1DVgcuRqUKhjeKW+YLb8f21rTFgWwQJiNw1hoZ5Q==", "signatures": [{"sig": "MEUCIQDatOyfugC4bMj9GSrs7d2wqa0V/0bUngVnRjNgzg+r0AIgK5lQrkH2mtEp8c1Hk5MIBxN9ym9HoMyR2vCy+b3nOlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 342400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhA3TTCRA9TVsSAnZWagAA1nsQAJLpy43QiyZEgipbgd9q\ncg5s9Yg3o4nOTgd6G1NHByMFV2Z6WMFGAhxecDyP50Q/m84jVvklYvsU2Xg/\n7j6M5di0kY4GJY3gXtdAbbkTck4N/E27T4qZDQ8llF1+fUZJe+oj+9nWPrGg\n4x5YupHnTHh0eHCJdp6mY5TV2I8kKObYtI8GQdh6nlnnrhlpIHSNXEjhgXyt\nUPC200a3ywKV+d+cSAt9dkwaqalVuGMn8zKOwusEiDJJT2vhE5tFTuYz7mfe\nQGS07WDnhdN+NF4gVwZDxg+RFUfBiUG57RSzbKwqOIHky/Eaz7K9WdUdhYL8\nomHEhMMgEK49OTUZz1nZotwhOHZLMHM2BUr3M0YSdo2giijG2PpfdGqBibIP\nzU/636N+IxybTCqWfwJM6pP0DvrEZasb9WLU8nzfJxzxYsLUYFEXwfiu0C2M\njzwEHS0uTsqTGjnoup2N4E16y1IPrwzPG8uS/LNGgJpH2AMTGqbaKZXT+Occ\njKLOgSTyriWZTXWkkQhDJAkgtx2DtiiZv04qcSEJKUj2YKNlMETlPN/h6uGf\newYMVMSiBJ+fEpsheYzPqd3lKX4zCKGeodyhNZ8sxL/gPaciznWhvCYgS+tR\n3ScAEATCltamRBuJjDPQi53h8AfbsL+oz73oYG5R3ir740j1/DqfDtu9bb3Z\nQKwj\r\n=Meb6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "89686ea7429ad46a05040269be44395e3760837d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.16.0_1627616467703_0.9460507031956511", "host": "s3://npm-registry-packages"}}, "3.16.1": {"name": "core-js-compat", "version": "3.16.1", "license": "MIT", "_id": "core-js-compat@3.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "c44b7caa2dcb94b673a98f27eee1c8312f55bc2d", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.16.1.tgz", "fileCount": 13, "integrity": "sha512-NHXQXvRbd4nxp9TEmooTJLUf94ySUG6+DSsscBpTftN1lQLQ4LjnWvc7AoIo4UjDsFF3hB8Uh5LLCRRdaiT5MQ==", "signatures": [{"sig": "MEUCIQC+2IQm0E7LMFT7e1EKQWHXQsKw+e/zzf3QoLzM6a+PfAIgEVbV/75qb6xBZtcPKzZvwiHdxNn4Zxr+TjyxM2BGNU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEBW2CRA9TVsSAnZWagAAIDMP/jzr3UK0QYa/cQzGCCWA\n9Twaul1+EYVj8z7WCl/JXCMjhpR8gIf4opR6ShIX77RG57KRnqPn5mZhQAP9\nqG4LQ+8WW/9yQUpIuit3A31mU/x/uhhJt2pcfvAKnSDd/r9oSKaaNELdr5GE\nKj14OlYNbO/okTmLoHNspStqudAH9t3Don5iYp513USFj++bSorfq24ixsGY\nXaiBfqbBZv2sNu+pxnhDyyOeITE39svvGdRsX+iXqf+SUER+cKVSH4zYmtmd\nBvF2FN19k8emPwpB5okKGv/0coOrgVCcpOgSyZ+B2+OawwpK+XikGu4AwQWW\nOZwYUdLZbG4rW+0qHKUTdanvwCuy9jOFvygYpKSUxfyv1BNeOvbghc4TdT8t\nBsCyfRkF4Jk9pMCSYi0FK6NDy8+1xkdQ6PyrBHfyuEoI3+i8ou1Y6OnMAKr+\nwFuY9uCzOXWYq8FB7pyEV7gVIa7/kQebtnX3i9PfyoXdl+iayNPcaaV1O2H7\n7bvp0d5Qy5QIpUhuO4ACrKRidwilWvLx4E3YE8CEusyR0KllgJ/ECAp6Lzer\nW+d0HYmRS/OSNPTYTSPqTbFHN7vmQe9FOcWXxAXAtj6ono/Wy/FMxrMadcpp\npI0jTUbDUrAbU8tIKygVjnusEHv4+pX6h9pjfYZckq8EIO+iGz25wQEGktDK\nlVfV\r\n=yY5B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d4ecfcefcb07bda80fad210142efdfdce5f66acd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.16.1_1628444085886_0.7956628971528334", "host": "s3://npm-registry-packages"}}, "3.16.2": {"name": "core-js-compat", "version": "3.16.2", "license": "MIT", "_id": "core-js-compat@3.16.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "442ef1d933ca6fc80859bd5a1db7a3ba716aaf56", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.16.2.tgz", "fileCount": 13, "integrity": "sha512-4lUshXtBXsdmp8cDWh6KKiHUg40AjiuPD3bOWkNVsr1xkAhpUqCjaZ8lB1bKx9Gb5fXcbRbFJ4f4qpRIRTuJqQ==", "signatures": [{"sig": "MEUCIQCR++QPsUAqm3zfYAXxHvUI3ZrW+EE+DXCmFTQNpKidbAIgNhTXwCCsTW8Y+4ulbFK7Wyflia4apgbEihiCRY8GKIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhG7YaCRA9TVsSAnZWagAAa2kP/2SHYHnH5Pvs1ngkiHCr\n1qs8nPD/4IjVI4sPac0+GqhTSMV//HBxHh946TYJjVMrDoykujs3irLS1yud\nZdPXNDbjlNXXq3a9p+kAYcVd/HjYmEGnN573iWRGAu+Y4Vm8Q4QZqvaA+7bg\n/SlOeyf67kG+xo0rXpoWM7mtwW9FUkSl252Nkz9d54nPN5pcOoSS4lHWdRDE\njNELlF3fV89vQ3nZ9GdzFVoWsf+UUMF0gEOiJIZ7qmTunB4C9uBzXJi5dfSR\nORL7g8Cq50M82EpCa50tWxe2Kol/HZ8m2dO6y6aICmxeA/2SJLNsJwJf9VFL\nBAWeQs36C1QqWjjOmD7lj6jjvOyNRLSGIl9MwaG4RcJtQjFGHNYJDFnA+qc/\nxUlz74ow5Lw1EEKVUyBipSZAD88cimblCMuHE1440SJx2pSh3qYgbGVK0Rht\nA52Fo60O1LSxxZ/J84mhm5Gj98rJPOA5DWaD0o+wDI/IX4ef5uQ3gPdSoQXB\n441yJtMefRZuliJKH2PtP8i55sNFFFqKN1ZXwFxiz5YufFZD0mqLOBYT74Ft\n1X7w1g6tVPLUk36I/Zpo2hxw7AQ9Gzimty05lbc4sZaPxuNpybEBFWzIgOZH\nmcVGPb8tEzuo++9jjrFdzZY7jtOIBqWchJSNUSIvA252p9Kc3mZQsANJ9Y4Y\nJ8dg\r\n=SsFL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "ef826cce45d2896ee92a2a27f6f09c2397e0a8b3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.7"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.16.2_1629206042593_0.8780615932993232", "host": "s3://npm-registry-packages"}}, "3.16.3": {"name": "core-js-compat", "version": "3.16.3", "license": "MIT", "_id": "core-js-compat@3.16.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "ae12a6e20505a1d79fbd16b6689dfc77fc989114", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.16.3.tgz", "fileCount": 13, "integrity": "sha512-A/OtSfSJQKLAFRVd4V0m6Sep9lPdjD8bpN8v3tCCGwE0Tmh0hOiVDm9tw6mXmWOKOSZIyr3EkywPo84cJjGvIQ==", "signatures": [{"sig": "MEUCIQDqaGVcAVYpqKG4lYB0IOlqlLc0uG+8PEtyIcU/B6WPSgIgTJmVAfelMRxPot9iROpJtlROw8Bf5hKcW8atCNyBvEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJVPeCRA9TVsSAnZWagAA3MEP/R5TQ+nb3fhvDF+B7x08\nswYxHExJsoHOQSDdbOs6cWfSHB00pyFuvdG+DcQpcY4zZMMs/4cDYL4u6DoP\nasEDskPMCl9vXVKP5aollbi9aBZEtUVpvW2nqurhLtcaDSooE05ybtT8Oz3K\ntYeL7J0QTy7rNY3ANZo0PIcQw1lm6vI0a+ngvoFKm34NaY/mIlmAgEVeZGYc\nBhQqXcVPeNTfFc4F8Ho0KCo2eBkOB64lEK1refbywnoxV4RJ+aWlUubFCdaN\noVtSXVDr37fIpsznaHTYLpjZEbGEtjZ6i1NIIvZZlkZOW57wbzCGFE8EUyLt\nlqSvYoNEmvzSk4uQ/qSAOSb00BinpKfwq4csDA6C9yzeDn0jT5fcIUNRQIyh\nzX7qjWrmMnwTnbawlme1wAuK451XQmGGVGmWmvnJobttRAoFvnxD/GR3X1Mk\nb9mI77zQLsn2qEfVR5t8PoeXF1u4NvFoJO2PnOYczHfaHECiwbtIHU+TA9B9\nEV0+P1n3MhkKkyGADmmYA6I3jbG1m8yKVK9KVCPruhVp8ucNH2eSLFtLzH26\nihj5m1qCIK9/awrxNGUvIez33F7at7/+0ANh09YztloyKHEmoiXm22AWBe6D\n/LEgpyvdMWqhZgmJYFjW8LGsuKpe49tvl+ds4cICIk6SnCckeNg1xF5BOvaY\nrL7+\r\n=Md+X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "390238278109a4517e438c339a22bcbadd87362d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.16.3_1629836254813_0.12606736547293984", "host": "s3://npm-registry-packages"}}, "3.16.4": {"name": "core-js-compat", "version": "3.16.4", "license": "MIT", "_id": "core-js-compat@3.16.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "cf28abe0e45a43645b04b2c1a073efa03d0b3b26", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.16.4.tgz", "fileCount": 13, "integrity": "sha512-IzCSomxRdahCYb6G3HiN6pl3JCiM0NMunRcNa1pIeC7g17Vd6Ue3AT9anQiENPIm/svThUVer1pIbLMDERIsFw==", "signatures": [{"sig": "MEUCIHYZ5b4qTFraslWbprKREyWDw4sbw/diiuFa932pj8a9AiEApt/b/OEsLMiuQZWbQyD1ehcXIWxXpwA/zY3G41F4dvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhK5/2CRA9TVsSAnZWagAAIZ0QAJtpVaiu1F7hRiT5stVe\ni0vG5ZehOnPEiF1BHOFi1X16VNOhIKwQPXHtKYqGAlgRyjj61X8JFcBjkmnU\n0Z5H70J2U5CTSWWkeVfgda/B80Xlm9rTtp7ijF3VffbqsESJz5cgese6p128\nAmeqNGbST08aEe6r2Vd8IbCcLoGU4glcnlG9Y+1f0WM4rcEfO24o05U76hpb\n493YheYMtgwJgKtjbJnnvsaKlBeNbvCJcsYyk4SUF1C9aLwG/vifkR4uh/cm\nALGNC3F+BY6yw1A4OZxG2UCzBAyL0H0eLS/nOfXLfyJZY32NUVOx5Xq2/dWZ\nt8hvFvrtV0OJ3b9c5f7HnmE6txf1kVGoeHCgZ6NNPucI+n77HQACrSyVTFkA\nIT0gb7DVwxfwfuuIs94m5dBcqiQtU0nNmNo5hS5VckH6ZvFES9neYBAmiRQm\nxhFxl1MoDVW1jzArIfnsF4r6Uhw3AX41qdu7S01HZh6cua9fuxJgxE/ItidN\nnNHgCf0itteLlxAsFE28nbkDNQ/rIzh2I542LrWkXQIzZuUZ81iytLP0Ycqp\nNn1M7ZjyaDgGHckea6OWQ4bEEjtOg7s14RWcGTlWHeeZcOjYqRNEGdQ1oEKR\nUCrCLSYY1myR9Qi4G3lRiWNxj+Cz6mp3hp0g1z2aD77pn4LdyX5S+IYgkluA\nvZjk\r\n=OqMN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "eaf15afc85003c336f71c113b886aaf6ecb2a807", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.16.4_1630248950650_0.8872785006897708", "host": "s3://npm-registry-packages"}}, "3.17.0": {"name": "core-js-compat", "version": "3.17.0", "license": "MIT", "_id": "core-js-compat@3.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3047a5057911425b62940aa91aac7b11760014db", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.17.0.tgz", "fileCount": 13, "integrity": "sha512-haEcBrfU3hu83JXWpcLHzeg8Ypf05LGK4GIjzLiYgFJYXuxrkdN2MrDBeHt/t5/ZFmIzLcdsT2x8Xw654wXsuw==", "signatures": [{"sig": "MEYCIQC59GIPvzCSJm1Lho6yhrtVfh2JMSsl882RTtadddXNfQIhAPZIm3N88swUWJCmRiJn2yN0HrV2Tdz06+o0vJQU+jLU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 352140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhL7b6CRA9TVsSAnZWagAAr4cQAIxeHFbNGCAXcLHJ5Sla\nfqfGhX4bx0Sdt4dX2MYcRGDmAREQBPrIpksR0pdKt9SOhYAgVjUL5a5WK6Jz\nyEl7qfuyBhtDEZ9thhkbF4IFObaXVMknfI2WWV7n+pWdpKCurddMg2VZ3cz/\nfdiitSIiVKFZPW8WOTEMIGPXh4fKWA22S6RVbgxZgLthzQPXBBX0MEoqb67w\nZ/mwZdIr++bZsvUZ4U1B3rxBEznTssDiGkqtsrGow7zgm8+WUhwpQLfc9uCW\n/icfS2A1xkcE3hA4Zp0XLjaMHpsQeQ2tN5i+a8E5sd9/2tj4d3sdYb09ZOhe\nhFYWRercJCfORm1AwE2OP0JlA0zQ1nzp2xkLMoskcfcQTiXPt59Ti1JTdRwN\nBpxfRkInwgP4/gYUOE5t4ZukLGy43x/iIONKBDDEZYEtJfAuhKl4sEwHhZYH\nmnbmIyft6m3qT9U76r9y2p9OKScQtxqoic3UQ3UFLEF2kws+uamiCi6rBg/d\nXYffBlr49gb/8x6rUsqkDAWzzHJfsBx3XpX9hfPkM+gQo2H/CZ1ENZRvU+oW\nv2vn4aXDUG9iztUYkp8Uenh4YUWBlsCd/+4mu47d5r+eQpFbTvzr26CY4slm\nKqE0F2G4cH0gTkkylPRE3uiusXXiRUhisH1JlqszAY5K9BpWrSt7XOMntUrr\nXvrt\r\n=YpSz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "cd4c647b1903838239277291d53955179fc4de0d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.17.0_1630516986444_0.10650945725985927", "host": "s3://npm-registry-packages"}}, "3.17.1": {"name": "core-js-compat", "version": "3.17.1", "license": "MIT", "_id": "core-js-compat@3.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "a0264ed6712affb3334c56f6a6159f20aedad56b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.17.1.tgz", "fileCount": 13, "integrity": "sha512-Oqp6qybMdCFcWSroh/6Q8j7YNOjRD0ThY02cAd6rugr//FCkMYonizLV8AryLU5wNJOweauIBxQYCZoV3emfcw==", "signatures": [{"sig": "MEUCIQDauaGIyTU9hWFTDLmAusa7/y144Rd9e+PSfzx3qI2icgIgFQ5qCUF3TfxZh1i/ViC8uW9hpeAE9fc1mphcu7gTWEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 352156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhL99+CRA9TVsSAnZWagAAonUP/3DUQAzaFKzpy1RRgTdH\n7E2MbRuHncs3Wz9/IfcPoyLFUXKuGCbOHJQ3RZVn/ArVJRn2k+Z8kJDZO806\nnvoyhtHneGQOU+cXviu2g1mQaMjRTOJcmOyL+z1zEk3ePiSd1g4T0ySr1nWB\npqyD+egJlqqX2gClsaMQjaKUPkL9YEWQSxpUa3qZPLBiwv/J925hloKzzqI4\n5IAbqq1gLcUezAtrM3tWIMwz/4W8TxMmKNnNsu/sbgTIrJglitIFuNzF9vsf\njSrOEGMVz1qrMyeTO9ZuQLmhlLpA9IVY17zOHLdzCEdn768gOvUI7S5I8UAK\nHnaB7IZDw3EXuC0Nk/z0UL2Rd34/8URZC3neCkOWPeMKfv3cr6ROAHhq0XXK\n5RNm0j4JYHksFC9LUCQNWEMbPMjpYfQY24bJ7e7D/mlSjAum9ZDJtUUC7bxR\nie/IflGOGdeG7MuisQNw3Vjv3Fkv9Wk7zHDaHIEnZz8U4VFzY7H7dSGxHOZX\nO/f8bSq0ph7Dx+48NUJXsXgS/K2yPw5mLEso6t/VRZ3o279HrcIUKWXyN2xA\net8YZhDzjEqR6mNKFyBiIymigQLHcD0nZfZDAQsmQ78m/umdLFlW+wWILf6c\nhUS2cYnH8Wk7ehqnJahPoksW0NahtH4PAjyX94W99HTr/dfpjUQHfyjL333a\n4t0a\r\n=gIeE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c526e7a00305a917fc697a9b87e64a82781f09f4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.17.1_1630527358809_0.43174419667530084", "host": "s3://npm-registry-packages"}}, "3.17.2": {"name": "core-js-compat", "version": "3.17.2", "license": "MIT", "_id": "core-js-compat@3.17.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "f461ab950c0a0ffedfc327debf28b7e518950936", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.17.2.tgz", "fileCount": 13, "integrity": "sha512-lHnt7A1Oqplebl5i0MrQyFv/yyEzr9p29OjlkcsFRDDgHwwQyVckfRGJ790qzXhkwM8ba4SFHHa2sO+T5f1zGg==", "signatures": [{"sig": "MEUCID1v+BUuzQ5+Py0IWCy1wdD6VakJJJicSTPaApk9MBVrAiEApuHvRt8iPX47pyxUal8sbHIxn3RrBdWDLaJ6ZRQvfi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 352269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMQgICRA9TVsSAnZWagAAegYP/3OcrrkGEDsogC4XS+VM\nnpP1sQlAPzKGnvt3734y8fMYmdOZRADQ6MQQhjm16XnWslq31ewthne8DTwU\nUHjujFWv8txYmVzfYEEZrJakvqusNlP7Ck+2x1tf9REGcVmhTrdp2XaEeqjz\nILl0GE2OHKAL+5RKbzx0P8O4gHOnHSXkx2yX4GzIl18orcgDA32KsV+//eR+\nkN0losGkETzOgBkfLb4aKT2ilYyNlDE4Z/KvyMxefrFClsoNUjbMn5Q8aR0q\nQ/yXPbwryuKJscn+2aDr6tW1b4lhOs5UdIWiPxuWtUHgHn/t656axYYbijph\nqZrO8m9xXf6RSdWR7/xyKpN9I6SoTknjfK95MyQmd+TRN88DKLpaeb0hGWEh\nTi7eMSWOWPhsYnoIZH2Rtr0NAQIkzySt5oBMRTih9w4aD4IbAQFWc/dGBqC0\ng6GQ7PcMpeofoNH4v9W5KbGpjKNHGhIdvbpaxFzUvE+YGWo444mfOFOibSRT\nbRJX0FC3dSonFaliT5TOxJPy4LOwlpaUgYtaST8ZGDlyQ312vphSShPQ9OR/\nep4zJnfLnJNmUy739y9SuByLVW7R6iEwX/2t5f+0FHJPIP/a5NIpT7yqKI4l\nEoMKSQP7wxWlqJJBc4sQrmLVL0h1XCLliirsecILs4MUZip1EbUi77KqkH+2\nedoL\r\n=bEve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4f68180d6e5227f144ba0a9a024f62ffe541be12", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.16.8"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.17.2_1630603272145_0.5914735516531799", "host": "s3://npm-registry-packages"}}, "3.17.3": {"name": "core-js-compat", "version": "3.17.3", "license": "MIT", "_id": "core-js-compat@3.17.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b39c8e4dec71ecdc735c653ce5233466e561324e", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.17.3.tgz", "fileCount": 13, "integrity": "sha512-+in61CKYs4hQERiADCJsdgewpdl/X0GhEX77pjKgbeibXviIt2oxEjTc8O2fqHX8mDdBrDvX8MYD/RYsBv4OiA==", "signatures": [{"sig": "MEQCIGM2Lh1YShKkjpG1SE1Gx8p//u8/b1sC7VN2v2C6KggBAiBxQPFTMfZb3pGrDFTNuHz1Mez847/U3gnMXMRaPBt6eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 352424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOci8CRA9TVsSAnZWagAAkF8P+QAOzBWmBV+9cTDgkNCq\nKC7807m8qWJPK8FIf7RSnmuvnAeUFMa3FYYdGDdwXqXcf7L/Lls0nnlMhIlY\n32+eb+f5kS+jwWJVaP34dfJE59WJdp+pT7a1/R3gt0fEIkHwgXxGPq8mpWwn\nIeiu5bwv/+USKeAaJ0C20O2rZq7c2JXDP1kXRcaghRSHFrbWolud1UNW7k6g\ncJNMG8n1uImUk8x9zTzjBcrpKHh1FMuQOuCIx+dIcs6y0KhKAjK3InMtYmO/\nehnJTF87Fz2fMRB+pGucIBww2hPBhtu9wZ/65ck1kENTGOxJTe1ouUNR7W5a\nr6+SJzmrPmVJp6TuthLI8b5yVkzehTW8CEUhP9gHcalfpk2d9lN5Z3gP9OUS\nzPj/TuzBmi3IAi7ghQD5lUSFLeWWGduEMiYrLzUEjoLzoa8mfWVbAmK0Ab4c\nQrhXR0kr+FohjmGNaY61dDFR48GTmCmCvBrFavqfNWa+bVE8pKq3gsb3Ziyr\n/2OQ96ZS1hkDtbuW55o94KoXyoct622evhUpwurc9gjcZVeKZLT3WFlc5B+v\n7Fhcv64Rp3yYRzxIsmnMgy8CnDLIgTp6WCDgH6iv+BN7hMoqyEKLcGALAzbW\nSGdhTOVjeuXQVsgeLFM0J9jN/Pe5UlDX54ki0LkyucSgSx0JcdZhowmYYSfx\nBqUe\r\n=IKaW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c65f52bdd73b4ff6eee0bd3ec39ddad901dbfa49", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.17.3_1631176892799_0.7675314577853702", "host": "s3://npm-registry-packages"}}, "3.18.0": {"name": "core-js-compat", "version": "3.18.0", "license": "MIT", "_id": "core-js-compat@3.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fb360652201e8ac8da812718c008cd0482ed9b42", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.18.0.tgz", "fileCount": 13, "integrity": "sha512-tRVjOJu4PxdXjRMEgbP7lqWy1TWJu9a01oBkn8d+dNrhgmBwdTkzhHZpVJnEmhISLdoJI1lX08rcBcHi3TZIWg==", "signatures": [{"sig": "MEUCIDaF6BZzxN8HxAzpx/PEhRByyGnSgcwm9pqZzY5baQa1AiEA9Bi4X8sKunC2OcrDIgNMOWbTmSaAcAJb43yqwFLtXhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353776}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "272ac1b4515c5cfbf348b069f1da829165a7181e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.18.0_1632078222371_0.6085885382507794", "host": "s3://npm-registry-packages"}}, "3.18.1": {"name": "core-js-compat", "version": "3.18.1", "license": "MIT", "_id": "core-js-compat@3.18.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "01942a0877caf9c6e5007c027183cf0bdae6a191", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.18.1.tgz", "fileCount": 13, "integrity": "sha512-XJMYx58zo4W0kLPmIingVZA10+7TuKrMLPt83+EzDmxFJQUMcTVVmQ+n5JP4r6Z14qSzhQBRi3NSWoeVyKKXUg==", "signatures": [{"sig": "MEUCIDurQy7tYqiRiRBUfhARRzkLQvtQDBjWfB/IySzcVLNjAiEApxboBLdYu2cOX34iCYVFMWvDFDB3YjsbPWXsGoQgfoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353982}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "1a347bdf0297884ed8d9e32e1971267c6d572822", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.18.1_1632695547482_0.12306857297576279", "host": "s3://npm-registry-packages"}}, "3.18.2": {"name": "core-js-compat", "version": "3.18.2", "license": "MIT", "_id": "core-js-compat@3.18.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e40c266fbd613948dd8d2d2156345da8ac03c142", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.18.2.tgz", "fileCount": 13, "integrity": "sha512-25VJYCJtGjZwLguj7d66oiHfmnVw3TMOZ0zV8DyMJp/aeQ3OjR519iOOeck08HMyVVRAqXxafc2Hl+5QstJrsQ==", "signatures": [{"sig": "MEQCIGPcqm0ykvUbBkPcWVPnt59lrZqz9Vb/nR1PwRtTu8/NAiAjkSTUOOBcsMq+ObYHMQtNndkwZerZPqg0wxbulFyuXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 354532}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "ac4a27025eb557d7c7a94b928236fbcb6f21ba0e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.18.2_1633458624855_0.7522240553925497", "host": "s3://npm-registry-packages"}}, "3.18.3": {"name": "core-js-compat", "version": "3.18.3", "license": "MIT", "_id": "core-js-compat@3.18.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e0e7e87abc55efb547e7fa19169e45fa9df27a67", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.18.3.tgz", "fileCount": 13, "integrity": "sha512-4zP6/y0a2RTHN5bRGT7PTq9lVt3WzvffTNjqnTKsXhkAYNDTkdCLOIfAdOLcQ/7TDdyRj3c+NeHe1NmF1eDScw==", "signatures": [{"sig": "MEYCIQDS5Yo6P6vyFX12Qh6O8OChIA4PYR4q++uPAZTVoo/OmgIhAMfLvcA+rmo7JnNls6Xjwx0x9yEIRUz/c8r9p1Qiz0WV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 354414}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "11f8efbfd552c8fca86188554ec3a8003580dd0c", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v16.6.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.18.3_1634062429728_0.042754082659448134", "host": "s3://npm-registry-packages"}}, "3.19.0": {"name": "core-js-compat", "version": "3.19.0", "license": "MIT", "_id": "core-js-compat@3.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b3b93f93c8721b3ed52b91f12f964cc410967f8b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.19.0.tgz", "fileCount": 13, "integrity": "sha512-R09rKZ56ccGBebjTLZHvzDxhz93YPT37gBm6qUhnwj3Kt7aCjjZWD1injyNbyeFHxNKfeZBSyds6O9n3MKq1sw==", "signatures": [{"sig": "MEUCIDRsbv/xcv5tMTWrkidkA4Mj/JBFSjoQTtcduSt8JL6pAiEA/jRHKw5eDdmrAEulOweC1lRlBSMfM3sJk3Kg8ABiJXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 354542}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "6123ff17d26eddf3ba8d456feb97decab3a9e9f6", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.19.0_1635142253473_0.47592376774724454", "host": "s3://npm-registry-packages"}}, "3.19.1": {"name": "core-js-compat", "version": "3.19.1", "license": "MIT", "_id": "core-js-compat@3.19.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fe598f1a9bf37310d77c3813968e9f7c7bb99476", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.19.1.tgz", "fileCount": 13, "integrity": "sha512-Q/VJ7jAF/y68+aUsQJ/afPOewdsGkDtcMb40J8MbuWKlK3Y+wtHq8bTHKPj2WKWLIqmS5JhHs4CzHtz6pT2W6g==", "signatures": [{"sig": "MEUCIFEdLbC8ivakkhoRwTcDTsSH+iNL+to+uMwiNc4ome5NAiEAk8WS0rovzCO54RpCtcXb9NyGPzsbK/US9C6dRlAC3So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355016}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "1f16f36fa2807bbe793b9da852a110e6c6077693", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.17.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.19.1_1635883109436_0.6707613510599595", "host": "s3://npm-registry-packages"}}, "3.19.2": {"name": "core-js-compat", "version": "3.19.2", "license": "MIT", "_id": "core-js-compat@3.19.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "18066a3404a302433cb0aa8be82dd3d75c76e5c4", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.19.2.tgz", "fileCount": 13, "integrity": "sha512-ObBY1W5vx/LFFMaL1P5Udo4Npib6fu+cMokeziWkA8Tns4FcDemKF5j9JvaI5JhdkW8EQJQGJN1EcrzmEwuAqQ==", "signatures": [{"sig": "MEYCIQDoEZ0SzD+NGOBttBdH/zvvd1F69th+Efk9BNFVdbcTSQIhAPhfHe8ZTceh6GlQxM/RF+9DMr3auS+f1HVvj2Rpky5s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpR+GCRA9TVsSAnZWagAAjwoP/R/4Jy/XN6lrtSK51KOJ\nudiErMns+EnghS71lf3iXJSI80qC0//vqLSxE0Agov/XCgX1TT1P7hlzDf3x\nHaNY4NQ0KVcUY47f9/oLS3FTUV1XdMRUnpnUQ2aXE2yR00rEctQw67X1pd4C\nMTxLJm7io2w3RcTvidXkjdlYwNAURrL6UBy2O75HK4j1KfLrccEteIRuDCdh\normFoWg3fmwqWTBi6hYYa/tlNP0lfx0MhPs9LObmYdz8KbTvMOisdIg9Qogo\nvAjoy1DXAbGiGt3UtW87hGKZL11mxkYOLogpPqi/mK5pEC/E9CaA/PkSMtp3\nmXQrQfzQKeOnXOYfnYaPIgoY8ri8oX8kokm4wFdg0uhQVPsLJKPkCrLzyMCz\n+cdZczfw+6rRWxIhrZlnfD2mO2lN7OXaUIhCXrjSVtbhu770k7mKx/2fwBgN\n7P0arHrJ6cDTWtzBGRh8OyBOTNBTHj6e8AL4eP/gCUnshO0p7LVHQMfgtrQ5\nztWNWXkI0AY333S1und7xZVYne9B49NyqGG4HJR606CVgG4F57L/YJFYGMbQ\nCMEtw3fsnyVKh6Y50b7bFmDN/ambNbQKYhsMPyBpwPTOKFIoFqb4dgGb/e7V\nQTEjUaWW1CRrQCyPAu5zl3T1JbY0tmyAnywjAsv/wLy04VVSnK5Vh+ZKrtX+\nxysy\r\n=aa/4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "e49b7f34f7b3a10cc88e1210431508c7a58e069b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.1.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.1.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.18.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.19.2_1638211462217_0.789445646455476", "host": "s3://npm-registry-packages"}}, "3.19.3": {"name": "core-js-compat", "version": "3.19.3", "license": "MIT", "_id": "core-js-compat@3.19.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "de75e5821c5ce924a0a1e7b7d5c2cb973ff388aa", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.19.3.tgz", "fileCount": 13, "integrity": "sha512-59tYzuWgEEVU9r+SRgceIGXSSUn47JknoiXW6Oq7RW8QHjXWz3/vp8pa7dbtuVu40sewz3OP3JmQEcDdztrLhA==", "signatures": [{"sig": "MEQCIDYnhiCswzhR5WMmJxjwXpD5792P2Z1fOMh2au1tDvTbAiAIrhaos7nvPy8G3yscHxWcB4Wh4nIzlCmCQ40kWatMVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhra0LCRA9TVsSAnZWagAAH6sP/R2DLjTeaqyZTUsFNywB\nkdMWweUwdXP6ZWjNwFcvM9EkNao929xQMru7Jzt0XrggJUKCievonsGLFddt\nSSI86zAS/KPzxVuHtfSs8O8eUrk5jhDX8uIEJRz6bUMpeE+5GP25rxzojSr9\npyNO6mUSDlcODCuAQx+GskrQMuFCD9Fo4i6R4DP0nZFHXQP9vhWsQFqnDM5R\nCSBueSI5k1JcFAp9ewxjJ24XHKOFLgvw3A3zmU7AMNy9e8Zq2JR1iE+yAyVz\naYsGp3ROQ/RmYx+EaZYqSbjpbAI0cnWR62PobHvCvN3/G6QwS+7Vq0+OkZ+y\nI5JlQuLbQQGw/QNZooo+jOrK9OpJRpSIFiD1w+/aHHl0BiJIaQCG0K10msGZ\nd9yPlpZrSMEkyyPtClQrKpD37BZtw4hFa5JvivAcfLnBhyQjSpty6ygGO3hb\nmOjpXQE9Nu2oQkafIi6ZuyUR5QeQp7czAimYMavMtjdYiOXg95Na2YtIXCU8\nufKffM4QcMJwYOUxjayKXYsxjGe77Kna4DKKiiFWNjVYAoqY+qMWhKVk01B1\nY70aIf6LpJ+qVs8MM2F/hdisLHN74OnNAkOw+YLyypGPhL/O1mVNtggwjJCQ\n3M43+9dIBxqn8oBeFkoQxuSif0kYbECgNQHHo/oqaMCEVyALCECFE98iN1Z9\nJVNt\r\n=WMUT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "7dae270c1acf496ba701acfc6272453d7c06aa53", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.2.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.18.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.19.3_1638771979696_0.1813045302703249", "host": "s3://npm-registry-packages"}}, "3.20.0": {"name": "core-js-compat", "version": "3.20.0", "license": "MIT", "_id": "core-js-compat@3.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "fd704640c5a213816b6d10ec0192756111e2c9d1", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.20.0.tgz", "fileCount": 13, "integrity": "sha512-relrah5h+sslXssTTOkvqcC/6RURifB0W5yhYBdBkaPYa5/2KBMiog3XiD+s3TwEHWxInWVv4Jx2/Lw0vng+IQ==", "signatures": [{"sig": "MEQCIHL2wPAFr6Pcv2LbCiLIK2roJCij/uBVEFbur9bZQqHJAiAlvIQ7mE0ZfPfsCwr6m/zsivFNICY0lhoonhvdjdh1dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuj6OCRA9TVsSAnZWagAAhcgQAJUT8PUE5w942IWCVNku\nI3UYeUOmnCEtqVyKMKPJELMwzzVVfFFKwa1YnDV3VHb7X2aUgjEn04sOa5qY\nZrecaBesjQZ4wIZ2ihiOMRL+z3yEz3VnCIatrXvH5V5AZyHmaHehtAov+TX9\nGy9VXYr1xi2/FqnFJSIgJZWZMWS6ll1LWh65ODqhj/hTKza2RsB5M/Fkvlpb\nmZpb4UJmtgPOIREjfnYGnhB5YDotCEuiQtywaoG+x6j6/MzxzsDjqUh5lXbi\nwKYbCvI0kA7wzEmgE1qfDZb0xwNjjE9lOPdoncjug3bD1rBIBNCwAnrtYd2T\nALdNRMT1zI7NhiJQZwZSAsLdkRQYNIA7d9qHAn9rAFXPSF7UHgh5wuGYQTy7\neVecrY5LOKhrTKNbu1LtJZfBDRKqr7wdKMmd9lMnUJzQCJe1XpHgBLqSZ09h\nDqM1txN+obZUXqRBRIMNvqAMPOtY27fK5P0jLzQ+D7GEPwDNIguXN64qbV8o\nJ/UEjrCccL2PCZPA47Y4MNQVLq1W4uWAc1ubHt0/qXUYazyuXas8ptqScAo0\nx0ohOovf1RtRlLie0dfXxEaa2chjm7e0anEU1XIwEmgmvOzlu7A6oHlf3ULt\n8W4vhzRMs8IRsO7RijRVCUfF6VPOsIivlcTjV848utQo9LUEwRwDjf00tlfe\nsRYL\r\n=Tmlq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "29590d0f9f1ef51856b8498da76c316712ac7fae", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.2.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.20.0_1639595662386_0.05677496167533569", "host": "s3://npm-registry-packages"}}, "3.20.1": {"name": "core-js-compat", "version": "3.20.1", "license": "MIT", "_id": "core-js-compat@3.20.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "96917b4db634fbbbc7b36575b2e8fcbf7e4f9691", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.20.1.tgz", "fileCount": 13, "integrity": "sha512-AVhKZNpqMV3Jz8hU0YEXXE06qoxtQGsAqU0u1neUngz5IusDJRX/ZJ6t3i7mS7QxNyEONbCo14GprkBrxPlTZA==", "signatures": [{"sig": "MEUCIQD36FsPLbmPekRdXO0B4dsdT9uFB12DOMNmUOa2GDHXXQIgIXCH133eC6RckqEl/SQcl4e1a8O5pTwencL0hqM03rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxFwSCRA9TVsSAnZWagAANMAQAIJ7r8XFUMxuyO54yO3a\n2HslczOJ3DzZJuWy/lrVjbt8sy37n7fMrbdFWp6pJ5buHPGk4Mj/36O61Ofr\nz5MJU0jYt7FiZPj8nz6qnSGSMEQLOmXEzwO8+aMDseJBGGzkq+/hGDIPSSvc\nETkah5olfnhu9gW+IH8knPn87vY69wDM40oGlIUgNGhT9vbG4BaJ6NLUafY1\nRfF8SnCi6B/7MFOTxt/w/dJMdjB9C2LY14jQDbeIOHvc2dkNwhO5K8brRCSS\nj07eUxSqugZJhABSCfcOTMISS46JEGFWJaqtGsZsQC6xGOgLgztuy/djqsvf\nco4rleNbOpZm75/UviILbmt9Vgk0KY4HYHEBLdEVbJtKQbRHG+UQ0V2DNSZ+\nWTLgNcW7QulZMEOvDiNN68loJEK0EzuJ2RzIqv9ZU4CrCUsvbOePJHwOO87L\nStri3yEMlLWKff/0swr6J0+vOXsjDxSns0fa1u4uh9iKUr/SPEKTbScu7W1E\n9dKwffqX6TYn19FYGfkzQDhTdm1+kKMtXpeBtj5j/PXs3d8+jDswTl8G4myt\nZipJB9dS26ZAYJyemdR+jxRWKXB6iAeqkB3GDuxEV450+nA2XodPoCg7OKAy\ngqkAbIvmbpNnWd/HQS/eLmloc0B1TKFRUhRySIbH8msI5vgkvGOvQIggoqnE\nxENZ\r\n=BUku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "8f22e986132c93458b778e656fb501dd6c86764e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.2.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.20.1_1640258578655_0.9239688442104599", "host": "s3://npm-registry-packages"}}, "3.20.2": {"name": "core-js-compat", "version": "3.20.2", "license": "MIT", "_id": "core-js-compat@3.20.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d1ff6936c7330959b46b2e08b122a8b14e26140b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.20.2.tgz", "fileCount": 13, "integrity": "sha512-qZEzVQ+5Qh6cROaTPFLNS4lkvQ6mBzE3R6A6EEpssj7Zr2egMHgsy4XapdifqJDGC9CBiNv7s+ejI96rLNQFdg==", "signatures": [{"sig": "MEUCIQDR7J8RLfCD1Lc2aElPLBHOO+Txm2dEJZ81qPv45Q28xwIgJ0CIOZynHgyDKWo5siLgWcp5PzLyUMSCmi+n7oG1NxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0J5iCRA9TVsSAnZWagAAJlMP/23ezDg+Ejoq3g5T1nmZ\npnnBRMiqafpgvOkZFnPSZNqyvhk9emE8KgnWJ94/Aq6hrF+PjRtdy5tV7BTk\nMRYiOR+43KO69Np8Rj7U0d38/ByO9q5PEWAMm1ScyXLILlYJcvE8Cs45/Xxp\nhxvGZcF2kvfvE7hDbcVzoWiYdmp/r904eChuJKKF+3HBKA/mECKUeWXdD1J3\nhS7t5dpWVbX3BFOYyBMjjUtkjYrdAFriRs1Z0cXAVJJvRHdMhAtzee741NZ1\nq5tCSdXrb8FtcyAvy7zWggARKICe8EQM5GkkRyTZZZLwRkklNFoOGlB0pbDi\nOjsche+DtBK+dCJ9wq/My538RP5GdNVxEaPh0x1l+fQqtRfCcPDqbuvzIHtu\nzMxoN3gPUq8dA9EDaV8sbpcoU1bkEdQoYT9Sfswy7nehHoYoRCNZwTp9GzEM\nuAS03fIRy7WdJU/uB9Gj73azE00QsQ+Hvq7/bmr0ZjSJZvhi551T3pQlsZn6\nYKK75y2/HdWMs+PHcDaAVmchEoQihIbct4opy0skwO214jm07PNxNbOTr0aS\nWb9z5nyaQNC4Oe1jjwdrXBDLWMeAqnz8a0J+9njmVEO1PbPmQz2QBefw8RYT\nr/MeLOnIyFj0dMbjLcr/jJHb5zL9Xkjr+tHghZpwYZpiKByd0xKmGDTSm9Q+\npM5b\r\n=DTTh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "da1bf15799afc93d4985392a5d1ce5468db1bdd8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.2.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.20.2_1641061986826_0.5352652631219734", "host": "s3://npm-registry-packages"}}, "3.20.3": {"name": "core-js-compat", "version": "3.20.3", "license": "MIT", "_id": "core-js-compat@3.20.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d71f85f94eb5e4bea3407412e549daa083d23bd6", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.20.3.tgz", "fileCount": 13, "integrity": "sha512-c8M5h0IkNZ+I92QhIpuSijOxGAcj3lgpsWdkCqmUTZNwidujF4r3pi6x1DCN+Vcs5qTS2XWWMfWSuCqyupX8gw==", "signatures": [{"sig": "MEUCICQ06zZyndrs6j3tlrXFo9BvXs4FHq3wmslihqv5aYzMAiEAp7hyFQPK8u37rbrFWfbRBk/S18LBYqvdRfd49YxLjiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4qxNCRA9TVsSAnZWagAA5T0P/3MbVIDgj6kaRGPdhp0y\nA/2gaCjDCdz9ifZ/dV58d4XhumRI8S1ZhE9vNalVyohRyIeS9bDkC7L3eC7I\njqto4rn6mRv2ZfH+KsmVvO720OGIsxtB5i7dd/B7b02kOPGFPO75BOCtH6u7\n3LRi/0GVdc2IRu+wKX/6EIx9K90DA0hBLdfKAFOFELZtZMQDYrjrP50r2ycM\nKHi2gDGu2qBkAxO31HgobJQ2yi6KzpotJx8C6pD/tU4ZKuBAgr8x/lm4hzuF\nC6k4f/gmHNbV0nFDlvCAxKCBoDYe+UtfkjkzVic39TNgbs733atquaLy8KLF\nlgIMP2zfAA7xX5rYKklDUKFe1IY1SIgGK4dGsdCHTJDP2IApjrllM7+EARtF\nW4rpcCsdQIeHU4uJ7eF1M1rIpkAv6KPxwVO3OafGu9fPpPgAQ24bG4J7VN0X\nEFu+gucDXkajNjOMhd2V8/zV0j70so1wuAUtmWbjJOxXWbOZLsNVC6Gx74LC\nzZI22D5G9zio0b2Cj/S6DACuvnqDkj2eDL+FyZgOW5j9LOlRth1VCUcAHgBt\nMut55fnXhwOCWQ204yDmgb904ky3kWIMhNna4rQI7Yvba/53KlfZr+4FQdsa\nHv8ToS92IJEh9ybl7ViUGw/obKwQqLWfSwP6yULe+PvTZgzpzlfSPTwPN5Az\niRDR\r\n=z/Zi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4bcdaf8646f4e60bab9ac182b06803ebd230568c", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.3.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.20.3_1642245197381_0.9709557025168236", "host": "s3://npm-registry-packages"}}, "3.21.0": {"name": "core-js-compat", "version": "3.21.0", "license": "MIT", "_id": "core-js-compat@3.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "bcc86aa5a589cee358e7a7fa0a4979d5a76c3885", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.21.0.tgz", "fileCount": 13, "integrity": "sha512-OSXseNPSK2OPJa6GdtkMz/XxeXx8/CJvfhQWTqd6neuUraujcL4jVsjkLQz1OWnax8xVQJnRPe0V2jqNWORA+A==", "signatures": [{"sig": "MEQCIGFzVLBbDe/aR5UjKsKLyN906dA8CuQg/ZkqUDJe5QVFAiBNz3zUJJomRaD+a0hJtnVOsCL61oEqoKIgq8m0IeR7Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wn5CRA9TVsSAnZWagAAESUP/3Qzo68zkL6a05Agawb4\nZ3Jb17lkabetoGGRTS45HIe3Msr2drGBodDQmXWhTUfyBEV+RjgUNmZZSYZI\nTN1wyhyD4WTWxMECy4H19lPzCVsVPsC1Pp478FE6k6FGBPm7dWxMFzOPrYT6\nf9ZatHLthSYvXhrGz8tirJZeMezMb4RiNgo9coAedYnKtdv85f0ZYfcpHkdN\npadMLbAcjVlNoRpUC97xSxotidv3aIfC9Lm8cdbOBosDiPP0gUx1zktS2NZp\ndyINNbV0JdStFUtNjD6ttXo4kDeYXDsMaXyOOdjxgVUuOgPeRg+tiRdxEubp\nL+/s0MEetacnfafoXurTeDa41h/EjujVF1ztyVQlUM9oCrXdFu1+KijbWGAN\nrviK9DQUIW1LxJ4q6po6XopTsCEY8PHsgFI1Thp8FZXMeDKmBUY7q/pZoZGk\nyOr6EfvSLEZGSW770ckiWC54d5Upu6daxpmpIZPt4KBBzURKv3u721TjAu+3\nF5dFPfNBulBu8efT4hT25zQ/jPjwTbhfflRZm4Vfe59axftRNuOt6VW3Bp40\nJo9PrkxOZy9OxDWSCdJy+QGo5A6tf2y3OpPG3HdVwxsZU212S3EnEr3milik\nKHAOjG/aGWZrcsQjpYX6u/M/sp/bQ9JWKEuX+MuRo5zY9/RvwgS0e1TcwmhP\nfvmR\r\n=uU0B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "32492dc5f55ea9bbd21b8fa8145cedd36d68c160", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.3.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.21.0_1643735545152_0.6470936411823174", "host": "s3://npm-registry-packages"}}, "3.21.1": {"name": "core-js-compat", "version": "3.21.1", "license": "MIT", "_id": "core-js-compat@3.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "cac369f67c8d134ff8f9bd1623e3bc2c42068c82", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.21.1.tgz", "fileCount": 13, "integrity": "sha512-gbgX5AUvMb8gwxC7FLVWYT7Kkgu/y7+h/h1X43yJkNqhlK2fuYyQimqvKGNZFAY6CKii/GFKJ2cp/1/42TN36g==", "signatures": [{"sig": "MEYCIQCTQ+bwEFABp3SoYgjpYUr/ktZcEDHQSLoyB7krDIJ8cAIhAIX75lKn2D01L3qf9vB1vH/W3eIa0YewKjZQa+Wh8Jcs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDUFCCRA9TVsSAnZWagAAAZkQAJC7Y3bnBGgsu9/i+sK3\nUeJY+BVDFfOqRm+5r2//3ZJUuL+DFz+xcB07cELVrmOjWyFQTxKlLzuUvu4e\n7SUG/5+m3SUdGNnxokgFz3V8rKyLXVCoLvLL7Hd23ZMus3lpqhRBkM8Zhzhd\nPfUp0BtibFrXltikpww6mj5wvODI4+ZKaJrxNTNl0aXaqhzFqmOPrQeowS6/\nIQTJOuMnnkGc0cd0myXsoFP2dgCxLJEZDKE+R7ryx6+1WLAv++EaNdCeWNZF\nzdO3OWEJbRRVHrRokN/5I3ZahSvikznsxobkWxzTzRRd8a9ZTkem+lXfbnSr\nsr+lZhSob1elcaRTr6RIxMMOsJL4WRr/Khxbpl1HxtuWaX1Dzj4CjWblX767\nsbO2VVhr7Fql3Di/n1bgRjOMXTckPGUVB60IeT9mJlRPVW8jp0GFoXvTB26F\nJp9ruz7QQ9byzzMYGgOfII75ElB1RztWg17PJPsiUNCHkPXil2cTOQ4XbUcj\nwFNPq9LDBEZwy7LYg7o50HWCsqHMQKZ6m+/3MImCgehLSTw6ivEvwKTm6NgX\nQUH1Ux1YMZ+6V76spCRGiUT9yiAvZSHx4STOAUzn3SP3StMHeqxC/e/UXDza\n/opFlt/SAAgwm/plz9w0MPurb6vJN6hGdUIF5z0m9eRHVmItqHaVDfp1+dyk\nNEhh\r\n=oksx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "eb9229ae88428edea6b2be250c98a518fd2c22e3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.5.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.5.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.19.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.21.1_1645035842805_0.3489960263201497", "host": "s3://npm-registry-packages"}}, "3.22.0": {"name": "core-js-compat", "version": "3.22.0", "license": "MIT", "_id": "core-js-compat@3.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7ce17ab57c378be2c717c7c8ed8f82a50a25b3e4", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.0.tgz", "fileCount": 13, "integrity": "sha512-WwA7xbfRGrk8BGaaHlakauVXrlYmAIkk8PNGb1FDQS+Rbrewc3pgFfwJFRw6psmJVAll7Px9UHRYE16oRQnwAQ==", "signatures": [{"sig": "MEUCIQDoKHogVhRCwJIeGQVsmVXa0YCuc0zl7GuJ5CcQZWapHwIgHoX8S5bIuNZKlBCe0tG+A1vLFLkpjkvBZwMRkbfmnHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWN22ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhYw/+K5VyABFN3EhUsEDrzl03PKEi+RImtvXLnfkBf3FwHLmUDsAQ\r\ntw5AVLuQcT0yPrAFGcE1QHXP6cdxBwBC9GKq4OuOrqiJ24CRS1PYmfZGxSew\r\n62AIi9VDcfdFrsaOcD37ldHNUF5NXsCF8yeJ5JfxRkPT0mQO8uzehnD71+ab\r\nLFKFf/y7taHMSuWz0YRvQ5ZUMYaBT2xKx966k5XkZ451QTetszxUvwTW8C0v\r\n0+/8qc8I0DKnjzZF9WISzXSI/jP/sQz3oFpcbavXrsfecnO6nuU8fEAvl8s7\r\nsSxLvCdP36MP4bXn7trC3LZ75H5yz6uDp9MZIgflt3692/nDJW4VLFQGzfUb\r\nckn/s55Sc2gwWVSYrIHvvITOt9inrO4qSbrXK8xMxfuEFngq859yCsbW/GD5\r\nwzDCtyjpWWXHuQCZI8h4hb6EnLNOU3X7vkzTgeQ+CZ65TjEyGHAbi05ljHdj\r\nFq9uJvHwKl3A2A8E1hazNDzrxMm72Si55M8jHBRDWQCFB1qbWm4rom1kZVKY\r\nbVRCrb5yWA1vtpbevpU6X1BuzMk9B5OGw5Z68mCNDtZAmLRFPtxOPdjU2KEQ\r\nNBgLFBLXsiaqbgE7U70iKo7mSirnyRTRC8hq2QpRu0JDn/rBS3tAeaA7820s\r\nV6QPtsZnYt+MNwUFhQULSnm5x0shTUZt1Ys=\r\n=3wvB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c5e56b664756455f9715481eca92f4a3a421f475", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v17.9.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "17.9.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.0_1649991094207_0.05710212921124591", "host": "s3://npm-registry-packages"}}, "3.22.1": {"name": "core-js-compat", "version": "3.22.1", "license": "MIT", "_id": "core-js-compat@3.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "47b9c5e79efbf13935f637449fa1cdec8cd9515f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.1.tgz", "fileCount": 13, "integrity": "sha512-CWbNqTluLMvZg1cjsQUbGiCM91dobSHKfDIyCoxuqxthdjGuUlaMbCsSehP3CBiVvG0C7P6UIrC1v0hgFE75jw==", "signatures": [{"sig": "MEUCID6T5AsPFpBi6SXTPJhvj5IFZHFvHdLn5EWIOVHk1EQ7AiEA1bPMG6fx1sUQOTqxZ1LEtGrDIjUcJDKCEu48kmotSfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX0iLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdVw/7BJ5iTbnPSXnR50NHQnFFcJOTENcad+wwYtDxAIaYlN8kD9p4\r\noYcVaAwv4MSsG090Kc+f5URb/UWEQyuXKGiXcr/VXh+an+IdYz6omq8/I/mi\r\njVK4kQUovqxuqrXNA6Kszg0HfO32yjz0ASZoCVrg13D94Hb8pHGU88SAcNCD\r\nBEHxOlQMUOfaloOsdisuuYYvgtuQ12evXguN+XZe3tgbhq4pIB30GZA1ROKp\r\noH6/IvG114i1y1gZxJ/N7Mds1BVj1fL1G7W5yUt+yCST8AP8fL0Rg0yiO1ol\r\nv87k060bRFpa4t5fiF+X/pr7lSJGuxZTpfS/24VIyh39oQ2DCh/OtJt9PuQo\r\nXqfkb3vKXJljA4A8NtQ2nvmUpno2cFATLvLIdRTAAsnchoSlFCxOWA97iL0g\r\nL4lStdGOMUkD6+4RMAE40twdrJw1vRYoC/NwIJNoaOxu5XAHeKllyomNDvGA\r\neuX9eaD9bc2AbaFPyUGvZwuGLrwgPmn7QtiKFO8lFXYTB+18DCc6XTQzqmhP\r\nxfWzgBBpgg7gN2ys3JSjD15t0HeQ6MpNJBaE00VOBufZfWdsrEeDj1qaGVu/\r\nMUHl3FRUlFkU8wfA2q2K/DYdZzafLl0f3a24ocUQKKnuZ2ZdLd5UOl3opaVH\r\nE4D794wbzKadBuS2P4eDiSrcwLroMGQWQPY=\r\n=t+wn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "48aafd056bb4fbe0ea749d0ad371b15dd2cf51fc", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v18.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.1_1650411659485_0.1420336959389099", "host": "s3://npm-registry-packages"}}, "3.22.2": {"name": "core-js-compat", "version": "3.22.2", "license": "MIT", "_id": "core-js-compat@3.22.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "eec621eb276518efcf718d0a6d9d042c3d0cad48", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.2.tgz", "fileCount": 13, "integrity": "sha512-Fns9lU06ZJ07pdfmPMu7OnkIKGPKDzXKIiuGlSvHHapwqMUF2QnnsWwtueFZtSyZEilP0o6iUeHQwpn7LxtLUw==", "signatures": [{"sig": "MEYCIQCMHqvDLg1iDPxmY6QDj0/76PYg2ZWB9BoNFv1PgE1ppgIhAMb5m7lAeuYePwOJ6Qc1+XrI3d9HUlIhqCRmdIj00TcZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYVmXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEoQ/+KAAGf4BF3frUBNf90Yj5t+9eKUHy8i8Ea6uSS8/kfbM62FLt\r\n8WSpHnwb7dRdP8OwuSQS0bykih9apzypezop4/Mm8T9KFih7bwwJoYIp1As4\r\nEBwoUJm7I+jmHTja5TK6ar+I0AwutNucdUCFexhOEK6LyKHqwSonqpPyQF3y\r\n97dQpsk1Svbs/NppTWJ7VNKOMRBzSyS21cIC9ZEDsK+NI2bngcLTK6QZF8cN\r\ntprZX0mMgvvBaBEqevS0wmXLCegzW2kZVcUA9oA9g+tVW/urUdyj3Z68iZ32\r\n0mRJX9RJa2d+AJ9mtwJt1rnUFvrqaJz8cbvPxFWfIYbdhRCMkYClIUUBXFbw\r\nQ8ZUC5edBaswzZHOUSD5uZe0bBRWiUkMIBN250pSahcnZC/iL8BGuXnp/Oc6\r\n9BGBhrWWOkBqI8n2W+PWi+WCH8ntgzfrjj1OAlbuFflI6cHGxpqP1aplIPVp\r\n/rOVtUvh5Q/FEtWOwTj/g4hWLBZUA1r4kgVMpImIlUwbcQTmPsA2EC50VMM8\r\nRPcnu76Fv/NjWVBeRm8Rbqo9hPL6+9RajVP5z0Y3snTtsoFHfpNag9prECkk\r\nhdS85UqYnb1TZdjVG5+FAiDbzhd3O+7a/hp7yQV4PT03FSzacphMxF/IFPQE\r\nh1C5C48FM28DCuxU4XF+cx+dQTkrNg825NA=\r\n=G3ph\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c949d92f76531a2fc31f0fdf7fda6c86e258d9b1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v18.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.2_1650547095613_0.6862005904290711", "host": "s3://npm-registry-packages"}}, "3.22.3": {"name": "core-js-compat", "version": "3.22.3", "license": "MIT", "_id": "core-js-compat@3.22.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "9b10d786052d042bc97ee8df9c0d1fb6a49c2005", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.3.tgz", "fileCount": 13, "integrity": "sha512-wliMbvPI2idgFWpFe7UEyHMvu6HWgW8WA+HnDRtgzoSDYvXFMpoGX1H3tPDDXrcfUSyXafCLDd7hOeMQHEZxGw==", "signatures": [{"sig": "MEQCIDNOMBkYTfzhuKEo9TFO0ImhmQrLztB6Px21LTS4TxO7AiAH+2cH14ZIgZKZaAN0JUib3S5eWzoqVsbBSphV6O3Sig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaghRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5FQ//RvlcK+kQGCMjdm8oakklVgls83p0qlWi9L/29qSgxHfabIFd\r\n88N7TN4XiKWmGt1fe12oAAVWSNB9jASabHM2/VGvLTmJb+gQ/i6Kn1UT4U5C\r\nEWAm5oLX10clsaQaIaAcqlpvD56Dq4NSrTFkLhDtQjgZ6a12QApM+27xysSe\r\nsLQulC8BXYnBgtVSk8e882cFNfr0uvz3BduPvGx6a5GPUHWMIS/F5DEVQeuC\r\nGV1BR/jUu9iTi3Bgw1r0VHtDNMWhxlidyHcWa9l6QVSPOB4HEqWq8tzrAujU\r\nFg8fnqx1AynIrMPvlN/hoTXA+QvHud0iQq4MtbL/hi6EzRfUllr3RkKdq/sf\r\nzTjZBZ1R2w4G2w1ZV8yTI3w+P4IN0rVgvDINmoJDREPF1DJNrtzte4JkgJFy\r\ncBcgipPH6P+s8fr4oGaSTxoTwqFsMnUryrGO2b2UcRiDqcWJgVPSpQC4dMyn\r\nHJcAczzQUyLHYRK5di4QZRLpQzhjkJp4KBWnqNIGt3BC/UUnaNI17SajEzUo\r\ngrG0BC+0D8DJw9rlEOeKYq9xCBAfPJDconJ9ZCg1G+LLKZAeOcJMDu859SjN\r\nE8Ndwk++G/rWRcBliQ4VI/2puDeqTc9svPtKZ9N9IlWyf65G3Zd+c1yh9ymb\r\nzaWX6eo6hgM2ulX5y8vkYnUHLFcWLzD2wd0=\r\n=8VhF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "3c83544a650f20115b2a586ac226d00b2505fcae", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v18.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.3_1651116113737_0.4349698705223961", "host": "s3://npm-registry-packages"}}, "3.22.4": {"name": "core-js-compat", "version": "3.22.4", "license": "MIT", "_id": "core-js-compat@3.22.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d700f451e50f1d7672dcad0ac85d910e6691e579", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.4.tgz", "fileCount": 13, "integrity": "sha512-dIWcsszDezkFZrfm1cnB4f/J85gyhiCpxbgBdohWCDtSVuAaChTSpPV7ldOQf/Xds2U5xCIJZOK82G4ZPAIswA==", "signatures": [{"sig": "MEUCIQC0QGiYrKKy1yqKFZuhLgRgZ7adwr1cx/X4Ld4un17cQwIgHOB7ksi3/pPIuO05gzmp4aFP0fao03i+RV+zRvTR36k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicB0TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMzxAAieO9xsTvHwhfOcEeGdrp7LzZM5LQMmaJjQpb+8W1AhvRga6k\r\nkcvpGZsXBvlfOx/l91vgcSOzuWlMG30oTP+SUTDtF6qq5za/scpGot7BgkQa\r\n12ICXLxrhiAEnH+F0aWp7sYmQz3rNVR8RbGHsRQ267dL4AZ7I+4QbHYCryQP\r\n0zmYeK1AKWuOVydOTqnphHpzH8BKtIQNFmLr8HL6vqd7rEcbpOjCf9ZftiMx\r\ni4wqeWQid4UYe+2uev4/dP3k61yf0kFkaxoKW93PK3lXBgHDgkyLU9ZiRPC+\r\n0yiRQazyIwOhyPC0+bWf9YWMOp3lcI/3CbNzeQ8wj5bwnVG2GKJW5oz5/Mnr\r\nhOnYEPRyYwgkge8WOZAzGgoiR+ruHv3FBWJq4ilBe331WWHBX+ocmci5KQUj\r\nbeI6c4ZavfE/y9ljlx58CvL/oYC0JvswIf6lBxzOL0+6S/13nR0l59j7X14U\r\nFcT+rbN9tRycrK/4wPkb23vrwz4x5+VXzpYsqnivZoOvaJr1FNeeIYbiz9AL\r\n+cAA5btWHmxisbNN1MmnXd/w4P8D4W1IrT2EZXjqMPiYcg0/eHNZ+ecxQ4ut\r\n15IEVYlR+VQp6hiFWIUHkwyxRYLcQRvepfbzNgkU1WDfOKKtXgarC7yYDoGb\r\nPKJ8IJeZV7C8GEfwbpkeDHE3h2HuAMPwEuw=\r\n=o05u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "6ba79a5aada7286aa44ca9e4029cbb74dd84ffd6", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "lerna/4.0.0/node@v18.0.0+arm64 (darwin)", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.4_1651514643532_0.5944660856402832", "host": "s3://npm-registry-packages"}}, "3.22.5": {"name": "core-js-compat", "version": "3.22.5", "license": "MIT", "_id": "core-js-compat@3.22.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7fffa1d20cb18405bd22756ca1353c6f1a0e8614", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.5.tgz", "fileCount": 13, "integrity": "sha512-rEF75n3QtInrYICvJjrAgV03HwKiYvtKHdPtaba1KucG+cNZ4NJnH9isqt979e67KZlhpbCOTwnsvnIr+CVeOg==", "signatures": [{"sig": "MEQCICtaiQiCI70tecWYxvf909GTIm8BtTR0viEO25jhpgu7AiBdaa/nKYktjnE8uWs8vIYJFbxYysCWuxi9NjxUkl40/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiejFFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqehg//YeCWmaqSQEKL+qZD+axM3mVy7pBTd2iM1IomRaiaYjeRNSmG\r\nolPXGE0FvZH+LBo9DCDjYQA6RZxfyMM8bFzDoCWbq1emXTi/XT7VO3CAu1BH\r\n1x5RAkiiuVO8h7GiWtMyjIVz7h1NTVMdhg7EEGctG/kZLu/tA91VcXUhtjeT\r\nZcuvEFWVGb452jvwz9/Eg+8G2Lcqru1+prB+Fd9zsxm2qXdzoU2StT3yvGMj\r\negr99jR7HtVxX+Z4Hxd027AF5OhNp9tGbVcoVuXAASSPAwKjLUJt5VrA1HUw\r\n6NZeyqDIueKflzpiJtrRQp5NSbN05QQYEr5VV1NmeibDkoheDCFL1o1trFOU\r\nTHYg+XfxZtoBI4SC0FvXxVSnCKc26bbqXePORTMT9uVONu+gS2Cp3k/XqI2C\r\naA9Mzj31LKLqFWW3IBII7ekzOE/RqZ1TCw4nFSR5Yy5oJz+yOIo10VFQBwFl\r\nPPFRxakP+n/eRXVOM/9KSJoyEGE3bd6ANUnBizEtAtIcqdgaI2yB9LEFyMN9\r\nvXiBm9IvxiV/LpsqqAXuzwOtvsFVebeoELYKbOIWHupXDVP0iJwcgIAUCDO5\r\nJ5vTv8X+9rI4SGzAV4RhKRh9aiupLscWzvKDtiHoWExwQqWcCTK1mAJjbRVw\r\n96GK1K5vaRPUNQ2riHTW7SPfbf4jvHC3on8=\r\n=iaLu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "f1b4f76cdb9e7359c53126163bd804e2d2cf1f20", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.9.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.5_1652175173123_0.9505898162379303", "host": "s3://npm-registry-packages"}}, "3.22.6": {"name": "core-js-compat", "version": "3.22.6", "license": "MIT", "_id": "core-js-compat@3.22.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "2e7c7a703238c168883dbf55c24bbb67503cddcb", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.6.tgz", "fileCount": 13, "integrity": "sha512-dQ/SxlHcuiywaPIoSUCU6Fx+Mk/H5TXENqd/ZJcK85ta0ZcQkbzHwblxPeL0hF5o+NsT2uK3q9ZOG5TboiVuWw==", "signatures": [{"sig": "MEUCIQCyz7gYxnY7Sd5xJ/h7gUkmLiENGiDgVJ8WB8SpXumVPgIgSXbyqoQ7/LElLqrDmdMRQqg401RcO43C2jFhVjSKsxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiin0bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8ExAApBIEs+zNLpIuVlsVjJP7ud5uTI8T2Yv6FwnsReaoUXSsyWuC\r\nVAX3CE2uLcitOLitNDZ4b3BvD6y7O3aWtgLngaLlYl2CyOJVDHAG0JdrqN/C\r\nhnWElKslX3Q/FTQN3UaMg9/ZY59Vm8/9/0EKjaKNXMle/ElU+yzrMoId0/we\r\nbD7JhYC9Ls+wyEKVmU1iKN2KXXtNq3NuJv0FwzcK2YpjbZ5GNUtxEYIQJEm3\r\nNcxMdOznB+9TYtjeqkN7d6I62GZCrTFYJiYA4u86JC4Z56p0UoWPMzgwqAdp\r\nmmYjH6oWrF9JCHx/Dqy5g7M/IBbwZGhungkKA3he1YGQ1ZiQb3Mvkd+kGbXp\r\nKr+Qvqr+pTcQojcXi8I27kl8NrHoocuS0vCpet7BvFJmFTe/jCDm49UKXQ+Z\r\nHjHuLyOvo66PRHrFybCS8fvOgXGqHtX3GNyJWsKxbCP9lSBR5a2si10xC8SI\r\nLMldrEjNfM6PK/C0HDbhvhffu/A7utkOxUM0mZHnU6zgxdLfAFSOsFBsoa2S\r\nyHfXLsfP6YSdMFm8t+009sQIGlSYG5pfumYZhPsSGoMo9f+a86BFdS2ODZHa\r\nVx67Lfs+jjZ/f/JMV4FmmF619qmG9+ykJoddY/0+cq6/bsJGeSeOOJMDeapW\r\ndgTr+IKjLqteuLnRNrXf95txDfFUwX+RIjU=\r\n=Cm6q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d3d49a279c44a3759d9b060672a8799c3090e17a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.10.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.6_1653243163391_0.5531222488259739", "host": "s3://npm-registry-packages"}}, "3.22.7": {"name": "core-js-compat", "version": "3.22.7", "license": "MIT", "_id": "core-js-compat@3.22.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "8359eb66ecbf726dd0cfced8e48d5e73f3224239", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.7.tgz", "fileCount": 13, "integrity": "sha512-uI9DAQKKiiE/mclIC5g4AjRpio27g+VMRhe6rQoz+q4Wm4L6A/fJhiLtBw+sfOpDG9wZ3O0pxIw7GbfOlBgjOA==", "signatures": [{"sig": "MEUCIQD6bxNGIgXgCIFmwOQpLr++9xHLNIf5UslHJlbV79mpIAIgIrITwuFc1TQYsiYOqitMJKAWi3e3cwwRW21/Wdox7us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijOU+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogXhAAiwavDCpSmspC7vzJzrIy/E4X+23+yf7TCB/34MT0/hzbfDDK\r\nn+vGvgK5wRghDE+efnnGjJwIWLETkxmWnBbTqNp725E8aTN4An4kWCGGjtlg\r\n7p/JTW0KkI2zO4oRlxMKWBJqN6G4wCODxYJhoznRAJ3zG+PMUC9twMelzKGD\r\nIOg9xTEOUeaHXSHewNt/wtLLPAw8tOd4+0l6XvKuuY2UQzLv5CY/hYM8NnIQ\r\nSiVJkuhZ+a2B7MC86fkhgFksgOh5K2Db+eW0PiwNFNIwRwHNWVx8hdisVs8o\r\nhQ+sfb408RtEdOX0tOk95tWROTs5CtRCuyDx5ub11E9O3np+JMZ5uN13l536\r\nXZ4lx9ruIQBUhfR2sNuqw7MBlEeRuIb3qKRQ7F+HGVVsOEoEIWBWFtZWmIBp\r\nF9zSW7utpGoei32zLe01NAI0L7TG1hKIiM3C0xNEM/Nr3O/MZ2BPwKPE3eBP\r\nS95JwoZ+m+dBCQpW5W7zuAVjNKFpzO8UCTmsovhOFdnGDpKvkVi+udztNLCa\r\nMQY3Bq71DDpAYLb8nx6QvEj2NcCbQThR0vVlDfS0Vd+EygQjJYgvanbiPnyV\r\ne+U8IthxPrchW3yPcw54SrPEu7Mdvhr//xOkMrNX/FC6DJfOjzP47DDyYE9Q\r\naeg18QgydJ7QAXj7Sdt+iZdMZZo1b/F7CkM=\r\n=GsRt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "b958991fb5dae6b69883fb724baf923bb5385e57", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.10.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.7_1653400894039_0.39702682156712465", "host": "s3://npm-registry-packages"}}, "3.22.8": {"name": "core-js-compat", "version": "3.22.8", "license": "MIT", "_id": "core-js-compat@3.22.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "46fa34ce1ddf742acd7f95f575f66bbb21e05d62", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.22.8.tgz", "fileCount": 13, "integrity": "sha512-pQnwg4xtuvc2Bs/5zYQPaEYYSuTxsF7LBWF0SvnVhthZo/Qe+rJpcEekrdNK5DWwDJ0gv0oI9NNX5Mppdy0ctg==", "signatures": [{"sig": "MEUCIAn6JFmX5yINd3xL9JT4xZ41BwLoNvpO/hv0CZXJFrr7AiEAin32rvizrqo1xkxVDgBjPZTFXx9pvVBQGQ4miQ4Svf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil6pHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZNA//csQK81lxlPb+RzYzJXAu5XK8Ve1PghQ84riH6q8Jrs9ayll3\r\nOv2bd0+jZjOGtyEgAxjJmq2n1OUYC2s1sZuSaB/5DqXUhRa5EmXGD+uREAZl\r\n8COoj6TLp5dkGm7YUSrdV0c9wp5B/bZ15ZslIZU6AhVv+5FZesW8qRi/USWo\r\n0iiE/O9PZMzg7a/AX+APoypPjVid+BJGSDHudZ1yS4/j68Dz4QyYBNNbUf3P\r\n9N+Ic2DY1R8wUj0avh7NNSpDJ7VVDrVcCUHFRe92+zKcTr11d9wwrQQOLlCq\r\nVu0S/Wyjdp7JUDg0qUYIy7NCEQzByzrcA1zvxY17jYoyxk9cDpYstHouwUa9\r\nApM7SQ721OmRbgYpd31+WctBWIauLekfexNYM83O7s69ZuL3F+m6/cCQKZT9\r\nJskpBQ6kUva1Lns2Cmt5ou/IZWqO+mY3wJXZlVBlymFlJiqxkWtRWtq7pUbn\r\nxuVoPazB6N73KsNPYzE13Z6UMAEnyOcA5HfNW9Wu7EDhlQ/P8EW253ikLBxP\r\nqIvVtCRzHbb3iIZ9qFrb2I4z/l/QJwmMr7mMoppnQHfQT9cvUqvGIjiDUiC5\r\ngwyOleqz4hesEDWUonreogIGw2Ams7okw78KZC/nUGGClegJgj8F6Rjfe/j0\r\n5wmr6mL3TqIdQHmjr5e9P6ChZV1zNnfHGV4=\r\n=EQtg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "18a2e90f73c7fadd137803d440fd79fa5262fd00", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.11.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.22.8_1654106695006_0.3627153940136534", "host": "s3://npm-registry-packages"}}, "3.23.0": {"name": "core-js-compat", "version": "3.23.0", "license": "MIT", "_id": "core-js-compat@3.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "324191a23bfbd229802718dcc1d213414b244624", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.0.tgz", "fileCount": 13, "integrity": "sha512-i4FgbtahOArZBEteiL+czI5N/bp17w16bXmLagGThdA2zuX1a5X4HbBmOVD7ERRtk3wMtPOFEmlXpVV4lsvwNw==", "signatures": [{"sig": "MEQCIHvb8rgvwnzoDJsCD0KFk7JZbkum/f8kHbJx9FqhCdrtAiAYpRKJHyJlYuRwUFi9SDvMJj8PxAnwx0JCELpQX3leiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip4PKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlIA//Zecss3oLisYtzbggVrIGh+Zlh8YKLhKDpzqgq6EyYXtBTgpd\r\n2LsxAB1DGVQziAnHINvqPlBB1wUPVlU4ulhnYU+4u0jHyJdhawKkuYRXpoUr\r\nCWVdY+thDvsG7N8QBfB8mBn8/dmWvEBZgRcBm3lHlpcgKTzDRVvsGkyiPHGF\r\nYvGeIF8lHTfo4s5m+yqMZi4tVDNNOVvjFU9z0B0IvIfBZbg0/wzT7Kdk4snC\r\n3c645Q20UHUE1WR8OCIw856rxHUVHKMGBpUVnsAFnmkO4eMUrM1hHJB/puwe\r\n3scT1xyH0H+REGT9X/m+wst5FhyQEe0JJw9i3aetQeuj4/xH6GjIioXgLbxp\r\npC5fSSzGe20ZHY4IrCITXbcfEPeCd62lUMTsApDLSiPH3f/iAg0/mHcCAHiS\r\ncGzVLWFAAkleNqYKsk0a4if3JkG4HTbxtzKX+2jHZpbmJHPHQR1q0mjxZyOJ\r\nCvJ6FM/g1VapuPMp+CK4ma8dhjwNoI4ipPqAL39p6oh6iBgXgnlb81z74s89\r\nKUbhkB885PvKhKHZhhNxXWGOuAI5eUqpV8RjDAfNgsz3sKVYSruVzAL1kty4\r\nFRXBHdShN+DAjvOw5yAO8moft9x41bPzQ9i1Rfdk/NPhS1ns9Mwnyhf5SnE7\r\nqOOhpzV1ndBCpW2yctbDoGBKhhqkd+F/uw8=\r\n=DmoZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "54fcdb8b35a6c83ed7ea7db5e1812bec1fcb9f5e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.12.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.0_1655145417820_0.12392780486119181", "host": "s3://npm-registry-packages"}}, "3.23.1": {"name": "core-js-compat", "version": "3.23.1", "license": "MIT", "_id": "core-js-compat@3.23.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "23d44d9f209086e60dabf9130cea7719af6e199b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.1.tgz", "fileCount": 13, "integrity": "sha512-KeYrEc8t6FJsKYB2qnDwRHWaC0cJNaqlHfCpMe5q3j/W1nje3moib/txNklddLPCtGb+etcBIyJ8zuMa/LN5/A==", "signatures": [{"sig": "MEUCIQC/FXHLG+nM6T6Wn+qU+p9U9nz3aY30Kt6bgsKM2EBPiAIge8BvXNcsFaegwvUBoQd6FtRKEkVlDxzOwwjbFTSnimM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqIwXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpVA/9Fc7pXMGxhq8xrjG33MViXKgupuOPjYdt3nsXpsGzFBKboK44\r\n9HfcGsGHmyRkZrU6+w58UtnT2OJwFr128WGPgARjOcc2dgB404wm2w7nL5FQ\r\nnzfQXMPHLteCU8qzA/cwgFWSdvHIxM9udbvWKaY9wAsoDpqsW7gO/7EQ2Zfd\r\nwhOprtYN9wlZvmkj+N+jxGbMHcxjgw7PNXISYEGQ/bxSlFxHlsmoRUf2ujFk\r\n3ODF7bNU3I7qCyxDaJG3+w4EIiXkJqFVu9iNOqUm1dMS8Sh8cmwupgj8nd9W\r\nCO2rwZyDV39Q4xqqMu0XD7DKVDlVwElFwaeEm/o4rJHhBFVoDk+V0FA+xozi\r\naPRxRuULLH3ZThMLI6TssJHDy8XtY5PyLPElz8dPhstUYoVZs5bUYhMsMU26\r\nF9Y0sABvHo04nzMAAC/HCapZsZpdkJwr98t3Deo0rk6ZGslZ6WMSrN6EJ+rd\r\nacG98ENHgv/xGRv5zsKAPQI8o6+dQy+ua0nGrbN45MrDtZNODMXexvMff8C+\r\n/zGkx1TLIgQe7Qa133yO7LBYRuEgSjp9zfQGJHMjY2aA2a7NPNcVn3FA0yVB\r\nojmgHiVbMjrNEPAPd+INl1242fl6S7nJDvPPWvo9rAQVOBdTPVDBacANLERi\r\nr1UTIkkNZwgK87pCmsIFhIdHpJfp1kxONE8=\r\n=d85k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "024e54a99608a7def2b44362db6729f196e202b7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.12.1", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.2.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.1_1655213079339_0.13277263748252133", "host": "s3://npm-registry-packages"}}, "3.23.2": {"name": "core-js-compat", "version": "3.23.2", "license": "MIT", "_id": "core-js-compat@3.23.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "5cbf8a9c8812d665392845b85ae91b5bcc7b615c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.2.tgz", "fileCount": 13, "integrity": "sha512-lrgZvxFwbQp9v7E8mX0rJ+JX7Bvh4eGULZXA1IAyjlsnWvCdw6TF8Tg6xtaSUSJMrSrMaLdpmk+V54LM1dvfOA==", "signatures": [{"sig": "MEYCIQCk+kieCe4pj7BXaCjVdtLpwibDBI5sdBytzSPEz8AmiQIhAM825TH8M5DoADV7u5EbRw80lL7rfGusr1rS+rfpiMl9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisMVjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaNxAAjD3Im3FbMAy70FiIi0SdvmtpEpvgwjaH4jNbxyAQyHyorkUc\r\nHQItf9X4Wy461Qb3tPwmwUD3jfBLEihuJAmYC/Jb5A/4puqTHrCvB1ujckAD\r\n2updqNaiPBsMSrABpsjGYwNpvtRmOBtjsLleo2bIhkCFYeGTc0Bnswx60n5F\r\ntDCzMiW7uAp6duD+48O1OFfv5IcyaOjxfgTk8Jx/xtVMiMiCH4eFZjJGNJ2c\r\nr8RR4h9sgoNAfTSsXRTtzbwhQgdDHnxTtc7U/WpVJ2QjDrLvWvSNLqJ5fNro\r\nS96gss8OaTUe7dXDyuAdgVknaeVnVp8kiMPvYUwyW+fv55Zi8M0I84Xje8Pk\r\nS7anpTQMCgeY4TFQLX255gZXw0RyoOWuCql2XX5b7Q7VVI7VgqAUxebC6hww\r\nNoFn810Ik5q3wxPTmGPCabKkIMnNlih1fAxL3/o6kcOludNY9uAYLL7vxfjT\r\nX8ALx1LTHOGzDXZaVFc5vvJyJcHMqpMETbjK4f08E+mGgOf57KgdBDyAPbWU\r\nLz9OqC4yB7AUKtabMoJQaltTuO3fISMGVWhuTtZhDAc607Le8mdBUtQr4D14\r\nHzdEFFfZZMrZ7/xwfnT21cY+XVV378lr9XMwokIKum1vqhntbYxDTbQOc2bI\r\nEmHaMiShcq9GJomMVKGW2VDPeR4YW9413jA=\r\n=GNhS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4c29e0a8fdb9a39cf4b5d102d4775f3f0409f76f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.12.2", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.20.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.2_1655752035174_0.5655730761070872", "host": "s3://npm-registry-packages"}}, "3.23.3": {"name": "core-js-compat", "version": "3.23.3", "license": "MIT", "_id": "core-js-compat@3.23.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7d8503185be76bb6d8d592c291a4457a8e440aa9", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.3.tgz", "fileCount": 13, "integrity": "sha512-WSzUs2h2vvmKsacLHNTdpyOC9k43AEhcGoFlVgCY4L7aw98oSBKtPL6vD0/TqZjRWRQYdDSLkzZIni4Crbbiqw==", "signatures": [{"sig": "MEQCIHLapa2eo+U9FXDotk3xPVjmRISuhG/JHdZKlla+5uP6AiA0TIGi6eznzdL2FtdyuBdnc8dMX690yMzSk9FeRX9Etw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJit20yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkvQ//c25VvYotSGpH/pFBP/8b/Rc90/tvBkuwQEccFxAS0IP8TT9t\r\nebJqakFNYGPGrn/H+AydSOT8Ox/xRVEqzpxobfmpFykzo3BuO9syhfsLBEgJ\r\nayWwEzt/rUnu0QZHyZIJ50DE3X6ezKi3HjSrm6IgOaB0XU7fUx8ddqK6qvW5\r\ni/YLQymKcZrfDHfn4+uJfZeBUYaCSGtCpv+3omAKH7XiYggjBuuznHxkTnrN\r\niKCLke5IZr3bsae8O4iLUR5vwg/Opv8bk6yqi6Y3thOTT9tMNjiIWGgNey/r\r\nONQhvzLAixCzG8BanCtP7Jyki4DIBvDUzCk4TtbyBn3AZPWIKOdnnR/ENf+P\r\nkt6aSf8I/1ahvELvIgs1UHaOoLpse9pQKIm+GMPbc/p5U8BGsQ6DtT4QhX77\r\nKv2eT+evHWT/JsZ6vfYVWO6M1O+jpGk7B3a+byhcptUGsJJ5tBvFeV2Hg2i5\r\nXJ4HbY5MdAhj2vu1UTs8Bj9pgrIhTSfk8CGR+AXn59V8JXXXnSDJuSXQy95E\r\n3lWDvtQVBqHn1IMhSdA2aKYk3UCZSTwEm/NxDSw5mWXJY0b9wwmnEhNxwh9c\r\nqqalsMymqjmbf6I7flZVpYMtMqvjh6XArxCSt6r84J7Q+sFQnd3gFUNFhfb5\r\nS3oh6r/UVkWIRDyMit5d6JQHTMu6JwSkMak=\r\n=5tjo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "adc99709d9592c3f4cc63afc7428c5900d9e054b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.13.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.3_1656188210672_0.501456497862762", "host": "s3://npm-registry-packages"}}, "3.23.4": {"name": "core-js-compat", "version": "3.23.4", "license": "MIT", "_id": "core-js-compat@3.23.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "56ad4a352884317a15f6b04548ff7139d23b917f", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.4.tgz", "fileCount": 13, "integrity": "sha512-RkSRPe+JYEoflcsuxJWaiMPhnZoFS51FcIxm53k4KzhISCBTmaGlto9dTIrYuk0hnJc3G6pKufAKepHnBq6B6Q==", "signatures": [{"sig": "MEYCIQC0LSCT6gksOQGjhNcSj/aXesCJkpu2QZQXcvqivuqK1wIhANveS+soivuiU9ZpvRDk3bBBnRQtGiS1xVrbgLLsCWjl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiycfUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpy8A/+ID1GKZU/PaYBQ6Bbs7wl5H/pU42oTBs6kyhv9/yzkXw97aRL\r\n16HoBEZa8zjh8LLeeryc4CXWncoqlcbTlydr3SHrEdfip/BoaK3bQR9QZuFz\r\nAHn3X6ohjJCtDrv815ZP9bkGlaoolwlKavSi1FaXug4EFyzE7P15iTr01SZw\r\n61a7sWks/vYsSiVM640MRqEzDZGOr8kJcQj4NL0Od613xhxpQvh887XB+ZlI\r\naaEtwhykKVqnytBcxf9JB3BtNWQspD8UUgL6FovMg4T6Hrhb5sx0LwaJmtc2\r\na7h/zNeNze14UiBaN3DKcl+TfDLxG22Jgp4/hDkJbEsTUIg+p2g/PFFTE8Rp\r\nSJ6KdfiStRzi1YH1QmyY5ON7FciC9G7/W6yFVMkK2iA3NJYcB/oyLXEHAtkh\r\nWoAYhixMtcWwDgY3JRcEJ4Q34kMi+PQ6wj2uByYpqFPT5XePTD3sVqCYtRQH\r\ng3Tobh7nID4MDDnZwQk+8l37mAYoscn21w2eCV+IBNaSalNv3BW3ii37qvwS\r\ni24Fo27bXLNvY2lWaTKDngSMDuXtaK+2Cm8fmCOU0AFPIvOM8Is2YP0PMw3W\r\n89ZKHUFxkr0PVzyTE1AUmXji7+1bTCI271sfQkQN3nDbVAv2DNeF7mY2XUZp\r\noGsNvU/0vMEoILfJL6cPvMFNCZm5a5Mbcjw=\r\n=hysm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "fe00e9c8e220a5e46b1c85d6af75863adb0d5523", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.13.2", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.4_1657391059784_0.309262570405505", "host": "s3://npm-registry-packages"}}, "3.23.5": {"name": "core-js-compat", "version": "3.23.5", "license": "MIT", "_id": "core-js-compat@3.23.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "11edce2f1c4f69a96d30ce77c805ce118909cd5b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.23.5.tgz", "fileCount": 13, "integrity": "sha512-fHYozIFIxd+91IIbXJgWd/igXIc8Mf9is0fusswjnGIWVG96y2cwyUdlCkGOw6rMLHKAxg7xtCIVaHsyOUnJIg==", "signatures": [{"sig": "MEYCIQC4DOkW+TYz+NFG0r/hUFOQnsHtb1ffy/Pkb56ozYZd6gIhAKeZQqHrK7UzqPuLQkoRmBX3aZQJxrep+Nz4egZ3t++2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1F/OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHiw//TqnI48oiQzU0vi8klowPGA+CVsGN3PmvtROtSXr2SmCZrSjY\r\nxE9yJ5azZpzvSdQDHYiGoFrUGuU5wnLj0VzuFnCCieqK5XVlBy6ttSBZX+Go\r\nuijud1ceiD9B89dsmk5M4RxBye9KUKE6x5kXmAgOrDLHUOSIjkGzvdNUQ7d3\r\nZz21wwieiV831rQVzWx6QHcPuuXij9KQWAlpB2P53ALC8Yku3L6bl7+J+sFN\r\nLxh4prTd+Fns+5MSYBwgG/gGlU5s5Ohe+OOY/VO1/qCKHUFSaIa2nqJUKNHw\r\n88k3VPfzIsAMFT6bSAYej5aBhDv/HXIE4xk5iLL2IMUbpzkOm/Aps+fJB2tU\r\n6V8kvFM0GCN7HYcLtBPq8FRNz9wHyT9ktY8qvFj1r6+13Uk9dPmB1+OnqLXt\r\n76ZeKGUGIagx8WMA8KqQZ0guyD9UrN0fs4n1OGEojTAZc9N4Wy29udqnvRfx\r\nPR+ZJWIwnUo5knHrizcSEzC+w28Asi72sP2ooE3lsMoyRLwph7mfk4KV7Ghh\r\nOhbR+YNKHDb9NIrW1a/26ccTBr3B7OrJimjj2OVuSJUlQffOASP2obZDAFaw\r\nCKcKyJAxy9E9J9IG4ZecoRN713tNSgZLgY+Pka+mNkFJcSEk3SQi4a2rBjOP\r\n3VMIBsBVsU+KqsK4oT9l/t+O5PiRfJTUaIY=\r\n=eAdk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "35bfb87ed543b7f89a767121ce484a3b93826ca9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.14.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.23.5_1658085325844_0.7182530654862316", "host": "s3://npm-registry-packages"}}, "3.24.0": {"name": "core-js-compat", "version": "3.24.0", "license": "MIT", "_id": "core-js-compat@3.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "885958fac38bf3f4464a90f2663b4620f6aee6e3", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.24.0.tgz", "fileCount": 13, "integrity": "sha512-F+2E63X3ff/nj8uIrf8Rf24UDGIz7p838+xjEp+Bx3y8OWXj+VTPPZNCtdqovPaS9o7Tka5mCH01Zn5vOd6UQg==", "signatures": [{"sig": "MEYCIQDSUNDfeE/jlBwlP44Snvi+K8J/8Lvfk/XuexP67co7nQIhAPx7x8DmEOM8obpKfJSuzvtBqTFzBRGAI5U8egmnemYi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3iLTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiARAAnwB2jvZcmhzpvNNvxLgWKeeKCm6yPKmu0V+SjqzxaD51CctQ\r\nG4yh3zCFZ84KdjGVKasA7+WvEPwyMVLgN6jCOhXbQuN5Da+AUEX592kP80vK\r\nF6zY1gzRBCxOkjU2N9Ly4ffK1tSCw3JNX+0RHsZeRiQkHnc+RD36Hns/ZwY4\r\ndoEUoLc9vN6sy/BCBJybmWnY/H/T5aFjrdjoMyCbOM0KB6tHj4B5jgIDz4eG\r\ntXL69cN8dzro44SaxG9nEKWKhVMHp+PUK42Mx/UCbDqrrHIx0zIgFtplHtrG\r\nilOW7XsdK/RXfJVRoQCqKwHLxY6U7SlK07xPawrZmzQvqF45MfDWsftfce4F\r\nxWBpgus3YKfJCnDLpWAVyzp24l5Lbrf7SzNta7T+bsYknwDDcjx1xk2Qrzob\r\n3GBfIu5r4OBqgnnForm2j/VAzSGGdtU1EWLStSdSqd3UZy6tMEMcdjagsd9M\r\nFLbM1gXP0kU9GnpEtg4oGTRRNk3osUMHt/JtA/KrIS7Mx7ksPw0qAS8udGKU\r\nRL2Ztsrxqn+/nQlDy7TnSmMq+SFzpInNqUn9fLR2smfolzJb5w/MEON9OYUw\r\n6trgNK71b4rqsyKnPywHvbPC8AL/WRTgmkWhToPe1RmJ9ISZ1DkzX//793Fv\r\n/zGAbi2G8DqGnT2+NdmWvnzaeni/wVX3VLg=\r\n=f4/q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "cc756d0011639aac2d74912413f7b5c695cfe72e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.15.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.24.0_1658725075655_0.20813872169847647", "host": "s3://npm-registry-packages"}}, "3.24.1": {"name": "core-js-compat", "version": "3.24.1", "license": "MIT", "_id": "core-js-compat@3.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d1af84a17e18dfdd401ee39da9996f9a7ba887de", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.24.1.tgz", "fileCount": 13, "integrity": "sha512-XhdNAGeRnTpp8xbD+sR/HFDK9CbeeeqXT6TuofXh3urqEevzkWmLRgrVoykodsw8okqo2pu1BOmuCKrHx63zdw==", "signatures": [{"sig": "MEYCIQCb+ruehFNagxduHX1r4XFrxfqjLvD/IYFW4x4jYZ6G/gIhAIWqNbSkptPHXjsl1kn8G/TLWiwAlUmF6Uo00aR81NuV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5CHQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokTg/8CMFfiHAyZDJ0v0+plEXBSLp8tAGwW277DeowOX7rhv7qYATu\r\n1jVpR5NcbbsEYj9nt8QZkK5CxsTHAwxzX3WgzNRbL2yazbsXgqQQYHZKEh1m\r\nep/PtpMiRuKJg0UOGZvRz8NQ/91T9dF4UI7xFn9q4CH/DLrHd/nypnMdCvji\r\n/aKfbkdNBfjDSDZrA74qDTTJA03FQ013odhICfvJLw1gZqsBtCy5mIvYXy+y\r\nT+kusIv+STZ3JwwrtXedkRRnamYtrf/uN4XgNanxdRJGNAf7XKnTL6vxSkLg\r\n5S3L25XDuSb8RH09ffrS+D3wyDhwRH/4fuKaHShxwq9pewn9WYx1QDxcp3G0\r\n2ELmXd3yUTfOtEIij3IaIOB63KEp4uf9JC0+Nh7wv2/G0ZrfDpZDWg6IuArb\r\nr9aQi4AKzDAkqaweXBbz4GXWGkj9KlyXHFhZJM281SRCgvripWovNkOaYlwU\r\nGlnPhGeH7ogbHhlGPQE0fqF6owARUSydfS0SqCvU7iDeHZz+2BUL6eElBWQS\r\nQWBF1HcxtGTtgxXRgwWES+JwKqqSvrqJQccBZRFbDn7fu1LC5UYzI/fhg800\r\n7lnUWZNBZ/56CehGymeUhH+kKXR+99DOMlzjZFaViusmKZ/xeZ1k+2QvJ6ky\r\nsfSc/YLFqRAn8ANAqLWHxEHelZUQ/pv4dR4=\r\n=ZjXv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "e4b27bdc440039ffc2d29ec97628c3e727108a37", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.15.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.24.1_1659118032483_0.16731645399594441", "host": "s3://npm-registry-packages"}}, "3.25.0": {"name": "core-js-compat", "version": "3.25.0", "license": "MIT", "_id": "core-js-compat@3.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "489affbfbf9cb3fa56192fe2dd9ebaee985a66c5", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.0.tgz", "fileCount": 13, "integrity": "sha512-extKQM0g8/3GjFx9US12FAgx8KJawB7RCQ5y8ipYLbmfzEzmFRWdDjIlxDx82g7ygcNG85qMVUSRyABouELdow==", "signatures": [{"sig": "MEQCIElHNYNNaWjj4QrZdmPfye4pTSeVLyC0od0R7jPTBhGKAiBReDMw+PAcG9sUTaWB9bhRrIpMxCWUZfnHhvmFkuPMbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBohHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLEw//X6h69JAo/vQJKuMzpHhSSPDr+Pz/2JA0jb0hB1ZsTOEhEHLc\r\nlBrwpexH6lWgPKujoaUGj+i5LeUVnySqj3A4Q6leo11p34gG7DVDMabqzFIt\r\neT8eSrYLVDs2JOEiBtL/qjTbsb+yiwAavqzfvX0CF/yiPfDdOcbog43h8lGh\r\n9INuWIm74BasLCWUCziLTy5OA3Lz875CZrXTTx+KOsQMwkhXoqRBC3bbvZrP\r\n4+DktACraPPYrAK+3drcL6bQpIXVG6XIgsuDqnurTUxDInULvLdxYvrkUq1e\r\nAW8QrJ98A3P49IZIE1NC1hk6bKyh7+2PoyK6hEcV/nkjAn3yBgf+y9sU8Ka6\r\nkpR0QZ2UsE7l9I7ERNgbYxBrI/pHROeIClEGxSaNZ0sYbcXfNREME7D98+pb\r\ndlgV8uu0XXB7hyPOvNffJDkqL/cbXM58m5YUPtO3T4hvF/mYWmJKbkRGuhYX\r\nHa6j9v7HH104onLR0BB9zmhtOhjVCG1+CHaBfZu1LzJYR7ucvPRJee5oi8rP\r\nc6iiNiHDBA9ASC++nLjuEADPcadJAcbUoBmdMZNP6VRZxYHQsgoxB+rYqtRC\r\nBm4MCvUYKH2TCaY2eAq2X2YHYnByOWSZ+90sL8dd5E/N53vRe9HlF3uFJFY3\r\nVoiYB4EQIdhKTbBhjbFDmy8NlP9GTSoKKSk=\r\n=SJzs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "b61f3c334f7a3010f376eae4fc465e68232da102", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.18.0", "description": "core-js compat", "directories": {}, "_nodeVersion": "18.8.0", "dependencies": {"semver": "7.0.0", "browserslist": "^4.21.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.0_1661372487727_0.9376112604379396", "host": "s3://npm-registry-packages"}}, "3.25.1": {"name": "core-js-compat", "version": "3.25.1", "license": "MIT", "_id": "core-js-compat@3.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "6f13a90de52f89bbe6267e5620a412c7f7ff7e42", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.1.tgz", "fileCount": 13, "integrity": "sha512-pOHS7O0i8Qt4zlPW/eIFjwp+NrTPx+wTL0ctgI2fHn31sZOq89rDsmtc/A2vAX7r6shl+bmVI+678He46jgBlw==", "signatures": [{"sig": "MEQCIFVIShIKaPuoNzHHz3vq+in8KirO/48Hl3tp5dQY1ijDAiAGvG46W07zZSuSQhTAf0lBXT+a5T9PwPO0Sp0NalHHYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 565822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGP/mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrt2A/8D9tvEkdxwpLv+a8Dcyz9hekCrif+j4H2O4EoKEiUq9j++xGF\r\nzFepihcuoi0nuKw9i6WR8wwR8m5GqXNgDx3ESroqibVkq4ZutqmUsEb4A5YA\r\n+n72LgkU1WwLp1g6/Qrtd0hThQJhhILUy4JP1I/08aDHT6Ool0s6Cj1jGjtc\r\nabWInPMTKt+LxJ/mN/GVVvYHMNDXdQCq6o9mZH/h0OQ28KyhXHKC669RXQeY\r\n/48zWFE+1JNe5CcVGxomJ9tMonpTJOuUsKOTvWh7QYAhAVCL9/QLADLOWWWR\r\nqvH3TgMEJWVj9OMILrRRceQkZdF7VjBM4i2/BU7xT/CXr0h+0zDTaR10UYct\r\nyPgvGaTxyH/xJ9/BrLIEiJg+p+dziq8zuS/FTM0wpkB/lxot9m+8q6KYkLPe\r\nj/6XVByX1RQiFoYtIKEsjTlfHbcPs8+MVc58TVjP2YCgTO43ghILssKo35eh\r\nokMnGIom8MGHl3cLqZkg+55KLQn/OaAdlk3Kuo3Qc7b/YIBz4mdXDTCnk9bJ\r\nJbhDmbNfSanvuYM5om8J9c4W/szFdwZwrtmhixPAIPvBH371UhOa1g6xmLpr\r\nInWzA4tnzSWG3Vj4O89xV4jHv6oXgWqsEamZrRDapbtyuYZ2QVdlF5xmJbZL\r\np9XTmExWQFxOGHnwzTAU9sY0NG3Z6hPXzOk=\r\n=xn0L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "3c2e601b9a58af4dca0cd13aa943ab66b3deb83d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.1", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "18.8.0", "dependencies": {"browserslist": "^4.21.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.1_1662582758183_0.04054661569297191", "host": "s3://npm-registry-packages"}}, "3.25.2": {"name": "core-js-compat", "version": "3.25.2", "license": "MIT", "_id": "core-js-compat@3.25.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7875573586809909c69e03ef310810c1969ee138", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.2.tgz", "fileCount": 13, "integrity": "sha512-TxfyECD4smdn3/CjWxczVtJqVLEEC2up7/82t7vC0AzNogr+4nQ8vyF7abxAuTXWvjTClSbvGhU0RgqA4ToQaQ==", "signatures": [{"sig": "MEUCIGh+G4KcRYpUd1gukn/2Pp2190j7tFS4dIMp28VY+z1LAiEAtN9LywQ0bZQ30luxBF8uH8IE5taBUjploNG1eUiMysk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 566369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ2BvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoq3Q//R88wvutdaatXcOq0Xp60/giNbpUSWPm4MwSKdSs+gQ6OOffo\r\ndze53UDZQG0NC1oMMLDjw72l/csbNHN0bdiVSL7N3L5Lp0l/bakOCxEYmvXK\r\nnbqApKbh5PfV+vqDWj6xmykDgNOGy0Jh+5UwVmDnBpvnTLuzXeHoCU+RGuR9\r\nHqJz3lNgNqyWB3fwphI5JAM3tdk0dR+j6hViSd68ERZc11Zr4o851JBmBbsz\r\ne4+eBx31O0iQMobowkFCVtjJOP59sE2wbkIkdBBwvSHJhluNd5Jhm3rCzHvb\r\naDlSQm9870/ON3ldfPfS2+9CxAXXsf8pCS+1KvzvAK0HiVfqaDZ9199MvZq0\r\nzSLhcoZ4RYpiP7dbx3g1CWKkOiRxzihXjG7bKOhontzMvybohxLYwfMHQQWx\r\nOyysNE/8VjNH/CHwxxXwgNEq8VksKGkpb2RH5ZueuO+VJK20T5csEYRBgBJY\r\nTZGuVSFllDNZjq5mzaIYR6+TYVdLAFwhnn+3HO43xkxJtXUJ4zWRLZRklkwB\r\nMRU+ql4ySgMxRmSHSJKrQ7f9uBCBPAvw1N2bazHQ2yfwAUnQP7pyLoSL3x+7\r\nGi1dbmKT8le0s1S3yRVekGh6IBuwU2F3nO4tdBB3IO/DahIRwRnbkn/kBExE\r\n1zrCv4X+dNehWqUvhCki1QLpv0vD6dgcFdY=\r\n=7tri\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "77771c7325763e27f39896112764ac716544fe60", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "18.9.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.2_1663524975364_0.6415035559202504", "host": "s3://npm-registry-packages"}}, "3.25.3": {"name": "core-js-compat", "version": "3.25.3", "license": "MIT", "_id": "core-js-compat@3.25.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d6a442a03f4eade4555d4e640e6a06151dd95d38", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.3.tgz", "fileCount": 13, "integrity": "sha512-xVtYpJQ5grszDHEUU9O7XbjjcZ0ccX3LgQsyqSvTnjX97ZqEgn9F5srmrwwwMtbKzDllyFPL+O+2OFMl1lU4TQ==", "signatures": [{"sig": "MEQCIAdVswd6xCNmQOzyY6gCekMcfjJpfa5+L6VMxUvi5zd/AiBUxDyEjNbDxkV/qjtft0Lf4WM6/wXyZeV5ItwZSQldrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 566511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMOCSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmodrg//QYkp1ZsbJC9wdjSHOY+XWELOULvwcaKNGgqP3AUqFyDIq+qC\r\nh9c4kuzhPVxKYjlvqamFHIJmKanK3J1E8TsQTnxGDnii/spkq1CidK26cZwn\r\nopGS7rZGmY1uVLOoB5zoEdmn9BzSykts14zYZbE+TrTrdEOO5ebCBJ3k7to2\r\ni4QuiU5gSAiKblGhnqOn+ZlozoH1MvHLqmVcciwmtobOlA7zFIECk1DCiSEw\r\nRw5/oPY2JKu7XTIy3w4VIxaLU1HDxT+6p/ATnR53R2ZWc+SaEEczl6LctkYF\r\nI41agSVWuTA282bDIFRNdPbRmOHLoeT7+j17LZJvP7Erq/KwiSgnKlN5U6eJ\r\nlWhZbgxlXiKalw6jNCtp0VHCnpruHCRgnlzKcNhwSAhMbejLzq6S1iFl4JdY\r\n4ao9J7USanIiptgH0w89AYig95ccN9EPb8uE7Q8yN1/OMYSE7/GXzalVv65l\r\nuJ8A/jLFPVxYdBsaiR8aTxQ4w68uLoi7qbE0uLHic5q9u1R1MKyfBzI9ivcJ\r\n/GlJPRSNUxtyEcuHDD4NbLBsf/0AarBCsQGTxN8ylHPfvcl+bC3RMuvLf3Ip\r\n7EVc03PfSDCDVCLbXzbY6e2Er0q7ZtRwt7d/eReOGWEYfZ5ylADH/s8ffmr+\r\n5Udo+Dusiobrz99jq+pkY4rdvQKRaXbdhdo=\r\n=DNFJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "12509bd97a7755ec4b10a7de2fbaa2a7e2845b68", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "18.9.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.3_1664147602153_0.057952385938596196", "host": "s3://npm-registry-packages"}}, "3.25.4": {"name": "core-js-compat", "version": "3.25.4", "license": "MIT", "_id": "core-js-compat@3.25.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "730a255d4a47a937513abf1672bf278dc24dcebf", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.4.tgz", "fileCount": 13, "integrity": "sha512-gCEcIEEqCR6230WroNunK/653CWKhqyCKJ9b+uESqOt/WFJA8B4lTnnQFdpYY5vmBcwJAA90Bo5vXs+CVsf6iA==", "signatures": [{"sig": "MEUCIFTLixM8xcB+Okl52NVL15u6QcBOS4dB/z4T/eQp16WzAiEApr+4V7v0ZlFoytDbnToCJgia4J3jezm4xvVmXmFNLgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 566684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOdJjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeKQ//cCirLWzSb6W1TDvL2D1ZOJcOxc3ISz8e+STWaw29/MHkfFL4\r\nylA8qiEAhfU3gldTYW4qJryuy0gz1SRp5YhYTI7r//mW2qryjwk2rjGFSHJJ\r\nHVSlKOJVKKHfBpUOvN0LYoPyzwXTzSEuUucrlvg7rulbonqcNs6IAM5suuHw\r\nAB0fu9xsovNVG/zk2FDQJ+1Gujq2CzCOBlbVLYXMRDcC6GMqFLXHiDH57a3s\r\nbM8gpJAgR2DmcPM4B4DPGiRRFPd+wW0VfKxRNaxDSISAeBQh4IEfvXfcd81G\r\neXEYQWFUUc+PEBSZucld0ws5EC+iVTmdrPIZbg/MjgiglpiOONozCTkQN350\r\nu6a91Vbg63yEtvK94gxdpAw4e+APHpIx7hW5/BaR/Zi1uIKnJyy/K3rw5pKH\r\naCLwlWG21bU3jcOMPptpWuki2g12cHMmMytRnuDqR5ewRzIT+E+rnSXTOCkT\r\n1oAtQvZSn48tGFc/hTYyANkylqgIEyAONo+yQPIDshZMuGudQD7utZ8dVb32\r\nFxmEpy8Ptqog90HvuvKKSqwx2y9Whbtqx8nxuggQ0i53LLSy5RpPBRfmcyb/\r\n4WAmlEQX9yUdRzlIYyt9lCQGDebTmVFVGO0Cs4UozETuOaTN57mFbf4Pr7OX\r\n6EpknCmctku+7B9GIh+g428k7059yT/ZnUk=\r\n=uoE0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d59e440f1472cef63579edd511a8ea1ffa474f69", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.4_1664733795637_0.8987346070953433", "host": "s3://npm-registry-packages"}}, "3.25.5": {"name": "core-js-compat", "version": "3.25.5", "license": "MIT", "_id": "core-js-compat@3.25.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "0016e8158c904f7b059486639e6e82116eafa7d9", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.25.5.tgz", "fileCount": 13, "integrity": "sha512-ovcyhs2DEBUIE0MGEKHP4olCUW/XYte3Vroyxuh38rD1wAO4dHohsovUC4eAOuzFxE6b+RXvBU3UZ9o0YhUTkA==", "signatures": [{"sig": "MEUCIQC3ptUA9nHRSEUkJmD4BxFZS5x+NKRNl9hJwnHTWZlkMwIgdYVCYfnx83HUBgjHmuTONeABJ5Y2+YbwWG9/6UyyTKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 566684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOys8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosAA/8DNn50pjG4BMgzsyitf9tA/I525KraxEN3uXCADqHgf+xRTf+\r\nB/UjW8mTRcqCBuz9KvOh+7BnqYmiHVz6e6+CGPTCRIWdnWLaGwoHSrSJ2BOR\r\nKmP7uQ2O08xVCo1ghP0cdemtC1xW0wYnpU1m1ZQE9BtBi++4Ck5nIjT47r96\r\nbemFwdxfpj9NZvnK8sPkkK1edkDyKqK/jdogvGyuNzo51kSsUaWfSGZAcw/j\r\nYz3Yk9B96vofzrmJIHq2AZFQAdLd6V3qAbaZtr/HCGjnvRTk4IaFbrUquKd3\r\n5H8/ZNm8fGmQ0DlvsuNZ9J1hgfKOYghj3dCv7Kk9GzrLJba+4Sg7TD4MT0YK\r\neqHjl+GUj0/3O8ctzJaGAAjc7mjjQuFRM58mg5Ev5lkJBki3FyIsa7wN5L3W\r\nkJfRZlxaboF9oGXpybKVkA96emdTKKBlxsk18PoSy3ZU08plHywZQG+bD177\r\nJHwsjsdw1tL2av4eNKwQQkeJ8j0Du5aPMYiD5Z7JMK1UnJLKJ5rvhgq8tX7K\r\nsVznJ/gRR6sld+Sxg3GorcG7mFVeHJTCprHYp3yF3B0wLnVQyDpqCgVoSGUZ\r\nprequ6SgfXX/QKfCnRwSvHiZumzOVM3TNI9p0miSsGEL9kZ5tLxLCqSt+YmZ\r\n4+W4CtCt81nlseNhlhsH13jRnDrR8uwOmIo=\r\n=fI7v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4a8b1f78e4bc5d3c4a0102325d3c74815a58261f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.25.5_1664822074657_0.7870664764441508", "host": "s3://npm-registry-packages"}}, "3.26.0": {"name": "core-js-compat", "version": "3.26.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "94e2cf8ba3e63800c4956ea298a6473bc9d62b44", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.26.0.tgz", "fileCount": 13, "integrity": "sha512-piOX9Go+Z4f9ZiBFLnZ5VrOpBl0h7IGCkiFUN11QTe6LjAvOT3ifL/5TdoizMh99hcGy5SoLyWbapIY/PIb/3A==", "signatures": [{"sig": "MEQCIBvBSNe2ZcRlcTZIiNorNTx4cA9mpJq79j4G/iQY/9GAAiBb2k0M8uyaHAOd/k25W9YIqg6Lc6Bk5AGCFWIuzWX9ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 570230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVYjNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpbxAAkoh9fOBHbF5CqPtbXESdvG8J/l2KeazHEFSV6cgYdeT5F3eS\r\naaW/AcsHOOlClPmn+j+Ml6qo0XdDfpQ0NWVlXBA+vKpBvpCrFqd8eQWAMNLa\r\nW0nPH3jXNmkhnaXqiLZKRejfaLlt7tKGXP2i45DQNuhad8ki8IpF5uacXoWG\r\nyv5/Kj1o+F8SBpPi9DAySIVCgxefAd9TxTr1cLmqTiysYTuR90GdnKc2R+dx\r\ncWKkv786bAkA5PFk2zdC5vJ7jrLGHSGyTIIWRniE3DaE+jPJfe//xKjPW7/E\r\nRW0bC9YAU6akhe5uEKBwQP0nwE917142e4waa92mTHAo8e/0ckqcmK0FHHnT\r\nd1M4bycud1KQpwA6SfxDsv7Bby3jgiKZjIHPl0FvAXdyCPMiE5nsgYpRNTtu\r\naPhvAhDD4fYEl6+6o6Zt99qlaMZDnsUHoSf5cRsWuUZb4IkWqJfstLb19IWo\r\nLpHukMYWxA+kEgsQYOEhPXFcC/zZ9LNmsmyKJ8ZnC/fGV/zA5qMH4zmmmDkM\r\nayYWfmFkwfA9rtJYmBf10vt/gIVJXcJ3lihuE1G4XItBHMAvKgF9Q4xPWSL0\r\ndo8L5PuESBGu7zNkRslKpx+LJZxnC/H9b0RMOWd1NDFm86+eUClJKeM7E+mk\r\nhCsoNCuoTFHZczYX6y5OGCvTohTY81X6gew=\r\n=e2QJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "3b13f58e6bf182489791905f3d16e40809c6d092", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.0.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.0.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.26.0_1666549965424_0.2595945695364055", "host": "s3://npm-registry-packages"}}, "3.26.1": {"name": "core-js-compat", "version": "3.26.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.26.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "0e710b09ebf689d719545ac36e49041850f943df", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.26.1.tgz", "fileCount": 13, "integrity": "sha512-622/KzTudvXCDLRw70iHW4KKs1aGpcRcowGWyYJr2DEBfRrd6hNJybxSWJFuZYD4ma86xhrwDDHxmDaIq4EA8A==", "signatures": [{"sig": "MEUCIEugLX+jIrzptJ8s9VAI0AmeVWEPKZ8UO7QT89hhS4RPAiEAlL2CPw2rdJpkNmUknOfoHN+hOBFjwNKy43/UIupKHJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 569980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcTNzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo75w//eBsjB61e9Xbax/nIQnpgolYeenKXNa2FC6JdSmYj7JitOKy6\r\nPWY2IAF2imo2bqBWtR/2ly3rO7LbqyThXzYHOnOcluwQPeXEVczfGbUZDwBR\r\nFQhzYgbtxXiV30QKVkNFid/gPV5Rn9ItJPKNY1Swd5vPc1o2jL51pZ3GLV0r\r\nonWFuAs6wZf0G0EpwTGmtgPlrYTDi66Ueme3qRMLLHZyiTysZZvIyR1LK5hP\r\nClG8VcHUuTbupjsyiUDZHUUG0QDigiLgq4xC1bU6C/tZxzABpTif/s553/cM\r\nR9mfgp4cFHB6yjlLVjyTl+KycUkGKKaCPPNGUUdP/ktZ8yLUW5qskqsrN5jZ\r\nZ70luBh13dSHRwdz4DpYStxUNWyqtDCmE2O3j+x2inQBFHooCCRIBiDlHL4+\r\nbJ546UI4EpKIARRP9A6mgGDlLanoX4tbgIDj4EtPWnl4Yew+QS/zjwAStMes\r\nNnzQXgWUOF7l4OpZE1uopf1WfRItL2umnDegsM/cmcYe97ltUrFZkEOj0Tpv\r\nx+EWqfUbOU1Oz5+YYddvrRGftLoEByAwkyTSF2TlBTMnd/UgY+ecpDLMVrev\r\nJoUxIt6A6A82L0VXeCu6GfpoFDV4F5JsFRjox/TiHK9/dovaovRDb3mWbeKk\r\n+lj873xnwj6HlehjmwW9lplh6Lo79kUuVmk=\r\n=o4Em\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "e47e6edbd04b3610b5824bc4880ee3736047ca32", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.0.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.0.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.26.1_1668363122915_0.33693454885749463", "host": "s3://npm-registry-packages"}}, "3.27.0": {"name": "core-js-compat", "version": "3.27.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "e2c58a89df6432a5f36f3fa34097e9e83e709fb6", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.27.0.tgz", "fileCount": 13, "integrity": "sha512-spN2H4E/wocMML7XtbKuqttHHM+zbF3bAdl9mT4/iyFaF33bowQGjxiWNWyvUJGH9F+hTgnhWziiLtwu3oC/Qg==", "signatures": [{"sig": "MEQCIHM5+s86PcZhnXnTK1G5nxDkIbOYihqzQy0M1YQGENXPAiBTgYCoform/5AZcaijlDT/bUqHK66jGCvp5TigBoUofQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 625429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqJG5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHXw/+JLLX9vo2qHVpc9VHlsvBMv+qRyOcB4G910/7rGyfI3jm+NKB\r\nGt4i5vND/W+HQKADJDKOC/k7OfHjcpTpSN67grw6vRM+QLKhhmPZBPsNu2nC\r\nZFxxL+Dojtl6/FARGQsHrojkNDIKYDcouAKrjmarvQAnOjEs9Jj3Lq7WJqin\r\n1IFzhGxtT/sBcsl+Ajo9UjYzdP63BdK/6w7rsv4alJTGtU9PLYwPhVlNWQmp\r\ng9UIEI6+moz2Xuz3DqzepZmeMO984ZNVlf8VcuNFer1pMZQfKCrXYMQ5Cxqb\r\np3rFY4VE/k/HzPCqDhy/5DDWSuA2JIk0RIDjVcezz05SOaGF3cPOJGVC4of4\r\ngl+NI0z5v79k2kN6/dQkPACKQlB7yfzueu2NSPOaCI3ubLDRyDjE5XlhkIcu\r\nk80Y3u41Rn82MvsVcwO4EPB9KXk8zH3Sj8QQ7ShZZSoqYGSXKaTQEiJ+n0ea\r\nRyPqhRH2v4yBkLC5Qk6mLo8cCUk8BOfh9bAJTSlKThJMvTKHsrVdp0I08342\r\n/lCt/iU63ozUt+t3+05/m0ejgrtOxdwksaG8R45OVHHCn81683OroVcjqOTZ\r\niGhZnblqkxtsaxYOrXdaj9yyXbHUDheMQ+IAfBeO9nlrOMHMgHeSzJGiUUJH\r\nHAmdSBcRqjL6EUwt3WVcYrr7ndwhExvVDvA=\r\n=i8M5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d73e06eb9377f535cef3b0d2c8a05740efb703ae", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.3", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.2.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.27.0_1671991737676_0.8812196585772987", "host": "s3://npm-registry-packages"}}, "3.27.1": {"name": "core-js-compat", "version": "3.27.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.27.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b5695eb25c602d72b1d30cbfba3cb7e5e4cf0a67", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.27.1.tgz", "fileCount": 13, "integrity": "sha512-Dg91JFeCDA17FKnneN7oCMz4BkQ4TcffkgHP4OWwp9yx3pi7ubqMDXXSacfNak1PQqjc95skyt+YBLHQJnkJwA==", "signatures": [{"sig": "MEYCIQCfJpP92YjGYp49CS5YFjQtwwLmH90qES2OTCeQ/mv+/AIhAJzF5HrhZq+dRjO0xHzQLtInaDpDCdfpM3f0SejQ8ILD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 628634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrdhlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAgxAAhx1kn0wQUzQCFX0ZVsFJpW1tnEsIsKxQi//mKSsTpFK6pkqv\r\nFld7nKED1J3IQ6jm3/0a/z6vagmYCTl3ew0AcJ/2d0t55j+7jEDViSKkJzMu\r\nxlkxAGB3EGemWCsxfVr+Vq/d0NSpjiXZSdgP2lkC66Mjd/HomESX4jl8wQC/\r\n+cytQcaZ1KzDoN/L17X1TsxUGYEtIa6N4A5ITevrWCe/aA3pJxJ8Y61V75Ip\r\nImb5A7gVTsWuILYgS70ZbsSjtPmT8BV0NNMqIdYDbfZXu/HjsuEUodWrGGHZ\r\nGO/25KYe32B7j8xdwrwopQNRMLvFOxFspMBsGtdouKKy0MaUjdMkre9aIESA\r\nHlEKyiKfjhxM2XqfRzKEgEu9KUNYGKRWPPy2i/MTWk8w/Xg8xNXWdKzTao6e\r\nU6UIvKe93JhUsDbk5GFcIFWNvrnYTZras8yGywPsNWOeq089NYcx7dWdU/Hh\r\nqGFVVf4M99XQ2gHInGNIA4AvDBN3bmyGOnAan5a8xPHjWVWaUGUm0kUQ2Tp4\r\nZoi8Pl16mWEHdSWBaQEk3utt/t3IInpekBu8CIvj3ybpUaiaAQUxQqap5e3p\r\n6ej6ayzn+sqZMo9nynYwZ2Hx4qRvKRcyN71/j0sK1CMSgGaXlbFgKQhpWj7/\r\nLDc+pp7ilog1FUaQN8JPy+GOaIttDgYpWvk=\r\n=28Z/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "1b54309cb699fd42ed8b39d3db8832519e45673a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "8.19.3", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.2.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.27.1_1672337509536_0.4836415059611221", "host": "s3://npm-registry-packages"}}, "3.27.2": {"name": "core-js-compat", "version": "3.27.2", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.27.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "607c50ad6db8fd8326af0b2883ebb987be3786da", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.27.2.tgz", "fileCount": 13, "integrity": "sha512-welaYuF7ZtbYKGrIy7y3eb40d37rG1FvzEOfe7hSLd2iD6duMDqUhRfSvCGyC46HhR6Y8JXXdZ2lnRUMkPBpvg==", "signatures": [{"sig": "MEYCIQCEyeademYMW5o2q2G3zyJ/QszxsVlFSmdzPc6ZBDXsSAIhAJz+q0ofEsoyZO35ztBo86t6sReO/wxeOcNmySfHxRtE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 629790, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEpsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJAA//QEpOjXs7KvEmOBxQpgIVaoNnkBMjxvfjx46iODS3eOmaG77X\r\nsANbuzyUrw7F6jBJoMTD/DF1776ef/ejbJzh0t2D13sJwLtnDEjHEx9qy0Am\r\nxF+jJGC3C7fqQCgFkEBWN06blVERfMF6QYuVFb6G5MlYoGHI2h60qR2FRmIN\r\n04Pof1Lh4C3IaQdbFaET12olzbxRjdG31/8CtGW0iWydAr4JMNcX/xMC0GnP\r\n6FgO6H1MbukIDbsIy0+fjUzj2/fu/EAdafTwOwatgDZs5vOMRY31W7uGqHrh\r\nKTjvurfxN797IoDvSV/oqml/4cpvH5FUcTtQ0Y+mPvApGxQFFzPsyxAPGjt2\r\nnqx+IIeLhioj/frRb9KSZm4TTzayC0zft5MuY+7eXpbTuYeojUjje8tmZ5EW\r\nn/EeeUySdxz71PrNewmHRf5aNN+9nNZ1c5Ob+rnX/7i8fH/xuMXCSl99j8D6\r\n9XJrrE2/ZxgAYW5wuy8G8BKNI3BKSa1K18i0FuyUZ7LCgByvwbxaUg8OGfmv\r\njOUQnbIRdDjLFBEz7yfSjkGIRViUhpzam5uCn4iYTn2J9EoKzioWyCP6Pdcv\r\nab5A77mHm8XjGFzcshcux3xm90BgtaV/9KN1O0/dEQQE0x47NAefgYHzDni3\r\nXHCSB0dpEkON/e0x+KmmMFrXn1O1qvJ+9UM=\r\n=51XK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c56b97f1c90f0e04fe37d1fbceabcf5835af2c01", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.2.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"browserslist": "^4.21.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.27.2_1674070636601_0.14793358463326722", "host": "s3://npm-registry-packages"}}, "3.28.0": {"name": "core-js-compat", "version": "3.28.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "c08456d854608a7264530a2afa281fadf20ecee6", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.28.0.tgz", "fileCount": 13, "integrity": "sha512-myzPgE7QodMg4nnd3K1TDoES/nADRStM8Gpz0D6nhkwbmwEnE0ZGJgoWsvQ722FR8D7xS0n0LV556RcEicjTyg==", "signatures": [{"sig": "MEQCIHL9QPZt3aRfYUQeOn2mntRx/OqvB7/AqDgu1iFAJbaGAiByHv6eGNH6Txlx5nuNa0prxEjLvax8UD+fW+Q9xw3l1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6ouCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolzQ//fGzrauJAAiF1cOsRCUoZrcNMK6/6PyrIk7lkTGseccCSWV1Y\r\nLPdMtzGuuZAJIkGhkzOE2XN25hJP4K5EM5ONIz5AHOG5RQeZAuZOIafnwxZp\r\nPI8hHY4FKh3jOb2WnxvNualIPONkxA7033O/0KY6YN6ghuqmaOB2r+loPrOY\r\nh0uDxHT+Wmc5x5SqfCTRU3oXA/aFe3LzI0QGRt4syrJw5r/BqKyiY9lZ7+Mb\r\nd0BNe6gKzTghoT2zO+vraf2Iaj3oOXHNRo+QZi1NlLl3NwkZvgZr0aVkJ5eS\r\nxxmCes0JTzORshnpkoNjHVP7msORtlwkHLwIrQH6hCG3EhqHhPnWqhxzixBM\r\nbplg9eW00N12cFrmE5XFuCJdZpHZC4Fhln6I9ffQEgJi8hHWy8Ar1UbCwPah\r\ntSENpXMhl0zzQf68GSJP5UF/WAWw0owAnTeqQIc+BamPu1Co6rzJHv613yB4\r\nfOpC3NhlCooZo+ed3afaH19Rl6hzVH43X5pq9AiDFhQVTsgdnrEbSO0mXGZ2\r\n14HgBjj4AvTlbDbWf2L0lUwxbE/3dKbmhZkgy3Ltq2s9ysGCDNvWtXxIHZpj\r\njkiLU0gQTYC0loq6bUJnpJgQIuwxD+0BFmxojtve/DBfltbRfNBT1fb7edrs\r\neunLWtXu1PSM92CuHmMGyztx2P6AOOwrc9A=\r\n=JBeD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "6f721fa6f173e019ba3f236ee59a6ff06f4e1b84", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.4.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.6.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.28.0_1676315522345_0.6472247287990402", "host": "s3://npm-registry-packages"}}, "3.29.0": {"name": "core-js-compat", "version": "3.29.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "1b8d9eb4191ab112022e7f6364b99b65ea52f528", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.29.0.tgz", "fileCount": 13, "integrity": "sha512-ScMn3uZNAFhK2DGoEfErguoiAHhV2Ju+oJo/jK08p7B3f3UhocUrCCkTvnZaiS+edl5nlIoiBXKcwMc6elv4KQ==", "signatures": [{"sig": "MEUCIAzwgvpLZ05J3yj7RBQfwpPaKHu8CSGTqvvCKicki0VrAiEAk9fLNfmap1ehVqsk7C1SGTgF8OVhBWZUSNr1LX9xtiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+80pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5og/+IDCoAkCES+BLeOZGncs/fORVsuDZMwI0vm8bzWPMasa+ExdX\r\nwV3jztk8zDd+0S7JWM/73l5n8hjBvT3heuTrDrfdPk5FFLsbnh88f5IBpcMe\r\na4odvWWzeq/5VVK//sqLFsQ3RKhXD+4k19wPlAlRV3O34+gBjiSe3ZuHA1ve\r\nNBNfcbfWSNwY4MH6p56ZlzbumDeEKrhpbga1BFzyhQV/nWIw80sQuvF89BFC\r\nmMZ6fGwuLtmYKvFwSKNkwEupY2gE93fL3XcwNJU6nEOIPkfCLHWU7amUq4G8\r\n1eKVdNlL329UN4P26/NNLDMi+wZidjV99NYVTBuYxnWR7F8tnxA3HJEfYTk6\r\nIMaPThV9qXGCug3rffbAZq5GUu6oTh6s5w0VGzY5KLD0JfJimdUqnU80GZWh\r\nKBXAGUKaBLii2YbSzCaKpob32m4BVwAmOiAWsmAxdj5VkER01HLzAjfhmuXo\r\naPPFF+URKzc8SeGl5HuWVdRWjUJwLfvKal1iPMjIosNe9z0BXI2KLgWNT+FF\r\nIqxIUk+M+8n0pOdq3cPcQUuaW3tpaFmRhbYOBvh0O0dIzOlPXz5yNRYtUu3N\r\nUlglUEjnJLxU7GWdWRL+NKYI3xe0JJN/ibZNVhefKpNjPR4DaCes8t6Kx1pY\r\n/3P/tJaMkvwxeUuO0LQpsWcxlveBKwnijvs=\r\n=VEm/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "8c77615bb9c7e8153ef7bafb65625d4375a6ccf1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.5.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.7.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.29.0_1677446441282_0.07682450161281218", "host": "s3://npm-registry-packages"}}, "3.29.1": {"name": "core-js-compat", "version": "3.29.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "15c0fb812ea27c973c18d425099afa50b934b41b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.29.1.tgz", "fileCount": 13, "integrity": "sha512-QmchCua884D8wWskMX8tW5ydINzd8oSJVx38lx/pVkFGqztxt73GYre3pm/hyYq8bPf+MW5In4I/uRShFDsbrA==", "signatures": [{"sig": "MEYCIQC/qQZswY0yuDfrrTOxjxVzUXg1DypvoZ6mCtEFrWrJCQIhAKvOeA1Tjuqyfc3MKU1vej2xtOGt5DAgXWwNSjnXYwkZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDrvjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXYw//ebdr8xKIH7WuS03vSag1BiMFA+7Ucvrici1cZrIQroxeDtQJ\r\nGz4zD3LQjBYEmMW5g5g499bEmz7rNEV59amb2QBbZkQMKULxBZzm2RXMB/L7\r\nx1PE7E0jrYKNEEBtIEiS/NGOp/lwsnfUnK3MU/AIoS5Hm7YXFGkyNeFpYtlT\r\nh9GiaDa3l+Sl8ic2bOYbWg718LGSmP1f7L9d0niArmYUUez092qWXSq5ak34\r\nP3zHqlwmMQsZvh+usNT3iUKiMqtJJxkjNdHR8I4OyjW6NCBntmNyF+u5YP9q\r\nH926Msej8pS8+2GIKURUx4QyNyepHqBIzjHIuozOVnxblyUH4uM4Js00fXMk\r\nkg98RUJg4wci3a9mwlvSOJxA7FU8NAPgbc6i2bDcFARC3j0HRD7kC72os79I\r\nYQE82+CdvpDEJ/xU12ncutwPff/ZQyR7+yqHCwQLjczeHo4ou5a3MgKVwGpf\r\n15abEIpONRC6XdfT1yqQd0vzBwy40WoB3TYSG3HZP7dsje/zuIWcj4jCIgo5\r\n+DRI5grUhmmeveZLevpogozpePgGxRb04S71mwl/gKLebvjEmEo/N90W+jhK\r\nwqKNaExoPWHpqz+RNMGZGfb91/4jsjl4LUsiViolGgzjW7vETqYrdwhm7E8b\r\nAi627cQvbsb4Sh2bYZRaqIWWRwm2tStjDRQ=\r\n=d3VU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "f50b58ee8fc14bd88653a82cffdd342c988b7df9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.5.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.7.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.29.1_1678687202989_0.8547458573768489", "host": "s3://npm-registry-packages"}}, "3.30.0": {"name": "core-js-compat", "version": "3.30.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "99aa2789f6ed2debfa1df3232784126ee97f4d80", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.30.0.tgz", "fileCount": 13, "integrity": "sha512-P5A2h/9mRYZFIAP+5Ab8ns6083IyVpSclU74UNvbGVQ8VM7n3n3/g2yF3AkKQ9NXz2O+ioxLbEWKnDtgsFamhg==", "signatures": [{"sig": "MEUCIQCfRKFyGmi1gLvpKZAUvVR73z19WeRq4/TQErYymKBrmgIgasviQb0MI9rDMTAjHrwei0xedxd5d11icseorrYYeIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKzbAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXVxAAmYz9nEQKcqhn5E77Ll6IDlGEAsHsIcHup2mrE2R7OY2MSFSA\r\nYG9P+tWVGvzuZDmxXOLs/YZn2pxSg8WS+AUEaaZst9o2i6NaeCEn0PJB++Z2\r\nuXgaVUjSSQQYUWUHi795jF33nHL9ak+RFqQlm8D/O+IZWIBHkQTPEqJJZjrf\r\nOmu2fLleQGV3tC1sylIR66vj1fYyf8MP8DjL8+Lm7vdODJ/v8eRCJ9f07brc\r\nq2HyMnKSspr5J/fCzAMZEMV6lhIAek2Hxcn8EwEDLjMePeqwfZB1ExKEULp2\r\nqGpr8PsoWdRdFbw95DfJsOV0kLA9/MVy9bojMlo9K4JRlsPBv6cwMFWPieSI\r\ni1sAOG6Z0n8gfgopwpCIm+G/Uw2Bs4NGFw58XwZylhjk1slpBxGmVRNBQ/49\r\nd5zaim54Pu3Kzm5LAnSK/Cem1MeJ8egmCgeFw/jwy0Z+8olQu0p3peRF6uxi\r\nW6bYep1AhbmoBaI3jPFFEm8rOS7goODJo7a7TietcxMGBrF9pTxXQqlPYb7J\r\nH0Q9ecII3jZy7ur8/sB0twHvJtbp2WHY4ikOHRoRA6+R0n3mMe9kM9yMG1Vr\r\neVris8wxLCacqg820edxNgUI/YFPAUMVcp5ojAet2UabfnkpIF3p95YKjJAK\r\n/loD4aIMBVEBGXqruiTEe6UseI4yh1fl/gc=\r\n=rryZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "280680b8f77094031e3908823c81a9be3e6d6817", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.5.1", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.30.0_1680553664607_0.8933747213568284", "host": "s3://npm-registry-packages"}}, "3.30.1": {"name": "core-js-compat", "version": "3.30.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.30.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "961541e22db9c27fc48bfc13a3cafa8734171dfe", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.30.1.tgz", "fileCount": 13, "integrity": "sha512-d690npR7MC6P0gq4npTl5n2VQeNAmUrJ90n+MHiKS7W2+xno4o3F5GDEuylSdi6EJ3VssibSGXOa1r3YXD3Mhw==", "signatures": [{"sig": "MEYCIQC7XvOJc++eeABFQ+nanOHOpkGqJ2HbFJ2ShbfTrcNLTQIhAJZg9damxAvsMPzmxdHY+3WCXDdlXQiqciSqd7xXcgDu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 661826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOEsXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe2A//d00Dck9KFHSf63daEhR8pYuqaOZ45KlnzlULPfV1Q10JIXnT\r\na9TJduS1jxGGG0CQ3TMj4s0+11+bxH9G990+VeDDQ+rZ075NFGqWx9QWz73v\r\nPu3ELLGKMRg+yMIWeioZ7bFLyDhJ+q1UMFzEGFRy00gxrg3EK0RBsY51iaGP\r\nl6tQecwghJrsWRH4J18u7KjIx2J04xBk5jzBTuL8kthN/1gAKALvlAauwJE/\r\nlhb8wSOu8IOKvhPDcMKZmuXl/z7zqOuSL4xfB1uk86SqYjISnKHIORaZkPu4\r\nvrocdke40H4SxHTBWVq4PURF6ID0UXL4ggyVyGKYrAqiYt1H+aNlnjdK8iHo\r\nIwfgl/ld9HM6qw8HEzi1rk5FQbNbf6vHgpNKwPz2T7tIP/WKUF1QxiGq751C\r\nBnscSPTF8r72Hy59/LmCXVLaMKR+p1gDwHrPjnxOSU+3kOe69n+o5a3IGb8e\r\ngiZXufy88cYNvYm0P7/G89B5TkunIKl6HNTUv989AUoWlZWaH70D4L/dywrw\r\nnpiXWknaKcCYp9JaZq+r/nFzp3WZbFcz8IGa8Ut1r690I+Dbvwq8D4LsqzDQ\r\nOEqwngaBdqrMAEL+4pwMH2RxaClQDarGJO6Ztc0jQ8sC65tQIgVlskNSfMkw\r\n7Qi9PRS9m3k+eeRsWSodjWnz4atgO/iH53U=\r\n=pqvq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "46e2a5ca1fbc4a32320df44d74456e8e14dc970c", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.6.3", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "19.9.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.30.1_1681410838772_0.023486929810986767", "host": "s3://npm-registry-packages"}}, "3.30.2": {"name": "core-js-compat", "version": "3.30.2", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.30.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "83f136e375babdb8c80ad3c22d67c69098c1dd8b", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.30.2.tgz", "fileCount": 13, "integrity": "sha512-nriW1nuJjUgvkEjIot1Spwakz52V9YkYHZAQG6A1eCgC8AA1p0zngrQEP9R0+V6hji5XilWKG1Bd0YRppmGimA==", "signatures": [{"sig": "MEYCIQCu6SP1r7x61pPIKiAWjXgpSk1Vz4ihg/GgKmbkGwqsOAIhAMQbJNN3FZZ6fW6BLm4suIrTNsZc3yNR+eseYL65ABtF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 662332}, "main": "index.js", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "a54caa5e5c5583ec9cc354b4cd7dcf8a059b6f72", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.6.4", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.1.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.30.2_1683400624790_0.9265441468451574", "host": "s3://npm-registry-packages"}}, "3.31.0": {"name": "core-js-compat", "version": "3.31.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.31.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "4030847c0766cc0e803dcdfb30055d7ef2064bf1", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.31.0.tgz", "fileCount": 17, "integrity": "sha512-hM7YCu1cU6Opx7MXNu0NuumM0ezNeAeRKadixyiQELWY3vT3De9S4J5ZBMraWV2vZnrE1Cirl0GtFtDtMUXzPw==", "signatures": [{"sig": "MEYCIQDCFinywQRP8hKZFrnVgxSqN+iAHDUjIWg1XAOqxIi2jQIhAL4Rw5sRDN9LnGYTGpFm1wrzRONaeR9JLf8DOfw5GCP9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 676450}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "199c633ee517a496ce789498a76ad8eeeecfc3a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.6.7", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.3.0", "dependencies": {"browserslist": "^4.21.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.31.0_1686513337940_0.0552192320685041", "host": "s3://npm-registry-packages"}}, "3.31.1": {"name": "core-js-compat", "version": "3.31.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.31.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "5084ad1a46858df50ff89ace152441a63ba7aae0", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.31.1.tgz", "fileCount": 17, "integrity": "sha512-wIDWd2s5/5aJSdpOJHfSibxNODxoGoWOBHt8JSPB41NOE94M7kuTPZCYLOlTtuoXTsBPKobpJ6T+y0SSy5L9SA==", "signatures": [{"sig": "MEUCIDHgedYt3j9FMJeXqdwlTqaozDdGga+sqHMyYMDNglDtAiEA6c9T5IMshjrcxS35tFyY0I5UG+8MHAQqGCZPGYMIhTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 677673}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "9376c6f8adc252e5fb66ec0dffd10bcb53b3c9cd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.6.7", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.3.1", "dependencies": {"browserslist": "^4.21.9"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.31.1_1688604908491_0.9578574025914621", "host": "s3://npm-registry-packages"}}, "3.32.0": {"name": "core-js-compat", "version": "3.32.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.32.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "f41574b6893ab15ddb0ac1693681bd56c8550a90", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.32.0.tgz", "fileCount": 17, "integrity": "sha512-7a9a3D1k4UCVKnLhrgALyFcP7YCsLOQIxPd0dKjf/6GuPcgyiGP70ewWdCGrSK7evyhymi0qO4EqCmSJofDeYw==", "signatures": [{"sig": "MEYCIQDfopDUGz07iS+EtOXbj4K/s9tc/gX80ifJBXWKP3TRQAIhAOWol4h1IiE9LkbYrQweQnG8/8fn9RpknYwSzqfNxe4q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 685859}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "c25219226fe4852c7538a0e62ed5d8ef1b7851a7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.8.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.5.0", "dependencies": {"browserslist": "^4.21.9"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.32.0_1690483916165_0.5620411151619136", "host": "s3://npm-registry-packages"}}, "3.32.1": {"name": "core-js-compat", "version": "3.32.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.32.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "55f9a7d297c0761a8eb1d31b593e0f5b6ffae964", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.32.1.tgz", "fileCount": 17, "integrity": "sha512-GSvKDv4wE0bPnQtjklV101juQ85g6H3rm5PDP20mqlS5j0kXF3pP97YvAu5hl+uFHqMictp3b2VxOHljWMAtuA==", "signatures": [{"sig": "MEUCIC8BDxKBVZrHIE/THpu1S+JNni1zdoeBHmT0LIvmE5ulAiEArl9ULL6S4wwPapxphqYKUEJwWM+2g02rxj5WHmuAQqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 685902}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "5dabb13bcfa3e428ff70731c87e75ac8e8b1ad41", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.8.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.5.1", "dependencies": {"browserslist": "^4.21.10"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.32.1_1692378342747_0.6635121594954265", "host": "s3://npm-registry-packages"}}, "3.32.2": {"name": "core-js-compat", "version": "3.32.2", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.32.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "8047d1a8b3ac4e639f0d4f66d4431aa3b16e004c", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.32.2.tgz", "fileCount": 17, "integrity": "sha512-+GjlguTDINOijtVRUxrQOv3kfu9rl+qPNdX2LTbJ/ZyVTuxK+ksVSAGX1nHstu4hrv1En/uPTtWgq2gI5wt4AQ==", "signatures": [{"sig": "MEUCIQCj7Wv5n5AND6FT2MoMyWQwcXRnSV3fQswdwjI2vmLCAAIgCRzid6Erk1x1aaso937eLgCiUoWKEhsGAFZLkPXXucI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686063}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "aaefe98d5fdc015e417801332eb4ede5113a01f5", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "9.8.1", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.6.0", "dependencies": {"browserslist": "^4.21.10"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.32.2_1694090917797_0.047766055881243163", "host": "s3://npm-registry-packages"}}, "3.33.0": {"name": "core-js-compat", "version": "3.33.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.33.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "24aa230b228406450b2277b7c8bfebae932df966", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.33.0.tgz", "fileCount": 17, "integrity": "sha512-0w4LcLXsVEuNkIqwjjf9rjCoPhK8uqA4tMRh4Ge26vfLtUutshn+aRJU21I9LCJlh2QQHfisNToLjw1XEJLTWw==", "signatures": [{"sig": "MEYCIQDkidyhj03x390GGnkDHbWoIJLccAS9Pe+aRrAQOCCJvgIhAOLH1eM09yZQbaB8UW2meqBil79jc9PAJ5ZpGKBRSckU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 685575}, "main": "index.js", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d47567b4c6ecd6e30237dd3848219617a3f08ccb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.1.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "20.8.0", "dependencies": {"browserslist": "^4.22.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.33.0_1696185453195_0.18516529906609613", "host": "s3://npm-registry-packages"}}, "3.33.1": {"name": "core-js-compat", "version": "3.33.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.33.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "debe80464107d75419e00c2ee29f35982118ff84", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.33.1.tgz", "fileCount": 17, "integrity": "sha512-6pYKNOgD/j/bkC5xS5IIg6bncid3rfrI42oBH1SQJbsmYPKF7rhzcFzYCcxYMmNQQ0rCEB8WqpW7QHndOggaeQ==", "signatures": [{"sig": "MEUCIEOjnnl/W5aI63MY0yOZVBQz1Wf+hqP/MO44Uw2OXSgVAiEAoWQyARxTbsa8nVv+FuVu0ffD8uvYUNMKIKb2ohAp3FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686107}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "55bec5621fb38ff554106761869aa245743f35c9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "dependencies": {"browserslist": "^4.22.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.33.1_1697780313714_0.2734142522045313", "host": "s3://npm-registry-packages"}}, "3.33.2": {"name": "core-js-compat", "version": "3.33.2", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.33.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "3ea4563bfd015ad4e4b52442865b02c62aba5085", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.33.2.tgz", "fileCount": 17, "integrity": "sha512-axfo+wxFVxnqf8RvxTzoAlzW4gRoacrHeoFlc9n0x50+7BEyZL/Rt3hicaED1/CEd7I6tPCPVUYcJwCMO5XUYw==", "signatures": [{"sig": "MEUCIDTDQS5UEiW0MtzwjUmbyaXiLUCZo8QT13vhA7a40cYqAiEA7BLiO4MVAeajQGd4CSnswjjKKaKQM8228tlhCzPPz/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686348}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "7cbd6b5e9e96cda050ae911c4015b0ecd9875f92", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.1.0", "dependencies": {"browserslist": "^4.22.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.33.2_1698686551745_0.8095063446340347", "host": "s3://npm-registry-packages"}}, "3.33.3": {"name": "core-js-compat", "version": "3.33.3", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.33.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "ec678b772c5a2d8a7c60a91c3a81869aa704ae01", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.33.3.tgz", "fileCount": 17, "integrity": "sha512-cNzGqFsh3Ot+529GIXacjTJ7kegdt5fPXxCBVS1G0iaZpuo/tBz399ymceLJveQhFFZ8qThHiP3fzuoQjKN2ow==", "signatures": [{"sig": "MEYCIQC9X5iT/bYdjHCdRjsQS8a89GzPDgrmIso2LbPiB4bU2AIhAMcvsM/6EmZuQzRYcAtsb/ol8AHX1iZa5NAsNrie87Yh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 685498}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "a18759b0b3a1041f72df263d9ffcd884760139b1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.3", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.2.0", "dependencies": {"browserslist": "^4.22.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.33.3_1700417304400_0.8174773817168945", "host": "s3://npm-registry-packages"}}, "3.34.0": {"name": "core-js-compat", "version": "3.34.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.34.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "61a4931a13c52f8f08d924522bba65f8c94a5f17", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.34.0.tgz", "fileCount": 17, "integrity": "sha512-4ZIyeNbW/Cn1wkMMDy+mvrRUxrwFNjKwbhCfQpDd+eLgYipDqp8oGFGtLmhh18EDPKA0g3VUBYOxQGGwvWLVpA==", "signatures": [{"sig": "MEQCIGi/OfzTXuhpgqONnE4fs6hl7XlylaSrOH4BD31yAiAoAiABV9mr4S6K/JiJX7GhIb4eYkLvECWHKXJOMYy/Fs/8ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 694728}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "bc16b93358314bf507542e8c0009785398afd269", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.4", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.3.0", "dependencies": {"browserslist": "^4.22.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.34.0_1701799593036_0.35375359780448656", "host": "s3://npm-registry-packages"}}, "3.35.0": {"name": "core-js-compat", "version": "3.35.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.35.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "c149a3d1ab51e743bc1da61e39cb51f461a41873", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.35.0.tgz", "fileCount": 17, "integrity": "sha512-5blwFAddknKeNgsjBzilkdQ0+YK8L1PfqPYq40NOYMYFSS38qj+hpTcLLWwpIwA2A5bje/x5jmVn2tzUMg9IVw==", "signatures": [{"sig": "MEUCIAZLkiU/U+YXtdeTRPYDzExzL/sGRbo9PypSBsIYva4/AiEAmmHpd2NMZMhx74v4UGFr5PDy3jXO1FxeSXFZG/3LSDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 695814}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "eafff6c0ec7738adb005b1186503e527180f4ece", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.4", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.5.0", "dependencies": {"browserslist": "^4.22.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.35.0_1703803615168_0.8385824028798134", "host": "s3://npm-registry-packages"}}, "3.35.1": {"name": "core-js-compat", "version": "3.35.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.35.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "215247d7edb9e830efa4218ff719beb2803555e2", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.35.1.tgz", "fileCount": 17, "integrity": "sha512-sftHa5qUJY3rs9Zht1WEnmkvXputCyDBczPnr7QDgL8n3qrF3CMXY4VPSYtOLLiOUJcah2WNXREd48iOl6mQIw==", "signatures": [{"sig": "MEQCIC1HWtI28VlBEWU3wu9X5SEYzklLELjNa+KhURARQly1AiBe14lPzVZ2f4WUD39JQdgGL0Tbil+JyRQGlUof0L3MYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 697808}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "02c96bf4fd8a6765acaf1d6bcaf27a026c248d08", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.4", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"browserslist": "^4.22.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.35.1_1705791036496_0.8089521663199828", "host": "s3://npm-registry-packages"}}, "3.36.0": {"name": "core-js-compat", "version": "3.36.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.36.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "087679119bc2fdbdefad0d45d8e5d307d45ba190", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.36.0.tgz", "fileCount": 17, "integrity": "sha512-iV9Pd/PsgjNWBXeq8XRtWVSgz2tKAfhfvBs7qxYty+RlRd+OCksaWmOnc4JKrTc1cToXL1N0s3l/vwlxPtdElw==", "signatures": [{"sig": "MEUCIGhquTPELVIUagw08GJ7KRMcUnzhCG7+A2/GxatHgMXWAiEAs0eLGcPOLElk2tbKqGXgmsuovWBQwAeF2kTlTNKnNFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 715230}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "77123c42d3bee524768f1f8495fd3404892ff910", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.2.4", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.1", "dependencies": {"browserslist": "^4.22.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.36.0_1707898432282_0.9054068488172824", "host": "s3://npm-registry-packages"}}, "3.36.1": {"name": "core-js-compat", "version": "3.36.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.36.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "1818695d72c99c25d621dca94e6883e190cea3c8", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.36.1.tgz", "fileCount": 17, "integrity": "sha512-Dk997v9ZCt3X/npqzyGdTlq6t7lDBhZwGvV94PKzDArjp7BTRm7WlDAXYd/OWdeFHO8OChQYRJNJvUCqCbrtKA==", "signatures": [{"sig": "MEUCIGrc1u485ivY3+aahcdbEEDBpH8xP9Bs7YUhbJo+a5a5AiEAkdCrFTllggcFgl4GyyDF6ZwPr/l9Q+tLlwI+qDclGTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 717159}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "454d98ce1c3697660961bb83e71f99970fd64888", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.5.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.0", "dependencies": {"browserslist": "^4.23.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.36.1_1710812593850_0.8510599334531388", "host": "s3://npm-registry-packages"}}, "3.37.0": {"name": "core-js-compat", "version": "3.37.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.37.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d9570e544163779bb4dff1031c7972f44918dc73", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.37.0.tgz", "fileCount": 17, "integrity": "sha512-vYq4L+T8aS5UuFg4UwDhc7YNRWVeVZwltad9C/jV3R2LgVOpS9BDr7l/WL6BN0dbV3k1XejPTHqqEzJgsa0frA==", "signatures": [{"sig": "MEUCIQDgOwkvltbvnlD0LKvMctU+ZphxxGJLEFkOEdRIIyjB8wIgV6kpxyX8mCS2RZKKAWomU2E/RcP/itKHxmUrHUQFHCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727401}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "598d0b2fd36a3a62911b1507f945363050de98bc", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.5.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.3", "dependencies": {"browserslist": "^4.23.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.37.0_1713297053442_0.10838215572716625", "host": "s3://npm-registry-packages"}}, "3.37.1": {"name": "core-js-compat", "version": "3.37.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.37.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "c844310c7852f4bdf49b8d339730b97e17ff09ee", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.37.1.tgz", "fileCount": 17, "integrity": "sha512-9TNiImhKvQqSUkOvk/mMRZzOANTiEVC7WaBNhHcKM7x+/5E1l5NvsysR19zuDQScE8k+kfQXWRN3AtS/eOSHpg==", "signatures": [{"sig": "MEQCIGz4PnVQh516oByzpgQ5i4o1Jat2t066ZQE4JnZSQJ0vAiBz4OIxFJdTF8B1vUSkLU4vqhFdwxd4L5x17RvHbRYTEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 730593}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d044cb58903ca34926708cbfde8979880dda81e3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.7.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "dependencies": {"browserslist": "^4.23.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.37.1_1715674121929_0.45058628282562574", "host": "s3://npm-registry-packages"}}, "3.38.0": {"name": "core-js-compat", "version": "3.38.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.38.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "d93393b1aa346b6ee683377b0c31172ccfe607aa", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.38.0.tgz", "fileCount": 17, "integrity": "sha512-75LAicdLa4OJVwFxFbQR3NdnZjNgX6ILpVcVzcC4T2smerB5lELMrJQQQoWV6TiuC/vlaFqgU2tKQx9w5s0e0A==", "signatures": [{"sig": "MEQCIFv/PHMWlbpvBX3y4FEFyFQG6JKEoBZXyB9BcT1dG2qWAiBh79NzfQ0ySBsoO4stJcECK3pk2gZ3wQs4qnNPT9IB/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 737438}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "4a322bf3a89fd4579b938ab09941b509f9db7136", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.8.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "22.5.1", "dependencies": {"browserslist": "^4.23.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.38.0_1722795556270_0.8040174710044534", "host": "s3://npm-registry-packages"}}, "3.38.1": {"name": "core-js-compat", "version": "3.38.1", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.38.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "2bc7a298746ca5a7bcb9c164bcb120f2ebc09a09", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.38.1.tgz", "fileCount": 17, "integrity": "sha512-JRH6gfXxGmrzF3tZ57lFx97YARxCXPaMzPo6jELZhv88pBH5VXpQ+y0znKGlFnzuaihqhLbefxSJxWJMPtfDzw==", "signatures": [{"sig": "MEUCIAZBemc4s8jTJuoC7lpPIk0ApJvfY3prhIW3zZZ0UWK8AiEA0W04PU17oq3zlLmyQHzHybMGxmrBfw73FC7v4jshtvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 738910}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "d1e7889678f9d09ee4bb1d6bbd3184462567c730", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.8.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "22.6.0", "dependencies": {"browserslist": "^4.23.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.38.1_1724153930401_0.8989080271964636", "host": "s3://npm-registry-packages"}}, "3.39.0": {"name": "core-js-compat", "version": "3.39.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.39.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "b12dccb495f2601dc860bdbe7b4e3ffa8ba63f61", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.39.0.tgz", "fileCount": 17, "integrity": "sha512-VgEUx3VwlExr5no0tXlBt+silBvhTryPwCXRI2Id1PN8WTKu7MreethvddqOubrYxkFdv/RnYrqlv1sFNAUelw==", "signatures": [{"sig": "MEUCIQCZcWo6+JZUc/jZrylULQIsSHMX+KgfPbDknke4JVHx5wIgXi2u5KPHDKaJV2gWJrR6RQoF5eT36o/gBRBNs9I3H+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 764753}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "fe6d2d6910f460630dd016163d0d5c8e4c2eaa22", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.9.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "23.1.0", "dependencies": {"browserslist": "^4.24.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.39.0_1730334089148_0.9786567656535652", "host": "s3://npm-registry-packages"}}, "3.40.0": {"name": "core-js-compat", "version": "3.40.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.40.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "7485912a5a4a4315c2fdb2cbdc623e6881c88b38", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.40.0.tgz", "fileCount": 17, "integrity": "sha512-0XEDpr5y5mijvw8Lbc6E5AkjrHfp7eEoPlu36SWeAbcL8fn1G1ANe8DBlo2XoNN89oVpxWwOjYIPVzR4ZvsKCQ==", "signatures": [{"sig": "MEQCIFi+2tptgiimriAHbmiTPZmo8TGFAxYBNtG8xQ07Wd40AiBD8wrTaH+Gx0jZH4ySNyVDuvRMeBVZ/ZhFtSUTHX8bmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 768004}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "6e493925cb4348e99385b5aca6b0ff75ef39b48f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "11.0.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "23.5.0", "dependencies": {"browserslist": "^4.24.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.40.0_1736274151232_0.0028868823114696074", "host": "s3://npm-registry-packages-npm-production"}}, "3.41.0": {"name": "core-js-compat", "version": "3.41.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.41.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "4cdfce95f39a8f27759b667cf693d96e5dda3d17", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.41.0.tgz", "fileCount": 17, "integrity": "sha512-RFsU9LySVue9RTwdDVX/T0e2Y6jRYWXERKElIjpuEOEnxaXffI0X7RUwVzfYLfzuLXSNJDYoRYUAmRUcyln20A==", "signatures": [{"sig": "MEUCIQCQjqxaX5mT+pmdYqgE2m6KHYeaiiodIAXm2bdc3szbDwIgXjKqkiCQNIfkUMbo0SkMZOUG4TYEG9x1RNOKZs1WUdI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 775766}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "149541a1b17cb8ae2a8c85d35d027dad99f78501", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "10.9.2", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "23.8.0", "dependencies": {"browserslist": "^4.24.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.41.0_1740848181161_0.6895247888456051", "host": "s3://npm-registry-packages-npm-production"}}, "3.42.0": {"name": "core-js-compat", "version": "3.42.0", "author": {"url": "http://zloirock.ru", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "core-js-compat@3.42.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/zloirock/core-js#readme", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "dist": {"shasum": "ce19c29706ee5806e26d3cb3c542d4cfc0ed51bb", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.42.0.tgz", "fileCount": 17, "integrity": "sha512-bQasjMfyDGyaeWKBIu33lHh9qlSR0MFE/Nmc6nMjf/iU9b3rSMdAYz1Baxrv4lPdGUsTqZudHA4jIGSJy0SWZQ==", "signatures": [{"sig": "MEYCIQCrr1stjYvei0ieK05sByT0XlnFY0IqzhpmdGZUy+U2cwIhAMUJGbSzUzR/UTope3p6D6jkX+cinh7eHuYJcsyOu36O", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 772930}, "main": "index.js", "type": "commonjs", "types": "index.d.ts", "funding": {"url": "https://opencollective.com/core-js", "type": "opencollective"}, "gitHead": "ce8281cbca924f1f3cf16cb5f7098b2d9e428af2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zloirock/core-js.git", "type": "git", "directory": "packages/core-js-compat"}, "_npmVersion": "11.3.0", "description": "core-js compat", "directories": {}, "sideEffects": false, "_nodeVersion": "23.10.0", "dependencies": {"browserslist": "^4.24.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/core-js-compat_3.42.0_1745949840496_0.39182920643857555", "host": "s3://npm-registry-packages-npm-production"}}, "3.43.0": {"name": "core-js-compat", "version": "3.43.0", "type": "commonjs", "description": "core-js compat", "repository": {"type": "git", "url": "git+https://github.com/zloirock/core-js.git", "directory": "packages/core-js-compat"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zloirock.ru"}, "sideEffects": false, "main": "index.js", "types": "index.d.ts", "dependencies": {"browserslist": "^4.25.0"}, "_id": "core-js-compat@3.43.0", "gitHead": "f4f46336b3f2b9ac7a688f9a6d2fa6f12f94ebab", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "homepage": "https://github.com/zloirock/core-js#readme", "_nodeVersion": "24.1.0", "_npmVersion": "11.4.1", "dist": {"integrity": "sha512-2GML2ZsCc5LR7hZYz4AXmjQw8zuy2T//2QntwdnpuYI7jteT6GVYJL7F6C2C57R7gSYrcqVW3lAALefdbhBLDA==", "shasum": "055587369c458795ef316f65e0aabb808fb15840", "tarball": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.43.0.tgz", "fileCount": 17, "unpackedSize": 791287, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICTQC6c/JirFd4Ol5TINF6Wa1qSl14ZolOwZp+YSIDwnAiEA3+7dAjpVE748qsaCsmns9X70lh3uuDCADSgmTkqROaY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/core-js-compat_3.43.0_1749452102111_0.84276552583638"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-12-24T21:42:14.806Z", "modified": "2025-06-09T06:55:02.454Z", "3.0.0-beta.6": "2018-12-24T21:42:14.958Z", "3.0.0-beta.7": "2018-12-27T00:52:04.537Z", "3.0.0-beta.8": "2019-01-10T07:36:49.762Z", "3.0.0-beta.9": "2019-01-17T10:18:53.628Z", "3.0.0-beta.10": "2019-01-22T01:41:52.527Z", "3.0.0-beta.11": "2019-01-27T23:28:38.659Z", "3.0.0-beta.12": "2019-02-02T02:48:54.506Z", "3.0.0-beta.13": "2019-02-04T10:09:36.664Z", "3.0.0-beta.14": "2019-02-07T07:44:25.670Z", "3.0.0-beta.15": "2019-02-09T21:15:35.385Z", "3.0.0-beta.16": "2019-02-18T20:37:56.342Z", "3.0.0-beta.17": "2019-03-09T21:14:49.292Z", "3.0.0-beta.18": "2019-03-09T22:17:09.536Z", "3.0.0-beta.19": "2019-03-14T20:45:12.790Z", "3.0.0-beta.20": "2019-03-17T19:59:37.293Z", "3.0.0": "2019-03-19T19:18:27.306Z", "3.0.1": "2019-04-06T07:20:20.539Z", "3.1.0": "2019-05-19T19:26:58.906Z", "3.1.1": "2019-05-20T20:42:03.997Z", "3.1.2": "2019-05-21T22:28:01.567Z", "3.1.3": "2019-05-27T13:06:20.038Z", "3.1.4": "2019-06-15T11:57:28.517Z", "3.2.0": "2019-08-08T22:05:44.306Z", "3.2.1": "2019-08-12T12:25:23.619Z", "3.3.1": "2019-10-13T15:06:40.695Z", "3.3.2": "2019-10-14T13:41:49.341Z", "3.3.3": "2019-10-21T17:07:59.417Z", "3.3.4": "2019-10-25T11:07:55.546Z", "3.3.5": "2019-10-28T17:31:13.577Z", "3.3.6": "2019-10-31T17:08:53.615Z", "3.4.0": "2019-11-06T21:46:04.805Z", "3.4.1": "2019-11-12T05:23:40.868Z", "3.4.2": "2019-11-21T19:03:00.459Z", "3.4.3": "2019-11-26T14:03:51.704Z", "3.4.4": "2019-11-27T06:19:35.417Z", "3.4.5": "2019-11-27T22:07:43.350Z", "3.4.6": "2019-12-02T17:27:54.603Z", "3.4.7": "2019-12-02T17:58:29.521Z", "3.4.8": "2019-12-08T21:14:39.861Z", "3.5.0": "2019-12-12T00:59:54.953Z", "3.6.0": "2019-12-18T23:29:20.042Z", "3.6.1": "2019-12-25T06:06:10.742Z", "3.6.2": "2020-01-06T17:36:17.917Z", "3.6.3": "2020-01-10T23:23:44.245Z", "3.6.4": "2020-01-13T23:10:48.728Z", "3.6.5": "2020-04-10T16:16:13.097Z", "3.7.0": "2020-11-06T15:13:06.249Z", "3.8.0": "2020-11-25T18:20:47.184Z", "3.8.1": "2020-12-06T01:22:31.208Z", "3.8.2": "2021-01-03T14:25:27.303Z", "3.8.3": "2021-01-19T07:08:05.595Z", "3.9.0": "2021-02-18T17:20:14.317Z", "3.9.1": "2021-02-28T18:33:39.935Z", "3.10.0": "2021-03-31T08:08:56.656Z", "3.10.1": "2021-04-07T19:04:42.353Z", "3.10.2": "2021-04-19T17:03:36.283Z", "3.11.0": "2021-04-22T10:41:36.587Z", "3.11.1": "2021-04-28T14:16:03.055Z", "3.11.2": "2021-05-03T10:45:57.302Z", "3.11.3": "2021-05-05T13:48:10.566Z", "3.12.0": "2021-05-06T09:06:38.912Z", "3.12.1": "2021-05-08T21:04:16.587Z", "3.13.0": "2021-05-25T19:11:27.594Z", "3.13.1": "2021-05-29T09:00:48.712Z", "3.14.0": "2021-06-05T07:50:02.945Z", "3.15.0": "2021-06-20T17:33:17.211Z", "3.15.1": "2021-06-22T18:32:15.627Z", "3.15.2": "2021-06-29T11:26:32.093Z", "3.16.0": "2021-07-30T03:41:07.892Z", "3.16.1": "2021-08-08T17:34:46.009Z", "3.16.2": "2021-08-17T13:14:02.796Z", "3.16.3": "2021-08-24T20:17:34.943Z", "3.16.4": "2021-08-29T14:55:50.803Z", "3.17.0": "2021-09-01T17:23:06.712Z", "3.17.1": "2021-09-01T20:15:58.942Z", "3.17.2": "2021-09-02T17:21:12.280Z", "3.17.3": "2021-09-09T08:41:32.924Z", "3.18.0": "2021-09-19T19:03:42.562Z", "3.18.1": "2021-09-26T22:32:27.637Z", "3.18.2": "2021-10-05T18:30:25.007Z", "3.18.3": "2021-10-12T18:13:49.923Z", "3.19.0": "2021-10-25T06:10:53.631Z", "3.19.1": "2021-11-02T19:58:29.620Z", "3.19.2": "2021-11-29T18:44:22.421Z", "3.19.3": "2021-12-06T06:26:19.841Z", "3.20.0": "2021-12-15T19:14:22.607Z", "3.20.1": "2021-12-23T11:22:58.835Z", "3.20.2": "2022-01-01T18:33:06.964Z", "3.20.3": "2022-01-15T11:13:17.640Z", "3.21.0": "2022-02-01T17:12:25.340Z", "3.21.1": "2022-02-16T18:24:02.980Z", "3.22.0": "2022-04-15T02:51:34.380Z", "3.22.1": "2022-04-19T23:40:59.672Z", "3.22.2": "2022-04-21T13:18:15.826Z", "3.22.3": "2022-04-28T03:21:53.899Z", "3.22.4": "2022-05-02T18:04:03.754Z", "3.22.5": "2022-05-10T09:32:53.284Z", "3.22.6": "2022-05-22T18:12:43.576Z", "3.22.7": "2022-05-24T14:01:34.197Z", "3.22.8": "2022-06-01T18:04:55.240Z", "3.23.0": "2022-06-13T18:36:58.036Z", "3.23.1": "2022-06-14T13:24:39.561Z", "3.23.2": "2022-06-20T19:07:15.367Z", "3.23.3": "2022-06-25T20:16:50.813Z", "3.23.4": "2022-07-09T18:24:19.969Z", "3.23.5": "2022-07-17T19:15:26.128Z", "3.24.0": "2022-07-25T04:57:55.843Z", "3.24.1": "2022-07-29T18:07:12.663Z", "3.25.0": "2022-08-24T20:21:27.935Z", "3.25.1": "2022-09-07T20:32:38.370Z", "3.25.2": "2022-09-18T18:16:15.554Z", "3.25.3": "2022-09-25T23:13:22.378Z", "3.25.4": "2022-10-02T18:03:15.858Z", "3.25.5": "2022-10-03T18:34:36.593Z", "3.26.0": "2022-10-23T18:32:45.607Z", "3.26.1": "2022-11-13T18:12:03.088Z", "3.27.0": "2022-12-25T18:08:57.821Z", "3.27.1": "2022-12-29T18:11:49.706Z", "3.27.2": "2023-01-18T19:37:16.753Z", "3.28.0": "2023-02-13T19:12:02.585Z", "3.29.0": "2023-02-26T21:20:41.433Z", "3.29.1": "2023-03-13T06:00:03.196Z", "3.30.0": "2023-04-03T20:27:44.763Z", "3.30.1": "2023-04-13T18:33:58.989Z", "3.30.2": "2023-05-06T19:17:04.997Z", "3.31.0": "2023-06-11T19:55:38.198Z", "3.31.1": "2023-07-06T00:55:08.754Z", "3.32.0": "2023-07-27T18:51:56.390Z", "3.32.1": "2023-08-18T17:05:43.008Z", "3.32.2": "2023-09-07T12:48:38.004Z", "3.33.0": "2023-10-01T18:37:33.446Z", "3.33.1": "2023-10-20T05:38:33.904Z", "3.33.2": "2023-10-30T17:22:32.100Z", "3.33.3": "2023-11-19T18:08:24.589Z", "3.34.0": "2023-12-05T18:06:33.202Z", "3.35.0": "2023-12-28T22:46:55.361Z", "3.35.1": "2024-01-20T22:50:36.723Z", "3.36.0": "2024-02-14T08:13:52.444Z", "3.36.1": "2024-03-19T01:43:14.032Z", "3.37.0": "2024-04-16T19:50:53.630Z", "3.37.1": "2024-05-14T08:08:42.087Z", "3.38.0": "2024-08-04T18:19:16.477Z", "3.38.1": "2024-08-20T11:38:50.555Z", "3.39.0": "2024-10-31T00:21:29.403Z", "3.40.0": "2025-01-07T18:22:31.482Z", "3.41.0": "2025-03-01T16:56:21.409Z", "3.42.0": "2025-04-29T18:04:00.682Z", "3.43.0": "2025-06-09T06:55:02.276Z"}, "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://zloirock.ru"}, "license": "MIT", "homepage": "https://github.com/zloirock/core-js#readme", "repository": {"type": "git", "url": "git+https://github.com/zloirock/core-js.git", "directory": "packages/core-js-compat"}, "description": "core-js compat", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "![logo](https://user-images.githubusercontent.com/2213682/146607186-8e13ddef-26a4-4ebf-befd-5aac9d77c090.png)\n\n<div align=\"center\">\n\n[![fundraising](https://opencollective.com/core-js/all/badge.svg?label=fundraising)](https://opencollective.com/core-js) [![PRs welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/zloirock/core-js/blob/master/CONTRIBUTING.md) [![version](https://img.shields.io/npm/v/core-js-compat.svg)](https://www.npmjs.com/package/core-js-compat) [![core-js-compat downloads](https://img.shields.io/npm/dm/core-js-compat.svg?label=npm%20i%20core-js-compat)](https://npm-stat.com/charts.html?package=core-js&package=core-js-pure&package=core-js-compat&from=2014-11-18)\n\n</div>\n\n**I highly recommend reading this: [So, what's next?](https://github.com/zloirock/core-js/blob/master/docs/2023-02-14-so-whats-next.md)**\n---\n\n[`core-js-compat` package](https://github.com/zloirock/core-js/tree/master/packages/core-js-compat) contains data about the necessity of [`core-js`](https://github.com/zloirock/core-js) modules and API for getting a list of required core-js modules by browserslist query.\n\n```js\nimport compat from 'core-js-compat';\n\nconst {\n  list,                       // array of required modules\n  targets,                    // object with targets for each module\n} = compat({\n  targets: '> 1%',            // browserslist query or object of minimum environment versions to support, see below\n  modules: [                  // optional list / filter of modules - regex, string or an array of them:\n    'core-js/actual',         // - an entry point\n    'esnext.array.unique-by', // - a module name (or just a start of a module name)\n    /^web\\./,                 // - regex that a module name must satisfy\n  ],\n  exclude: [                  // optional list / filter of modules to exclude, the signature is similar to `modules` option\n    'web.atob',\n  ],\n  version: '3.43',            // used `core-js` version, by default - the latest\n  inverse: false,             // inverse of the result - shows modules that are NOT required for the target environment\n});\n\nconsole.log(targets);\n/* =>\n{\n  'es.error.cause': { ios: '14.5-14.8' },\n  'es.aggregate-error.cause': { ios: '14.5-14.8' },\n  'es.array.at': { ios: '14.5-14.8' },\n  'es.array.find-last': { firefox: '100', ios: '14.5-14.8' },\n  'es.array.find-last-index': { firefox: '100', ios: '14.5-14.8' },\n  'es.array.includes': { firefox: '100' },\n  'es.array.push': { chrome: '100', edge: '101', ios: '14.5-14.8', safari: '15.4' },\n  'es.array.unshift': { ios: '14.5-14.8', safari: '15.4' },\n  'es.object.has-own': { ios: '14.5-14.8' },\n  'es.regexp.flags': { chrome: '100', edge: '101' },\n  'es.string.at-alternative': { ios: '14.5-14.8' },\n  'es.typed-array.at': { ios: '14.5-14.8' },\n  'es.typed-array.find-last': { firefox: '100', ios: '14.5-14.8' },\n  'es.typed-array.find-last-index': { firefox: '100', ios: '14.5-14.8' },\n  'esnext.array.group': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.group-by': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.group-by-to-map': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.group-to-map': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.to-reversed': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.to-sorted': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.to-spliced': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.unique-by': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.array.with': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.typed-array.to-reversed': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.typed-array.to-sorted': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.typed-array.to-spliced': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'esnext.typed-array.with': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'web.dom-exception.stack': { chrome: '100', edge: '101', ios: '14.5-14.8', safari: '15.4' },\n  'web.immediate': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' },\n  'web.structured-clone': { chrome: '100', edge: '101', firefox: '100', ios: '14.5-14.8', safari: '15.4' }\n}\n*/\n```\n\n### `targets` option\n`targets` could be [a `browserslist` query](https://github.com/browserslist/browserslist) or a targets object that specifies minimum environment versions to support:\n```js\n// browserslist query:\n'defaults, not IE 11, maintained node versions';\n// object (sure, all those fields optional):\n({\n  android: '4.0',         // Android WebView version\n  bun: '0.1.2',           // Bun version\n  chrome: '38',           // Chrome version\n  'chrome-android': '18', // Chrome for Android version\n  deno: '1.12',           // Deno version\n  edge: '13',             // Edge version\n  electron: '5.0',        // Electron framework version\n  firefox: '15',          // Firefox version\n  'firefox-android': '4', // Firefox for Android version\n  hermes: '0.11',         // Hermes version\n  ie: '8',                // Internet Explorer version\n  ios: '13.0',            // iOS Safari version\n  node: 'current',        // NodeJS version, you can use 'current' for set it to currently used\n  opera: '12',            // Opera version\n  'opera-android': '7',   // Opera for Android version\n  phantom: '1.9',         // PhantomJS headless browser version\n  quest: '5.0',           // Meta Quest Browser version\n  'react-native': '0.70', // React Native version (default Hermes engine)\n  rhino: '1.7.13',        // Rhino engine version\n  safari: '14.0',         // Safari version\n  samsung: '14.0',        // Samsung Internet version\n  /**\n   * true option set target to minimum supporting ES Modules versions of all browsers, ignoring `browsers` target.\n   * 'intersect' option intersects the `browsers` target and `browserslist`'s targets. The maximum version will be used.\n   */\n  esmodules: true | 'intersect',\n  browsers: '> 0.25%',    // Browserslist query or object with target browsers\n});\n```\n\n### Additional API:\n\n```js\n// equals of of the method from the example above\nrequire('core-js-compat/compat')({ targets, modules, version }); // => { list: Array<ModuleName>, targets: { [ModuleName]: { [EngineName]: EngineVersion } } }\n// or\nrequire('core-js-compat').compat({ targets, modules, version }); // => { list: Array<ModuleName>, targets: { [ModuleName]: { [EngineName]: EngineVersion } } }\n\n// full compat data:\nrequire('core-js-compat/data'); // => { [ModuleName]: { [EngineName]: EngineVersion } }\n// or\nrequire('core-js-compat').data; // => { [ModuleName]: { [EngineName]: EngineVersion } }\n\n// map of modules by `core-js` entry points:\nrequire('core-js-compat/entries'); // => { [EntryPoint]: Array<ModuleName> }\n// or\nrequire('core-js-compat').entries; // => { [EntryPoint]: Array<ModuleName> }\n\n// full list of modules:\nrequire('core-js-compat/modules'); // => Array<ModuleName>\n// or\nrequire('core-js-compat').modules; // => Array<ModuleName>\n\n// the subset of modules which available in the passed `core-js` version:\nrequire('core-js-compat/get-modules-list-for-target-version')('3.43'); // => Array<ModuleName>\n// or\nrequire('core-js-compat').getModulesListForTargetVersion('3.43'); // => Array<ModuleName>\n```\n\nIf you wanna help to improve this data, you could take a look at the related section of [`CONTRIBUTING.md`](https://github.com/zloirock/core-js/blob/master/CONTRIBUTING.md#how-to-update-core-js-compat-data). The visualization of compatibility data and the browser tests runner is available [here](http://zloirock.github.io/core-js/compat/), the example:\n\n![compat-table](https://user-images.githubusercontent.com/2213682/217452234-ccdcfc5a-c7d3-40d1-ab3f-86902315b8c3.png)\n", "readmeFilename": "README.md"}