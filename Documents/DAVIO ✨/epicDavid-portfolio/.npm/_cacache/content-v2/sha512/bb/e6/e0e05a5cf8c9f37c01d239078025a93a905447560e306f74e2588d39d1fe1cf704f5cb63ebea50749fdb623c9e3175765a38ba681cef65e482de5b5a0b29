{"_id": "@babel/helper-annotate-as-pure", "_rev": "122-4598a75d45ee8a77754be342b599049f", "name": "@babel/helper-annotate-as-pure", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.3", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.4", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cee09312fbe2fbba2eef2d20fe9fa95435945c9d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.4.tgz", "integrity": "sha512-qv/RSWo9TkTynGR4q3AZigs7Ij9ewg8wXRNTam/Tox2MN53Oq7BLuzuONWtx8Hp5CoHHrXElX+Wtdpc4U5yBAg==", "signatures": [{"sig": "MEUCIQDeBmwqT87/48epd2dj18mJONHVjWJ37zoUI41UFaDpFwIgMOEUTmP3zsuHKAICTL5/+3LEdB8sUCPUNFln+7aawP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.4.tgz_1509388500759_0.6496302862651646", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.5", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d061aaa9f9dd6bfed8a3467fdb7ea3a38a268afb", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.5.tgz", "integrity": "sha512-1vBG2yLUPQinKIknrKRYFH1uzPgNiFKfrpVG3nSRPiYd78EHg5aOkaWwFTbRSkad5aLU7W0AxpIKduqU8EVV3Q==", "signatures": [{"sig": "MEUCIQCPggHmpsaRTk3maIlqjRNFNtXCDaiVLFEn7+xuCTWu4gIgS0u3K6zkbpH9sjDSqyMQPAFouCpzz29AFpn1FdvjSxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.5.tgz_1509397000676_0.14055340271443129", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.31", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "acdc6e9a409037545e9d97cb9e84d8c96f0c2c7c", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.31.tgz", "integrity": "sha512-ZOmnLEo9yTiG0C6m8gzS0wQELKIbrE4WqED0naBUHe0umivlg9zs4MWXnIRnOI788OYaABNRxUlFx5qS1yZnWw==", "signatures": [{"sig": "MEUCIDROFNv3TVocsVJ/F6zISbyhxORM8Z4VUj9Ach3OxZs8AiEApF4JJbUQAf+ict7qFlvniAKGqFkfwaH6S10qp7tppoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/types": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.31.tgz_1509739419301_0.8355776565149426", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.32", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cb1fde5b8a0f349d0dacf6c7cbc449de4ebc0a5c", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.32.tgz", "integrity": "sha512-k7kaUbYARwMPbaSbbC1BqIZFQ8eXUxrzvDmI9JpJGI73ITZCFbO3R8fLkJ3rTAA+VNcBpgCrSFy6hzrkoYRJLA==", "signatures": [{"sig": "MEYCIQC/FvZbk1aCzr+zdvODrGmVSdc3jcqb5xWCUB9rJEhg9wIhAOS+ZqnJeyM2jj/SNqxYteBBxnqEGPTk3kBR1kQUIrxi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.32.tgz_1510493610862_0.39451721287332475", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.33", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e939c6c21342c95a96e574d598c74286818415f9", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.33.tgz", "integrity": "sha512-V+1wex6Lbag67cphg+FrwflWpfY5dwCkGayE+asfVaSSM0ouGCZq3ZkHCR//Pl1ZtAyQKEa/bUCgoxkNucDmxg==", "signatures": [{"sig": "MEUCIDtENcR7ILW9lGTNk7Zl9nqoTbbQQkIXvemN19KXBzRTAiEArSWmi051e7WHa05/1KRKscslQxl0ejnlluS8+mY9cKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.33.tgz_1512138517130_0.43082021176815033", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.34", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f3c8951dc87f5700d6cc41cae8fa0db43393bc4e", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.34.tgz", "integrity": "sha512-Sbf/3iLVGtq0SEI5qDV2uGKGLWsqMKFl/jKVWXomp0SYYuLtWxvMacvVaYa5EQ7U17y4yf9iao9GQrBJ6MOzXg==", "signatures": [{"sig": "MEQCIDFhZO+mlTDpXdL6gQE9ay0O2vNCi3J5XbnH5GJTn7x1AiBmf4kmfsvL/Qk0T9GLNhU8QJhG2hWk8flMi5W4wD4nxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.34.tgz_1512225577755_0.3031258094124496", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.35", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "d391e76ccb1a6b417007a2b774c688539e115fdb", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.35.tgz", "integrity": "sha512-bc2idaE5XgHlyZX7TT+9ij2hhUFa21KVffQY6FTwDRT8BgqgFhIzLMFLRfk7Bd9jj+YwuydHCbdp5jXbeGFfRg==", "signatures": [{"sig": "MEQCIHm6J3qHx7zer+RpehgC6heob9tBULsYB6TAde/mBSc4AiBLubyxzoQ1Il5SPkp4HcwarjlSivbwc06CmC/sGXoWJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.35.tgz_1513288080257_0.06295189936645329", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.36", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8312c64df8ef47e16c745fb47bd3336b4e5cfbcc", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.36.tgz", "integrity": "sha512-tEtiA7u7ez+GLAX5nrRsrqfCzK5m1VOG82xZ9EHACn2vHphYd3aJA35JvNT7w34MtV8yCjM95r3lnenrIxpbNw==", "signatures": [{"sig": "MEYCIQCQPPlwGyYQaHWGy9UHePqaF2pUR/scVqqJ3BFfbUy2QwIhAOpLiPQgh3b3JI6cc91WkL0LfcsxrtMikZ/wBALwPTE9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.36.tgz_1514228697512_0.21187214250676334", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.37", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9c10fb9c17d36837c9d29a5d5de6fee963402b71", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.37.tgz", "integrity": "sha512-BuyuHcBNu0ov7djHr7BjHNAHOEOXWPuttZ94xB+ErCuyjf+EQ3MsmQdkcHUh8EDW75DVPF8OC1EFYZ0XdhBvmA==", "signatures": [{"sig": "MEMCH1KOLr2A0q03Zg2KsAXj9GatDqY2YKmHXdgajzIeKwMCIGHfaM2rqaaAuJGSZmR/y1oNQ/C/+/FWwurjkL/jvNr9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.37.tgz_1515427361476_0.24974165693856776", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.38", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "888cf28a1b9094d670dbdb1be1ec550b40c2dd9c", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.38.tgz", "integrity": "sha512-7k67bppxvfSS+hu2yiDeuVTf8GHOlz8haHwRJZDXiXwjm2IBqEYYA6Dvi+xNMHy05pZVBy012qpS7BpguXoW4A==", "signatures": [{"sig": "MEYCIQDz/UVKPHFtSdsvc+WLWAEPkiSzC8/1iBEThQErFupxMwIhAL0RUTyffFqnZsSMMs9MEAqENH59ihXfh3SlIMTTmNtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.5.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.38.tgz_1516206729724_0.8357682153582573", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.39", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "cf9506c721c838806ca5eabe15783507ba2edce0", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.39.tgz", "integrity": "sha512-q+rE/5K8dTi5FYhG4iOsh11BTgEP8FvQ/zVInOezcihPUUJxpi5t9t3bi/SGooIIoYQdwSL4/bo5s6Yl4ZoMcA==", "signatures": [{"sig": "MEYCIQDzcjIk4mgt47vZNxMrdM8X2n/LUoBojarNO52TDDwCxgIhAKhNjDCkmM9pe7k+CSw9nBQgnDqfboa2C9uSF9PFTwGL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure-7.0.0-beta.39.tgz_1517344064050_0.8540469219442457", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.40", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "095dd4c70b231eba17ebf61c3434e6f9d71bd574", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-bJd92d70QTlcqCO9WiE8C94r7NwVzJx1V6Yz7rYi4IQ53P0jbh9jjKL2zl8YoU2S8M/KX1jpu+yIgXbx+LOruQ==", "signatures": [{"sig": "MEYCIQChkgXhYGA/KexTIMgvMnZ/nKEjgSPXx8gMUtWTF3KLSAIhAM3KntHQyaOwPDrCn2u4dYaDSHm32Tpbr61qv2X7/TsD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2209}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/types": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.40_1518453713804_0.30196239199070973", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.41", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "dcf9a282d013cc68dc8944966939b9db951948af", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-HQiEKNpsHjmteGz3NXnxT4sbdcCdnyr/2sXFi0LMT6iRB55lId8AZhvuGIErVXKJ4oYm6/vbFNT/DPEWLr627Q==", "signatures": [{"sig": "MEUCIQCKR6kH5n/jGjn8CQ8P2shposgj0hmLlFTIvYTuZF6LTwIge3ZueUkRhD+Rw3CgY9RDwO4h2k3bo7D2ku//FuB1p5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2209}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.41_1521044741971_0.5376275526144216", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.42", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f2b0a3be684018b55fc308eb5408326f78479085", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-2lmcB7mHRSXZjDV9fdnWGRco+5fbI0PdUtsL7mNA2GtJs0GPoKdV3sCx0N4cpzG2YRR4dNCiB2riYIrzWjmQ1Q==", "signatures": [{"sig": "MEYCIQC2uOnRO8ViK+9cb9caHFP0Dl1nYvfi2S7Fw64rvQg3twIhAN09oCqVqGPVhLs+7XSz+5QnTMqNUct0daz72hsf//OE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2209}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.42_1521147016388_0.7584053085005431", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.43", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7bf40e89ea38f8657aa24116f5ed7d62ca4b7b49", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-UyjSb9qCjVHKe3una6TCH1kIqnBEWmHVaOH0EXDCaUgvj9E6dm/0dSclCJpo5tVPLy0sB7LLHVJwT3OLK6atCw==", "signatures": [{"sig": "MEUCIGoKRU+SYkfO5LL0D9pSihklaaeckfwC9gTDB88eOGjcAiEAq59WDDGTsPNO+bzGIuopNZYa1LM2e9/ioYD/F/LUQLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.43_1522687686916_0.1938086814279969", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.44", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8ecf33cc5235295afcc7f160a63cab17ce7776f4", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-trEw653XRNMCBIno/GyuffHi7AxB4miw1EHDeA/q9uIYNdyaXahIdQuBlbzGRWWoBdObFf4Ua0cDFgWkrfgBPw==", "signatures": [{"sig": "MEUCIFQcAHMp7jOt2HK9L3MAK8k4nqENuign8cqCe0Nxj/2pAiEA1F/jHw4C/P5Ekr4lL13bRlKBvsPoHXEwKnQ35i6G9ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2331}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.44_1522707588809_0.7820693029312529", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.45", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "29b5a9a8f99fb3ae3b13033c5f0afea8c8327876", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-7h72vEkuQMLAypRD+A8McYH9u5onYSk6ymvy4IfaTlwo4aDEkssP11WCMfohVQytdywX15JSCF6xrdFJbecR5Q==", "signatures": [{"sig": "MEQCIAl0OfST5DDyF5HOxZ0BuM7TJ5tmJlsOfm//B5/R7QvcAiBZJ3vTlhnp0q5nidL0C4WYzQe9+ICw5n0GFmyVsR2czg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0fCRA9TVsSAnZWagAATswP/RcIejFOTz1UCNk1P61Q\nE0dWLrp35dvz8F/d3IGsdaOTtBAc4IsFIVk2Te/WUlZwwsJ8rCz6NWu5q6yE\nEw8+ncZmH2pAHbrZY0n7egG6SdOlrS+XRx/29cVYwBTekxN+mGShwv8420u1\nYVclBdhzFe2xMuA6Z1dZ5keD02Q8w1OywrvGEDgglvKsnYVsFNVWCyzU7QPo\nIAuZlcIn+LLzsFwCcyNXHQ6QjOp1mAKG4AYmcSLYbMRDu4qIXHaA2Ool4gno\nhpYAxnBZTYwyHoLLjnLAEO4HB0WGt9NXg+noiku0V9tabDjDfzzsrXEWxbD6\nkCj2rZOsy3A1RLHOgoq5Unmk6jFJAzyGzSFSEgPlIc6V/JUzTyVGGM+VmtA4\nojJB+OgtWjYk4x0IkC0OHGrmWcjfJaW/HAC6PITBEtr2VorM2zys0zZIB9a+\nUkTReoIdjwWYmDAhgTlup8+xdk33lzdz8egXBoRQCYVOpGyYHuAtjm0LHA3u\ndr8a78pofKyeArsNwhKWCnzYKJrkQG1MTcMa+TZecrTWFwlB0pCIfktaF3ns\nu8gFACA9odF3YNtUbHBqkRX6dT8sLyDxjYDnnx1lZoxB6ljBDNgBYidAro14\nJf7OgRGcNR3rC6i4oc96xpyFwWxSWM8jCh6xYcuAXMYm2n5JqKw4Vg4HujmT\nRCry\r\n=yp8f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.45_1524448542993_0.29035421732130184", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.46", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4cd76d5c93409ea01d31be66395a3b98a372792e", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-ej5W347ghJF1p2TM3VcEyds1+o1uy1apaQcHrYFJPus2xCgn5KkHPkBGf+6euLfFaQDtB+eWPVKjiZx/hpYXvA==", "signatures": [{"sig": "MEQCIHCyvMgY8IlTG9jT1aNz9BPdDslMIkm2V2pGXC49e+E4AiATD5Y3fgCPzSs0Gn7uorGYJ6ZAPjm2q6vF14aPXz2sIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFQCRA9TVsSAnZWagAAbvUP/ArISzDz6Nb+WmK3evO1\n1KF4FPgGWDViqvyv7aIFqGUWCr9Ew/aicPFtd/yEa3DNu6J7WxotRINZxoA6\n87VD2DyaQjCzVzAzY7scNIOjALzXOcxNITzaYzAYKJwbRthq+CtBDhIh4kIF\nNolqGmp2aC+pd0XTdFBHRvlvF9qmtL2K3UX3tdrzX9y3BECDPl3KEJn3iZKe\nwFZU0upSKPWb3AjbKPF2nk2EVfLxDxKSgWbaWaf2BLhRP8CuawYkwPVWsMtQ\nGB/441ugheC12xnaxIXNeiMiX4qUOGA1u1nPPnusKQ5bITGKimoooWnDrmuq\nXfHo7nE8cxHeHx5GpAp340RWXbtK2gHNL5O0QBtKAwDQig3DqX817oyjNrby\nwFIo4azIb5b9EUcjW+iRfEkOu6/FKVjT7v4whiQsT7ewO4UhS0D41a8mSlQ/\nfFMqyOjL1DT+ULqU/DX/JxndR3EGtPxqjWs3LPzpKqk1+46zZc5D7C91oSq4\nJjPebtN5myPl/Pzbv6P0Vjfj7GgIS3i+4c7yD6IXRPPJxxgvfguwDw3zE1G9\nCTjiAJ8Mo0rBGiLPTseRcPOtqPR+hwA0vXXBuqWczd8RCH0TGvN1japbwhrC\ncqC1Xqcdt3Wt4pbhi9ZLCikDhAJbIDZdBpe0J+VKtibFC9qjyJ2h76EJwiAE\nwZdN\r\n=xTVb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/types": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.46_1524457808263_0.8120676181723858", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.47", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "354fb596055d9db369211bf075f0d5e93904d6f6", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-Pjxb/PrxyKWc7jcAXlawvNAQMxxY+tSSNC5wxJstJjpO10mocmGzBOqNYjxdvVhMb3d0BEPQ8mR+D65fFpZ+TA==", "signatures": [{"sig": "MEUCIGPAHIUKAaw33faODX4Uql8rwWhgt5QZpU3tV3tAhqaDAiEA9HHhPBut1bIz15ghkZldi7dT6X3G/LniysBZ1V6AWm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTHCRA9TVsSAnZWagAA8f8P/jYVMFmZrlQjrtv/vcgt\n/MW6A4GUnUhVXjdt6WBF94s6th08f6ejtgiva0VE7m1AratEOCyTxZowEus2\n/9BxV598jyj/AMZvrO6K/x8rdiGrvfX2bUsiCaNz4noe28SlU4Rre6fYCztx\n3cafGhJohsEg3t7igzfLUt2xcZZ8KWzuL701zfqB+m+eXMpbuk6CcO+ZKTsd\nZxITSSceizpkCYfi/Bwibb7aVPj2YOwYoyHa9hWBgy3RYrSWlrM8LolLtXMr\nsBh/GYmXk2KvEr51F58Fo+Ct8Ug/7Uw8BvGCjJMiD0+4qfLNMJLlcgxgNrtd\nztLyBfPCfUwWCY2uWia/2/VYvUIZozssh9RQoi0SebiKN52EWtiBMYhVaMjN\nOxIvUUG1gGKDKaMr3hLvELoTCJJl4wzdiilsGB5v1x385rxiMqc1I8vx7AEL\n4Pmsh6JCUuYw8Sa7MeaCPPc0uIGGl6WF9wG18NqpBYVD7R5ePEX96CcMJ/Q7\nT1up7htMDM9xxmFY6vZvK6yY2rQIH6G2u+LweJm9Er9tROe+BDhoilx3/Bug\nDbEekD/yoGSEIFVd+c/598yvxCT479o+frumSRpwoHFbiZZ0b6AwLSTxJyKf\nCD0Ei/kWzQjjdVd0N4txw0vK0Eoqj1p5sVZFAnFJ0z7Udal1eGmcqnwpHeTz\nZ+Eu\r\n=oiU2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.47_1526342854451_0.651764327530802", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.48", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "edd217fb0349ab36a5d79af04856071163513f47", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-LBldrgCApkHFwwtMJC2P2MeH+09+vPVj3Gs+mltqSpY+LyowjjUmqLUcmrF+Kj/gmRY/DqGAbvZWwNlNDs6dGQ==", "signatures": [{"sig": "MEUCIQCVt7G16UIl/2n5S0FwRB4gd0hA1JX7dvfw12prfl/K5AIgCO5T7pobfRLOLvmB5ar+3NQiYuCAhHnFhnwONu5BQIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCWCRA9TVsSAnZWagAA58EQAKIXFUsEBc7jCm4b6AaD\nIYxm7UXlaLi3ZiklYHiNJ6AOqiWZq80e1Uk/7aRSJc9lSv9KKRcmPwp/5/p+\nEBNXtQP40PuPtAwRvG9/H9lbGkbDVv3vEJ2bx+PcyAV7T5xRth1F9iMMp8Rk\n/m1FzBdru5bTOKJggZlWYWl/k3G5qMiHWobbQJLa0d1VbObL94XMIKXN/nDY\neDsG+wNxqnKHMxWMKSa7Ja466KgzFF9d7AfsF0Fo+vBiuJdACMFEgOc6IqW8\nJcNRELjRXhQnMd7mvfGtwL7rpPe6CVbBbQqMwTxHbf799N64SwhRCFpVQY9E\n88DTbCwGbDJ0JPsVcJCyxe8kIOwwrwuLlAhEho/U4ghIlZzuBXR0MiaKwfii\n7Z5J7ymQrXbL6cvgrcuou6M0WzXMQ+737mvp5oDut3NTbhe0LZkjW8atTEU8\ncAddo7U5pLiwhAYsshXGpQnRNeCywNmYp8xwRzmDapGOgnnElNSA51YtKnm7\nvKeh9Y819KCj4SqxLoa82uG7zrWzatZIfTqNfcYOYWYJKqSbPWZJ9e4npvwQ\nEp37QMB81Z5ARCoguuXz6SjSKO4N7LvTc3huL/i288kRxbdf3UpWhcS7mMSx\nZXrNbhMSqRxs+w6X0HJ+DJdMYQbQ/wkWF+YDr5JOa8olp7aKAU1KAAb0oXo3\nuEKy\r\n=WCO+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "5.6.0", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/types": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.48_1527189654532_0.2075251287275124", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.49", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7d9005d54fe7ad6cb876790251e75575419186e9", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-j6xn1pp1e2n/IObpR57ivUrzUzCJ6UXV/EnTPduVg7Mf0B4PaOTceLNwyOsslFNKV8bH8NRC7ndprMCvTQSGyA==", "signatures": [{"sig": "MEQCIGMN8bBEYka6pjR6/FXw57F+sDjq+VoLAZRONijemSYUAiAGkeRpgIjCqWmvlwqUh3QKnYDjwuVaVitxBo2bpDDhmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMmCRA9TVsSAnZWagAAeMMQAJ6WvlFdirFXSWFm4TLu\nQ3LkvemlhHqMZ1E9qQ+3k0pWu/p85GlSZKinXTfU1E2FtzxydIBbNIbDCc1k\nvYCy24cw7G1WUqMcObdd5h6QunKCZ+GpAnMxwLhLYD2fWL1tw3MldE1E6ydn\nRl66NN6feuxRtedrhD+Z/PFmp0yPN7WRkNZk2zhki2PB55t3K6632UDfrY7Z\nfe7FPa2DM0ljdJP4E3VfgnmqFLOPFdB53LtnyLDIFqQh+P888i1rdZF/Gmhk\nPqmXGUKjmPDSsbtNVYT+sKLt37iH3fzuWNC1shEv1yNqQxUmoOj35w5bhPwW\n2GGKNxmzLWdmZQx2Mx0+FdeJxhuadxSRt7ru94/1f8ieVSkZKJsyLe7r4ZaZ\n3Or/QZkHCIKgWN888wSXzNNJxq2tiKIDdk3S1sTeFoBlqe+l5QUWux8Cq/uZ\nxPWksSRXV6BWo3wsCXR2a0Mt6hPOItSf56g8aE/wTlKmaGoH1g/htRXgxIYc\nfJm4aKMlLrLrOupcE/g1voTqJhYsm19MoIVCeQUV/2i95HftnlEmAeGQvgDU\njWFGaj0JcMhXiuYRidUIr+o3j/ugC26RZRMMLPZTFr8zBsb2JBsY98k2RiMe\n2gLPd5Vd1JlOeAk/DTkEkMIqwn9LAb5R9Vhdh5CbYM8YspBmig7joKJfuNJb\nMIW3\r\n=ssK2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "7d9005d54fe7ad6cb876790251e75575419186e9", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "3.10.10", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/types": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.49_1527264037171_0.14751280484689344", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.50", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e3c8e0498ee934966f8eba6558aa66fce99e585a", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-MvIdHCt8GQA1UKXepIs+MWKGCbGnYpM1dW6+2+lylB+w0jk/UZZL+8JK1piGcAequBD7zkl4G/BCMiaqC4d4Lg==", "signatures": [{"sig": "MEYCIQCrDvsM7xs7mI3gGR37V7KYt43ZSmfbQyRMuFClGHV80AIhALZT4/ClapxapDC0muS5EOXHlfq73t4+aWfjXl50jKSf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1875}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.50_1528832809325_0.9336975556672351", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.51", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "38cf7920bf5f338a227f754e286b6fbadee04b58", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-3RhwyAC/G+wEJ1UFE0XVBs+127KULN+18+ZgYl+wDR+JXUT4vw7TCqf4KJqzzS00iBRbungWLStmUy7mvStoBg==", "signatures": [{"sig": "MEQCIFZqEau+sRKwsllDrgZebMw0F3XSrAAC/AVxAIHdB4SyAiB2svBZD+wW4SfDVWPnRjNyyqHRPbEoFWZA+peK3mDqMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1875}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.51_1528838355719_0.9815616893540373", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.52", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4d5bff58385f13b15b2257c5fa9dfa2d2998e615", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-Or3MveqzkKy4tuDd2yr9mdzo4NUznstRDZclMkzJuZ37XfAMnzD1D2Q0AXLh4bzmdfniMulDdsw8ojBGbhTuzw==", "signatures": [{"sig": "MEUCIH+U9b302oGEZzZg2EBPaTPT8AgvkcLb+RlB1Pb5hanAAiEAq5BBEAHjWAjq0x4G/CUzFEC8qqTI73uIrUuE7HQBMls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1874}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.52_1530838751909_0.4574807230884881", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.53", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "59960628375cbeef96a07edfe1ca38b756f01aa8", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-HVsEm3wjSe3BCXWxnyqrTWWQAxvtHR35F4q84jS68aS8R3WfbOnFEwlqsrWX5quZL0ArR68REOWRDCyG+JBSlQ==", "signatures": [{"sig": "MEYCIQCxAbug7cBVsNX41xasNbCOMZT10w+C6DaHolSuEzJLrQIhALnDvuUIq8vWVFrYStu8bfBKlY6UyMlnXziOjYJMXk/i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1874}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.53_1531316401336_0.03691586489740839", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.54", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1626126a3f9fc4ed280ac942372c7d39653d7121", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-r/tdXBGEy2w3qX+6OrmOy7Z5C9XCm3y/55YPks9yMIluuSjxcGhV2rjiHsbjUeXJPwK+F6X1v3Gksb3kfBjhKQ==", "signatures": [{"sig": "MEUCIEpt7Mzaj03fDgw47ag7juw/c3FSit0MME61qGS9Kq83AiEAtsfsUGE8md8I/IQ/n8UlXQQyD+L3jKh5u/0WqGRA6eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1874}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.54_1531763991630_0.9663155439564686", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.55", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c3e4c00e14e7dea917938e35ed5d9156cdd35ce", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-TUSyZlSJeKNzso/lvpLOF66P1z/bpcvUSxiJzC//7yShLVAtsSEt5ULqqiaCyoPyK44DcaZxTv/AInIMZofFdw==", "signatures": [{"sig": "MEYCIQCZvZTH/tqPmUYPMBDluTMZv5nz2wkuHfsEHMVaaYAfqgIhAJtqUQL4vaY/6XeRJg2SkzurrCpWshz3ukbxwBXrkBdu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1874}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.55_1532815615551_0.9130938290526875", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-beta.56", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3814bfda01ec19f7daac810cc2375932a79acbbc", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-PaHQ8R489lwBZYz/F81YpKDurIQfKWliNIpHZAysYbnozq8hVyaUx8D5wW6Dplf0lUUQ8Y/I3YKtiNoyg7bLHA==", "signatures": [{"sig": "MEUCIQCnpwUEYBzGRxMgJH2UQoEG9tQJ8gR9SIFdCkky52vgfQIgS7oWpE7l44XMfG5uVKR8goJ/ChvTZPZAxKhxuHnzQfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPtjCRA9TVsSAnZWagAAPnMP/2xnWLy+egQLHKSg6yw2\n1rTA8vVhoTtaX92y9gFh7TE44kPCtk3yCqehpjvdwRfrKNuI1VO7Nm8Guzue\n/UPMl0MOYnluuSJ2IeecJxCdaHnnWF0bbUczFbN1DNH61/4Of7ggDg+zUqrA\nojdIHUlRzFSjGCwyfh3B/q+OcYFKi4Lz/t1s7nf7gFaYByY0YBRBrrnfN9Q0\nfppaxAkdYIri7y3VgL5Ooknk2Q76y8isfDWko51Y6rBlQLddwwaCqQxQS9YB\nTOA/zNfrMrDMt4vI4QDz0ykaJwUL4pJV4hYfTVElG9q0idqd1Xm2STyPe4fX\nrdX5Hk/I9zvjq2bDvN3vDCry5G8tAu0SIUQzbmhZFpXWhEDl2dNlAOWHCkWU\nDE9CLz2lTFfgAgmE8WdyMjcdSR0hMt4hrNXRHMV9lHmVIK4Kso7fR1lJb2zM\n9w/9DPXM8TdJMLA+bLHHEOtwktONPx4tD1wF0IYVOxd8765N3iucgtfyRIHS\nOITNppoedOG72UTgED5kpNJl3RJVmaM8udT/YPWX/IrwoVTG5TN+JQDXhvKV\nBeLhkIsEmfGNHVH2lAOokWuoq3nzow/5UJTAEX+zqLGrg8ypYX3xPkhxKJ+c\nN2LK2PW/rcLfZyVGmUXrVL0tAKGsU5XNCa4KMhLQAnMclMtQX84CFl/Sjv6f\nOpBD\r\n=UFBw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-beta.56_1533344610769_0.7427542834783691", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-rc.0", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "afa9910eb99ab23e071f8ecfac963804cd89e49f", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-ywTND2HVmManxvXCuRt9LA1OrwBp3RNwlTHDzL4N2Fua9BCEQ3MeCOg/4DYsjndzctBZq2sdWMuoMw/fRjOfgQ==", "signatures": [{"sig": "MEYCIQCBdF9X95uPfk5WH2Iad1/TL499er4yUXyI5rMCUm6WiwIhAPKfcPHsjE9wBq+3uEQXiMyU1/RWAlZMNg1iVKt788wM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRBCRA9TVsSAnZWagAAGDAP/05rJIvdxqyHTo1OMbHh\nmBm2313hNT9E3PCAeM2En9N7L+9I6sSBSTwsL1/ANFbu2lD8EsOV/EjAKu1x\ncqvsDhs9zetc7D5q5SOtjgZsP8mxi7gWC7AE3oFJTG9o7wJuTKJM3xktpBS/\nwXDK/E9aP5w3zXCwtHo9zyd/oH3JynYaMX2/w4xkITuB+Muyym36uSHb/587\nw8JH/s5d6RKhdPnH8X9DLi51NTTVx7vjmvnrvchV2IdnYcrBa0LJ5+sNfMno\nD0mKQOP6ZRDtv+SpzSZbSh7o/msKJAlSX1GDj1OEcKqMZY7DxJWyeY8a4jM1\niwFlfe48cmQjwcheFVmvx2a5zeg5Cpxv11a/iFa6pgXqjVUeia7kObvqyz51\nqefDJ/JK/HAglMy/cFOUPlrPGwN/YDoWrlOa7TyD4XykT1XloPb5+hE1xLec\npbihm+/px36zFFHq28Sjh8a0BISQbE8OGYe2y0OHXnfTJudwPsSJrrmO8/i3\niDD8tuERpstA8QB1iZ99jxuiIiUU/xaytQMLHzaxznQ1rOTEHL3awr/H0pEa\nUdVvpLXVSiWrlnmD58cfMJ/KqBemFQ85AAuzC8IOJ/z3EvvYnJ0K1bnaJJkC\nYKyNLiYrnzAga0+0qOVPTtUyWNi/6s8NCsBcqcZq3WDDJ0Rk9vIi0TaicO1d\n/q5X\r\n=e5j5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-rc.0_1533830208909_0.2672882702771704", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-rc.1", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4a9042a4a35f835d45c649f68f364cc7ed7dcb05", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-GOV2UExs9gAvSrZF4rcgocXXeLJplq2kL2AsCrn6DmGwMUEfo/KB7FhedN3X6cVh0gOqqKkVKXrz3Li1wQ84xQ==", "signatures": [{"sig": "MEYCIQDi+i3LVDrruNTDeBKo0oqOZAHIR1BR2xoNdTwrSZABlAIhAIfs8NfL6zOdlI/nICT5iWUN/2cymE8FqBdykS4PG2TR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7PCRA9TVsSAnZWagAAwKoP/2lUzJdcdH5HdZdfQKK8\n/NLL5OqoggO0ujzYrwdIerblzi0V0tXObQiyFmlKAxfV6GHqVqpaLwKfM3Sr\nYrwJjpyamOLLuNxTD7QE4cRRtkmZVV5UGhQSxNoSQIdX2MJhTSmgGqUiEHIa\nNn7ZiPz0fzoWG2izwOizxJaJLHzSbW6xnN+XHjBDZxi/Kt06c9IJ/vk2gosY\nPu7O+3SgsbzwEBqLTLYpTAVcot/em3oqdZCDAroE3az0o9sOG83ErzRmVn3f\nBEHtCfPd4IPsrcxlCv0UoFg+9au42nG+ScOxhgCvmUgxJ3247Brc6KmINWk7\nsbuRWZGqHAL3KWVnam4NTjMNItGfnvTV9UUUCvgzzYFl/iV7YBwO7gdFZ/3R\n5NV7TkAi5jMPq0M600dwflQLVmKztqxuv0djEuB9RP0VbQ8MVdidIL/UVklI\nQbG83VuLFDG5NfDCry8h2OtO4tW+LCG2CDcOlECFV22SRt9x6avCRVXJTicq\nVdK83eD0gs9iP1kdv9FaceUh96Qx+6PuP0maE730i4ViRQJDH4REsDtIxENz\nE8vnrKh3wexoQtrwK5Hsewz9feKYPi4l3uUicLcq9X2B5QC1TEfyqaghdFZH\npAUMO1Mt5WFdq5kbTP6a306Blo7yu6RY0q5jm9pjHBtzXdtGRCQb8a6zx2Mn\nyg/k\r\n=8CF7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-rc.1_1533845199508_0.7573165451018269", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-rc.2", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "490fa0e8cfe11305c3310485221c958817957cc7", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-HukwqtJnDKo4++QL33d1cKwULDENi2YziqG4goiRiILJsVZYdZxEaOho0RYlzsKEvq4A70sbakUMw3bFC3kp3g==", "signatures": [{"sig": "MEQCIGIdCvAyFcdkm015oLYY1hz4rGEh9Z3oX26R1nawcak+AiBJCsAM8FCCq8Tfom7mSNWeTxVqcsoBsVA5jwiHTVxQwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaDCRA9TVsSAnZWagAAblkP/1kjHE5YbeOUrDhGLC8R\nLAks3EBwnNwJbWDWjinup4DeaQ/lPNRHHgWFCr3yZW1TqEIQ/TfaW42zAXL1\nJa/4x/QbHYLjGczTIOHIIoH6mByuduHbGrb5Y2cQNPP9zj9EklME6enax4KO\nhBOrE8RfhaVlOYJr3Nr2EG0QndA3IaCfvOxxGeFo6qBmG225j71VP5+xP2eZ\n5xLTDhIaHXOmVpVvNVTGHVhA3fLeMtfo4lFdR4qbnN1cpjo1fl8rOnQNz02F\nF+6mMZZ79aJgWnVlv/pSyPn6j89m3jU85h26lY0E5GIoYvD3WiBTXqorfsxU\nBaVuqaVJMFkjH1S9AjAKDYFaSjnFk7Jf0GMEwLBR8mGs/G6mOQYjkvuqHi23\nGtWPPS4EJ/5PNMeZqE9ONSQD8RQmli8L5HT38US1vueKtiN8sfBzZJEprfUK\nrftaN0mTjCEzsChXwV8NqhF4R068jRTVpDHXfySLIRd3s+q1ciyGVqkFop26\nwmIWGSeufqqX4j960gPvHopE6M4G7ta/82rG0EUVqyP1NyuJGoBk7uVTDJ31\nmmWY6MYfhcjcJMFYIi4OgTqDJ19TH0ET3F/zsEdyufDxyHJGMn/FwM8rrKKo\nDezjhxFDbyV9AZbgJjKxEaw+7MkscTdfMlcgnhbDgMRPKlMLUKl2SDot34OM\nW/Cj\r\n=XI5q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-rc.2_1534879362829_0.549937782455981", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-rc.3", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0251d48d2f6d175ffdd9601fc032e3bdaa4e580a", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-RhZtfIVaNmz9MfnvAdkxh8rgsYr1OCg1Oj3s7AC53W7Jm7sDeHGJihO3qnDcKbudHXyAdNWACTglRo+72pVhOQ==", "signatures": [{"sig": "MEYCIQCsI+9om4aKE5LYSQtV77VH3HT3c7eXrMYQ2bI5EmKangIhAO8my6Xa3rAdV3aCsozSncHDdVFRybiZsnfkL5doaX+u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkvCRA9TVsSAnZWagAAUz0P/j3r2mwf7Uqu34RqWLjW\n5D9nv3QCJjZuAqme3DQKG2p6gmnewePR0QB0D07K85uc1CAhsvCqT8VUvW35\nT5JzccGcEUE95aG6uUlJW4dQOSPoy2ArFLbPCNY9/4XeKeYV6vOYRZwuY7ik\nUMu2W71jUhCjtQAacSNEpj0+eC/s6zpjSJ5OmSt69PmWm+AQ0TTXP3P6ZFnG\n2xRD7jshZ8uEZGNjwZ4+ztqTIkUvS49cmcH8flz+NEe4IaUS+f64lBJmTSAg\nSa+yrFzsaSmFUUQmBovjv+76/zmXcPP/TTLAEEzfYQP6mvS7BH25DomRgfjf\ncUaHjQew2SopV4znR2hEsFAidZpPNLu3HcrkcipbBt33q+xXwPKtgAgGvxXB\n3B6JMgq3MZIBNawH3a+Acm/HowGT3Hgxb82zpLLOhZDP4MOpEM6lRAoSUxds\n1Zl4ZFgNhXlEnP9uiuAUQPrxCkh8vhi0pb7BvniLG2B3sUqq6lek9vDJCr3T\ngAgQL0rTiY5XtuQMd7kJnI/ds/B0IWb73OfsTQlbz4fUowOCZpznqDfzLqm2\n/2K73tvUlTRAbh6I1PxmmFM3JGaRqjYz/e1gGCjJePFNk+QpNkzIQwe+x0DB\nptNrk8hUl7pRA8OpkXVFzpbYVqSDigmxN0pIUXlGYbUjYclcnLTl9PxBcoka\nhTfu\r\n=n0Du\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-rc.3_1535133998941_0.11408199593626622", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0-rc.4", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "59bd933355282c168c337ed5684f4eb94e900694", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-rmQ+AeWSzEwb+7Z6Opj8CQw5wx8xYHY32Y8sk6kRFEuCawD0k0LpOGGco08eAVfnp2pRmJ48nkfmKwTXGdHmeQ==", "signatures": [{"sig": "MEUCIHXZwTjMf9GPkepEriLRLPN5/LVIFkpbDs8aGsAT4xSdAiEAxT8Lfxx+FwPIuHn4L+L7HoiJVWWo4kUOU8qPuQVsbH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoKCRA9TVsSAnZWagAAPEUP/0kt78zAY413lZHR+URj\nhp8wVfHLVithDu175lfmNID9VToiX0878kDDMSyZzfp8DQGvcUZ13rP/l5GE\nBvJzCRq4VQlb1Spn6ErLC9gR/uj/ccskZDYFhOFMmT7MKES0ZYuv7dAXY5Dn\nw4dE//8+NfJXU5Bee6ip2GtLZ+0qjDG6Fp09wWxL0uhp9LLQ+W1cZfYqJxc5\nLW8O72bNSBfXErHh0xvDl2xQHi0qQ2j7KZGtKzV1TFbd/XVXw+TYp4X+Sgc2\nP2P/eXJWIPezsE5IH8S2pPOhLkEmEDnReH4MNGiLw548adAF0wrWRdI2A/ZW\nQWgY4GAFSMPUXJF8RqDCF7f8heRIavSB2tZZKTuQQgoTuF+JFYSiBTY4TMae\nuqOGzpIzlb4kJq1OObGN0Of8AYvdK2oV6aKdfjpcih5cKHHW+Hk9GzTyWW2c\nsjBqoYN+NDekU51LDgADhoOFOJow5pRNEpA5RQ9CoOHUp8yec3naJeS5nYHA\nlsi95ZfGJQWAqQOKEVfUqFzdj8Xn03KbADiEHhsZAjeTSnp7XmGyeLMwOpuR\nzuLCwmo8K5a5ozVWfqWHg3csrvAKDLnzGZoqELNo4KvlNJGZn5oSJYmr0GJh\nGGnvNJwsM6CI9TEQUAjvR1iCGlij+iWbfIMpGZlg9MfiC0NmBLq/uIEf+/YL\nRVXX\r\n=nSKq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0-rc.4_1535388169199_0.06960236456702096", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-annotate-as-pure", "version": "7.0.0", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "323d39dd0b50e10c7c06ca7d7638e6864d8c5c32", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-3UYcJUj9kvSLbLbUIfQTqzcy5VX7GRZ/CCDrnOaZorFFM01aXp1+GJwuFGV4NDDoAS+mOUyHcO6UD/RfqOks3Q==", "signatures": [{"sig": "MEQCIBlH0rpqZ4txBOHYi0SQ9ZIgftr9FVfV55vOzAKENlqBAiAaOWPq2zbNaY4MectO+n/VDEMvaDzwHTFXmL18UZ875A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAoCRA9TVsSAnZWagAAVOUP/jArylgoRsN21ukf+ixo\n6GwCJT6X9zCb86z0piC16Mt542KfMHUKlV3zH3fUjb+8cHohBukGDEKcRdWt\nC6UlWM2tipndiAmlPrXybSnwuaacfXiDw3N94M0A+7fSWCZLoo8pOhsKG4kX\nEgrdMvCMTDkYHsXKCagtk2seoil48+kNiFE2yQnivDYZa3S+ejk5FYwxLjdb\nJ3pWv950HOCcsRLZsfZVTRjvbP2PSgT6Yr+9ZEr+iSI5x5Q/iiorvZcy2uJe\ncHSqvKbu2I/7qEVYDKlw+D9lgk47VSNobsoihU2kI/YubSwL4Ip+nBWQMOzC\nhVQ9dUHtcqsHgTPgEFgCKvjts9OjSl3fN3lVEtORD6sDf56soGQ9wzkax47D\nU5mg61ZWwvXoKFkxw+ws3J108VOBb/jf+SYnMvIn5vt8HRzLRzcHTrwP2rpm\n/Hxi6C4HtuJWYz/W6RoDCXd6s6mhwLITOb0UUAIFsl5bn/erradpwYfD5vpr\nt4sEQo/lRs1uejnk41md2dqN6F/TQsLXVzmD1sIfiuMVgFZUaa9yHauQ0QlN\nAK1TssZfoUBUTrfYF5dWYpRCFyoNg9eTD/uuHHI09HFQDHyGgIggVhzcsT1S\nHPJgzCfWdTFR/o9Zz4MSB3VSUsd6PRvU8CdPC86zh1eOpRQT8Takance6Nty\nXpun\r\n=7R21\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.0.0_1535406120006_0.757773453439019", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-annotate-as-pure", "version": "7.7.0", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "efc54032d43891fe267679e63f6860aa7dbf4a5e", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-k50CQxMlYTYo+GGyUGFwpxKVtxVJi9yh61sXZji3zYHccK9RYliZGSTOgci85T+r+0VFN2nWbGM04PIqwfrpMg==", "signatures": [{"sig": "MEYCIQCeBefONhBFQCavWDAXZ9LMKA1TDFeyEzCRYur/7rA/KQIhAP6ez7DTmR7Ejzz5xXBW0rzF3M/ZWF38UAR5RMNMBHO5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSpCRA9TVsSAnZWagAAvD8P/jq+ykGlOCe3WgOwtTmI\nImVq6F6MzKBzRNih8HwO6xDwpi1voZnTloJHZRNXIegzAGhNjbG69A4g1qGS\nJ+ECORRzzsA0372sXSAJd+tD9tetB6Ew2uPmVn4kOoOEfVWD3n7Ji2os3Tzt\nBt88i9g0T7h93mUvHOxWjKn7xT+1wRy7h4+GkU5Q3gb4GiN7rWahhBPXrAbg\nFsXTbYl63xvxTLcMkQrjLcjPIKyEo0s0F4NjOmtC1zL9WT/ybjyy0xMIPFlJ\n+qWUqs0uYUzAVo9hYsjWhZNhFWH3J0erEznJmnlSPRpl+CNjZSkStVFvCFI0\nNOeXP1HYsr86HR3/3daakp0jSZIErUbLAPW3MAo3hLrea955rzOWrNTlq6j5\nbmupsOLaK5+prO2u6wUQPvBqB2ehm1kOGm4sSSpD4EO82B1IGX0an7aznCfF\nwlUbE2mPeEn1QEtRxQfXiJaNgOcrZOcD75E2USbwStQ0Y6s+6gNs0AMNoVF3\ndcg/ffzzw3/Nlo1eHnyiiLfdc5PustbvYs5pI1mz0LMARcfqvP2arKusj3QQ\n0xmRKRxVBHsujC/6LwwJwie900YsVEucGFclTQte2CJ3kl7ZQmmURn4uZtBZ\ne++7uVnA8OQnGtA7IHHNkKLDuaLVPuv51dr0qUUDaJykKgXE7cnhHKPoFbDv\nEsZw\r\n=89Qn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.7.0_1572951208843_0.9587157602984584", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.7.4", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "bb3faf1e74b74bd547e867e48f551fa6b098b6ce", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-2BQmQgECKzYKFPpiycoF9tlb5HA4lrVyAmLLVK177EcQAqjVLciUb2/R+n1boQ9y5ENV3uz2ZqiNw7QMBBw1Og==", "signatures": [{"sig": "MEUCICW1E32q/prwxVwQEfwuL1OZ+Mce4/CtlYO1Xg3LuBDQAiEAzZp08LHmZE7PaEcHRi8VhHPY6K++KuVNGRrnoxflJbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HA7CRA9TVsSAnZWagAAyZUP+wZozF7jb8RdFi5/gGir\nFw3cXwkx+ybDG2ZZI0yquZOYRD6aS2X4z8SEVYZTjVWW+3hk/NEjH7h0DUBf\nnOtFcH1qJzO0mMqAYdXSzfCFjLi9ztS3CGYiDUDWbY5DFRSmcSPhoazC4CTl\nAqUb0kVzHOkcs0YUJtG4Xkp9V3RBV0UIaDRF5MlyxRsxEF6AsG3h9BarrUSV\nu16EPWWadqGQuEwRvrbz9XEQygBMc7btSFI5mrmFkmXlNTeNGNH0j4pQj/Km\nxX8kQJ812aJHfk0X+AxvvTJwkoSTAcDBWy12Xbe55C+vFbJ4T7shPbexHFCg\nOt/eMhpkzqDEUAnBjNjYvlrOyK2yBoyDIpyx/hYQeISS1Y1zGUxn6oERtq/b\nSfckGk/3Vv2SLItrfJyeDbbESTygwKbcst6WPmy+mnWomlyL/eSv+IAj+ubz\nP4KQX/P6UN3D3+Ml20LC6PN/F52f71CZJGam2oVC4GKTEnAdkZuU5iBZ6pAt\nEAhKstgqZzRl0Yjrarj94BEs4xMUXIetiCqZYb1R3AfRAweGKtJchcPBDC/9\n0UFLcXn8nh7Dlp9dC7Ht1/tx3zbxGTneTZJ5+iBv4Eq0XsfJ8h0tGTkLeeQC\nxdlN6Q+RgSfYq/er7Fk1rC5QXk1fFkiYt+5Oem5qFDeyA+njX+OMC2fQpnNn\nL0Fm\r\n=0jBe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.7.4_1574465594728_0.6979705110971672", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-annotate-as-pure", "version": "7.8.0", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "334ae2cb801e2381509631a5caa1ac6ab1c5016a", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-WWj+1amBdowU2g18p3/KUc1Y5kWnaNm1paohq2tT4/RreeMNssYkv6ul9wkE2iIqjwLBwNMZGH4pTGlMSUqMMg==", "signatures": [{"sig": "MEUCIQCQgz08xPsogLYMFw6c46xT/xqRYHsHl1YPo60XTCgJ1gIgF3hURIkmZS75sfsEnmZF/nv2kWDT+u84VFR/Wh1b2wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVzCRA9TVsSAnZWagAA0IgP/3/Nk9D2MyowmdXHM3XS\n0jEOkFaIngLVYjrEAqASrIQPM+eE5mzBpIh5u/ZcQ9c0KW+ID9FPwn9ugzrY\nH09hjHiQxgCGvbpQjaKo2NUWVTPzS+fohyuluhTIRhrw5xzMJcYIX6dIwjTS\nmJnOmI/1VCC4ShPRxiWSrqHpUOwBv0iSvBlb7+lz/ySHU+HiGxbe5nyud1a8\ng1lsF/IYfP7N5aoTtnAOA8Wp92bBZklD2/W4C9TPb3td2GHK7xpQ3tEK93gZ\neKm0OgqMXChfnpHqWQahcSatNAlqoC7RsovQYtEmcM8L272dsQA3yfV708ha\n03VtELwtTOkJPOIFJ8Ur6UG1H/+DzoVcdpxQpPkpvt8Xoe3ITDQJtsVXHB3d\n8vAZV0h7BZdnwt0fFdDYN5IMo+5kIEjJutdOpRf6ekwk4o5+D+PBu1MhbREP\ndWGN3a9ZnRAV7rmqiYhC17rTRL5KRvwUm3sQg+ITXZL/zrHPB3G3U3h03mXo\nYXgfua63NoYCXgbsmbwxJ7FMjFMSi6EdXMQSbEl9VZi422YEIzjc4uTVOLz7\nwWwUAV/zWWTauuat7Jp3bq0m9uRp0+Lpu36kNhGl0M9nCOEJk2+uWayPRhOh\nQ8xrc2qwRYrrR+Hi6G3w57lsLjIZZTNn1sHKhJMER8j0H4k9rMBLTN1RZ1Ll\nhbsZ\r\n=UOaS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.8.0_1578788211242_0.5437606632724594", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-annotate-as-pure", "version": "7.8.3", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "60bc0bc657f63a0924ff9a4b4a0b24a13cf4deee", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-6o+mJrZBxOoEX77Ezv9zwW7WV8DdluouRKNY/IR5u/YTMuKHgugHOzYWlYvYLpLA9nPsQCAAASpCIbjI9Mv+Uw==", "signatures": [{"sig": "MEUCIQCY0dXbrnEEKLlSn0nDDjRuvZs8bl2CuhDq0NfqLioxrQIgErnqJkhBUzi7kfoJZBLyMLCFC6fzIe6YHU8WVv5S4Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQZCRA9TVsSAnZWagAArCAP/0zSY71XAEXUGSRbSiPl\nSsqOBxbjgCCoYakINR2JWOGY2Nfp1RASdl/SgxrXf+xagy7A1/fvsxauX4Nc\nJvgLW/9E0J+wkVuZSyNB7EmBMWCAaMhsaLkccZSWHzDw1lPFCH8Agi1T1Urm\noWTWschadx7OdZeX3zqhs6klHGYaNhvpMPa7CmYX2eHQsoWE+WKR3CVTyrcH\n6PWfGD2Ty+h3v3xSCdMgLhtrSwjxVeC6KUGTYl18KU5/F7Gh3fl7F9uvpP9m\nI31N/UuesOlmWb4ec5H2oju5YrwGyy+nHLzmXRMl+Bv4jJM8Jq5IJBZ2idIf\njwhALkrK1eMnml2Mmg1OJTS6apf/I3c07wQLFvW73NLQoZcs1klXCT0G7F4v\nJVn55lx95GrFNTa42S38J3Hl8a1ItdT0ao9OMw58YpNpjnw7taF2VT8Za8p6\n4LCeQTE3G7Y8w3OrZRZuQ6XFhqHuUvZS7prT/YXzWHAny72afLC7ENW8NyLT\nUX3kP6tK9snKKVgzWBkbzSxvOQAuaXmLAYYKCmGQhaEYMf0OJYt/NTcI3TXu\nzGojCsZAxjkERHNvriebvaIYTC2Yt23NXRsmuMp5lDJZR0o6xcmnYKHmuzBC\n8M4O+oq9TiD6XKZKKmYtf28OLfZOkBNLVRoGHJRmaYjPtmapuh6whBDzWaH/\ngNOu\r\n=ItUy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-annotate-as-pure", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.8.3_1578951704667_0.45968288852138395", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-annotate-as-pure", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "f6d08acc6f70bbd59b436262553fb2e259a1a268", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-ewp3rvJEwLaHgyWGe4wQssC2vjks3E80WiUe2BpMb0KhreTjMROCbxXcEovTrbeGVdQct5VjQfrv9EgC+xMzCw==", "signatures": [{"sig": "MEUCIGZ93ufOCFHobq2fa3RYCCRIRmGFadnLHOQE0+OaMKXlAiEAjeoy45bcdFBmjYhVokD+aHNSAgT7V2nRgB5w1qenQJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSzCRA9TVsSAnZWagAA6rMQAInOWCbsSTbcx38zCLXo\nq0S1OE+8ogxHv6yZWrzPDYvcfIec6fYi3/h64PnTbTw6Igu4NSM5lS6od+mN\nLxS/1vr0D02+9ngdZb2Y1uGf9X3QX5LVWQiDcX5HPpzurPlmDraSD/UtadIw\nzRGixZd/g9QRd/NXCbdSzwAL6UW+P/CLDxROaR+rhKWFZgshsNDuKWbRr2ab\nX55h75Jcty3SEsM+AiIdVbVrefhiAtTTySnF/A7H9JNAcFlbT+6Jt1YlfsgU\nz+bDu5TD3QKaWVqs0efVV2O6+JxawX0ixhUOWb/ykdMUfCCu4E3lduOBuMaZ\nYUw+MSddvZ+tC/wfw2Ya8aNvQndTBGY9Zs95AbsssGum9jqzJuMelAYAtrJd\nnWHBo0eNafzkR4hYuJzjdZu7vKKszP45Q+qVzRMyZl/EIU5jysxKiVeB8WgB\n4eJ0In9zvYvAUSqb5iAYAxVkQ/+ZlByNuap7aG9kqHBHXe+zQffzCQxFx+Qn\nN0VxsoE2KKZnbi8sm6BAgk2oVM8JOU7mdEDUOlygpmPCkor3QlSwXvbbR2jv\nKoZn4r5skdikTGZ8g9k7/8uxJGLZW8+3vJodRYyOtJJbTaJk1/OXffw2MvsG\nm1WW3DFqIVWBtRq8mzmyI9tCvh3Kp/6FcCjYm67pdA3GUrfN2wu4sGlUbKBZ\n5Lc9\r\n=WmAZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.10.1_1590617267564_0.24027839267541795", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5bf0d495a3f757ac3bda48b5bf3b3ba309c72ba3", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-XQlqKQP4vXFB7BN8fEEerrmYvHp3fK/rBkRFz9jaJbzK0B1DSfej9Kc7ZzE8Z/OnId1jpJdNAZ3BFQjWG68rcA==", "signatures": [{"sig": "MEYCIQDJnivi27Pwy3E2eOmoWkNOW71VzoQdTrRg1Kd4xEDcAgIhAKpfTe3gonNXMaJFH0maQDHk6dy8WDqrD4zPEZLy4HP0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zovCRA9TVsSAnZWagAAXMwP/iT0qbIrZL9VqOdRnVWL\n0hHr5Wxryn9kV3Jj8GZACfKrIa5BOKfS2s7SDur8SU46vrqF7hzUIRSX4ju1\nWBq2A/zt5rmsTfmRPBCjTfHU/f4fjz/xh2XAu1hTswmcZLj+S3PlNADFWIBS\n6NNrgTpJpzW3ybL3C8uUeX9425HeMYPWrhohhsmRarXtiVdcpneN5ZLRLo+/\nZFv1csWmckdwiGhDbKqIxTQja2enkvtLgtwsuAr4letiaGKkEuoFTt2HXudh\njf61aB+IUTuvg7XlaeAu0Q4eCZj4qQuszJ8SrG4N01KNvZUgDLBnY5AhtRdW\nT13m30j+YcnA0WQiknyGLuHIaoqWNXaJHpN+85h+1js/owyuXnjyeuUygUPy\nThsv1/QKXDNKUYqT2IW0Cca5KOTdoTjBzjXp5e5a6E8RZSiEFmw5X80BTRbB\nq2VtYtBG01BgFX73xXyQrRmjuIX5HWaDdaOoapzpCLEJ7hQjNsfmbHlcPCLl\nxxevEvKjNqmBR9LZEw7pGrCkSBfJ2txk1tQSvKbQ1XWLPCesa0iwHCVYtBf4\nXS6vXlDZyptvBtxjWsClut70HXsvQiAPJib3G7oV6Jo8uNIP4LFyLN9ADeDt\nkAGRFGsKjGGz7kBHHTYVW4bJeUXzEjfLEcMcjV4vj1NOVEfmIoueLsWM/IR2\ntl2s\r\n=aR3p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.10.4_1593522735297_0.8672446321373863", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/helper-annotate-as-pure", "version": "7.12.10", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "54ab9b000e60a93644ce17b3f37d313aaf1d115d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.12.10.tgz", "fileCount": 4, "integrity": "sha512-XplmVbC1n+KY6jL8/fgLVXXUauDIB+lD5+GsQEh6F6GBF1dq1qy4DP4yXWzDKcoqXB3X58t61e85Fitoww4JVQ==", "signatures": [{"sig": "MEUCIBEKgddlSYRiIIcC9at3LOcGqGoDLpA/x9v56eUhqRsjAiEAr9uzph14RUQ4/d5mOuBT4xpqrZM/GBakkIZUGokINPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQjCRA9TVsSAnZWagAAXd4QAJuVT8ibeUJn83M4iPYy\n9+cgtBvt0X69K4Vm+w78C/XMAK6Gcra2epyc/me79Tnyp/1ES7e/dycvedBl\nK59H4nOqHpHJ1y1fQrGIc6alm/kU9yQI2SCND5FSHfp2NU4BDVN+vXg37IsN\nNfBFZFv6W+wRbvCr8AegpS86M9YnV37SgrvPaNsZ0zs2rccMV0AG8bbxJI1h\n3Zy629Ekq0IVUSNW6VtLccV9n6tqC0J6CJ9f9KfL5LOo1spUpMlSArY6z/v6\n0f9lzHhOQjPK5DK3di1qMusKy7/dLQ8EVPDxH3iYUDWJzuJuy1TWGmU+aLn+\n4DP+RzG5bqigNA8KEOmB5TSZJUYnaOhCaAcirt4Pl+cA74sK+VVQB+G5cNhc\nn9Bt9PHMF1Dv4X9pG0/Z7KPJVuoAJKD/8ntSuELLdtfzB3+OxSvD4WXg2OQC\nicuFfxa0NOzaDvbCjQ6/1cofBNSr6x0w57Bf+QRT6B+QxwoO1/xSx8xEtJ+6\nrcHB4JCBcMG2PtuWQCgqVz7GNZZk/FmAvM10cB0Uz0DNni0/Pa6J3syvFwkX\nttvlUcR8pMCG775V8AKMow+afO3oV/zBwSsAN4BOR7yGrjtFIDjCWH6TSZ1h\nFjcFmfMb3k+p53GEZ6wfElfPc5aH3pCmZJjXAkeiZay/jtSXaAV05KKMZMCg\nuxBi\r\n=iIeD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.12.10_1607554082957_0.24052654948324004", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-annotate-as-pure", "version": "7.12.13", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "0f58e86dfc4bb3b1fcd7db806570e177d439b6ab", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-7YXfX5wQ5aYM/BOlbSccHDbuXXFPxeoUmfWtz8le2yTkTZc+BxsiEnENFoi2SlmA8ewDkG2LgIMIVzzn2h8kfw==", "signatures": [{"sig": "MEUCIQDvt4qJJbdA5vUW/huZm7sMMzwOVEcEwv5y+dIEaoQY4AIgf/ZQX2zlgTh6hv9ncUtgTZ0xoTVh2M6saXHPJwxwh6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffsCRA9TVsSAnZWagAAV2kP/jZ2dRhWtEZCpyBToEDz\npdLyvrgf7kcUhYBscdr2BBgRhc55VlGgtIuBdfRsdLTdK2DouQhIajlqu3I/\n6LScawqGwDPU/a+OxjcLuqG8u8LHpAVJ11e3hConqsszR9rgzqnd1n229LTO\nYZqTYdGHtK44Y0Hf1QgvLoo6rlyXoVNEa1gPUCTAHemuFvopxyFijN0zFft0\n6MUmluvEfWXAPVkBTodIyRlAJjg9zOKv/HVY8we2aU20AQ5rif94dmcoTN1e\nUTiCQ/GrsYvePzma8yBlIuYK5K2JKPYs5oVPTVObgyV5kHchahcfbOqf0AaD\nIQVe2ZYg1TeBn8tgkFqComChHOLR0bzczHntzzUhVw7HbwNO0kD/KtWF4vJZ\npAlOt78hK7DimkZXAQ+e0ZLzpdZzLt/X5fLKkQAvyXbD1laXBRjnO1j1F66V\noR9AUmIqEO2UtMLZ5ztWmkCIJX/qLtOPVPIHlZ71sjLcfry7sq55HtB7/cTv\nIYUrIaHpkDG3h3sxMv+G+UefuW4Z31qGIrBXL3vybIa67s0BDOOjI4BeSB0C\nThkNzIcBbBwnshbRFgY0dUNQ1JLKGfNoGarhO7xP5SFl/m3gO0CPSa1NwRDP\nyP/ww0D9v8cRne+chqbZWnGCRY0NVargOMCxY4grApyAyjS59srJnj1J8Vkc\nZIgb\r\n=5XAj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.12.13_1612314603954_0.18196385557101125", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-annotate-as-pure", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "7bf478ec3b71726d56a8ca5775b046fc29879e61", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-EivH9EgBIb+G8ij1B2jAwSH36WnGvkQSEC6CkX/6v6ZFlw5fVOHvsgGF4uiEHO2GzMvunZb6tDLQEQSdrdocrA==", "signatures": [{"sig": "MEQCIB/vLZ5ZoVSShstFG1PFuzjpA0EPSmb6mwU+ABcJ1OuqAiAE3wQXmzFgO1OlCisUqFt+BaLDwATiSi1AYbCBk1sw/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrVCRA9TVsSAnZWagAAGzAP/iMSy/sP0k/qwg/oJ1gf\nHbJYwD8L8v04UuZCTbIH1iPU5iGSVi7Rgy5SNCyhJ0sRdNsD6u1yxN2x+6ee\n3ZD88AyjW3Y59kQ4cfNe4reWbYkkSYM9sytX/55j2eCjcwY3/wS3AQS1Fx1n\nMz9oKpYZ5SG85kelQFJ3G2scB9CmFWvB8cTqEPdRKXKWtr9A6G9LcJD61yIL\n5YiGpQiL622QICp+0M8frKzSndlk7jiSXQB3ju6jagI3jmePS3nNN7dNk1Aw\nOUNKlo+9earwhNXgEv6dTVduVWs+j8ZKSI22+WyKZYkoVeTTt2VtXbpATpjy\nOrQzKw1CgkOTarKL7sWBppPLbMzJjKnKqxSPeoMqEqlKaZ7ldmmcF/ufabL7\npYS15631OOHKB3NNcRksrEdgZKLJ0oGty0TI9+Q4Ij9Rt6nOLHW6yB+eNC73\niUPT/d+tA3s3/Pq+xqJ8ibhweQfwsPDwN4QEIcriA4Koev2RJLxXR1HMMmP1\nBhm5dGfiyqB1n87lAPLH89Dxjhrf3urzS/grmywpz3ZY6Omr8HercwwXj5aY\nEqgGm/yH8RAvkto6VS763PUWs7A+QECf4601ysc1SkLkaOEpv+H7TjfknMfd\nKDEcNC6LXkDs5kh9FRKmnpVZOxJQOtGuKzULkNElR8Ftz5ShAVuEIz/TG9Cz\nvXGF\r\n=sALn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.14.5_1623280341563_0.8947683566699605", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "3d0e43b00c5e49fdb6c57e421601a7a658d5f835", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-QwrtdNvUNsPCj2lfNQacsGSQvGX8ee1ttrBrcozUP2Sv/jylewBP/8QFe6ZkBsC8T/GYWonNAWJV4aRR9AL2DA==", "signatures": [{"sig": "MEQCIF9WCBoVjWJO0ivZNlLtGSKQoiz0ikvwjuBRqtQ0G9+kAiA6B16cJNYoizsH7fnrGA3yossKWNVsdwyRAbNYs4VSQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSTCRA9TVsSAnZWagAAYI0P/1oVfp6aJO13hhcOYfPq\nUH4jq71LeyV/qp9dan7oQIEXr1EQX7YVSM7/+LakyUkUyrKG+ceueJ4h/7PG\nmCRmI0fHFBeL+Rlsy5OP4Q0V9nblS1Q490z8lPIzd4sIx7TZ/ZwPs7VCG9Wl\nqg8SlZu4c6H/4j3Dv7PmTalD1bg9esybOT+z2gzydpmrTcIM9i2QqOFKdUmj\nVGD5KpXJ+8aCm1SitgO3sbm3UT5GrCHmUNFvMk9JdJvtFDbOKWinjKjtF9Pa\nx91UeUnbo6xxbvk3fDNU3zt87Io9ueZpnmP9x+fsH+yXqOXqFJZgboj/jJKB\nrdqE/2XJ1JD7oBYWaRze4qqMYRf1tDNwkePRazV9gBkion4RPqEg2otQhAk5\n1/Z1UT1nbynYbNIg7mbeTfwoVun/E2S9SCw8OEyiVpsF60dBo2Qp6+ItEoAf\n8gnYqX9SJHWXSQF3tetf/WWY8yau9fVeIyBk/rLiAhPUL9/y7Zbxfpi0vYmw\nX2SdEnjJPLUiFFFeydg5hhD/qJZvHKtx24/yPlwA268RR0AxShyDlVZnnzlO\nNGlxB42Xy+qB5RrFjnYFa8VIdAvT1B6/brEU4+Udx0uPPyTEMQoo+hPxZNjx\n3FeCwJZ6voEBmXt8iohOw0O1BO+GoGlro6KCVDXLdOt4CC7VkPQe+QyMZ7B9\nvVhG\r\n=dFcu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.15.4_1630618771256_0.4130738924627564", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-annotate-as-pure", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "9a1f0ebcda53d9a2d00108c4ceace6a5d5f1f08d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-ItmYF9vR4zA8cByDocY05o0LGUkp1zhbTQOH1NFyl5xXEqlTJQCEJjieriw+aFpxo16swMxUnUiKS7a/r4vtHg==", "signatures": [{"sig": "MEUCIEqipiR+vikP/bBD9c4djNanVBBzX/KCnlk4BwqXr3wxAiEAgVjjgnwG0rMjsgLUPnwwJgBi5uxSw/sK65ThUidYPMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDMCRA9TVsSAnZWagAAttgP/36nNMeRFuzIP0dRaXgd\n6iXK9l4TGpy6wEdWt88EzE6vLS7uue42bv18LrqqEASVARjQBTbZrMxLr9G1\njSw+41VnW55UCBDKq4JZGRzIQhPgnLkM5pO/s5Qd+990Bwr9kYCIQgToxCig\npy479ryPPqW/vbDArELg2PyFs+FEixfygJ+usPjahrspyIOakKM8kiOuYKqM\nvMi6I8LBT/o0m/sVYwaOJgwgTYl9XIfouMS3BAWdAHlUv0HbxqLX5K0SJ/W3\nxDMbDxr0ToH8il+AD5+QK8AEInBwL2ScPjJpVZvsVK1Q3d6tOoipOkLqW3Fn\n2PUNpIKBTsQ9opcL/rq9uRqrI4piGkuN2vboe2oW28+dlqoXMwbPq+XivRvP\nmcINgWXgAD0fgfP6UXNr0H0TLokE6aiEdq1BvMNQ0tqKpGyv29/JEEnP5bOI\nct2RtwaZYTaS8AjZ+zJJAG+76KeS3sRl7Lxq9XN4JkE4mHFLUUDHbi9srBaR\nEmBerVd8Jw4eKbe6caqHHABZtwrGpsqDQw+hNTL5/1hV1pG/WUZXsfMjs2Pw\nqfwUR33fsRr2dNytizHd2fRlqIGjzmjjpiZyYAZHljycZKEXNTT+35nZECo4\n9NQs6naJkqS1t1suvb4+4oRbsGyqRTzvgTQpqd2dhN5RjiRcqmWvcQ9brs25\nc4st\r\n=Rv6q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.16.0_1635551260811_0.4204011950576574", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-annotate-as-pure", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "bb2339a7534a9c128e3102024c60760a3a7f3862", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw==", "signatures": [{"sig": "MEUCIBvQfWo7v+pF9kQOj2To0bI/dJPKqDdCTIhENawNkbKlAiEAx5VNKN2BZzCi0SQfcf/7V9USaPl7bV5h4G3vlIkP09c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk05CRA9TVsSAnZWagAAo/YP/2cRpSm9flutJjJ9w/6t\nGIYJ9ZD8TwE6hlaGwQS7395PdhcNdjJpbA41TuyuVG7R1mt/VTrIeHUdmkyK\nL+Z2aqOPzbLYtP4Uo/1nm56mD5+sAzlNa8n0xAyXoobAXB4SvgCFhXZzzkgP\nszHlb0XRSa/1kJ35wb9t16TIVJuddTGZolTwCdD/GdGwrmgtwmjXTlygphdo\nNCt58UYe8SluqXOY6OWCLxuU05QiQeXODdW0UaR7wMWCtu3IvDP/ukEoboMg\nc0DuIK4j2aFJ4zZUKnEezV1BisPRqh/5kz2EGUlm7AJiZKdbt6ZNIqI2u4w9\n8gD9ew8+WbFEGMaNo3pqbPXf2zzd8nmH9Q5IO7GxIUQhbKMPFZZlJ/M+CkkC\ncJlQN7coWWSWlUrqagd9/+OK4gkWcG4puW5UHLONhnspw4PZxar3/UK99H18\nyykQUJJXhmF80J+pMEAZ7Mu1+aM1JqJicZbLxoP3ES5Mx5wcFaJyKFvA8mbj\ntFiOwaSPX8TMiczeOFMez5Wscx74A3xTH7S25k65T419QdU60DTE/ivo/Z/n\nYrvQUiAhO5NnidptwSk0v/zLgQJRdY+WMMF2TeQClkqBrmlCMeeRasUK37kT\nuPf/BofOIGz6ehgUn0pStxlWW8ci3rNz3Fk9AfMOdqsxVZjkCAhRh77oVqxs\nnXQC\r\n=xS4G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.16.7_1640910137490_0.33849507088073927", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-annotate-as-pure", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "eaa49f6f80d5a33f9a5dd2276e6d6e451be0a6bb", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==", "signatures": [{"sig": "MEUCIEKnGBeFXkwIUbmZHghSxZPQ+zLwRiouEMH0Rq5qDVRwAiEA36iAIY6nNGL58BK+GU+zE2rvFqsEQq6kOD61S6KE9TI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFURAAi78jgVbf53U6NfitGS98lxWTbWGF9wGgR1Md7Nl8YeIKnGSV\r\nbGxe65ZEl+BXnW1R0B/QAZHxoCWXKRwWm4oCeZQSxvX3tC5m2TxcXAQX934K\r\nLNW+TxWSyGW/z0mxr3Cth8GY0kRbcGek4RRTMDv3TWfnRGGKN68sAYvZzl+y\r\nrRKZFybBp5Na1ggLasrjxcuzhxKiAu1fSVg89IVVh6lJd2APqFtx+cg+GuVb\r\nQV0E0nuU+3FGWY8MizqjNulfNYBuygPIxc+JPo4UD6UFpUg8eo6xxCj9Uxpr\r\nmjjD5zHCG/1VW9EVhPai6baMgEsylbRsBLhpzcmTNI+CR9ikuIbFRtSfVP7k\r\na/yIdf9M0yFbJ17FVyPeXjyBYc+WaUJfwGP5bdIsNfNK6J9OsbqCnfyvwQ2B\r\nmMtxOm999M7E8tr1LW4T6keD+5+26Q675gAIkoWs6U4Hjn3IuQJ+0ee1q1GB\r\nFEJJvq2KJHIrCnuW8pg6ZWnPlOBquuhL7oQhJEWJiXSiVj7K42sRjDZkoeym\r\nbuUdSC1j1QHcurE6YZAOUc9rF+bdTDaupFAB8ZyyWyJwl8EqSou1Tf16Fzu2\r\nbMvwaqtiTbdUzwNrOZpHUVrOe3Of+scMYwTRzZgKB2kQIse4NUs2UvnJhZPF\r\nVlWb98AzGYyj6pgdJOk2OBOMiQzizoeiu8Y=\r\n=JH/v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.18.6_1656359407536_0.16288818216081546", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-annotate-as-pure", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "4bd94ae0ccb902f4038bd58ff10e722433987011", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-Aa9fe2sFNrYNwODkBWQQnxojhQ8LeI+JvB4GZU5DoXje0DcEol3VpRg538967yhy4DL2HPlrxKFD+2hRp8ORZg==", "signatures": [{"sig": "MEUCIQCBjYjAb+8fhpfYxAQFgj31Xk612IUFqzWF+Dhn5IJdqgIgf2YT4VmPxfvpyg1yfFvSm2Av2cwNoJ7OgQig1DeFxhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8GA//UZErbNgOqiSl6Ue0T2uO77HBRsIsEtuwD9v1uVDH2mg80eLg\r\nF/QOQ9sOB7CvYcciitGN/Et3F4Td8PpSFv08TkIVtJ8D1Xi6RDt4j3xAVLQg\r\nXnwnoSDJQh53bp0sH6NveOlHq2v6m4OLY+xgSnK0qj5clcIb01zNX6u0FwqT\r\n28uZAmzC7k440GwBD9x3uJyUJBPmFb1pjF6GjFAtcJi7So4kPCm6EnS0Sz2k\r\nW5vk5DhRC5nfABFeQjfa9LGdaYMKMVHCZlE5KNHu9URgzf77hU5/j85xiDh+\r\nl+OdGcW6oLh25aye9lH4FDGFADW4ORcYoFOqs48HApZZwtwLKlkgeii6pn/N\r\n2oYnu1NF+GnK29A1HVLKiuqZ43I8VbpcXTFhw4fr0fgPctlmf3vvsU/y+lwj\r\nWQ6+OZzvZjIZ4fMHhsyxMlbcMX7PTEGlAFAl08Hbexjyr2M4ovQPwbsg4Yah\r\n2OQ29vKnYLwrSgXR8b0273E0huf/WGARHKftW+dEbjLc7eBFRS6/tzw3q3Pl\r\ncRYBmF3YRuL8YPtwP4hwG7wm4QA922j/e9M+dZWJrR3/ae1UYpDWXxkexEgj\r\nZsa1+EZ05seEdbwlluqD5mjtUM+L5sf/THFL5Wfx2734LZ1QCaFKJMwOBwgU\r\nVUuVHD3wRDDHJHXS8MsthWhCOewx9zsPY3M=\r\n=xLVx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.21.4-esm_1680617373863_0.7576663573809577", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-annotate-as-pure", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "807eec09ae62c3d71c938b0ebfcb0828a49a28b6", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-+VxzOAfwRDVq6qdL5cc5uD/o8h6NVFMZ/DyIDSVibJCNBG2aNHxaJIYsHpo422P7OO7lZburAC06ziB0GWRFqQ==", "signatures": [{"sig": "MEUCIHmcDLenO7kPIlsD37dCwvGweOa41jKlwvpHdbuOr+SNAiEAoWdoUSwLYedcQZuVStXF614xMJo3ZHZteCmxmmfwxq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4qA//dFJAMD7NkxRMgZTzQ+Pnc2MZhiYqZjE7Y5NgFZ07gvU+U+E4\r\n7P9Z6ijYXwTpGIWu81hQzyC2u5xrqDWiDaEQBLTXC51ds+UjJHQWe6aWmqEK\r\nBdpv51j1RnOSD00ffvf35bPFutDT8Uly29dfgXr4jKbST4N33kdYHsuqhkxF\r\nxP8eGW4hIWmyxMsjw0UVJRAKT2n1oghGIPFBuPSTCMWHOt3rbuKP73Gr63jC\r\nr5cIQGeOpJUIci9n+MVY0tfkexHKLuCjRjh2U4kV8SZ5Wv0t8kW3d5L8xuOz\r\ntlYKd+OYi+PZyuvsQum5ww5a4iG4Q0iRmxeHrazsxl9nr3/ikKxuvTbiotI+\r\nctAh+MIGoOMBkL+ENEmUjDFiMThh0zwjnbq33ijyCUS4QBa8hRvecG/rFtjS\r\nHNhMfQH+Jw4JhUx88cQJaWRPvoibHBSeGOjKgIwhf7UA7rMi8QLRjexiwPzo\r\nEudA4m1QZerJe2TH+2/QeR9XoiQTExtjE3c/XmoYiCEMb3jwwz8qJqXJCF32\r\nwNcD0/h7R8Li5TTbMT4CsVaJQBLlqMpnqyEeFLpiLDxgup6RQdz84AaKj1dC\r\nn6Kkf1NNWPg78uHv7Vzganc3nKNpbJGeTM0oSf41YgbIh1AyJE+gWn3zpjVZ\r\nIPKaD3ToHKBhZw5gc3j9nstMEmBH3fwQGJU=\r\n=SYWS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.21.4-esm.1_1680618086000_0.07245848015439704", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-annotate-as-pure", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "902e9f6cce5945b79f0e3652ea6e5c6a0e88ebae", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-ZZ21n96gQ5WrImFzm6m8nYh0Qqj7mvI7eRRoPpTQSmrjR5kKOL2gUhgr5Y5xBpcVZCIQIfnhBe6fZbjFeuF2CQ==", "signatures": [{"sig": "MEUCIQDWpd0+w2b6VRQ2Jq718H6YAAD0UgGm2wxsmxrepulZWAIgYBn3Ybhnv1pmfPjHkSjVVgvptmruwf1dSZFR7HSPgis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDafACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwbQ//emXEc1Zr1fFDiIQjffI8SUcQG53EdsFWb07KmKO782D6lCK6\r\n1eA9xKsKtPsxQXXFsmQMwDC+nfbpSePGHQ3mORtM1nYKBNYcP7SitYZV6GL5\r\nAf8onOasjbY6NM7j0LFKJ5F8Lolsg7cnvPH7SAQTbOUbiHKWnAgMhRlln3pH\r\nDP3gekWfGH5qRphP5vOWFHH0BwTpPgQHThzO6ixrokAyk3UXqGYZAwgIqR5M\r\ntq2BDi40bbWO09cGTNw4a5TZWyf6OjOM1VC9i96LWDTDiHRN7xrfzD08j11V\r\n1Mi3mtGXhDHyVhGJoDg6Arbe45bsfxcB5Q7SjplRgSOnwU1wl8lBg8lbx5//\r\n+JUJdT+rELjDnFTprePinZKU8dRYoBvkFxxkYgkctEE9YUZC0s/jHHNOwzZS\r\nMBsi2gH1KjhBODjg3vJZbXl7+tFGUSUHoj8/vPAFp8CZGnscE1n9FO+KsrqX\r\nSrYdl9vDhj+z8w5SA42rTHaE0dq7fHSURVqXE2mzSZLNiplZYV5JgwmFDu9j\r\nvXvvFF69HbVFiJbEQsnf+O3ab39C22k72OFHLEvniTjJZk8k4bd3tqPGPJ6B\r\nelmxO2R1IfoU7V5Ip3pjeqAfG5ENMfyZiA62GQ+729D3MRkSgdMWQxau29Ze\r\nx65WXr5MGsq6so7vz0v56cX/lfxrIBBB3YI=\r\n=Ti9E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.21.4-esm.2_1680619167106_0.45166696549737817", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-annotate-as-pure", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "e27137b9301889c4f880be6819b7abc55e555d1d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-llXJFFp1EJMdd24AwOr7HXfZqC+34I4zUm7hZfDHCiLmU44Plp2+z1IrXctFIwSQBnkwqAVzQQWKd+9fo6RboQ==", "signatures": [{"sig": "MEUCICG3qD7dsmKL2h1AcorXt93VObRbeA7lrEmXT5mj4UehAiEAyZtLvSt4bKnWFTQ49Imnb+GuujlkC3Nm6GOTLtUzP2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6Tg//QJ3VhXm4kPghv3TllKnaAiy0mTa+TBu2HmZMHFIYjQ68VGbn\r\nVZAvB8pSZRts+zrbQ6hdrJDnT0OS59TwaOmtpY/MEbktbN0XLHZZ3EiDjNui\r\nEaevP0j+46QbE4AM6pBwHqRKmTQK/JILP82iWdo9JfS2ybTlfoc8fBdT++0/\r\n1B3PVitG3OexYqugtLm0lBe6PWgda8AmF85T/eWbpM3F/A7khzMW7wK9TDBE\r\nd41AJyBNMT7dwePafHiEkNCyduljksE3z56Z/2sonyr84Z6iMRvWrsTnX6Ey\r\nKyrMQhOSNjChQCxpfnSnJdWal7M3GHp3P2fcL9q7jEvFf4LlU9iAWaS05Ox6\r\nZYKtWZPajX4RT8Og61DWP32pxQJqlVoiHLQ5PMD0wnXzA5G7A6G9uxo3kb/H\r\niOJW9JPZKCQqF/glSrC1M/AIMPyzB+KR+bU+n0eTrsbZaOP/WBD6zkELa8CV\r\npwPLw+FUquNbsE3QQKun6GhbEUNtqGr5AKxoIuXW38mUYB7tD8N3uGfe+od7\r\nLFfY0NX/eOr6V2uUTVLbuk+pTGxdCSTtNAQi9prabVTYB4bBAA6ktlDl+isD\r\nkw84Ihn7IVyeXDKGrYtJlfqFXWN4bg9li96GvcbZEoBRbV53wAEgYnQpFaXd\r\nSs1FpHQP6Dt3/9aMn8P881mIkoraw/z5+aU=\r\n=WO5E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.21.4-esm.3_1680620175893_0.22019802755938467", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-annotate-as-pure", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "691c5554eb3acf54159d865afc3ea04dd6971a77", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-gsU9hFx4XDf7dgnDd5IOxUJMPcQlLpbLuLHmjKrD2CSZIwJHvI5Te2bDN8K4iencMBV5xeBFr3/tGfvkQN6rpg==", "signatures": [{"sig": "MEUCID3vrjZqXdmAq0Hh6nOzSVuy9h6E0oWAcwZ+aYhB9CLxAiEA1hmUOSReMqIyZ9QLDRjgpcCvtQCG4/fhzzweQvZem94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0qxAAjP3Wjm86Ew3jXSXAc2rj6syIkVe6NCnK2mwq1rRyndm42Rel\r\nEcnKWsDdjkus1vvcEs5hj3ik+vZFyT4qc4ow2j4jGb1bMqtFfxVQXTrJ1Xnl\r\nRnhKdW6YgORPUFm1XUc4+JaLDWiSp30eo6IqGp1ytdVJ/+VtmFpEMtU0m+Uq\r\nV7r/4/Mq3yBjsmd5/thUBgih7gtl4LIm5vkJX0sv3IJAyNQ5L56r23YwCVPl\r\nCamzTTRgMWx9bv++0DBHQ7y/nu1iDqABrukZF4+MK2SNC/agovKc1fKEEzBU\r\ngG1miVwLrDa8jqKNRhtRBYLnDOsSgXt8eSbtDnOehB0qXnGyzigxYKWFNNt9\r\nHwtVOaXPPCpnYwtQHvtXKtitgx3rNB7zAvIcqS7zOgL72TnCLbOLiDRQ0Ojr\r\nSOWqUvhdBbGKzWD4GG+nYq7kODnsGOKqTOyu76orTj3Xa2tLrpsYOfC44CGT\r\nh6wegLQkXyJmrN8Oaz3JM+6P1IfrIfOtPy+YYfZymIifs3uBrvS3abp9Wvw0\r\n9YxUIPsjozW8dzkWBcmJpGim+fXicg3yatAKNnFvPZmAtUGaeP/3XSw6dDPv\r\nF/DNDJZqiEMubI32F8XAM9bBnj5PGx9nme4udT3pJAnimZEsyNo2kLNHF3l7\r\nijuJlspFjDGnwVh87fGPvNrMkqzNH76bINY=\r\n=NcLX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.21.4-esm.4_1680621207592_0.08628119270845769", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-annotate-as-pure", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "e7f06737b197d580a01edf75d97e2c8be99d3882", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==", "signatures": [{"sig": "MEUCIDFmtDlmYPEOWZV7vMz+DhuYQH2s14oTFkbIsYSp+YWwAiEA71DN1JVoB9g4OkiKB7ajtbDqUBycDyVD22mR7B9Mh1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4018}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.22.5_1686248485187_0.4217321985268765", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "169799fd16fda847e7e6134d040281cc1b8ff78b", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-HNWP4QUWPCcPE8DVutGccJJhWs3FICZB+HXrhP0sPhZld1/UmG/w77Km6IFpOehuSzrEfNdmBQB7Z8ekLFnXDQ==", "signatures": [{"sig": "MEQCIF9OVCl1ucBNPeNV5/gDD0DMBt08XHH/8j52wKdgeIIcAiAdMZKuCTsZw6dUIMfHQcHKM2s+IxIcuVE9xiXygpj9aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.0_1689861601916_0.5611701968299563", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "3b2da499567bc652dd86d449efd36237e33ba71c", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-4zNSl4cL2KVx/D/xlD8HFZzEfjaeMMtng4up2YiZ3ywlC049K+2PNRFwww9kPbGe2K4ArCEn3JC+tyE8eLzfWw==", "signatures": [{"sig": "MEQCIHZZcwdVy10hs2DLENElbiem9CUh9en7M35NV7D+Z9s0AiAJ9ozp1QrjzdBRwMlWGPXjU8KnhVHKFdyyoPurDBGDbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.1_1690221128475_0.6689216090946857", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "d889c6047295cd1b79be9cf3f04fea5f3df4bac8", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-lmzusgwf276uWpXMbxRldjtmh+jpES14wU8LaEY4z+ssI0EH8kHXgzqpAW8xXJVm/eeHsv+YDHDgLsYeku/6/w==", "signatures": [{"sig": "MEUCIQDjJPtEd4p/SM1YEonqAf0omvjEr5uyBJ7y/rqUcjfsLQIgSFP0DQ9rtYZqDwwA3TI21Q8yEAdzi2ePmAizlu937Os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.2_1691594101722_0.4383014361228299", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "80224eda34282534e3efb80280255968ede9a243", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-ccROOWoLJ1+cvc0ej5SzTek3pLsFRo7UbIff6FMXsVBcMxvpxEWQ49BLUWx8+BC8TOZfIigr3+EcIJCPtTpUrg==", "signatures": [{"sig": "MEYCIQCS+xWCGJT8Zbzmtt08eY5w1oKzShM6nhhXy1w+nOMZvgIhAIhPgGeVeEgQRG5VWszYUV9clL6kc5Aw6RmICabUTeVN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.3_1695740225388_0.9660855671999367", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "26c282d4ecc6ec115f0acf87cd0877ccd286940d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-5xwfiVRQVYY/ATKR67uKFnFU0Mw6ixjBUBvn2eM3fwKJk4OVAMUbYFwtBtso5KqHdvD04ElXttHqamMUkOfAzg==", "signatures": [{"sig": "MEYCIQDbkcZm6xen6J0Ih8NISWDIWtNxR0R1TvgIc7c7xnlvdgIhAIoKrx+XsiCfrh5g0tY75W1fvBXkst5RDzGT7nYyNrUI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.4_1697076385317_0.44555646643858204", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "a06475032c8e43387a6f4206c28a231eb5277834", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-XfVBh9ce2GDsMYmxYR8u6yx8eeTL/OeiiZsXEsnn2RJCuEe7XNbUA96Y7wpOqgBDjSVQquzuV5qR3EyMzNkmvA==", "signatures": [{"sig": "MEYCIQDc36O2ok3RjHfNl6ilEZfIyAVcFWT+Wag4jMoTs0WYFwIhAO7tgPl4URB0zpgrhsndfKvoV3nCWUf67Mot0D+8wHQ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.5_1702307943408_0.32692449800934864", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "cc5901acaee5350a56dc856af782bad2054c013b", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-byEkBV+aDarVTVwKvN9I+5wTdSAHFK0avcs8ZMgIO3XRWfQjo9Gp3bpBuBjQxi2X+AJXdHyIv2SkKyfrLK09rQ==", "signatures": [{"sig": "MEUCIEsBsN0yptlqjt8FCKVkNufp/LnDKC7/syi6InJtVKFtAiEAgM8APORWLe/eGdUowPVPdIk/OKDxXoBinl4h8pmiwJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.6_1706285654350_0.5352272489845649", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "42b626540350664762cb4895dcec213264372537", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-MYLegTBovqhxZtWXqG7dvORhS+SM/pg3lDxOvBSCBY6iY29S3qIHDzYMpO7KiCOETakswwAxa78JJENooNzb3A==", "signatures": [{"sig": "MEQCID9RVg+ajZYOkv95K/bTcNM7NHvdv/G68ch2khXPluBWAiBjUhysq8DZsoUKpqoLAGUeKHXF6L76qPWwKMExdV2bvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.7_1709129103408_0.398688238196965", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "8a37aa614431dce96bc826e481078b1caa9bf8a4", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Qr7HvOSETOMwfyfmOFjSUCkAdkpK8hof5b07BdR0nMdPjQZKPgg2OM8HVS+GF6n8W71JbyuJAYX3vhKEVBvPgA==", "signatures": [{"sig": "MEUCIQDY4+6hsiHulV74Kxbc93MwpSAF9etTOaGut6bkPk+QrAIgMzvVBF7HD3NrB06+1/PwFzR8uDYENZKw15/6iOVa7eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4136}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.8_1712236796466_0.27127028788628404", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-annotate-as-pure", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "517af93abc77924f9b2514c407bbef527fb8938d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-DitEzDfOMnd13kZnDqns1ccmftwJTS9DMkyn9pYTxulS7bZxUxpMly3Nf23QQ6NwA4UB8lAqjbqWtyvElEMAkg==", "signatures": [{"sig": "MEUCIQC4GhAVmWLG2CxFmHWK07UaOzPpzWT8xtENubS7WNCAiwIgSTNn7fRTC5DXY0Ql5j8AseoKNpUOuN7gUIksEycBPOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56579}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.24.6_1716553478672_0.39573428314739134", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "6ead564c379ac600d4a85a68ad37a6b6a7a90e66", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-UuTIiqdsBoh+IxUXBH+UK06BR6PaQi3ZeU9RyRtjIzF4IY/Ztp39dapDXSwd9J+wvA8iXVqDTLdWjfkX0CzPcg==", "signatures": [{"sig": "MEYCIQCPvX4Z/QJor1Y2++pS+MfzbR3G2cbexFID8sAJALsoagIhAKQzx9Woolg6alLvasnwerA0uBK7+ZmzA2FGk0qhIqCY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52739}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.9_1717423464865_0.3953987897838429", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "f61048da49865d99f5dd5ffebddba98578a7f30a", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-cwj/lNe6mofcwguY59LflfN+6BCUy+kh8YJCL2efM+XvqCES4b1lJ+v3wZ4tIfZybuILAWdSIsNUwpTgyaa9FQ==", "signatures": [{"sig": "MEUCIFT5qrV+daAmhrFfFbOPKyG60szNFk8J45yUUoMNuQbNAiEA0P7fsrsU40nEh/JAAr0zTpW9E59XouLlfft0WHKVjcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52742}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.10_1717500014778_0.43436676587980405", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-annotate-as-pure", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "5373c7bc8366b12a033b4be1ac13a206c6656aab", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==", "signatures": [{"sig": "MEUCIQCs4etVdXeb76NvqSLR1BrXKslNVCRQfeufcZU7BCBG7wIgNJXyu/2w4S/4CyLPZMw+PakgVoaplRbtZ8bHrqIe2QQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52411}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.24.7_1717593328693_0.23224728746852175", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "24003cb3d239f9b23564cfcf8587a031b83b5944", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-lUrwdLIxHbLLL/0tT1EwYFi+pPmuIFehf6Omo8xeBHSat+9zI9ez/j7Oawff8Ctr8mSDkw4ZAElGo9lHkVdOwQ==", "signatures": [{"sig": "MEUCIQDRlxz/HNcLQ8nz2C9ktewcybxeU0c/uUY602UulCUtmQIgVP1eQoNyCKvst855zEF5dsXrkfLEaX6UDejfHO2kzmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52631}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.11_1717751739147_0.5469588501368601", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "954de63b0fc6c346c96b167adbc3ef80f0b54fbe", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-Mh8M+JxDbuEyJ8L2G5XLGDhhFR4Yt//IrIeGb3QYV60vBm9gn9mTJHSRqlYEWI2uVh/PKmIvPVPZOSikN+g5cA==", "signatures": [{"sig": "MEUCICCpgc/nnbYqU2VqlQHECJY2xp6nB3bHB52J7gakT12WAiEAsYwIrQmz/cfx0j0fLjl6AJUb0rq1JheJN1LiivFsGbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50765}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.12_1722015215620_0.09340131333426327", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-annotate-as-pure", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "63f02dbfa1f7cb75a9bdb832f300582f30bb8972", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-4xwU8StnqnlIhhioZf1tqnVWeQ9pvH/ujS8hRfw/WOza+/a+1qv69BWNy+oY231maTCWgKWhfBU7kDpsds6zAA==", "signatures": [{"sig": "MEUCIQD/NYXINhvF86sjQ9nlmUoVd5mDovsC13uEG2ndSq+V4gIgSCCDPs2XaEEEyZpiakEc4OfuRaptna351RMNscG41Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58012}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.25.7_1727882097525_0.5561728560355821", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-annotate-as-pure", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "d8eac4d2dc0d7b6e11fa6e535332e0d3184f06b4", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==", "signatures": [{"sig": "MEQCIBZYJMuYJe49Ig/FQFfUU2X3AIy3eT3w+ILYNhfwEKBoAiA4uQziUQaiz9iYF75EymBFMftHwfqzzjxrgDqCYcjaJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4054}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.25.9_1729610475927_0.2996421426332223", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "02a843d8fb2149c7f591ec1dde709b914dd9b7c8", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-1BVYCcXya37obJ3FX8EzgJfN+6SqB2xy7JESE5NGA1Zpw1TTHP/2Br6IwznIh7EL+HVVGoGg6QL89ci4wEocuw==", "signatures": [{"sig": "MEQCIDdmpgFUVEytRmz0ALJj4ftdNzZUnW4801zoetK33zOlAiAdtxdHaljhI/z9MTc01tHJYuUXfdVpNw2KZasp8xZHWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4385}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.13_1729864458447_0.3928335650279311", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "2b188a5d91a9ecd78a09dd1db8174d13c610304a", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-LqAvJjy/ThiwIUHU4LIxpPAtK0cLD8N04Ja7Cmo44Z8ccisrYuqopixqKjWdnhLC3ZBpmjFr8MjHhhUZ78P9Eg==", "signatures": [{"sig": "MEYCIQCBSEsFGapSK7KZg/vU/MPrPViuO+BSluWhDpO89hNVYQIhAIbTZb/Ai6LpPysEajti3CyeO3adrwFd07/ez0vl1El6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4385}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.14_1733504049093_0.7981820476641261", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "712ee54eafae738f20765ea9362709493b5ebb3c", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-VsHmE5RI5vYMdtPB4/1VvHD2x4aqF/U25C20dDGSV6fuYScGSOF316fhwRYnoxMOIBZfkdudYWKHmxy799J6/A==", "signatures": [{"sig": "MEUCIQDCcIh43Hq90g/UN+mIIzBN09dlfPacfuSflOiRzqyhqgIgcsh6fbM3wTL8VMX+13/wMMd+6d8aNQrqjKxM5s2zfug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4385}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.15_1736529875994_0.24978887350160628", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "3ce227cf07d131dc4d191cfc29ca3eb368f07cbc", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-xy4EjQmtnpZiRI1GuLQOaRCRALjbjyiJawx13Dw80BjI5XmLbjXIDLqD3sd5UJDpsG5nVEerJMeSjCtBSrDRnA==", "signatures": [{"sig": "MEUCIQDnEKmdIaTCXOp9V554b7e5OdOVAFt1Z1SJXsk52p1xTwIgFksJk2se3ObzrDfX5xdTbL+CQ1ee76N3lcqDUAJwu/c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4385}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.16_1739534351590_0.30718831987066597", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "db236d721ce59540d459013a842fa1ca7a0f71c1", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-SYQ7jDYezmHsRF54vIXLU+hsP7u6YaynZ8hvM0svHpf8oN0BpSgn96zhMhaGbruNHHaitcIjctTkqyiFSBazpg==", "signatures": [{"sig": "MEYCIQCHblO1objpkx+/TfqpfMzw79DNUONNb46YJd8M4nIyEQIhAIJWC/pmz+41Te2TGuTFdhlZxvuWCmHhwk4eo3lAw0to", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4385}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-alpha.17_1741717504992_0.5245875366315997", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-annotate-as-pure", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "4345d81a9a46a6486e24d069469f13e60445c05d", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-WnuuDILl9oOBbKnb4L+DyODx7iC47XfzmNCpTttFsSp6hTG7XZxu60+4IO+2/hPfcGOoKbFiwoI/+zwARbNQow==", "signatures": [{"sig": "MEUCIQCThX7gd3p+MYaydzC5p+e6YGhsJsbGHxrdwq08B05KywIgNOu4zFGONEsth7qFzvpooCsOrmloKpM8083e+rxl1HY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4054}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.27.1_1746025741612_0.12034063490643154", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/helper-annotate-as-pure", "version": "7.27.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "f31fd86b915fc4daf1f3ac6976c59be7084ed9c5", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "fileCount": 5, "integrity": "sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==", "signatures": [{"sig": "MEUCIQDedRC5A6nJsmBHmIy2NAuQelt7SGLbcb5HwvXQ5DjdgQIgMDSVV5rty+dMX5G9ZeQkbq98dKsuk1O3OE/LB9jSnH8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4042}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^7.27.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^7.27.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_7.27.3_1748335159429_0.8722699161491123", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-annotate-as-pure@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "dist": {"shasum": "322a5472a04490ba0e993a6cac478b791887ed3e", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-aIHcG86CAETilZN7Lb4J3LaBnJVRf+xQr8TIkOkbFZgyTiKejd/49isonHwFL7a7Zi6et49Q168Sgq7DIcI2Iw==", "signatures": [{"sig": "MEYCIQDYMq/L+/icsdr4ktBiZzbF+37neL8gv2DqYiP+y817nAIhAINStFAownW7Yb1zk0rqIjrUR6IAkAiQQEWyAl2Znr1t", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4356}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/traverse": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-annotate-as-pure_8.0.0-beta.0_1748620273324_0.38387351811953896", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-annotate-as-pure", "version": "8.0.0-beta.1", "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-annotate-as-pure"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^8.0.0-beta.1"}, "devDependencies": {"@babel/traverse": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-annotate-as-pure@8.0.0-beta.1", "dist": {"shasum": "60208d6d63162fb55ee5ec4ef8058f2d00331e6e", "integrity": "sha512-UdL0+iZSj7Wx3/8REsWrT5iSCtQu0bmFLmCgYSix4lpdtlGbW4ltmRCXevQt494Ua1t8TlcYT7twbgvS6z81cA==", "tarball": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4356, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFPRTDt2JJIbcnx4QWVBbC/i1aqiNLXQlAHtOdmi93xtAiEAvBpWJbgaoi+Kj9q8U64ngTUMU8p+6jovuxUus7V7Ws8="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-annotate-as-pure_8.0.0-beta.1_1751447064175_0.03396311611110914"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:01.689Z", "modified": "2025-07-02T09:04:24.657Z", "7.0.0-beta.4": "2017-10-30T18:35:01.689Z", "7.0.0-beta.5": "2017-10-30T20:56:40.792Z", "7.0.0-beta.31": "2017-11-03T20:03:39.374Z", "7.0.0-beta.32": "2017-11-12T13:33:31.731Z", "7.0.0-beta.33": "2017-12-01T14:28:38.935Z", "7.0.0-beta.34": "2017-12-02T14:39:38.665Z", "7.0.0-beta.35": "2017-12-14T21:48:00.329Z", "7.0.0-beta.36": "2017-12-25T19:04:58.454Z", "7.0.0-beta.37": "2018-01-08T16:02:41.739Z", "7.0.0-beta.38": "2018-01-17T16:32:09.798Z", "7.0.0-beta.39": "2018-01-30T20:27:44.171Z", "7.0.0-beta.40": "2018-02-12T16:41:53.847Z", "7.0.0-beta.41": "2018-03-14T16:25:42.030Z", "7.0.0-beta.42": "2018-03-15T20:50:16.435Z", "7.0.0-beta.43": "2018-04-02T16:48:06.979Z", "7.0.0-beta.44": "2018-04-02T22:19:48.927Z", "7.0.0-beta.45": "2018-04-23T01:55:43.232Z", "7.0.0-beta.46": "2018-04-23T04:30:08.308Z", "7.0.0-beta.47": "2018-05-15T00:07:34.989Z", "7.0.0-beta.48": "2018-05-24T19:20:54.619Z", "7.0.0-beta.49": "2018-05-25T16:00:37.547Z", "7.0.0-beta.50": "2018-06-12T19:46:49.404Z", "7.0.0-beta.51": "2018-06-12T21:19:15.775Z", "7.0.0-beta.52": "2018-07-06T00:59:12.007Z", "7.0.0-beta.53": "2018-07-11T13:40:01.395Z", "7.0.0-beta.54": "2018-07-16T17:59:51.773Z", "7.0.0-beta.55": "2018-07-28T22:06:55.600Z", "7.0.0-beta.56": "2018-08-04T01:03:30.888Z", "7.0.0-rc.0": "2018-08-09T15:56:49.016Z", "7.0.0-rc.1": "2018-08-09T20:06:39.577Z", "7.0.0-rc.2": "2018-08-21T19:22:42.929Z", "7.0.0-rc.3": "2018-08-24T18:06:39.013Z", "7.0.0-rc.4": "2018-08-27T16:42:49.277Z", "7.0.0": "2018-08-27T21:42:00.065Z", "7.7.0": "2019-11-05T10:53:28.975Z", "7.7.4": "2019-11-22T23:33:14.852Z", "7.8.0": "2020-01-12T00:16:51.358Z", "7.8.3": "2020-01-13T21:41:44.910Z", "7.10.1": "2020-05-27T22:07:47.697Z", "7.10.4": "2020-06-30T13:12:15.403Z", "7.12.10": "2020-12-09T22:48:03.106Z", "7.12.13": "2021-02-03T01:10:04.086Z", "7.14.5": "2021-06-09T23:12:21.737Z", "7.15.4": "2021-09-02T21:39:31.422Z", "7.16.0": "2021-10-29T23:47:40.970Z", "7.16.7": "2021-12-31T00:22:17.637Z", "7.18.6": "2022-06-27T19:50:07.665Z", "7.21.4-esm": "2023-04-04T14:09:34.019Z", "7.21.4-esm.1": "2023-04-04T14:21:26.200Z", "7.21.4-esm.2": "2023-04-04T14:39:27.325Z", "7.21.4-esm.3": "2023-04-04T14:56:16.035Z", "7.21.4-esm.4": "2023-04-04T15:13:27.771Z", "7.22.5": "2023-06-08T18:21:25.355Z", "8.0.0-alpha.0": "2023-07-20T14:00:02.137Z", "8.0.0-alpha.1": "2023-07-24T17:52:08.685Z", "8.0.0-alpha.2": "2023-08-09T15:15:01.967Z", "8.0.0-alpha.3": "2023-09-26T14:57:05.654Z", "8.0.0-alpha.4": "2023-10-12T02:06:25.572Z", "8.0.0-alpha.5": "2023-12-11T15:19:03.629Z", "8.0.0-alpha.6": "2024-01-26T16:14:14.584Z", "8.0.0-alpha.7": "2024-02-28T14:05:03.533Z", "8.0.0-alpha.8": "2024-04-04T13:19:56.644Z", "7.24.6": "2024-05-24T12:24:38.968Z", "8.0.0-alpha.9": "2024-06-03T14:04:25.092Z", "8.0.0-alpha.10": "2024-06-04T11:20:14.964Z", "7.24.7": "2024-06-05T13:15:28.943Z", "8.0.0-alpha.11": "2024-06-07T09:15:39.325Z", "8.0.0-alpha.12": "2024-07-26T17:33:35.808Z", "7.25.7": "2024-10-02T15:14:57.810Z", "7.25.9": "2024-10-22T15:21:16.285Z", "8.0.0-alpha.13": "2024-10-25T13:54:18.655Z", "8.0.0-alpha.14": "2024-12-06T16:54:09.308Z", "8.0.0-alpha.15": "2025-01-10T17:24:36.159Z", "8.0.0-alpha.16": "2025-02-14T11:59:11.785Z", "8.0.0-alpha.17": "2025-03-11T18:25:05.159Z", "7.27.1": "2025-04-30T15:09:01.820Z", "7.27.3": "2025-05-27T08:39:19.581Z", "8.0.0-beta.0": "2025-05-30T15:51:13.499Z", "8.0.0-beta.1": "2025-07-02T09:04:24.436Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-annotate-as-pure"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}