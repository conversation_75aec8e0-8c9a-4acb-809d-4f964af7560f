{"_id": "postcss-js", "_rev": "25-611fd41b5a85cb2048979b5d1c71e8fd", "name": "postcss-js", "description": "PostCSS for CSS-in-JS and styles in JS objects", "dist-tags": {"latest": "4.0.1"}, "versions": {"0.1.0": {"name": "postcss-js", "version": "0.1.0", "description": "PostCSS for React Inline Styles, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.0", "postcss": "^5.0.10"}, "devDependencies": {"eslint": "1.6.0", "mocha": "2.3.3", "chai": "3.3.0"}, "scripts": {"test": "eslint *.js test/*.js && mocha"}, "gitHead": "943777eaf2e31ba02acf6f82fb1b22bb25687619", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.1.0", "_shasum": "7219cb1f84c3f835a2a337f67c983c4798e1d5ae", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "7219cb1f84c3f835a2a337f67c983c4798e1d5ae", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.1.0.tgz", "integrity": "sha512-iuP+vXXHFHcn+ix/aARuDOfse1jpWr5xvu7PnJzLhVXXZ4UPT+hfrxfkhHPQV3oVTFfG9wPKQbAswZFvfnOpAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDukqdaNqkmlIV+cN7+UVIvmUkSj0QhQvmYBEWVQ70NTAIgfV5k+UsQXZpLUy1cSqaDavxcICBLpX98hWijGP6b1so="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "postcss-js", "version": "0.1.1", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.0", "postcss": "^5.0.12"}, "devDependencies": {"eslint": "1.10.3", "ava": "0.7.0"}, "scripts": {"test": "ava && eslint *.js test/*.js"}, "gitHead": "1859d70ab346f53766a89b36efc3eac921b429d2", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.1.1", "_shasum": "22635ecb7908c84767c32cb6918c2f6cba2d0a1f", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "22635ecb7908c84767c32cb6918c2f6cba2d0a1f", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.1.1.tgz", "integrity": "sha512-o3l3RuKQjvOFWGCO4NPvO3qM4tYfZafSHveutqw0ZMZIDkDmjDOfmY/P1CMlKo5R7ouyTbpDdlBppY2KlOrh0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDsqNJEnWWd6Ad1hAI+RV75xzzFaZUZDjwzWxLCAaLtcAiBbItXleBadakekgmPpJfcqcS/cn7WQITjWLBNwqK4+YQ=="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "postcss-js", "version": "0.1.2", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^5.0.17"}, "devDependencies": {"eslint-config-postcss": "2.0.0", "eslint": "2.2.0", "ava": "0.12.0"}, "scripts": {"test": "ava && eslint *.js test/*.js"}, "eslintConfig": {"extends": "eslint-config-postcss/es5"}, "gitHead": "59b903b794487b10f098f1a5762990b871bc722a", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.1.2", "_shasum": "1c104771505874bc92d999535b2970e534729360", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "1c104771505874bc92d999535b2970e534729360", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.1.2.tgz", "integrity": "sha512-ifVB33/FNHvBquHKCp8Lmq/Eb/nE0fUH2aRzfdACNLXEEPKaPTN+DWnaY07/BaD3TlznRIqXdrV7ockyEjUjbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDxmuTAnoTE9pBm6gWISctLV9XGMep/YXAUQm1G0vE2gIhAKwY+GVJoy2Un/4qtpk+NwWumWiD32+EtycOhcasfzaP"}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/postcss-js-0.1.2.tgz_1456641916637_0.9618430256377906"}, "directories": {}}, "0.1.3": {"name": "postcss-js", "version": "0.1.3", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^5.0.21"}, "devDependencies": {"eslint-config-postcss": "2.0.2", "eslint": "2.10.0", "ava": "0.14.0"}, "scripts": {"test": "ava && eslint *.js test/*.js"}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"quote-props": 0}}, "gitHead": "0fc94c077148dfc71ae26beddc3168c57530c1bf", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.1.3", "_shasum": "8c0a5daa1c918b3073c4806a3c5b332c67250c03", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "8c0a5daa1c918b3073c4806a3c5b332c67250c03", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.1.3.tgz", "integrity": "sha512-/GCOrP7JuvZ/n0An7YXX8DSeXHfkDNxQ1Xiq3NGx/5dAeRkB27dWbya/GJWY7L0Rq9lDI4gVFz60uKFf5mTSvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0F2ImBXZK6rnSjVK1OSGy3FVBRbgQ+v3wBD3DCP65wAiBMu0Z+4NAT3nhj0Y2n4d1ADGAc59YsgCFo9gMHBIhiGg=="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/postcss-js-0.1.3.tgz_1463240006413_0.25465655070729554"}, "directories": {}}, "0.2.0": {"name": "postcss-js", "version": "0.2.0", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^5.2.6"}, "devDependencies": {"eslint": "^3.12.2", "eslint-config-postcss": "^2.0.2", "jest": "^18.0.0", "lint-staged": "^3.2.4", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js"}, "lint-staged": {"*.js": "eslint"}, "pre-commit": ["lint-staged"], "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"quote-props": "off"}, "env": {"jest": true}}, "gitHead": "a40971fd53c53b2116efb984ef27f2692679670d", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.2.0", "_shasum": "56e6db0cd910a6dffec3dfb34462693ac72e3882", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.3.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "56e6db0cd910a6dffec3dfb34462693ac72e3882", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.2.0.tgz", "integrity": "sha512-qSLm9VGcd42LKUMef//ssPL9RdEOEuH9CxuDziHD1rjeQqOksz17UFAw3AiU98/vWPAeSHZLl/phpfo7O9oBlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMUG2HHDz00SbrYJwVKP3KOH9I4BazhQRAAulvOOYh5AiEAhosNxCIhdx5mpKIFqQv/BlQsxAm4i9A3m0+0JTQcz2E="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/postcss-js-0.2.0.tgz_1482491653221_0.4251765194348991"}, "directories": {}}, "0.3.0": {"name": "postcss-js", "version": "0.3.0", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^5.2.14"}, "devDependencies": {"eslint": "^3.15.0", "eslint-config-postcss": "^2.0.2", "jest": "^18.1.0", "lint-staged": "^3.3.1", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js"}, "lint-staged": {"*.js": "eslint"}, "pre-commit": ["lint-staged"], "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"no-use-before-define": "off", "quote-props": "off"}, "env": {"jest": true}}, "gitHead": "552b2b33e5212b8e60b7c66558978508205cc7c6", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@0.3.0", "_shasum": "a5b690e24e7697d94eb293104da0f8e5cc10adf7", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "a5b690e24e7697d94eb293104da0f8e5cc10adf7", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-0.3.0.tgz", "integrity": "sha512-Sr6rxpa8Ku+3pHLd5T35WTlOGA4sXjTH6/D0/Trx+vtnPJqAPkyaqub46v9wUU7cEGyjT+NyE4BguitwcjVUTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHVA9NduLZ5AuCPsR5Z4NHlmTGgSqJw+Snwx7IN+YlagIhALkQwqttGY4msmKMcB8hWURv5P0GSxpo4dofn5U7eVsv"}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/postcss-js-0.3.0.tgz_1487605276422_0.4156566867604852"}, "directories": {}}, "1.0.0": {"name": "postcss-js", "version": "1.0.0", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^6.0.1"}, "devDependencies": {"eslint": "^3.19.0", "eslint-config-postcss": "^2.0.2", "jest": "^20.0.0", "lint-staged": "^3.4.1", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js"}, "lint-staged": {"*.js": "eslint"}, "pre-commit": ["lint-staged"], "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"no-use-before-define": "off", "quote-props": "off"}, "env": {"jest": true}}, "gitHead": "b619fa15478cb2f64f4f886e19dec62bf272c213", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@1.0.0", "_shasum": "ccee5aa3b1970dd457008e79438165f66919ba30", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"shasum": "ccee5aa3b1970dd457008e79438165f66919ba30", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-1.0.0.tgz", "integrity": "sha512-nb7Dv1abopcvcLlqvGDf2RjqzWXzKJBlu/OWQew1kO2Ro4jcVyCTswigSCwcsbzAgxsFgPaRjMdY8FCNelLNew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICPiwSPWytEZ4RQbEbBed03oB/iE0VxFUDtO2zVBAFWGAiEA7CwJhoXVo2xjghxzMsllQF0UPkXgbcE1qDiOXkkn2LU="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/postcss-js-1.0.0.tgz_1494255222528_0.4495049307588488"}, "directories": {}}, "1.0.1": {"name": "postcss-js", "version": "1.0.1", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^1.0.1", "postcss": "^6.0.11"}, "devDependencies": {"eslint": "^4.6.1", "eslint-config-postcss": "^2.0.2", "jest": "^21.0.2", "lint-staged": "^4.1.3", "pre-commit": "^1.2.2"}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js"}, "lint-staged": {"*.js": "eslint"}, "pre-commit": ["lint-staged"], "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"no-use-before-define": "off", "quote-props": "off"}, "env": {"jest": true}}, "gitHead": "dea1e865dad643e34c4a8d5f449812a3d2f79066", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@1.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-smhUUMF5o5W1ZCQSyh5A3lNOXFLdNrxqyhWbLsGolZH2AgVmlyhxhYbIixfsdKE6r1vG5i7O40DPcvEvE1mvjw==", "shasum": "ffaf29226e399ea74b5dce02cab1729d7addbc7b", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-1.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVlMM1vtw7sjC4kfb2htvo6GsDDakITQX3DbSsd3167AiEA3obDNzZwPpjr/E67VrD1Qb0Gn+Fo8yhMxoxTaF81I/8="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js-1.0.1.tgz_1505282710813_0.798162859166041"}, "directories": {}}, "2.0.0": {"name": "postcss-js", "version": "2.0.0", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^2.0.0", "postcss": "^7.0.0"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9kAApW9G5kN8FkQ0ZdvSmbgbHIRrKmXtde2ZWYbwrW51gfEWfGsLlUu57mTpioPrmQlQFOgEvaeGYp+poqlX0A==", "shasum": "f75b70470009eb37f998ee9bb516a2899c19ef8d", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-2.0.0.tgz", "fileCount": 10, "unpackedSize": 9765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTSitCRA9TVsSAnZWagAAxNkP/jQk2fplw1EtExzqC1h4\nsBisbiVokTPL0eUu7dGW22TptveQxcwI1m9s/P9kIyFxfGY3jgOaZaBQPhV5\nXLjgQ7QNIG31gZ/ZAsIRsDvtBP+3yWS6rWd6WWgXhOm8RpGQOyTZ5cQYKttw\n+Xsg0CUyCZ2v1iQqE3rgwyeIFHs9sj/LZTawWRm2tveaEhgiKCswK1szIATq\n8UFCYU5kMoS5TR4a9+JSbpgm5NYblV2u5MfRlIWq00gCaqcULWwdJpqjZ5pp\nUnXbsaDvzZWh7BlHipLHh6gsdLbqNXM+6eBor1k2GQRzQnvLN7c5WahtSd9a\nlgKerZlbBvxL1oiLL1bJvxZOz9kzKTNa0tTf3yHPWo54YEiIrIe2NMxOOWvh\nuK8ui2Y3lYEC+MFCO++ffTf7+FW9EI9f+lLNPwPpM1uIuQJQ47mDzlJDdm8Z\noETh238WX0eSs0CENRmMdw+acUqZOMMFboxypPtitsMb58v5PcKlcfr2U+A/\n3PcIuOgoDq3RyrD1v3OSKlhYy57vtyKWDZPwurd4VhJwp42WRrSNvECrtTgw\naodyVJ8Bce7FwU/u2FfI/dS/+iQ04Kl5Yeu8X4IZrJF/QHbDgtr4F52pnbql\nJJR42vv4jryLBd31PrpUFMTJbid3L8k3bBCT0RN21CDqC2PfOUiW482gLtJg\niJtC\r\n=vp6C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZFBoF1E+mU94SS0Q7M7vbDm+Nl1xlvwgX+V3D7OPhEQIhAIdSpPtcqR5S4poCMrnf7IZSxlpcL7j4M5HTlFz5uaAY"}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_2.0.0_1531783341871_0.6442203464721141"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "postcss-js", "version": "2.0.1", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^7.0.14"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@2.0.1", "_nodeVersion": "11.13.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-8XQGohCbj6+kq8e3w6WlexkGaSjb5S8zoXnH49eB8JC6+qN2kQW+ib6fTjRgCpRRN9eeFOhMlD0NDjThW1DCBg==", "shasum": "4154e906ff410930afab63a24210be1b831e89a9", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-2.0.1.tgz", "fileCount": 10, "unpackedSize": 9793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp+ioCRA9TVsSAnZWagAALFIP/0XIhjJVI+Sa5uqhr14N\nntDFpHS4np/8QuHfiZUGeCr7p/np0IC1ZRrCgsjkFPadpqiDidu7PIFhECMs\nWh59I8dB/jY2wnvo7Fhsmd2Yzb9ah+lvXpPVt6cNfUde+CyhndBY6mMiZ5Wu\nI5iYSh8o8cgjEqYTayQo8dAbkFSqKoj/Uas/INPdzS69cIlSWWTNG51zgEiH\nMAO7sPcLcs++rAGgPXjycCJs93qNoxM1+UMaAMLXRA5cdsKhPZG1PxuE3zNS\nERJ4kyMO+BdT7h96otpQH+RtkgBL14nmUt1jtV1g3WycVv3xSkAd4dz3AFnT\nL9AyKeAxsZHy3pxlWRk0gSUm4oJDC98hYb/7eOqGbd3Z/KEo1ugC0nms1CW0\nnbMLaSt6Zs8Xf2XYDTrlHM/H4rPi7Wd8zHxmdliFGEQOp4K/18jDcGbSPQuI\nbA4qCTL3gTssM+woWIVTrV+q1ypnlwhV+3BgmikpCtey6mt2R4xStBN6ApQi\nErG35qGDhEcRN2kXq56ggmyTv7tVxOVShzLS716WVYs2pDFaZYCATZp0U2I6\n9rZ9fC1dyH9vhtC/0CsA1l/d05FV3g0kqobaC01Cu9u4QaCG/vCZNW2S7FbX\n6i0/Ns6veLRB4pg8i53pAcmQi7jPs4LPDphfFJzcItK6+KGtBsGRJetBGNUF\nbH/E\r\n=j3Tf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNwofTE45JNhLjyLs+yF2eLbTEm960ye/dp8bbtjG3oAIhAOmHlYgf+i8a6jK700N6wZ/z52T8Y+YTw5MMXQa4CvwI"}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_2.0.1_1554507943184_0.50827206603218"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "postcss-js", "version": "2.0.2", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^7.0.17"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@2.0.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-HxXLw1lrczsbVXxyC+t/VIfje9ZeZhkkXE8KpFa3MEKfp2FyHDv29JShYY9eLhYrhLyWWHNIuwkktTfLXu2otw==", "shasum": "a5e75d3fb9d85b28e1d2bd57956c115665ea8542", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-2.0.2.tgz", "fileCount": 10, "unpackedSize": 10052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHrb2CRA9TVsSAnZWagAA1woP/Ay0VK12Grr5N+nB3dH2\n6dt8E980+/jkuEQni6K/J2WOfoZ0VUjFrvpqDqcEv49I0e649rB2+wadJOfQ\naekCt9MiaBiz6+F8cElAACQoe3u7/QgDfScHvyrtFzrM/+vxTwWVK+nP+D5m\nnzL9jguI4I2na03AydFRGskdZ3jfFI7SBgeeJrkqF2JHWtMzzQcXMF69h+UO\nTeKPWAIRt9y8f2vI2eHy1NwhZBkqOS42canKIl7ulf2uyAaVRrX8AVO2QIvw\nmY/yJ0o6A0Z7h8WFBb0dPO5SRZCOFfhCWn90j8u1n0jZlUSk9KOfMjlr1OLd\nK34p8i11wrCQ4ylEHcry9lG2K6Pv/skm+N5X9L3TfUUh027le6LACQ50ZO33\n6raB9FOAPPBZec+hILQV4yIu7WhU+prz6FDRqUFYtjHi6TV9VfsDQMC38DGl\n2szVQ0b1jMHznIkZ6kn5XMT4veZOcoX4/IxI/vAm7Am4Pr6JpwzeKdb3XGmJ\nGCp96S21sRwsgcq0/0EcxemhqCKkcX78AOHRLz5T1xjsQV43VsTO/34NDsqs\ngAFuaPQ+POF2EGCBC67By6l2jLhbuoKNGlk0Akl6TOwISTepmQWvubwbqnXl\n7mG2OZ7IVEuo4u83rI88x1rPPllb1ws6eOcefnqpPr4Bghi+3yZiesqJqRL/\ngwPp\r\n=t5+z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxSM6Ch8i42S6VpTKW7rh3j6OuJezTAoZjfl6zq3qPjAiEA2sY8lSnV93VKcN/RitRNx3b2/fpXRJdd9yCyXfPjyKU="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_2.0.2_1562294005329_0.28481989907449723"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "postcss-js", "version": "2.0.3", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^7.0.18"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@2.0.3", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-zS59pAk3deu6dVHyrGqmC3oDXBdNdajk4k1RyxeVXCrcEDBUBHoIhE4QTsmhxgzXxsaqFDAkUZfmMa5f/N/79w==", "shasum": "a96f0f23ff3d08cec7dc5b11bf11c5f8077cdab9", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-2.0.3.tgz", "fileCount": 10, "unpackedSize": 10141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddL3ECRA9TVsSAnZWagAA2UIP/2WpGjgDXsjlF9cm1eJf\n+hR8LsBg1mvfqvE5Z/Jid5fDsrzwdx1cisMPQa5rBIntKc1jH44qyhO1XOmj\n2HPkhHk2wWbgI4XeU8CBSdjf0PlK0hHj/wOZrcUWjylt9jXemdbKq2mHEDG1\nluVsfs6jS1Z6tBry9rpc5xso8p57FDshG1FlQHhvkA7SFWe7AouWdGMPRhtV\naia8iithOui0A+CzzpiiuxIi2Sa5KwNlaQ4yOc1H0oP6EKEmhaC+IyNN8OdR\nldF9+LF1IVRk55x9txu+yYEzFA7MHA0wIE72Xgzb648JivWyD2IS1XWgKm21\n3fUtJtz7UUBZFfZFw4XgJHtMFKmBhdP3/cs9aFK7TfFuZ3K+dPhjxG9z76YD\nAD3bE+x7/ReBBIt6KIdYEvQEsnSP7TSYsBvJoVVE7mnK5HvsU6LqeAXG6y23\ngiokM8hJM+h56Td2vH3qm7C+AMwyeioUXlnXK6JK2jD6hAMbNWYJD8SAQ+yE\nm5MUVo2UWXXoAZcycy+ubQXUagIAnNXgIIYTSiHede7utJPUoWZr2CxQR4//\nprV1WfmL+Ihlm44myMyyhlgO1okHA9K1NSL7a2LvkZggd0SPafGxZO/KuL3M\nVIjxKvuwpogzSE/iZO2WA1MK/6tqBjWVUwi3rlKP1COMFGCOkkFSINPqBmsc\neMHm\r\n=7bIN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFOMAWeVje+FIdYpqsXEExEuAcqkKdCVNYvwm6vpOWNZAiAbr0dqKhURxL3eSOrSPH9u5y9YJNniqIJIS12CQAPZ8Q=="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_2.0.3_1567931844047_0.7256776451286187"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "postcss-js", "version": "3.0.0", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^8.0.2"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@3.0.0", "_nodeVersion": "14.10.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-1WsYJF27Qib1Jvu826qp43xxDB4h6XWuYa8rvXIqLY3WiAfDImvvw2LLu6fxw0UTJBpnuDMdpBkYt+R0Rvtrbg==", "shasum": "84f410b4cf45dd30a3c0a6da1726a2898d955479", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-3.0.0.tgz", "fileCount": 11, "unpackedSize": 11438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYPVRCRA9TVsSAnZWagAA/A4P/1Dcla/Bnkl+ouI+GNuZ\nL2DwtJQTnaxX5oU7GkwVicvWfmrbOuSyco7AXMWt+kPuhuBmUepdM3kcCJ9S\n0LNbkAfjHCGDKb5c1bVqqvEu5/kJlNcSdVQHeDoYGNlAAerTn79Duh7BTS2v\n0JpcFoh612i4Zn7ikz8dAQ7/ZFOW7kFM3xQVXmWLq/b6i7NbgiYZFtkpbOHb\nYttPuicY6nSiMbGc3lnTEmUwb65/YANdqQ/XJw7wIbX9d35ZPJr6ZJ9VQupL\nQOuc8PIBpk2oX71+/U8/lhDemRBgxRjiFGZfIBIwcgsBNQEPDux0J/cU3VQK\na25OQYrHnRMyrbKZHimcF7FARJbd57dQ/GqANOxk2gfCmnvHB8waqG6/7Dj0\nUepIZcbbg/0Y2vWBh35BEZOi832FRU1r9stU0zWu8hbJDvglv5rPaQ+fBLsV\nTIOmgKcYncIsfNIn+vLK4QEOMHWAwCkrUWOVLFufid6amKblufxfL8XdJcnW\nArVnOcGWI0IeM0LtplDAf3X8hRo1UG69/SWlld4diWc3WWKtQlWcJBqTLFEB\nb7ZXB8FFYhLTnSqB9herUKwd1oFCyZZBClzYXYE79l4OmFISKCRTBf9zScnT\n0tQhwCNAkcdzzz+fORddTCXi+3tjXlMCaXlRjtKwVePMCk21TbdGMKyCZGdI\nDDjI\r\n=hJ57\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8GY+Vn3osm9AHDqW31azCP2X9O8A45yYnMhOPhyA41wIhAIQycCAYonxBcONyUux/lj11PQVDzr1U2ckZ1KEMQde8"}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_3.0.0_1600189777320_0.8904391292868727"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "postcss-js", "version": "3.0.1", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^8.1.0"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@3.0.1", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-m1DgECmEbOK9JhGkdctaP9ZRVheJuEnkk2eb/d3K+5uN10C3S004Ng6Hat4Aha7PsLt824x0xwrT7rVwGRVLHg==", "shasum": "e467efdce80ca02e072c60b42e0b35ad2f950a94", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-3.0.1.tgz", "fileCount": 11, "unpackedSize": 11570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb9o/CRA9TVsSAnZWagAA0MgQAIe2PZ+nBfEcNUGVLPKj\nbs7xskAz35fwvfQ3JhjK7NpTJP3z33NAyZDyqrxWM8USarMy1b9BYRopMCPN\n0heeUfAHgPU25O/neJsc/iFh6Bxay4LRqCedEN4e08XTKQ1lGD+rc0MmD5r1\nNfybcxB5vHQGQ5HLW9k0KPJVsODFGKYTx5+Uxh/+R5qahvcdM2NrgrDfI+3N\n0ALZ3q3TNUbtS0h+qUZOKGKzQegTfn258Z2m6Q51g7YlpXOpUdZiM47e748l\n9pRpA61ddfss7SKKhVpEBHq/nRhU9GOdDSQuUiIaDJsIje6z7GmrXjJlK+KJ\n9ylxqjZVrxVlq8bemiu86Q6O7rBH9O29btnYuzniyf/rhCZkm3NcyTulYE3P\nN3TivQN0pyGjE6WP10dh+KjIRFhSoMqnLkF4lJrSc6QdYgUVKqHMM2UqrPpN\n04vG7BZDi5/FkJe179njvnC6ZLPwYiZ6hpAq2of3ay74InCk6nGcAwxULf5S\niFglfgRTTQG/pZ9eZUYp3WjXWlgZcuvIk10rWPZZ0VRAxxBUOZWs3tTkElS8\nheMhpWvo7hz/VLOWVR3GuMIaAWIo5XN4Xq0DTjJpXuLWC4UZiTZKgTRoRwxH\nV2MnsQlymX8bJ6Uo0/Ri1cNcIJgr3kua3JQh+EcA1InJvGroTr8H15o+TDWv\n7Uwm\r\n=LUuo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFuEIckfmH9lM2fF9sLGmj3d8WbIMd7Fb3ZBTh7gRYzqAiEAnS3o2p4a5F3JJzj0XLCl9U8uG6XlBGmzt9OyCH+tY8o="}]}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_3.0.1_1601165886774_0.30436916924454294"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "postcss-js", "version": "3.0.2", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./*": "./*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^8.1.5"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@3.0.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-JcCj3f26PLRCjmNrsbvd9BtoanvKSXhB0+Mkhfw/w5tzspsbwnEZonxi0FXaTBr5CmncfrLCQGpIX0p0Zhhnvg==", "shasum": "6ff82b17ad14c180cf8b16e2fb9b35959aaf9712", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-3.0.2.tgz", "fileCount": 11, "unpackedSize": 11610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpCMnCRA9TVsSAnZWagAALOMP/15WeODfu1RMMbo0zR4V\nS2HA3La3Ozw0bRL4gTIVHvmOgJFlLkoOqx8xHVB6K7Zn5ZtDNsxB7fsr4i3U\n8GmNtGRqJRDRQIHE60fuswdutjVMOtK50XrZdwj40PGC1Bs3ASO2SyY0AijA\nlOe4gilcM6IP6Acr/R7uEmmDe5CDIrgzFEJCZGkdg7XMcvsc2KGfLF3TIaP5\nrpci1ed8dEIvuBfMvWAVnPzq70yN4asXNBCFrzVdbPo60TkawCtRyvekXR0f\n0BdxKBBDiczRw5DjVDM+hBIIYb1q91iMB17zzRT9Z0QOO5DKXQVaxwDnkgiK\nzrDpIAiBxc1mhZaUUCuLvIWEjStosIHTIE/lRiPZxk8Ki4CCAkJ1fWTu3hZf\nCaWaSUWZfrBOhpFEvPH3SSlEBkmmWAGiMsordmrAQ00WOd1KsWrUTBeY3afS\njsZTV6C5FRe2NuocAt7BCWxBOO3iHEMjVWFlAxQOSnbpQEYArNiacLt3XQLZ\n/wPZLH3g9U2x6FpwSshrYfgamfIu8Fw4/fwT1RuszifzNK0HQEdRzb/FtSK5\nMBr544xEjNwUVoyCfvRFWwn6c2kf4LampxuV79OBFDn9MBtLKKLJOki1SpuF\nIFkCMdFB1v6uzBYvLcSjk+lDEU1039sSjs6zs42X5jTg8wxT9t+4akfSEVGf\n2IG3\r\n=mJAc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnWvi1Wsk/v5M1slZMonaYYnQOX6jbpoU7l1208JHTcgIgBq2bV+9Onx5d4IupXkjfy5hsBI0S+IYkDEPe7z5sFdA="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_3.0.2_1604592423406_0.7580131168204876"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "postcss-js", "version": "3.0.3", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^8.1.6"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@3.0.3", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-gWnoWQXKFw65Hk/mi2+WTQTHdPD5UJdDXZmX073EY/B3BWnYjO4F4t0VneTCnCGQ5E5GsCdMkzPaTXwl3r5dJw==", "shasum": "2f0bd370a2e8599d45439f6970403b5873abda33", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-3.0.3.tgz", "fileCount": 11, "unpackedSize": 11663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpCoXCRA9TVsSAnZWagAAmh8P/0HoEolE40ADX1CEpsQ3\nYUA5L8c+TcmJPEsgYp/XhZVwxLPKXdtoZDB0JLNcPxLQgRSFKxE9NtNWQTCD\nv4gNORUmwDYLhYF5mg7WQyo4ZXzRzuxsnM5WI+zKRZE61RDO4Tu+pKXp7Uyi\nGzna2Sbk3ElecoWpbLS1eBwjM+cNDmPbwL4AqCILoODzT8W+QJdxiDz1n7G6\nI2TlwR4eyOmMPiQRdboi/gqYl6tfVriKU6Ob026P+tUF5lqRESDLTJZRQZIv\nBJnvf6Tht4K2E2t/bVGJSa8O2OxSnlt5eOtMd5giaEj1W/v3G0ASXpl5MN0i\n/hDyHqrk4I6Sk+M2ssDomdZC93kzBHCXc3SNwR2lvV0CVnZ0QfPy3KjQF/3B\npdUnnPFw0eGuo3SlcFg+H4+1cjS/k92Bb0AH0zCmrMs8PxCYTPUovAID6p/S\ngg4EVbkqsm0+Z/8ml9qjPYGEj81FIi5Fp/GkibWMPqvcq3iyrZAy9DgPSrGT\nYafqXHoHKW99PsNHqjiCa3q2SobTf+sEdAZg1IZROdOl9Tik/iEFZWPC7aWU\nWu8GU4b5Jig5dspBaZt5rLQxWEXNOEYFxYmoVD7MkflLwmr7OsgS35eJh6a4\nu/C2ebFZTSjI8zDB+epI4FXoJJXjKW/iUxecbPlE5SIOn/SHVkZOpyjdxBOM\n2efL\r\n=e8Jb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF2U7l9CGB+X7QGDuDBvC5AxJoJFSZNNfr99NzOG6EsyAiBsHjItW3xIBDaqcATTje1JlRctIje/m4ugrhYOiE3v0A=="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_3.0.3_1604594199100_0.6225125961324411"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "postcss-js", "version": "4.0.0", "description": "PostCSS for CSS-in-JS and styles in JS objects", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": "^12 || ^14 || >= 16"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./package.json": "./package.json", "./async": "./async.js", "./objectifier": "./objectifier.js", "./parser": "./parser.js", "./process-result": "./process-result.js", "./sync": "./sync.js"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.3.3"}, "dependencies": {"camelcase-css": "^2.0.1"}, "gitHead": "06f853088cf51d902ea9897ba2041d23e1cdfb08", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@4.0.0", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-77QESFBwgX4irogGVPgQ5s07vLvFqWr228qZY+w6lW599cRlK/HmnlivnnVUxkjHnCu4J16PDMHcH+e+2HbvTQ==", "shasum": "31db79889531b80dc7bc9b0ad283e418dce0ac00", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.0.tgz", "fileCount": 10, "unpackedSize": 8359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1RTFCRA9TVsSAnZWagAAhBkP/1pFquFsRYjvR+goOw+q\nJ6hz0nkMXkSUvZbopSZ84wRDP7KGHm5Mvb+AbKsgV5XZ7kLF5EOGhXvTCMlV\nMykdjuZbR8tI/eWhs4JIOu+YHEzy/GO8FNjxmRVzH1CoZPLqyNUM844XtMiG\njn7mYLBZNU2QmlBLmoOe8AzI7FGgobAyTJe9STQi7khBeDQEgxMZSx1a1ucN\nau1Yn4cpphXFdBw3t5wzZtSmRSqOckCjllgQJYN7z7wbrN43QgkqfQPvmbyp\nxWdIAg1UkuD8fIAJ4Orj9IJWiL8GDPvrZkyd4gZ4VyTP6UKDhA9G523uFdDC\noOe7atrLJyNPH/ofP8/FFk4TLJXYkRMXHfGm4qhkpsHxQrxa9Ys+8IXs3LBr\njmBx/ax7g1EwKhmhxEFoAVRHax2WBQUCLQglSM4Qs5xSKyd5A6lUNRvVXzDu\nXwwNt8R6ST61pY1OoMommCpQvowD6KNw3u2vrnKZM5l82IidM7sQsSb2bnY7\nC007F1xJlBANLTaLSXi/y9+oOJTuvAo1rfnzytHpxXIb4ti4IGCYbbUL4UR4\nYLrP2FbQVGT3Em17d9GzrwSzGgKUs7AngcVj0PRPhUYPVg7XHvLV6zHViulF\nxvULRixq5ugLm2f8DMtusg3keLMl6zQdqOFxYgI2hQ4vtJsQdP/n1+MazIDH\nuZfw\r\n=cUpY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC0ss1D3FgkK/qEl7tcNDKrB6PaZOapYFxtcJv8U5KCsAiBojNpbRgsKxCTCHe/LPeljP2VZcZRgkSwPciSK9UEu0A=="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_4.0.0_1641354437516_0.030068758734072665"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "postcss-js", "version": "4.0.1", "description": "PostCSS for CSS-in-JS and styles in JS objects", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "engines": {"node": "^12 || ^14 || >= 16"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./package.json": "./package.json", "./async": "./async.js", "./objectifier": "./objectifier.js", "./parser": "./parser.js", "./process-result": "./process-result.js", "./sync": "./sync.js"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}, "dependencies": {"camelcase-css": "^2.0.1"}, "gitHead": "358b83b41d050bfd07293bc90023d8b1ea8916d3", "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "homepage": "https://github.com/postcss/postcss-js#readme", "_id": "postcss-js@4.0.1", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==", "shasum": "61598186f3703bab052f1c4f7d805f3991bee9d2", "tarball": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz", "fileCount": 10, "unpackedSize": 8449, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH8yW/gx3YoKQ2sGDsxkDBxWb896dIL7Apwk3j669gglAiApmAg2Q3eE/OuJHPBsEfRUnng37jDkDIH/I2Xsmn4b1A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj47vSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKSw//THnTKwfJtNHPD0OqhZB3l0DuAxkm0QBotulfTXVH1B7riGKp\r\nfXDJ7DhxEtAHwj/mR02WvzkMmcdgvhIb2rsfFsRoqsbnUWPPhTrc7KW+yVGf\r\n8b40PnMgDSO9Gp1bu9aBEyiVs0oU6yDLXZaaPComF+tD7v9lfYmzRmfluCAU\r\nix4DVSdxGOERe+KQobZYeha9DmVJ23vF3DTHuq0n4rXw0bwgLv0vlB1EzNj/\r\nRoCMHXXEU4a9ugp8ZkCILqa3QU+V4/pYnr49PWsHphOV1GI0ZlBQAqVpvYSL\r\ny0zotB/3iK/hGNIftXNK2IjP43kMNFl+j5zg0Lto8XzwkVpeTW1oZwxbujBf\r\nMGcjaYWI3Q2hlVvBo0mEmIicXfAIJe+Fkx6+PNFsWTxH+vz5L8pOxB8axqvT\r\nakzh0sPfgtdJYqLsw62isZnRECFV2pgfetgPfrIvjoLyVRllfGMGbSKBlZPt\r\nNCiqdEqlAMmdNyuE92ph1wla0pW3wdRBQ3dihrq9xV4sVIQjF4FiKu2EJweG\r\nnOSRX9BcWdZVbbDZWAeDFpdQneMLi5910fhTpNzv2dWu9qXSPVi61hmFZOjK\r\nKJhxiBZL39Vhd2NQt06pigspbk/Kbkgh7eilZZSa1sFdUcU1t9euHFs2TkGg\r\nalYuz/3eQSqPPq8VaNZOKa8autpZQ1EPhNQ=\r\n=gN9q\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/postcss-js_4.0.1_1675869138147_0.5755130705467986"}, "_hasShrinkwrap": false}}, "readme": "# PostCSS JS\n\n<img align=\"right\" width=\"135\" height=\"95\"\n     title=\"Philoso<PERSON>’s stone, logo of PostCSS\"\n     src=\"https://postcss.org/logo-leftp.svg\">\n\n[PostCSS] for CSS-in-JS and styles in JS objects.\n\nFor example, to use [Stylelint] or [RTLCSS] plugins in your workflow.\n\n<a href=\"https://evilmartians.com/?utm_source=postcss-js\">\n  <img src=\"https://evilmartians.com/badges/sponsored-by-evil-martians.svg\"\n       alt=\"Sponsored by Evil Martians\" width=\"236\" height=\"54\">\n</a>\n\n[Stylelint]: https://github.com/stylelint/stylelint\n[PostCSS]:   https://github.com/postcss/postcss\n[RTLCSS]:    https://github.com/MohammadYounes/rtlcss\n\n\n## Docs\nRead full docs **[here](https://github.com/postcss/postcss-js#readme)**.\n", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "time": {"modified": "2023-02-08T15:12:18.403Z", "created": "2015-10-15T00:45:26.625Z", "0.1.0": "2015-10-15T00:45:26.625Z", "0.1.1": "2015-12-12T06:27:26.704Z", "0.1.2": "2016-02-28T06:45:17.968Z", "0.1.3": "2016-05-14T15:33:28.515Z", "0.2.0": "2016-12-23T11:14:13.453Z", "0.3.0": "2017-02-20T15:41:18.481Z", "1.0.0": "2017-05-08T14:53:44.479Z", "1.0.1": "2017-09-13T06:05:11.011Z", "2.0.0": "2018-07-16T23:22:21.937Z", "2.0.1": "2019-04-05T23:45:43.309Z", "2.0.2": "2019-07-05T02:33:25.483Z", "2.0.3": "2019-09-08T08:37:24.294Z", "3.0.0": "2020-09-15T17:09:37.460Z", "3.0.1": "2020-09-27T00:18:06.940Z", "3.0.2": "2020-11-05T16:07:03.530Z", "3.0.3": "2020-11-05T16:36:39.276Z", "4.0.0": "2022-01-05T03:47:17.688Z", "4.0.1": "2023-02-08T15:12:18.331Z"}, "homepage": "https://github.com/postcss/postcss-js#readme", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-js.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/postcss/postcss-js/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"scotchulous": true, "zuojiang": true}}