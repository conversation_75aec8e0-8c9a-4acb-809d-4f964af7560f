{"_id": "@babel/plugin-transform-async-generator-functions", "_rev": "44-641251458023d7fc454f353b89c9294c", "name": "@babel/plugin-transform-async-generator-functions", "dist-tags": {"next": "8.0.0-beta.1", "latest": "7.28.0"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "437bd57599c7f1c93945272426d650d907ea313f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.0.tgz", "fileCount": 8, "integrity": "sha512-SLpCXbF08XTYRJ/QM0hn4DdgSQB6aAtCaS+zfrjx374ectu4JbpwyQv3fF0kAtPdfQkeFdz86Dajj8A6oYRM9g==", "signatures": [{"sig": "MEYCIQDt51lMMxEVp5MZiBVcer67YHw2jh8nS4j1GMM8IOI4tgIhAItw3mCsNnas9i/SUwxcyAD9FkThkuelJdpn1VFp6DDs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20450}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-environment-visitor": "^7.21.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.0_1685108714394_0.40614123205891484", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "3ed99924c354fb9e80dabb2cc8d002c702e94527", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.3.tgz", "fileCount": 7, "integrity": "sha512-36A4Aq48t66btydbZd5Fk0/xJqbpg/v4QWI4AH4cYHBXy9Mu42UOupZpebKFiCFNT9S9rJFcsld0gsv0ayLjtA==", "signatures": [{"sig": "MEUCIQCGPjUXKMPBIxf50dwa+0ij+JAJfH/1TDp1eVjikjZODgIgXGAFTblu7l5V60PaGk8EZn0YA2QLu2Ln94KysauiOVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20448}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-environment-visitor": "^7.22.1", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.18.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.3_1685182254082_0.5634911894538424", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "7336356d23380eda9a56314974f053a020dab0c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.5.tgz", "fileCount": 7, "integrity": "sha512-gGOEvFzm3fWoyD5uZq7vVTD57pPJ3PczPUD/xCFGjzBpUosnklmXyKnGQbbbGs1NPNPskFex0j93yKbHt0cHyg==", "signatures": [{"sig": "MEQCICyWb/j65XNlp7g/62M6y0fEx3jrgWjmJ0dB+W4LytviAiBQWyloMmYzC7LTD1tsnCiDc5KUab0mysGLnvUJUt5OfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20448}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.1", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.5_1686248508074_0.13379782919230365", "host": "s3://npm-registry-packages"}}, "7.22.7": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "053e76c0a903b72b573cb1ab7d6882174d460a1b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.7.tgz", "fileCount": 7, "integrity": "sha512-7HmE7pk/Fmke45TODvxvkxRMV9RazV+ZZzhOL9AG8G29TLrr3jkjwF7uJfxZ30EoXpO+LJkq4oA8NjO2DTnEDg==", "signatures": [{"sig": "MEQCIF0cwJPYipleK+Hu4jghy0MGj2/Oim1mx9uMZPgn14PaAiA6aCBY7hPxuYAHdajRlt686L3CnA6mTG7ynPtvdRGeBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20445}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.7", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.2", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.7_1688634237586_0.7836392964155536", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "fb096e330060f85c320fc18726d9c5dc1fababf3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-RhEI8yCRfcG8n5uhODuPnaaWcDVlV7PCFlLavKjcVuyp/mxttyKkMXM8qQJieYDQe5p2dCFaWTa/v6fSYol3jA==", "signatures": [{"sig": "MEYCIQCkCRP9LlCHTU0F+h/N+61UB5r4xm6zTmVkVUf3+nqPbgIhAJ8+ol0EtVHuonwthY+ydBzy6/35j7eA0wJe7kOtT9NZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27427}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.0_1689861635213_0.0978260346408808", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "665270d5bd60dcb7739c8451860e6bfe6e70fef0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-YJE1/XVTOdh6hi9ykIPWkVUxZEmM9uOhtEsAnnX+a1isYKgP7tINxYRAIaSoueUnVePYcNGE38AMCan0DKtXoA==", "signatures": [{"sig": "MEQCICzNqP34VPr7Zc0RQiIZpzBhrkx6AYEUUE/S6Rlb3sjtAiBDJMOZnijFa7bnFvgbApM5D5pMziI4GNQQsqpJiz4afw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27427}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.1_1690221189985_0.1713951079592102", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "45946cd17f915b10e65c29b8ed18a0a50fc648c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.10.tgz", "fileCount": 7, "integrity": "sha512-eueE8lvKVzq5wIObKK/7dvoeKJ+xc6TvRn6aysIjS6pSCeLy7S/eVi7pEQknZqyqvzaNKdDtem8nUNTBgDVR2g==", "signatures": [{"sig": "MEUCIQDi8RCPfC0DGeibR+3R6ymbikr08z3k6xVDUnkmNQ4p8QIgMfvWrPlOklBiLkh+gs3wj4ohQHyo3U2wgMl56VSPtdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20447}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.10", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.10_1691429112651_0.9665073542831653", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "45dd6590de18d3c6e5f3dd28d3ae1678486feedb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-8SYzlSugbTJaThfUeCJYCfJT6JoGr0q9lCbQiwG3z91u6vbNZAf1JF5YLt4ELDLSDaCXgGmoRyXUIJF31D/K8Q==", "signatures": [{"sig": "MEQCIDaFhK0FpgsT2EDEz0Gw901xwF9UFTVAm+hy9CeTWEaCAiBg2wTDSTkLy5m2ps2GfgER9TZG7423f9naPqp/6bpPCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27294}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.2_1691594130533_0.24596239166262168", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "dbe3b1ff5a52e2e5edc4b19a60d325a675ed2649", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.11.tgz", "fileCount": 7, "integrity": "sha512-0pAlmeRJn6wU84zzZsEOx1JV1Jf8fqO9ok7wofIJwUnplYo247dcd24P+cMJht7ts9xkzdtB0EPHmOb7F+KzXw==", "signatures": [{"sig": "MEUCIElb9eBlGd2hNM2mysWtOZrvFU5NUf0JdqBn6py9Rz8UAiEA9X+5NC/1XdYnCmIy+3zZZomCvtdQMb2rWwhZZ5Pq6og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20403}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.11_1692882516534_0.48393316038538847", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.22.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "3b153af4a6b779f340d5b80d3f634f55820aefa3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.22.15.tgz", "fileCount": 7, "integrity": "sha512-jBm1Es25Y+tVoTi5rfd5t1KLmL8ogLKpXszboWOTTtGFGz2RKnQe2yn7HbZ+kb/B8N0FVSGQo874NSlOU1T4+w==", "signatures": [{"sig": "MEQCIG7yR43UOBqBzLk7aCBDuBeUw394aUzNL9sCYbwxEvL7AiBQ7prGdumTcTRR9UJPcNX6B7J8aUqlcnPK333AHBoVdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20409}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.22.15_1693830304611_0.4676812583884631", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "c794fa97eff62294eb8c71f9121e170e2df74fb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-Vi16nYgCRLibwMc11/inKQLqwVT66iMs6c16lQCjhZt2p9hKMO9hfluCQH+aXQUvD6BWR2e21KTNJx8IQmWrzw==", "signatures": [{"sig": "MEYCIQCmNlDOppXK81L6RJ59oQCij+7PQtXC0Q6F/QYu4jMUPwIhANCpgORlLJgznBHGUcPDa6B1LbFcY6TuPOsK0h45KFEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.3_1695740262238_0.9312398928008767", "host": "s3://npm-registry-packages"}}, "7.23.2": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.23.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.23.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "054afe290d64c6f576f371ccc321772c8ea87ebb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.2.tgz", "fileCount": 7, "integrity": "sha512-BBYVGxbDVHfoeXbOwcagAkOQAm9NxoTdMGfTqghu1GrvadSaw6iW3Je6IcL5PNOw8VwjxqBECXy50/iCQSY/lQ==", "signatures": [{"sig": "MEQCIGX6baw706hEJz8jVtAq9zy1kA1VAPj3NbqpnJ7EZgfIAiBKGDFX3LK4yFRR7bUanAYlCt+TahvBffOqeTuRcrS69Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20409}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.23.2_1697050282071_0.38237110600225255", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "806f39e1527ff3575d24ac8af0dcb928bcf048d3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-RjtfLeL2SyClCb+fLxkQ/LA6Z3fr150ICrlauuxiHptanwJiImSKuV2Gx+G5Mb+cCLRhYvHN/KSorUmUaRt5Uw==", "signatures": [{"sig": "MEUCIHD09YEgY8WE05JJZVu8grPWKLV+0/Ra+ZErZ/DoIPTVAiEAyyW8p27mQq/Ych6Sy2Jt+Hp4QHAHR0MoOfxLRKiMFQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.4_1697076415737_0.5289266012095617", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "9df2627bad7f434ed13eef3e61b2b65cafd4885b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.3.tgz", "fileCount": 7, "integrity": "sha512-59GsVNavGxAXCDDbakWSMJhajASb4kBCqDjqJsv+p5nKdbz7istmZ3HrX3L2LuiI80+zsOADCvooqQH3qGCucQ==", "signatures": [{"sig": "MEQCIGf/ygNwp022rMButlzy1UtFcy69NgG5vZz0/X+Tpae4AiABEaP66gMjOTSirByPnTCbrAh1vDAwSphq1eOSMBEudQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20490}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.23.3_1699513425390_0.5773460372552379", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "93ac8e3531f347fba519b4703f9ff2a75c6ae27a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.4.tgz", "fileCount": 7, "integrity": "sha512-efdkfPhHYTtn0G6n2ddrESE91fgXxjlqLsnUtPWnJs4a4mZIbUaK7ffqKIIUKXSHwcDvaCVX6GXkaJJFqtX7jw==", "signatures": [{"sig": "MEUCIQCrTPHnFVQ4XxmfVbQ2n6A3eM5MtB/5+ExYpWg7dXp+iQIgTEYLRYy0pHxsU+x+/J4VS0zNqjnyuZfzHuZYSdsnE0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20496}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.23.4_1700490125478_0.6831938382677332", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "ef2f6fffe8be14b721d28db17d4fc16040159272", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-AfBbdY4vci89fXIEfBt/DCApbiYS1DgYMVEhcRDbVKGePAuriCv5grK+1t8P6zNIpRYsIx6Vi77J0F9FZUG53w==", "signatures": [{"sig": "MEQCIA/VFDuJIV8dloX4Qho7FyW6BzmyVJoHf/rqIEbqkhQTAiAU33+TZ1a5j62rYGaSEb/0HKbF2tjF0gucznh0XKsqEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20004}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.5_1702307998307_0.002193290178420293", "host": "s3://npm-registry-packages"}}, "7.23.7": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.23.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.23.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "3aa0b4f2fa3788b5226ef9346cf6d16ec61f99cd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.7.tgz", "fileCount": 7, "integrity": "sha512-PdxEpL71bJp1byMG0va5gwQcXHxuEYC/BgI/e88mGTtohbZN28O5Yit0Plkkm/dBzCF/BxmbNcses1RH1T+urA==", "signatures": [{"sig": "MEYCIQCPViwkSiTErriwD8lC+AqCFtRfSEtornFAtxSrE06g0QIhANj66sVwJ3IvyP7FOvdFU747rAAyfek0jmWXLY6UbeiO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20496}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.7", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.8.7", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.23.7_1703880089266_0.8792682403750249", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.23.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "9adaeb66fc9634a586c5df139c6240d41ed801ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.23.9.tgz", "fileCount": 7, "integrity": "sha512-8Q3veQEDGe14dTYuwagbRtwxQDnytyg1JFu4/HwEMETeofocrB0U0ejBJIXoeG/t2oXZ8kzCyI0ZZfbT80VFNQ==", "signatures": [{"sig": "MEUCIQCIfoiK7z1MjJpJirXj00y/qcjkj5VdhLp7fWhw0woz+AIgOpySSH+q/rH0j3PhW6wv2Ubg0tdGSioPG1PRDhM9mYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20496}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.9", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.9.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.23.9_1706201867987_0.20246453346723303", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "658048af706df69b8bdd9343cd8e7f3d3323ace9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-iWGAklky4lzbcnrfl0oLOCZuWbzrxQ3ITSQLrEfDCeTVuqLrSeu6kJP+57llERmfHDc0EE6aBM+HfSZSQuEzwQ==", "signatures": [{"sig": "MEUCIC+kyiRj43Qe0fjYKKek9S42SULmuMHG6+erNPOtHYdRAiEAqVEREJp4ReYLZ98Ur8pWBIBd0WnvnsPROWYBm9YILcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20004}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.9.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.6_1706285696554_0.466687877915799", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "e418c87e4dfb06696a9931b239d4c1a7e59ba43f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-VzFkf/PR++pOoFe4mbN9pKrVs/mqS/H+lqVbvCWGqVsAGaMyf1zcLjYZoEW6PaM6/JqIJg4jrK96Z1dnlw//gQ==", "signatures": [{"sig": "MEYCIQDeww+nJi/EIDMTLXCndYmRelTgGEjuhc4wkt37wiFQRQIhANBTdaUN6RwmXyRJyaClrrhiu/frbiJ4pgukHTXL3vTj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20004}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.9.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.7_1709129153840_0.4449615152525783", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "b38009d650b3c419e6708ec5ab4fa5eeffe7b489", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.1.tgz", "fileCount": 7, "integrity": "sha512-OTkLJM0OtmzcpOgF7MREERUCdCnCBtBsq3vVFbuq/RKMK0/jdYqdMexWi3zNs7Nzd95ase65MbTGrpFJflOb6A==", "signatures": [{"sig": "MEUCIQDhDAHCqm1kXg/y+vbHaIpG71p9NniKnHV/8JhC3rf/lwIgEJCXRBWdW2HrARZXtdFwXgQtb4CHGtxANqd9imRD1OA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20575}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.24.1_1710841707739_0.74800531580627", "host": "s3://npm-registry-packages"}}, "7.24.3": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.24.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.24.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "8fa7ae481b100768cc9842c8617808c5352b8b89", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.3.tgz", "fileCount": 7, "integrity": "sha512-Qe26CMYVjpQxJ8zxM1340JFNjZaF+ISWpr1Kt/jGo+ZTUzKkfw/pphEWbRCb+lmSM6k/TOgfYLvmbHkUQ0asIg==", "signatures": [{"sig": "MEYCIQDAmTWheYC9V6SukXVo3VogAgvIdAlsBlH/aYX2oVw0mAIhAJz/fy/KxXTTovAY2vMBoB3t0HR0rno93wtwuTeXeN3Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20575}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-environment-visitor": "^7.22.20", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.3", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.24.3_1710936535599_0.6306296958627473", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "4bdf9a3c6362c8007ebfa87cf69fc5d534d043ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-fbVs0PeKDJDPjBD+4WkWBFCLkX0r0wROJ2PpdH19sE+DQqFD7oh87JN75yTzPx6kBnH3DGxfUwTNDRlx5mlKuA==", "signatures": [{"sig": "MEUCIQC/eifEYrizsj5Po07r5zK9L0j4yJq6eqeHpO+gJ33u4wIgNWY01PVBU0U5DQXnTNM7PtLKTHaEamFI6tvned3aBXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19929}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.8_1712236825434_0.5738429530316436", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "fa4a9e5c3a7f60f697ba36587b6c41b04f507d84", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-VEP2o4iR2DqQU6KPgizTW2mnMx6BG5b5O9iQdrW9HesLkv8GIA8x2daXBQxw1MrsIkFQGA/iJ204CKoQ8UcnAA==", "signatures": [{"sig": "MEUCIQCRSb9ZBfAHX+bE9kEncTcXSEmPd3uN0urEBSsxa2aJ/wIgMoLcLBtLNTtXVHeKmk9EhdRaVRcaXmI6ZQIXFDjNAFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87201}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.24.6_1716553515894_0.3965332608998018", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "cebb1e2c85f6ef3098975c61a571cf566b3d5df2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-sHGLx700IOh0ApHRLCdZ/pwA0mj3//t99KH1Re/LDqw22Q4FEzALBvd+kKgske5PFyb+WYcAucuZ1BnyvF0IPw==", "signatures": [{"sig": "MEUCIGBcyFZNUynzf1n9hGjQZIrJ/tArgYK19VOalHvuOC6BAiEA+TmrNuX8+CfHte+yfF8LCY4uNl5P77WviktxqzxPocI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86802}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.9_1717423540635_0.5266069948416705", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "cc52feb9304d441f1aabeee38b3082bfb17d4a56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-S4DpAHvRCd0fn8fToEHqvDHjAvopM6oTUqFNpNRRSDwOHg9HCvPJ68lVXrLxJfJnnha+aGNQ4dh8Jwi7eZHqHA==", "signatures": [{"sig": "MEUCIQCKvN7uDjxgkWcGVjvNNhF3JOG0pjLu8KLuSGqlV0wW4AIgaUqQUsCfGJFDm0lNtHHYXuKc7Mcah46Uf8igx8VSU1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.10_1717500040917_0.6058953707680317", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "7330a5c50e05181ca52351b8fd01642000c96cfd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-o+iF77e3u7ZS4AoAuJvapz9Fm001PuD2V3Lp6OSE4FYQke+cSewYtnek+THqGRWyQloRCyvWL1OkyfNEl9vr/g==", "signatures": [{"sig": "MEUCIFQwBQJ/KBzptwqATMJGWNd09yW1n/uB2SLDkN+KpKonAiEAyg82CwoHlNZaFzOKTl8akVY+rR6A0ChDSUMSkzlqhGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87135}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.24.7_1717593354554_0.6610283360641644", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "b9a973838e7e32b0a890b3bec0fa246c12eb31c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-oWV8dOlFUMuX16SMrqUZJ5f0dhhg5hKEuXUWKc9alFV4QvNRy4CP9lz5mVCYwv3QekI30F/cyyuTc00+JntzuA==", "signatures": [{"sig": "MEQCIHAMub24Z0TzEkaGQV8UeM7B61VrOyDTTaISymdIwQvDAiBisZWKm6kPdyhZhaIQVVorhjJmeYVN0vYHhwF5iARvxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86700}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.11_1717751765294_0.9803531198083792", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.25.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "b785cf35d73437f6276b1e30439a57a50747bddf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.0.tgz", "fileCount": 9, "integrity": "sha512-uaIi2FdqzjpAMvVqvB51S42oC2JEVgh0LDsGfZVDysWE8LrJtQC2jvKmOqEYThKyB7bDEb7BP1GYWDm7tABA0Q==", "signatures": [{"sig": "MEUCIQCA1KW4hVkJMYrAJyeGZmT5ERB6NEH7TC5OtjJtCIbrNwIgCn2Gf2N9SdZ/EYCq7fegbPQfm8mGbQaO6ag2VqEciuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83223}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.25.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.25.0_1722013174117_0.8044372050358495", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "da5d6788e852743b9e12e109cdd86bb5bda36e29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-4xlchz3X0spjYZbBaqFolYrtQmDNuY2b7luhNMrAnw+ETfXoF50IA3P2Ym70K9g9vZpsQoRr4B8AABy9EnodFA==", "signatures": [{"sig": "MEUCICNAo04Geajg0x1doULK6Hm7RLh4TX/ywDvewQVFXr4wAiEA7Fi1zb1voAMVvoVw5grVZiTU4OkNRHNnftXL+Oal0lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82964}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.12_1722015239784_0.44568295139962877", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.25.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "2afd4e639e2d055776c9f091b6c0c180ed8cf083", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.4.tgz", "fileCount": 9, "integrity": "sha512-jz8cV2XDDTqjKPwVPJBIjORVEmSGYhdRa8e5k5+vN+uwcjSrSxUaebBRa4ko1jqNF2uxyg8G6XYk30Jv285xzg==", "signatures": [{"sig": "MEQCICAblP8COiRAP+kFzfCqsZXI2vl4ixKnGMa0kweRlze3AiBE8yIMBALAQH8Z53NHKUYozGWodRp5JstGeM83AvQKmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83341}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.4", "@babel/helper-plugin-utils": "^7.24.8", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.25.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.25.4_1724319275389_0.7145459281578919", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "af61a02b30d7bff5108c63bd39ac7938403426d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.7.tgz", "fileCount": 9, "integrity": "sha512-4B6OhTrwYKHYYgcwErvZjbmH9X5TxQBsaBHdzEIB4l71gR5jh/tuHGlb9in47udL2+wVUcOz5XXhhfhVJwEpEg==", "signatures": [{"sig": "MEQCIF2AtAt99+V3rXAglH3e8VG7QgJlavLw3iUUUkLB7ItWAiBSg/FKsd1G4wNvULwNk5e7+gxZ8cabfoaQVtzf7oJFAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91445}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/helper-remap-async-to-generator": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.25.7_1727882129130_0.49476446920499617", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "3331de02f52cc1f2c75b396bec52188c85b0b1ec", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.8.tgz", "fileCount": 9, "integrity": "sha512-9ypqkozyzpG+HxlH4o4gdctalFGIjjdufzo7I2XPda0iBnZ6a+FO0rIEQcdSPXp02CkvGsII1exJhmROPQd5oA==", "signatures": [{"sig": "MEYCIQDdqBWK+6eVG0cagNVUJIEXePeik6RrfA2qwqSTxqhzoAIhAKRIwF54+9awOHmkAUQx0N9U5ZeGSzBISPa3Xkv0Zoqc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91768}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-remap-async-to-generator": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.25.8_1728566708096_0.14328918706612614", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "1b18530b077d18a407c494eb3d1d72da505283a2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.9.tgz", "fileCount": 7, "integrity": "sha512-RXV6QAzTBbhDMO9fWwOmwwTuYaiPbggWQ9INdZqAYeSHyG7FzQ+nOZaUUjNwKv9pV3aE4WFqFm1Hnbci5tBCAw==", "signatures": [{"sig": "MEUCIQDCmJ7OnBOGweYaJGlI097UO560wzKd7Eshqx9Mtqms2gIgUEJAMqJNZ2aZH5csFVFQSDaYgvE4skFfwsq9aFdGWaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20293}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-remap-async-to-generator": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.25.9_1729610505398_0.34446685283342715", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "6bee83626311e1f9c3f03043905a2fbbaeaa47df", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-5CBOdD+NX43DwdWRj5Q+xMIvBp+6mtO3DEKP/brNo+kuYB9beWcxSxtg+Dwa+kEGuefssE5z3Pg6GxmfdxDCGA==", "signatures": [{"sig": "MEUCIQCneIiof0mf0kXDiHf32qzapgs+Z3eAIScXG3XsTKoBVQIgWsLBybExTYzJ0l8VAd6okwn0yWtQ4gVRg2nN2COLWz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20173}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.13_1729864485021_0.4646005519086689", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "6dd8633d0a6a84715826fac63dfbd7a1c5e367a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-Nb5dQTEcQ1We714oZMsDXtCYxA6AcBeWzLzGWz80r5Fz+lit+KmgasFtNM0sr1hVLw5lLxcalOo2+KLf2FM0sw==", "signatures": [{"sig": "MEYCIQC2Kaetzjm1+LOA9S3EH1JYjDZomBf3Mr/uB25+C81CVgIhAOQE825oduSCjrjR8vFwOjZMHJFPkQS5PodVqR0zo69s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20173}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.14_1733504074192_0.05874369425719306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "0050b582d94ae1ab0e0cbe14fffb36ab2ffb0558", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-VlTxwWvZDnwZW+J9LuyIqZr1w6oPHE3nM4ZC6mlp3v/8os0RbDRoFfT/Ehgh6fa0YzriotF/Yw7mMzwWM686hA==", "signatures": [{"sig": "MEUCIQDzgUr2tut8iojXgz+d+G0gCGcrU+VKdXczR9yQbfYPKwIgPFYqwZe+st4WzReQLNTdUOROD1AtfGc3PuMqkNYnWzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20173}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.10.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.15_1736529903887_0.9321759305775394", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.26.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "5e3991135e3b9c6eaaf5eff56d1ae5a11df45ff8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.26.8.tgz", "fileCount": 7, "integrity": "sha512-He9Ej2X7tNf2zdKMAGOsmg2MrFc+hfoAhd3po4cWfo/NWjzEAKa0oQruj1ROVUdl0e6fb6/kE/G3SSxE0lRJOg==", "signatures": [{"sig": "MEUCICNV9QP8PcpzvbsV+xdDpszDUW5FAeT7Ip5WZhvBqn8gAiEAhUBXfYKNWhFNIwaVFBpT7VKCiQ1DQphHBw1fBGOn6K0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20293}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.26.8", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-remap-async-to-generator": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.8", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.26.8_1739008772553_0.2173952343845864", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "dd1c3f3d6f0a6e43b16eb4f5cf5d849d65fde218", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-aawfl+LaT26Ze603rEbK8BSMb3ZYnSQQKSfo2bjZ0/uiWuYRsbd5eYyp5hcy4+m7asuhlqahANfchzEAguWPyw==", "signatures": [{"sig": "MEQCIDZktf399YSso9i2ZoR6WThr7vhMQhfTGXzLPWhuWSk0AiAipiPB2Jic0uUHGWcSJnZN7mveUpwEQI1/a/mxCyCrQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20173}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.16_1739534378640_0.8272587567220262", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "7287db02bed3f033e20b12c8f6ff9ece3fa5b729", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-rDQzAhZmXpEHrYwOq0HNOJrYGKjRXIyidvyttdtoZnzmV2soAdNBXSUdmZk0cCsNSKy7bxUZOp9wujhaZ8wd7w==", "signatures": [{"sig": "MEYCIQCOV1jFcHhXbfaaevFp8sNCq4ZxzJZu9qz18FbI3bxrVgIhALypxP7vrNjzFhSkt5udhjkgyM9qf2s8nWb1C7YUldph", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20173}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-remap-async-to-generator": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-alpha.17_1741717532445_0.709698883705183", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "ca433df983d68e1375398e7ca71bf2a4f6fd89d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.27.1.tgz", "fileCount": 7, "integrity": "sha512-eST9RrwlpaoJBDHShc+DS2SG4ATTi2MYNb4OxYkf3n+7eb49LWpnS+HSpVfW4x927qQwgk8A2hGNVaajAEw0EA==", "signatures": [{"sig": "MEUCIAKjXPCd1+hDntLx+os9mFpjaSgqDmdHB2Kk31eQ+mq4AiEAjSdhdjaeOmD91P0I1GQ22ts7zKkp/EAhOALgSpw70rE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20293}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.27.1_1746025766819_0.0022476328264089407", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "2c70f4e3696e77c37be7f5ec856c729bb46e69c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-NAvenKaq3hUjNdZgZi56Hh4eXzqbr1K3jEOBCzxA60nM+lF+R+sbCdDzYhno4h+S0Ka1BUIzNryedXidzbXoSg==", "signatures": [{"sig": "MEYCIQCAopEs4T/zPxhgrEXLGBtdsuFhSCz/BY+tUZEInDGOxgIhALUb+Dim/C5+bPl4Rbrym693xAufP1JMieAIt+8wnhHl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20145}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-beta.0", "@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-remap-async-to-generator": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "core-js-pure": "^3.30.2", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-beta.0_1748620304010_0.7295665244690821", "host": "s3://npm-registry-packages-npm-production"}}, "7.28.0": {"name": "@babel/plugin-transform-async-generator-functions", "version": "7.28.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-async-generator-functions@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "dist": {"shasum": "1276e6c7285ab2cd1eccb0bc7356b7a69ff842c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.28.0.tgz", "fileCount": 7, "integrity": "sha512-BEOdvX4+M765icNPZeidyADIvQ1m1gmunXufXxvRESy/jNNyfovIqUyE7MVgGBjWktCoJlzvFA1To2O4ymIO3Q==", "signatures": [{"sig": "MEQCIGjfYdLdGKKgcQkHSRVHUmzAcitMHAN7U1e4Y3fLv1VCAiAGXQKWM2N5pEhiGdcKOEdWSqsc79rLSw0rH9HqpecBDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20293}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "directories": {}, "dependencies": {"@babel/traverse": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.28.0", "core-js-pure": "^3.43.0", "babel-plugin-polyfill-corejs3": "^0.13.0", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-async-generator-functions_7.28.0_1751445507477_0.2957590434452304", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-async-generator-functions", "version": "8.0.0-beta.1", "description": "Turn async generator functions into ES2015 generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-remap-async-to-generator": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "babel-plugin-polyfill-corejs3": "^0.13.0", "core-js-pure": "^3.43.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-async-generator-functions@8.0.0-beta.1", "dist": {"shasum": "fd030c60bb89a8dcc982808ad053e444b1554594", "integrity": "sha512-sjc97Xt+i1jbHv/N0IBVdH/EMDpe+pOB0lfdiKxTA0AG1mxGb8aecFwflrDQLnfEh3C+gCJc6rT8qkwrgAHU1Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 20145, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC7AWlPzMeP7G9fBRqJX9S80Qxpx27fBC0+zstijL+pYQIhAMbU9VimF4KtZMgAzbnZgydoKdS0Hlb51pyrGjkRU8+M"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-async-generator-functions_8.0.0-beta.1_1751447087175_0.7462219395253062"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:14.345Z", "modified": "2025-07-02T09:04:47.587Z", "7.22.0": "2023-05-26T13:45:14.598Z", "7.22.3": "2023-05-27T10:10:54.233Z", "7.22.5": "2023-06-08T18:21:48.228Z", "7.22.7": "2023-07-06T09:03:57.743Z", "8.0.0-alpha.0": "2023-07-20T14:00:35.405Z", "8.0.0-alpha.1": "2023-07-24T17:53:10.132Z", "7.22.10": "2023-08-07T17:25:12.956Z", "8.0.0-alpha.2": "2023-08-09T15:15:30.680Z", "7.22.11": "2023-08-24T13:08:36.760Z", "7.22.15": "2023-09-04T12:25:04.839Z", "8.0.0-alpha.3": "2023-09-26T14:57:42.396Z", "7.23.2": "2023-10-11T18:51:22.252Z", "8.0.0-alpha.4": "2023-10-12T02:06:56.130Z", "7.23.3": "2023-11-09T07:03:45.595Z", "7.23.4": "2023-11-20T14:22:05.633Z", "8.0.0-alpha.5": "2023-12-11T15:19:58.546Z", "7.23.7": "2023-12-29T20:01:29.436Z", "7.23.9": "2024-01-25T16:57:48.163Z", "8.0.0-alpha.6": "2024-01-26T16:14:56.722Z", "8.0.0-alpha.7": "2024-02-28T14:05:53.990Z", "7.24.1": "2024-03-19T09:48:27.916Z", "7.24.3": "2024-03-20T12:08:55.767Z", "8.0.0-alpha.8": "2024-04-04T13:20:25.580Z", "7.24.6": "2024-05-24T12:25:16.047Z", "8.0.0-alpha.9": "2024-06-03T14:05:40.795Z", "8.0.0-alpha.10": "2024-06-04T11:20:41.111Z", "7.24.7": "2024-06-05T13:15:54.808Z", "8.0.0-alpha.11": "2024-06-07T09:16:05.434Z", "7.25.0": "2024-07-26T16:59:34.261Z", "8.0.0-alpha.12": "2024-07-26T17:33:59.953Z", "7.25.4": "2024-08-22T09:34:35.531Z", "7.25.7": "2024-10-02T15:15:29.348Z", "7.25.8": "2024-10-10T13:25:08.402Z", "7.25.9": "2024-10-22T15:21:45.627Z", "8.0.0-alpha.13": "2024-10-25T13:54:45.208Z", "8.0.0-alpha.14": "2024-12-06T16:54:34.369Z", "8.0.0-alpha.15": "2025-01-10T17:25:04.080Z", "7.26.8": "2025-02-08T09:59:32.855Z", "8.0.0-alpha.16": "2025-02-14T11:59:38.826Z", "8.0.0-alpha.17": "2025-03-11T18:25:32.607Z", "7.27.1": "2025-04-30T15:09:26.985Z", "8.0.0-beta.0": "2025-05-30T15:51:44.164Z", "7.28.0": "2025-07-02T08:38:27.633Z", "8.0.0-beta.1": "2025-07-02T09:04:47.346Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-async-generator-functions", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-async-generator-functions"}, "description": "Turn async generator functions into ES2015 generators", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}