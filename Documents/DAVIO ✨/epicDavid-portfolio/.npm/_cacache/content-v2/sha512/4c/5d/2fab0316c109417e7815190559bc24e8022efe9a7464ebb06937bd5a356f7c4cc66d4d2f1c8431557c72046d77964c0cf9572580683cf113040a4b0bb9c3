{"_id": "expect-type", "_rev": "64-a05cccf9eaf1400b8447c40844dc7849", "name": "expect-type", "dist-tags": {"master": "0.4.1-master-2020-03-06-00-05-12.11", "next": "1.0.0-rc.0", "latest": "1.2.2"}, "versions": {"0.3.2-master-2020-03-02-22-01-34.76": {"name": "expect-type", "version": "0.3.2-master-2020-03-02-22-01-34.76", "_id": "expect-type@0.3.2-master-2020-03-02-22-01-34.76", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/js#readme", "bugs": {"url": "https://github.com/mmkal/js/issues"}, "dist": {"shasum": "4028abac4cf83604101757329afc1f781182892a", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.3.2-master-2020-03-02-22-01-34.76.tgz", "fileCount": 16, "integrity": "sha512-nT0oSmrQhvoLcjfBtubXULozKKvf5Eg1B83HBKyTPIzLnAxlZXpLvuSYIycrUQXirVgIX/izQCymKxUMYBIZ4A==", "signatures": [{"sig": "MEUCIQCkLszVuEBQKiuqkNIaKgo0xw6hW+qFw23KT41cknuD5wIgZwEfNH3rD27UlrEIB/GV7ZU0o37s+OkoE7JqMAE5YaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXcjUCRA9TVsSAnZWagAAl4kP/1kNOuN/gSNwznQTSxoB\n7dX/SstZ1rKjG55J8rYrESNleCLrtX6FKGzJWCjUdMdhK/JKDlido8e7hGI4\nOkVo+k8HnufkrUqoYKqACfdDNxdz3UC6pylF2JQUQHUx9AtNF+Ra9UJTUgVR\ncSotuPyTY+q92UwEDvUSVaT7HmXRJAVChLTOdiEDjIibkGLLj/Iij6awbEZo\nX7lP4CuTFsQ3hUefylH56Xwm7IDDsRB1Ph81BqcTiujoGrfVH1hnmKajvcr9\n4+CsEwGVy6b5Ffvgueh++qRPE1hCiZ0d4bDoA/TFd1zklrFWbryjPyp3Efoi\nFMGc0z9PI95B6RyrzWeG7KNIKT3uO9XzZJ21YgF8NNb02rjJr8TDA237LlyC\nq35Tj3b9+55Rh8Lwo1k4YOP7datKSKm7eipsUIkVVAOQyGEVYvvrkoYI+bRR\nIfhjQjqjqGb6Ih5OwqNuzXbZi0KI7WU2vK0hSCMgTVmdxWQ1OimzxiFyboo3\nyhsZNEL8T8q91uFpg2ZgLMWtEttzqMaxAQuDUWvfbtSXjUrAUoA43GVRjxAM\nATziYpHgOlHlSALvnhnoyqxC/w8z6C0Aq1JQdMi3jB4d/nHKN3UywqzLrhYC\n/cheDLNMqW0E4VTCcIZUCAIWGBP40PD+NH1bnb0Bc5zksX/t4NF7FbUuyaIS\nuRtr\r\n=brle\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0afdd459e1dc89c2c39f56dcebf2ecdabb5df123", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/js.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.16.1+x64 (linux)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.3.2-master-2020-03-02-22-01-34.76_1583204564461_0.012422282930347084", "host": "s3://npm-registry-packages"}}, "0.3.2-master-2020-03-02-22-57-03.79": {"name": "expect-type", "version": "0.3.2-master-2020-03-02-22-57-03.79", "license": "Apache-2.0", "_id": "expect-type@0.3.2-master-2020-03-02-22-57-03.79", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "4fcb5db6c15e3f1bc2997c9261540d99ac87c06c", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.3.2-master-2020-03-02-22-57-03.79.tgz", "fileCount": 16, "integrity": "sha512-fYKkow+7/EWcSt+zNmk7OReT/W/GOMtZh9q305Ai6E8IXQj28aR2GNOKVVE2l+tU/90AD4YujaZ6t8Vy6C4mHw==", "signatures": [{"sig": "MEYCIQDqYVMDcnghDvID5HWWUpsMHGOouy5U6QrBWr7j2I51twIhAPls8lq30UAwgBhHks1d4brN/rWoVpjsN03qoATsjaWs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXdX5CRA9TVsSAnZWagAAQKEP/1SIaWSJqsq3Sj4IDcB9\nhG1Q+aco2U//ZyUf47y5ofWVxEdpoJuZD7BVNl8SHXKxaCOSKl8ptQDZXWvT\naTvyfbOL6lKdFqKxRoNJsS79r+MAjazxargOpL7r6GAeLOjSIZoEKEAqVZ42\no/J08hcOmHdj72FFRfX64dcg2dIJJyqkb+WBcvgLCbg9Rzg0xSXR56/QfS4+\n+WWHgf7DEItizJtKy+EzXgHnC3lYyAYdueicId3fs2xjziHwttwnPR+x91LQ\nHNv7nibUC2h1XSATAAtsEX6DAeJ4XzegnIJ9zgNEWNLdYGPQYZG1bjDTHSEL\nEgoZFWKpx2CZnrQXK+qH1ZvXEzAz2cfrtx3z7QrmLhadN9UmAL23kWP00VOA\n00OOGX/CCz4MgKOdko+B/MGdTD2jVf3qtkNqOIqWVs1hznIqqR8t/WZf3nJM\n7ijajvS7dluusThjChG/7CLos1UwhVURIgbvCMaKjT6OYCw4SuqMYgXeCsa+\n1kHaP+lDoebKoYcMbhMxJX5zaJZQp6V5CFb8TPVzGAVrm8xXgPQsArlJ97d4\nHKW84EL5O9h3f9vApIiWdAAwrbMX2xN55rrXWoDjuM58jRztyAcdT3cgMtNI\nuNoJNSk1OxWRXYWfaARf3XkvdZwbkJT8zw3lQwXrLv3SueVVxR/AYxDHG2Zc\nJSSV\r\n=v37d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "87ab11fc2d22f7b105be15a6d918c1da38920a8f", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.16.1+x64 (linux)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.3.2-master-2020-03-02-22-57-03.79_1583207928799_0.8460773212564172", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "expect-type", "version": "0.3.1", "license": "Apache-2.0", "_id": "expect-type@0.3.1", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "c613ce1116145f263e8ab47dcda833bb0dba0b46", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.3.1.tgz", "fileCount": 7, "integrity": "sha512-z/wgiP4rnhkFACaOg98QmOCIL01BiE1PngazdXzwsQXJFJyf0xT+ubJ4JjnwNPQ7fmNzA3Jhgmc9asxG/5C2AA==", "signatures": [{"sig": "MEQCIGmov8AtOtbve186POmx5NztIB18HHoB5xY18/NbATo+AiBVQROx9PTqpzroA82/pvAbblZSJdbYwlvVr/wQooqQCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXdd0CRA9TVsSAnZWagAAyp4P/08pULmuaj80uMNa2DRE\nFCQb4oNeiNsg1I7qNeAkOIUfiiAil9XvM7xH2nCZmDN6fgszxlpDXrF38kj6\ng+rnHPjlqIT55F1HOIjejOa5DA/QDcH1DOiP+p93F9Dj57MExnWqzGdtMGqT\nUQt/Qp49qisq/21jd01uQyI1HI18hWDyOtZvCQjVCiuMTpq1rphcE5MX+5+x\noJKnlnw/Hm462NnjhEf3ZEpOHTyWKjvGUUDX4mWHDsmj0+x1XuZ9T7A8t7Pl\n5dnG8IWAnndAWRvho/Lm496Yto5eVlG2POKWiGMAe77lCmhFE9Gb2rfFZ8hf\nG/eIcbx2F+NV9oNmb9cgSFZ5ZrA2nJcDd77ozPj9YXTtAmb5TEQvpbgdEhBT\nc6Xv0Xd74dBHYAlrrebjiOl8xO/t/qQnyw5WLohCyS+jN+hxOebPDS1REEY0\nEGD5cgg7xkExZQrH4IfWoUv6irlerMzmLppPIMDsNAhsLQsLIR5L0288MHuI\necY08Vaa4AUgclsCnUrnmLh09FFOwSyG/RHxMSpw3dVGKZ0vwQpOV7iCZ+bX\nlmKoKxLIq3dJCwjL0PHINNIJKyHLZBg1QX55V8yN94uFTUZTUkR+iS+arqS9\n8oNeZ/2NlQBY51mEk6mIzolTht04yucBABXsVi1qhF2/fxlAUasovEVh+JcZ\n0UH2\r\n=kPrV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "969fb8a8a7d41be358fd3dc0e14068d4de7ae310", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.13.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.3.1_1583208308315_0.11902079112361541", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "expect-type", "version": "0.4.0", "license": "Apache-2.0", "_id": "expect-type@0.4.0", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "64dcface3e9fecb1d0df961c2f0b3a665d2b568b", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.0.tgz", "fileCount": 16, "integrity": "sha512-JDkH6QRP0xCXtqi/Kdtp2hdSWavPriYSw7gFqFAn82oDiCesbLoCA1d3qCOUxpDvbreg25ZA4kdsEi4cNv8X8w==", "signatures": [{"sig": "MEQCIC7Wzi5/u07aN3y0FuuvkAdmITEQ8R6lS8Wx+xDYRzhLAiBG11cP1djBM6rlXJKadPPCU+CPOPQo4G+/wtwzKzmuYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYI2OCRA9TVsSAnZWagAAUz0QAJrGyIrqKa72Zba0ZyWB\n97ZeFYnxNNNyE6DrrclhVqDkp5JbAppzjWiqhv3JPYp1WY7xUmENxHNGQVHR\nXZgDTn0F/KXZYXR+E52VLs5PgwWhDDZpMbz0fU0Fe9MWZzPOtkkVe0HCxTtO\nOLBiUEKu4m5v3QeGdvrudekMgFcRSFoVGYE/ZsN/GBxUluWbiKRWK+HhXC0B\nU515fp3vkRUTQ18sdh1vfsvDd3p1dbzxWzK055WZPIXIdHqwnpJof7T2QtKn\nyIdS3epCB+GjzCogTmWRNnJAiwtGr7NgxwBicB82BmDP/mE+KTGTvqrmiGW2\nToWulZ8jwyTRSgrTlbwEOSXXffbBY+UAemdg8C/inQ0Vg7RQs+KP0KGuiqMq\n+5VslO2yX+cvZZmVNN36Fc2SxqqiYirsGLXSTa1CASVNy9kTM97bLpHR/uoM\nWUxI5J1LXnjcDrqurLpO2Z1j1sqWhxpCKBNYl/reJbs5EGpBt4B5lIGclFcl\neqtkxG9VVSNUM0jo9hM8QbML26dGqdFVed3WdkgWshd8irJ1lgmqlJEo/e9x\n49qUIf9oWgk3okg6AaTuisjjBis6z/TNxwhxT5gtElcdf1GxOEB+hlaZO9qg\nnzf5mOoN/pINkGEn/WX7isr62q+NxPYdFoZwj9PCsX+E+AqQlxEqRsihujWF\noqIz\r\n=ICqz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "bd5431fcfc6f9a06037edeccd8a3055bc73f966b", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.0_1583385997663_0.9464577226824162", "host": "s3://npm-registry-packages"}}, "0.4.1-master-2020-03-06-00-05-12.11": {"name": "expect-type", "version": "0.4.1-master-2020-03-06-00-05-12.11", "license": "Apache-2.0", "_id": "expect-type@0.4.1-master-2020-03-06-00-05-12.11", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "0b47578f4861a388337753f1e94e5cd6e67eb2bc", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.1-master-2020-03-06-00-05-12.11.tgz", "fileCount": 21, "integrity": "sha512-OAf2fZ9LFhQ5MjMsLLghAwvJPuWTHdyV6azNkd7niGBC+bLL5oUXVLdiPLH395U3KORabxQErJL1IIaXLSs0eQ==", "signatures": [{"sig": "MEUCIForaM+/ZoPLWaalwpzY3m7y+9gADJpBDHK1ZEvV+36GAiEA/lBAJM8wPMRdDTLQVCSGKZl/hl6g/n+LTSyMjPJk9b4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYdpkCRA9TVsSAnZWagAAqqUP/imBTXK9tTUAGz2+bTsN\nLfKT+b0xohresMzATlSzN5n13JCa93Hk8mC7gIZr69nDqXIHafsYj7f1RuZv\nwXV8XBoVVawIZmqBLIb5OrWNszH7XvRLywI0JP4z5gl2l8p8xOoZsa2C+b5j\nyMG0remCLceXQ0iHsGZTNSat+xhLZhInoiWodMwkdmYY+hB6O4JfCNpni6yM\n2n0FOcacL9RPBrwZXAtmEnYSpyj16+w84cB8BKx84xzJqi16Zk8nN1uDRYDD\n4l/eyjQhRJRe/YRl3snEohgQdNruZrptZVt7LuxVraeiCEuHxypaNQJOpALm\nmPlupYhWaGBNQRYxBzsBiwjAJSPrpx6PbntxfM//OnV4SLLGYbl0DBCJOKrJ\n13feZQ8b8yibcyfWaSiJ0zWGXxzw/zH5TRVFjDRyfzvyoggvkOk7/Ie5lv/X\n9HvLQ9xcX1zlgu0N3xZQ4zn4T2qQf/d6H4n0MhePo/VPzK1FBrrPzuv56o+N\nKI20RIfb8m9pYwsI9JYYywPlU6Yz96lxbdUKdIV7HdShzRG5gSaxKM4rqeNI\nkpDOvZ0Ww04UviDTC9RBzG+6gZ7o5K/x4/AAkHoPBc61s+qDFHNeNf0TUJ6W\nNngRKZPak6aXNNL6BV4AzNtr0AIz7tNIhdKKlLTGlqq8AcyxzSu382fVUcKe\nA3bz\r\n=mkkL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "b7ba300cebbe6cf58dd989c404b9a6cfd757e748", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.16.1+x64 (linux)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.1-master-2020-03-06-00-05-12.11_1583471203875_0.24217733686382803", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "expect-type", "version": "0.4.1", "license": "Apache-2.0", "_id": "expect-type@0.4.1", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "53a8dbb0c05f7997fae21707cf1583b3cfd23f92", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.1.tgz", "fileCount": 17, "integrity": "sha512-XbCvOdzdk6ZLiTQ45xBvRoWrzvLqnvlXt93ng9pWZaD8q3ebD+qevhAG3TqQi7EldCQKmshYd4rSU258ixhjdw==", "signatures": [{"sig": "MEQCIEsJhatZJycWRPlmsrMGF43UOJjnAYR+ghuhASYEzAPFAiA4DL8gAaFux9qK6aOr8W3ywKzJaBNXV+JOysPjHE51NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYdrPCRA9TVsSAnZWagAAZu8P/jj3cATWl5Ou4Vgo3jrb\njDv5M8x5b49FDJI6vyWjzyru9blrZA2dB+YeqBua08uusskUqRP3aoplA/AQ\nqSc573LmZCuYSTYWd52XI034MJjTjGLRmTurARyqsA3PoYeWJRX78IYO2bs4\nko9y0Tw7gvmFfWZO4mVhWuMMG0ghiEpfMZS+5/mmYMdY/tdzxWq0bG1B+EW0\nb3UhopLrvetAAizgV91t8zPaLud+lJTYllxxvVTtvsq5MDJjy6G09XTTMhc4\nk1gdCGq+rJxIBycnjIUJxa3Zbax0CLZmwnLkjFh13yDD/w0gGbSlnz0w4Jom\n8gDcytdbknDhk1tiPFHTl/puRyAWzRh/PyFnexNbborPMJDcQGZRP6hCkg9f\ntgJBW8l5hDQZ0P6hhcTY541+jncVxrfm4itP6k5jEc8FBBBUQD/dh8BNDW6Y\n3nBLJmG/5dsiCexdqWU9IVX/P5IUqseU7mPmkqaO/1m+YcFUjZ1rGGhxNtCl\nawsYT5Jv0TxlB3ZEEbbSMuJuLTZwHuX3Zx2m/Mgu/GdeAsYlDVbE8wR9vCpV\nw1FXYwWRIt+dulS5Hn7JjPc80/LyEXSQ62w5IFnnWRwBcyQSY+mG/AblaNgn\nq8EPvt1oKbgKS6to+hXm24JXA2M7IMlQxk47RehOgnOSBKQBbVfz/R+gJ2t+\nVHqZ\r\n=3i+1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "7ada63d995dad1d5077e8d60de725381a4ae4f4f", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.1_1583471310449_0.3199960114528564", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "expect-type", "version": "0.4.2", "license": "Apache-2.0", "_id": "expect-type@0.4.2", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "10533aa71c856e27e979a42bceeab92cffcb2e25", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.2.tgz", "fileCount": 21, "integrity": "sha512-ZHrKBrtLKm31zS2Yj4cPoldbmL8RTlBTVhtHmZJD4LQm+UPcyuGwu0YbqBR+wvypB8oYfYHCf5liE8FeReaiww==", "signatures": [{"sig": "MEUCIAyuqJ/bmlyPsNjS6fZZR0hMkUPeQMJAs4rVrrabh1wxAiEAkAX0jUs6gy+FTDDP5k+Bue7KCaQiie4fAAxZHSYP8tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeY+kHCRA9TVsSAnZWagAAx7wP/1qHs9jFnAjQyUaTz/UI\nR3X3WXQdgWxrCul3/DqEeYD66ZX/DR9YEp5Z32ZU/yKAzTeHlM5Pw1p54f9I\n3GDlpMq2SX6i1Lv+i1jiMzQfEuf1QOJ3+YO85EhR7hgwD9VfDEmLPrwStIc6\n9S6Zv2NdJZWXT36R+8QyF1uni4/ZVOe2+v+ws+M6w+r5UdCQhchsmTV8WkMJ\n7iiQwDyQLMJ9CJ0dmcUw34BHbIbbfBab06I4Zpe0DBDM7Xe2ht9DE6/cyB0x\nOz7gwhfOyiQSNPHiWmWj7T1hr76oeaPjv2RjifJ0FphHHIb82USGY1mckLpN\nKvWrbHd0pGhqhhXru4kQ1xldXetJih45fGgqmfc7YGWw/CfBfskIkufgHPTH\nGEqBPxEui5AfE4j2LuJdXWADEHTgzDuURGGFk3Qr3u/p4zhJ547xRU+S3U8c\nWpTcfsjPgQlfWiEjuzfljZPXqj32rsAgTYK0pY1OKwo59hzBQrwgIcqIgDEJ\nKbv0plblQYeFvb/oGxb6B0mVqdEP6h9RqbftjxrqvFSXCcL7OBS+9nLfjwRP\nNM5/rD86TUE2cYupikXYSmvKAsYXuJeXcT1wltrfgP1/8Q0Q904A7V4soGv5\nVh64T6xGAmYorOtLibaVJ0jVJDudjVx01aeZmVbjto0yD6DUAqIa+ZcKbfy1\nDBzr\r\n=OjYT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "d9cc762ec6fd4c525d8207d680fbac527b8da7d5", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.2_1583606022825_0.7610512932245332", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "expect-type", "version": "0.4.3", "license": "Apache-2.0", "_id": "expect-type@0.4.3", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "a9e33fbe268bf29bd5afcbc29a9caef2e0e792de", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.3.tgz", "fileCount": 21, "integrity": "sha512-3wF1BcPHL0zjF3TLKtS4f/cImMWW4tdAFEZKh/44z/cN2yr0q0rpUNJ9iEgmiH8TchijxAp+p2X7LBIuODQArw==", "signatures": [{"sig": "MEUCIQCkjEeOjsMEi9RwNG26ZM9nNEXAU51b/Nom5Bg8j8PeRwIgBS8HesOZvqL5oHgOux/7pQAnPd3cdH2LJriLBAtt34k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed7zHCRA9TVsSAnZWagAAwqoP/jDtUf9gyhHy46Zc7KRx\n2hz9y8SwuEOMwJLMMan2fVPNNfO61/LB3xwEjgkqmLw9psE4zV+0kc2+bQ+c\noRTyPKrL4BSG8mUetvV6SxckDctqiciKF2ya4OScV7i5uGZOdEA+YeNTEjp/\nGxuBJ1HTvi4CZFG89yf5VAgWlS7JivZFVO9BvlC8zVjUe4daNsQyNWVBt5qj\nD3HYE6NpZwAahOSOI855kf4w7eSJAqg7d8dRF7Vpg30Zj6rkg8XD93BQh9Dq\nrMIp7XyfZWxN0z+OdoPiOs3pMcpLG/F0kaMMN+hF6JCNff9G5jFwmuJbRFbA\nvxNfyzgON7Ekxq3AjwulBvdN/djgiQZBId51jMv9Dumjd7O/nPm6v2/zx0pN\ns2iHVxP75xySwHGjpFSpwDrYkCVAX4NMPL/U/zJPOFtyIIjwUygFcNzWkvoL\n1O60JjSPMbeBavJtzEWy/ihU+51+AA5ZcNoR2FH2cgt+cbQ/UVH2vQYPPT0R\no0joHXZPE98Ru4i/il71aI196ewYM80AF0gPJ3pDN+Uw2cQNRYgfqRb+rPzB\n9pi4grd6k5N3oP9IvNLqWb/dye96thIHEmJP6NrfM7EFj4AW4w5UMA8QB2CD\n3nFF680cK5vI7U1N8zug3NhnpPxi4sZN4Oj+90dx4GITXyK30Kq4hid+CGBh\ni4gw\r\n=ZjuZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "146e41ba2b242dd629616299132f5c8ef45485c0", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.3_1584905415233_0.027829343111313243", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "expect-type", "version": "0.4.4", "license": "Apache-2.0", "_id": "expect-type@0.4.4", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "90f8e0fdc40919d6d074cbb77c2a4684936686ed", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.4.tgz", "fileCount": 21, "integrity": "sha512-AU76UAAmg7eAN0g+yo+5Dji8IF5gfzaNYh+qLiJPprlti3sz8DTk8AorIpDtykmvaBekGLMIoE+uO5UNewT00w==", "signatures": [{"sig": "MEYCIQCWKg6bN4q/ZEFcaRN5EC0tSAmF4N3q9oxrlqgY9H52MgIhAKCfzHjTjJwe8iZ4XTrh/fq/S+qZIitg2JWiDfeC4NPL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed77DCRA9TVsSAnZWagAACPQP/2X+A3Ee80JgU56mpvb9\nxEmlImyG2+gc4uqSdEKI09PWD/6OX6dwo/pPrFAX/kLjykMh7+fcX1MMg6UO\nluTyV255KmIFPPLl0sqeUtcLc/vuwMlzkwRreJlHX+VdLuY5Xbzfb6+UDmRl\nN6Bdy53AEPTVPXNoGWPOlyCvXWJL8yQcksNpvhYGymao6dHXSliXez2rbnnn\nwpo05AmBj3LMQf0Y1Qt0OGHgee+XR9tTCc6/28CB71+MZnXAtIK0gSmuq6mN\nBb/ttzCEYme4NmDwd6OKC9dSBgc5C/DasuW3K3+1L8A00MizxRdil6x47BpC\nIHMc6F+O9bOQHlC0j1qvhm7EOQMzfKy3lngCsbvKHslJH84EdqWQAjaJ7pmC\nFwQ0Qr3AvEyFCU0gDE6+URud0e/7zxDcD0yfzY0eqfwVuJFBL9RU2s5XX/l9\nh9z/v3gvPZayWrgcSZrtKXt7Se5lMZrKOlVfM51YVW5sRxxumfZ9peNSvw6K\nExRTG+gtsVTV9j2sBc+zer7FF4E1mEopuK3ZMlkijlohVJZe38XkUmOgofEv\njmN0Bttr9by/tp6jMOU/h5v5itu0noo5Pisd3V+D9CsbISIFKySl/3H/HTaX\nxAx/LDRj7n2dt/ZZh3dYaR0UHp7erZuIteDpzEsexyOuS49AjlGSy00D4Dir\ny/HT\r\n=g1sp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "c1221c0ed8aa8f0e10155b06f653f3fb97ffcd1c", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.4_1584905923017_0.04079732447464246", "host": "s3://npm-registry-packages"}}, "0.4.5": {"name": "expect-type", "version": "0.4.5", "license": "Apache-2.0", "_id": "expect-type@0.4.5", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "f2ea90156edd6e3306f8630169227afed8f8e1e9", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.5.tgz", "fileCount": 21, "integrity": "sha512-V94t8ypPurWEl+QoKEtGU58qZZRFOBwI4c5eHSvOsMfBYzrYKPqKeXJ1IucUKzdHBoZQNIrUeVoSZoolQ7Iz9Q==", "signatures": [{"sig": "MEUCIQCBKijJp7ldzvdJIgf2gFr+JhEQVIgS0oKgp/N76DFHSgIgHrv4TuIrLcFXFvcXpItASo2L821pV+5EPby2Hiy8Vf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeehC2CRA9TVsSAnZWagAACKIP/2i76SKayputghTKjYdF\n1gG7uAEi7MfbzFEDu2DTNJgleCgI9zbKk63B8VfKb8xZ57I7Zdh0B1KHT+yG\nQpo/3EyTXH51+2to+sdSeH2JAkxYcGx5AqXTd2irFvOtIyknlMcOc8/AyjRL\ncKGk9DGqJyLiSayH0wUi6pnV+cVr1TqKadvmB/C3Sv23lCOXCyKHSx7aRbIT\nkqYxAF2JWiAdSyQyfu6V+iaUg8Tou7iXU6AgjxnZ0TP4tS7Dl5DaVKDg5Qzv\nS0DYHvMjGrVJDRGg9+epn3gCjId8ey8s1wvBWmejvJqlHlVozt6SF8lPxn8S\n/xBLNhbhmPuOEgM0jwtEPH7VKh98IyHTuwdr/och8mP8pFbaAUYbC9e9aYN0\ndGYpS4UoNjZo/FSWF/69VVzj8/Yv2x8ie6l9nZo1J8C6zobNUy6jtD8h9gG0\nxfbeCg1uTpkaFrXAaWYh7nRK+XRA8SjPtYyubsELv8f7PQUTcd6BmZQ1GTLB\n7284fex4FiQoWtnragoSUzHHhdgGGvei/ceQRgBRIYePtBRhSYZzG/bk0gdY\nICXI7W3cBo3l4kcyd7vAc1aO8DQVJRBwFPquAOZJ6uuWInK80YnYfoiRGCIv\nJcavAMrOnH81gReuo0Mtafs+F1uoKo+geC038r8iemePo350vJ5ShIabiIia\nmWUL\r\n=Cu5C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "bdd108689be3bbae84a5a147a3ff174e641947aa", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.5_1585057973947_0.022971835125903617", "host": "s3://npm-registry-packages"}}, "0.4.6": {"name": "expect-type", "version": "0.4.6", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.4.6", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "a99309cd5856fd6b7eaf661fe9c80259faff300d", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.4.6.tgz", "fileCount": 21, "integrity": "sha512-RS5YnZkMmCWnculADPdLf5B7BlL4M2jC+evZc1ntRBzzYYPhTblygJGH4XvbrKSDBH8kKRFkBNx5CStrPsVrQQ==", "signatures": [{"sig": "MEYCIQCVtX0s8BLHSQI+G0sf75fGU6ri3CtJq5WIRz8yroMOjAIhAIUkEdpkDQdJPFDopFpLl7XOYvg3sYM1vO6TWcJY7o1v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegV94CRA9TVsSAnZWagAAIvcP/3P+kqRN7d/xrzdnRCpN\noFFA1gsYgAGWybhLeoEiopQo0mQFwF8e1Yu7oFvf7YBxCp+IHy6FOr9+DhNZ\nfMa9O/wMhmplhTmgv/7AdZqCBXaWDcSU3DLRJImylbhFIp4KNSrSBBqTc3CI\npp2P+weetyD82Ps0eeDr1xX2URXsDDF+E5enqvzbtDDCJv5wFs6e64UCQjaB\nkkwI6Fa5PLkQ8j0qP+GULNmMLJOX829Ykl6XeyYuGNG4OQs2hBYZq/pdCSbr\nJ8YxXcTTwSdeycHYVLe/3zRTb46vyT241zI5OCMh83T/moG4Y7HFbaI7CRSw\nOPIdQLaaMvyAGdJnJmfuyvoatmsDSITQdY72lGj+Ax30ak+1uBo7J1ETWy4P\nlvNknI8fjrgSWGxM9KYj7eX6SM8xKBKgoGIzwQ+2VJAsczoP/UYAc1rn9r9z\n80KNHOaAK/LK6A8VP81XcMuFsiTZC5HQuon1rix3i0gdsh+DavHc21r2gEGH\nVUDCTNsJ2UzKr37RIAk5b964AWPZt7l+FQ40qn1LuzvXp/PBF9mXP6hp+6TH\nLlYiov+sDo5Gr/fXHFGZqNXx2ON8S8bom5ugwS1xkZv7LdXJSGZBFLypeUkx\n8dw9yN5tsRYn9zjCRNjhociZPlp+9w4mMiEGfyeCuJdvF6fbY9aAFFAu+IXx\n8GrZ\r\n=1pkD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.4.6_1585536888139_0.15734651002778555", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "expect-type", "version": "0.5.0", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.0", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "3efe2b50799fe9e8f4c0b121bd2cb6a91d86bcbd", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.0.tgz", "fileCount": 21, "integrity": "sha512-N1EY1jeXpgX1HnDD/ONRvloyzFuRMqjqOvXbAGJVU1mfOuGfj/K1R+cn6p846C0e+k8f50Y+guIGURoYTshPFA==", "signatures": [{"sig": "MEYCIQCGeme552h3i17ms0HCl0yVAXzZbZEKlsrEy50vbfT3tQIhANhLCR1un6Ua2yWxmGGUvn4Tovz5vFwIv6ELQmgqM7T4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeii9ICRA9TVsSAnZWagAA9TMP/35IBLpCMRti9bfOIntN\np2Gp4uAyQeMOvAXkpyVZO7E05iPsi43nDZO8BrNMDthdVcA5GGCi2YhbDJYD\nnBlb5ER8TZNmf9bdfQwHvWMUQK4n0iIOLrMte27EY4lJqEgpmQ2K3MR/lWbt\nR8QIdRpGljZD/x6qxUYHcBANnZ3lcrsj4jT1V8ch5TMT9oFGwUzWG9teaEoZ\npB5GhpzH6T8yDFoMeTiUjaZ8OL/tPeROjDtJBqzU+I/zA2iz/PLt5i4vdOXD\na4uXdBbG7BS7K+dIsS0SMMrwmc04hWhXcVCyqZxNy6oV8MawkTOiWQl3Ed2k\neLDsM4avfNcxzgnEfXRxi5ZBbC8+OySkL95Km6mpEsv+hfciumBM40dgUcc/\nKsGuudt87fEGJpzVCC0HDRWlpSjWPI6od47Y7pIUlrWUT7ifY8M0BqRgMzcI\nn8BtTk9J1HiszPOG4nd48Kmyq3ScjOBA8G0EodlVZxBcTPQV5XdBdHp5WxQf\nl7+F5VVsUCIuAjSOwwOLGvtOXR6MNw9yXw6258mnxMfj3+Pu95bt5ofWQQj5\n3Q9Q5UZ6GDL22ZC9TLQvUIrfJTrKzp+u0s/kvoTjB5EVFcjvB03iCH2e5Vef\ngvRjqhTsgT1bFLImBa+BJp2IzNb+55CK9l+sordSfL0JWr5qjQG8t9s1uI3j\nzbxS\r\n=r+Nv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.0_1586114376540_0.9487609300863611", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "expect-type", "version": "0.5.1", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.1", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "5e183668d88658589424751bb483803733b98fe7", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.1.tgz", "fileCount": 21, "integrity": "sha512-tnijz1ZA0/+JviQa4EP38jspfojLeEjTRhNAqTIsJRJ3UNQJC4poDcebW9M5uKqUqK4d0wJnU723HMtq28qzyg==", "signatures": [{"sig": "MEUCIQDOHaRW3LamXxm4jgCe2ht4pIBVELyW5wsHUk5nRGGOIwIgcvl6rpho0vYq6VWXJ6Qd6H5lOI6uZfrI9G/GLJ3J5j4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeijCmCRA9TVsSAnZWagAAzl0P/2Nf3k77uPljODF0Jo9x\nlDzLQIkKFVAGSO5CVE5ZVTnqKb06jqXOiPifpz/SR37Lz2OA2at58cJA6gvy\nEuoO23oN4a8h4zHF3zBjUJf+tCu/f0scO2Jc+DojuWYTUiOIXPfuvR/nPL6m\nmDUdPfrLwdURCZtQaPI0UEizoUVHx/7rsr3rYNguwune5QrsSfeRYh7MKNOt\njlhOH91rJaBlamxclnpLPpm8hBA3KxaLAl7hMjy/9KUY5wAqxcEJYOSqafd6\nBIufAd4ehC8MDhaxRF74BbYDm9Q9N4yicRIeFzjfRayl5ojdodNQCbTlZAFl\naBd828V7UbbF2C6KZBUz6RoHahPg14NimarWRAFInVjKV7iFK2JtpNtyKReU\nkQIFYrFsVLF/CcT2uhsoZ1swbstyi+A2PngB9+UathyQ/xT5xu/vyNgtjil/\nmuKxLYQSiutfPGRd9G/0NSLDZYa3Dgnf0qxOC1tg7gSVVWELdzcrpJN8reiP\ndaRgu07vAY0PoxtEYEsGY2ZBqa/l+nOZBZHoC4/B5MWzqCd0R7VZq14r3ybf\nbWs4ufi1fTdnJ9RpZdVWbG5oLXynVAJVHtaLcJ7ZFzY3EGS/te7k/8CseidB\nqh553vgvmHhWkz74BjM5zy9ArBYiqpTmth7lSNmjQRlRKN6dyNTBYSxYPCwQ\nN42W\r\n=gfVI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"fp-ts": "^2.1.0", "io-ts": "^2.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.1_1586114726068_0.43997477534116136", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "expect-type", "version": "0.5.2", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.2", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "ed558f68bd9f1918c6b302f1a26a1827784a1643", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.2.tgz", "fileCount": 21, "integrity": "sha512-JM50FoSh2iFjgw93yPONggJwLkcjOjJ3ar4PQMAOYHnYwV0fsBDC2L6HNn07XYTwthxBALFEssQ02hI6LOpJug==", "signatures": [{"sig": "MEUCIQCFXppZO93l8kILL/rBJRCV+/pB2R6Enm9K5lsqKwSJYQIgOiL6EXEaSCpRSrppp90Yg3msLOL57IL4hjdmppM2qTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeijTsCRA9TVsSAnZWagAAglYQAIF9vot50PrOAkQYhBJU\nT6dJ4jFldgjHyaUArH5oqUTtKdZJdT0Qtsq3PIU6y5QIrT7hRWuPKBCPYL8X\nIEyeHMeKJyRB3ZQeWIDfuWSk653wtkPSHVCJVYsTOPAfXXV0l0gFdmYL+Tkb\n8FUHU+19J/KsUJTKDhDmFiXHZP05eAxAfZUhmHFCATBmCjcCjDBy2/LkvKBY\njinHvsRV/P/m0wLdi14jZyayxg5AIhKhLxtse5gxnY+v3tEN3xphe53/f63S\nt0F339HmbDtYI+sJlcKJK7fm5NYfjss+u22s7j3yiqqc0FtmZB8jUWt4xoJu\nzkjRGatmq4V+CxBLbYxKFihQg2MXPYQMzGPmyWN4xJoqAiJ7HAkrqcFY6ZtA\nc1iS5bQb9/9ZLUO5kWZjGSlKl+BGLZJ+FwMjjxcrQTRWtSP+JvLZejTfmqpO\nVNYDqqn4Xijrm0rny1p6Z27QqgECapp9Zqsl/jbqzHBEpyzoF6wq3KlTul8Y\nk+UDFYA6/P7pEOGgxxmuKTWBn5yrPAgjzQuIDYWKOLDMdjMNBLkyuZN6yF7T\nqcedZlg5vtCx+4NZ8wFbw/eDr4ZNAf/wza86UoQRvhk4edAANeoqjm38zuZm\nGlROSkkUf2eQ/tAaxuw+I5Or0rNU6GfPkm7DpXFpUkRpP4r0soKgb6qcdGET\nQD2O\r\n=It3Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.2_1586115819611_0.15025697083004652", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "expect-type", "version": "0.5.3", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.3", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "61f5c35d2c33d4d0b90a592938e2cc2461f26580", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.3.tgz", "fileCount": 21, "integrity": "sha512-zYGVldD7aAY0zfTXpcPk3RMzQSXE9SLs37itbpLa5/lvwPVkgYGLHtnMEc0TkRtTl07KYtLP4Un9AisBASamGA==", "signatures": [{"sig": "MEUCID4XL9+oj6MrLpZnXgynilnH77/LtDu7D9W3+qtpoZfEAiEAu1460DGAa9rwkW3mgKvumybS0dGauhgaebHXShxmkCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekeAQCRA9TVsSAnZWagAAIscP/1Mz0QBnl+qjtd3vsi1k\nmtKrrcfob+513TwPHEfewlUwr74FWUb2kkgI1WdtciJsYi8L+fwpgSMKzdKy\nHOX6rmRZdY2coOxXkof99LOaA4xfpMHoIabQSRXYK7brW1fsxQv6NKKjOro7\n7D5Zv/6BRQU43iBTbY3aHhBPZ3n7ojTs7uxuldxEhX1C7eXY3Th2gbSkdics\nMNoP1AXb5Ws3m302RrtqKbbSm4QeEYkRCFue73XImFs21G8vW5KEBe5E/vLQ\nYuReqZsd9IeqCdWFL643f4eRatHh3zshd7wCZR6T49cxVsckWrPzvrSpKmla\noE1rKzoP2OodwcNBHQkIVxtfRy3nQ1/IJbo+hti891cyVOwB2LWLNAUW6pGm\nSgflpZS68j0QGhlgjnhBYaFQkf4nowLbBEOexIUZIHWbbsHGg7m4Fw4ZRCpt\nTsggWoE+NDrEnXQjAEwOehmd42sXpMvQG3zkM4xNRLC2H9HS7n4LWefDWqEF\nOahgvoWqaJ0yuStR46Yywm6R745Xjkt7Ex269ClrjhTimu8uEHgkBYxCEwF8\n80kcPqcdFxF5b2LNsdRPhs5xGyJ2uwzqkib2ua93F9HEvxJNtFILoZn210eL\nk1FPmxWaHv4zAyambefwNf33AxsMdVMu6QDLXKK5JLujz59AoM6Jp/MyHdBq\nON9t\r\n=AiOy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.3_1586618383691_0.5202587738743862", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "expect-type", "version": "0.5.4", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.4", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "8d12f25fb7fff7c0af4d5353f247df5f83185843", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.4.tgz", "fileCount": 21, "integrity": "sha512-rUCGDtF6NbCCY2+5YND1fQSh8ysYu0aQRrcFXURfWoxIZ+RL3rNRRMAMD+CYUEUyiwXwc8J1OcjABWU69PPP5w==", "signatures": [{"sig": "MEUCIQDAAkHMlFlu4RsecvPK6pEotpa4ALg+ndVi6mfXaP8LzAIgcr77gARBr8NC3i1dK6lMMc1EGR/+oltvht8gPpoGOao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekjHyCRA9TVsSAnZWagAAxPoP/2vnIiOcu/gcZ3b5zuIg\nYNdQgz3eAdth2WOiXqUDdq0Xc7IFUJdZV9W6BMl1Rk/x4Ge0qKOKvbDyhOsI\nm8AYbu1y7psrnCu5/pAf1kYtKQlANZRwoxVrFN5V9hoT8xB2xzEmUqltBliO\nRsRD0hRfhpC5Qnsla6GypEWT1vgv1XhmtBCRdwxI57XoJj96uzl+LO1/njmR\n9Hf/e6ZVr69+JdzTY7QCMQ32150FtCnk71869WKkntU+4vZZpML7UjlS6noO\nlzoPYlc/7AuItpRCgYofcN+detF13ZZdXvrngPNPcniT63fJ5O3sjmdzoUgQ\ndZMRu6FdhwEXJlf3EZV8FTHFqBMRAmOtkYRUQMc17gQkeCGd2zweZD4L0bBk\nZCg1Zi3lKt9idiJXpaaf1UvTy1Ks04Jxf1jSPAA5gKxTS3xAXwpRrVbQhgbD\nlUVVeNPSBObp0zOw5Ds7MmGP7PgpPBCcjGKvbKfgfCY/kfZ/wkqfM3hoSxaT\nDykdPKbm4pW1sv9CRU9kuuDxUfsd9R9OzUKEvdLm3Q5Qh2OuN3yqya5oLfHC\ngmLzpwyovNceXkOuFhGZYUoQJq/BK0rSAw28X6B6g+d3s+ht9g9mqOp31WWP\nYgMvv4Lnbm9W2V7B2nxF0eWA/4HIx35O42ir8aFwUrBBemWhYfLJ/GP+oIXB\n58EX\r\n=5P0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.4_1586639345785_0.5703280384991614", "host": "s3://npm-registry-packages"}}, "0.5.5": {"name": "expect-type", "version": "0.5.5", "keywords": ["typescript", "validation", "inference", "types", "runtime"], "license": "Apache-2.0", "_id": "expect-type@0.5.5", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "90ff9d30a2f3776db26c8fa8fa8cbfa9a3f3912f", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.5.5.tgz", "fileCount": 21, "integrity": "sha512-eN72J5kRtA7NThkVNwId31yd5IbhkkKmv55SkpIvXgz70oQqkT4Rv/E4Hl/I7/aVGgXGpe9XvnHlooITzV6ApQ==", "signatures": [{"sig": "MEQCICq9yjjWQAP2XJOX+oDE8R4AbtQuRwzRZWhJP+B9Br4DAiBcXLRoSA0RHZJK5fXLv5gK2PIURSJUrwwGadAD3s+6eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepcCwCRA9TVsSAnZWagAAx5IP/iamemVFpKGzUycPX5G9\nCOigKMCK8vd0YoiJuZ6GK/O4GhVyCrHLX+oK5aSe6uC0BEMOBP1VF5jsZKtV\nF8P5FoxhiCehBplHIx+tTLsZj4De3kAeWxAkw352RxeyYxhZn4QrMKdQsAKy\nY20vH7SteHvjYkPD9pb8vVSddKFUpOcvBWBnnvNopnjNP2OHpDtiIcLpeeaA\ng9G4HIPpM6LC9LFQozKhnrlTf5Mo9bLrXpsO27ap6JBmVWfEH8b99vdGB5Ic\nO5RSdyjG3qBN9cGXzWql1f5HtaCCy7kVd87DZg3Wk4+6Hi7d6PrIig/a+AbA\nFyScpYyUMrFmXo5t/96T+fnUCcNoTgEogtAtk0PXMYx5KKzdD19NOydqsQjg\nzGV5b6D7AR9J4lfUjCruje+0b0zg8s6iNkVRX/LIP+bBWnqtpYSdi6OcMtaH\n0kJbScnTKTXNtzpdDVrZT4ryo3wuGZeGPhbnzs2I6m1Vg0dN+09SfDk9hKwk\n0niXFu/ZzpR2FqM/Lc3eVyruRXIWJkhlGm5bc2T6mF6EumehpyPD4ZqapZeC\nVf0cPx5X2KXS8KAY/4IAnwPzZEuSsd9lHumzHU4MN48xtMFm/9VwN5y1zlCx\nXFH/xVMkakx3lUebMiNZqJ7s+Yvd1PWO26HMXyPq4Ipmnei4vks/Rv6dmhoL\nZEdD\r\n=p1sv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.5.5_1587921072166_0.9415014162615785", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "expect-type", "version": "0.6.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.6.0", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "3f58b69065443589960cf47e00a096f602770628", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.6.0.tgz", "fileCount": 21, "integrity": "sha512-pgJHNoSJtSyyeTOyEWLNFFSul+cX7qJeJGumSVYuegb1ck1If0yj7pp2BszCVrpO+s0eRXyBs58yFbmFDl2SOQ==", "signatures": [{"sig": "MEUCIQD4uljhAyFOhf8wIRw9Gsy26cQDo19LkN5PJjxw5LM5wgIgSG5b+JJ6+NErupvhRRQhDEBHvgLTKUJ3s6UR7Fooeqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJer/K+CRA9TVsSAnZWagAA4EUP/2dr4qmSPXnJH9u4/9cr\nsZhZ3YN+6M69ltLI9E0JuqjmlJcoBPL1FNQ/m/fAmJpsOKW+urPf1ToZ3pJb\nbv+iBlwxEGtyqgwf40tzYpXDSZYSzTFtCMZLr+s9RZrNH40hUG8z0VKHYNKN\n3+pnu5xFc0E9/VOfIUFeJmxJk+9u2FnTNbiqd+yBjk/C9NmByp1gSLxHgCxV\ng0dXMKvWdWPV+ml3byYegdaCRPBJmDZYqiq9yvyJRXB8cEgikEwwRi/+KfEU\nFc4bHcAYfbW7ByxcG7yIvZG1TEpKI4Z7B3mXYTFP4luVYvZAJIgIR99c4w/G\n0kIPJ+ynRnURhqZS4WlaAn1jLdGcBQc7+P/oUZnCK7x6fP54t/RUhI1NNhYk\nU+KJZlOnEqnXkd1AcRh0l2Br5B7TKKfV9f5awovRdb7TqcUzC77/W05uW46h\n+wMgTskMcSf8J3diFRNK0dE52c79GOPcCWxnua+ZXeAL7jq6C3b8bpecG7LX\nR96pOQvIn2c7xHQtm8mRrkPA/5vZVQUDnX7cZpMhr0o9EgBS9aOqCDrUD88F\nZQoUwtNVweE2P1bLDjpEhhXnotKBCnc8sBZD0P2pcBCWldeK0N8i8BPACfaK\nzn5eYg7ZOBz8eKcSod0Jq64fJb56mpHIhmpT91Sm3Pk8+bW8Qz0Rlz7ru9f1\n3d6q\r\n=+e/m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.6.0_1588589245961_0.3651604701400031", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "expect-type", "version": "0.7.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.0", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "e345b98dac727e26dbd54f66e366f7f50b6c4536", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.0.tgz", "fileCount": 21, "integrity": "sha512-psVQkPQas0vL7yFUH4vrgAy+7sldWU/pp6RG9JiTFSlObCReSwxdJNg/uR1ganx09cXNwsiAa+wf4Dff9c4AJg==", "signatures": [{"sig": "MEYCIQCGaXX4LI7xR5R2ENPfsKkBr9wM/CGaf4/FT65Jl/mxngIhAJMJl5ubz4oqmOixsAph6dlGSrUfaqerzO7lz8IyQXkc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215206, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesAf9CRA9TVsSAnZWagAAA1cP/2vKoXuBGhv8JADyO0WN\ns0Et9IAmxyZJa8TcXEcF/8U2gfiPPiNL4WqgJLFL4jrbHg6Pk0c73TKA8974\n3aoFX8hz0jloGoLZkBjVAlk3rXhc1EOzmeLVMy/U3P+e4/yr+49LGQunP8f3\nCuxo/+FfP3qiaGTE0TnVmiA//QhS73/lIDEvE2x+Qu7aBKLpGUOEwk2mf0Ez\n8sABx5j42aEj1ONIdEE5oL6C1wXAfykgHS46dhQ1N6DVdMlcU0Y3ae4xSeWS\na3jc2EG/yeKp3tZkPdQAIu+b/GNjLd9KJelUWX03UipHK+Z1H9DUWoDHaRUP\nn8WOPh94bTl7jgphAu+xbHaXj2uMpz51pFNWdLLhnCOmnuHOJuMSd7eJrKFN\nrjVVMW9uI1pvBPgMplRI1lEiqs298a3INy4n6jgbe/LbOXKOCTOLnSUTMH5u\nl1DmEyU0L5BW6KVkyPR850Eb7Qm5OmSNAb0Nt64CTAXdIj4uWzaHCibBNgWG\nmyShAJQNpzTclXclStgOj5mRFSnIdiK0qhniQAQXf1jJPXjKfJbYOlCvpte+\njw8ESJ+uPIXUYbRUaX459/qtcZ/3lpqOnYuh7mIVNHBJ+zPKPBpVSTg4dsiQ\nkalcRTBXL8aeyj0c351PLrWDsqeOHThyHPf/84JLNlXL8uGJgjtiGANp2Sjp\nS6hJ\r\n=N9dx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.0_1588594684654_0.8948817770592645", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "expect-type", "version": "0.7.1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.1", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "7ea0ade69823f669a0a97b7debf2070f0fd082cf", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.1.tgz", "fileCount": 21, "integrity": "sha512-D7JPh/UDxbUmhvpb0CanVNIvikgTpbY5j6u610+HqGY1N7P2qM/syQL7rGWR9ulOhjzw40DOQTGR6u+j1S7SQQ==", "signatures": [{"sig": "MEUCIQDVy6ULBGk3NI1+WBJUV/WxARKAflPsNzqaHO/cjoqvegIgIguvuk2CLnsz6FytjIG87fjA/814eW9SrUSgqeXChKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesz2rCRA9TVsSAnZWagAAO9wP/28ZbDpZ4iUdyiZbygxP\nWrP2e3zSP9K1eytSRZsx0gPyJkN189ZwrGYh1Z+IdMtJ3xGz9rKififmMBGI\nD+VqChQdCFgpnEitLsX4C5eefMzj5pq0sOqJHL7WFdx03+1TqWYBTNeZpTid\njOw8sU6Zdj0aRszFr82h+N+cWwMShgXz/BdwYIGNLeEgwag1gtxHScAKbs2B\nQcwT2e/1PNnXgrNnfyaCeffjEgoqxO/MY5Td5hDCfra+o9TSdkyWpc42ZUma\n45GFStJl8z40DRUX+JO6tY0hplMpy0njCKvvpguyXKdOi7koC/g8CVVRy8yR\nEgkqU0OzVJuenx8shZk49Tt1cBvEqlzGyAl2vrUBKtXjDs/y9qcBUL7g0OIy\nmas8GBVH/WQR0/aIxjQvpah4XGF5Gb20CPs6pocoFGUf702z0n1NwnoCD5BW\nZxuS8TNn+tD45ocd5MomjXKyQbPs76kQTPTlispCX/+4usm4FUks4+esrVvC\n1IqtvfBEuIr1f0dArwpnsEgFaHFJYrKevfIZs9GBKxtNhLeXDTW7no700Jzr\naoUYUKo3GwinUV1Z2AisaQaEZpeoE01yECN/W3RxX9gyDr1UfU9AdPgrDfbj\nFYfOuivVMxozYXhKHzNvBOpQj3q5UhH6CEgwtcOxksHVOaEXGF9r2d4ZjfNs\nvm1K\r\n=8y19\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.1_1588805034341_0.9899402565973974", "host": "s3://npm-registry-packages"}}, "0.7.2": {"name": "expect-type", "version": "0.7.2", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.2", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "ce9d77ea5b166315e2a1c0ab7efdd6ec03448099", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.2.tgz", "fileCount": 21, "integrity": "sha512-iGXD5huuxjW+BKQkntUdecMHQrJMYJkY3Qn5+V74mGS70G8xnKLlMytN3nrbglhzbH9nmDEyL6btx9Umk7TyGA==", "signatures": [{"sig": "MEUCIQChvuOF+zOcF/Wc0hXBwjjzkD7UpVxuSSA5lOfimzkXHwIgJahH1uMvSDRHKJAncz6ATwJf3w4BIF4Z3xeF34BQt7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetNSLCRA9TVsSAnZWagAAV6IQAKEz4kL5TzbPFg9AYX/J\n7BVDLDdoNw+LPRhbYwBGKkSeoAkyB2tGq95qSPN08qA4sw/6lit0JVLXGsH9\nvJ2hfPB+LTiscPb7/P7SGYiHnUyQ87N84jTKSoEL5s/EO9tzTa6LJkMDk8J4\nF/2ApPMXzdeExMBAx8xuv+0usfwyfjcHmmMpSDJBeOEQRxeFpne3ITJHJZGa\nZWfvXwAKg/fQhlrJumi+J2/4qf5s9ocQoFOAy3expFPTd5+PmDUweLpqJdVP\nKp/HiUobs2NveezFVUzAUqWsA39HSg8zdAPc+xrRbRZbqwQJlTUkmEoTDrRN\nO8Ia9f+Us1rDl1r4kpq6ES6yKpHNdTYdjtSKpl+c9nEQJOQDloqoQGgK7AV8\nrxZvfOjyNIDPOZzoc3xQsIPrc7FBfoLg19k+XUGnYDq6Eh2SBzBFX38mkOuu\nWNVgT5Wm9tN2ARRVL3HBkploRaBSa5SWw617vWL5/zvn7mZ4P0qNlB3+MFxV\nH1+Spbj4vNSIO41/4D3HIJf5pJpE+7DoUZNxxbBcYyUa95XF9+VTnGtw6rbL\nUVtUEDjQOBC4eYAsElsYKS2XCBPtQ5av2Gbv/FZGmh33Q2SrpaIJ+tTLAbeK\nI0mwPo3JZ++evO+Z7RATMAdwnwdtD5ByUMjdWxLlRTIYOlWPgxDJEwOxHuef\nJh3S\r\n=xJJs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.2_1588909194449_0.328870844816624", "host": "s3://npm-registry-packages"}}, "0.7.3": {"name": "expect-type", "version": "0.7.3", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.3", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "6738bafc40bb58a131172a0452feebf35cc62122", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.3.tgz", "fileCount": 21, "integrity": "sha512-65P36B6B6KjYd8Hq2aFp2Y84krsLwz5WLT4RyvEuKzQkEcloPLFnjJPrSX/tMp2mC4lGNex9iS9uzs/MRZtEoA==", "signatures": [{"sig": "MEUCIESaEtCokJqDMiDH8cCy2R2LBlutiwJJnoKsvIjwOs/dAiEA3VD/WNqcuAIQSGuaDx7HiRZh3cLTwYjyFO9z7+/ARxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu/TyCRA9TVsSAnZWagAAXKkQAJPPp/rdxk02IYA8oKoq\nn0Y1y3Z+MpewpswLh3oSNVYiLrzS3Fefb24ukN73pGiFm/KpOKxR62QdG7fx\n2REoe8ACsUA5R9VEvK+BKpiymv0jSWwjvV+hIZrLnuR5DkWBdaH66SZycjEf\nhCRf7q+jc6vKI3GWd0FEt0CoOBoEYCiM7MVdrGKjH2qwnNy9MI69HEtJCxVv\n2UsQZZFMglf4cNDLYmYCKGhFnbJ7+0BMKNzxCF90GjWwBv7yk1DKCbJ1FAnE\nvFQyrKFfzh0qKPRDdVh1kbImV5dovozEvRN7ZqgbZfUSUusW/MwoQQz36q3D\nQigNpUHNj+qpaxPzLF/IEL82KP/oGLtTHOgqtvIHriwJdTMU3Z9mGkIV0v7K\nV6SqviwTH9FzMV9if67Pbn3E2qBCUSHuHgPogYhAIxw6H3hAHuxQxPo4Linx\nG0Q3aa+1M3u46gdUDvKLMvbJjzg3c1qPr6qSg0+0PTV/MBguiGd9ki0GdPbK\nf0NS7eTF85PpluGLcikSprYdo9Ut9G+TeJsKInqcgqX7YxkyUrHxIqCbHJDU\nanpKqR40A6KhPwJv30VeexaWLxwRtW2qYwAfUvLCT0B2uras/oJzI7+rt5Ht\nx+EVhXsvF08nemPBebdlDOiS2WEqCwDcGHZaiYlWi4360FzLO6ZW+SSEsZh3\nSA2W\r\n=hra7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.3_1589376241530_0.595199852458894", "host": "s3://npm-registry-packages"}}, "0.7.4": {"name": "expect-type", "version": "0.7.4", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.4", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/master/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "588739f5e86e6713df49ae43812570f11225f9d7", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.4.tgz", "fileCount": 21, "integrity": "sha512-/ykfDxhYq9vz/EgqH8YTeIehvb9YCP2W7qztpEqm84LaA23nOeGe6+H7huxxfncehig2bV0S0Gyhf+ncUwt2Mg==", "signatures": [{"sig": "MEYCIQCXamPL4esL4kbpZe1BJEdyYdHa9AS8u8PjxwGaGlqTEAIhALn3vvT2u67dOoc8qzJgbCLIQ+uABQAlwbeAwQg06CYR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevcA5CRA9TVsSAnZWagAARdwP/1XCqaorp2GDazwtT3Lv\nxl0wuG52Yp2WEd/FV6xyB4Do/AAS1ETxM4g99fqfRyp3XOq74+ZjW/YMyOq2\naY+GIFThb6C++MZdgjIMwD2UCBo1fSOWYghWLgkiB7w65aP3iEo1Snp5iImG\nv5TywmJbH/T4VufPIWh3rA/urVnHLk1Q4fQnNV4C9jPOMuyETjN/z8TbVi69\nNyCV5Wa90MAdIQVVtai26rpPDFqiwexmcnVHm0DQZx2qMiurpspshtXeLTGG\nKWLkzV2qD35J6xsqkOCpT9Q9IvLwshrgxaJeeoP3owKuqOJ7Gce+zWet+lO0\nfoTBNcnQvCx9NqMLrlWUAN7FHYfXB4qv65gz6u/C2+C+6ceEe8STIfSOUY1h\nxD0MBXOirWwp1KSFZ/f7KeZOtVFcQKKTQUfJd/FFh1fzQJBJc3lfZ9QvwjjF\nTqwk1/LCkGLR+zeD+wYjcE2u7T9mQiCwcKESLs/+vcrtfhFgnFqKXnp+/+lS\nS4syatu5FYQVdszS491+48NnweozyXWASRoMyytw9M4sbvj9LVLbjd6KuqaY\n7XFjCct2oONh2ZVUYePO9gPJf1A3Qt8F01hrmwNr8b7PcOvQxcO48AYPBfRq\nqMVlnQjbZJRBzAnzKcjZh6lxtvaJOTcmdsus1AU5dhOBGz76bZGBI1XzlUh/\nrNWp\r\n=2mgd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.18.1/node@v12.12.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.4_1589493817253_0.3155673379406234", "host": "s3://npm-registry-packages"}}, "0.7.5": {"name": "expect-type", "version": "0.7.5", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.5", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "df40f76ebe119b4fb0206468f6050b5d4c067f31", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.5.tgz", "fileCount": 21, "integrity": "sha512-ewSqSEaqAL1K1TjFJ7PGZ8GtlE4wLmjwSPRLi6wc8IMjK/XDnQHGdlqf7X8AYhORqhTI8Q5UzSD9jZDC3x2wOA==", "signatures": [{"sig": "MEQCIHHSO25R/Ei3H0BngieShF4a6T61EhhCDHSWXZSzbOIKAiAJBW2VBCmsWAoIFAWsKTsbKp4qoHOUZukSm2k2lG1g0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+T8JCRA9TVsSAnZWagAAVT8P/jJkcQoTua2mQowol7r/\ny+MXzMl+XZpgYmY8fgDvq1X1zvgYEQdZyNGlgxXhaNZ2XUzIVUtzt4Az5a2g\nXFWy/Q/K6qOvzMfGmJvnbZQIV3ptaLLreSgX5HOiOAC8syDs06xKFptwAQR9\nBs1B2G/P+0no1QvvUTaFnE99O+SluRXk0xe0TuJipoKnjo5cMmumWK5qr/xV\nXZPRFlFBdXzLYeWj69uj12AXswOsYb0/3UtlSZ9HjR89u1UQZ1ndrcWydZ7c\nf7ZE1SroXZ/wl/K/hXgcbIa+xcHLxozhxW4PRvURU80P3f3BW0mO8yo7ob5r\nuNRtoLuAz499Ep1276x1o9HPuU0tOS8f/4uZ0qaKnvoPpD6FbDZn5J27FAks\nD82ZyQSFo4YBzoegTqTbTgFT4HfWY9ixUCDmELuJTfJejziroU9aA5kkGsEC\nOx8Uzd2wyW2EiaCk4RrLysxzmVjalG6LOIJzkuhiDDnOyj8UM/bkuB4wsu9U\nhOlo+4DxhB3zDHPHH44E5vnTOHtct1JS0PpoZQfdig7kBlWnrnU6tcXjbsMr\n1c2rjIi92TUNfm3lvUSIkNWQHHhllNCtDDZ/bSs8s1Hrmcj3uBCfDI/lqBkx\nkdvdq1Hsb3SAhkewj+xj41Y34Lq2j9yMlUFu+IzhhKFQFS4ug/wZacPPqrq2\n84ov\r\n=h0cA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v12.18.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.5_1593392905092_0.4422714807004142", "host": "s3://npm-registry-packages"}}, "0.7.6": {"name": "expect-type", "version": "0.7.6", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.6", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "1e956b20f6ecc487b9175f2fe37712f834c13266", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.6.tgz", "fileCount": 10, "integrity": "sha512-A2C9+nCJ3ITUh/yG9bmml6aDhwqwzcCCM1myAV1ynko7IVgWu7w2rYyyFn700lGYdRUPlB7XgJi+Umb/FeNOlg==", "signatures": [{"sig": "MEUCICiU0v6w1RFL7YLfVoCOfnMLmLnZwVOPX2UkQ9sdNzq+AiEA75mfoUGjD83J+oECrILPkIUBGYF+JNiLNj2bdFzG0hQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47316, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+eJqCRA9TVsSAnZWagAAqKIP/1jmeqHtcnBKpkEthm6/\nworAU7J2cOTmxbkQa5QMzrSfjsSUmC+zS2N5SOZUwutA+Arh7QQbdvHyFTeI\n2Jhc4XYsmMWbPAQ4eneSNPxZDZfa5XWH0wg0wbJEoGCIF8m8BOq1yan+WJYK\ncOK0Eqp79fSfhmFEZNbH5M/XlxfPCrdD2Ct6kGRqnOvB/qvRWHESNECSaCjL\nzCHhaYcQeL9vNMQ0JJiAHFrLzNVtupapD8MXmSRSPP6qa+wMoWSakHwgiXOO\nIdkt5885aGJz584kDi7sc1yEtnrobZFFqnhU3gfe20emrNHq8ggSwydFgw4W\n8lnzNqt+6W5j1qAG0824XAj+Wf3fdYIz7eAP+T/iDzLa5Txwxut09ei4NxJs\nq7Lolpf9DIylMUQIgUoHqNFOALgemUUBFky06AEttaBuVXm/mJwZM0G41WNX\nJ/wlQ9XVYrGVqaK1yhNomhhv5Dzcwtd3nuDqQ/vLmCVWYc0k03IGf32awusm\nfQ/4M3fUs1oEqLxf2i9GUUnYVPX8ga1PMCTeH21SRZaP1JHgSUFv8AXjOeFE\nu9mabrsnTdQu8kT9pZiDlTTYzjKMsiMv0pKgpRtcXKQ2A9JlL2ljw3Z/nu4S\nY7LnTChb2JqHIVknhfoleNPl7nNGh+C3CV+457B+xqImvPWqcaT8XW8cn0+Y\nv4Z9\r\n=O8Zn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v12.18.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.6_1593434730302_0.051351249194746984", "host": "s3://npm-registry-packages"}}, "0.7.7": {"name": "expect-type", "version": "0.7.7", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.7", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "37c33ab7fdfdf324e4b3f03ef4e041b015579bee", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.7.tgz", "fileCount": 10, "integrity": "sha512-R9odt8lHV36gr3OSA4cyJNKLIrzmF+tyDV5u/kXqx9/C+d5EgJf4GcpGxIxEOsho5KuWujJ82bdRlfqMVicA1w==", "signatures": [{"sig": "MEUCIB/FOhsB7hNW7xHFOAtYM8FUp4SzULPdY6kaqzMkwJ+YAiEAip8IaiWbAfSC6EelFHEdzMNjgk7/4FLmxn6iEi0HgkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBmstCRA9TVsSAnZWagAAuawQAIJjuHr48/ZjrJoJDSR9\n8dyIwdR2xg0BbhM4Q32UttRhTKkj+B7eyi80cj4BlvCKqoCBwlG3662yTs08\n3oBqTnZs3j/2W1plmjT+tXhXprwnBa0TOYudH3+Cij5sJb2L+Tq1iLuM+LUQ\nUkauj4HpiLhMe/Km/PvIx3tVDb5Ev2R1QKVbjwOxc3zcey0AwrgVfgLRjEED\nWlCE0mmestb3VVl1Mlpqqn/lgP/9b/Rrem4uM2NXwmhiMWzW+ESDZnt220/F\n7RJkXaSJW8lYHz1BYAu1FJxJ9HRB22dBtG1Sz9kN/SAJvU77MVNuBXt9M39T\nCZLv+zVlSVScN0rKh75xggE2JrKIyJVn0hyVcN2QhjkC/NaKS62uGcYrtdJt\nOVF6/1p5kzGfH/M91QeCQk7qckqC1yWzZkmYB2CxpzLwD3nSAsOsmAoj4Uss\nv5AqyxW7gAZUyirmcMOMxnoAfnfrlhnl/N1aM+bQ9222qge5uFAJHI5yvXF1\n2h3UIu5H8QKhl2xJAe94QVo5Dd6oFqfNDtd45pssG2Abj1CGdP2D/bU18ii0\nkrqR3VnHs4C2PlcxZbCnW82huUnhiXgVOlaQoFkSaLUbsaqBJMkuFL0kE7Eh\nG/9pWDQy0lr488d8CS+XxgHgL9+PwKwepsgtxs9C4F4+wZjX70RG5ueD68xk\nBIm+\r\n=TVp3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "lerna/3.22.1/node@v12.18.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.7_1594256172614_0.7422419332271939", "host": "s3://npm-registry-packages"}}, "0.7.8": {"name": "expect-type", "version": "0.7.8", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.8", "maintainers": [{"name": "mmkal", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "e97ba1981eab5dc69766858a8a7a06df4638d2d3", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.8.tgz", "fileCount": 10, "integrity": "sha512-nFFkv3ywLhg8qfAygefj6jvmNmuGAVdRbDSCnKJnRdTob8IVWmBWFyimNBFqORRGbhg07bgNSBZ9imY0vE8p1Q==", "signatures": [{"sig": "MEQCIBZufwOHahz+ovmf5owCsLC6rDKyYvXWo48+DUAv/C9DAiAJicO2p/Dg0h42mBs+L4nPPybql/bMNorrLkyPW0DAZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBnccCRA9TVsSAnZWagAAsvQQAIqjhDqwxfBir6JsNpTk\nkoJR3TPbc3+uvyJnrinBmUer22043tIFzvDNADjwX2iydAeTFmpg9cgPq61M\nyGkdT6OGjSHLXfNTMOZg3S2tlZfB/vB9C1FVXRM2/QMxbW2SRsw2+knZ6y7p\n6nGR/6SP6n68j0DpAyJyjSVodWnNSlgBzvgQaCQ5q2Z7w7ERtsYWw/DFx5uM\n0y9GdZdpqcf4BwLpMgCvJOcDN6gcuY02BRgZfbq8yEZbi3mSt7rd6M0E1Slw\n0nWWP2sFVKP9aDB7bTvpBV5nvCYjkUP2maiQsiU23fAGFNWTnn3O5jeqNDGW\n9eTpORN+XkoYyUzsQf/xMzmKtFlCz/WEcqCwzjSPeuYQ3ffOjFRJWN6idzyI\n4ZJfpG1mIcrvc3bCabKVsbvptZPfGxC7Xp+ThIX5LICrmeFJfIgmI8ySpItb\n76ZUZrlAGxbeDQ3yNYDUajlbTEiYbc6UfjUsNTw715E1ssdjTu6uE0wc/CxN\n5EQ4DG3Bpa5+NywxD7pgRPG2gCol+EXvGAsBeIOWj9YBhh27alHA6jS6Dwbe\nqIhPDXOXDoMPI50vFdVKhRFtCfdLxYDUKljQ+WvOZE4TvI4UWRWfEEJjVOnl\n13slaXk106Ct9Agz+J4uMMVwzvSNyRkKXJejwNMYSQf3kU4bmAnQQYupjQm7\n6NJd\r\n=6E8J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkal", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "lerna/3.22.1/node@v12.18.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.8_1594259227634_0.5655318478975071", "host": "s3://npm-registry-packages"}}, "0.7.9": {"name": "expect-type", "version": "0.7.9", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.9", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "3036ebb8d09cb1975c764355c747a79d27467c2d", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.9.tgz", "fileCount": 10, "integrity": "sha512-jvPxXvxzcXNOoRVEw5KtT41d3b9xmeUs6zThmWTDoRuoyK5dAwtsZhUGxF1AGZvgN3LHZ0LDsN0usmK5jtdL6w==", "signatures": [{"sig": "MEYCIQDZCnwK34ynXf3DydCCG1rf7fqc3Y3kQy9PAiT5h8Xq7gIhALWmHYUBFTRmoQXq7zfZZZlE55WcM2KOOMpcjAkmUlO1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIKW6CRA9TVsSAnZWagAAomEP/AqqXOvOZrBWhnQVQRNe\nOv9qBioqK4haoWDoJsMlxTv8rIj9dMbzl9Net72YRNfemoYpKDVcZ2twkakL\nLYpNkY9U0zI5a2FT9MPkh9Hfi3OaWEhfxEoiMtkf5LpuwkRIrqmjd90eKW8a\n8bJo58P/x4SNuhIDP4F6oQgkTPj0KFIzMatT0KGjdnWsEZu5+JZsobL/L+1j\nCu+8U8hujQcfVLJV4M0143PGLVsw5wtLw/xMEN8nmqL+xEHnX+vmYBVI57zD\nxjNdIBfQ25aB19YCkLXZhObNdH1uHxEe6U0ieLAsanYXYW7NTd7EC4Q359RJ\niP4z/ky05mfigzD0GimKeUO6hpraQOGCDaRwK7XZyVslICBsvWVcEABR6j90\nclqgI9Wb9nMDjm57egFwO6j0kPdwr88XJg5U5zDUn9Crhk8J3Ms5nrARSrSe\nlSAqWEj9vFQjrWOhkisFVmUzmck/xWP3wnMnY2jyxHOzjU12xaLwL4KBMPsN\nL5TPQmSEhfOQnH7VmpizmOr8M8/j31BTJn+OvoGyJkhhZQEXLGarK0neM+vE\nrbL49hZhWg/cTYrTPCfv0XzoWo8SosFHVbr/skWLZ75LhZKiG5IA2ap9rXgI\naodyXS0HlssJWIkereaDWOpe75xGcVdmFHF00T6P0WjyFCN80DzjZSCLuNMk\n7gYi\r\n=qd+E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p ."}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "lerna/3.22.1/node@v12.18.0+x64 (win32)", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.9_1595975097988_0.3484007571770753", "host": "s3://npm-registry-packages"}}, "0.7.10": {"name": "expect-type", "version": "0.7.10", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.10", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "a2e41f07e57756db74a4e535688f7a13cff75699", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.10.tgz", "fileCount": 9, "integrity": "sha512-XwL/0HIv7C6WmDyVgrlhx+EXj0Yd2x91DmyCHjj8X08lE1ZtVQ+1bHYE9Q6ENjABrj5934wvScRBWdLsGFvLlQ==", "signatures": [{"sig": "MEYCIQDMBcPud7ru6Kjtok5zi+QI2ShA+Rh4Wr2qswMy7WOh/QIhAOJ0p/d448PAQ/SUgsh7b0msyC4lwACa4ZDS1xWY5Nxh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZObbCRA9TVsSAnZWagAAWE8P/15x7lVkMX44m1ZoJDjf\newgNP2BzJwnNMngOKgY2JEf+xqvsZBJSsMWYO/AQDnJ1okoC9RF+fGrfbmu6\nm6XnH0eUZyNnRc2jJX8qmg2lC6U44u20RIVWWbLFlmulHn1I7aWmxoe66udK\nzb6oLpKJsgZdFLm/67PyzmM1Pkh9kTrcN8QsXMam52lRN57DPuuPcDt4QgIg\nhDtXZdvC4Ud8L/iIfoOhEs784CE7s/V0SPhcnuqnFKOTF1GnEIEp9uZ4cobI\nI9mV08+jOP3zxCjUt8sIciHC8pR+AhnxXJ1zg/+eN5Gr6Fnhoz9zJ9pagikc\nag/mGLje1A+3vtuOpBf8JrkFUs5/EEeQ01xisLUCt5A19keQO1zTcItckG/x\nlPYMRJA1FCCfjU7ErfHgS3ahYkiFfRr9ss1wVTLD9bMvx/ouKph6TtYckAdu\neMCc72Nw/IseEaLSyH+HSLAH7yiLVvk5cCNE0b51l8LysCtpTiRJRwsGAAZ2\nw0k2AdHPyWy0ZrBQo719SY9ef0YL7XxRctiZTXU0vPPYL4ZHwqhg+gF8PJ1A\n028SEuyHZ3KDGNEYqLVgynypqn5yGHN+thlG6oMlTV+lpE051FkCmCWA+K/x\nI/+ie6yYnXndnh44QQX/iPpNTx3M0e+XLmdOHINqIGoRtqIxtbAG+0KbXNBm\n02HP\r\n=3Yr3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "run eslint --cache .", "test": "run jest", "build": "run tsc -p ."}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.6", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/builder": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.10_1600448219077_0.43595001279756995", "host": "s3://npm-registry-packages"}}, "0.7.11": {"name": "expect-type", "version": "0.7.11", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.7.11", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "e6d4ba69aa5db383d4c5c1dcc7b439e72480fc30", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.7.11.tgz", "fileCount": 9, "integrity": "sha512-wh/Hpbwsumt/yu2gQyw5PLBpVQaWL4j9i2bKrGNaZxyLGfvhRWwuxLoeCmQHCKYFMild0lp8rK+H9haTW3biQg==", "signatures": [{"sig": "MEYCIQD3D4d9v6MVzh2mSaD4ZbJL0cEIunVdUEdxQRGxhqPhZQIhAMpsBJKCqndjjy0mh0uMLuB//vCnBz/Gpc04E04aCPkX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdew5CRA9TVsSAnZWagAAKpoP/2kETw3FYqxyyvUqXXSQ\nqEl/BIIIpUNLNefe6LMyD4tGf7NACD9kxcDoRH7GcdtVTlH/ZC1ENDKSm72m\nZ2PmVTgVvGpQyme1DAkAW4bsh3GLMFh3feo0S66lVpcPC4omPIXzBb1ky/2x\nugvAkE8SAlet+nwKedYBBUZsDOWKbNhVJ3huPV0KEzpU77urQEyMYfWm/ghP\n4hZ8vxq+gKSNhFkimhnq2q0/v214sclfU4/5unX+u0rqW1JvnMZ65IpTLaX5\nzeLsNLU5Evv2GRmv+ioEohZ/Q0pncIxXNjZ0Hy6dP2pCguOgN/DAeA8N66SG\ncsTrRWKSfLyy77Hdcsu1t0qy7zsXAZgsQhmIK6VFteVszX5wVWazHM6lzf8h\nZTEpkk9pKujGj89p86rD1KZxl4SjuhqeczxtcZlGOW+beQbkB/E/aUYUGHlj\njqPbvWdxIBUc8SozUwCOc/xQXMgrj3D1nmgXVT52xH+gGNSS9oiDI7ijPsLW\nyjG/i+1CChHXQsDEQVXtRxHpNQZahGXtxN+ZDARrBuOmDSLaMY/8tGd9NeLk\nQh51zT1Kd43M128yedshy5e5Hfbg6JnRf+CRa+7qNr1xBTm1SqhaEPpMUVsH\nAo+EgXiV041Ehyxyos4OivD3RNER/TWbFk1K8V4aybCoo7zkQHc8g3HNk99E\no9sO\r\n=xQYN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.6", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.7.11_1601563704790_0.6470898410968633", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "expect-type", "version": "0.8.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.8.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "dd82ab508e7b3eae083072d6197551cb18a82551", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.8.0.tgz", "fileCount": 9, "integrity": "sha512-JJqodW5GNh8tDfupFEwlR9CGLWlI6JrS1G/bktgS2xFVZCLwIfgqstODtmoZ8/pSeqzSDSWLCYrqZy130NwyHQ==", "signatures": [{"sig": "MEUCIDWaDC9s/vM0h5RS1DSeR1tl/5otTEOyePxYZgKOfJsyAiEAvsRWkQoZRSjoC1/m5w+xKJ2zXdBsSHppcaPiO0XncAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfe6BvCRA9TVsSAnZWagAADs0P/1kz5jUvLw2MYcRERveI\n/c7Ufmue/bQHHeXC2F7Eu7sdsZARzDe5ZliJJtEzpyHuBk9JIe02ZmPsrSc1\nmuemp5lQUWPJlhIQyNjgr3GCeIw/ag4hWDH7pwqvQeT7gtvSG3kwQrA1ytWC\npYU6WX1TY1b55MB9oTwzX0BKOiRg+dYyMTdkRquPiLNtCXhCpVslV3XeqE/J\nQcpOTg6QQ/cyJkZjtBOEMN0sEjRy/LGPvkGaCnup8vOVdsaNXWHuplDvYDCb\nRipHL5XDaWM4HBASYxvHKng4ECnQHkbYpVjpKmQBR3Uj0x7NZas+LLuc/U/W\nr7dj/Q5MQbnPs5t7g77pmqn8jgDPuO+4N/fZH4rBbJx64d8Onj0ltyS/0+Fk\nuBdvvChGr35aFnu+vG44jjSuc5+FDKMaF74Heqj6oKkrKR/yrywyPTpz7ldJ\nGO+ACRt/lkI1dvZRBvs9k3rdSpk3CxqUWayRZdnezvrkWyapopNKPZnc1uBa\nz6CJFDWKLQYw6K9T2Kcz0lDTzvdpVD8TBTwY7Cb1+zcFJewS2J/Oq4uU7Z/u\nc2vZ+0fQC7kQWG2wSZ7dqFHhEnXkAOmYMa22AGnG3U6Y4j+sxRGNgnC+/P5+\nKA14MQUc+g6GwaE8F7wo1yH+48165Z2zEQLvkiFhhzT13nL5Cmp9ewDqeN5S\npCZa\r\n=fZlh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.6", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.8.0_1601937518928_0.05968057705514496", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "expect-type", "version": "0.9.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.9.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "2a7af71e62d1bb1c0de9db4266d616604857c614", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.9.0.tgz", "fileCount": 10, "integrity": "sha512-4AM+MjAB/WFsuufmkmbhHjjd6Ks3ytUWJiwRYnzRN0lI7CtekY2hg2oUM3PfYQziKfOEUVqtsR7SgW7qtu1WLA==", "signatures": [{"sig": "MEUCIQCrF/WYjSwg5aSq7eGst4tL0hyXWQ33xaVYtRF6ELlvIgIgP5DNPIA5jWkw/k6R6J9n/N10gx4xxpgFJn4q9TZ4vfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmEh2CRA9TVsSAnZWagAAqnEP/1pigcrS/7dR/2fKncyC\nJM3fnB5mVNK1EXQftFbwVQ82Qu7LvS+XDyD9aDKH0xVBXR1EEtaJ16lRKFUK\nnc56ZT7uYgkVs4FUg+Bm/MiLFQm/cFiGyyCz+xkWnxDnAqLJ+tY16v80izhZ\nID8GKl06JSykyoQ4v+0uE+O3mQ0UtmaVycRHzylLtW8JICIkxz/LFk/MM+L+\noDW936aiXqxgi79iAozKrsTNgANvAgd99XdzgT1NL1jesXVpI/lCf7JzsPcI\nkp9hAJFjLFUKq/0gh6lru2X5aJISJfwQnKwqkGKH4Bvs/qwcBad0n9YNwbpD\nS4Ri63w1cc8/FB3iDdP3PGgxskRKLmbQ5L5Y42iV/Wzrp8jBQqBfzbFaBQl7\nfO/5JiwuruuO3yM8G2fJZBRKiUz+7X3uHWCi7X0r90U3YOmg1SsRkLkU6Rrk\nLh0TTKrEEsTi5TmmBzajQH/MX6hgbe+cZ9F4ljdP32sLrdzyw7QcCzDninWf\nnIOubYmt2nMuh1LcecyANhNjIUw+/JP143lIL2cyh7BapV6tAcJ1III+pEWf\n6ojzrQbVQlic1Vr07Vif/y2BEHI+GccatcwIuszOUVZ4jhRFlTOaWjEFdjMX\nR0Iit8pfIO8t9t5TDh7NWGuUCDroxQKMck4Cw6+J66GaxdV6OnYuU6YsR9tJ\np3Da\r\n=VY7z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "npm run lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.9.0_1603815542178_0.314856551985518", "host": "s3://npm-registry-packages"}}, "0.9.1": {"name": "expect-type", "version": "0.9.1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.9.1", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "83886ebfb60282e96deb533cca34dd5efc7c4979", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.9.1.tgz", "fileCount": 10, "integrity": "sha512-DdpucVq/ktW+P0VIR5EVdqK4ixh0WDSlX/qYoS1EpwpyjyqZWcnEx34xCkCYVv3RaP+MHFY43cUawHPeHdmm8Q==", "signatures": [{"sig": "MEQCIBeU0TzB/4K0hAsrVPQ/7r0In4urT+NJiciVqyRGaHmJAiAmxGTMcQzlHJMVzoSQaoXUfUeoxNnxgUu5EOAlcz50Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfv+CqCRA9TVsSAnZWagAA2M0P/0PInJWqw1+DQaR1zwOI\nZpdQAaK7Md13VSclS2k4JYcZ2MomrxBekOsX51WU/8rWODys6VaTY5AWuogk\n4t/UAZpa25MKhe2RIZCtDIx7vHW9PSo6t631phCsqqu+og/4movUTSnbued0\nhJKqzi1FKAsCrHx9v6R3b6CVs+A7TcQ94ja4eMUlRadetyeML9RexcbguyWU\noSl/SlBpaa5RuqdZHYgXSeFa1/ZibSaq8rqkw61bmTCAnRiRfawOvQ42HR0C\n3NWk0qXOEXjd+6FWpQAg64/tZm/86XwC40i9UFdEeZBRA7SWF6q/QMn4sBoN\n1WXKbD/i56i0sPwgbNyAOFEq77Tf0nBF6cJZ7s9+WPo4bjJqJ6zWpD+es3sr\nETlb5qe0BohOaK9aM7wFK03ne37NjFzW9AqM+4s9rADpexbT2F+F2aQ3PegN\nAlsUS+QhAXStK4ZyidFouNbUfDF5dEV0o6j47+ngQK4N+mPLImv828N9HybU\nWvo9MPdJ4q8xgDOS+DUMiBflMxIfNjY61V5wKdtySTrhvRUT1mQJFe9vikPI\nhjtTxdjonwdOMSyDyCHo/U2x4WdgomkXQYiRUZOFQZni3SbuPbp2IABsi8N/\nXvtOr0R2ncab6bbv8jkMDbdZVtlhRwkUH/aqu9aSKppAVVAE9JU0ZnYXvIgk\ngmFF\r\n=jhum\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "npm run lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.9.1_1606410409519_0.5698645683314423", "host": "s3://npm-registry-packages"}}, "0.9.2": {"name": "expect-type", "version": "0.9.2", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.9.2", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "5247594cc76f584fb7a24a46fd345ac86da33cef", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.9.2.tgz", "fileCount": 10, "integrity": "sha512-0Ju2iY+rtEhSQnwhRDaY9hPwXtXUQjRDPQFirt1E4eD8sEMN754v88Fmi6JRVBxsFhqjju4/zIS2XAF6V4lZ4w==", "signatures": [{"sig": "MEQCIB1oZeeoCyVZROk76chUo505taCIKRt67IZwFTrTuScWAiAE9LcJyQ+fb2Ds7w6uljCzOLI7WS4s4sGNXj50nnU2bA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwqCUCRA9TVsSAnZWagAAcz8P/Ai+E7z1S7hox8AmsLyY\nYQd9k4Stpe/pMGVE1vAhOp1GXijvfvXV+SLiPAjO5mTuA6hvJtzuY2RBr5/L\nUZvrU323xLAdRsOp/Mk9pW7j+GEDOFeb+vxjlEbTXk++18sBDVWfD2pKgLAy\nmWdDVCPfiFVdU9xm2zrzElMnQ+tpK7B5rv76zysbRqfkNdQst29uPWR4L9/8\niZjYXJEAmLrlUl9dfjnXnzrlpfBpXn7S4imBc6RO1yFw0MLmUJlbK5gZBto+\nsxnKdwZr5EvZTw9hg7LJxU+mgewTF8m+il2q7/gdUHyC/iiDacC0Bj7Iu4Ro\nqw/fgrWj35A7rSzMGAIFT4tmG3wIxi53EFP6G7wkw5jOYmTa5c7pFZ/gVIaq\nJLu06a3gXUwqU9ctLbS/NFlO2F+fviShLmx1rDhIifM5sZKSyyxarsG6VpeI\njcz/A70rJD0bE5OURokE6yH06dsnF5l7YBT9DujRDDFdEJDFPW80FrDpUDoE\nKUHiWKy3AIgxV2lgoCNDJrZffFVp08JbFeLQFEARpEfSUPJ+JW9sdvlOmx+X\nYKMw4LvHe/XCRpqkDLjAZqXuO6I9jhpOm6z4wKWDag30ZCZzIUPv8TUqR8u4\nlVymR+WSD1TyumuGJkL5MrKXgOyqO/4Nc5z7MPqS/jCx1bXM03E0T1rQgPrF\ngn7N\r\n=gCD0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "npm run lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.9.2_1606590611466_0.10716889284407238", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "expect-type", "version": "0.10.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.10.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "0d0125715045fa611edf5da58b6fb50e81ea7b55", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.10.0.tgz", "fileCount": 10, "integrity": "sha512-Y3IJY/RPItzebmwBFDtbM9VrOdn5hYquo6aQKkIzA4Z4klPWPIEpwZSsck418eIRkvh/vguQtvcgqeNHM9JX2w==", "signatures": [{"sig": "MEUCIQCkYUGtNnjss1qBQQjnmTJzv+9+a+vwJ995Oe02ZYpJWgIgLKZi6CaQgzcqKFL97U/nX2rvdM3AxHCN4vQLPNGIjsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyTgrCRA9TVsSAnZWagAAAmkP/iBTT+0vNg+WcL3JakZs\nP6Do8zSbVJb/r85D0t1Q33IPTPmXPF1iiGPlnJrWkfRs7Pq84oBX1eoFb2sJ\n26ZNw55/leuUDONqdTzD1uXzxzd5AJazwO5WNSD6GNpAw8QSgrlKYmBbM1+P\ni7JPmRkI5PJVQCRbkLFA3xJMXlC3GkTc3LI5Yh+3nfwTj/ygj2OGHZ9YrOEc\nqNpuz/Gl5W2vEeDI/iNYpLulB7pvMi0VEPb856VdYPzoiqZKzlYi7CYReHIG\nTpDxhFYzJqu4RZX37jy7sOmDj9P2Onn/G3KkleThSNYF9OEujhlSjdKxjygd\n73FRjkR2jXFD5f1lqvM5pCwzmRFSirWpVLQQuYLkXpq97UWWFsqNHCVWkISB\n8zB8QV9TvLru1rMWgX1YnkpCUMsTkisE7DugPa8hxlEBiatNAQrSDhc+0wWT\nB4Lc1srN+gDOPvEQJN2+DL1a0ie38ZvZJd1hWYhqee603MkENjAgjDYBCPsg\n4mTUef62ShTtDPPfYOqFP54NMJTwBi4EVO8WBDE5+RP0vEWszrMFP9YBKUec\nEVLQSHhL/Gj0TDVCwjth6+aKZzyJY1KNHLtqpJkyVQ2GeKGEFJc0Cdl+fVZm\nP56rXUWFaIijfwtBMaXGZJ07PF7HrQ31wh2jiRHzMbjZELjtZDzEbvQcJ9Qa\nW2IP\r\n=6xJf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "npm run lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.20.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.10.0_1607022635311_0.24044820650657606", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "expect-type", "version": "0.11.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.11.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "bce1a3e283f0334eedb39699b57dd27be7009cc1", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.11.0.tgz", "fileCount": 10, "integrity": "sha512-hkObxepDKhTYloH/UZoxYTT2uUzdhvDEwAi0oqdk29XEkHF8p+5ZRpX/BZES2PtGN9YgyEqutIjXfnL9iMflMw==", "signatures": [{"sig": "MEUCIQD5yuvL2FTIwTt5bTRsFQd3Clwy1Kt9SJb4F1xVegndhwIgfe6PIfEgVWEkSg5dgRALvwZ4HE8a+mo19H+pKoGbdQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0NBNCRA9TVsSAnZWagAAuJYP/2O10HA5gH7HKkHa9zEM\nyjAYGVQLfrrrOBiGnQbsrbrIXzNLgvJ/zl1mJcRNDv0LQ+XssbgyxiAqDVYA\nhtxOeW+qTwuL8I6RB2WF4N1e1rh62YpBY0hiIYTlt0lwr1IRXQ9S4zQovz+U\n5CjOfEw5a9EZDIeCQXP+Qwl5e3I06+rXpUuNA/QP5dwSQTOu8GJDrAmYBiyc\nXkm788i8hxUT+oaZeD0puWYtv0SiO0FGm8MgnwZbfZFQN1jUXDDqD02Ib+fY\nwYpzasjDyr/6RHrIiRcstt+iGsjTk1KUd5AkF0mY8KeYNjNCVs8UnLC+TLih\niWpay/H+1JifoljVcfNqgZuo/5kn/YnnBcfESohH6znhdnrcyQG14FPzAQRc\nON6PAYqFlQ+vTgja920kNbaMraw2DLb/6MRn3xbgDdzAla+ogpZ5+Zrj8516\njKjbfmSblnIk3IhnZTKqoZT4RiheV56W0NomjLSE/sIvnWRRKFOElbf5m/bZ\nKRbTSotby7NbLrMFu1ttuC9uK7Yn5ySudDZwriiJjW/73mEtTPBTAGj4rc4N\nnAUrvjpXR62CHyqgd4eYxHToOzBfNl3u4yUpbJx3flBc5lqKa45b56JxPQkL\nKEkrCFUKsEovNPC9gJIla9A0pJfqB+Wqn2Dz5OhmzAYYbIcjSVshr8kvGs9N\nFbTe\r\n=QOGj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "npm run lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.20.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.11.0_1607520332804_0.9543247085070734", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "expect-type", "version": "0.12.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.12.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "133534b5e2561158c371e74af63fd8f18a9f3d42", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.12.0.tgz", "fileCount": 10, "integrity": "sha512-IHwziEOjpjXqxQhtOAD5zMiQpGztaEKM4Q8wnwoRN9NIFlnyNHNjRxKWv+18UqRfsqi6vVnZIYFU16ePf+HaqA==", "signatures": [{"sig": "MEYCIQCruWghHKHCBCsPZ4uzOVgA6yQu9gR2CjKIY+gn/80UmAIhAM+2vEljs09tZ0DETLb724Fp2EwFD0RWnStuOlTO+75e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4IFKCRA9TVsSAnZWagAAAScP/RqxBA+C315ngLr+dTKJ\n2GM4ElKBFKdu5PtPhDuX4wSblJqUK1DZce4aGpHToPvIKFb0nNp//W7Nx3Dz\nq80ktmWdTlyo5aUpkg7Ed1MPTd6lpG56UqH/HJdd9tIXuy9tM/MS3mVro9ir\n525VNXQ4fLuUTpxYlfqYkzONmtqjFx5fDcWD1rdN86Zwze1TzGagU0RTFg1a\nznjq+fpjuLhuYNPmCc2zFFdthwoM+UfR1hQ5xi8pD7hSi0lEG1/KSRNFn0RR\n4g9qeo3eqAZ99ofjFQ5koMyNPn3lJ8nnwwXODJkvEimOe9IfGwsOeQ1SjOiI\nDYUUE98vzr6amLQtD6GAcPIKkyJPpvV3imhMk0zpPW1uTP+vtwpoB6wtJ6mT\nd6wJx+d4yknyCrJNn0qR/yEXhdX4u2FSA64ZkvKttCZyXZacHN3reDr/QyQy\nQYVANBvrE052o25pM18sVHNrjj16uqz1vmo5Y71JeUGggs7fBrKRK1ZlLcy1\n+mGIFerAsTUmlKmKwAot0uto45ROgpQbcwbSAI/vlCdKJsZS8FV+yn+PYppR\nsyqXf4I+RS1FQxi5DhFhyqas1SARam4UOEjTEl9b79Fik6fGRr+tx6UlyBXc\nsXViM76WDvDEYmI/Dacdy/h3l+4wsS2TirNr9L0t5XEMq/VqkpEAAPhVZQux\nEF0f\r\n=7n0Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "rushx lint && rig permalink", "postpack": "rig unpermalink && git status", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.8", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.12.0_1625325897982_0.4379931075910768", "host": "s3://npm-registry-packages"}}, "0.13.0": {"name": "expect-type", "version": "0.13.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.13.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/ts/issues"}, "dist": {"shasum": "916646a7a73f3ee77039a634ee9035efe1876eb2", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.13.0.tgz", "fileCount": 10, "integrity": "sha512-CclevazQfrqo8EvbLPmP7osnb1SZXkw47XPPvUUpeMz4HuGzDltE7CaIt3RLyT9UQrwVK/LDn+KVcC0hcgjgDg==", "signatures": [{"sig": "MEQCIHkvg2mioPAQciomD4CzejMDkoGhNGBInHgJUKnwsEMiAiBjN2KztqjqcGu3YYbKm+4GMaTYK5amkalDLa08decOsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59818}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "rig eslint --cache .", "test": "rig jest", "build": "rig tsc -p .", "clean": "rig rimraf dist", "prepack": "rig permalink", "postpack": "rig unpermalink", "prebuild": "npm run clean"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/ts.git", "type": "git", "directory": "packages/expect-type"}, "_npmVersion": "6.14.15", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "12.22.6", "_hasShrinkwrap": false, "devDependencies": {"@mmkal/rig": "0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.13.0_1634390247555_0.0004997402013098284", "host": "s3://npm-registry-packages"}}, "0.14.0-0": {"name": "expect-type", "version": "0.14.0-0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.14.0-0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "b02c220948f3c74e8ed2b552ac934e93617f65d0", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.14.0-0.tgz", "fileCount": 5, "integrity": "sha512-Va0K01LOYZIU5BV1ztxLK3bCKVe7RX6Q2HryYK8A9gnCIKCkcz+6xaccSz9I9pNPTmUFTFT4huZ7S6JCtNudUQ==", "signatures": [{"sig": "MEYCIQD0TTPE2rJ093ZnZViDYrLUh6bPwhAblQrgR5Ddh/6jIQIhAMFtDyzIxPWy4rBKnnbITrV0dAep9xiKfKpX5sMpGd5s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFnwtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+dQ/9HgO11eTNLYpdmsneuzKq9PEDk24SDYbF4u2F/Pk4Nn9s+zMv\r\nJwLObhawOOHSHf1LnQ3vlfyCE6HgL78GZNj9TeoLk7nTDf1mILrUdMZbdlQw\r\n5BQcENQpkSjYUiFGTkHd1dm6s/IsaztPoewaFpJt9A14FPm4iniI+uFeBQQb\r\nTS6pMyujVLsdpXRvt/jvPFVcPzGMbiuvysr2qOyxXlGkM6unCDWjxx08ntwg\r\nL7zKQRiPsl1hMUTut9piGIkyTSbBsVygS1GJMra9qwU5yABX0GgNbLNIxgA2\r\nRPG0Dae0QoTEYo0IthTOrLOFMN+BaZCFlPmT/3JiTCA7jhWJxm8MonC/3O9F\r\nWTG/GI/wu9WVaJwVE9SxikDXuhXNAi+Ic5sHffhB00L2ZD777UsHguiW/tlO\r\n9KGGl+ZdxPkGazWuAyZbZU/liCz5glJZ/J3O27kefpueTHE4Z3TyV03201cG\r\nzPS+ZqySht453Pg+RMgnLTtrYNv7cJnSaOZfA6KEvIIYZofY4p+WpDrVo23B\r\nlcqNPygmb+rG3s9O+Uu8mbosl7SscPrOP5MKTZdt8oEomg//mLPs0cYPoYDE\r\nmkzM2+SFLVEENurnEUQAEpNzjJohXY9obMp6DKmcmE8erqcjTLoYqXxhnxRU\r\nMyTKUL9YA+5vsthX6aXrQhctrOGrwUWcUFE=\r\n=BqF/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "a2eff48785712208bd18c7106246e8ddac87bff0", "scripts": {"lint": "tsc && eslint .", "test": "tsc", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"eslint": "8.23.0", "typescript": "4.8.2", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.14.0-0_1662417965740_0.7859506018638616", "host": "s3://npm-registry-packages"}}, "0.14.0": {"name": "expect-type", "version": "0.14.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.14.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "94eb230d237ad5f2d1f910609e8a8528cff46e67", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.14.0.tgz", "fileCount": 5, "integrity": "sha512-c1b8FMNk9lUP+ho7z4Fgug6wyqvT/its7EWzZ8kRvJkGlNmvAk5ZKHlan5VVYuozY+3C9iWRYX/cK6DQvduhRQ==", "signatures": [{"sig": "MEYCIQCi0i2iH6BfVYTUDvEeE+ubLQUYCEdfP47OKdYvaRlbbwIhAKLDlrOZp+9u1mQ+dQ61HeLzBVB+3SmzQUM2+gDWdkJW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFyXYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUow/9HputTY3NFR1NAwnWfQ5pvjzYiZlUAWmU2P1ewKnkDC904UMX\r\nSK1w2PStpFBP+flhI5l4u5cXV3CM19C/lu+Ltb37U3J05mak9I9pjFQx/prn\r\nu9M2yPcFbMKdbbKv7kj6T48KRmcnSeYcdzDSbyo7ceRyrRC9Guz17xBtsbbF\r\n/K6AyzV8TLq+U6qF98+zw30v+kwaGIPOz35P+dfapjpmXrOMC7nFVSgOhmqs\r\nFpSyZcguvhQzJ/I5hlLvnEaPXY8a6wTqesFLCQ4dr+kq+Qavn4yshDtXwHK/\r\nCTjM169+rOMI0IOeUu1X7llHpTejXu6vBGspkSTtm731F9YZ3BRZRQHNrpZl\r\nRHdXptfOspYBLcMe0kaObeKFrVqoOP2cpDXa/pC3QE9Y/8cLIbXh15Jg1uXI\r\nfzOWYEklYHkCHch76EBCnC5OAWJncDtBvLr9SFBZSJbgB1YnFoOexE5QXFd8\r\njBPKyDvuyYcLmEsQjZxlwbpHg/aLufi8kYR/Q1TJa8JGruWeB3kk44xsU82Y\r\n5IISH/0E+E489CHnb4B2oF8bJB2SgTzShhrd9nA29gA2N6yNXWd9d3fqbC7t\r\n09rLQxVKD8vt7SLMh3D3xhfnJQdDc1VuRVCD6yXlulnfEnjEjHJ43ETb8JZW\r\n+KBVoF3BPDoOZ2+CvKS/lXBdtDKTsDJT684=\r\n=nh/x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "0b9a5efe2452573f2435b7fe4e33c3f1d45eb01f", "scripts": {"lint": "tsc && eslint .", "test": "tsc", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Compile-time tests for types. Useful to make sure types don't regress into being overly-permissive as changes go in over time.", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"eslint": "8.23.0", "typescript": "4.8.2", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.14.0_1662461400504_0.8720236589023502", "host": "s3://npm-registry-packages"}}, "0.14.1": {"name": "expect-type", "version": "0.14.1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.14.1", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/ts/tree/main/packages/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "4758e07b6729f9d055155645424db8f4003ff47a", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.14.1.tgz", "fileCount": 5, "integrity": "sha512-A/5izqLRrnWIDNk2RJdns6u8GHggR8fSRTdK4+sawQb73X0cf+vrtIeYv/c9rPnujCjOhynzZI+rwlQyI/OAGQ==", "signatures": [{"sig": "MEYCIQCUdqymHbqOHc/hgR6fumL+FuaVZkijv////ppP+THaHwIhAL9vZdSkqZGr11CNAzSofaE1lbTwrlyFsu98I5r0bzrD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGJCGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoazg//dbh6TnZb0Ve0zUVh2twf1/8kLUKYB64bmDQJuGvMUvSxzRBt\r\nV5snZiO2btv1mDx+SllmUdWygLk6+hyqGydZFFd0AxysV9LJTy9L8LskV/tL\r\nW4uaWpNhoBaUGtEi6aAOOl9pq5zK9XMxyRClNIOa+mrklapwpgp1yOR55vxX\r\nq1fzzgtpdn4uSzg3cPcPUEl7qI/VOWvCSkr4l1V6CBlDr2eLIp2wPsk8UD2k\r\nG0ln6z0MQttq7Kz5iY+kpeFNbgflculYUT0Gt9pF1/SlT3zedvwcWlEdIZej\r\nO/EcmHaGF2wTgTcuMFIN7lvD4EKn4YyBeZEXAQ8JgT9fHGyVSlrEunObSCoG\r\nIZK9+NxUX1fQ1KtCGrht1zu8k30BlnQ9SWZ0rdTrRPmt8BQyPnsektyRxd2K\r\nV9iPkhQKOn4O6FFJ/8mZ5dgUENea3nEPwYk97g+vgucULXuwU9FWSLWco/OH\r\n2d8iFBQB+4ryHVw6dLuS0PvYKaFMO+XAQzEtTy5nNIYlXS/ewl0/F4jhtpQp\r\ne9N+TvMvEvkaJB1ULWYp93GV7kagCHcqs7VxaZCiqwEuLCGGn5x80x0685OM\r\nlOydrRCjxZMikWTXfg5kYdYsEFavDgmgE6pdrRxh5twrxs9LoQtEKYia2rwd\r\naBgVPrGjPAFQ9SHsIXhIDfyvtS9nwUUKyJM=\r\n=Uhlq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "d41489f8ab2735041bd53512bc3eedb095cc685e", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "typescript": "4.8.2", "@types/jest": "29.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.14.1_1662554245847_0.30370541893823244", "host": "s3://npm-registry-packages"}}, "0.14.2": {"name": "expect-type", "version": "0.14.2", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.14.2", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "3924d0e596455a9b27af48e8a99c582cdd4506eb", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.14.2.tgz", "fileCount": 5, "integrity": "sha512-ed3+tr5ujbIYXZ8Pl/VgIphwJQ0q5tBLGGdn7Zvwt1WyPBRX83xjT5pT77P/GkuQbctx0K2ZNSSan7eruJqTCQ==", "signatures": [{"sig": "MEYCIQCGOemQLeth80rgG7AJvXjr07s2ysmF92mozDcQMwJSqwIhAJn0jpLU6alDqcMavrS+PSKMRT9GLYc2c4qgMdxq3S9Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGJEOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoCQ//Vky+XCLYHITQOmzoxgcqAS3yviz/QucFdwLXiR440Zuii0Ny\r\nf0jdpOeglBc8YlaHVOdPuxZXIIfltmaq1MunmOtsAXh+JRSLLiaRP98rAch6\r\no6YKc2n7m2qhuT+OMEYF/c/6PJ8UZfOnWHVpbAW+fpPeLlT9/xJsOqkit+cp\r\nc1cH5T2kuYvZS1D9/bL0/hg9mS8YiN+32JxGWgTUydEfG2y+8hz0ectoHJG8\r\nJ5SgQKo1CEMZBt3i9VOEwcm6fffR1XhPuXrwnATcba9oiq9Xzrqkd5zrUqg0\r\nM4h4VZ6ns+yBBw/7ERBYRvYVp0gBn7ByGTlc/xbNlEhkxFfPZeBIDDuUfhGJ\r\nu9L2vgbJki4EvvMpxo3+36DcXb5WsC5BxX/FdrO55CaEYn0tD3U8p59536lU\r\npunpwfQhcl+/794WMeu7OhlrYpHFRczATl0KSOpTbXRIpEuw3MpPQn2VFWdL\r\nfcBkLZYKBdjar8SsyjrFiWXAb2Hnj+GD60IqTmLHXARrWaDxTkIFqt440O7+\r\n+NW7DzHEtq7l/4ecknL1rN5wuH/e06mHUnOU8TGWdyEu6grxNpy+iyHq24x0\r\nbMTc8F0AB1Vo4XBvLhQMOPFzPoFpFvEvzBZ48R11/PtJMWMfsZWYhnm/Nnyc\r\nD+kBZWs6d9Ec4jgDRUh3KjEi5Rx9w1H8ywI=\r\n=MbiL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "b4491e06b86da7a18c99313186e5a3ab06f99b08", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "typescript": "4.8.2", "@types/jest": "29.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.14.2_1662554382213_0.21159872102275656", "host": "s3://npm-registry-packages"}}, "0.15.0": {"name": "expect-type", "version": "0.15.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.15.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "89f75e22c88554844ea2b2faf4ef5fc2e579d3b5", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.15.0.tgz", "fileCount": 5, "integrity": "sha512-yWnriYB4e8G54M5/fAFj7rCIBiKs1HAACaY13kCz6Ku0dezjS9aMcfcdVK2X8Tv2tEV1BPz/wKfQ7WA4S/d8aA==", "signatures": [{"sig": "MEYCIQC3k6xrb2OBobpXWePieIf1KON5Skoth6PuGfSe8ACYPAIhAINipxIkX2ZwlqXGs1079E58/MzoM/8rXC8DGlX9DUMi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUt6QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOOQ/+K/l6MyHnFZGH2022p66tACBX1t79+oZ37KyL2HWs7yQMXxlX\r\nWKLciresTe4PRrqi7fmW4E8a+yyi3ILUR+bZVLrI1NGKMKhnk8vJZRZc2U+O\r\nLZWOJ5/hN7TVwXglmIrF5rg6gDioP6GuaR4aq2LMfqQVfGTG0zLWMY4kOc5C\r\n/vBGGHrABalnhmrY76ci48SDwLnP+e4os6hviCKvFO1dKe4damme9FunCazi\r\nnj7l5leSGaLw2Qo0X1M3WKMi0ajm0thbSXu5065flWW/q6drnyftNCx+R5xY\r\nQ+FKxuIzdHRfVXrghjFbyW5bpBg4QmTB6qp/1kN5XTO7xV84cuBRW1hqQzs3\r\nESMpEHP/cKNNhgb16whyM+wgrhC09WE2+nK5Jaw9yry6DKGU8pLTZt1IMWRO\r\nqM7Hn9vKJF0sbE17hO/BJhcT/T52G0qOcZLtHuG2fYSlF1BC6bZENUt61b6g\r\nolWiyvMlKbk4YjQrk44qDKQIwggJVkJ4kmh/EHE7M4mrEj+QE4DC5Q60rZps\r\nopu+OjLGHwp2juw4bqYnzqG3mH1Qd2N+acnnCCfyrPlEiIzNGYoSHS3bXwJI\r\nhGi6/xsVmZTT8OEby8OtsMQX1WG/GKEpml8g2yDSD83vX1zUx2nqxarePgMS\r\nikxPEfgQN6EOOEMUaI8wc4iHC1MrmN2Ogfo=\r\n=Rzgc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "87cecad2155a2a260effd9cf70d34660204f61f1", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "14.19.1", "_hasShrinkwrap": false, "devDependencies": {"np": "7.6.2", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "typescript": "4.8.2", "@types/jest": "29.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.15.0_1666375312406_0.23344056145264425", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "expect-type", "version": "0.16.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.16.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "8e2d5134dbbd7a3dc009e42c87fcc84fd5671a76", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.16.0.tgz", "fileCount": 5, "integrity": "sha512-wCpFeVBiAPGiYkQZzaqvGuuBnNCHbtnowMOBpBGY8a27XbG8VAit3lklWph1r8VmgsH61mOZqI3NuGm8bZnUlw==", "signatures": [{"sig": "MEUCIBMTONBoZBxzC16Kwy85YnOcka9SIYqribHtGiuh4j9oAiEAsIo/av/GjJTRgkP1OvBKBPWeQErsmSdP9AqySK/RQ7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39947}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "540387ce74fad9205ea376f9d1fa58eff9ba7401", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"np": "8.0.1", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.16.0_1685383137181_0.6878170509651278", "host": "s3://npm-registry-packages"}}, "0.17.0-1": {"name": "expect-type", "version": "0.17.0-1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.0-1", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "21e5a307a4835292f2f52aa9b56bb02cda20e3b9", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.0-1.tgz", "fileCount": 5, "integrity": "sha512-r2ubQsBXoo916ifRzrdVXayJDZEZnBMLk2mBkgvZwINSATyqTNYWDDRaZFAqyh1a4Wz0g3bLUDCmJkmqi8zBQw==", "signatures": [{"sig": "MEUCIHDzq0dFufzjB7bBTNJZeB/9SnTXnuph4ReSSt+NBiXPAiEAk68C2/JsqWFabLxz0Yk9rCohpvgzVjeYTjl7ItvHZro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50625}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "fa81d7307a83e37cd93ae7e79d3a8cf2d9e4fe75", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.0-1_1696182097179_0.31253263533140974", "host": "s3://npm-registry-packages"}}, "0.17.0-2": {"name": "expect-type", "version": "0.17.0-2", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.0-2", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "bffb7799b3777a362ec26957bb277a74802c9057", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.0-2.tgz", "fileCount": 5, "integrity": "sha512-Fad2dldx6QVuAorBCeeXg85IkfhIKlxp879vZqYMwQvqSawZt5IuAulJzL64NPJOmapVBTKXPOILsgfZzbMIVw==", "signatures": [{"sig": "MEQCIHRUQMr6eC51+8+zGhXp2X03s0NncGT4E0cn5uTiI1V5AiAYxGUOL/f1p5V4fU23WsxHdriEKEVThTJSgO8+5MLW8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55173}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "0f664da4abce732826975a6c4d2fae8c2790741e", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.0-2_1696274240651_0.9845149172356231", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "expect-type", "version": "0.17.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "506a5edaa88e4ef810f80c2da87c361879290a51", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.0.tgz", "fileCount": 5, "integrity": "sha512-m7peQshFPL8LMTNvYjh0fjPy0tQgOyi1wtgCt6vnh+iaa+JJPWQT3cf1W2GzBo4v/otDr29RJzJGepGJb+G6hg==", "signatures": [{"sig": "MEYCIQCM7vR8+HUaYd4Vij3dQdBNjCyAuyD9srGZi+kZSkEbtAIhAMFaQ59JVHE9mUY+VkgJh51gpwnXnAlqMcR/TCoXNzcP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55167}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "cdeac9c06f2bbbca14fab0053ebaa17f0150e3ab", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.0_1696298098901_0.06179170524829458", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "expect-type", "version": "0.17.1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.1", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "9290600529f29b4f49217758a7fa806f5848b295", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.1.tgz", "fileCount": 5, "integrity": "sha512-DR6gDDjTnfdVuHCfDBQADOKspW+XJQLV+9Tx9Vnq8JWo/rIurq1Wf+lonG4FnkIOWj4LGi3miVzs1Vacl+zD0A==", "signatures": [{"sig": "MEYCIQCGLrt4QpXVNruGkkLXSdgGoH9gID2kjGVSOe1oH9AzkAIhANwmmN8TDcx86KD5Imb6l8V4AIzyExVXGMuwPjMTnric", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55003}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "fefa43cbd11268767af04ff9d0f8efaba6c42ba9", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.1_1696298806789_0.06470060666416422", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "expect-type", "version": "0.17.2", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.2", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "02520f9e10029aa1214d45c4981fed0f19779465", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.2.tgz", "fileCount": 5, "integrity": "sha512-QHGW/RR4t7WwrAl0IN1W+jIuCtHLak54xr22hrJIbVD2lFki3JEc1+aRLksuOqWf38pgfkQ7ho4b64uV5/j3yA==", "signatures": [{"sig": "MEYCIQCEIx984+j2ugTbULChfESwlrts2ibtyrjP8KIwzZBr5gIhAPbKTlOsxuLMg6oMqdMI3QtcyRQxLIRYqxaKAeAh8DYh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55003}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "95bf9cfb438d07ff69af66e23bb3d288e51257a0", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.2_1696341343445_0.5897536403198045", "host": "s3://npm-registry-packages"}}, "0.17.3-0": {"name": "expect-type", "version": "0.17.3-0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.3-0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "e34ae300597b185e9f5888bbf21213ca710cfd89", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.3-0.tgz", "fileCount": 5, "integrity": "sha512-aURKRI5iltb+sTmVDbv+L5F59/B2fJlUjtpqDUS/HaN+hKAbsJ8xMpDY6GbG/Kc0XYIxd+Lc/TvQwg/8v3Qusg==", "signatures": [{"sig": "MEUCICGqPaV2Z8rOxk5DcZ7X94x6DNv3Iy1MTgWsQbr8oZ3NAiEAnqg2Sqqt6PzbCOeYsUdQuGN0ciOhTfRr7S4SjxM2fqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55521}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "3ce9a7903f5e0871c5862521831e769732a26d51", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.3-0_1696365456102_0.4687882758914983", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "expect-type", "version": "0.17.3", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.17.3", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "np": {"cleanup": false}, "dist": {"shasum": "30623b0c66f6483e89b31272848776db662778e6", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.17.3.tgz", "fileCount": 5, "integrity": "sha512-K0ZdZJ97jiAtaOwhEHHz/f0N6Xbj5reRz5g6+5BO7+OvqQ7PMQz0/c8bFSJs1zPotNJL5HJaC6t6lGPEAtGyOw==", "signatures": [{"sig": "MEUCIQDnCdN3snTRwU5FZgnWO4cyyeZgLASrwZqCcYDeIxtl2wIgBqMdEoEBy0I0dU5j93eQcTkbCJw9ASSg0Q6BKryXSC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56120}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "gitHead": "c23f0c127a19846910843cbc0f30567d84e0926b", "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^8.0.4", "jest": "28.1.3", "eslint": "8.23.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.0.0", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.0.1-2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.17.3_1696366604443_0.38845131215100914", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "expect-type", "version": "0.18.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.18.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "16f62ff9d04950d7d286ed9179a52bc3ea366f2b", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.18.0.tgz", "fileCount": 5, "integrity": "sha512-xjKoyyDLoia2h1WF+vwV8AmEpQ0drGW0InRgyywAHyOC+XSPYMxGoMXSwPjXs46D8FgLmp32sHMd1KrVingDuQ==", "signatures": [{"sig": "MEQCIE2+xATm+T8a0PnJVbua0D9DQAhRqRXrOcL16yso1U5VAiB4OD4BugI2JmX/WdpLygIc173ODPVWSpDXdbnNckd/BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56738}, "main": "dist/index.js", "_from": "file:expect-type-0.18.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && eslint .", "test": "jest", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --ext '.ts,.js,.md'"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/3208f251c1e0294216b7282c22585e68/expect-type-0.18.0.tgz", "_integrity": "sha512-xjKoyyDLoia2h1WF+vwV8AmEpQ0drGW0InRgyywAHyOC+XSPYMxGoMXSwPjXs46D8FgLmp32sHMd1KrVingDuQ==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) [![](https://byob.yarr.is/mmkal/expect-type/coverage)](https://github.com/mmkal/expect-type/actions/workflows/c", "directories": {}, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.10.2", "devDependencies": {"np": "^10.0.0", "jest": "28.1.3", "eslint": "8.53.0", "ts-jest": "28.0.8", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/jest": "29.5.7", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.18.0_1708982066942_0.562597528425199", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "expect-type", "version": "0.19.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.19.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "72eca0ea90f34fa793c70f44adc1974c0e031914", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.19.0.tgz", "fileCount": 5, "integrity": "sha512-piv9wz3IrAG4Wnk2A+n2VRCHieAyOSxrRLU872Xo6nyn39kYXKDALk4OcqnvLRnFvkz659CnWC8MWZLuuQnoqg==", "signatures": [{"sig": "MEUCIQCfkdy/sJulFU2CO8Mt1mGhn9FZmbZYorRxw9B1yCIw8AIgHVQDMqBsO6RVmgsUPPp45sS1y5/Q0EOVdFYEzzzqJsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82826}, "main": "dist/index.js", "_from": "file:expect-type-0.19.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/5f62e1d852b39600b4b3decfa84b0bc3/expect-type-0.19.0.tgz", "_integrity": "sha512-piv9wz3IrAG4Wnk2A+n2VRCHieAyOSxrRLU872Xo6nyn39kYXKDALk4OcqnvLRnFvkz659CnWC8MWZLuuQnoqg==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type)", "directories": {}, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.10.2", "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^1.4.0", "ts-morph": "16.0.0", "strip-ansi": "6.0.1", "typescript": "4.8.2", "@types/node": "^14.0.0", "eslint-plugin-mmkal": "0.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.19.0_1711054785247_0.5921993182108582", "host": "s3://npm-registry-packages"}}, "0.20.0-0": {"name": "expect-type", "version": "0.20.0-0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.20.0-0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "3b7c76e3f97f608c414083d4b4f610868d888ff8", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.20.0-0.tgz", "fileCount": 13, "integrity": "sha512-kKaIqJJIKTRwkU6F/ikQaFuYFTiIO46iPsPoMPvcGCOuaIvYE8rvjMSaO7vo77BDjHtGJJCD5hrWVATasiqbHQ==", "signatures": [{"sig": "MEUCICJifbCwWEftkeEM2/28Cqf+5RMGnjPdxfmc9QcI4QBHAiEAxSstHVCkRMjAOGcEwQPXADOGCeY9V9JkmJ9Coa72fE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105583}, "main": "dist/index.js", "_from": "file:expect-type-0.20.0-0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/134f13db23ba4aad765b249411ded844/expect-type-0.20.0-0.tgz", "_integrity": "sha512-kKaIqJJIKTRwkU6F/ikQaFuYFTiIO46iPsPoMPvcGCOuaIvYE8rvjMSaO7vo77BDjHtGJJCD5hrWVATasiqbHQ==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^2.0.0", "ts-morph": "16.0.0", "pkg-pr-new": "0.0.20", "strip-ansi": "7.1.0", "typescript": "5.5.4", "@types/node": "^20.0.0", "eslint-plugin-mmkal": "0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.20.0-0_1723572711040_0.9572178931281972", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "expect-type", "version": "0.20.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@0.20.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "be0db937492df1db368ad772b977336f3395a152", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-0.20.0.tgz", "fileCount": 13, "integrity": "sha512-uHaC9LYNv6BcW+8SvXcwUUDCrrUxt3GSa61DFvTHj8JC+M0hekMFBwMlCarLQDk5bbpZ2vStpnQPIwRuV98YMw==", "signatures": [{"sig": "MEQCIDyZWSCAXEpDo7mF+8k8aXpTMVrbTB3y+fq901kOO4uzAiBKzwgvMU6Y3XyyiBODjR4gtwOD+aioLLWyMEJipSqrTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108848}, "main": "dist/index.js", "_from": "file:expect-type-0.20.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/d2b22b4cfd4a4a318b64d8427313a39c/expect-type-0.20.0.tgz", "_integrity": "sha512-uHaC9LYNv6BcW+8SvXcwUUDCrrUxt3GSa61DFvTHj8JC+M0hekMFBwMlCarLQDk5bbpZ2vStpnQPIwRuV98YMw==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^2.0.0", "ts-morph": "23.0.0", "pkg-pr-new": "0.0.20", "strip-ansi": "7.1.0", "typescript": "5.5.4", "@types/node": "^20.0.0", "eslint-plugin-mmkal": "0.8.0", "@arethetypeswrong/cli": "0.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_0.20.0_1724170569054_0.41247010547799223", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.0": {"name": "expect-type", "version": "1.0.0-rc.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@1.0.0-rc.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "cff562950ca5b676dad89e8048bebbf4a6a83004", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.0.0-rc.0.tgz", "fileCount": 13, "integrity": "sha512-u25cmq7drGh3cHFxp4s1gAmKhBbZkel8kNwiArIZBWlqJxiDUdrJjCr9/23XksfWaB7JtNuBmqLF1nm0jrliDQ==", "signatures": [{"sig": "MEQCIGd7Q36Bq1HexVk/boSiKZKDGDOTwimQpgKmkqXMqKHIAiAlu0gxkYRY77DUN3UISihRzBIh6Ix/WbQIBl7xB9CUdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108852}, "main": "dist/index.js", "_from": "file:expect-type-1.0.0-rc.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/f9ed38e003963767740d8c9f560469b2/expect-type-1.0.0-rc.0.tgz", "_integrity": "sha512-u25cmq7drGh3cHFxp4s1gAmKhBbZkel8kNwiArIZBWlqJxiDUdrJjCr9/23XksfWaB7JtNuBmqLF1nm0jrliDQ==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^2.0.0", "ts-morph": "23.0.0", "pkg-pr-new": "0.0.24", "strip-ansi": "7.1.0", "typescript": "5.5.4", "@types/node": "^20.0.0", "eslint-plugin-mmkal": "0.9.0", "@arethetypeswrong/cli": "0.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_1.0.0-rc.0_1725932883850_0.021558536147541663", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "expect-type", "version": "1.0.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@1.0.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "dadeec4ddcc3965a962e4a79ce744927f57c5f8b", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.0.0.tgz", "fileCount": 13, "integrity": "sha512-WcdwgWmGAE48kSHjxPVLriBuvJdJdi1VKTh7HmHvcm6WPdIT1Z04wUVX8BLiqnq0Rz5SnfJ+P0kM1RBqSLLoPQ==", "signatures": [{"sig": "MEUCIGp7Q1snLsNS7XKuysvi1gP/Z4GDyQnXbIy8pkMKziHYAiEA4YfRxWhPu/gU0FYiSzNKmP2lQLyVqxaO1TpHV7J/MUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108892}, "main": "dist/index.js", "_from": "file:expect-type-1.0.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/ffbe8dfa49e37b73b74fb283e848cece/expect-type-1.0.0.tgz", "_integrity": "sha512-WcdwgWmGAE48kSHjxPVLriBuvJdJdi1VKTh7HmHvcm6WPdIT1Z04wUVX8BLiqnq0Rz5SnfJ+P0kM1RBqSLLoPQ==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^2.0.0", "ts-morph": "23.0.0", "pkg-pr-new": "0.0.24", "strip-ansi": "7.1.0", "typescript": "5.6.2", "@types/node": "^20.0.0", "eslint-plugin-mmkal": "0.9.0", "@arethetypeswrong/cli": "0.16.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_1.0.0_1727979851974_0.5952581502748946", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "expect-type", "version": "1.1.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@1.1.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "a146e414250d13dfc49eafcfd1344a4060fa4c75", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-bFi65yM+xZgk+u/KRIpekdSYkTB5W1pEf0Lt8Q8Msh7b+eQ7LXVtIB1Bkm4fvclDEL1b2CZkMhv2mOeF8tMdkA==", "signatures": [{"sig": "MEUCIFDtlpYpmEjsqVDS22dQSsD9zjNsGEZ9r7YtR2oDuR4fAiEApivM+JoinDAr9PN66eILXyURFCmMEv+qwV6je2h1Z5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110105}, "main": "dist/index.js", "_from": "file:expect-type-1.1.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/2f1d03b2f28d124984107e87052365a4/expect-type-1.1.0.tgz", "_integrity": "sha512-bFi65yM+xZgk+u/KRIpekdSYkTB5W1pEf0Lt8Q8Msh7b+eQ7LXVtIB1Bkm4fvclDEL1b2CZkMhv2mOeF8tMdkA==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^10.0.0", "eslint": "^8.57.0", "vitest": "^2.1.2", "ts-morph": "23.0.0", "@vitest/ui": "^2.0.5", "pkg-pr-new": "0.0.24", "strip-ansi": "7.1.0", "typescript": "5.6.2", "@types/node": "^20.0.0", "eslint-plugin-mmkal": "0.9.0", "@arethetypeswrong/cli": "0.16.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_1.1.0_1728500572481_0.6323905882072627", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "expect-type", "version": "1.2.0", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@1.2.0", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "b52a0a1117260f5a8dcf33aef66365be18c13415", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-80F22aiJ3GLyVnS/B3HzgR6RelZVumzj9jkL0Rhz4h0xYbNW9PjlQz5h3J/SShErbXBc295vseR4/MIbVmUbeA==", "signatures": [{"sig": "MEUCIE1+zF3ajobSIsmFKqFY4rSnFNLuvnK/8H2Uo/STIuXxAiEAykxGRbyBDr3XX4YO85WKacAfTqXE1fPxLcvqMwl9urE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 116081}, "main": "dist/index.js", "_from": "file:expect-type-1.2.0.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/60ec4418c5979a6c740dfe717de16c4b/expect-type-1.2.0.tgz", "_integrity": "sha512-80F22aiJ3GLyVnS/B3HzgR6RelZVumzj9jkL0Rhz4h0xYbNW9PjlQz5h3J/SShErbXBc295vseR4/MIbVmUbeA==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^10.2.0", "eslint": "^8.57.0", "vitest": "^3.0.0", "ts-morph": "23.0.0", "@vitest/ui": "^3.0.0", "pkg-pr-new": "0.0.39", "strip-ansi": "7.1.0", "typescript": "5.7.3", "@types/node": "^22.0.0", "eslint-plugin-mmkal": "0.9.0", "@arethetypeswrong/cli": "0.17.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_1.2.0_1740768099005_0.5731115792128736", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.1": {"name": "expect-type", "version": "1.2.1", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "license": "Apache-2.0", "_id": "expect-type@1.2.1", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "homepage": "https://github.com/mmkal/expect-type#readme", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "dist": {"shasum": "af76d8b357cf5fa76c41c09dafb79c549e75f71f", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.1.tgz", "fileCount": 13, "integrity": "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==", "signatures": [{"sig": "MEUCIQCCQzb3wioxr2QcULDxFw95TWzsnnyoywt27Iy6gcGm8gIgacjhXt63gaDmHVizoU//K0165jxmKykvKxgP6JlLWSc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 116102}, "main": "dist/index.js", "_from": "file:expect-type-1.2.1.tgz", "types": "dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"lint": "tsc && pnpm eslint .", "test": "vitest run", "build": "tsc -p tsconfig.lib.json", "eslint": "eslint --max-warnings 0", "type-check": "tsc", "arethetypeswrong": "attw --pack"}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/611d8b51590a2fce57f0872f7d3719a7/expect-type-1.2.1.tgz", "_integrity": "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==", "repository": {"url": "git+https://github.com/mmkal/expect-type.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"np": "^10.2.0", "eslint": "^8.57.0", "vitest": "^3.0.0", "ts-morph": "23.0.0", "@vitest/ui": "^3.0.0", "pkg-pr-new": "0.0.39", "strip-ansi": "7.1.0", "typescript": "5.7.3", "@types/node": "^22.0.0", "eslint-plugin-mmkal": "0.9.0", "@arethetypeswrong/cli": "0.17.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-type_1.2.1_1743435873847_0.6293888578898277", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.2": {"name": "expect-type", "version": "1.2.2", "engines": {"node": ">=12.0.0"}, "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "homepage": "https://github.com/mmkal/expect-type#readme", "repository": {"type": "git", "url": "git+https://github.com/mmkal/expect-type.git"}, "license": "Apache-2.0", "main": "dist/index.js", "types": "dist/index.d.ts", "devDependencies": {"@arethetypeswrong/cli": "0.17.3", "@types/node": "^22.0.0", "@typescript/native-preview": "7.0.0-dev.20250527.1", "@vitest/ui": "^3.0.0", "eslint": "^8.57.0", "eslint-plugin-mmkal": "0.9.0", "np": "^10.2.0", "pkg-pr-new": "0.0.39", "strip-ansi": "7.1.0", "ts-morph": "23.0.0", "typescript": "5.8.3", "vitest": "^3.0.0"}, "scripts": {"eslint": "eslint --max-warnings 0", "lint": "tsc && pnpm eslint .", "type-check": "tsc", "build": "tsc -p tsconfig.lib.json", "arethetypeswrong": "attw --pack", "test": "vitest run"}, "_id": "expect-type@1.2.2", "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "_integrity": "sha512-JhFGDVJ7tmDJItKhYgJCGLOWjuK9vPxiXoUFLwLDc99NlmklilbiQJwoctZtt13+xMw91MCk/REan6MWHqDjyA==", "_resolved": "/private/var/folders/3x/kygz_jhs6wv3s95jhds9z0t80000gn/T/8c950712150d27a1eb3742657e207cfb/expect-type-1.2.2.tgz", "_from": "file:expect-type-1.2.2.tgz", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-JhFGDVJ7tmDJItKhYgJCGLOWjuK9vPxiXoUFLwLDc99NlmklilbiQJwoctZtt13+xMw91MCk/REan6MWHqDjyA==", "shasum": "c030a329fb61184126c8447585bc75a7ec6fbff3", "tarball": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.2.tgz", "fileCount": 14, "unpackedSize": 119253, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGpnJOGbTmR3dMBF06Mio+7UcINPhg5XP44N+5oWifRDAiBRJVr445U7NdrqRW0IAFWft+04MFFEn1TmZA+wwL2fwQ=="}]}, "_npmUser": {"name": "mmkale", "email": "<EMAIL>", "actor": {"name": "mmkale", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/expect-type_1.2.2_1751761593391_0.15715409499047484"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-03-03T03:02:44.461Z", "modified": "2025-07-06T00:26:33.825Z", "0.3.2-master-2020-03-02-22-01-34.76": "2020-03-03T03:02:44.561Z", "0.3.2-master-2020-03-02-22-57-03.79": "2020-03-03T03:58:48.937Z", "0.3.1": "2020-03-03T04:05:08.420Z", "0.4.0": "2020-03-05T05:26:37.845Z", "0.4.1-master-2020-03-06-00-05-12.11": "2020-03-06T05:06:43.994Z", "0.4.1": "2020-03-06T05:08:30.598Z", "0.4.2": "2020-03-07T18:33:42.931Z", "0.4.3": "2020-03-22T19:30:15.362Z", "0.4.4": "2020-03-22T19:38:43.174Z", "0.4.5": "2020-03-24T13:52:54.177Z", "0.4.6": "2020-03-30T02:54:48.338Z", "0.5.0": "2020-04-05T19:19:36.682Z", "0.5.1": "2020-04-05T19:25:26.271Z", "0.5.2": "2020-04-05T19:43:39.741Z", "0.5.3": "2020-04-11T15:19:43.809Z", "0.5.4": "2020-04-11T21:09:05.881Z", "0.5.5": "2020-04-26T17:11:12.269Z", "0.6.0": "2020-05-04T10:47:26.135Z", "0.7.0": "2020-05-04T12:18:04.764Z", "0.7.1": "2020-05-06T22:43:54.538Z", "0.7.2": "2020-05-08T03:39:54.546Z", "0.7.3": "2020-05-13T13:24:01.657Z", "0.7.4": "2020-05-14T22:03:37.408Z", "0.7.5": "2020-06-29T01:08:25.197Z", "0.7.6": "2020-06-29T12:45:30.480Z", "0.7.7": "2020-07-09T00:56:12.760Z", "0.7.8": "2020-07-09T01:47:07.773Z", "0.7.9": "2020-07-28T22:24:58.137Z", "0.7.10": "2020-09-18T16:56:59.216Z", "0.7.11": "2020-10-01T14:48:24.992Z", "0.8.0": "2020-10-05T22:38:39.070Z", "0.9.0": "2020-10-27T16:19:02.367Z", "0.9.1": "2020-11-26T17:06:49.795Z", "0.9.2": "2020-11-28T19:10:11.608Z", "0.10.0": "2020-12-03T19:10:35.491Z", "0.11.0": "2020-12-09T13:25:32.935Z", "0.12.0": "2021-07-03T15:24:58.108Z", "0.13.0": "2021-10-16T13:17:28.115Z", "0.14.0-0": "2022-09-05T22:46:05.923Z", "0.14.0": "2022-09-06T10:50:00.646Z", "0.14.1": "2022-09-07T12:37:26.036Z", "0.14.2": "2022-09-07T12:39:42.371Z", "0.15.0": "2022-10-21T18:01:52.613Z", "0.16.0": "2023-05-29T17:58:57.352Z", "0.17.0-1": "2023-10-01T17:41:37.446Z", "0.17.0-2": "2023-10-02T19:17:20.868Z", "0.17.0": "2023-10-03T01:54:59.126Z", "0.17.1": "2023-10-03T02:06:46.982Z", "0.17.2": "2023-10-03T13:55:43.699Z", "0.17.3-0": "2023-10-03T20:37:36.319Z", "0.17.3": "2023-10-03T20:56:44.698Z", "0.18.0": "2024-02-26T21:14:27.115Z", "0.19.0": "2024-03-21T20:59:45.476Z", "0.20.0-0": "2024-08-13T18:11:51.180Z", "0.20.0": "2024-08-20T16:16:09.220Z", "1.0.0-rc.0": "2024-09-10T01:48:04.073Z", "1.0.0": "2024-10-03T18:24:12.161Z", "1.1.0": "2024-10-09T19:02:52.657Z", "1.2.0": "2025-02-28T18:41:39.203Z", "1.2.1": "2025-03-31T15:44:34.015Z", "1.2.2": "2025-07-06T00:26:33.572Z"}, "bugs": {"url": "https://github.com/mmkal/expect-type/issues"}, "license": "Apache-2.0", "homepage": "https://github.com/mmkal/expect-type#readme", "keywords": ["typescript", "type-check", "assert", "types", "typings", "test", "testing"], "repository": {"type": "git", "url": "git+https://github.com/mmkal/expect-type.git"}, "description": "[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml) ![npm](https://img.shields.io/npm/dt/expect-type) [![X (formerly Twitter) Follow](https://img.shields.io/twitte", "maintainers": [{"name": "mmkale", "email": "<EMAIL>"}], "readme": "# expect-type\n\n[![CI](https://github.com/mmkal/expect-type/actions/workflows/ci.yml/badge.svg)](https://github.com/mmkal/expect-type/actions/workflows/ci.yml)\n![npm](https://img.shields.io/npm/dt/expect-type)\n[![X (formerly Twitter) Follow](https://img.shields.io/twitter/follow/mmkal)](https://x.com/mmkalmmkal)\n\nCompile-time tests for types. Useful to make sure types don't regress into being overly permissive as changes go in over time.\n\nSimilar to `expect`, but with type-awareness. Gives you access to several type-matchers that let you make assertions about the form of a reference or generic type parameter.\n\n```ts\nimport {expectTypeOf} from 'expect-type'\nimport {foo, bar} from '../foo'\n\n// make sure `foo` has type {a: number}\nexpectTypeOf(foo).toEqualTypeOf<{a: number}>()\n\n// make sure `bar` is a function taking a string:\nexpectTypeOf(bar).parameter(0).toBeString()\nexpectTypeOf(bar).returns.not.toBeAny()\n```\n\nIt can be used in your existing test files (and is actually [built in to vitest](https://vitest.dev/guide/testing-types)). Or it can be used in any other type-checked file you'd like - it's built into existing tooling with no dependencies. No extra build step, cli tool, IDE extension, or lint plugin is needed. Just import the function and start writing tests. Failures will be at compile time - they'll appear in your IDE and when you run `tsc`.\n\nSee below for lots more examples.\n\n## Contents\n<!-- codegen:start {preset: markdownTOC, minDepth: 2, maxDepth: 5} -->\n- [Contents](#contents)\n- [Installation and usage](#installation-and-usage)\n- [Documentation](#documentation)\n   - [Features](#features)\n   - [Why is my assertion failing?](#why-is-my-assertion-failing)\n   - [Why is `.toMatchTypeOf` deprecated?](#why-is-tomatchtypeof-deprecated)\n   - [Internal type helpers](#internal-type-helpers)\n   - [Error messages](#error-messages)\n      - [Concrete \"expected\" objects vs type arguments](#concrete-expected-objects-vs-type-arguments)\n   - [Overloaded functions](#overloaded-functions)\n   - [Within test frameworks](#within-test-frameworks)\n   - [Vitest](#vitest)\n      - [Jest & `eslint-plugin-jest`](#jest--eslint-plugin-jest)\n   - [Limitations](#limitations)\n- [Similar projects](#similar-projects)\n   - [Comparison](#comparison)\n- [TypeScript backwards-compatibility](#typescript-backwards-compatibility)\n- [Contributing](#contributing)\n   - [Documentation of limitations through tests](#documentation-of-limitations-through-tests)\n<!-- codegen:end -->\n\n## Installation and usage\n\n```cli\nnpm install expect-type --save-dev\n```\n\n```typescript\nimport {expectTypeOf} from 'expect-type'\n```\n\n## Documentation\n\nThe `expectTypeOf` method takes a single argument or a generic type parameter. Neither it nor the functions chained off its return value have any meaningful runtime behaviour. The assertions you write will be _compile-time_ errors if they don't hold true.\n\n### Features\n\n<!-- codegen:start {preset: markdownFromTests, source: test/usage.test.ts} -->\nCheck an object's type with `.toEqualTypeOf`:\n\n```typescript\nexpectTypeOf({a: 1}).toEqualTypeOf<{a: number}>()\n```\n\n`.toEqualTypeOf` can check that two concrete objects have equivalent types (note: when these assertions _fail_, the error messages can be less informative vs the generic type argument syntax above - see [error messages docs](#error-messages)):\n\n```typescript\nexpectTypeOf({a: 1}).toEqualTypeOf({a: 1})\n```\n\n`.toEqualTypeOf` succeeds for objects with different values, but the same type:\n\n```typescript\nexpectTypeOf({a: 1}).toEqualTypeOf({a: 2})\n```\n\n`.toEqualTypeOf` fails on excess properties:\n\n```typescript\n// @ts-expect-error\nexpectTypeOf({a: 1, b: 1}).toEqualTypeOf<{a: number}>()\n```\n\nTo allow for extra properties on an object type, use `.toMatchObjectType`. This is a strict check, but only on the subset of keys that are in the expected type:\n\n```typescript\nexpectTypeOf({a: 1, b: 1}).toMatchObjectType<{a: number}>()\n```\n\n`.toMatchObjectType` can check partial matches on deeply nested objects:\n\n```typescript\nconst user = {\n  email: '<EMAIL>',\n  name: 'John Doe',\n  address: {street: '123 2nd St', city: 'New York', zip: '10001', state: 'NY', country: 'USA'},\n}\n\nexpectTypeOf(user).toMatchObjectType<{name: string; address: {city: string}}>()\n```\n\nTo check that a type extends another type, use `.toExtend`:\n\n```typescript\nexpectTypeOf('some string').toExtend<string | boolean>()\n// @ts-expect-error\nexpectTypeOf({a: 1}).toExtend<{b: number}>()\n```\n\n`.toExtend` can be used with object types, but `.toMatchObjectType` is usually a better choice when dealing with objects, since it's stricter:\n\n```typescript\nexpectTypeOf({a: 1, b: 2}).toExtend<{a: number}>() // avoid this\nexpectTypeOf({a: 1, b: 2}).toMatchObjectType<{a: number}>() // prefer this\n```\n\n`.toEqualTypeOf`, `.toMatchObjectType`, and `.toExtend` all fail on missing properties:\n\n```typescript\n// @ts-expect-error\nexpectTypeOf({a: 1}).toEqualTypeOf<{a: number; b: number}>()\n// @ts-expect-error\nexpectTypeOf({a: 1}).toMatchObjectType<{a: number; b: number}>()\n// @ts-expect-error\nexpectTypeOf({a: 1}).toExtend<{a: number; b: number}>()\n```\n\nAnother example of the difference between `.toExtend`, `.toMatchObjectType`, and `.toEqualTypeOf`. `.toExtend` can be used for \"is-a\" relationships:\n\n```typescript\ntype Fruit = {type: 'Fruit'; edible: boolean}\ntype Apple = {type: 'Fruit'; name: 'Apple'; edible: true}\n\nexpectTypeOf<Apple>().toExtend<Fruit>()\n\n// @ts-expect-error - the `editable` property isn't an exact match. In `Apple`, it's `true`, which extends `boolean`, but they're not identical.\nexpectTypeOf<Apple>().toMatchObjectType<Fruit>()\n\n// @ts-expect-error - Apple is not an identical type to Fruit, it's a subtype\nexpectTypeOf<Apple>().toEqualTypeOf<Fruit>()\n\n// @ts-expect-error - Apple is a Fruit, but not vice versa\nexpectTypeOf<Fruit>().toExtend<Apple>()\n```\n\nAssertions can be inverted with `.not`:\n\n```typescript\nexpectTypeOf({a: 1}).not.toExtend<{b: 1}>()\nexpectTypeOf({a: 1}).not.toMatchObjectType<{b: 1}>()\n```\n\n`.not` can be easier than relying on `// @ts-expect-error`:\n\n```typescript\ntype Fruit = {type: 'Fruit'; edible: boolean}\ntype Apple = {type: 'Fruit'; name: 'Apple'; edible: true}\n\nexpectTypeOf<Apple>().toExtend<Fruit>()\n\nexpectTypeOf<Fruit>().not.toExtend<Apple>()\nexpectTypeOf<Apple>().not.toEqualTypeOf<Fruit>()\n```\n\nCatch any/unknown/never types:\n\n```typescript\nexpectTypeOf<unknown>().toBeUnknown()\nexpectTypeOf<any>().toBeAny()\nexpectTypeOf<never>().toBeNever()\n\n// @ts-expect-error\nexpectTypeOf<never>().toBeNumber()\n```\n\n`.toEqualTypeOf` distinguishes between deeply-nested `any` and `unknown` properties:\n\n```typescript\nexpectTypeOf<{deeply: {nested: any}}>().not.toEqualTypeOf<{deeply: {nested: unknown}}>()\n```\n\nYou can test for basic JavaScript types:\n\n```typescript\nexpectTypeOf(() => 1).toBeFunction()\nexpectTypeOf({}).toBeObject()\nexpectTypeOf([]).toBeArray()\nexpectTypeOf('').toBeString()\nexpectTypeOf(1).toBeNumber()\nexpectTypeOf(true).toBeBoolean()\nexpectTypeOf(() => {}).returns.toBeVoid()\nexpectTypeOf(Promise.resolve(123)).resolves.toBeNumber()\nexpectTypeOf(Symbol(1)).toBeSymbol()\nexpectTypeOf(1n).toBeBigInt()\n```\n\n`.toBe...` methods allow for types that extend the expected type:\n\n```typescript\nexpectTypeOf<number>().toBeNumber()\nexpectTypeOf<1>().toBeNumber()\n\nexpectTypeOf<any[]>().toBeArray()\nexpectTypeOf<number[]>().toBeArray()\n\nexpectTypeOf<string>().toBeString()\nexpectTypeOf<'foo'>().toBeString()\n\nexpectTypeOf<boolean>().toBeBoolean()\nexpectTypeOf<true>().toBeBoolean()\n\nexpectTypeOf<bigint>().toBeBigInt()\nexpectTypeOf<0n>().toBeBigInt()\n```\n\n`.toBe...` methods protect against `any`:\n\n```typescript\nconst goodIntParser = (s: string) => Number.parseInt(s, 10)\nconst badIntParser = (s: string) => JSON.parse(s) // uh-oh - works at runtime if the input is a number, but return 'any'\n\nexpectTypeOf(goodIntParser).returns.toBeNumber()\n// @ts-expect-error - if you write a test like this, `.toBeNumber()` will let you know your implementation returns `any`.\nexpectTypeOf(badIntParser).returns.toBeNumber()\n```\n\nNullable types:\n\n```typescript\nexpectTypeOf(undefined).toBeUndefined()\nexpectTypeOf(undefined).toBeNullable()\nexpectTypeOf(undefined).not.toBeNull()\n\nexpectTypeOf(null).toBeNull()\nexpectTypeOf(null).toBeNullable()\nexpectTypeOf(null).not.toBeUndefined()\n\nexpectTypeOf<1 | undefined>().toBeNullable()\nexpectTypeOf<1 | null>().toBeNullable()\nexpectTypeOf<1 | undefined | null>().toBeNullable()\n```\n\nMore `.not` examples:\n\n```typescript\nexpectTypeOf(1).not.toBeUnknown()\nexpectTypeOf(1).not.toBeAny()\nexpectTypeOf(1).not.toBeNever()\nexpectTypeOf(1).not.toBeNull()\nexpectTypeOf(1).not.toBeUndefined()\nexpectTypeOf(1).not.toBeNullable()\nexpectTypeOf(1).not.toBeBigInt()\n```\n\nDetect assignability of unioned types:\n\n```typescript\nexpectTypeOf<number>().toExtend<string | number>()\nexpectTypeOf<string | number>().not.toExtend<number>()\n```\n\nUse `.extract` and `.exclude` to narrow down complex union types:\n\n```typescript\ntype ResponsiveProp<T> = T | T[] | {xs?: T; sm?: T; md?: T}\nconst getResponsiveProp = <T>(_props: T): ResponsiveProp<T> => ({})\ntype CSSProperties = {margin?: string; padding?: string}\n\nconst cssProperties: CSSProperties = {margin: '1px', padding: '2px'}\n\nexpectTypeOf(getResponsiveProp(cssProperties))\n  .exclude<unknown[]>()\n  .exclude<{xs?: unknown}>()\n  .toEqualTypeOf<CSSProperties>()\n\nexpectTypeOf(getResponsiveProp(cssProperties))\n  .extract<unknown[]>()\n  .toEqualTypeOf<CSSProperties[]>()\n\nexpectTypeOf(getResponsiveProp(cssProperties))\n  .extract<{xs?: any}>()\n  .toEqualTypeOf<{xs?: CSSProperties; sm?: CSSProperties; md?: CSSProperties}>()\n\nexpectTypeOf<ResponsiveProp<number>>().exclude<number | number[]>().toHaveProperty('sm')\nexpectTypeOf<ResponsiveProp<number>>().exclude<number | number[]>().not.toHaveProperty('xxl')\n```\n\n`.extract` and `.exclude` return never if no types remain after exclusion:\n\n```typescript\ntype Person = {name: string; age: number}\ntype Customer = Person & {customerId: string}\ntype Employee = Person & {employeeId: string}\n\nexpectTypeOf<Customer | Employee>().extract<{foo: string}>().toBeNever()\nexpectTypeOf<Customer | Employee>().exclude<{name: string}>().toBeNever()\n```\n\nUse `.pick` to pick a set of properties from an object:\n\n```typescript\ntype Person = {name: string; age: number}\n\nexpectTypeOf<Person>().pick<'name'>().toEqualTypeOf<{name: string}>()\n```\n\nUse `.omit` to remove a set of properties from an object:\n\n```typescript\ntype Person = {name: string; age: number}\n\nexpectTypeOf<Person>().omit<'name'>().toEqualTypeOf<{age: number}>()\n```\n\nMake assertions about object properties:\n\n```typescript\nconst obj = {a: 1, b: ''}\n\n// check that properties exist (or don't) with `.toHaveProperty`\nexpectTypeOf(obj).toHaveProperty('a')\nexpectTypeOf(obj).not.toHaveProperty('c')\n\n// check types of properties\nexpectTypeOf(obj).toHaveProperty('a').toBeNumber()\nexpectTypeOf(obj).toHaveProperty('b').toBeString()\nexpectTypeOf(obj).toHaveProperty('a').not.toBeString()\n```\n\n`.toEqualTypeOf` can be used to distinguish between functions:\n\n```typescript\ntype NoParam = () => void\ntype HasParam = (s: string) => void\n\nexpectTypeOf<NoParam>().not.toEqualTypeOf<HasParam>()\n```\n\nBut often it's preferable to use `.parameters` or `.returns` for more specific function assertions:\n\n```typescript\ntype NoParam = () => void\ntype HasParam = (s: string) => void\n\nexpectTypeOf<NoParam>().parameters.toEqualTypeOf<[]>()\nexpectTypeOf<NoParam>().returns.toBeVoid()\n\nexpectTypeOf<HasParam>().parameters.toEqualTypeOf<[string]>()\nexpectTypeOf<HasParam>().returns.toBeVoid()\n```\n\nUp to ten overloads will produce union types for `.parameters` and `.returns`:\n\n```typescript\ntype Factorize = {\n  (input: number): number[]\n  (input: bigint): bigint[]\n}\n\nexpectTypeOf<Factorize>().parameters.not.toEqualTypeOf<[number]>()\nexpectTypeOf<Factorize>().parameters.toEqualTypeOf<[number] | [bigint]>()\nexpectTypeOf<Factorize>().returns.toEqualTypeOf<number[] | bigint[]>()\n\nexpectTypeOf<Factorize>().parameter(0).toEqualTypeOf<number | bigint>()\n```\n\nNote that these aren't exactly like TypeScript's built-in Parameters<...> and ReturnType<...>:\n\nThe TypeScript builtins simply choose a single overload (see the [Overloaded functions](#overloaded-functions) section for more information)\n\n```typescript\ntype Factorize = {\n  (input: number): number[]\n  (input: bigint): bigint[]\n}\n\n// overload using `number` is ignored!\nexpectTypeOf<Parameters<Factorize>>().toEqualTypeOf<[bigint]>()\nexpectTypeOf<ReturnType<Factorize>>().toEqualTypeOf<bigint[]>()\n```\n\nMore examples of ways to work with functions - parameters using `.parameter(n)` or `.parameters`, and return values using `.returns`:\n\n```typescript\nconst f = (a: number) => [a, a]\n\nexpectTypeOf(f).toBeFunction()\n\nexpectTypeOf(f).toBeCallableWith(1)\nexpectTypeOf(f).not.toBeAny()\nexpectTypeOf(f).returns.not.toBeAny()\nexpectTypeOf(f).returns.toEqualTypeOf([1, 2])\nexpectTypeOf(f).returns.toEqualTypeOf([1, 2, 3])\nexpectTypeOf(f).parameter(0).not.toEqualTypeOf('1')\nexpectTypeOf(f).parameter(0).toEqualTypeOf(1)\nexpectTypeOf(1).parameter(0).toBeNever()\n\nconst twoArgFunc = (a: number, b: string) => ({a, b})\n\nexpectTypeOf(twoArgFunc).parameters.toEqualTypeOf<[number, string]>()\n```\n\n`.toBeCallableWith` allows for overloads. You can also use it to narrow down the return type for given input parameters.:\n\n```typescript\ntype Factorize = {\n  (input: number): number[]\n  (input: bigint): bigint[]\n}\n\nexpectTypeOf<Factorize>().toBeCallableWith(6)\nexpectTypeOf<Factorize>().toBeCallableWith(6n)\n```\n\n`.toBeCallableWith` returns a type that can be used to narrow down the return type for given input parameters.:\n\n```typescript\ntype Factorize = {\n  (input: number): number[]\n  (input: bigint): bigint[]\n}\nexpectTypeOf<Factorize>().toBeCallableWith(6).returns.toEqualTypeOf<number[]>()\nexpectTypeOf<Factorize>().toBeCallableWith(6n).returns.toEqualTypeOf<bigint[]>()\n```\n\n`.toBeCallableWith` can be used to narrow down the parameters of a function:\n\n```typescript\ntype Delete = {\n  (path: string): void\n  (paths: string[], options?: {force: boolean}): void\n}\n\nexpectTypeOf<Delete>().toBeCallableWith('abc').parameters.toEqualTypeOf<[string]>()\nexpectTypeOf<Delete>()\n  .toBeCallableWith(['abc', 'def'], {force: true})\n  .parameters.toEqualTypeOf<[string[], {force: boolean}?]>()\n\nexpectTypeOf<Delete>().toBeCallableWith('abc').parameter(0).toBeString()\nexpectTypeOf<Delete>().toBeCallableWith('abc').parameter(1).toBeUndefined()\n\nexpectTypeOf<Delete>()\n  .toBeCallableWith(['abc', 'def', 'ghi'])\n  .parameter(0)\n  .toEqualTypeOf<string[]>()\n\nexpectTypeOf<Delete>()\n  .toBeCallableWith(['abc', 'def', 'ghi'])\n  .parameter(1)\n  .toEqualTypeOf<{force: boolean} | undefined>()\n```\n\nYou can't use `.toBeCallableWith` with `.not` - you need to use ts-expect-error::\n\n```typescript\nconst f = (a: number) => [a, a]\n\n// @ts-expect-error\nexpectTypeOf(f).toBeCallableWith('foo')\n```\n\nUse `.map` to transform types:\n\nThis can be useful for generic functions or complex types which you can't access via `.toBeCallableWith`, `.toHaveProperty` etc. The callback function isn't called at runtime, which can make this a useful way to get complex inferred types without worrying about running code.\n\n```typescript\nconst capitalize = <S extends string>(input: S) =>\n  (input.slice(0, 1).toUpperCase() + input.slice(1)) as Capitalize<S>\n\nexpectTypeOf(capitalize)\n  .map(fn => fn('hello world'))\n  .toEqualTypeOf<'Hello world'>()\n```\n\nYou can also check type guards & type assertions:\n\n```typescript\nconst assertNumber = (v: any): asserts v is number => {\n  if (typeof v !== 'number') {\n    throw new TypeError('Nope !')\n  }\n}\n\nexpectTypeOf(assertNumber).asserts.toBeNumber()\n\nconst isString = (v: any): v is string => typeof v === 'string'\n\nexpectTypeOf(isString).guards.toBeString()\n\nconst isBigInt = (value: any): value is bigint => typeof value === 'bigint'\n\nexpectTypeOf(isBigInt).guards.toBeBigInt()\n```\n\nAssert on constructor parameters:\n\n```typescript\nexpectTypeOf(Date).toBeConstructibleWith('1970')\nexpectTypeOf(Date).toBeConstructibleWith(0)\nexpectTypeOf(Date).toBeConstructibleWith(new Date())\nexpectTypeOf(Date).toBeConstructibleWith()\n\nexpectTypeOf(Date).constructorParameters.toEqualTypeOf<\n  | []\n  | [value: string | number]\n  | [value: string | number | Date]\n  | [\n      year: number,\n      monthIndex: number,\n      date?: number | undefined,\n      hours?: number | undefined,\n      minutes?: number | undefined,\n      seconds?: number | undefined,\n      ms?: number | undefined,\n    ]\n>()\n```\n\nConstructor overloads:\n\n```typescript\nclass DBConnection {\n  constructor()\n  constructor(connectionString: string)\n  constructor(options: {host: string; port: number})\n  constructor(..._: unknown[]) {}\n}\n\nexpectTypeOf(DBConnection).toBeConstructibleWith()\nexpectTypeOf(DBConnection).toBeConstructibleWith('localhost')\nexpectTypeOf(DBConnection).toBeConstructibleWith({host: 'localhost', port: 1234})\n// @ts-expect-error - as when calling `new DBConnection(...)` you can't actually use the `(...args: unknown[])` overlaod, it's purely for the implementation.\nexpectTypeOf(DBConnection).toBeConstructibleWith(1, 2)\n```\n\nCheck function `this` parameters:\n\n```typescript\nfunction greet(this: {name: string}, message: string) {\n  return `Hello ${this.name}, here's your message: ${message}`\n}\n\nexpectTypeOf(greet).thisParameter.toEqualTypeOf<{name: string}>()\n```\n\nDistinguish between functions with different `this` parameters:\n\n```typescript\nfunction greetFormal(this: {title: string; name: string}, message: string) {\n  return `Dear ${this.title} ${this.name}, here's your message: ${message}`\n}\n\nfunction greetCasual(this: {name: string}, message: string) {\n  return `Hi ${this.name}, here's your message: ${message}`\n}\n\nexpectTypeOf(greetFormal).not.toEqualTypeOf(greetCasual)\n```\n\nClass instance types:\n\n```typescript\nexpectTypeOf(Date).instance.toHaveProperty('toISOString')\n```\n\nPromise resolution types can be checked with `.resolves`:\n\n```typescript\nconst asyncFunc = async () => 123\n\nexpectTypeOf(asyncFunc).returns.resolves.toBeNumber()\n```\n\nArray items can be checked with `.items`:\n\n```typescript\nexpectTypeOf([1, 2, 3]).items.toBeNumber()\nexpectTypeOf([1, 2, 3]).items.not.toBeString()\n```\n\nYou can also compare arrays directly:\n\n```typescript\nexpectTypeOf<any[]>().not.toEqualTypeOf<number[]>()\n```\n\nCheck that functions never return:\n\n```typescript\nconst thrower = () => {\n  throw new Error('oh no')\n}\n\nexpectTypeOf(thrower).returns.toBeNever()\n```\n\nGenerics can be used rather than references:\n\n```typescript\nexpectTypeOf<{a: string}>().not.toEqualTypeOf<{a: number}>()\n```\n\nDistinguish between missing/null/optional properties:\n\n```typescript\nexpectTypeOf<{a?: number}>().not.toEqualTypeOf<{}>()\nexpectTypeOf<{a?: number}>().not.toEqualTypeOf<{a: number}>()\nexpectTypeOf<{a?: number}>().not.toEqualTypeOf<{a: number | undefined}>()\nexpectTypeOf<{a?: number | null}>().not.toEqualTypeOf<{a: number | null}>()\nexpectTypeOf<{a: {b?: number}}>().not.toEqualTypeOf<{a: {}}>()\n```\n\nDetect the difference between regular and `readonly` properties:\n\n```typescript\ntype A1 = {readonly a: string; b: string}\ntype E1 = {a: string; b: string}\n\nexpectTypeOf<A1>().toExtend<E1>()\nexpectTypeOf<A1>().not.toEqualTypeOf<E1>()\n\ntype A2 = {a: string; b: {readonly c: string}}\ntype E2 = {a: string; b: {c: string}}\n\nexpectTypeOf<A2>().toExtend<E2>()\nexpectTypeOf<A2>().not.toEqualTypeOf<E2>()\n```\n\nDistinguish between classes with different constructors:\n\n```typescript\nclass A {\n  value: number\n  constructor(a: 1) {\n    this.value = a\n  }\n}\nclass B {\n  value: number\n  constructor(b: 2) {\n    this.value = b\n  }\n}\n\nexpectTypeOf<typeof A>().not.toEqualTypeOf<typeof B>()\n\nclass C {\n  value: number\n  constructor(c: 1) {\n    this.value = c\n  }\n}\n\nexpectTypeOf<typeof A>().toEqualTypeOf<typeof C>()\n```\n\nKnown limitation: Intersection types can cause issues with `toEqualTypeOf`:\n\n```typescript\n// @ts-expect-error the following line doesn't compile, even though the types are arguably the same.\n// See https://github.com/mmkal/expect-type/pull/21\nexpectTypeOf<{a: 1} & {b: 2}>().toEqualTypeOf<{a: 1; b: 2}>()\n```\n\nTo workaround for simple cases, you can use a mapped type:\n\n```typescript\ntype Simplify<T> = {[K in keyof T]: T[K]}\n\nexpectTypeOf<Simplify<{a: 1} & {b: 2}>>().toEqualTypeOf<{a: 1; b: 2}>()\n```\n\nBut this won't work if the nesting is deeper in the type. For these situations, you can use the `.branded` helper. Note that this comes at a performance cost, and can cause the compiler to 'give up' if used with excessively deep types, so use sparingly. This helper is under `.branded` because it deeply transforms the Actual and Expected types into a pseudo-AST:\n\n```typescript\n// @ts-expect-error\nexpectTypeOf<{a: {b: 1} & {c: 1}}>().toEqualTypeOf<{a: {b: 1; c: 1}}>()\n\nexpectTypeOf<{a: {b: 1} & {c: 1}}>().branded.toEqualTypeOf<{a: {b: 1; c: 1}}>()\n```\n\nBe careful with `.branded` for very deep or complex types, though. If possible you should find a way to simplify your test to avoid needing to use it:\n\n```typescript\n// This *should* result in an error, but the \"branding\" mechanism produces too large a type and TypeScript just gives up! https://github.com/microsoft/TypeScript/issues/50670\nexpectTypeOf<() => () => () => () => 1>().branded.toEqualTypeOf<() => () => () => () => 2>()\n\n// @ts-expect-error the non-branded implementation catches the error as expected.\nexpectTypeOf<() => () => () => () => 1>().toEqualTypeOf<() => () => () => () => 2>()\n```\n\nSo, if you have an extremely deep type that ALSO has an intersection in it, you're out of luck and this library won't be able to test your type properly:\n\n```typescript\n// @ts-expect-error this fails, but it should succeed.\nexpectTypeOf<() => () => () => () => {a: 1} & {b: 2}>().toEqualTypeOf<\n  () => () => () => () => {a: 1; b: 2}\n>()\n\n// this succeeds, but it should fail.\nexpectTypeOf<() => () => () => () => {a: 1} & {b: 2}>().branded.toEqualTypeOf<\n  () => () => () => () => {a: 1; c: 2}\n>()\n```\n\nAnother limitation: passing `this` references to `expectTypeOf` results in errors.:\n\n```typescript\nclass B {\n  b = 'b'\n\n  foo() {\n    // @ts-expect-error\n    expectTypeOf(this).toEqualTypeOf(this)\n  }\n}\n\n// Instead of the above, try something like this:\nexpectTypeOf(B).instance.toEqualTypeOf<{b: string; foo: () => void}>()\n```\n<!-- codegen:end -->\n\nOverloads limitation for TypeScript <5.3: Due to a [TypeScript bug fixed in 5.3](https://github.com/microsoft/TypeScript/issues/28867), overloaded functions which include an overload resembling `(...args: unknown[]) => unknown` will exclude `unknown[]` from `.parameters` and exclude `unknown` from `.returns`:\n\n```typescript\ntype Factorize = {\n  (...args: unknown[]): unknown\n  (input: number): number[]\n  (input: bigint): bigint[]\n}\n\nexpectTypeOf<Factorize>().parameters.toEqualTypeOf<[number] | [bigint]>()\nexpectTypeOf<Factorize>().returns.toEqualTypeOf<number[] | bigint[]>()\n```\n\nThis overload, however, allows any input and returns an unknown output anyway, so it's not very useful. If you are worried about this for some reason, you'll have to update TypeScript to 5.3+.\n\n### Why is my assertion failing?\n\nFor complex types, an assertion might fail when it should if the `Actual` type contains a deeply-nested intersection type but the `Expected` doesn't. In these cases you can use `.branded` as described above:\n\n```typescript\n// @ts-expect-error this unfortunately fails - a TypeScript limitation prevents making this pass without a big perf hit\nexpectTypeOf<{a: {b: 1} & {c: 1}}>().toEqualTypeOf<{a: {b: 1; c: 1}}>()\n\nexpectTypeOf<{a: {b: 1} & {c: 1}}>().branded.toEqualTypeOf<{a: {b: 1; c: 1}}>()\n```\n\n### Why is `.toMatchTypeOf` deprecated?\n\nThe `.toMatchTypeOf` method is deprecated in favour of `.toMatchObjectType` (when strictly checking against an object type with a subset of keys), or `.toExtend` (when checking for \"is-a\" relationships). There are no foreseeable plans to remove `.toMatchTypeOf`, but there's no reason to continue using it - `.toMatchObjectType` is stricter, and `.toExtend` is identical.\n\n### Internal type helpers\n\n🚧 This library also exports some helper types for performing boolean operations on types, checking extension/equality in various ways, branding types, and checking for various special types like `never`, `any`, `unknown`. Use at your own risk! Nothing is stopping you from using these beyond this warning:\n\n>All internal types that are not documented here are _not_ part of the supported API surface, and may be renamed, modified, or removed, without warning or documentation in release notes.\n\nFor a dedicated internal type library, feel free to look at the [source code](./src/index.ts) for inspiration - or better, use a library like [type-fest](https://npmjs.com/package/type-fest).\n\n### Error messages\n\nWhen types don't match, `.toEqualTypeOf` and `.toMatchTypeOf` use a special helper type to produce error messages that are as actionable as possible. But there's a bit of a nuance to understanding them. Since the assertions are written \"fluently\", the failure should be on the \"expected\" type, not the \"actual\" type (`expect<Actual>().toEqualTypeOf<Expected>()`). This means that type errors can be a little confusing - so this library produces a `MismatchInfo` type to try to make explicit what the expectation is. For example:\n\n```ts\nexpectTypeOf({a: 1}).toEqualTypeOf<{a: string}>()\n```\n\nIs an assertion that will fail, since `{a: 1}` has type `{a: number}` and not `{a: string}`.  The error message in this case will read something like this:\n\n```\ntest/test.ts:999:999 - error TS2344: Type '{ a: string; }' does not satisfy the constraint '{ a: \\\\\"Expected: string, Actual: number\\\\\"; }'.\n  Types of property 'a' are incompatible.\n    Type 'string' is not assignable to type '\\\\\"Expected: string, Actual: number\\\\\"'.\n\n999 expectTypeOf({a: 1}).toEqualTypeOf<{a: string}>()\n```\n\nNote that the type constraint reported is a human-readable messaging specifying both the \"expected\" and \"actual\" types. Rather than taking the sentence `Types of property 'a' are incompatible // Type 'string' is not assignable to type \"Expected: string, Actual: number\"` literally - just look at the property name (`'a'`) and the message: `Expected: string, Actual: number`. This will tell you what's wrong, in most cases. Extremely complex types will, of course, be more effort to debug, and may require some experimentation. Please [raise an issue](https://github.com/mmkal/expect-type) if the error messages are misleading.\n\nThe `toBe...` methods (like `toBeString`, `toBeNumber`, `toBeVoid`, etc.) fail by resolving to a non-callable type when the `Actual` type under test doesn't match up. For example, the failure for an assertion like `expectTypeOf(1).toBeString()` will look something like this:\n\n```\ntest/test.ts:999:999 - error TS2349: This expression is not callable.\n  Type 'ExpectString<number>' has no call signatures.\n\n999 expectTypeOf(1).toBeString()\n                    ~~~~~~~~~~\n```\n\nThe `This expression is not callable` part isn't all that helpful - the meaningful error is the next line, `Type 'ExpectString<number> has no call signatures`. This essentially means you passed a number but asserted it should be a string.\n\nIf TypeScript added support for [\"throw\" types](https://github.com/microsoft/TypeScript/pull/40468) these error messages could be improved. Until then they will take a certain amount of squinting.\n\n#### Concrete \"expected\" objects vs type arguments\n\nError messages for an assertion like this:\n\n```ts\nexpectTypeOf({a: 1}).toEqualTypeOf({a: ''})\n```\n\nWill be less helpful than for an assertion like this:\n\n```ts\nexpectTypeOf({a: 1}).toEqualTypeOf<{a: string}>()\n```\n\nThis is because the TypeScript compiler needs to infer the type argument for the `.toEqualTypeOf({a: ''})` style and this library can only mark it as a failure by comparing it against a generic `Mismatch` type. So, where possible, use a type argument rather than a concrete type for `.toEqualTypeOf` and `toMatchTypeOf`. If it's much more convenient to compare two concrete types, you can use `typeof`:\n\n```ts\nconst one = valueFromFunctionOne({some: {complex: inputs}})\nconst two = valueFromFunctionTwo({some: {other: inputs}})\n\nexpectTypeOf(one).toEqualTypeof<typeof two>()\n```\n\n### Overloaded functions\n\nDue to a TypeScript [design limitation](https://github.com/microsoft/TypeScript/issues/32164#issuecomment-506810756), the native TypeScript `Parameters<...>` and `ReturnType<...>` helpers only return types from one variant of an overloaded function. This limitation doesn't apply to expect-type, since it is not used to author TypeScript code, only to assert on existing types. So, we use a workaround for this TypeScript behaviour to assert on _all_ overloads as a union (actually, not necessarily _all_ - we cap out at 10 overloads).\n\n### Within test frameworks\n\n### Vitest\n\n`expectTypeOf` is built in to [vitest](https://vitest.dev/guide/testing-types), so you can import `expectTypeOf` from the vitest library directly if you prefer. Note that there is no set release cadence, at time of writing, so vitest may not always be using the very latest version.\n\n```ts\nimport {expectTypeOf} from 'vitest'\nimport {mount} from './mount.js'\n\ntest('my types work properly', () => {\n  expectTypeOf(mount).toBeFunction()\n  expectTypeOf(mount).parameter(0).toEqualTypeOf<{name: string}>()\n\n  expectTypeOf(mount({name: 42})).toBeString()\n})\n```\n\n#### Jest & `eslint-plugin-jest`\n\nIf you're using Jest along with `eslint-plugin-jest`, and you put assertions inside `test(...)` definitions, you may get warnings from the [`jest/expect-expect`](https://github.com/jest-community/eslint-plugin-jest/blob/master/docs/rules/expect-expect.md) rule, complaining that \"Test has no assertions\" for tests that only use `expectTypeOf()`.\n\nTo remove this warning, configure the ESLint rule to consider `expectTypeOf` as an assertion:\n\n```json\n\"rules\": {\n  // ...\n  \"jest/expect-expect\": [\n    \"warn\",\n    {\n      \"assertFunctionNames\": [\n        \"expect\", \"expectTypeOf\"\n      ]\n    }\n  ],\n  // ...\n}\n```\n\n### Limitations\n\nA summary of some of the limitations of this library. Some of these are documented more fully elsewhere.\n\n1. Intersection types can result in failures when the expected and actual types are not identically defined, even when they are effectively identical. See [Why is my assertion failing](#why-is-my-assertion-failing) for details. TL;DR: use `.brand` in these cases - and accept the performance hit that it comes with.\n1. `toBeCallableWith` will likely fail if you try to use it with a generic function or an overload. See [this issue](https://github.com/mmkal/expect-type/issues/50) for an example and how to work around it.\n1. (For now) overloaded functions might trip up the `.parameter` and `.parameters` helpers. This matches how the built-in TypeScript helper `Parameters<...>` works. This may be improved in the future though ([see related issue](https://github.com/mmkal/expect-type/issues/30)).\n1. `expectTypeOf(this).toEqualTypeOf(this)` inside class methods does not work.\n\n## Similar projects\n\nOther projects with similar goals:\n\n- [`tsd`](https://github.com/SamVerschueren/tsd) is a CLI that runs the TypeScript type checker over assertions\n- [`ts-expect`](https://github.com/TypeStrong/ts-expect) exports several generic helper types to perform type assertions\n- [`dtslint`](https://github.com/Microsoft/dtslint) does type checks via comment directives and tslint\n- [`type-plus`](https://github.com/unional/type-plus) comes with various type and runtime TypeScript assertions\n- [`static-type-assert`](https://github.com/ksxnodemodules/static-type-assert) type assertion functions\n\n### Comparison\n\nThe key differences in this project are:\n\n- a fluent, jest-inspired API, making the difference between `actual` and `expected` clear. This is helpful with complex types and assertions.\n- inverting assertions intuitively and easily via `expectTypeOf(...).not`\n- checks generics properly and strictly ([tsd doesn't](https://github.com/SamVerschueren/tsd/issues/142))\n- first-class support for:\n  - `any` (as well as `unknown` and `never`) (see issues outstanding at time of writing in tsd for [never](https://github.com/SamVerschueren/tsd/issues/78) and [any](https://github.com/SamVerschueren/tsd/issues/82)).\n    - This can be especially useful in combination with `not`, to protect against functions returning too-permissive types. For example, `const parseFile = (filename: string) => JSON.parse(readFileSync(filename).toString())` returns `any`, which could lead to errors. After giving it a proper return-type, you can add a test for this with `expect(parseFile).returns.not.toBeAny()`\n  - object properties\n  - function parameters\n  - function return values\n  - constructor parameters\n  - class instances\n  - array item values\n  - nullable types\n- assertions on types \"matching\" rather than exact type equality, for \"is-a\" relationships e.g. `expectTypeOf(square).toExtend<Shape>()`\n- built into existing tooling. No extra build step, cli tool, IDE extension, or lint plugin is needed. Just import the function and start writing tests. Failures will be at compile time - they'll appear in your IDE and when you run `tsc`.\n- small implementation with no dependencies. [Take a look!](./src/index.ts) (tsd, for comparison, is [2.6MB](https://bundlephobia.com/result?p=tsd@0.13.1) because it ships a patched version of TypeScript).\n\n## TypeScript backwards-compatibility\n\nThere is a CI job called `test-types` that checks whether the tests still pass with certain older TypeScript versions. To check the supported TypeScript versions, [refer to the job definition](./.github/workflows/ci.yml).\n\n## Contributing\n\nIn most cases, it's worth checking existing issues or creating one to discuss a new feature or a bug fix before opening a pull request.\n\nOnce you're ready to make a pull request: clone the repo, and install pnpm if you don't have it already with `npm install --global pnpm`. Lockfiles for `npm` and `yarn` are gitignored.\n\nIf you're adding a feature, you should write a self-contained usage example in the form of a test, in [test/usage.test.ts](./test/usage.test.ts). This file is used to populate the bulk of this readme using [eslint-plugin-codegen](https://npmjs.com/package/eslint-plugin-codegen), and to generate an [\"errors\" test file](./test/errors.test.ts), which captures the error messages that are emitted for failing assertions by the TypeScript compiler. So, the test name should be written as a human-readable sentence explaining the usage example. Have a look at the existing tests for an idea of the style.\n\nAfter adding the tests, run `npm run lint -- --fix` to update the readme, and `npm test -- --updateSnapshot` to update the errors test. The generated documentation and tests should be pushed to the same branch as the source code, and submitted as a pull request. CI will test that the docs and tests are up to date if you forget to run these commands.\n\n### Documentation of limitations through tests\n\nLimitations of the library are documented through tests in `usage.test.ts`. This means that if a future TypeScript version (or library version) fixes the limitation, the test will start failing, and it will be automatically removed from the documentation once it no longer applies.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}