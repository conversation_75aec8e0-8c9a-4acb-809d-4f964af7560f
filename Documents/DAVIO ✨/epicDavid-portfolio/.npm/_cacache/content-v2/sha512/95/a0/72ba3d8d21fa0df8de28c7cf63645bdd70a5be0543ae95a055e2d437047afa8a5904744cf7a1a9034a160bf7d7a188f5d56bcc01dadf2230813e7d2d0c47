{"_id": "reusify", "_rev": "14-adaa1198f95d751473520d33222a087c", "name": "reusify", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "reusify", "version": "1.0.0", "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "reusify@1.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/reusify#readme", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "dist": {"shasum": "6e227aacc80b984c542ebe38e29e4ce82e911170", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.0.0.tgz", "integrity": "sha512-MfO/DCwNyOaMA0Xbyi8HB8towukW4HKgUVYgXxnlObjI2DpxG9z0qix3iBQfL6FNeXT1A+l2/fZBB2O0iHkq9w==", "signatures": [{"sig": "MEYCIQCKy7kH7sK8UxdmUMpTsH7CB+iwc3S+qsKKHCCBPyU4gwIhAKdYeIYb6yhDMjq61ECQrjVHYLJSPwJQACZ/xOFH1LSV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "reusify.js", "_from": ".", "_shasum": "6e227aacc80b984c542ebe38e29e4ce82e911170", "gitHead": "460929abe2b5a31fc5e6dae6617242b26f9a5364", "scripts": {"lint": "standard", "test": "tape test.js | faucet "}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/reusify.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Reuse objects and functions with style", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tape": "^4.2.0", "faucet": "0.0.1", "standard": "^5.2.1", "pre-commit": "^1.1.1"}}, "1.0.1": {"name": "reusify", "version": "1.0.1", "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "reusify@1.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/reusify#readme", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "dist": {"shasum": "f53584367adc825c87431917263fc7aa19118693", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.0.1.tgz", "integrity": "sha512-QwCssfLPwXehhwODORcqQRdJu+wenkV9zafWRGODbJOIAsiXouQNVbWNWhILRWrnWmigxBn4YK1CbmRLr56Ozg==", "signatures": [{"sig": "MEQCIG+9bANGhYEkhrqI4PsgDfieHvt3+0MRJcv77nRk2MiIAiB/RnCwzSOvb771BJed4bURR9XFDOTGWfU/zwc9kmmFAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "reusify.js", "_from": ".", "_shasum": "f53584367adc825c87431917263fc7aa19118693", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}, "gitHead": "41336018ac1527345d47cb6ec18c5515d0e52b7f", "scripts": {"lint": "standard", "test": "tape test.js | faucet", "coverage": "npm run istanbul; cat coverage/lcov.info | coveralls", "istanbul": "istanbul cover tape test.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/reusify.git", "type": "git"}, "_npmVersion": "3.4.1", "description": "Reuse objects and functions with style", "directories": {}, "_nodeVersion": "4.2.0", "devDependencies": {"tape": "^4.2.0", "faucet": "0.0.1", "istanbul": "^0.4.1", "standard": "^5.2.1", "coveralls": "^2.11.6", "pre-commit": "^1.1.1"}}, "1.0.2": {"name": "reusify", "version": "1.0.2", "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "reusify@1.0.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/reusify#readme", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "dist": {"shasum": "e3ca4a613e64daac35d40ff34819e72776bf5d85", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.0.2.tgz", "integrity": "sha512-uvHpBEwqP0XOAlNski7iUcFn0xB75j3FupCnAHVMf1Bosyeo0Jwb/i0QB6PLkMU98NVus1q8NUhpJU1wgWdJCw==", "signatures": [{"sig": "MEQCIBpmSgDJtcg2Szp/ys/UL1yFg8cOHTkZ1/AR/2eUu+IlAiA4qgcL5MOwbRiQA1GnyXn2JNAZLmjCK8x6LJxnkmR6nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "reusify.js", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}, "gitHead": "26c9d4d819562a807c2dacbcd1c79b79d32e18d0", "scripts": {"lint": "standard", "test": "tape test.js | faucet", "coverage": "npm run istanbul; cat coverage/lcov.info | coveralls", "istanbul": "istanbul cover tape test.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/reusify.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Reuse objects and functions with style", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"tape": "^4.7.0", "faucet": "0.0.1", "istanbul": "^0.4.5", "standard": "^10.0.0", "coveralls": "^2.13.1", "pre-commit": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/reusify-1.0.2.tgz_1500537786491_0.876660275971517", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "reusify", "version": "1.0.3", "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "reusify@1.0.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/reusify#readme", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "dist": {"shasum": "9da7fc786f39400fd6809e3d694b3b28ecd0f38e", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.0.3.tgz", "integrity": "sha512-t8ZQIaf4CHxy8mb4QddDuGOygGiSSiJHMZrtrC+4E7urVtqON4dHVwmV0gfiBOE1tt5p1puae6o8e0b3wkr1Ag==", "signatures": [{"sig": "MEQCIFhXsdIh7NTg+EIkGjhbtAOjlV8Dlnb9y+kDEcUZN/MGAiBqoarqan+VA1id2G4PkS+V/hd+cOql4ur27dlby36biw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "reusify.js", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}, "gitHead": "1d7ea6292ccf73789503da32caddd920ec8b1ed0", "scripts": {"lint": "standard", "test": "tape test.js | faucet", "coverage": "npm run istanbul; cat coverage/lcov.info | coveralls", "istanbul": "istanbul cover tape test.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/reusify.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Reuse objects and functions with style", "directories": {}, "_nodeVersion": "8.6.0", "devDependencies": {"tape": "^4.7.0", "faucet": "0.0.1", "istanbul": "^0.4.5", "standard": "^10.0.0", "coveralls": "^2.13.1", "pre-commit": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/reusify-1.0.3.tgz_1507322290520_0.9243531748652458", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "reusify", "version": "1.0.4", "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "reusify@1.0.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "homepage": "https://github.com/mcollina/reusify#readme", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "dist": {"shasum": "90da382b1e126efc02146e90845a88db12925d76", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "signatures": [{"sig": "MEYCIQC8P7oCieBaTF67MMkichln6bgQv01nbokTuav9oqD1qgIhAMxv5iUwPA/Fy6ExvCE0RFZO0vcvr5eqGZXaVUt8+2ie", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "reusify.js", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}, "gitHead": "89420d4369258d0b768aaebfb1843951193d315d", "scripts": {"lint": "standard", "test": "tape test.js | faucet", "coverage": "npm run istanbul; cat coverage/lcov.info | coveralls", "istanbul": "istanbul cover tape test.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "pre-commit": ["lint", "test"], "repository": {"url": "git+https://github.com/mcollina/reusify.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Reuse objects and functions with style", "directories": {}, "_nodeVersion": "8.9.4", "devDependencies": {"tape": "^4.8.0", "faucet": "0.0.1", "istanbul": "^0.4.5", "standard": "^10.0.3", "coveralls": "^2.13.3", "pre-commit": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/reusify-1.0.4.tgz_1516958898700_0.8839292053598911", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "reusify", "version": "1.1.0", "description": "Reuse objects and functions with style", "main": "reusify.js", "types": "reusify.d.ts", "scripts": {"lint": "eslint", "test": "tape test.js", "test:coverage": "c8 --100 tape test.js", "test:typescript": "tsc"}, "pre-commit": ["lint", "test", "test:typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/reusify.git"}, "keywords": ["reuse", "object", "performance", "function", "fast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "homepage": "https://github.com/mcollina/reusify#readme", "engines": {"node": ">=0.10.0", "iojs": ">=1.0.0"}, "devDependencies": {"@types/node": "^22.9.0", "eslint": "^9.13.0", "neostandard": "^0.12.0", "pre-commit": "^1.2.2", "tape": "^5.0.0", "c8": "^10.1.2", "typescript": "^5.2.2"}, "dependencies": {}, "_id": "reusify@1.1.0", "gitHead": "b2b16eef390ac330068c1ccea4fdec233f4d0f6a", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "shasum": "0fe13b9522e1473f51b558ee796e08f11f9b489f", "tarball": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "fileCount": 14, "unpackedSize": 11696, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDv7AOEepHYWobBVXb4bqyYDR/avrVs4meRq6UoHBswHgIhALhSFIA/DyTUXHUhdCD92l4qNYqvBu36WNx373PjdYjF"}]}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/reusify_1.1.0_1740499249782_0.4468161870600842"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-09-11T22:13:22.776Z", "modified": "2025-02-25T16:00:50.115Z", "1.0.0": "2015-09-11T22:13:22.776Z", "1.0.1": "2015-12-16T15:33:41.090Z", "1.0.2": "2017-07-20T08:03:07.428Z", "1.0.3": "2017-10-06T20:38:10.606Z", "1.0.4": "2018-01-26T09:28:18.775Z", "1.1.0": "2025-02-25T16:00:49.941Z"}, "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/mcollina/reusify#readme", "keywords": ["reuse", "object", "performance", "function", "fast"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/reusify.git"}, "description": "Reuse objects and functions with style", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}], "readme": "# reusify\n\n[![npm version][npm-badge]][npm-url]\n\nReuse your objects and functions for maximum speed. This technique will\nmake any function run ~10% faster. You call your functions a\nlot, and it adds up quickly in hot code paths.\n\n```\n$ node benchmarks/createNoCodeFunction.js\nTotal time 53133\nTotal iterations 100000000\nIteration/s 1882069.**********\n\n$ node benchmarks/reuseNoCodeFunction.js\nTotal time 50617\nTotal iterations 100000000\nIteration/s 1975620.838848608\n```\n\nThe above benchmark uses fibonacci to simulate a real high-cpu load.\nThe actual numbers might differ for your use case, but the difference\nshould not.\n\nThe benchmark was taken using Node v6.10.0.\n\nThis library was extracted from\n[fastparallel](http://npm.im/fastparallel).\n\n## Example\n\n```js\nvar reusify = require('reusify')\nvar fib = require('reusify/benchmarks/fib')\nvar instance = reusify(MyObject)\n\n// get an object from the cache,\n// or creates a new one when cache is empty\nvar obj = instance.get()\n\n// set the state\nobj.num = 100\nobj.func()\n\n// reset the state.\n// if the state contains any external object\n// do not use delete operator (it is slow)\n// prefer set them to null\nobj.num = 0\n\n// store an object in the cache\ninstance.release(obj)\n\nfunction MyObject () {\n  // you need to define this property\n  // so V8 can compile MyObject into an\n  // hidden class\n  this.next = null\n  this.num = 0\n\n  var that = this\n\n  // this function is never reallocated,\n  // so it can be optimized by V8\n  this.func = function () {\n    if (null) {\n      // do nothing\n    } else {\n      // calculates fibonacci\n      fib(that.num)\n    }\n  }\n}\n```\n\nThe above example was intended for synchronous code, let's see async:\n```js\nvar reusify = require('reusify')\nvar instance = reusify(MyObject)\n\nfor (var i = 0; i < 100; i++) {\n  getData(i, console.log)\n}\n\nfunction getData (value, cb) {\n  var obj = instance.get()\n\n  obj.value = value\n  obj.cb = cb\n  obj.run()\n}\n\nfunction MyObject () {\n  this.next = null\n  this.value = null\n\n  var that = this\n\n  this.run = function () {\n    asyncOperation(that.value, that.handle)\n  }\n\n  this.handle = function (err, result) {\n    that.cb(err, result)\n    that.value = null\n    that.cb = null\n    instance.release(that)\n  }\n}\n```\n\nAlso note how in the above examples, the code, that consumes an instance of `MyObject`,\nreset the state to initial condition, just before storing it in the cache.\nThat's needed so that every subsequent request for an instance from the cache,\ncould get a clean instance.\n\n## Why\n\nIt is faster because V8 doesn't have to collect all the functions you\ncreate. On a short-lived benchmark, it is as fast as creating the\nnested function, but on a longer time frame it creates less\npressure on the garbage collector.\n\n## Other examples\nIf you want to see some complex example, checkout [middie](https://github.com/fastify/middie) and [steed](https://github.com/mcollina/steed).\n\n## Acknowledgements\n\nThanks to [Trevor Norris](https://github.com/trevnorris) for\ngetting me down the rabbit hole of performance, and thanks to [Mathias\nBuss](http://github.com/mafintosh) for suggesting me to share this\ntrick.\n\n## License\n\nMIT\n\n[npm-badge]: https://badge.fury.io/js/reusify.svg\n[npm-url]: https://badge.fury.io/js/reusify\n", "readmeFilename": "README.md", "users": {"bajtos": true, "majgis": true, "blitzprog": true, "hengkiardo": true, "flumpus-dev": true}}