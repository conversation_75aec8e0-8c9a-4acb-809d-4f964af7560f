{"_id": "esquery", "_rev": "30-5c13b7774b9255a44971e391630e387a", "name": "esquery", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "dist-tags": {"latest": "1.6.0"}, "versions": {"0.0.1": {"name": "esquery", "version": "0.0.1", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@0.0.1", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}], "dist": {"shasum": "d42025e726e72eb64b413ba24b2acecc9718f1b6", "tarball": "https://registry.npmjs.org/esquery/-/esquery-0.0.1.tgz", "integrity": "sha512-lnLrZDsItB31GictTnlGq1WCU6/81rIhghnEDnn/S1NkVPoUO/uGBhOmB7UYIPRZ7W8FO8Fp8sgozfXK3YHAZQ==", "signatures": [{"sig": "MEQCIAfBbW1X83ntYZ5fXTJEXw4GA+lNhf8qepeQLaUSgL+VAiBdWX6tBhXm1EGUz+sPtUHbQ7Vr9NLJssZHnfRTsRq9zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "jrfeenst", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "1.2.17", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "preferGlobal": false, "devDependencies": {"jstestr": ">=0.4"}}, "0.1.0": {"name": "esquery", "version": "0.1.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@0.1.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}], "dist": {"shasum": "b812f49d96c896c59806a21bba613dc03bb67c1c", "tarball": "https://registry.npmjs.org/esquery/-/esquery-0.1.0.tgz", "integrity": "sha512-ravRCyfjw2MsGFgsbfF9fCLmSYOyZ8PjEczqT5X0esTUSn/8XpNUp3li8uYBywl9T+whBKEWpHzH1VZMLVuSHg==", "signatures": [{"sig": "MEQCIA9P0jt9m5gPKtVbsU1G5Y9l0PQhuvnvMJ1QsdnXlCpBAiBPa+2+Cwd1EjM7isKGr7xSrzcoIxlRo34UwCey3CCUXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "jrfeenst", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "1.2.17", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "preferGlobal": false, "devDependencies": {"jstestr": ">=0.4"}}, "0.2.0": {"name": "esquery", "version": "0.2.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@0.2.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}], "dist": {"shasum": "07bc770fb94b879687326d5c6cd0afaf7273d002", "tarball": "https://registry.npmjs.org/esquery/-/esquery-0.2.0.tgz", "integrity": "sha512-K+P1N/Nvu4fqRm2gWoNIt5MzsbP4HIUrhGxFF+XGXJEnDZLXITxnWi9vH/s1TkVY0dWR3e7+4iCQXoZbajVepA==", "signatures": [{"sig": "MEQCIEJcxqUXGIw4IkG5Sn1rlRX9PQyJFfQsMHrtMFtXk6YIAiBhSE9oewFntfnIPnNlbgUUOcG1NBAGLkZfrJq4lGpS8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "esquery.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "jrfeenst", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "1.2.17", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "preferGlobal": false, "devDependencies": {"jstestr": ">=0.4"}}, "0.3.0": {"name": "esquery", "version": "0.3.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@0.3.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}], "dist": {"shasum": "e43f2d321614d9d7cf6abd96243c70fc056585fa", "tarball": "https://registry.npmjs.org/esquery/-/esquery-0.3.0.tgz", "integrity": "sha512-8MJujr5n+d7oN4fZ1Tk29ou/Y5BFkztkpbBRR+LTlcNjU+m2iAqCpD14CFzyc0lMLPoXUv8lBPBWZHkEjSXt2g==", "signatures": [{"sig": "MEUCIQCiZVdaD/XDHnaI7O6g+hLMW/y8z8TjBFprv3CFeeckQgIgYKq4sIkxp0fx21dICUc2HfCE9qSEWwLXNKY9Dqc1Hq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "esquery.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "jrfeenst", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "1.2.17", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "dependencies": {"estraverse": "~1.3.1"}, "preferGlobal": false, "devDependencies": {"pegjs": "~0.7.0", "jstestr": ">=0.4"}}, "0.4.0": {"name": "esquery", "version": "0.4.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@0.4.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jrfeenst/esquery", "bugs": {"url": "https://github.com/jrfeenst/esquery/issues"}, "dist": {"shasum": "4c4fb5f6a5854fc12bbe2bebf3933b4ed9d4413e", "tarball": "https://registry.npmjs.org/esquery/-/esquery-0.4.0.tgz", "integrity": "sha512-f4RGz1G5H+2iqlXpNljMS0yaNM/QYV6VUDLKZXQkxkMaDhEx36QfiGU7fCXN2Q+1phrbDxQE1USRLUxIc2ol3g==", "signatures": [{"sig": "MEYCIQCQkxiLa5BKydHWfZ3UmMLR9ewtqm3dtnqKOMjyhlWACQIhAPEhrmBG4YQcqiYuaFeX8QnShrt5VGgXhRQAzLs90eBP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "esquery.js", "_from": ".", "files": ["esquery.js", "parser.js", "license.txt", "README.md"], "_shasum": "4c4fb5f6a5854fc12bbe2bebf3933b4ed9d4413e", "engines": {"node": ">=0.6"}, "gitHead": "dee284cc4eac7a31d251076199409b1ab5df86b5", "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"estraverse": "^4.0.0"}, "preferGlobal": false, "devDependencies": {"pegjs": "~0.7.0", "esprima": "~1.1.1", "jstestr": ">=0.4", "commonjs-everywhere": "~0.9.4"}}, "1.0.0": {"name": "esquery", "version": "1.0.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "esquery@1.0.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jrfeenst/esquery#readme", "bugs": {"url": "https://github.com/jrfeenst/esquery/issues"}, "dist": {"shasum": "cfba8b57d7fba93f17298a8a006a04cda13d80fa", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.0.0.tgz", "integrity": "sha512-81Hhof+z1FE3KIrTFOXjaRl7vphcZyUEwRY+pbVv2tdVxM3uxJzd3xvdtiFSUxQdq7zoH+U5Qy9UAKyHqv8LfA==", "signatures": [{"sig": "MEUCIFN7MlPAVej1lb5qxUwGFLu/9IhEIhf6Sncz0ooo56PxAiEAj7yL1culKluN4YSMNM0Y6kC+5yAPYJX+r9bXCaHCUhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "esquery.js", "_from": ".", "files": ["esquery.js", "parser.js", "license.txt", "README.md"], "_shasum": "cfba8b57d7fba93f17298a8a006a04cda13d80fa", "engines": {"node": ">=0.6"}, "gitHead": "c029e89dcef7bc4ca66588a503ec154bd68f0e05", "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"estraverse": "^4.0.0"}, "preferGlobal": false, "devDependencies": {"pegjs": "~0.7.0", "esprima": "~1.1.1", "jstestr": ">=0.4", "commonjs-everywhere": "~0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/esquery-1.0.0.tgz_1489187536588_0.0852991035208106", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "esquery", "version": "1.0.1", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.0.1", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jrfeenst/esquery#readme", "bugs": {"url": "https://github.com/jrfeenst/esquery/issues"}, "dist": {"shasum": "406c51658b1f5991a5f9b62b1dc25b00e3e5c708", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-SmiyZ5zIWH9VM+SRUReLS5Q8a7GxtRdxEBVZpm98rJM7Sb+A9DVCndXfkeFUd3byderg+EbDkfnevfCwynWaNA==", "signatures": [{"sig": "MEQCID7WAi2NzNWGz+E0WGiXaa/CndtBle30kZnDGAalvL8qAiAk7Bo6N9eTjvrpztMkq0FpPdGt/n7r7Wm2IKi1sjoa+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94954}, "main": "esquery.js", "files": ["esquery.js", "parser.js", "license.txt", "README.md"], "engines": {"node": ">=0.6"}, "gitHead": "1853a6fbaf40709dc2bf9f3c027163a573c3824f", "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"estraverse": "^4.0.0"}, "preferGlobal": false, "_hasShrinkwrap": false, "devDependencies": {"pegjs": "~0.7.0", "esprima": "~1.1.1", "jstestr": ">=0.4", "commonjs-everywhere": "~0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.0.1_1522684887830_0.4750576904720585", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "esquery", "version": "1.1.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.1.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jrfeenst/esquery#readme", "bugs": {"url": "https://github.com/jrfeenst/esquery/issues"}, "dist": {"shasum": "c5c0b66f383e7656404f86b31334d72524eddb48", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-MxYW9xKmROWF672KqjO75sszsA8Mxhw06YFeS5VHlB98KDHbOSurm3ArsjO60Eaf3QmGMCP1yn+0JQkNLo/97Q==", "signatures": [{"sig": "MEYCIQCH7RCETTK5FGTvRbw7lm4ZsBV0aYf1ZgtOOfrEy8bvZAIhALQ1wf/3+OvYzZM4AIVqDeuD4dYSecJlQ+c6yKqOyP3C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeREB3CRA9TVsSAnZWagAA/vcP/1EfgVGzGfUoqfP+wC+A\nqCPlAUNUeaZy77LSqOQpIDcW5b3lMIlLHnU61q/LULxdF6stWywDKJl6vuiS\nHDosdX11NCZSJFXPsUUAvm7dQgZuLXJK5SdOlMO4tX+c5nWcPyoxbiPdTbqv\nhPE4LGNu79XSeiNX87MmauCO+o2yNtaCOOCR/YbOerQgEjhN8tavQmHckdaf\nXUv95puAeJND5GmuJLzW/vK/5f8jkM+D0ErZ2jzCF38vMPSVaqIXotyY5aon\njBtAmAqjK1hRUkJlzTHf8dXHd075vZBiYQzDs3NbCwni5UYlEvMWmV3XygfK\nQnKXvs7G55FsvM8NlZ1YJanQ9LmlDxTUyLCQhtvwDxO1xv0DOnS3yuOEmRaZ\nMonVlk12H2PUPBC2EdWdolBRECtYU+vaIYvAD0CX0ECwGzJ8kN5lRGR/tx3j\n4dKiut09ODRsLSAlX0VpdcZM7vD2WC4xpF+gX14afINk2brLI+jE0eCm69f9\nzNUZGIPir8aQPvuhDaY+dsl7zwPWvaMtylHRt2i9SwNI0IYE1hc0+0AJ6My0\nzGRndpSBb9lYThEZEbIjXTq8XVwb0TRv+JVPz5wC/D6Wr912RnG4HGcdp3WY\n3M7UFATgYpbNvIecNQMxH5wnlqJz7EG33nu5+Fpao1juV7FQ3ct9oqvPR3GK\nfAiE\r\n=mh03\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "esquery.js", "engines": {"node": ">=0.6"}, "gitHead": "cf004f240a26bb0b2de673b558889de5723444be", "scripts": {"test": "node node_modules/jstestr/bin/jstestr.js path=tests"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jrfeenst/esquery.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"estraverse": "^4.0.0"}, "preferGlobal": false, "_hasShrinkwrap": false, "devDependencies": {"pegjs": "~0.7.0", "esprima": "~1.1.1", "jstestr": ">=0.4", "commonjs-everywhere": "~0.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.1.0_1581531255452_0.8107591957315223", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "esquery", "version": "1.2.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.2.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "a010a519c0288f2530b3404124bfb5f02e9797fe", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-weltsSqdeWIX9G2qQZz7KlTRJdkkOCTPgLYJUz1Hacf48R4YOwGPHO3+ORfWedqJKbq5WQmsgK90n+pFLIKt/Q==", "signatures": [{"sig": "MEUCIEE/y8NpcT7ZGCQDarftXmpO1mRieZAa89rc0aaCPLu+AiEAyPjSNDYlA5e7EtDPF6C3k24UUyIFWgimKXTADz2A/J4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 680647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeDN/CRA9TVsSAnZWagAAHZoP/2ufL3zG0efy1Bdci3/e\nqGzYI1P2Jtjzt1r5Sn1lEdcJ1RHoVn+oTDTza6qRwGmOptggILUuzB0wKkp+\nwHwAar4cniYmcCx3+DAjCUTNk37CyZ2ZZ4q4Xqs1YfiQRQjSDVw4qYVQLUAW\nYY4Sdc69nmJVrjRNO3L1XQmSopp7Ou9Zv6FCjuBZ9jdPhRqhEeYbobqaz8gz\nOVpPx4lo6D2uX6HS6UDEQjUyN5IatK4gm9qRnOh2s20C/56R8ympufBvwwnZ\n+8i9WxTxTDuv3fvP3bLPNnJndVZltwGW4ln/+g3G+iZ3oXMs2DBc4qE8pIIL\nbA5ZUVeOcDfo1iEaV7jaVmseCHE09mMGmJBRTpyCL0QXqTWh40KHdH49MTPz\nzcUziS+2rE+9WyCKCtt1FywcvOZkZdqKChJBFaDUS8D7S2uy3s8fDJYyJ4Zy\nUn03Sc8WpRMrnFro9tZzq7uLAM5mWPqcDF7OMkESYWtwur2yRCgUaYq1UQwo\nLkENka38YWCUIcOCBUkAz/2AwjnK+/TQve5RGycBBijgvbifHD1KdcBhqRHu\n72czhKh6wpoe31lSw2o/eOsKblOT2akx5qB86wxKpP0A8TMJTJJswLQR7wda\nLexHlzf+z+0d1DxKz9Ey0pOsGAaB6+M6M9CObej8ep7i9ndEr5nxkqR8IRz1\ni7x3\r\n=CBns\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=8.0"}, "gitHead": "3721d7a01b31ac4fcf5704b4128f5160a1821d54", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require esm tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"estraverse": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "nyc": "^15.0.0", "chai": "^4.2.0", "mocha": "^7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.0", "esprima": "~4.0.1", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-terser": "^5.2.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.2.0_1584935806764_0.6006702810822124", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "esquery", "version": "1.2.1", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.2.1", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "105239e215c5aa480369c7794d74b8b5914c19d4", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.2.1.tgz", "fileCount": 10, "integrity": "sha512-/IcAXa9GWOX9BUIb/Tz2QrrAWFWzWGrFIeLeMRwtiuwg9qTFhSYemsi9DixwrFFqVbhBZ47vGcxEnu5mbPqbig==", "signatures": [{"sig": "MEUCIQC6BW4wFx7asHZ+9/B2AFy8knUDD1FuBlRQAjyGdO6wuAIgGsLEB2Zw4l69vUAwdb8FYLSD85E9sLXZwYsxNlDSEEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 699693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelM3NCRA9TVsSAnZWagAAD6UQAIkqBAc0yq2mdEnlN8av\nqnJHc3Ek1YcPBvEw6KAUsV2rZgULpNeKj19166v0DGyyARHFKN4VuAU9XM3a\ntukuURhe3tlHHg7mC6IrJtndZ1sbYZQ2E+Ab1fOX+A0XuehF0jrXMygUZ6Z3\nmt06EEvqBCkUkWFBweZ4aO1fPFDxQu6QcXzEqfGRnuiKxhg+VfdgXM5GmLgF\nycgXIfphI0nDPcl5fvmSgqV4+Z6RJMmTj83mraQ+9YM2XcC/rrwRj00ifxOH\n2sqvnFVrLaAUeyZUu8sHlYQvmkqzfEQis1keYysQ0Kvp+zwk4nxe96Lw8fc3\nzVV//h6FkshxoHnMMmIDvVmVccbbsyotwHjgRmBCoqO3pKh246hCdJauWNSQ\nEXvlpa56ed744MhY3JeXs9FNCokMp1HdQjLK8dc54JoIGFInTgDOSzBhjtUg\n2S64vF7WqtwFkVpEVwwAnpiH4edZU6Ms8coeiBc8FCxFPKfDQYE+Ei5Zg5JI\nHsLCYdq8L+fB2Q7AWT72Xgl8TEHxpkOHW5YkcrUVS5j9Xgip2swLZQw1E+WO\n3XHeaEF0adkCoNz2byb580bKYBs35qGWCYMrtgJqkS+JkcjvR5awbemoaVLF\nYlzAo9Ry4iILT0jGgaGkkjN6oP1hchNCc3JLZ2g7+db0rRpB1HX3O9kQ2oLf\nwI9g\r\n=UMm0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "a97c44c64d88700e49a175fa1f31fd1d89683a38", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"estraverse": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2.0", "mocha": "^7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.0", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.0", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.2.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.2.1_1586810317247_0.04426990571162315", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "esquery", "version": "1.3.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.3.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "e5e29a6f66a837840d34f68cb9ce355260d1128b", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.3.0.tgz", "fileCount": 10, "integrity": "sha512-/5qB+Mb0m2bh86tjGbA8pB0qBfdmCIK6ZNPjcw4/TtEH0+tTf0wLA5HK4KMTweSMwLGHwBDWCBV+6+2+EuHmgg==", "signatures": [{"sig": "MEQCIB8iUTBFRO3q2eN8WcP40rHSuATcWJLGi4KjEr6W73S3AiBodCESCd2PAezVjQejlgENWhXuASlfi+LEqeiXQ/LKWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelmCgCRA9TVsSAnZWagAAM7kP/ArzICTiB9nWgqG8Im/L\niJa3/4C6LfOnDzWYhQ/LY8QV9Jqr5LyxmxKrazRy2+xAfYc/jfhID1mZoIix\nHM76PYhrD0epAVONJgUmDgwulLxEKeMxfs6LvsmoIzFU4eUvDF0hq+im5I+0\n9cP1HDR13n8v3vqb5oKEOqx4DMyDqmsQRE4PhLc3EMkFrsUQ4QqNDNNFindP\ns6UfjbxDSae+UliEuwaQeQbeIGsxPNdT7x9ryYNIkXissChjXLtWynEzckJm\n0DCbeE5uTGJbroXBgdJ3RWSSuIW3r8Y6/sqiNk+5Gb3+mVZo9TXiM5o5PECf\nRMZ9dul3OOK3zs/Vx0posCtNhII/KVX2Bzt8Iz3LgKy4X1UeFu3Jc5VcqDA+\nwfSP+rHOAQXs4/PeAzciFk1nIqLPXUGcnExaFb2d+aGji7ZiQvIu9ZyHkXNt\n5zbGiEK8nZE9ii+G9ru/7z3uFkbOIhPovcjj8KfX2bcAdtmyhhoUuVdYyU+V\nMEfxlSs0ZvC12i8XJzVtDHe/9ThOe9DEra+bBw4GKgkiDGxxG/mjcNjpzTXg\ncj/5CAwi8bdUrnZAfFycihLaPfhiN2c6YxzIgEt3esj57Ta8aKqEuOQTKCBX\nTSUL2QRIQH/4VpnCufs6g62fskj3XmhoNrisWsnJETqblCrCqtrJBl9bj15j\nOTvJ\r\n=1h2Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "333b9841e404714d7b1d0521bdcf97fa702a519e", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"estraverse": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2.0", "mocha": "^7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.0", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.0", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.2.0", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.3.0_1586913439707_0.7481198259181494", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "esquery", "version": "1.3.1", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.3.1", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "b78b5828aa8e214e29fb74c4d5b752e1c033da57", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.3.1.tgz", "fileCount": 10, "integrity": "sha512-olpvt9QG0vniUBZspVRN6lwB7hOZoTRtT+jzR+tS4ffYx2mzbw+z0XCOk44aaLYKApNX5nMm+E+P6o25ip/DHQ==", "signatures": [{"sig": "MEYCIQD5HdWDGE/MUhBB1ep81PHrazY6DNF1gWilxvFFUHQ8yAIhAOItwVuIFJrL/A+qvGldX6h5uw2pNcGAZYQNc+rJFVPx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 705627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemKEHCRA9TVsSAnZWagAAjCEQAJhWEyt9XQP8POeAvzBJ\nXAVlD48eUh2bNDK+sBkUx2VQd4klowVHfcHBrSyq7fnqK6zXSkZ6NhL88Vlp\noiWYK4BbU1PKDKgv/qddGThiBowWikXekYKEzJidFusFG4N1yoA7x1hea67l\nUx2btbw9bh4PF37latprcFNLd6SXAJm9CWe3bzoq4+zqNYGuatxsN7cQvjcE\nTrktNEUvcie4/qXTakit9aaRX9s9QF6AFedIsxAacSGt9guuZg9+8Ne6ivt/\nA6m43roRUcMCWc42tvCQ1EPE7aZhloTRyHBml18u7XLT4X41Zn0VM2mBYqs1\nPx7pRwTvAhkL9aOE46+NwebsjGxk3oq9GFcTmoGW6AOETC/kF86NOK/fqXbY\nKmwSX+1psesuD+z9lMtak3Zm+MBBnX67QgFe4se6ZQ8pr2BmOWTb9zEh79ED\nSxIylXIMVYmoYBhrC+Ow0k43FSFwXIlN+o9nYErUemZYhhh2yrRpxo0f5RCC\naNwbH4tCZ0A1xg3Hy5c8xA3xzGyXz9T/sBhwX/viRIk6pLJ6a0IX7pwJ58pz\nJnkRh5njhpGUImA501000m1I69aNP70auur8Fg+Oujgh+/LVy6bcx9iRESOo\nmyU3VVC46MLJoOdpFqbRE2gqBJt6EJZkSRb3un9skAXkSZf6kkEbTDnn0/tj\nOKRS\r\n=chGg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "a48262deb20861568acf1bca0b7e02867e0b2c48", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"estraverse": "^5.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "chai": "^4.2.0", "mocha": "^7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.1", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.3.1_1587060998631_0.7794353253302886", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "esquery", "version": "1.4.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.4.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "2148ffc38b82e8c7057dfed48425b3e61f0f24a5", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.4.0.tgz", "fileCount": 13, "integrity": "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==", "signatures": [{"sig": "MEUCIQD/uRZXqvOAEQrVbWdz2whkKZiqABqaDW+0wS1vcWW34QIgB+ARb64E4cZEn5VPWRvhZtTRTZLNTHtX6RlL9j8z8LY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 986468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHcwMCRA9TVsSAnZWagAAHogP/ifWyqZgZKe7Dy6Ypx5T\nlrDowiArKt6BuBOzKAiuWe2hgnCY74Ner4e854p7Y0/qYpoPMX6akbEQOzjg\nFUtjm17jQyDfOsQuchAL0WJ1ZvxRPJPuONXuVfH+iYUJzGOdauNU4iOdMYJE\nLadqUhMxD1aHNUI1IoE9gZEuRWmUR086FsAgTZQYvUqR0XwXx9NFrAEz/z8e\nPvazIu8nGfer5mZy8x7PkK5Sf3c73Dv82Xk7HkHCE/klXPi/cQ1aL/1exYkv\nTf4/yOJXA5cZ514/C1tNkyKbh4DMPTHnVi4/hVMRwuU0TbMq5YM6+C1pbk87\n1LToqMVB5f4Xt8ad9o0WZbF4f/G3fZd9FQudV+B6O0Om7PKPUxrqSpeBRQjo\nBDLlui7bFQCuHLJtQiHH1iWqdGqhmE5BLXB+47sfEq8n8p60tHwqgxIgFBqa\nd1n2GAP/KPalmzFFiVreaSUHf1OlrhlLqz3ri5nMH4voXM9QQwA2A9JDMcqW\n1so0Uk/RokUVlA8rMRmV7jVuRUwA+pMJ2YzvrQ+mhT4tlZXhr0/mAs7CLgEO\nIOzySgQulS2bIjftK27hXYCMjP8c4Wyj9kwgtmA0+t+u4aYewknA9VAAKTtg\nm7Ded4RV19J0iAAKBOPEBvvaDehV+SkVVLSlCVBmbR2lWuH75xBSMuVCHDDE\nOKjS\r\n=YnRF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "7c3800a4b2ff5c7b3eb3b2cf742865b7c908981f", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"estraverse": "^5.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "chai": "^4.2.0", "mocha": "^7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.1", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.4.0_1612565516319_0.7444306558831926", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "esquery", "version": "1.4.1", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.4.1", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "ddb8e1e2666750113b78c15f59e977564f52b116", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.4.1.tgz", "fileCount": 13, "integrity": "sha512-3ZggxvMv5EEY1ssUVyHSVt0oPreyBfbUi1XikJVfjFiBeBDLdrb0IWoDiEwqT/2sUQi0TGaWtFhOGDD8RTpXgQ==", "signatures": [{"sig": "MEQCIAHvQj/l0zVRY/VOMr3VBobgKKlIjL4XrL/1Ua+LChtkAiAGIoksRasZi5VfxsKfxI6s3DASAZ2szsj0pwwwMxYMog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1011731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7YZJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiyQ//RQjrE6Zxyr4BDpO6tk1cS3AvF5EtItqEjNTi/gaoMw+EAT3/\r\n29LjcZ6T+zdX3NlhIaBvDcih1XX1QvvubouuE0kWZ2P1zgpL+PzRWjszFe69\r\nYDyARniUlYUQ1fwn+DcE4NPtqPJCHgTL+9kHmaw1+pgSGpysXV3fRn4KAKwG\r\neC3c9veYXZWIscJBOS/fDuw0JoXmnaZjl/X3MEMqhZRMMboh0pI8V45rXWBb\r\nlCztQhAfBkYu5Vom7Yoi5wj/ksVvW/77uh9MPJ0SFj/9pMaTrFh78pyaO65R\r\nUPy7q8qmqPYmZrjqqg3CUujlJZndlsxDfNTgaqkjXjDQYduG6A8WNRcJ7saX\r\nldQ4t3UTpKjBtshCQ+F1bNxPle3UHcoqwpEjPrLgZRJxVERs6HEuHKtfWWyi\r\nhJFARm1up4C0/mbwaNZVlZwwmcf9QBhYLVfeASkF6n5Px/QCQrLBHri9kS/4\r\njsA9xX7kithW0/0jEh1bpvvKSYt2YhHV2uoVIYPs8F9Nqw9K9LQ2kbZHa+k/\r\nx5wgCGD4tZ4rP4torNrQBXuLbesqi33GLZLmVlXsIIAk6ltnLYvmLxYSes/K\r\nuvvHQ8Y/7rLxVIp6wKzln1Ub7S0cX2j5ga0xGrvx09WBK/NCo2/8/0rkr/Mm\r\nU9a5Oyd3PJbQyXjAx20uK/ftczHxb+S+8Hc=\r\n=wGQJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "64c05aee6c5b9fabfe0b267020b2b8a45548600c", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "19.5.0", "dependencies": {"estraverse": "^5.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "chai": "4.2.0", "mocha": "7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.1", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.4.1_1676510793519_0.35254754695334145", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "esquery", "version": "1.4.2", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.4.2", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "c6d3fee05dd665808e2ad870631f221f5617b1d1", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.4.2.tgz", "fileCount": 13, "integrity": "sha512-JVSoLdTlTDkmjFmab7H/9SL9qGSyjElT3myyKp7krqjVFQCDLmj1QFaCLRFBszBKI0XVZaiiXvuPIX3ZwHe1Ng==", "signatures": [{"sig": "MEUCIQDaWwlOmtU6gemlObWlEuqh4S0+Kexv2coBF5E0g/3DhgIgXtDD/PP90Rfyv6CmIG0Oe/TdMWkHtOS+Cb6bP50YkTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1011971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7jNKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Sg//RxAZnFQIklwQ/xJK19P/TbytRhtTBh+Jrl294eXHUf1B87SJ\r\n1XzFPWTOVKTLKSC/KAdbHi3w3KGY3MEHVb6QTAGI21m72wf8/c22ngWry7NA\r\nqgaPnUTAfCmOWKIBAxiaQWuEnmUJh08XlgoJqBM6PPNvRkya0Gnp5pusMpLo\r\ndQDLaZJ38UCCkAr1QjbF56JipvB9W3rnS3XphH9LbUfBGzKllWB7sl75sPhU\r\nvv7LtXImA+ORtz83R7BZvcm6LACsBV+7mA/qWoVK2jMDMjksMdnECcW1tK7M\r\nESAqh0KlgM7i7VzBnb4sYM+wztUxFDUIRpVMY5/vwO5WBFzyZpeqVnW/WTsI\r\nO5TvUIpRcvWwkdqBUajaVTWk2c/6htzDZdYvi9XQp0J/AZQE4Yugygegpgnk\r\n5YAAZdOlLt8hcO8Ri4gd+7tdDL8EpnSYNNEITeU1gN+D2viF99Pnur7SvVZU\r\ndi8yfZO5792APSHZkgcVboMGyZ0/vK/JG0Q4A3Mi3BHetDnzWaPCEuxykJqb\r\nydbVOF5E7suYARlePbfJ+LYU7lL5DvE3siOGdZ4m0esqM9o21kY8mMBWhQqZ\r\n0BW7cvx5cKDJ2nI0iqGzbvrsrWQ9XeYu3RMMRwBOcUGnmxPaphB/J4dIrCsF\r\nMjjN7+D9wgLPhZRrtos5iF6lLxSWBP1YHmk=\r\n=JadC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "d00dac8750b58835ff84efd219e31afc69ff13ec", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "19.5.0", "dependencies": {"estraverse": "^5.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "chai": "4.2.0", "mocha": "7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.1", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.4.2_1676555081718_0.9524418637950767", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "esquery", "version": "1.5.0", "keywords": ["ast", "ecmascript", "javascript", "query"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "esquery@1.5.0", "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "nyc": {"lines": 100, "exclude": ["parser.js", "dist", "tests"], "branches": 100, "reporter": ["html", "text"], "functions": 100, "statements": 100}, "dist": {"shasum": "6ce17738de8577694edd7361c57182ac8cb0db0b", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz", "fileCount": 13, "integrity": "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==", "signatures": [{"sig": "MEUCIQDaQrQ/2alsil43DiBmQlOuksW5JnY8IZeZQHdCJwj4hAIgAIY2MXSNdJy8lRpElE3eCfLHcAu1sF2+MY//jn6zkkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAAHCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpynxAAi6GEUXjWBcT+m+hx4X44L8GfuJRHvoO0tHMojSStNnnc8YKR\r\nDjwciDG7L4OrthrP9dgM6M5qge7HrEdv0r15DLPzd3iu2X+/ZQCcWJfT8FtF\r\nbj/G8i9XCPWX06YkqvlZAtGLEnVzamDcnRTBfXaLtXuOX+HioOkIJ72pIHuQ\r\npqREQ5nGN4o54AmpRIva9uFuFm0C1P+JEaJ2dB0Wb6ccX72W8/eFuOZ6x1ej\r\n2AMy/poRIX4vpwX8ML2rgL70RrzCWMoVceQaOf74JpNd3elTcRfaCXQKCJn/\r\nPL1iE6NepWJH/4c6yGc0HVber6tfixno32Biwn5C68njptkK3tnXvS7BAQMf\r\n8j646jsHKeTyQNTgEBKbhfOWIm2+F0hvGWRbL6HZv6j5jvDSzsUBC2fpl6Op\r\naYMpF0MLRqLgE9p2aeUYx/QLqLx043izbMsKGt96Xlrg7yACk0WzdhpXuNKH\r\naScsMbRc4INfx+0O4lq28vhuPYjse4J1ceuLMfZ6VaVT5Kb41nhJobxNBiXC\r\n4wMoPukrrLuD3qhTs4UAy5Ji7i9+guiDEHVwZR4XILQNcx+4ssabqmndBnIb\r\npbQVgHd76PISw3t6OoFwifMlnGrx65hX1HAnRSSUxLYT0q8wnU9LW+dRWjar\r\nw6UR1QnDv9/6mvk4t014TVOnjmgDAEesrWU=\r\n=p0Tf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "engines": {"node": ">=0.10"}, "gitHead": "7d1f6691d8f76a85a84214ce210427d6b055b196", "scripts": {"lint": "eslint .", "test": "nyc npm run mocha && npm run lint", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test:ci": "npm run mocha", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "prepublishOnly": "npm run build && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/estools/esquery.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "A query library for ECMAScript AST using a CSS selector like query language.", "directories": {}, "_nodeVersion": "19.5.0", "dependencies": {"estraverse": "^5.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.1", "chai": "4.2.0", "mocha": "7.1.1", "pegjs": "~0.10.0", "eslint": "^6.8.0", "rollup": "^1.32.1", "esprima": "~4.0.1", "@babel/core": "^7.9.0", "@babel/register": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-json": "^4.0.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/esquery_1.5.0_1677722050556_0.8463666160281951", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "esquery", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "description": "A query library for ECMAScript AST using a CSS selector like query language.", "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "nyc": {"branches": 100, "lines": 100, "functions": 100, "statements": 100, "reporter": ["html", "text"], "exclude": ["parser.js", "dist", "tests"]}, "scripts": {"prepublishOnly": "npm run build && npm test", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "build:browser": "rollup -c", "build": "npm run build:parser && npm run build:browser", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "test": "nyc npm run mocha && npm run lint", "test:ci": "npm run mocha", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/estools/esquery.git"}, "bugs": {"url": "https://github.com/estools/esquery/issues"}, "homepage": "https://github.com/estools/esquery/", "keywords": ["ast", "ecmascript", "javascript", "query"], "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/register": "^7.9.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-json": "^4.0.2", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5", "chai": "4.2.0", "eslint": "^6.8.0", "esprima": "~4.0.1", "mocha": "7.1.1", "nyc": "^15.0.1", "pegjs": "~0.10.0", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0"}, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10"}, "dependencies": {"estraverse": "^5.1.0"}, "_id": "esquery@1.6.0", "gitHead": "07ee329d6aaa6e468114687468e5c6f28a7b7beb", "_nodeVersion": "22.4.0", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==", "shasum": "91419234f804d852a82dceec3e16cdc22cf9dae7", "tarball": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "fileCount": 13, "unpackedSize": 1037210, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjd+vZSIjltDSTz37dbvVWxGELJXzDGz4q0GK9FGQ/SgIgKrqb0/vFASxLVjT5FMAL9JkVPtTSCm4b4tMhIZIkd8w="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esquery_1.6.0_1720456518074_0.5304937281801345"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-24T00:20:02.985Z", "modified": "2024-07-08T16:35:18.589Z", "0.0.1": "2013-07-24T00:20:04.295Z", "0.1.0": "2013-08-03T19:06:36.521Z", "0.2.0": "2013-08-06T23:48:02.992Z", "0.3.0": "2013-11-04T23:31:50.965Z", "0.4.0": "2015-05-05T04:17:31.458Z", "1.0.0": "2017-03-10T23:12:16.863Z", "1.0.1": "2018-04-02T16:01:27.890Z", "1.1.0": "2020-02-12T18:14:15.571Z", "1.2.0": "2020-03-23T03:56:46.904Z", "1.2.1": "2020-04-13T20:38:37.463Z", "1.3.0": "2020-04-15T01:17:19.865Z", "1.3.1": "2020-04-16T18:16:38.750Z", "1.4.0": "2021-02-05T22:51:56.573Z", "1.4.1": "2023-02-16T01:26:33.748Z", "1.4.2": "2023-02-16T13:44:41.994Z", "1.5.0": "2023-03-02T01:54:10.770Z", "1.6.0": "2024-07-08T16:35:18.418Z"}, "maintainers": [{"name": "jrfeenst", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/estools/esquery.git"}, "keywords": ["ast", "ecmascript", "javascript", "query"], "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/estools/esquery/", "bugs": {"url": "https://github.com/estools/esquery/issues"}, "readme": "ESQuery is a library for querying the AST output by E<PERSON>rima for patterns of syntax using a CSS style selector system. Check out the demo:\n\n[demo](https://estools.github.io/esquery/)\n\nThe following selectors are supported:\n* AST node type: `ForStatement`\n* [wildcard](http://dev.w3.org/csswg/selectors4/#universal-selector): `*`\n* [attribute existence](http://dev.w3.org/csswg/selectors4/#attribute-selectors): `[attr]`\n* [attribute value](http://dev.w3.org/csswg/selectors4/#attribute-selectors): `[attr=\"foo\"]` or `[attr=123]`\n* attribute regex: `[attr=/foo.*/]` or (with flags) `[attr=/foo.*/is]`\n* attribute conditions: `[attr!=\"foo\"]`, `[attr>2]`, `[attr<3]`, `[attr>=2]`, or `[attr<=3]`\n* nested attribute: `[attr.level2=\"foo\"]`\n* field: `FunctionDeclaration > Identifier.id`\n* [First](http://dev.w3.org/csswg/selectors4/#the-first-child-pseudo) or [last](http://dev.w3.org/csswg/selectors4/#the-last-child-pseudo) child: `:first-child` or `:last-child`\n* [nth-child](http://dev.w3.org/csswg/selectors4/#the-nth-child-pseudo) (no ax+b support): `:nth-child(2)`\n* [nth-last-child](http://dev.w3.org/csswg/selectors4/#the-nth-last-child-pseudo) (no ax+b support): `:nth-last-child(1)`\n* [descendant](http://dev.w3.org/csswg/selectors4/#descendant-combinators): `ancestor descendant`\n* [child](http://dev.w3.org/csswg/selectors4/#child-combinators): `parent > child`\n* [following sibling](http://dev.w3.org/csswg/selectors4/#general-sibling-combinators): `node ~ sibling`\n* [adjacent sibling](http://dev.w3.org/csswg/selectors4/#adjacent-sibling-combinators): `node + adjacent`\n* [negation](http://dev.w3.org/csswg/selectors4/#negation-pseudo): `:not(ForStatement)`\n* [has](https://drafts.csswg.org/selectors-4/#has-pseudo): `:has(ForStatement)`, `:has(> ForStatement)`\n* [matches-any](http://dev.w3.org/csswg/selectors4/#matches): `:matches([attr] > :first-child, :last-child)`\n* [subject indicator](http://dev.w3.org/csswg/selectors4/#subject): `!IfStatement > [name=\"foo\"]`\n* class of AST node: `:statement`, `:expression`, `:declaration`, `:function`, or `:pattern`\n\n[![Build Status](https://travis-ci.org/estools/esquery.png?branch=master)](https://travis-ci.org/estools/esquery)\n", "readmeFilename": "README.md", "users": {"jjdanois": true, "kaizendad": true, "flumpus-dev": true, "chocolateboy": true}, "contributors": []}