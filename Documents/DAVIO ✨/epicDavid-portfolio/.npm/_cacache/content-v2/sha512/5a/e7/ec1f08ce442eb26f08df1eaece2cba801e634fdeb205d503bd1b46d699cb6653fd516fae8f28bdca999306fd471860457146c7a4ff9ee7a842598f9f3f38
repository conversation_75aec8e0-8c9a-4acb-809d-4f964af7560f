{"_id": "@babel/helper-define-polyfill-provider", "_rev": "37-619c920128c9a8d55c1cb1224aaa131c", "name": "@babel/helper-define-polyfill-provider", "dist-tags": {"latest": "0.6.5"}, "versions": {"0.0.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a67fe9fe7000832c09267c57190904b695b6b7b9", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.1.tgz", "fileCount": 15, "integrity": "sha512-OLNwGAwO6HPglKFsdt6apZlSH225nFDCwJg+Nfu9o2m9GHHMUYd9X9V4fbcwyiyCAoy7MBxR2qSwXoaa/Ep+nQ==", "signatures": [{"sig": "MEUCICzZJlIuuXiqKkS4OO5/POuHLi6/rT5aWaR3aD/p1RSUAiEA6KBZMHc1DAGmDRa/x2fiHr5Tvkac5Wpx0Z+YeSA/egk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezX8xCRA9TVsSAnZWagAAw2MQAJo5c1QNGVvWI4ORFCmm\nybml0zgMe9ip3sYtzIOT6Yseb/SUK5oR3nrTORQWxFbus8FtYLOp0AFjplx4\n/JW158b5b3OXwGQiMeMofF4VaJn7s2M2O0pu1qbbmE9NEXGW69fuuQqgeU13\nil2jQl4f0Z4/2NijfpP1xeXkEE0dDudWrWQ6UDuAbmqiVG9Q+zPRN6qELnu+\noa7fV7YTWkyN1p9NuOkmdKoYEMb27/CH7yw9pt3Hn/myXgxDoo6Zoxh21bAr\n+MsLU4ryGAmQxyisn5TR96htvb18NOeiN7RJoD7ASFHOXQJ+3OiaSLXGkI/u\nrJRe+7OPbS4dqGNd03vOEq1QdTusOhxcPCKvEHmouBpJVe3vgqKYtPOzK2Mg\nkdV/xe78Gty6sEhFKXaSyjhhPOrNE4VNY2ubcBagc4oPaZHbpucz62nJ3tMX\n7UITY0Xebw3QS86uxuBleqqHzLTLy/5Pms2/ysQ3ATo4SB3mcUvwPK9q2b0P\nWg4aQEATgvL8IZ/dGglEEy+rdXF9XV0md1Sv2MWIK645hrPAMkqMuze9blXy\nwVK2/IUzarT4Cw4drLsmij65EYUrF73VYVHoNyyNHpyafKpyQSgDka5iORaa\nSLPgaFFU2YyuwhHbFBINfvA2z8Y/4x5u32AfTB2mVFyMBAsUWxxzZimcj0wA\nZtpP\r\n=T7TU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "82bf8ef0c6f375d290434a1c083983f0b8c6dffc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v12.16.3+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.0.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-compilation-targets": "^7.8.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.8.4", "@babel/core": "^7.8.3", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.8.3", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.1_1590525744906_0.6770839818867049", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c6bd27c2a990285df028cf4e5d952fc7b51de499", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.2.tgz", "fileCount": 15, "integrity": "sha512-JKslvq5j0hyH7pnw8KdvPbUbCeqNeNJvFChsaVqqQaASW/F920wIRVoU5B/14MHk9sH6Z94gbymzOTab7v9nIQ==", "signatures": [{"sig": "MEUCIH6yxVOUSNhfRLMmmOajwyqhzpYE13nmDNdenC88x95EAiEAxmZIwqKVJCL15fE95SvMyhRiblQLcTGGfhdaElqb6IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCN9FCRA9TVsSAnZWagAApQkQAIzUkKElCUP403sEi0bH\nDMTQHTyXtQtKIxbj1VCOheoAk2AwXIWjZTChbft/7VrGUvDPohQWzVT3QN88\n8o76r9K+umSR+eSsfhXOLyO2xji4ADX1vomuYtumkQZG9yFS4T/9hcsaKR5A\nxHlE/ddwkj0z7AMr5FpYU7q08Hub8oImcU7lpEmPJDt2SDKloRgbWB8+fIBZ\nHs48rKE8faTcLY/P0ZXmC+1B3wOvRexDDBKSCtq6w8nDuzm4XDZi/k0LUegY\nICiatiGW71bGHHv02wYDjW7D3diKkYtTE8KHAsyD0ymmd8OBnnSfrFRqSycn\nf+G5hmfqisjXKavkLOcwPzLOwYgwFaPpdWPBSdRfZroeyrwTJWcpkiZJZq3l\n+J7xNhVJOLwZ5+df82143wQWkIsCkwi1aiB+A9I0CoXvmLg4ntCgPD0q/0Sy\nHxhkMz88f7KWm8jkAiMDFDxPPYzGwZPA9QADHoz35tyskA6cvihq7TTIUjyw\nuUIgWp3jUSdEDW8rX6uRCM+iDJ4ETaI232/cGH0i1sk1Ke9oeKqRrGOXiyqO\nKvAfNEeQHL3kYJShQBAUiQVo4pvnD7FSuWtZqQDQQKR0xemdBuNWQvVmRfgX\nDHwIqy9BeXEZIIJz0cjlEb7BhtVJXcJ9X1wLJm7XpFgcshGNcTrhh41+Ukll\nW00a\r\n=2HC3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "884798ac0f8dec9d8a4b6fc18967acdaf6596836", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.0.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-compilation-targets": "^7.8.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.8.4", "@babel/core": "^7.8.3", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.8.3", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.2_1594416964587_0.0648406671333579", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "df9da66285b884ce66417abdd0b6ca91198149bd", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.3.tgz", "fileCount": 15, "integrity": "sha512-dULDd/APiP4JowYDAMosecKOi/1v+UId99qhBGiO3myM29KtAVKS/R3x3OJJNBR0FeYB1BcYb2dCwkhqvxWXXQ==", "signatures": [{"sig": "MEQCIAvemlNagokB4EpTfjgWNPuw/iHobX3cYyiEj9HfwsM+AiB88Mjp28eBtjZOV4KsJ/abvQLTuA3QooTHJXjuUcYquQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTYDaCRA9TVsSAnZWagAAcR8P/3/VrgbdCBRyG3lbtWBG\nGpy7sMHqASBXLo1vcrsWftgiJSVbm7pz1hl0qXEIG/pLV/0dNBHLd+E10Jl9\ngUqn8UQ5/g1pho1jvbTCEYRnQFjTzOVV6T/pEaoMsL+fmrq3x1yI1T62Y/Eg\nO4I0CCnVduOB42E/tmpY9wM9TYCvTlCKIlNiNsIhRMkXqOuPivhsKuHHPATP\nqjSjME2CXd3BZjoOBqiqU8syHsrCt97pZMCPJfMNZWsv/P48/u998ySbpOHY\nEs34MS/yMmu7uco+cYwVRVbabY/iXwjSLPjHNlplp6m3kMAwXgG2R6MOsLd6\n3Expng4+fOI1l83zJIjeLwCyFLw5G8sQwMwENX8SCQydDywe37JVaY6sgAuE\n9ZcZtkQpIhmLLLHH/l3P9FkUkodBz4VPo3Bw1mZV3ED+Cdr3JASnDyn6Y5gn\nfqPESRAzJM1N3245cl2jvj6VKse7BFLCS8Mw8aRKxMdneirX67GIOc0Z6mrJ\nAqQvS5Bl3238NM0YBZcNa+Qt1tmDVLEedInV3K60z7lFeQ1i6zMybi/rR1yq\n10hUZkcuW4S7zyRLMUSoDVj3SytBMPyMF2Yo56s/atj/V/F7l22RF8vQIsuK\nMAKhX3b4tMNU6jDp1+rh+l1n7qx6kty46AdhvxWbtrk481s96sH1jzeI++o5\nv1KQ\r\n=UIXT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "84d6f5b367b9044dcf25d8539a638531effb5add", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.4.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.3_1598914775295_0.970045424042425", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.4", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "d8b382b8796057343b27a2094944f8d5ab6a2561", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.4.tgz", "fileCount": 15, "integrity": "sha512-wlgaWVW0uGs6mFZC/ghJWgWwAjhzjgjGcuedBw3TLFFtXXbqBLBwJx5NPboXfWPNazhpduWdM+Rly9E/RpX5Jw==", "signatures": [{"sig": "MEUCIQDFzW8/45DpbnJTHs0y4s9kAEEomrgCWFeCk/hYYf2UhwIgL6G+B8vasr4LqVdqtuegA6P59bUplFk4G0vKmuAroPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeFhoCRA9TVsSAnZWagAAAZ4QAJbamTg5rKyu3iF/Lz0z\nFXE8bM3oOQ++OpkQ5L0d7oh9Gnx9ZZSftHnvOwnCij0XyCePcS6uJ0rkS6ZO\n2CpOdBDKGAz6dbHXLNqv5TOe5kR1zTWy4i/+xvtZw9cPh2yts8cFzDgEUabo\nM2DfgjmCoknyj4AW1AtN457/+uLENlqwbz5DMfZ1LE3QlVhxGllE9mJf0H47\nLMuCt+nBty+YnQuu+KOdnAjKEQ9+uNNqeKbbe+sLRCBNw3syLTGq+rKfH8j+\nKdyNk3H6qFlLI+pgkwDyvSWuGqPs9gkqmzZcsU97Y/DPyUETfq9tnrVEcbaU\n3wnl7YqMIXYcwSlPQJIbhpBLHD/1p4H4xS9x0fGJ7Se1L61U/0615n9qoEBm\n1ZtUafm+2DWe+efYWsgbaTOl89Z+VXIMS0CrBhMySGto/PL1/jrXQzE9GSeU\nBdBnD5I6tM7fZZ+gCWeKFnN9MWh/1sqPiBN9bL9W8yyOUOtzJwST1aIh4wU7\nPNhtp778+MKpVbIhyCF/3bhfJ3LwlhG2oz04qNqLbXgNOzeF+rcnfFmUeG9N\n3tYZlaSjZzuOTFI/DtU5u51TcUTwvBgCKauelyPLBiGBUnMT4sOR7EGj+Lf2\no8uByMGCnm57mzOwljFiaLQer4bvQlhLKBZdRKkRj8SKpL7loG+3+BjjX9oE\ng85K\r\n=icHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "a3e08229b65e7224eee3d822322576ec922a79f9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.10.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.4_1601722471678_0.15203418466245755", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a800224db85c562db2f31a1a6fe6e93d25b7b8d1", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.5.tgz", "fileCount": 15, "integrity": "sha512-SjyoYnKgK5fXyIx9X5Fa02cCXJtkp17t0iQeSv0cKu3/rDhcA2DszStwx3ugFy+mdZeTm24ZwlDU1n3/LSN6Cg==", "signatures": [{"sig": "MEQCIHqBUWlDpNSJq7nmkWdLi2oW2NOxh/y0/i2Upy6EU1N9AiBl0mLkr1iM4MewmXFipBtEADRwgMq6cC58q+HaDUXljQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqVQlCRA9TVsSAnZWagAAMhQQAKFl40Ph642jkdlc2PFb\n5pCTo5AUrNbcenGsnhUMFEyJ8+RxIG+eJFZQABU0Mpl1OfSZf+UoY5dPNlCZ\nuP33WDRPXz8VxTk8QcICu8iZqsQ8O2tUF460zQr3Jp3AXuBYfj5Yw2A7ydCJ\n92y0wednO5AkuqQuMgdn4B2TRJ8gww4EaAndH+gOfBsOSYSjDQo9tQ5mZIrD\nAngiyqUiI2IwfxbO64DZvIhBYykRpH4Klboy7MQAdanFLa+f2TJp/88Xx/rf\nsmoj82jbvPfrJNBAUhRaYdJXsY1nKrJQmrRg6N7qsvr4fA/bRjgvClLpP9YG\nMuU3uGKczwrTW5PiT5NlCyR8dLMnC3I31dievksEP+tnxZhX7qRflzDArotq\n17amojXfZHuClBYGNEyiKdcf7Y47U3OR/8bgK28owrqa8c1sVZ5XrmkTitP+\n5mlIq9YwAWaC8UuHJahn9Lqy034mf0FswfQBwUX2h5jwoJLW+r8CDyQLY5z7\nqrETHopDGI4wodR0uHs79nmgffjqB1r3+8/wg/w3e+USn9pf6+W6wG0eXko/\nL+xI9MrSA/+pjQZYYeTj6XgffwTk3M51kDjJagpW1Oi6ExMQ5E/ml8ZuFTMa\nIIz9R8+7h5aRt3bpAaztXx1vVny6nfY/yxFl1wtOVlp7hpg7vMxA0pIT68Rb\nYwCU\r\n=YUoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "ab1ebbd308b1111920327d95dc4c66a5ae4518f5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.1.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.1.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.5_1604932644888_0.1472905221465879", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "140740ace1338cd6fbf28dd747d886af04002997", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.6.tgz", "fileCount": 15, "integrity": "sha512-EJHDp6i0P0c5ul9hRHj9uldk67SRSjLxrHyceIzLfY/xTdwsFpzYFBqHMVok7aKZ92Sil73aMqaEPccVu8H4Xw==", "signatures": [{"sig": "MEUCIEIZLCn1VYNMkC15ot5rhAWx/N/88s4O5NvVS3OREAakAiEAitBSYWrBLDk1/kSk0BSTM6D3r/TzntYDDfuSB282c+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4UOgCRA9TVsSAnZWagAArSUP/3hLowHBA4hG+RaTyIfB\nDW2W6jve5RwFoLsCyXaRkaibPJSSJ/7zdx8wQkjYt4q6Yg2fbrJdzKTxNfBp\njtktXwzyWq74VuajVmPc4d3iOA6AKWfR6fOtQqcb7idQGMwAzZQ1zB0qvzqe\nA4bdhZxlQFWkQacBhvqgyAVdrRtZizerhhCzsdHkqOr3/jYOhPtjIRMw0sX5\n1Z8SbuPJYVen0GvgZmtD16Cs36UphC3YPHyWoWpn+VRZi/msMUKKs/Wqo3UV\nJS/r2uQ6BCgHGCS4fmwpBO/XJ5UAo+Whj2CgXY6xfad2MFRzXT6GEKjJq8Wd\nJUHJzPofr+/nwuUIN6wydxFv6LkkGmRkf3blzt+aY2XdXn87hnFiy5Eipsvp\n3FX3If+hdcal+AE34H7HyxAqTBB1PLgH1+C1BvXjHuVJmSnue2FDhxdBgEeR\nEbL2cusmXMiAgH+zovZ5x2/2kDle9yHi+jRitFtm1lmlZqTfjGSzk3yaKpMr\n2DJRD6p6rOQYS0/DWsCgSzqPTmDtaw+s/bYC6ULjzvGuznmojde261ah+O9C\nGa3fzdt6ocgJ3ttkz71nkcd3q5MrISibx59VmLPBVfI11kXyy3MrZA8tiHvL\n6bL+dA+7JIwG+nL3GcPht0ph8O8FR7+qTFqT/I2QNM2VHKDkL8HcMH7lr2a2\nh4IQ\r\n=sWIe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "04e366d5948db64ebe1eae82c3df42bb062c5071", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.15.3+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.6_1608598432036_0.44975445614284215", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "@babel/helper-define-polyfill-provider", "version": "0.0.7", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.0.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f61deeb9bdf72efdb8d39b534a65862506444e45", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.0.7.tgz", "fileCount": 15, "integrity": "sha512-EGERVJUd63IISyZaMp8WZirGvmPAEc1PZ/nYXFewg70nsW3qXQS1WakKcKQUyPlauDMXUGhk/ssl6RTR+qTpYQ==", "signatures": [{"sig": "MEYCIQDLORlXQAZnyYleP3P3mdcaxxk2L/iE92cEMBZ192jJDwIhAPof1WUuuyaH9CElFieLv8IWi8g0TEzoevsUW/xSnNfr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9yNcCRA9TVsSAnZWagAAVisP/R6PFaxoHhxc+6Iyj22P\nmKHUb5/U+NoAVwpXOtLjCZaeUDxK1BSrSSLivDVY1iy+8Am66rDFQsE9r72X\nyXGEH9CVkNPRkLD9n/DK4v+0ySPeJcXv//ewx6ENjcbqbP6Qg+6kywGgNEWx\nOkVvtvIXFXr0z0H4C0RHwnxdH9HIthjNRD5DpTiEi1svuLPoGe6ua8kAuYeD\nVuso0BokgefQeOizQiATp35gHLucgpDXTFZ0s+cK/db71WKe4whDkg/Uv3PR\nVrW4pZsA0HvtYKAAyaEUk499F0kva7iRXOIKWDVbkYOFH6iKA9/OJ+N8ye91\nWATbB48YdNQjscv6k3mc+F60Ok8+e58UUnSqYOPN2ctL/6BCiyRhkInX66B1\nrVT1tts6PJgtaCY8Zw3DctpP1uxHA4IhIBhI4TERh1jaC585rYm2VrTaIlAd\nrg6plwR8YFUE50RpLqSpYvGEmT/Od7WoE37aigMiZJZk3oU6TFlk1vZMXt9l\n+7J89XekJ9QRHRUrFMwsAINYyMJc9cyX5lYAHSHpAQ3kdpZA4T1BrIKl245L\nudJEzlp7Jcfbh+t/mhkgQbUE0RQ2jyo74FaABfq7lk927fsa5vmdWIRnq3mD\nxQ9YfrjXKBTe41ukrAgraSb5YMR9ymQcDqdnnIclji8jQev4hIwVjuYsOWph\nBprN\r\n=QfLw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "bb9300ed54b6cc33e09a89764efbb2bd2949ccdf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.0.7_1610031963375_0.6720032552269306", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.1.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.1.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a9e8a161bb594a6a26ba93f68efd475c935a1727", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.1.0.tgz", "fileCount": 15, "integrity": "sha512-/nX4CQRve5OZsc0FsvkuefeIQFG7GQo2X5GbD/seME7Tu4s2gHuQfXTIKup++/W9K1SWi2dTe7H9zhgJxhn/pA==", "signatures": [{"sig": "MEQCIGbeQCrm6E3wwMVuNB2EONDREw9f2pnpxZNh1mRXa5WCAiB0PP5/YgjWsq2s6JIAT/du8ronDTu/F9zpI1pPxK3z7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+bkXCRA9TVsSAnZWagAAuj8P/izJMsHPcq3CQ6ggbnbI\nQeAXcorWvxxa1s0+cuqFg4csfIxPU7Rgbv+LKlcgvF5zWQZyBJQ+GlzAICjg\nhgDjvchP2j+IW3ecL/tfQ4YmXFv4hTkFMIt2iyRIhfTojkgMUNInPa/RULDy\nY72vj6xQlMghJzO2eEILdRauENTbg1Ds8fQJVAgO1UHlRoidlPf8fjRwvcky\nfEzHkp5eyDeZQbsdGhyminr/XPsZO0kiyzBjirkEAOWZA/Tw09cnmbHNdAXz\n+erS7uJT6anx6GuZ8amwWS+Dv404F+GVr/jjJ0ZfM8o+tsJFAujl5JUSAwOE\nuMjnseIZs18/ZK+YPpzVDqw+tliylL6U5nitiBAyn0XuVlDLTU/aMLVR4UYH\nYDQfL0r1W/gw/iXGEjCeFaCH3T2d5JFla8bPkiaRMgKyBO3yXCwUbmKRxC7K\nZQfcabJT5idXOCItTcgxXaDuHQTwBeXkNO9IgQ8eAMUuKu4WFheQ0/mSC1Jo\nohmy9mTiR87KpvBM14BA6wTrgWVX06uoiyfvCbHsyqUPvPEFSUgg//glaIEK\n9huyvADX5Rm+V4TdNiquadA5djRSv9eysSOkkFoIgtvVLcihdRFECoPjIswp\nYD6aizEPUiQqFuLWZgpub1UIyYQSiTnhXptzryiKEt5cm8+1BOWXOMTFeAfN\nhpa4\r\n=vvQn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "440228dfbaff5d0632a02f295002579fe862e614", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.5.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.11.5", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.1.0_1610201366191_0.34822339160951365", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.1.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.1.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b9adee042f08c8fa6c73529e26248300e8c6277a", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.1.1.tgz", "fileCount": 15, "integrity": "sha512-ZwE8b0l6teFdOy6Zdd3KzBl74FyJeH1Vzz20ebBqcovUPjgnU5KUSaY060LeKgVvBrP8Ko+A9jPX9V5FRRr9Nw==", "signatures": [{"sig": "MEUCICMICTSxe0Ud5chwWmhUeGQmSt063/clsItLY6IGOXJxAiEAvw4UB9A4xuRgU0R3VGpqRspf2mOyH+QT57nAlWHe1EE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNOWiCRA9TVsSAnZWagAAq+oP/juHea2Mf1yvxnIy3kSd\nZHbcYhqJZQj0oD5QX9WMJJ/IPNQWfcYhRgwWmxhX4qI577WBMqa5wErMMR4c\nmMmnErLrqCtqT0b0pd2xF3ltu/WoEqARQwM5MOwn6gFF8pUxAr1+AvLaVXE1\nep0G3S3Kx1g78EJmuHI+fr6Z/bqen7lWBEXC6uMakKTuRqK7Dvb9v1i2sc7M\nxq+l02unMA+7BiPOmTJ4meogp8awap9JW1RZ2l04WqndHG4gPWFuy86OI8YG\nZQlWS+K0nXlaIax6z9eESWuya3ivWhsCkBwTDqmyv3CHuda1Na9pxbmQ0a/0\n4LjF7nKMkUhep00s553KOSjWMgTNr4VMErnX3up++oxKoHJNjU6FBoHIzKbt\ndxZ6iQcgd5VEbzA/5UXaQ+Yb+icZuxDSmBAqaW96csrPJVHH3RCyCTbA6SGL\nzozrrbT4Puhi1hhK4wFvGsMeJMPD3SFnLyusdJ3IGi1drYTxJIw8GB0NarZC\ne/jbMC6DVBo/cRzLzbFcDUBb93eh6eGNTHaLwhajk4Ogyn6JcCwjjK7QQhtF\n4VOI6Vi1GYRa7JDx4mwIgvOGF4S7xIjAUthUkb1ELbEsQ02ioSsf0xsdEXiY\nP/dZBlbnX4OKwM8kAZVZWwoJvm9FFFndExSfSNaW0b9bHEKrTW8xBScY8IZs\npFBE\r\n=UtON\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "187944845010adf2dc1a673af48d0d9e6d06d6b0", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.11.5", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-compilation-targets": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.1.1_1614079394398_0.33591442374336555", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.1.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.1.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "619f01afe1deda460676c25c463b42eaefdb71a2", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.1.2.tgz", "fileCount": 15, "integrity": "sha512-hWeolZJivTNGHXHzJjQz/NwDaG4mGXf22ZroOP8bQYgvHNzaQ5tylsVbAcAS2oDjXBwpu8qH2I/654QFS2rDpw==", "signatures": [{"sig": "MEUCIDlBymQF7FQMEAd+DXnNAu4UC0Lxi6q0ta/Kf50ROAvWAiEAueTUyW02CeG+Kut5XfRbOTMQmC8ZTHPe/aYJqu5G+U4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNQFJCRA9TVsSAnZWagAAPvMP/ijhF7oYtFl7zzFimsKk\nhHC7NRjoPh9V8smhhL6flf9zG02RdbZET+FRuwTb5e/AknW28sCnv1djIGzp\ntim3VXNtsQktAF0Iy2eQFOJdXyVSALTm82oeYnTd6j+TJFWa73HEfE+ACljC\niF0um6pbzXzAhvoraEqB4p2OOLQegAIHk0zStgmY42ETqA/eydbXo1h6udpj\nOTBZRB+RFa6DGpD1AkFx0uTpdcBQgnh6Z9MVhxDVrWyEZO2I7WjGRUpUvocO\n9aROYP9oVSI06qmguvUuvv/T0r9FKAVGIFVsJmy7obA4V2jOO+0UQSd0P2W4\nTKxRA9osbu2mhzP+MdRzeu+ymtkHZx4yLdemN5teWWNqCp85t3uCTiDL0n3y\ni3gIaSE82nluWierIHiOCJGhaQkOTpVd0I1eoDmUd+cOwTaeaSrdSEkL6046\nVR6qskD/CgVG5aYjhXF6eDosGU9ie+sKxgbH9of2R9Fc14rs0GIEQLEU054E\n6dKOtvrynKlql37OaXrS8/CH4Ix4qNwu9F1cpr3VMzB2Lli+lshc6kEROsee\nxUem+WAXYoiRJC0CsfEJKNWqtNtazYjXOgw/2OY5sXmXy6bwwqrszQltCu+S\ny1vzdnhmXRxC8DV+jz9iEClvHTD3xrbUDdZQqIdeFPtF34PIurPosrKrx4Md\nTQ2Q\r\n=3AnB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/config/loader.js": "./lib/config/loader-browser.js"}, "gitHead": "55f582c8ebc4b8a181a51fecaa92c89158cc7ac9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.9.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.9.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.1.2_1614086473171_0.1909213591578296", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "@babel/helper-define-polyfill-provider", "version": "0.1.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.1.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b618b087c6a0328127e5d53576df818bcee2b15f", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.1.4.tgz", "fileCount": 16, "integrity": "sha512-K5V2GaQZ1gpB+FTXM4AFVG2p1zzhm67n9wrQCJYNzvuLzQybhJyftW7qeDd2uUxPDNdl5Rkon1rOAeUeNDZ28Q==", "signatures": [{"sig": "MEUCIB8zRC/OsVHjhvknXY2VlOt23f/HGdaySmqRbBEMFE1xAiEAu7hEWM0iw4hdinUXbnjxcORPfPbHV+ZWRn/wjq80EGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODAlCRA9TVsSAnZWagAAe4YQAJ46EDuB1abKP/M9xGCl\nTgkIpJcU9Cj84u2HZKm0+aXjSkxTUzbWtXob6AcBHIiM0GCvX7H3OP8wz+Qc\nExXv1nTEFYSrdw+YMeF1V69hdMLaJ35j2EVfhtlre3KeMfTDy8ZyCjCadpAC\nsS0KXiyxGCN+RNNdTgohRzwECw+oxcdwDwWdRorhop8A8rlYLB7tQyHaff2Y\nULczxo/Pk3+1Di6edmL7wS3dN9Mq7TPUdeMauapch6V5HV4FB6uot/Irml94\nefsf5i+gJdKMH3Z1a2eMjSDqmrC+WaFP4Cvr8VUtLOxNnAuyTdMJ8XHFn+OF\nMcPIxcLxAgmC/vnGsgKAwSQDd7y3Qqmzsl6rLkIggRJ8s9GsIUN4QjEhxmB1\nNSPpY3yQGSUiGJrRKlVKCWNi2GFXHiTY5Ukltv6FtKhpiUTMG9p7BYnX0ji7\n145FeM07k6ns31U9ehJUdDRsADbVJ8r4TPHHX0pQNPkvzzzuit4Ak2DP5K9c\nt9SLQ5fiQfy1hg4AzFCQNCOIqi6gJS+3lUwu0JhxonZKWIKfyKWKICQsHikM\nD/meH70b00gguPlNNJ6fKmeQaXoddo6cs/lqZxV0iEZjiceX1Lv/aJREEgUK\n0fKkVX4bamjzUO+iPLucABmE4Z/9W5ot+10PvHEI1EFHKcMvnEMVyHBWhTaQ\nCEXa\r\n=MLwY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "gitHead": "6e6c7d2925b0f512397518d9f5ec35c925ea9960", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.1.4_1614295076656_0.5545538627077415", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "@babel/helper-define-polyfill-provider", "version": "0.1.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.1.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "3c2f91b7971b9fc11fe779c945c014065dea340e", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.1.5.tgz", "fileCount": 16, "integrity": "sha512-nXuzCSwlJ/WKr8qxzW816gwyT6VZgiJG17zR40fou70yfAcqjoNyTLl/DQ+FExw5Hx5KNqshmN8Ldl/r2N7cTg==", "signatures": [{"sig": "MEYCIQC7UpWqNp0cJVDNvrLbOgvmrPeTKCSr4WWIFAmiGnLrlAIhAOxNW5TKjMf0I+NHMzRS/Dz0TMbFbSMfChniejpZNLRX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPfmYCRA9TVsSAnZWagAAK/IP/1mVGiZDowVhaBLo7dNN\nKeYnml4jvOlb0wUdfOSuRHtYn0TBygDRIb4ofNseHAe8wt+cU5SG7yD3Zkfe\nDjpFGIhwkBF0uD2Gv++6E/FIOapwG3gEyiZJL1YM5fkAg5U662aPlfx0ujNy\naTYKGlRhH7aajnYhXkvapEBAtYcDCqo19Fw+Xj9y0rzDbqPLYDLxrZwDRRTn\ns6i9xgRfe2YIWNppPI6hymJ1UNjihjJ3Vi9FaQHFw/mK0unuWq5m0shGuAyD\n6+DwGZy3N9t3b/wJnSqPv1hfvk1/FIG1fBIUoHuSYlzKcPEH7shXtU839Cd5\nz7K0BK4ed4NS1RN33Uaya1oJf/BdD7Vzwwnk6gu0pNQjA4hNIPGPoD+xgWHQ\nJcngDS5v8TrscBGgQt05Pe8bmJwKIb2G2JxtfqYgfz2whXrL7F/fnaYHGOJ2\nciurHGfFv+UYtBLbSNr0qRYtUFEF7mageZ4M39fT2Q9UWLR3aHmp1sGEjdzl\nRScz1g61Fas1QwD83mdvU5INYPH4A9NkD664v19kji82T5cCXb/HKlfJb6rj\nlOwA7ik965nKhDnLfPd8R1K/QxyGID8HJpEAwNGAk6VTOFgx7QGngu5jLua1\nEofj+/vpsFG946N+BeV9Xs7wYNQnotXyBcFxPFKlbPhCWpmQZuxuBbVRrbky\noz4K\r\n=Oyz9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "gitHead": "cca90e89debf689aff23e982f8a30bc3b512c573", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v15.10.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.1.5_1614674327584_0.886653768444682", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.2.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a640051772045fedaaecc6f0c6c69f02bdd34bf1", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.0.tgz", "fileCount": 22, "integrity": "sha512-JT8tHuFjKBo8NnaUbblz7mIu1nnvUDiHVjXXkulZULyidvo/7P6TY7+YqpV37IfF+KUFxmlK04elKtGKXaiVgw==", "signatures": [{"sig": "MEUCIQCge7zLRRSfAs/iipmYwATEatkmNunUfKTIkR8QxfCUhQIgTibzR2I7/JgZMpJrRklBFYdWq2huRomUQDzBL3HLIhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZJrWCRA9TVsSAnZWagAAbCwP/RQvVCd4uuN3Ktd+z38/\nAYvVodktUFBFhCVwUnVXHPjQ4yeU3yAnQjMplr9JhyCEqJnatnQoh1nqpzxd\nu2gW85l6uLXm9bu55wRCrtlQTTXHqcO4zkLpGffs5uv1q37qiGmadhbINkdU\noSQZ/3ewQCFr1ggaOjB9DfoTkrx1a2n7G9pKnFgTkxcGrgtkjlcxGrdTwLJk\nGnwgtcMumlnCa7+keViQAJm9VeTfXY/mm081x5FN4jp4tnK6pNkB8TdbMPbu\nKaOhOeNBn4EY8L0cduaw3bqLHXuutfVRWCi4R0i9nCD7JmfSwt/hHjvF4A7V\nWuq5LV8Rq5vAqMe3tSnuz76tDCLrws1pWQZNrwGSCRs1ZLC4qVCx0kC5eB5a\neBxSD3PUPevEiznPtdyEUPQj2gFZ+V9B+zTkj2yy7vGzQL7R+/1bByGz1Pew\nT9Nf7kfWlE+wvJpaDDx3Iw9oYeXANZw0A3We0Abd8WutRil1xeYlll0sqdWA\nmWRH3PzKGdnPfMvgMi476Giqn5XmiNx0Z0Y2WJ9a7Ud9arpxHalEeNvcd5g+\nDfLucDFw2VpiToOxZ28ePwqF+tl2VS8qkdjqJqOZGmO32yd7ANcosEd/+SzR\nWssR5L7r+n46S02Q6qfAc792LORVXfOLcHqYOpf9syBDfvvrvm2FvYAxfAUN\nxGt1\r\n=osMy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js", "./esm/node/dependencies.mjs": "./esm/node/dependencies.mjs"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "841e713e0002eb8aa167553fc43840e526fa2d8c", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.16.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.2.0_1617205973932_0.5769875896721532", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.2.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.2.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "e6f5f4a6edc3722152c21359190de67fc6cf664d", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.1.tgz", "fileCount": 22, "integrity": "sha512-x3AUTVZNPunaw1opRTa5OwVA5N0YxGlIad9xQ5QflK1uIS7PnAGGU5O2Dj/G183fR//N8AzTq+Q8+oiu9m0VFg==", "signatures": [{"sig": "MEUCIFndf9aZwu2l01Zpnm2Xe51w6Ou7b2tZ7Wm4ZfsTlgYGAiEA2A6ybMArboy86FjmpYvpObdDSAgleYlIZpIWvJik57o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqDI4CRA9TVsSAnZWagAA8fEQAIFrc/5emQWRNMXlyEHG\nWnKkb/k2BOdvG+iARWZc0N3bnTzzPrMgRf5IRGw3UMqR3YGaJBvkoc2K6FoR\nI9+IdQ/6o1QZtBH6iv4mN981VjTBvhPxB2ITXFY0FomCLoa+dlzbNqayKeMb\nl4pEjx3tNinSBC2b9soPvczjPPPZ3BCvUhRkaWmhAslD5OESz/2W4jYHe/sh\nMZqz0sddFSicdjSnwhKdZcaW7huRMDmGp8s2o7xZQYNhnFwQZqBByyFgaDZx\n29YzysXEnIfm5ykM8Ktf98kvV8HwfGlp89C+zIy5/nkVVdZstfahxQAWOz+C\noySwbq6Qgcr18SYQ1/hcJLeFBpDpwE8xzRKI2ETYRvm6VcA5NtdhycGSGMDe\n5Oc90sDI7lyNXaJEUaVWOKxioD3ty+nyo/QUYYCcPpjLIngUKRC2vdHUCFZP\n6dK1+iBH+FuCLtUi85aDKvo4K+dG/a8FCfkeLnaSRWMKllHkVnzViJe8Xc46\nqXSWNVPDn3tKmRH0vEwwE/5eDDEQdQwaXe4MxPaD/2NgNjUTSl1GyvLkyAhv\nHjE4NYnih8WIcqKe7l2UazPz3c46Biavp04J/okHljPqOUy2MNxwUaQl3oKI\nCsRk4JzvGBL0DieqsVkIBnEtRFzthSN++QQHHZFs1X/0LAdrd6w+0Po9UARh\nmmm0\r\n=8Fu1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "a82aa2032d84ece15914846fde5f1544701b0a04", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v16.1.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.2.1_1621635639670_0.23036224234144775", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.2.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.2.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "b9f469f11b38b4c947f069e2cda44f5a56b8b533", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.2.tgz", "fileCount": 22, "integrity": "sha512-XjIrEdsW56J7UXKr88RWr6EwjyWSu7b+o1EwFUysFvME1nTNdg6z9l+zaSj/uMFIorUuIAjTWDQULUQauQG3Cw==", "signatures": [{"sig": "MEYCIQDQOdByw4Wp0hFcTaLqI6YELnV3kYk8sweP+i9gS0DaggIhAPKaQQrnWSHb55tGZf+WfXaRE5xos8ac4upPdxTLG/5U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrh6UCRA9TVsSAnZWagAAEXEP/13ZykDFDKnspaIK8FhF\nZMuG7zoiumhF7JtqepghiL0ePLstq4x8qWJjI5MJSkdHeAVrRyhklE+BP+19\nM2QJjsw37y6LaQGbqvEgy/mqVdgpHQElYRo5pPJw01N95hB93D9B87scCgYT\nzgoRD8ozB9CG2WMKw2IYRKU4zWV8ZmYjf7r6OCJLz5QkIx73tA9micRWtWUV\n6PYUL3feGYuztRd4RV/ksy5tB+z0hFnLiChlW1KnExuHkFZ8roRGtSOvDjS0\niEwR8fAI47Z5wIquihhmcLNopcQ+ezFa+EPqlZ7V1pmwj9IIgQOPUmeQYxV1\nJHnaAtPoKKwWOOGwfQG0zWX3Ilko3PJ0/CyaFQ3FfGytIcs5VGFgOlSVqVau\n4pnEoRy6yYr9AqDJBv0R0+17SS3lXMjPQK7Zi2sM3OeTpC6ZgrZcThlqAn7k\nXclEnWT2oKrs3iC1jLnN/FrRvCWOAEHC+QifOVC6bKv6sa1UHNxvvdnodNuY\nT/F2cObWlyOUD8U0S3f6k1QijhAfbZwvimEdlxePa7lYTN/TKflploO+ggLV\nwqWl4HkBmuAffHK92ybRU2B5Pu/t5qJmdhPiB5bhJwEvyYGtN1uz+CyzSeXV\ngbuQC4nlA+OmX2KU1I6x3vVsbhn09FWZXBqFS6j9C5zKJyYyMg5Z3WaEdX6n\nNx+0\r\n=0WHV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1db1e16a7e6855094c52a6cf9b98410e3f0e80de", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.2.2_1622023827991_0.7237030809082943", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "@babel/helper-define-polyfill-provider", "version": "0.2.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.2.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "0525edec5094653a282688d34d846e4c75e9c0b6", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.3.tgz", "fileCount": 22, "integrity": "sha512-RH3QDAfRMzj7+0Nqu5oqgO5q9mFtQEVvCRsi8qCEfzLR9p2BHfn5FzhSB2oj1fF7I2+DcTORkYaQ6aTR9Cofew==", "signatures": [{"sig": "MEQCIGxe5I1kEYsKS5NVijBxGBCeUIQOWDjytjCZo7iAR7JWAiAxy5wwfOCWWwHXKU7NO5U791iZq8eCH1RNVkGu3CWX/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgriSeCRA9TVsSAnZWagAAd2MQAKNDDuB1a0Q3zBjkSnDg\nSxU9j1Kd6L5GqHsh6zf0vneRWRnhg+vn27fpz+DDiN4pujyENewrwp6d2iah\nPK9EQrhaKjUX2qW7bXfrcFRgxgbqJ+kn3vNKy5fXCswsdR+XnOXiEN5lfrxE\nxHsLNAowxBCgI0BW/yJ5pIrOuukPPDKIpnSQBEsOx817fsgHRra8xrPdYd3J\nxnGWlzv4HpOIZkP/pfsTjmM+s5zCT5xjWYYrfxg+/LMh0j3DE0UfZfhOmsQJ\n0WjZui72NTy+cgUyx825Y1plaureU3lNmYaHOstEgZ92wjyRLayX+SUsc4XX\nIUn7XXLQ/4g9rbRwQGga1YheLY6pfoajuKlD4Z3bI0Kkl1C8BRhj2WH4uyV5\nYxYHFKtRWJ3aqqcHV/400gMqHrHW56o+DOKBUnnhg0nrltpp8H+HyM5O30gC\n254YgVyFXOSZZpHKtxCmZS5rZtykIZEBd21lobFxf/po9ZpFmT+I87zp61jW\nBFH3bt8zTS2vItFo9OEbePf6YAjIy8H0PWaS7aUWiqvdCQ+vcSkjjnk4GHKS\nWElLvDPqBA7NBIIELvTxxCUAo++1r8d6VvIjWd8NWAD933WbpRN2gzeHsjnk\nD78FLvckduwZAUNogc6tlZsCqEppnCsiQ0NjFkh+ZUIkl328RFV5M2sgPX0T\nEOnl\r\n=Q72r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "9ecc6171257ca4185ebd880a75ae0232862720c2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v16.2.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.2.3_1622025373409_0.2137497506357524", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "@babel/helper-define-polyfill-provider", "version": "0.2.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.2.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8867aed79d3ea6cade40f801efb7ac5c66916b10", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.4.tgz", "fileCount": 22, "integrity": "sha512-OrpPZ97s+aPi6h2n1OXzdhVis1SGSsMU2aMHgLcOKfsp4/v1NWpx3CWT3lBj5eeBq9cDkPkh+YCfdF7O12uNDQ==", "signatures": [{"sig": "MEQCIBxVK1VQGT1llzrE5CdQuXDUZon3AY6QWN15KZdbRadpAiBzgdtKQjEPj4htAqD4dTnZQ3AxLYBqqgcRqfpOaroeGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201909}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "de48880e867791eaa94c00ce7132dc259f3c777b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.2.4_1635546016599_0.14527217133863934", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.3.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.3.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "c5b10cf4b324ff840140bb07e05b8564af2ae971", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.0.tgz", "fileCount": 22, "integrity": "sha512-7hfT8lUljl/tM3h+izTX/pO3W3frz2ok6Pk+gzys8iJqDfZrZy2pXjRTZAvG2YmfHun1X4q8/UZRLatMfqc5Tg==", "signatures": [{"sig": "MEUCIQDp/THqOIx4u8X/v+butZlFilJLXBGJD2H7ekxaqQjM5AIgej6GZu75p6xB8LXiEGiWmaKTk7VqRM3k5eDP6OoRn2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDOCRA9TVsSAnZWagAAX6sQAJMz+B7fJ0uC/ug0SD6h\np5S1gkz5QFX4ZsI/P37Jx+szg0z6/A9ImnstrlEyAh8CJhp9r4xq19IltTXR\ntIu5JvYQg6jkQkHSCEH0D+o4LRoY3R3R5pjur/JfFxvbeBcpxInG63op6z2t\nrKPktM4rRoxtF993MYwPphL6QmPW2sPzh6gSwl4TkYgdDeOJFal5UXun1+eS\nnxvuz7bFQi9IeleAgPisP/js6RknaZtmS1aTEmYnsoOzeixbDRMpT8mOb+O4\nxQ+OimvNWH1H5hgiK7J7bZiENHb4Mf27fqiCa3ixle1BYl9UChaiktnpbhxo\nrvXp5DMdpyQCEUpQ98JQclGvbG+GUj/Eoy5avyF7+ETUiAaN3B+Ccuc23rPI\npsZFDZoX4wLim98tJtgr/qrnvjABP11YA6T/YIfngzeVn3Fb+AjCQQSjOGJC\nVcJ/GHy6Nc18Dbk++bxr+N9V2bXm0tViclhbDLJVryLgwfNo3WjcE04Q+0vP\nOYdBIANVUANCkGtFcHgsxSyWSS5yMeZFvTioQGesXJsKk4YjW5hFWkQx7ofu\neFtzYxB6k3EZ9EuWCi9ynyv8J0ae/1KpUlbvbuC1gpvL0PE8Vdue/jaiSq5y\nerIuVYdBM0NYAnHQiWjJTEZShnvO3/vuHo7lJj/9sK2Tde43xcOs8d7jMhWZ\nLh72\r\n=4mOt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "b2688bbffcae0f0c6ef5cd6ddf1abacc574060a1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.18.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.3.0_1636799699827_0.6999036722279381", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.3.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.3.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "52411b445bdb2e676869e5a74960d2d3826d2665", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz", "fileCount": 22, "integrity": "sha512-J9<PERSON>GMpJQmtWmj46B3kBHmL38UhJGhYX7eqkcq+2gsstyYt341HmPeWspihX43yVRA0mS+8GGk2Gckc7bY/HCmA==", "signatures": [{"sig": "MEUCIQCTFvOywvyw5PTMXVoqRNW/SpYBY9qDKFtT0f4qqWWDigIgf1mAsqHGWon2/Q2TIt1rb8/FlOEkgk6nVO5qo89c+h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4uKKCRA9TVsSAnZWagAAsO8P/3cb5Bi11yX+3EyPtAC+\nImMd8jJq6I+/3VlNqjolnYYmK4rt7OBHqfdmUySVcYEkrIUWfIGogSMuN+vX\nbvGknJE5fPWrZ4e1M7yEuWbxqeq7qV060kJnaCHy4WrRwdOFnXwhx257yWiw\n5qsc/rNuEFd3HFDO20TcvzdlS0jttUQWR8sAYhZrHtjZt8iS+u4j2BFmmuTC\nRg5M0bkWEeV+UuBHCEF4m7mAomHUNR/W6QH9CMy/N//xcyqPCf3yM4LLOLQg\n5PAQ4rbfq50ybqLQmz7D9y9SGqeYDbfLES0Ecqm2MdWb2fBwf4ZtocvVX2/k\nTWsLiGG/TkT6hFxENOz9csK3oT83MlUaQ2WO4WHpq3cUxpBmnWwWIcZa00jp\nyUl24WH2k4hfxIbXYXr8xG3fELfYoU3qsGyzamKFsUjA84WzMY6vAUFQaV0Y\n5oWVcZTZavWKbnU1CEHJ8lKZmUGVMy9VFHguz9jJxtoj+9i0HQY+FninIQ6y\nYsJV6Jb8rZL4VGftmC+OqR70UeiNQsdVqzwUH3IWLivhaYZos4p11/vUQ3m5\nvKrwirBb3yFw4MlHwQifGLo2766uEhSMOLWZKWvUsAnsMB9vOJkjQbYaVsiH\ntAXEs1GPHQwE4GxHFKmIhrk5nrnXKqrfn1vRxA8kfgpiJOTx9qjLuPrRBL14\nifPJ\r\n=rJY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "f5eb1c6d74fa29afdb0a3a533dfc5742557197bc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v16.13.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "@babel/traverse": "^7.13.0", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-compilation-targets": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.11.5", "@babel/core": "^7.13.0", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/generator": "^7.11.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.3.1_1642259082489_0.6032668801327383", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.3.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.3.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "bd10d0aca18e8ce012755395b05a79f45eca5073", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.2.tgz", "fileCount": 20, "integrity": "sha512-r9QJ<PERSON>+uDWrd+94BSPcP6/de67ygLtvVy6cK4luE6MOuDsZIdoaPBnfSpbO/+LTifjPckbKXRuI9BB/Z2/y3iTg==", "signatures": [{"sig": "MEQCIHKASa7UZ63Ne6esOMRh9kyis2ke6FYNluVIcm+lqNC3AiBiWkV2ivrZFCkeWpBFNm6dq+BeeByh2MzaTmAOm0tA9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3O04ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTgQ/8Ci3/MQwGtfk1zYOcw8fEEAcIhXVk78DLoRKOfsxUGGI3TUDJ\r\nA/s5Qb6nABhKsiHqPlrDrmZBp0B2zX1pTbTKcxSxRqLII1pla8neDnmfp7iv\r\nM7qFLgiON+Njv+J6rZoiuDsEx0KsQX8zal/eHL24hoypKqexxeTI+musKtF4\r\nNpv3ESghdfrFayptGduU5VVXNAXK0cPq8u4NpFWevL07kTzpwY+4yk8GPNtL\r\nsR3W0O6OJINRoq7yUH4q3DPia4C3+OeWRHfh4YDD02BmeHxehvehjnDXybcM\r\n0gb/r+Og4RMwe4mnEWebR5/N5mppnrMWBTXVcq7neSJUFKcvg2tX6f9YbLHR\r\n9TkCa0O4BTi6iKkYq9MPpwkCa/qYfCshZQhY8mGfh3rg30iketZy+ZS6g6ZU\r\nuKnb4I7mekU+CgVpR+yL3HIM3YD5pSbwVEWj+M5X747zy9dnlivg7DNfmEOB\r\nJ7T74v40p+eNWrTuGOtDSiNVduWbbRVcvy/f+NRPNaPDlVzCGagahJqbngZf\r\noIzWt2hXR+egMqNg3EhTSOGHn0lmpe6TQxeLH6UOZm/Q8qRfSspJjOHJ0Kb3\r\njK5I2gQrflS87ns8JLH7VGrDOJ2EbwDV4ndlgW1yK4ut+ouDsxi7xirj7CVe\r\nP1oUvqZ76fa8XzZ3R6jP7NLZfhlqUswGFWc=\r\n=sSoB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "62b9025beeec450a1ff5d61fadcf63963aec5015", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v14.20.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-compilation-targets": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.17.6", "@babel/core": "^7.17.8", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.17.3", "@babel/generator": "^7.17.7", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.3.2_1658645815987_0.23799866255158175", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "@babel/helper-define-polyfill-provider", "version": "0.3.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.3.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "8612e55be5d51f0cd1f36b4a5a83924e89884b7a", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz", "fileCount": 20, "integrity": "sha512-z5aQKU4IzbqCC1XH0nAqfsFLMVSo22SBKUc0BxGrLkolTdPTructy0ToNnlO2zA4j9Q/7pjMZf0DSY+DSTYzww==", "signatures": [{"sig": "MEUCIF/EQWoel10YSfs7nVvEq2lzFjbPXS7QS58QQ2vY7XFuAiEAiJHFtb9tHkgbgPwihdZ3CY4Z3zTDsrPyT0w30h4LKRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH+hiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkRA/6AzWauTW68wV1Ok9vZipBOZWpvmXqmT5DaPYfy890KkqbMhw/\r\nhE3vwgkfSzTeYcngFLdUsoAtufZ78GQt/ccfGjYpEQ5jDKZF5aDOzIVhtb8y\r\n6hxNq6bpDTs0xDpLUstesZlgZzaIT6kWjF4+B80oYAfafJ6Oiz2NbSla59xb\r\nvp9OR92BaNq0FpU97GINLK75IJNOl5RvsRkI9S00FuYn3CSR3nI651AfGQEo\r\n7k9tgBB18h7w7AnZVvtvzoAPe1Ef/LM0QUkk/BBpw2tpBO1xmquhVrHAA5Ob\r\nml490AjYiOYVNtYO0Sm50ckS3vYkaQrXRtLbYMncDdPeZSQ0ws97z54JASBI\r\nMQ2naUn3mfaw9aDzA7VwZ1UdJGsPq65C/XAe0pdvk6xQgR6jJvoVYBd6wuPD\r\ngQhHglNSK41OwuXziNmbUT1XsMyf7k1eaIYRQzMgeX6EyEmEQxiS5Kphm+R9\r\nwYcxhQTRuOY98KzT0A5q33zGwuURXgK2Lq7GRtOkdYX9G3OCn073M0hTh3sj\r\nZKsPOTNLvk4VNVx3trUCP7xJm3oDzevtXbatHHSKjYaPAY3wTPtBYCuzPAxE\r\nziTcPQw0DN/rO7S2QSmgU59RyDS9wdV1xouE9SwvWgRyf0+52oSqtnsOtRpC\r\nVYZBDAxJduZMRInJPoYJgc09RKGQEcNAP80=\r\n=iQTg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "42b9477c199c0c5420b45cfa8c9bb892d94a64af", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v18.7.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-compilation-targets": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.17.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.17.8", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.17.3", "@babel/generator": "^7.17.7", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.3.3_1663035490095_0.6162474424612572", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.4.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "487053f103110f25b9755c5980e031e93ced24d8", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.0.tgz", "fileCount": 20, "integrity": "sha512-RnanLx5ETe6aybRi1cO/edaRH+bNYWaryCEmjDDYyNr4wnSzyOp8T0dWipmqVHKEY3AbVKUom50AKSlj1zmKbg==", "signatures": [{"sig": "MEUCIQDOoxAenjyoOjfThyrnjbToV38A02rp0+fp0c0dVbUexQIgdByexCszKl8FhtTRiJ3jE60fyEoNH1QhXDvyrYR2yBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201230}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "391a1f4049fe1d6943ca8e91cf7e2e23f3f1ef73", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.0.0+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"debug": "^4.1.1", "semver": "^6.1.2", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-compilation-targets": "^7.17.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.17.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.17.8", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.17.3", "@babel/generator": "^7.17.7", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.4.0_1683713938278_0.9266601179393246", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.4.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.4.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "af1429c4a83ac316a6a8c2cc8ff45cb5d2998d3a", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.1.tgz", "fileCount": 20, "integrity": "sha512-kX4oXixDxG197yhX+J3Wp+NpL2wuCFjWQAr6yX2jtCnflK9ulMI51ULFGIrWiX1jGfvAxdHp+XQCcP2bZGPs9A==", "signatures": [{"sig": "MEUCIQD3mfVOv3aI15mjilDQ8ft8MG42yhblxTnzNmI1vQFcGAIgI0K43UgGY0oGu56dbBh5MlQlfSoYDQTRVWhO9RY4MBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201593}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "74956db5d547985ac8e60bf1af56f4c61af12e4e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.3.1+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.4.1_1688546287937_0.21944381833373416", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.4.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.4.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "82c825cadeeeee7aad237618ebbe8fa1710015d7", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.2.tgz", "fileCount": 20, "integrity": "sha512-k0qnnOqHn5dK9pZpfD5XXZ9SojAITdCKRn2Lp6rnDGzIbaP0rHyMPk/4wsSxVBVz4RfN0q6VpXWP2pDGIoQ7hw==", "signatures": [{"sig": "MEUCIQC5vPFeBVuKF8hNq7VcODonASXdpaj+L94PDQ73oLfpUQIgLQMGdnCr6DL7IjTzPtZ+kvM7Nj5+LNM/B8kWbzp6zYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201610}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "0e8cfb85899c8fb01728199d81fd37108e1668ab", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.4.0+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.4.2_1689930192485_0.15291042371370267", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "@babel/helper-define-polyfill-provider", "version": "0.4.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.4.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "a71c10f7146d809f4a256c373f462d9bba8cf6ba", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.3.tgz", "fileCount": 20, "integrity": "sha512-WBrLmuPP47n7PNwsZ57pqam6G/RGo1vw/87b0Blc53tZNGZ4x7YvZ6HgQe2vo1W/FR20OgjeZuGXzudPiXHFug==", "signatures": [{"sig": "MEQCIBXjzUm9jYD6sWJk3Lf29wsI3YA8NomIo0kg8TR0xwBpAiBxT/wUbGs7K6dEJ99J35EjjHWU9obS9MxGx/lVcfYltQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202343}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66a6819f44a57152798cb3b0a9272c65752bae86", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.6.1+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.4.3_1697007735139_0.6833312859004623", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "@babel/helper-define-polyfill-provider", "version": "0.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.4.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "64df615451cb30e94b59a9696022cffac9a10088", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.4.4.tgz", "fileCount": 20, "integrity": "sha512-QcJMILQCu2jm5TFPGA3lCpJJTeEP+mqeXooG/NZbg/h5FTFi6V0+99ahlRsW8/kRLyb24LZVCCiclDedhLKcBA==", "signatures": [{"sig": "MEUCIEU4/QbxxUgPGvDasOFeY8R1Xd+8HwmqstrLAq0RrR1fAiEAjD/bVVqxq/5+S+69yDVsW1K71bOLc5QtwJktiDHazmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202476}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "984c56c59568283889c3f0f89e58d370e4fd10f8", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.4.4_1702304406607_0.14828632673282782", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.5.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "465805b7361f461e86c680f1de21eaf88c25901b", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.5.0.tgz", "fileCount": 20, "integrity": "sha512-NovQquuQLAQ5HuyjCz7WQP9MjRj7dx++yspwiyUiGl9ZyadHRSql1HZh5ogRd8W8w6YM6EQ/NTB8rgjLt5W65Q==", "signatures": [{"sig": "MEQCIA4/wLKc8Gy1eaTVTMo0OTcZQMC9HO/0O6XLyQHcGMUlAiAkuWsV6o5hczGui0a+exYn6lDD0zaQNqT0zwvx1QD2wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209838}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "9738ea2a12643376a52c9be30c20ac19426a88cb", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.10.0+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.5.0_1705571830150_0.7721819831102381", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.6.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "4d1a8b898c8299a2fcf295d7d356d2648471ab31", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.0.tgz", "fileCount": 20, "integrity": "sha512-efwOM90nCG6YeT8o3PCyBVSxRfmILxCNL+TNI8CGQl7a62M0Wd9VkV+XHwIlkOz1r4b+lxu6gBjdWiOMdUCrCQ==", "signatures": [{"sig": "MEQCIG1vpA5MUrYtsLVotkOhp3JIdeWTcbTFKoP7DtS1XXvZAiBMDUeOCLQG34VHJORjWM/6Iv6rHMsefR0rVJOamqLVDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218498}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "58703f07c9cff9f27d145215265042094739a175", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.6.0_1709919058658_0.7591531475782434", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.6.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "fadc63f0c2ff3c8d02ed905dcea747c5b0fb74fd", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.1.tgz", "fileCount": 20, "integrity": "sha512-o7SDgTJuvx5vLKD6SFvkydkSMBvahDKGiNJzG22IZYXhiqoe9efY7zocICBgzHV4IRg5wdgl2nEL/tulKIEIbA==", "signatures": [{"sig": "MEYCIQDi0o1yZ8MxC+kK5f6/smapdApNcJ4wjsRVbWOxax5fBAIhALO1p0QT7c5IVD99bhCkT1SkwpqVLfwe/BEhJO9gh60e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220557}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "1ce88db2507db2ef3d2ed2a2f920a3cf0b9364b5", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v20.11.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.6.1_1710259176323_0.5895338377216652", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.6.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "18594f789c3594acb24cfdb4a7f7b7d2e8bd912d", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.2.tgz", "fileCount": 20, "integrity": "sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ==", "signatures": [{"sig": "MEQCIDzUE5TyZaduznI/r2pwlJSsz6+e898hz7nEaHVzI9+DAiAyOcJXqR1dsnxQAALoi5gSajhU8lIdK6JKP46Znc2DJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221206}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "2da4da8e1a3d87640c88a3cd7e650cbb1c049a33", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v21.7.1+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.6.2_1713809613883_0.31062632621503794", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.6.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "f4f2792fae2ef382074bc2d713522cf24e6ddb21", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.3.tgz", "fileCount": 20, "integrity": "sha512-HK7Bi+Hj6H+VTHA3ZvBis7V/6hu9QuTrnMXNybfUf2iiuU/N97I8VjB+KbhFF8Rld/Lx5MzoCwPCpPjfK+n8Cg==", "signatures": [{"sig": "MEYCIQDvcX67upjfMeSO4WpUQ0wbRZ/bruICuD7eLXWt7FFo1gIhAMdMzxiCM3SJDmBVAMO2QjI8vv+OS1NTPwtEAMCgo4KZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221565}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "66340fb145086a826c496f008f67488367846c09", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v22.9.0+x64 (linux)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.6.3_1731351011935_0.6366997084672124", "host": "s3://npm-registry-packages"}}, "0.6.4": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/helper-define-polyfill-provider@0.6.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel-polyfills#readme", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dist": {"shasum": "15e8746368bfa671785f5926ff74b3064c291fab", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "fileCount": 20, "integrity": "sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==", "signatures": [{"sig": "MEQCIBRuedyjsYPbmKPJQbLC2YoJzAilQ8BGJu6z1WHjQzlnAiAzDqzDvJNetClwv3dyvnxuKzMXsqMABuYZLTAeVb2+XQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226587}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel-polyfills.git", "type": "git", "directory": "packages/babel-helper-define-polyfill-provider"}, "_npmVersion": "lerna/3.22.0/node@v23.6.1+arm64 (darwin)", "description": "Babel helper to create your own polyfill provider", "directories": {}, "_nodeVersion": "23.6.1", "dependencies": {"debug": "^4.1.1", "resolve": "^1.14.2", "lodash.debounce": "^4.0.8", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"rollup": "^2.3.2", "webpack": "^4.42.1", "@babel/cli": "^7.22.6", "strip-ansi": "^6.0.0", "@babel/core": "^7.22.6", "webpack-cli": "^3.3.11", "babel-loader": "^8.1.0", "@babel/traverse": "^7.22.6", "@babel/generator": "^7.22.5", "rollup-plugin-babel": "^4.4.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-define-polyfill-provider_0.6.4_1742390646422_0.1415508682293356", "host": "s3://npm-registry-packages-npm-production"}}, "0.6.5": {"name": "@babel/helper-define-polyfill-provider", "version": "0.6.5", "description": "Babel helper to create your own polyfill provider", "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-helper-define-polyfill-provider"}, "keywords": ["babel-plugin"], "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.7", "@babel/generator": "^7.27.5", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/traverse": "^7.27.7", "babel-loader": "^8.4.1", "rollup": "^2.79.2", "rollup-plugin-babel": "^4.4.0", "strip-ansi": "^6.0.1", "webpack": "^4.47.0", "webpack-cli": "^3.3.12"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "homepage": "https://github.com/babel/babel-polyfills#readme", "_id": "@babel/helper-define-polyfill-provider@0.6.5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/3.22.1/node@v24.3.0+x64 (linux)", "dist": {"integrity": "sha512-uJnGFcPsWQK8fvjgGP5LZUZZsYGIoPeRjSF5PGwrelYgq7Q15/Ft9NGFp1zglwgIv//W0uG4BevRuSJRyylZPg==", "shasum": "742ccf1cb003c07b48859fc9fa2c1bbe40e5f753", "tarball": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz", "fileCount": 20, "unpackedSize": 237196, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEdKw+pFUuds2eCYt8i7htAV4IxcgpnYEO9oLwtbGyWOAiEA3U9M3U5iTztH69ezTrE6ewyko49Bb9e2rX9kDvfQPXc="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-define-polyfill-provider_0.6.5_1751042267594_0.9112943965033315"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-05-26T20:42:24.584Z", "modified": "2025-06-27T16:37:48.070Z", "0.0.1": "2020-05-26T20:42:25.011Z", "0.0.2": "2020-07-10T21:36:04.724Z", "0.0.3": "2020-08-31T22:59:35.454Z", "0.0.4": "2020-10-03T10:54:31.839Z", "0.0.5": "2020-11-09T14:37:25.043Z", "0.0.6": "2020-12-22T00:53:52.232Z", "0.0.7": "2021-01-07T15:06:03.565Z", "0.1.0": "2021-01-09T14:09:26.290Z", "0.1.1": "2021-02-23T11:23:14.561Z", "0.1.2": "2021-02-23T13:21:13.355Z", "0.1.4": "2021-02-25T23:17:56.857Z", "0.1.5": "2021-03-02T08:38:47.834Z", "0.2.0": "2021-03-31T15:52:54.120Z", "0.2.1": "2021-05-21T22:20:39.806Z", "0.2.2": "2021-05-26T10:10:28.118Z", "0.2.3": "2021-05-26T10:36:13.604Z", "0.2.4": "2021-10-29T22:20:16.785Z", "0.3.0": "2021-11-13T10:35:00.043Z", "0.3.1": "2022-01-15T15:04:42.638Z", "0.3.2": "2022-07-24T06:56:56.190Z", "0.3.3": "2022-09-13T02:18:10.257Z", "0.4.0": "2023-05-10T10:18:58.467Z", "0.4.1": "2023-07-05T08:38:08.142Z", "0.4.2": "2023-07-21T09:03:12.680Z", "0.4.3": "2023-10-11T07:02:15.501Z", "0.4.4": "2023-12-11T14:20:06.806Z", "0.5.0": "2024-01-18T09:57:10.357Z", "0.6.0": "2024-03-08T17:30:58.865Z", "0.6.1": "2024-03-12T15:59:36.511Z", "0.6.2": "2024-04-22T18:13:34.059Z", "0.6.3": "2024-11-11T18:50:12.161Z", "0.6.4": "2025-03-19T13:24:06.647Z", "0.6.5": "2025-06-27T16:37:47.817Z"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "license": "MIT", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-helper-define-polyfill-provider"}, "description": "Babel helper to create your own polyfill provider", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "# @babel/helper-define-polyfill-provider\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/helper-define-polyfill-provider\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/helper-define-polyfill-provider --dev\n```\n", "readmeFilename": "README.md"}