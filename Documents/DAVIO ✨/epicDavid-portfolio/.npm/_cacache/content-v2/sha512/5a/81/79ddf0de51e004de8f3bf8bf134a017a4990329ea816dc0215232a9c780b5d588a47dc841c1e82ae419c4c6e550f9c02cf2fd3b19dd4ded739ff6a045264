{"_id": "tinybench", "_rev": "50-a89c8f90cc81b62cdf1883d74057d952", "name": "tinybench", "dist-tags": {"next": "3.0.4-rc.4", "latest": "4.0.1"}, "versions": {"1.0.0": {"name": "tinybench", "version": "1.0.0", "keywords": ["benchmark", "performance", "speed"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tinybench@1.0.0", "maintainers": [{"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true}}, "dist": {"shasum": "32e1861ae1db54d4a628f50ec520c77a1b28c47a", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-sLHtRpVmhQWa1tclT7aPhUhwLFstBX0t35hNou2nx/h+3x/JdXA6aL3uX0zh0jfZpOKqFc3rRnpsIf0JdjxkGg==", "signatures": [{"sig": "MEUCIQD72xKun58wSRc7cZI41FSyOUPvqqYTUfjqEA8K2E7ZSwIgPkbEW6DBw0h4b92RiZ+96CHTyMekktoUCsy8CqfGH1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia80tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrS2g/8CaqUWhHPgneJL/YJE2/Hx8zLwjiCpUP8GRaGaOT0HtWG0W4X\r\nY6+zILcvfc01N0peXS0OxKyGgdSJLfbHBMWdZEhFG1G9s9E8BL9fMgkd+bh0\r\nqH/4vtJuhWKLU4eDrVVfNMK1Hp2x2dBeRCJ/yLr5S+HlQfyyV+mQQTcKqwoV\r\npHUQXguPZvCeG6p9eOSJq7fe88+3irA3XScJ+/Jmxr42hhJx90GuOM8FL5aD\r\n9LJSBpmTsZP8ijBPBydhuKS9lOZNk80ac5I1BIyJ1IdIQa2PMRu0HUMYVdzM\r\n15o3ShFWkkrwzCIh2wVoDYj8Sw6QPAGd7Ev+XqqAPnS019By5pO7zHm2Fa4X\r\nMGtRhezU1+JhZIouygH3onr6HoCDqr8sor5P1IfNBJvJF5VeG4PHLARYKVek\r\n1rac9x7pWMi1Anth2iWDSAdhL2E45+2dC3b2TWMr7RfMWN4ZkyDyouzHutKu\r\n402CtHTZ+rAKLrKU45i8j/+Id0OM6mqo1lCl7/vVWs9MXIgdt75TwA1LdDbQ\r\nQTGJ13BmDhFwYWZos1pleLMY9UWw98aJlXjwYSTSVwTMlRV7DUeRibgSHdxd\r\ntV7jG7zXtgZm8IfobFNKOtkh1YoVwRsklfiycTIGJZgT3ILyDV8/6IgvFOVQ\r\nuEmIbQhxiL96cdWEmhhvlHcr62jaLQX4dxo=\r\n=OfDb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "module": "index.js", "browser": "benchmark.js", "gitHead": "1651c28b281105e770110e7172082972c16e2047", "scripts": {"test": "npm run test:mocha", "test:tsd": "tsd", "coveralls": "nyc report --reporter=lcov", "test:mocha": "mocha test", "test:coverage": "nyc npm run test", "test:parallel": "npm run test:mocha:parallel", "test:mocha:parallel": "mocha --parallel test"}, "_npmUser": {"name": "uzlopak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "A benchmarking library that supports high-resolution timers & returns statistically significant results.", "directories": {}, "_nodeVersion": "12.22.12", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "tsd": "^0.20.0", "chai": "^4.3.6", "mocha": "^9.2.2", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinybench_1.0.0_1651232045073_0.06189815667753096", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "tinybench", "version": "1.0.1", "keywords": ["benchmark", "performance", "speed"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tinybench@1.0.1", "maintainers": [{"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true}}, "dist": {"shasum": "555683d2d1168b3f4afe13d78603ee81f2bbd689", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-5LmWaEslFaiKM9eNzkZM6KFnpwoPGndL64Ckd9wlxvZV6znRTEVxY0oeoOHJH2YY1wVdv/BREi7AygImETFJDg==", "signatures": [{"sig": "MEQCIHQGVwvTUlqOaWc0+fBE5k7Ox+rW5zW4QCR7h2CD3v46AiB0pvT+SaiVQWxn0vjmO3uIdfNm0W0Zu5htxN0oM4O8YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigRlYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD4A/+MbxNkfT+DBBMBoJpZZXnk23z50YQ+7ByJGyUhjCuZ80p3Bjs\r\nhrb+YPtKP0AvASzAnnMYyuOJk2CrGFCS37RUGC5aGFS9vYMrmGo48cd1bAlj\r\n44tsJe7b0VIqMuNvdjqi6At08CuXoQ1jDfuQaVaY/XxO0v0MEbq8+7re6Jl/\r\nPLVTzugm+FC6KXm90Qm0Il+4rOEG+yRcBuzluF4jRRUp++FOD0Gl1Unzur4a\r\n2HR6FdE3LDHdsA2YELNp5w/nL3oDiwrZ5hDDKZqO2i9o3A9xG8cQPA5GjwtK\r\nADcTLA3gzEi7fruNDPEN7V3/3kzYwDzz56c55Sr38vRbXjxKrcB2FjIoBMB9\r\n4+RX5mbMgq4nXRV88dEzANBrtEK6YUj71xoiZiOx/lUBdSMtJmVzg0BLGZlh\r\noddEsYeh/mGf5H8RbTOL6FnIO6FfyrcRWN1llkGmtQAFyIyvDD6VcTfpPJYG\r\n7FWS04LpJif0Wf+hocMz4gcaFTvNMIQvhVsPCMqNvP+2hylIaAZeXi+cKKfJ\r\nMHHmYrXTGAalU7dwuSNe8wOuUwtQnnZ90CSuP97EC3JdjBCCj235hpKsKJWt\r\nacVEYuJNqntIJGDKw6vves4LDfbNtC92aWo8meJmFDt8XvbGmYdMd0kc+2bv\r\nXxSh+SxZp6nbiAR/QGjmuWqFUu1Kh1WwD4M=\r\n=ndQw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "module": "index.js", "browser": "benchmark.js", "gitHead": "d0306ff7dcddf72eb60fab4f0ca4ac52a3686454", "scripts": {"test": "npm run test:mocha", "test:tsd": "tsd", "coveralls": "nyc report --reporter=lcov", "test:mocha": "mocha test", "test:coverage": "nyc npm run test", "test:parallel": "npm run test:mocha:parallel", "test:mocha:parallel": "mocha --parallel test"}, "_npmUser": {"name": "uzlopak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "A benchmarking library that supports high-resolution timers & returns statistically significant results.", "directories": {}, "_nodeVersion": "12.22.12", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "tsd": "^0.20.0", "chai": "^4.3.6", "mocha": "^9.2.2", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinybench_1.0.1_1652627799874_0.5961276862981943", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "tinybench", "version": "1.0.2", "keywords": ["benchmark", "performance", "speed"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tinybench@1.0.2", "maintainers": [{"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true}}, "dist": {"shasum": "b4b188c14572bd8c5bbb5681bfba7f4ae58981da", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-BebpyGOoa+Mc4yUexuyY63wciXJshHHGeAP3SdJxAkadgfYYIX523zBmrmyzVbYofh6mXoU7ZtOjzh8n0kG2/g==", "signatures": [{"sig": "MEUCIApAIKODvBEdSxG2IhQ/gt2w2VRW7D6LmVK70oQ55qn4AiEA6Ie7wXR5uOCz2VhIkCo8taBaRslS6qWrqNzd2UBxM9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig4+TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ3w/+NBu32a1aJ2bKmtRLY9PoupixtP2KN21Nq0QaziXUju8UddD+\r\nUux3R1AUo1M73ZDmv8ROwuTgQVG+Exp1c7Z2hXKdhslh7h2VAQ6udRbfK5qr\r\npJAwnaD3IxDquEFCp6bw1n1nnkDI6ud7itXdqvT4dM25ZuUdfQf2Sa2tZP4h\r\nwil79bCkNbl5nGYEqz0BbRQYQhSU3ithHl9C7TcILDMqrNjTZVSIhFkvQ4oE\r\nMrIjbDFuGyhsrIrzYKhDkT4XuwB09n1bbVhoL5AxoCfa35abWx8fqphmW8KJ\r\nniF2zDijmsxrS/e12YkHWDvaoPNouxZffjLG5lwwVjzlVpteyPP8J2mmqITt\r\natMItofPEyaD9miMJIDgKWit3uL0nH0VjWybTbFKPyqMVXTY/kA8zrNgFg9q\r\nU9P1uRwli6G2U6ARgJs4s1az/V0VrJFA4VzdTj2V6ZHZpQrX9cFRS1kayhWB\r\n9zSs25hus89ICOatYO/3eYfJQ19PxvbAGlJdnLXAPDZHfkyAPGjx9D7jtR4K\r\nCcauqQfyfZdC0s3clnwhNuXL/hFNjbwDoOmJul9n2zFpOENDgERWEA7yRMk9\r\nsEoXpQbTYNtVQa9SKqxxYjXKeps7K3DXRJuFDIQCdThHgALU5EmJPXldz3Qw\r\n+GmNpNqDt47tD8PeXxvF1/sZwn4ghEGry00=\r\n=wu9X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "types/index.d.ts", "module": "index.js", "browser": "benchmark.js", "gitHead": "353a51b7e68ace3ddfe58e3ae82029b8c3141eb4", "scripts": {"test": "npm run test:mocha", "test:tsd": "tsd", "coveralls": "nyc report --reporter=lcov", "test:mocha": "mocha test", "test:coverage": "nyc npm run test", "test:parallel": "npm run test:mocha:parallel", "test:mocha:parallel": "mocha --parallel test"}, "_npmUser": {"name": "uzlopak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "A benchmarking library that supports high-resolution timers & returns statistically significant results.", "directories": {}, "_nodeVersion": "12.22.12", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "tsd": "^0.20.0", "chai": "^4.3.6", "mocha": "^9.2.2", "coveralls": "^3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinybench_1.0.2_1652789139499_0.9097063802175964", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "tinybench", "version": "2.0.1", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "7ca8ff64e71b3603997f87a26c4c97b49e1addf9", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-OJ5fxoDmzD7IGwK0KLpEgHYkXEMIctq7LXNtj75wyA/Lh6g7sUp8C7r28G9pA/lWqGKkuyInZmJGjWXHGqPHiA==", "signatures": [{"sig": "MEQCIFS+YxVc/2uaJWze5PxQv8PQW70v0KY/JvPERCxSdYLEAiAoM0Vc/GuW0+knhtIFwmlJCTdoI+dh7IpI6C3ZNSFaXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBglMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQ1w/+PVanLHKwalrUAA31cqPN92rKUYQpQRHTEd5h/hfxY7u6yhko\r\n8VC96WZKJ/DAaEt6eYlnsm0Kcyz6EremIPQd6tattoaTzSvmGwIViCSYee/J\r\niy/Bgvl2VJz6YoVzEMGSRxiPT4Wvj3+qyukf1ne9cXUUty8SC6+0jsbxJKW9\r\nDqjMW+TefASF9vos9LvyC6i9eQNd+dl04xqY5/F4RFeCX17hNs+dlWMHGgcm\r\n5Lh4D9i4QFS3tOo7qL3Ch66EkmyI9q4gcyZE3l9b8978t7ALh/P0/QFwEjzb\r\n0+lS5GCi56T2jh6MpENpg4hg9tXpPW5UUMOAJkJVubYGBvarDaOAqvIUXXfu\r\nxTrjAUQ/UkeWQdFgo/qQLiOOhwidqU+lWRZVaNIhB5vGRKifRPcagYS1OiRR\r\nvxUWr56vXXWX08SggM1efKopbqX8VUG4RqchcO23/w7TJZiH1CtYLmIe7qba\r\nbxxKKOwqMBuAqrH1eGJ91heHqPc1Af9XBkPbAdejBIyt1L3UKmaVmtoqiOAC\r\ngBbIuWZX04FrDI6z5JCA0cUuonWebtlknBneIpTHMxZPxS3PSOyPV/BsQbau\r\nF9EPrsUQXiGMPLkYPMbdtq9IeN9/wQ3YJA5cP0j4sRUh9RQMEe78CK9qM8Wz\r\nIHF0S1xS4dUEwh+F5noaMj2Xmnl+n/lD40A=\r\n=JoO5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "6b1a96f33d89374e9ce942430f057f89327a9d0e", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.1_1661339980694_0.4091057140236041", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "tinybench", "version": "2.0.4", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "355c0e25572ad588b2817ab5362fc6665f3e261d", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.4.tgz", "fileCount": 5, "integrity": "sha512-U/4wVLNw58gDkWz1l6m0begfvdCfrYNECEBHJe/RhGuRKxase9tjnY8Jr37t8ZnvME85M9haNALJwdYD/AKjUQ==", "signatures": [{"sig": "MEYCIQCG0Tm0E0MqJ2IA7vqG+j3XvImdiHRB1NE2LAekPaU8zQIhAIJsGZKNYeOOa5Jz0JOnfs7T9ta8ztXLJHW5Dmh4NV7Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBgqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSZQ/+MrQ4WMUAQnypVZQHMMIO9PuQmnZR7FPeNAifqsOc/iVzJ8Eu\r\ntlZ0jSSL10T+sjWvWP/dAvv2V8HETJ68fQDza52/ay3uelxPi+p9cGcp3AVf\r\nuO6vC9PRcbYdAVUbVliJOjqM3yJVm9QUDwdUMJDDAGD9QDIo33FHfZLhsSjJ\r\nSk4n0MSvU5dxvFUlgO6rsQnmybgQA9k/3rkv35Mbt503cUi1ARyoHbFPh60w\r\nbvLUPWkSuXRJLRKLheEqMhAsQPywKHstkwyWv23V3alqWLbTuxD33nLZVtfi\r\nddGRMVLq/kst3Oa0QR8mo/cNMVJwZv/RMuajFNAhdPfEKaXP+hTf6zZUKtMg\r\nd/wT4s/3btLJ6mnVq2EwlZETd8hSDnXiLb+T6EC2bjSATud0D8Wjv8sNEr8k\r\nNPRoRZcdQ7YOfaOIXTXWjT06TLVbD+Njx6h3TjAMF0VgAKvNfj/IYGNfpr8j\r\nl7hUby0071iXVdDCNRVaIIhzsovZqIODQyCDjKCZBFe9dd7jBfzsAVRPXmel\r\n/+O6lLrFhcHfRz6zGWCZNJFon7Vl6a9WZgeQpuSsk3/olU9XBlTiMWkvgxe7\r\nxKccslUctYK3brrM6tSbFiDunD2Q67cSBNqoluhDA68R5RmKXgmaZC4AvtqO\r\n8KDkrZjgSHVpACSMpxSO+qBwZEw31LZPtOc=\r\n=JPKo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "dc724557321a04260d4ef9cb2ffcff7b93a4c5cc", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.4_1661340323714_0.7446366765206025", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "tinybench", "version": "2.0.5", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "c23ef7c8437cba8cd8d7fd2f03ca7313d937edd7", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-VUnMVEKiRLSPaRJhTunQAAivh0OSDtt5TF1c5pXbEVXKU4gze9F7Fs8e7o5qIB9L2KJAW5AMwRcABR8FMhPLYw==", "signatures": [{"sig": "MEQCICR6T1ZBKe06xAUgAGszngixuIK/D/yvarjEWADzvZt1AiB9/uIOm/2lW5kNGGndfI1jPtO82ZXXHDklB1CIadF9wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBgz3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr0hAApKE5pX4pt9u3zkzR2q9bdAhYqo4h5m++/HajM5nk2ZmslmHg\r\nPLpj5/YaCMTBMqUmEXJ4/ifK7w0QKIQh5rJKQazF0Qo7n5ws3ci/h+Hkr1tY\r\nq7BR0FuIqMxzFckqzM8aI3974nqV2pba/JHcYf1qjImdHIksYcxednCEKLxY\r\n6OmPW5aGuTaiuR9GoPLmyfKClMofL+Gr19sREx4XAmhv7VYGUeWhCZaTmNuU\r\nGaKb78P+NW/cM8nXhWnObV+Hp5uXO+pTQ2OTVkeNo+vD9zh/Of0bt5DDvCO5\r\nQ1ZtaNrue6xv45yB/aqNZ3mzXjECv2y3On4OLd6r2gDMe2+BqGgTZlE0+tjF\r\nnQEO7s+W6DkU5mTfpZeOdrAq4Djm3iZ0F0wxSYvS5fvzHNSt3XxpKSK2kn6h\r\nCi28TiDlI6/oJAzsfZY9Y1rUoQGcF6QIe3MH2r0MzOo8jzHor5oGHtptaGOk\r\nEhXkz755Q/ud2Wmk+oc9NMMQehx9NQ2Ggg2ItgtAXjYZ5GRppCR1iHP1RiCI\r\np3doFOMfp8xeRhzdOCePsbm9rN5SE8Y3CLy8Sc92oUoJv67DecPgFwnXVyOX\r\nvUnf2uMnPJOU+CEv69Fhkh2qXnz0C78OGdFmBfydjCfDB9Jd5yteS8GMcGd6\r\nXD8o6eKgHg09oIKTdWdyESN4dbj4Ka7ojvI=\r\n=bCTc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "f76e809672e5622afdb9e000a91cd168b4ab9556", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.5_1661340919577_0.47615663568934785", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "tinybench", "version": "2.0.6", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "ea10475886c453d32e3e3fba355eb31c0d2a6991", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.6.tgz", "fileCount": 5, "integrity": "sha512-tgPwSeHr0qQiYDjxmYs3hkEcZR6WOIRd0TAUpB51ayG897BriC37bH9NUcpldx7SgrVVRu13ZwMPG4LXAzOKIw==", "signatures": [{"sig": "MEYCIQDFf4XsgetgA/HDm4xif+MZaWozyguw+GWpmEJxnyxhAQIhAIHNxo5oK9CDvEXSXHwCwQj12GcXUDqeSiew2b/m5CGy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBg67ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYxg/8DEwvJboX6kXR84jmTre9YK7oCyQryisLNas0THY6vvycFrcx\r\nQvXKBh64V5q1Obci6VX5EnFnZYooD3geTe8vLJP2npfVH3joybjAYhUxgjmp\r\n7tJrjSgnYUdxhc0rnzKWTv5LwVHwDb+VBZfqG/SB9XqRiUaKTOeIU0XvW8Lh\r\nHHcjHwYeC1HRsd3AWX6m32jVVfOKzVXre+U2E5Fh/4h5WoauIH077BSn6ZX7\r\n2ycL16C6ytk6rSIoSP3iWQGJs80n8KRv++ZKyNVX7Fog51EvwPM7Z5HL8Eda\r\nf7pDYEKZSH/dX+zuHTG0n4JOd8/5zlEEYCwcoRU7DeO1f7yO06mW27yWQQR6\r\nAKfBKWQXX/k2J3+k/bqpDNgeKmU7pBinjke/nTHjlrt/fz7iZicKHgvN51Lp\r\nvFfwu9Y9ABe1WZJrGxY4IlYw9RCH/mQtQTv9Xw72Y0WeFnXwX0EOT5zb58nI\r\nKenpoUHI8rzCEwkF+q+6cTygDzqb0pU5C+EEy2j14bxQgDI5/+JetuLOIbJE\r\nNmf0v0399bvA/1f+yRj7/+0C+qGM4VSn7TihJgmtQWxhzwKKIsr2bS06O8Bj\r\nbOYrz9XQJodBVuhQ8cEYzm41AkeUqax0ygcsJ3raefJfmbLt5/4i6Uhk6+V9\r\nkV42GxNp9D79UQi+koXd3/ZOvlW+B9fRgQs=\r\n=sra8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "b5a72b4c6189cd93a15ed7ed5b43583c45ebaf55", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.6_1661341370946_0.5291956919417053", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "tinybench", "version": "2.0.7", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.7.tgz", "fileCount": 5, "integrity": "sha512-iAsaWdfOCRECVOPlu0nwzJs+SI2goyAadyobv+R69OeAsYF51Q8a09LNIlDFWvba2gWAoAeMqKVjqYpoJlbeWA==", "signatures": [{"sig": "MEUCIHDI/aPAypymgaJRP5hg5ktE5ACJ4MUBoG+778tE2G/wAiEAvUB8vyr9eZ4cY0W6OsvgtpfeB+bVXtx5mU61T+pVPog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBhV0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqk/g/+K/uF8Hmb1oBF1gpUpcrpxRXPI52Vm7pYvBbLGGEhB9Hy253Y\r\nmXAPKdnX/818k58pS/eb66RHDULWER+k7HR8o+M+ke7dX9fQL9fOQGqck9xT\r\nODwexH/zPiexcoznZELGZQEbeBlhcaVvu7eFNiUb5GO9oGGv8pXKr6o2/CI6\r\nOXQ8QJs2U9ljVAnhk5mKnqixCwu7z3vatmkkJ57gTPfLLhLwuhMOHo5nED3x\r\nOrcyxKD+EFTeCoiIbXAmYtEnwUIHxbLYYDXUzQ/lIxFOkRbtFZospbbacpsb\r\niIky+4ufE4J133QP4F8GvFoW8Dq9o3uDMkMRYxuQGyDmiEcB3yZqPzVPhJas\r\nLuK/9E5h/PW1PkMK/BNRlObdOvhD0Kc88Op2NMLcJxmpwZBAbGup/TYxDy6K\r\nIAGqbZHk+6qlZokjUUfYf0tgR8XEtC490zCKJ/+LAskmT8TbYtc2OoNcDTtH\r\n7JXura7OqgtbmTiB6AiOXmCVG+Qz0Si0b+8dm9//74O3nMqOw2AsiDnYXDDl\r\nAoxQ4Isfg03krhovKd+oy/rR7p/34rPPP0ByndLlMpNQ1zwDgg6hzGU2m9ME\r\ny1/wFHDA9K75I3dmimWjJTD8+V6Vosy+AX333lKIPN+h62mIhd2o3R7Rv4w8\r\nommdwuSIJr04JdChsRJ0jtnuEtfjsgxQoeI=\r\n=jm2f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "3ad98340da8c35cc207436fd079e34793d4dbfff", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.7_1661343092681_0.822095421959371", "host": "s3://npm-registry-packages"}}, "2.0.8": {"name": "tinybench", "version": "2.0.8", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "2428122730872e465821b97ffe40f3fcd67a19ca", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.8.tgz", "fileCount": 5, "integrity": "sha512-m8kzoseL9tu3eozh/oBn54g3MCDnTMf2VE2dH9p/yazl8nFJ3oEYGsZcUt3oyrS3bBuML/KloTMeSm5JSASygw==", "signatures": [{"sig": "MEUCIEQzvOo7tOdMm4MMQ36Hy4m4y7kJvk/VOQZ3tO+u0ku5AiEAh4xFS1pV+ajwrZVgYUibn3cJL8ww2SUzxaKS1il9tng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBilFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxAA//bKBS3D+DnmgWtu86A+uHWmaaMWvzjQ97cQZb9EEgaHcGyIZI\r\nm8MTMDD3FxEZZHmRit8tcpmLNehBcrjGISuXia6sv0/6Cw14gJ1qGLwywWSI\r\nSkQewZMJQ2R9xlsBQ1njqiUKj/KMGVvS5GwkpIoXGOz6OVxZz3g1d5p5TZ8Q\r\nKUYuTW9zl5saD96R8Bu38NDCee+5FPSO3Mjui6SH1vnLUtMtqL4b+EidDueo\r\n0CMafcLMpNGNcwmQIA7xpcZk7E6S5Ocgix8ciyXoaT84CmSe4ZdMXuuLlAu6\r\n9+Ksd039a+8arCOzTIl0cI6NuxR/s7a+7CNXsARDh5tau/2w6oSqXL93+M7v\r\nzJg1FpMCo6E0DrCovy/AYiBJZ6G/hwgc/dOYk/+aWo0zfkM1sXQGNeq0hv3k\r\nbQFxax17juLVkktJvW049WbGHopDTQXvGngI3CU7o9o50CuLwsjazjxIsUyd\r\no09gWtnW9xaup8Kon+Kq2PEfQtoRwRbrL2jYWx8fWnd/NyjMM21qQCuHjRmD\r\np4UIwurd8h1lXwD3DLlMJeFn/1uM46OHIK4zWkbzLAwqeil/0/6XOW0Bkoyy\r\ncsfY+qiA+p95YzeGHCQseisvuGFnZeSqALTGNurNA6N7YFk5Rzz/4r+Nw0Vy\r\nOfvzsmTHY9BVqsIBckCXKVe3p5j7NpVgoJo=\r\n=DYHk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "7aaa756ba667922b1c0f0abeb372ebaecc5edca1", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"changelogithub": "^0.6.5"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.8_1661348165023_0.3929426873115942", "host": "s3://npm-registry-packages"}}, "2.0.9": {"name": "tinybench", "version": "2.0.9", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "bb70bd2c9688e64cf2132b14f7031ba7446afae9", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.0.9.tgz", "fileCount": 5, "integrity": "sha512-5CdQB8uXKwB9ru41FawVKvx1blQl8GYsjvwO3J6a/0uPNx7IgxUmhUzgtqALf55PTy+6qW/ZoRce7QFAa+by0g==", "signatures": [{"sig": "MEUCIQC/eUUQFNL+GQ3IYR+e0wCmdbZReHSSKeiKAPgJtVgTmQIgR63C6zbqF9d+/r4KXNTdLnAOk+CaY7rXzxq9I0/YmOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBuK3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRRA/+OAgr0cLSuNPhbRxp+QoPwZVewebMPuC/YSRUJobThgO7Tfyo\r\nIqi+1yyJVSBwV+3aSC+1Oy/n+JmDpYdUm+TSPOAuZHu4skXdyAZWL7Kb1Zq/\r\nYWErWeTzzFpSzkG9XZrEfU/Nqos59/TGkGWyGemv8DuPBcr5gmyTrPwVx08/\r\nLqvuqXUbrI3CH5sLSo7dLEZr6saxeZJsIQ6xBZkujCAU3WXdKywWQcpOxV3/\r\noCrM9iulNmc4pcTqxxoy6M1r6GCbfQVp1cNscEQg8yESPYEg9/h8OKAmx1pP\r\n5Le1Tsz4lLY+5NshYTMDDksTGIerthiP5FDwJagKiYN9xlQY7nr49QKuAbx7\r\nWQHC+nBoloaZ1NJDciCH6GJJarjV2Hde70f6QSSYwdfxE+/RPCbuDiIve5k/\r\nT+Ix5r68cosaxHUCvZULaltBrFvdxebSr/fR/+ljcshnpJS5Xzc+LV7KtuIz\r\nEF5D7PUi3YB1OUflcepUfd9g1WwIJkcr8rxgIUwWOlT9fHYFWawh0siof5u+\r\nGykGc13TC9R5KbFHCuv5ET1HI1zaYPllGWvpE3ApnGZ+w4I8I1LqJGYmCGdk\r\nTKA75UtAlS465hEI7EjApFrY1tfaLZt2N7ccWj23VQX6lrm7qexT77Q7IxCE\r\nOciOF74uklqGooYVHzLLQvGaGIgH/mvLO7M=\r\n=Josl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "2164d6b817753dd93b6387cce3818418bb9576f4", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.0.9_1661395639114_0.3662755927163701", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tinybench", "version": "2.1.0", "keywords": ["spy", "mock", "typescript", "method"], "license": "MIT", "_id": "tinybench@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "f1fa49a887ca04310f720df5ec5ab95de136e3c2", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-a/IJeaqnH7usTrfgasKrtHC7pHlfD/JDrWW7uDlAGXc/3RBULHRKe1DLZ8dqfPDqB+alKwrikyct0iDN2LMMUQ==", "signatures": [{"sig": "MEYCIQCpb+hQDUTVKW+YEYwjtXBBUI+BLUf/PHHbbc0r8HNn7QIhAKm30qg/u/U+DeypYYEDf052iF46RXHYcS4E6FQdpqU8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCJF+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQNA/+NZHglL+8UJ2Pwc3efRX5ihLb+c6OmHVkbtORSZFzEPxG4sSG\r\nQmiHYv7I5cEraBP/x1EvrM+dtmC5PxKwJ1Iqv8PcBgeES7dzOZAwGqX2QdCk\r\n+FhklKkFq3HvqikppyLXpxr0Rs2zQpGM0qdIY2deZROvcxt4qsZALLXNnp6u\r\nnW582aHE+niD9mqmhNKTwZ6a9K8bQ6j/G89Lae+3JN2C1o9cg9//CCOMKszF\r\nCrkSmkJhpUB9TFzVcUqgcFXRaPe41foDd+4L0BPr3MBXzSlxIrFq63xGNtmn\r\nsXSJcVAF97u5Am4wILtB5T4OvktAmfn40mBQdx/ys2iGOvHgDeq7jVJi4D3p\r\nTV9U4nxACW+oP5oYwkYrt7Avi5Tt2PzU2pYiaiPfsTPXUP1B65rtDVBkuXPD\r\npX06UxCnVm0f87f1Q276UhXQem4gcJMPP/ypYZYXaY1aa3mapfDkJB/+WpdJ\r\niTD5hCu7DoGwK6j29qvrjnJeAWl1yzGrL5c/7C/vRDksB2Oi7atOSxdO4/9Z\r\nK1NW2NPbspMtUWwdNb6wt8IuYHh3NhT1xqhboRCWtJYieaHaHwPNuAfEulzl\r\nomnVKoj9VKFzMp5UJOCuzsnJ7F1KmM2Sn5/YDB7maReoZ1pVMzXqESt3d4hK\r\nIpa0A0c+PqUZRLT23Xoxmb/MuvhmS271Fwo=\r\n=5aA6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "6922dbc1f2b6b592a14f5adab1d79385a037480d", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `6KB` benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using `process.hrtime` or ", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.0_1661505918071_0.27409195273216125", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "tinybench", "version": "2.1.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "33541b0ae5df1a7d43ce0d881f500b9cf060fca3", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-X7uGalztJkmeXCVyol5LfbpNo1F8G3g5xICabkLFerFiKGoPMMhLnjKwNbogml0DOv8KX4eCAV/kY8OVTDgiKQ==", "signatures": [{"sig": "MEUCIASJDvMUo5Zm6vjY9xDIRkz/fVOKoIZ3gjGGtW66Y6YyAiEAryYbD7JXczlMSfyC/kBM8ajShqDIDDwVCimE8ybg+4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCbFHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQLw//dTn1WwWDhQI0tNPygyLebR89OTCXKjdzm1qgu7abTwobe7qx\r\n3GcDX+gBhdkSzdaOAG0HuThkMpKJXEW+ZLqC9A0z9CNSZw5g7NxLfGyD2N8X\r\nY6KFFKFRC9rPif4bl4rL7/7zCp+O1/g9A60d69n2oeCDvkNn91P8mym2xrsB\r\n0Ne7JE20xAvt0jmGZgN+twfEn3uPDQV9ZWaoQv5fj65cqZZy7KclfVfzQ5h9\r\nog/o4ARfMW5U+fDWUK83v0iyGknNIwQXoW2luT4jSB9/WuWUKFZRqixJ5Xef\r\nJPBgtnwybS1aSMYzPgljfMvjwWCq2whNjJV5i0Q3l/+7NfH7JUnFxlPE1QRD\r\nsVRGOXaLKhReqd2cEl871HqL4hudhWeb/eqY7mYmsRj1Enudm/0Eh+wTE7zw\r\nZkTIubGG+h4CbDpVKb6vbJ/T8OaVg/H+fS1SfrBhIQ+7AXYEGcpn5XVyvms0\r\nqXLWYOndPBoLHMgBNsapQNzP92UDdU/PcW21oxyyuWTGbEyFrXcM3K9+qz1z\r\nbm1/JiEGNT0vocbLBvfpTztJGkTHKw7eFx7Vwv9Thqqnip9HQWTSrW+W+716\r\ndE6TYKbN6tU2MKlFP0LSTXkDHHvU7B7+ebtnaAWpiWVMcRt/oA7p3SK7OpL/\r\nv3PTOBOLnbYrQV7GfPi+nkey7zpqiRhmOf8=\r\n=j7mE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "5e503b89f28e1f8d5a609feabfe2ec406deb1e0f", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.1_1661579591541_0.9490182602783395", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "tinybench", "version": "2.1.2", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "2bf2d4b598c938837014bf9b9142037d78509af5", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.2.tgz", "fileCount": 5, "integrity": "sha512-stw9CxD/VbxMO3xQqYuJZQZMR73hRayreWd2bw6nealNjlFjkpPjRquLJmeuQw2zLKItQ94YpbaAJw0Bbm0vww==", "signatures": [{"sig": "MEUCIBs43GKjWcWx1RD/wjvKCu1dsOjhJqtj/lWm/lVMJxpSAiEAkQNvEETSVQDrsoEu1xyJ2Et+x7Z0p+YvvTDttKoIQec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDLTxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrXBAAh/S6AnKvQhlh5NkmnNeJloTI+TBIJGUd51bIh7BmGyaVkZSY\r\nX0KFTKkUsu8FO2wtesC1aKzAQ/MTD+Qjt+D9CfPyZSbf6SQsyrubJbXY544p\r\nq75LFn8WmbiVU54oi56T12xtYQ7kCLepDW0xYCo+hLU/L0p4LBVx+aQme7lo\r\nW2xsOMuafQbeCJL/s1GV5EWibAaoV0MbKIGoLhjPYK1b4ydhYKHSqPlygtW0\r\nT09qHEiEJdhbwnHiTPebSpkPjS6E95qm6V+2rs1yUTMt4TdZbuN7u1ktqTs/\r\n2B/xFsv1+5zLNmVv2ERpoVYriNsNb6doF1l+wpP/7K4UEShqjkQ+MoJ63jBN\r\nVS78ZZ0G1pe7fB6CHR3IwBco3GCjcun4lPd/QZo87F72vY2W0DXh3ny76IGe\r\nBGFBAN8/Yp5BQ9dPlgRKM3aaKU2Ml/I+28qtecUA0htmN87/RMf3eYL1/d9L\r\nd91Ep2ErbB30423vTwnprvfxmAWAP/zJEOgBjiNQ1IoHQWQodS+/j5y3/KIz\r\nJFWupi1dIzWsMrqHGYkKWOgsH/Ou/LrM1x9R/LDEEv1d8SfTVMtBgIlbUHzw\r\nXY2lsNbl4bPDYGrabfUdoqilxMlsPj1Wtb+8NhAtvSLnoO0dV+O7/DKot6iB\r\niR1sEtsNVgP9+/i9Nfh8ZPMe3rZl1dW0p5g=\r\n=auMF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "225fd81120a6657e8594aa9411c500093c65e8bd", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.2_1661777136889_0.6177827017717863", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "tinybench", "version": "2.1.3", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "f34bd7d0dcad2086cd9b0441c3db6401c78af19a", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.3.tgz", "fileCount": 5, "integrity": "sha512-HX<PERSON>cRGMD35vulAdRRXCKceLcws6aKfIZAfNTraX24XyK1SmcxJjWW1rkgmET+irNvmXBrNeKrcJh16XcvF9q4Q==", "signatures": [{"sig": "MEYCIQCzsPWQQeHnisIzgO5z+oZ7R9RuQS9J9+tempYrfr28AgIhAIEX6eeKCxgT5wtm70aR0w6AsATwZXbz02ueHlk1mvtW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDMyfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZLA/9GRDrwM3EGxy49Gmm4g7ZWOc9r7zpEUnvLwlrPd/jGu9TaTAX\r\nQJE5AglK/LfBfBGaz6Z/9D3tohrg/bIDKkC7jTj2LCrwZBIatBvjAC7bGSae\r\nTMBmv/Cxyc5Pjd6/AgXLtJfpLDy1GOB1mI4bEGIehu2SEpuW5kwlCy8TBlSi\r\nuFBoUEZoRwzASE6BMbBMfShWIqnyEqqKuUlVoQ0d5AbTgHIpzFcl/aEDpXX0\r\noUDUPPkyFfvQY+qTnUspbSGnHGqG8h3iqgpM9GzAdG48Q5OtckrG0j0JVEJ2\r\nphhBoihXZEkb7lJgl4VNhC/y4Z2FHukB5QmG905n2Zva9QE1+JGLLfSwx9N+\r\n+Xcjdvl2MCyZ2SYG+D8Xz62nYmbfPLX5VHn3N2NPn4FuZFIVUbTPDWuYSeJO\r\nue6/HO2sTaBnuJCsIbG14VBmCh25QAL67DtQ5P0N5t83D11ZdBJh1gNUDqmF\r\nosqPVDd+y91d4jeeGJ9f4zUFx1q0OHGooAiT1Qbm9At56jwVLXeKs3U7rWkf\r\ns0mdjOW6X+lf3Oj6BzuBS/8ZuT6I/2EAPnku+i7dN6n2Gf5qd+5sC7p+lBdf\r\nS+Mk5OBBy9CAos99uFVyplCSx5lExFzR4GMXYXcsw7w46KeNCTN9HzF5gpyN\r\ncT2q3w6JSA9pFPWDvQl708vFuvty4xo+fxs=\r\n=pTya\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "d7a994e7177980c31d8ec7270622e367f359bfc0", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.3_1661783199638_0.8308064176810444", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "tinybench", "version": "2.1.4", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "07121795c6a15fcbdcf02ab0d2ce329b1b6145a3", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.4.tgz", "fileCount": 6, "integrity": "sha512-NFWIw2Gg7EUPdeE8nL1Dc7AMVlk7sOr2PmSNKVuQrZ0YwTOFoshPQ+hcLrgnhK8dTP3FWMCJaf4N+/hXp6lKPw==", "signatures": [{"sig": "MEQCIFF1BPQdvF1mtSlM9G9NI620hzrQ/VHYxQg4lIfCE2tWAiBjKyxiYH9sFeSl+FN/FVsdjGXHmdgLQP4/UM0ibHWUyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEbJkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo1RAAhDt72OcHYX/UxrNvPecy6BjUddhArBLMRtLL3+olYzlhR1CZ\r\n7Rl4c1dyfU238gXmk+qh/t8UPtHNpes8SEWYaeYwIHTqNghQeQVxsPUv700q\r\nEuw/InFI/r+PKMOg3aY6t+kCuiheK6DFIL7L8YAmMhAAA6IaIc2ijUXVJFLi\r\n5pQkY1hM7WhjyKTDw1q0Ycvps9sBCKMJw1Akmq4ZSFqFHoZaB7x99Y+Rrhpn\r\nTuMm85eKAJYzGQACIVpQSh8BwCH4607FY6hqLjYAwKrr1O9OtCHmFw92osQs\r\nz/mgDT0gms3YyXRzjbYmb3MqTK4/VQKqNOKJorHXLgV9+TODTuRoTwSheCuI\r\n407Ahjbm0GwDiJYlVi6v+SdWa97k5c7+3t9FnrgTBwYdsPjfGI1lcvyJLDi/\r\nAozSKqkb2n9D/jDVd0Bx/A2p6zKCntdwTaDwMPPlKpabvtbKstHdFWPOvZIZ\r\nftAOPcCRMQC00eO5VTi9j75zvWQIcs0pSXzL3kMSgaAldryD3MwLYlhd7SiA\r\nc2RMu6lQ8E3Xeeh7ekfy8q64Kdp97COZ1lVPqeCG69swWHoDSOvzIgACA2Zw\r\nw3I3Um1YsANKT+OCGzipqoiVzQMgGcEHf/Mk60w4Qr0OInHsV5jSEWvHhF/D\r\n7bfm7oVzuqTC1XA1BvHdQWDM5bskqiAIdFA=\r\n=rcs5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "78d73bc7d60fbaaff54a622cf6ec2bdcf03ac59b", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.4_1662104164371_0.37254083671488303", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "tinybench", "version": "2.1.5", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyspy#readme", "bugs": {"url": "https://github.com/tinylibs/tinyspy/issues"}, "dist": {"shasum": "6864341415ff0f912ed160cfd90b7f833ece674c", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.1.5.tgz", "fileCount": 6, "integrity": "sha512-ak+PZZEuH3mw6CCFOgf5S90YH0MARnZNhxjhjguAmoJimEMAJuNip/rJRd6/wyylHItomVpKTzZk9zrhTrQCoQ==", "signatures": [{"sig": "MEYCIQC5rM8suEEbSIbXIv1KPlF+f2Od1MgqemZAIxJ7NlE7pAIhAPRfZrap3cTFS7LgnRqlWAEkoQT6aEtKGfVAar9ZsMaB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFyikACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Ww//R5u4EVd/r0Q/cZ4V3MVbH51oelLcMMOQAGDLFXlnSqiXe+O8\r\nmyA9JDzeaVH7ySfPuvXVBLLpCMpDpRMP5o8Wko2Daz5ncgGPURU0peifUaCk\r\nruWopTZ9WZnBHfv38XvdXvDmZZN069TEwA8iRWtFxQE14igR0bZzmJvb8/m6\r\n8IvlJaoEpt0AXETM0REjSJ2V4L48aLThx8i4YwQrI+oGoUn7PEO+8CDjGTz3\r\nrTbB7QTw3MDIoNJcJosjgTU3MuzusGP73o7mOK2BHiP4D5ZHLvV/6b8CiXR8\r\nS47/xWCretAyvVKvu89SXX5aGDidLKXihdslulbxt/ILAp21SpOTJUlH81DZ\r\n9NOuAVHwjaaxX1StM1swFkOtDikHs5qd2y6RFQQ0zi+w4w+laAjGrelyqsqh\r\nf7EHb9swixJeBCrSWQvCmEBd87M08YWT734xGnslX/0JdPDDgnmGA0R5q7Da\r\nfOmMVopW90ddDqboe7ETY4KYl1uGdslycfJkyAlxuoBuoSJvs45IdvPkn/sK\r\ngnMUf809fAYz7p0/DsQ/GSeS7tB8nn5R3Pg98cuQBLNRFKyC2b9SEkrwQOSk\r\nLTUvNvWqYXEDQ8f+07juCOQFHpjZNBXeF45iS3FLBJkxwyi3vw7DEusNNo5Y\r\n/XrABh5H0+Vr5UmP454gXHlZmQjiq+/nO/A=\r\n=dEfo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "46447e183c1e3409375c2faee6090f7f888b7c14", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyspy.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.1.5_1662462116003_0.3977955826068087", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "tinybench", "version": "2.2.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "94b3ec42e6dfaace740d2462b26bb611b034714b", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.2.0.tgz", "fileCount": 6, "integrity": "sha512-6KpYLnDob01mweJ/hVKhIEaVy3sshAAMHaCjntjMIX7B9W//oOLp4UBo54Hk09MlE5dGDV80W/gtVhHjz0ygKw==", "signatures": [{"sig": "MEUCIGAXQSMXeJXBuPQpOD1v0yo4wVYoLt4P2oMgs4n9IWZeAiEAtgrFQvTYt3/yPc5zccSX1T2/bq+enbtd/L40QgGCdEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKhXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr8xAAmfYXAtVAzBRoy0lTQ1p8HThc/h0IjappyHErRMSvWcSg30RM\r\n2KzKWFsEsCo9KeGY8wAvOI8qCTmEBDhw5dToTT2dTVrU8Gu41tI5Uti05Pa+\r\nNNFlFOplHQrc66SW34kSVM4FSmknkFlnuwrnLqSzgEh6rNW3Hf7na99aG5k2\r\n9CUd9kcwudqVpguVJmARzvhcMwcLNcZBdkL9hf9c9hmAmkTFyRuJmdYtQuGb\r\nyYCqGXcqP+rSN7pcS3opR0rjvr7t7yMIzg3wWBMeU8thWCd+DdK4YOgCp5kq\r\nsRM76o00HjAAVWfZkJCUZ5FRWE1m2sgo8vxJsAby6aOfI+6uFhqwZeKRwfJI\r\nCoc9k3FB/8Cx1sHKk8pueqlDZhk9tLHxBe7CFtpT3jXDi5akzETv4hddtDl5\r\nVheQG+Fo3LJtBOY4hKF9nGaz4ETo53dugMBQNbqyi3b4/Ttt3SYwDhVE8N7a\r\nQUVCuRAQbjNTKRxMgvhQYvNStsyLr3Bj608gJde4BfQtfhH21WpdTFh0DqxB\r\ngxObMpYj5dzzQziCK2of6cS354SPFLBRG0UrQJg2hthllqPnouzB5eHt88hW\r\nkNDziFP9ochjIUsrGVdewLimRcZZZQE1SE2A1u9w8t3RhZIMwK5Fzz7vAOhI\r\nhhksYhuMP90/XOsZm3jobhPBBVG/DDicUrA=\r\n=fGUa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "24062ecef2db1029dee1097cf15070f1df10fe8a", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.2.0_1664395351120_0.37722284081059154", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "tinybench", "version": "2.2.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "592e2da80e0de62cc5c9454335ac7f132b335b6c", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.2.1.tgz", "fileCount": 6, "integrity": "sha512-VxB1P8DUhpCC1j2WtKgFYpv3SwU7vtnfmG29cK7hXcqyD7lLiq6SYCVpDceoAT99mvTN+V8Ay4OdtZQbB72+Sw==", "signatures": [{"sig": "MEQCIHbIQP5Zzvz3yoJ7tuYa3cyZQdfx+8nqUhUi6DFqCcamAiAP6M6QPABAi2PMfzIOmaYiads33QMfWxagmvgXaig9ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNUHIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQIQ/+I6RXdi/yTtNQoLo7tWNGCGR7EtBuxlADu5BakCa/Lj4f8gLz\r\nzSn7JsdqCSEr8dBfYHGaf/Dt/79U9Xc2vSnpuZXvVgfAjTmh1C+03yycugQY\r\neOk65emju8zFw2coNMkm0ksislag3Y4/nOcaFeqGFe5S4TxTpu/O4Dt5bTRi\r\nrz12ooa6WOsOqpFq9Vw7MwIs8gUzVcYlLz/J8L97cJCmCnCv5cQUmz/7hBuM\r\noGf1ZChYwDIXd3ymI5uTcEQh92iL3SGcrqlLOMO3fd+FuC/4LKYiuG+ceDM3\r\nq2wzSBGBRwwfRvvq4LnDyPaH0ZBChfbh1qOur1fp/fjsFKttzOp3DIbNxAd/\r\npd+xSy3H5yqkzGeWuvFME473LoKkXoLSZHqzbOrQGG23vMl8HGJ7zABFQvId\r\nPEY0Z7vVOx8o7wM6+OVeOIeApdiFKHaQDnDMWBmAl53qb7oxsjpPd2OSIQGg\r\nwVJPn5Fa12y+t57wD+9fiUtRDD3/Qoiy8wzaf50M1xTIWIFbWeBki7HgRPdn\r\noXnfDYs3qjsccvJbLz8E9fG4mG0/aNhQ7sSBQaYC6qW3oqVtPuceK5CvGkqA\r\nTppbZnGdrz9/+cZvNSYv1bl+cKqsHzyVGXbtAUqsvpdI7SuERuhPHOF9PH+k\r\n0oYeEZeJzTliWaljDRlPu1ds8Dx6i2n5jtQ=\r\n=s75G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "4c4fefb88be64f36278382b5fec11d543635730f", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.2.1_1664434632201_0.34719591048291987", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "tinybench", "version": "2.3.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "febb2e697c735c0cdb8eb1e43cb1d2fa1821f983", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.3.0.tgz", "fileCount": 6, "integrity": "sha512-zs1gMVBwyyG2QbVchYIbnabRhMOCGvrwZz/q+SV+LIMa9q5YDQZi2kkI6ZRqV2Bz7ba1uvrc7ieUoE4KWnGeKg==", "signatures": [{"sig": "MEQCIH6GPzy1aQR9YkQuJ4BbcUekzOiMci4gtd4HOeLUfBbPAiAUq3EMWHljxAYQsf47sgGcqg51oV8iWtMpF4LkJAZtjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPIJGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrt/w/9Fvlbu2PaUP40MRNlKMPp6AfSCiWisiVkqo5cLWJ3V0TUH8Yq\r\n5gJZ0bcjmJo2RoneknHjKcieF63IBMU5ocXOGSwTVBb3VKBIjGOfaccTgku1\r\nwPlL6gch+YecQpEC2cfyEyRgbIrlezNallTiTcOhfBAs39PhvemMJUABjG9D\r\nK9XK3ze5xyYHLtJI0FQBX3x4/zAEo251WRiJQlJyXGbNy2D3hQzwkl2K8Wr7\r\nqInQgtShIUTScxumJFgLukDZG3hB5OOY/N9xGXatJxDhEttsbZbxEhqxchFu\r\nmFqniXd7vl+q1CUZpCTrDr0ERUybRXpSmJLUdPlVz4yPSsgLkoNS0vVnwJjj\r\niZBZ1ugX/+ODlB/qgfhK2jHwgXIeoW+GDlqDRN9YiusPGzCFx1atnqTVmyvT\r\ng3rjMvaAbdh8drb+cJJ9sutIOKO1L9FEVp/oHexZHz+9KEQHsf/p499hAudy\r\nbhwPqYnipUccPZ9csjEIIFz5KvpOI5RHKgjIf5tniHmqPPC2F0vGL7Klc4U2\r\nvwTuCrYgmjnVIDiJ/XY++Byxiopl35pj+6tIehTgcezBovF+XmzIP8Rr1B07\r\nM1bXOgsVfmgX1/CEOIFUhzit/yNmRigIZ3PmMwhV1j7QSQXW3EaDFXbPTitt\r\nC6KUuhtMupxL8bGGKbVv9FK9YI5gocJgaPk=\r\n=DB6g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "d86aec7fcf339e9bfa56cb008590dd2f511890c4", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.3.0_1664909894458_0.47177899182613103", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "tinybench", "version": "2.3.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "14f64e6b77d7ef0b1f6ab850c7a808c6760b414d", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.3.1.tgz", "fileCount": 6, "integrity": "sha512-hGYWYBMPr7p4g5IarQE7XhlyWveh1EKhy4wUBS1LrHXCKYgvz+4/jCqgmJqZxxldesn05vccrtME2RLLZNW7iA==", "signatures": [{"sig": "MEUCIBhL4/SNjTSKh8QCme+aZxVtExHkc5gqnmUSWlhBnmHmAiEA77kbdjzz2ZPplmSn/4DMluaL4k3dyYN3WFfvS+zh8eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTsNwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDmw/+OXkmmqA9xC/Af66InXYhfAz/Tvwa7YcmL59gRBXu8wPDxnFI\r\nNIVcQ/XXGpymjrtYZsSIrdULnB2bVRdn2ecOyN8w9xxgY6NdvQnTMyGfj+or\r\nTw34Azy7qS1KumZJ9XnCm5CweAryhEsjgjwPfvH5IfSVdRJMovqv2Fl03yCQ\r\n1v9E0Ncp+ZPHzsdKyCdyL2UPtyf5ev0HRl1yhjRAHcGEdS3GpmnuTI7YvDDl\r\n21Q2K/I8uvzLkK3IVTWIlO+zw8J6PKd1UTwKvJmMldxDAMvqhLEmkY2EMqkb\r\n9qamO0xgMaD0taFhTIFkE6CR59mFMO4AUWwYK4xrzl4VbhCVMQXR7YLxGZ76\r\nYm4d/nYEO+IB0q+5p8nSgULoVQbxbNX6LuwyYgOxdcE1EU1YQGNKXS+DDoyI\r\nQK66Bcxb4Cn2esUum9a0cJ4YtRvwc3xjyh7akp5NXzChkFh8DW29t+bxSlYn\r\nQkyldcyzRg4lvItf/cB4GYJ7QC7f6UZpwg4FpSdRKXRWBmBpGBZk6C4Y8KsP\r\ndqnCDeIP6CyAPjzmBWqTLgxeIYq9xe5oeqROEYU+aU6pe8ffFDrOGXP9QJiJ\r\nKcO/Wq/SGCo488ePnY6gSvwVlLnutHIRjjow6dzHWONbQ3uN7fx3S06POr8s\r\nOOZ9+DXqdMAm02wUYtpv7qb7k+icbRHR3sk=\r\n=ZdIw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "89db8314c7f3de43b515f800abb5584d506cf295", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.3.1_1666106224722_0.030288711302477456", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "tinybench", "version": "2.4.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "83f60d9e5545353610fe7993bd783120bc20c7a7", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.4.0.tgz", "fileCount": 6, "integrity": "sha512-iyziEiyFxX4kyxSp+MtY1oCH/lvjH3PxFN8PGCDeqcZWAJ/i+9y+nL85w99PxVzrIvew/GSkSbDYtiGVa85Afg==", "signatures": [{"sig": "MEUCIA/5kwO7sXER8eZmzJjI1fusw9++Ol9NLDoG6Cd3hSVSAiEAvIOknLWj6ZV4yofUizxSAmN5vAYHoskvgu0bWctwYYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAtlqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFWQ//cr205NjN8HC/oGkgc0rcSlpTLdsMs6SFXTIKsXS8GlwShD/U\r\nCFhtoBJK7KZXQTdaq3x5NJuun5++EM34/v6SVrr7qXmXf+Uq3v66bRdbOJw1\r\n/pslGUSUi5Y0s8iJMlCD6P7d73IsWdx4By7wZQ4pwb8MBBnRbvhWbQ7G9u4x\r\nLVr3tiR++v9q0S7mHUi31qP8j8j8azwEOC1HCi4pq9PjdaOj5NvPnBLhZVYA\r\n56aZatwic8VKd52iewbOWAng6jPBPBdh0W6tzcGWgvEN46z7ykAHmyQ71c8f\r\nQ414N5ApTVGS4tYmxDssXuZZNnTs6loHcV3LlHBY2s3BXirCMshGxiEgYiE5\r\nXhLDx2MTt/z9w0yMnGiNJdFdYzeHMITGstinS1b1vXTLFNb2bAZIqvOlOeGx\r\n19SldnEotyE4r76HYfMmsuhwO7C3rb3+15B1cxn6Fd5yNySoSWEnecgifIuS\r\ny/D4rORr4bdHdxL34ik2f2yrKm0LVAT+CmGPUbEOEyX728gFKb2nNpVXgXqE\r\nI46Ou5995KhDryfeZzo3l6jKu5F5hhVsHVCorrxjVnJ37r056e+j7gL7nvX/\r\nyG9KLwpo+k32vJaaANc9evPzG/AgOUQJ8Ki0Dr8jzdpaDCnKGlhdAdq3sr6w\r\nuroCk89kyYfGDViDgl6muEmAzsRsZtLuxUc=\r\n=gBgY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "8c9bca3490795d28df74aee5b9c6c61f35aa8267", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.4.0_1677908330345_0.08713867711406964", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "tinybench", "version": "2.5.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "4711c99bbf6f3e986f67eb722fed9cddb3a68ba5", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.5.0.tgz", "fileCount": 6, "integrity": "sha512-kRwSG8Zx4tjF9ZiyH4bhaebu+EDz1BOx9hOigYHlUW4xxI/wKIUQUqo018UlU4ar6ATPBsaMrdbKZ+tmPdohFA==", "signatures": [{"sig": "MEQCIH0SHy4N/9gPIyVu1bivFpwT30uCVOqfx0Fn5MhMZpDTAiB+WMrKJ8c5VInJ5FcbczfWQ+rIFWXXuuLzE6uMeN88Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTSlZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqEg//Zmoduip07qzcKl3K4E8xSYeHSXP/6dxv0fMzjHKDSVOZdLrh\r\nRL3wpTURhtrVz0HbinDExc1Fws6UjNJq/1iDf/GXkURB9CiG91Iik4Z4eoh4\r\n666huLFbU4zCmQhfmm4qb4v0SVC5s9WhG21R9orT07xe4yP7cizQtu2dI41D\r\nYZEKOes4nuiASdzCm6s7fDtA6608bi1SzH7i0nckJrLL1/I0Y/PRMmEYF67M\r\n+9qkAMtem3/trpNITWEh8vcaK4wNZSuXUYJSdvmOLXNx19AnjJO4++e6Ya3P\r\nbwYJF0z+aQln0fgf88+6tdQ4n8XBMJIicsiUOJlktIootCJ3p7K1UwK8F9O6\r\nOgwwtpAYhofzWiGEIljkyHhWTwnbr7lE1KM1VzrlcX7nAGZo7gPykBWBABJr\r\nd9oq78Qh+C++vtY8+UYVEPXNGiBhArE7aXGoX8pIsTlSITqBJE2wxprclDWb\r\nvyt4TF5HEkPTPS1NcXx4CEoPpVIAcBEP5K910xky+UnHiTNQVfa+T+pZs5vH\r\nYP69Iz1jmGLjdmqU22bZCl+/K419Qh1qYvkeosRE1Ji9JXOKqIY3QDGQX5HJ\r\n4PptLS9gZ98Y4nO5ClqbbHLpAqtCgdnncIlGrcJ8741WZhmarQ73sOEqU1y1\r\nvOTkMSAfapAaSGijoGQeyApSsyLac8plQMg=\r\n=bWTG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-2.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "scripts": {"dev": "tsup --watch", "test": "vitest --no-threads", "build": "tsup", "release": "bumpp package.json --commit --push --tag && npm run publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/5127f0b57a07546ec13699452b3a83e9/tinybench-2.5.0.tgz", "_integrity": "sha512-kRwSG8Zx4tjF9ZiyH4bhaebu+EDz1BOx9hOigYHlUW4xxI/wKIUQUqo018UlU4ar6ATPBsaMrdbKZ+tmPdohFA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "17.9.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.5.1", "devDependencies": {"tsup": "^5.11.7", "vite": "^2.9.12", "bumpp": "^8.2.0", "eslint": "^8.22.0", "vitest": "^0.14.2", "size-limit": "^7.0.8", "typescript": "^4.5.4", "@types/node": "^18.7.13", "nano-staged": "^0.5.0", "clean-publish": "^3.4.4", "changelogithub": "^0.12.4", "@size-limit/time": "^7.0.8", "eslint-plugin-import": "^2.26.0", "@typescript-eslint/parser": "^5.35.1", "eslint-config-airbnb-base": "^15.0.0", "@size-limit/preset-small-lib": "^7.0.4", "@typescript-eslint/eslint-plugin": "^5.35.1"}, "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.5.0_1682778457425_0.69426855096223", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "tinybench", "version": "2.5.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "3408f6552125e53a5a48adee31261686fd71587e", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.5.1.tgz", "fileCount": 6, "integrity": "sha512-65NKvSuAVDP/n4CqH+a9w2kTlLReS9vhsAP06MWx+/89nMinJyB2icyl58RIcqCmIggpojIGeuJGhjU1aGMBSg==", "signatures": [{"sig": "MEQCIADNTfSYfl1+H6JA5M6kKqCaK/IO55APOk5UO3ia+5zWAiB7Ttc6rs6z24CSgj//yECytmG6c7yjCwcXBW6dj7T4aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41638}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "d22a6cc2591d0bfefc99336cd48a358b27851bd4", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Benchmark your code easily with Tinybench, a simple, tiny and light-weight `7KB` (`2KB` minified and gzipped) benchmarking library! You can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timi", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.5.1_1694494051919_0.9419434872677275", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "tinybench", "version": "2.6.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "1423284ee22de07c91b3752c048d2764714b341b", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.6.0.tgz", "fileCount": 6, "integrity": "sha512-N8hW3PG/3aOoZAN5V/NSAEDz0ZixDSSt5b/a05iqtpgfLWMSVuCo7w0k2vVvEjdrIoeGqZzweX2WlyioNIHchA==", "signatures": [{"sig": "MEUCIQDw7P8MEBKZwAf/27Z/yyeVkgqklgpRLz9jL3AkQIW06gIgCH4Roe8Xhgq9WUYxQ+6iKGUaNGLaS0I7n/oJjXwWEHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42628}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "021605e808dea7a301a72a214f3b2a1168a0da35", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.6.0_1705379875131_0.433369508425995", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "tinybench", "version": "2.7.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "d56198a69bead7e240c8f9542484f3eb3c3f749d", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.7.0.tgz", "fileCount": 6, "integrity": "sha512-Qgayeb106x2o4hNzNjsZEfFziw8IbKqtbXBjVh7VIZfBxfD5M4gWtpyx5+YTae2gJ6Y6Dz/KLepiv16RFeQWNA==", "signatures": [{"sig": "MEQCIHT54tJ/vOClcFRECSQVHZKOT3L0Bf6N4zgU0kwewFWlAiBoZnsbQ58uiD1IQzfbxtdHimFO+tmhsnAGt1lTkUpT1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42902}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "b026d42ff26a3af7424b5df02e5c20e5b5f5907b", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.7.0_1713087808998_0.13711978143561887", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "tinybench", "version": "2.8.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "30e19ae3a27508ee18273ffed9ac7018949acd7b", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.8.0.tgz", "fileCount": 6, "integrity": "sha512-1/eK7zUnIklz4JUUlL+658n58XO2hHLQfSk1Zf2LKieUjxidN16eKFEoDEfjHc3ohofSSqK3X5yO6VGb6iW8Lw==", "signatures": [{"sig": "MEUCIFDwKXWJO3brtA7QSwPBZU9fzpiVHoP9Tu8T1j/kzTfMAiEA+lMMpzaFnHz+lBKpYhnEGn178GrEBbKdMMbs9BARE/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50984}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs", "require": "./dist/index.cjs"}, "gitHead": "4f77fc7e00b06c319a60042f2c035acc93d99bc7", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "_I'm transitioning to a full-time open source career. Your support would be greatly appreciated 🙌_ <a href=\"https://polar.sh/tinylibs/subscriptions\"><picture><source media=\"(prefers-color-scheme: dark)\" srcset=\"https://polar.sh/embed/tiers.svg?org=tinyli", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.8.0_1713688319509_0.21610546484526272", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "tinybench", "version": "2.9.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@2.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/aslemammad", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "103c9f8ba6d7237a47ab6dd1dcff77251863426b", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz", "fileCount": 7, "integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==", "signatures": [{"sig": "MEYCIQDemnYkAXp+LjAO2v/oznCoj7BwGjJ0orjFqK1F37eAOwIhANQBOzAw5bo+rD0Y2VlKxSBT9q+80BjlOg92YXoWJqm4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62945}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.js", "exports": {"import": "./dist/index.js", "default": "./dist/index.js", "require": "./dist/index.cjs"}, "gitHead": "40b95e7bfd7aecda8d19900c748d2f0da01b66bc", "scripts": {"publish": "npm run build && clean-publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "_I'm transitioning to a full-time open source career. Your support would be greatly appreciated 🙌_ <a href=\"https://polar.sh/tinylibs/subscriptions\"><picture><source media=\"(prefers-color-scheme: dark)\" srcset=\"https://polar.sh/embed/tiers.svg?org=tinyli", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.4.0", "_npmOperationalInternal": {"tmp": "tmp/tinybench_2.9.0_1722611384803_0.4309190055221628", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.4": {"name": "tinybench", "version": "3.0.0-rc.4", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0-rc.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "e1b1e2cf2bb4c05fc7fbd560cb8b2d44b7524347", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0-rc.4.tgz", "fileCount": 3, "integrity": "sha512-aE5ziuD3hZQ+U4coj3lUejOGUkbD70Tn9OM7Gnqvw0kmZSFd2ug/hHgp41W+MEIxEm4Me6BEoIZQ+pnW5e5/bg==", "signatures": [{"sig": "MEYCIQC5vuUWvHJHSih9iG5YX1WnMbDkof/wXUDyzMOe+GCADAIhAPdsrt8p4Nfsb9lbBDtGNYXTd0WRzeYF7zhpeMPn1/hD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15687}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0-rc.4.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {"postinstall": "pnpm exec simple-git-hooks"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/03c1e152fd98fbbc1fae38101ff63d28/tinybench-3.0.0-rc.4.tgz", "_integrity": "sha512-aE5ziuD3hZQ+U4coj3lUejOGUkbD70Tn9OM7Gnqvw0kmZSFd2ug/hHgp41W+MEIxEm4Me6BEoIZQ+pnW5e5/bg==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0-rc.4_1729941992791_0.6566891197832105", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.5": {"name": "tinybench", "version": "3.0.0-rc.5", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0-rc.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "9703ab82a67ebc2ceb3a04a142faa022ed0e3f20", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0-rc.5.tgz", "fileCount": 3, "integrity": "sha512-E3+qlnyV6lw0rcOm5mCvLEvnYPh5of6errjMca8NNkJnRDAxOPObY2CAPe2ii2k27+uf9HhNGxu/xlhGtJxIuA==", "signatures": [{"sig": "MEQCIDlfnyDyQaWRIPruoWZfQhN/7x0JiparrWmPRfhoF/68AiAIfI9AL97YHavYEPTj+DkAxF63lwAlwhRWjhicvUf8sA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15687}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0-rc.5.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {"postinstall": "pnpm exec simple-git-hooks"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/833e2dafc4ae297043d21cc88ed8a2ae/tinybench-3.0.0-rc.5.tgz", "_integrity": "sha512-E3+qlnyV6lw0rcOm5mCvLEvnYPh5of6errjMca8NNkJnRDAxOPObY2CAPe2ii2k27+uf9HhNGxu/xlhGtJxIuA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0-rc.5_1729942312399_0.2020401336212614", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.6": {"name": "tinybench", "version": "3.0.0-rc.6", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0-rc.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "4b3b4de41f44907b5fe54e360cb95b1b59889e9c", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0-rc.6.tgz", "fileCount": 7, "integrity": "sha512-pXIo3izapvBliXsRAilbtmLQzQPTWRsxE621QrIvkgMxFtv1+z/lhuA2e7+f26Eti6C3A3XGxjz09e+esuayHQ==", "signatures": [{"sig": "MEUCIQC61QVkUxk/pRYQdODoIlShZd2K67DUvfDN6SqxR3bXBgIgLYoOocVJ/oh3vmuLWUKneRQi4bN103SzcoGC1NtI/Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122157}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0-rc.6.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {"postinstall": "pnpm exec simple-git-hooks"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/4de3f8e83ea01ea2283c49d77a2d216f/tinybench-3.0.0-rc.6.tgz", "_integrity": "sha512-pXIo3izapvBliXsRAilbtmLQzQPTWRsxE621QrIvkgMxFtv1+z/lhuA2e7+f26Eti6C3A3XGxjz09e+esuayHQ==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0-rc.6_1729943239435_0.8585946704849421", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.7": {"name": "tinybench", "version": "3.0.0-rc.7", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0-rc.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "076e8ece086f52ee681cfd0b5a922767b8e8be78", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0-rc.7.tgz", "fileCount": 7, "integrity": "sha512-kB7BxWV2A4UxSYdztKWLHRWg1lkyTScWmTQfrfBVUXHWNrPHO2/Lg93K4LGF4rIXJtLhx89VQB4/f0DMIzk4yw==", "signatures": [{"sig": "MEQCIEBMSwGP4mcfpho+kvpPNOWs5pGOwcMCEUj6+xyMpkXkAiAxBl7//HcLHdPnKINya952DJ0ihTpSNp9g+ajm/JvoWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122157}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0-rc.7.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {"postinstall": "pnpm exec simple-git-hooks"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/347c5c604fa298355eac59d8a85079bd/tinybench-3.0.0-rc.7.tgz", "_integrity": "sha512-kB7BxWV2A4UxSYdztKWLHRWg1lkyTScWmTQfrfBVUXHWNrPHO2/Lg93K4LGF4rIXJtLhx89VQB4/f0DMIzk4yw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0-rc.7_1729944028827_0.513104601049724", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.8": {"name": "tinybench", "version": "3.0.0-rc.8", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0-rc.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "47c3fe118a30e7df1dd643419e7c4f6c3cfa5f41", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0-rc.8.tgz", "fileCount": 7, "integrity": "sha512-oHkqUWev1+3U2m6k+wtv4A9dQtHhAyrXRPEJzG3fcOyCbqssNWjb269o9Np66EtyDv8lQmEnSJTuiBh3CHiYDQ==", "signatures": [{"sig": "MEQCIH2qSDGgnCIhjz31k0E/gLsCaQrDdL6Ryq++ba9zMghyAiBxxUkZxTT+9z2Fufr8CtZ9cHUR8qg83U0xBp/BfoN8aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122106}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0-rc.8.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/f18d7972cc04991ccec99d529a209ff5/tinybench-3.0.0-rc.8.tgz", "_integrity": "sha512-oHkqUWev1+3U2m6k+wtv4A9dQtHhAyrXRPEJzG3fcOyCbqssNWjb269o9Np66EtyDv8lQmEnSJTuiBh3CHiYDQ==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0-rc.8_1729945050183_0.035974261722212075", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tinybench", "version": "3.0.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "138bbae089d2c851ed8a600b146be84bd7c1fb75", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-931sGm66Zjp7c4o/DePaq8AKlCdq/ZldpS1b8O7r3SxSuxJpqoqeUprTOsW2CBhrw54U3mTmcS97LsBqPXEQLw==", "signatures": [{"sig": "MEUCIEVisYZUry/7AcC2dLWratbavHNXYD6+EYE5QsBHfVuDAiEA1n0eMPrzCMY42hZZ6edsbEnWlYgrQj2ATs7tNUGiJc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.0.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.2"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/c6316f04ba6ec568bba61a9d53a0e3cc/tinybench-3.0.0.tgz", "_integrity": "sha512-931sGm66Zjp7c4o/DePaq8AKlCdq/ZldpS1b8O7r3SxSuxJpqoqeUprTOsW2CBhrw54U3mTmcS97LsBqPXEQLw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.0_1730049092519_0.736443900188585", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "tinybench", "version": "3.0.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "dcd5e02c5463fe2637b609ed892cbd41dd80d975", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-m1wm9bW6d3pSnzlWeKSBHnn8CwLf95YmZUA8WKJtokmWVZCzjo16S+ibBCGJauGGPhKr8pyITePJ2C5WIwthiA==", "signatures": [{"sig": "MEUCIQDso67lpaUWvKvbIVHpUcQ3DYFvGUYvrodpb6FZ2oETFgIgca6iNtrjmGPQhkJy6ezMqjnZNFl5uNn3m7KMa58MJMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123219}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.1.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/f74e23e477f44d7d16515ede80922320/tinybench-3.0.1.tgz", "_integrity": "sha512-m1wm9bW6d3pSnzlWeKSBHnn8CwLf95YmZUA8WKJtokmWVZCzjo16S+ibBCGJauGGPhKr8pyITePJ2C5WIwthiA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.1_1730237989139_0.40079826532489204", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "tinybench", "version": "3.0.2", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "c83a31bec20cc3021e8b0f1c4e670b1db8ec3b7f", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-4aVaW+e+RMFPYJt5ZiawIYrH8VfpeEufMS0P/WYgSlaJzrVuGY6/7YJ+a0l5oZAGMluPb0WtNaemn4C1UE53pA==", "signatures": [{"sig": "MEQCIFLCnnslmQ7QC+8krmKiuxbWcPnas8mcIzBdy17FTB1FAiBu6Fw7thawpcTrN6aigtyRXptHJKE0BiujSLLTjmITHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123219}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.2.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/a8e6a4571b67cd55db7aa9e722d1d196/tinybench-3.0.2.tgz", "_integrity": "sha512-4aVaW+e+RMFPYJt5ZiawIYrH8VfpeEufMS0P/WYgSlaJzrVuGY6/7YJ+a0l5oZAGMluPb0WtNaemn4C1UE53pA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.2_1730238251253_0.16825646768482683", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "tinybench", "version": "3.0.3", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "8df99603a1482fa811e2bec902257ff13f65c077", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-uJx7Wn5Dp5qd2TBbbixMaFSxod6HvJxhA7rb55BJD27Gcsz+zoHRA/Gk8pBl91GiWbKtoGqU02XS8GOVJhe1KA==", "signatures": [{"sig": "MEUCIG7JNhTGf1CrvGXoGTGLWaDN0N+bRbvyBK3UeHW5uYglAiEAqsgM5csaqC2zRH/0HB45FkC1Mu9DqryDtn3171ku7R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123467}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.3.tgz", "types": "./dist/index.d.ts", "volta": {"node": "20.18.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/15eb4dcb8deba4583fef2f987a7e91b3/tinybench-3.0.3.tgz", "_integrity": "sha512-uJx7Wn5Dp5qd2TBbbixMaFSxod6HvJxhA7rb55BJD27Gcsz+zoHRA/Gk8pBl91GiWbKtoGqU02XS8GOVJhe1KA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.3_1730240148409_0.30116035406606545", "host": "s3://npm-registry-packages"}}, "3.0.4-rc.1": {"name": "tinybench", "version": "3.0.4-rc.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.4-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "db29cf889314f2c6b3abbddd2743896761410d0a", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.4-rc.1.tgz", "fileCount": 7, "integrity": "sha512-Nt9Cy5aeDg4JF6VgrNW+oCkZPKPTNR9wL9WQPpLBNSQUG60boRkKHrGvBsqQEbHH5urqYfWZpfLGtRIp35xoSw==", "signatures": [{"sig": "MEYCIQD/yDBUa2+YBUfKNxJ5pHHSWix6NiEZgiZvnTgrlGhx0wIhAJurl4yM8pXlfkQgwylAhGBFH17wTYw7McmpZ09Mq0vi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128322}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.4-rc.1.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/ae2b134d8fbd5e028cc4890569f06b41/tinybench-3.0.4-rc.1.tgz", "_integrity": "sha512-Nt9Cy5aeDg4JF6VgrNW+oCkZPKPTNR9wL9WQPpLBNSQUG60boRkKHrGvBsqQEbHH5urqYfWZpfLGtRIp35xoSw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.4-rc.1_1731358960096_0.15878929471417158", "host": "s3://npm-registry-packages"}}, "3.0.4-rc.2": {"name": "tinybench", "version": "3.0.4-rc.2", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.4-rc.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "5926f76f08afd8b0c966cbae0c20f938a9da6fa8", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.4-rc.2.tgz", "fileCount": 7, "integrity": "sha512-Cd3yeX2Dhj3DN4WgaeU/oiskSjwmQ2nYX0PVjlZ+4IQyISXBs74gF+FAtEGS7pmv1VyxCv/vwLMPbQ4T1SF5Rw==", "signatures": [{"sig": "MEYCIQDgPHoPCVqcscIgeBaKVOYN+a2o4bO2L6odKdw2wgBMqQIhAMM2ohMZwOb+4CNevxMVmSWgBETT++iv9WWa8eTBHUaL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128322}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.4-rc.2.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/7a46fd50ebbfd55e37437910f25f1fb7/tinybench-3.0.4-rc.2.tgz", "_integrity": "sha512-Cd3yeX2Dhj3DN4WgaeU/oiskSjwmQ2nYX0PVjlZ+4IQyISXBs74gF+FAtEGS7pmv1VyxCv/vwLMPbQ4T1SF5Rw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.4-rc.2_1731359544185_0.49208733335863997", "host": "s3://npm-registry-packages"}}, "3.0.4-rc.3": {"name": "tinybench", "version": "3.0.4-rc.3", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.4-rc.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "e8ea050340a496e3e298f5997816c8c047e0a22e", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.4-rc.3.tgz", "fileCount": 7, "integrity": "sha512-fZoaLoPL62vRQYA0zzkpZpWNwIi+IcN/MnOb43pvAmjQGMgSFMJbp4Mq7mMB38cjBA2NclSFX6y8yi0yT3tmag==", "signatures": [{"sig": "MEUCICD/MFyjXvrsigJWzo9/K1uiJQeVsxtx8czfZekalo4fAiEAoM1rjRBnyAho2IARk7kQh2RANB3+OWeNv+krCHe+baI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128322}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.4-rc.3.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/2bf3bb7368608344a4998067584bcbcc/tinybench-3.0.4-rc.3.tgz", "_integrity": "sha512-fZoaLoPL62vRQYA0zzkpZpWNwIi+IcN/MnOb43pvAmjQGMgSFMJbp4Mq7mMB38cjBA2NclSFX6y8yi0yT3tmag==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.4-rc.3_1731360508546_0.5499885992806859", "host": "s3://npm-registry-packages"}}, "3.0.4-rc.4": {"name": "tinybench", "version": "3.0.4-rc.4", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.4-rc.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "693733054c5d686dd007e87ed3103726f0883e95", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.4-rc.4.tgz", "fileCount": 7, "integrity": "sha512-f3zR3xj+SVZVC3QIRfevHTLu15kf2EKyYlwvO45XALqAoJ5sI0vAgF2wOXWC7kJ9v4bdUodgcXDETY3qRsbvfA==", "signatures": [{"sig": "MEQCID7QhzDYSpV77BmVVcpx4+Cg11Eo53jmKUNNnxaMtu8AAiBJmlOeZxWKQ2PgtoaY6HMhP0V6ZSn/lZ/0DO8WSnXdGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128322}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.4-rc.4.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/689ebc2edcb3437fa5f111b0365ee970/tinybench-3.0.4-rc.4.tgz", "_integrity": "sha512-f3zR3xj+SVZVC3QIRfevHTLu15kf2EKyYlwvO45XALqAoJ5sI0vAgF2wOXWC7kJ9v4bdUodgcXDETY3qRsbvfA==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.4-rc.4_1731361751340_0.024934968527230428", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "tinybench", "version": "3.0.4", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "0fa23e1295bf0ace19d18ddc58da2222e178d0bd", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.4.tgz", "fileCount": 7, "integrity": "sha512-JMCuHaSJh6i1/8RMgZiRhA2KY/SiwnCxxGmoRz7onx69vDlh9YkbBFoi37WOssH+EccktzXYacTUtmIfdSqFTw==", "signatures": [{"sig": "MEYCIQCshpSLMNKHgu+vliyhPUOTlEkJPr8FNibZ171U3fQ05gIhAM+Vl0NrB0cL9CNon2c+SZ2qj3ZCzhg1HXr0JtW6qFB8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128317}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.4.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.12.3"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/fbfd50cf6201d9c152d2500ad67246da/tinybench-3.0.4.tgz", "_integrity": "sha512-JMCuHaSJh6i1/8RMgZiRhA2KY/SiwnCxxGmoRz7onx69vDlh9YkbBFoi37WOssH+EccktzXYacTUtmIfdSqFTw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.4_1731362028512_0.2369230371395723", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "tinybench", "version": "3.0.5", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "9becaa748ef2e7ff8e8aae46cd91130d5b038330", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.5.tgz", "fileCount": 7, "integrity": "sha512-8aGXFbu0G1uLqSnDNsTZlQSl6iMGPu36pchVFuTbAxnjL8ux+pK7R9Qvc6pYZOMmRw1BAbggjBhNm0cMH+fxLQ==", "signatures": [{"sig": "MEQCIFQk63hxGpuOH2TrvYJ1+GddFq8Uy86zCPt18nrw8kjCAiBInCzoDbN/7/W9GVZTcMpbUOEvAvDwlZMBXYH8F1zkRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128821}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.5.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.13.0"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/cdfc99d057738387db20770498f0957e/tinybench-3.0.5.tgz", "_integrity": "sha512-8aGXFbu0G1uLqSnDNsTZlQSl6iMGPu36pchVFuTbAxnjL8ux+pK7R9Qvc6pYZOMmRw1BAbggjBhNm0cMH+fxLQ==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.5_1731517601363_0.7861891304494741", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "tinybench", "version": "3.0.6", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "643b79171d83e8ca7775ff9b9bf2392913b04926", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.6.tgz", "fileCount": 7, "integrity": "sha512-ljQ0LM7ePiVrjM8KHkHUWH+eVo36hwpE34dqYvOJIvzVJvzqXwTpjjw/bLjduqU50Z8CuhVFgFN1U7yLaSCsCg==", "signatures": [{"sig": "MEQCIBJ16j/ILGeoBbtmv/tcIMnN36Z6Uj16cXF2wPuPmHeEAiBsH/Ja37YRkExp58KG+hV4K1+s8cukwdl2WZjejRc1CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128441}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.6.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.13.0"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/0c7fed9242a81a97f7a8f4427330e7cd/tinybench-3.0.6.tgz", "_integrity": "sha512-ljQ0LM7ePiVrjM8KHkHUWH+eVo36hwpE34dqYvOJIvzVJvzqXwTpjjw/bLjduqU50Z8CuhVFgFN1U7yLaSCsCg==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "20.18.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.6_1731610327853_0.6097409801962095", "host": "s3://npm-registry-packages"}}, "3.0.7": {"name": "tinybench", "version": "3.0.7", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "71500258fa98ddcaf75063330b6c570d39b2ad6d", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.0.7.tgz", "fileCount": 7, "integrity": "sha512-soxV7Dp8eDKvPDv3c4qPJbUjLm1cZxFlsTaIH+FqalsazJzFrLG59dpiIN8OfgVcl11Hfj2b7apD73inCB67Mw==", "signatures": [{"sig": "MEQCIDhec0r3fzeSPWjRCX/e6j8YvNrSbtbiq5yumHR1/A0MAiA+4QRwuNUIgD3Y1+OFJalWQRcd+jJaRV+wYSXAF7xfZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128552}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.0.7.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.11.0", "pnpm": "9.14.4"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/5cdb8f5e47ff8efc3bbbd33d5fc625f5/tinybench-3.0.7.tgz", "_integrity": "sha512-soxV7Dp8eDKvPDv3c4qPJbUjLm1cZxFlsTaIH+FqalsazJzFrLG59dpiIN8OfgVcl11Hfj2b7apD73inCB67Mw==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/test.yml) [![NPM version](https://img.shields.io/npm/v/tinybench.svg?style=flat)](https://www.npmjs.com", "directories": {}, "_nodeVersion": "22.11.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.0.7_1733140475631_0.5358803926477769", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "tinybench", "version": "3.1.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "ec68451ff05233cf3de12c46f39f06011897109a", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-Km+oMh2xqNCxuyoUsqbRmHgFSd8sATh7v7xreP+kHN6x67w28Pawr83WmBxcaORvxkc0Ex6zgqK951yBnTFaaQ==", "signatures": [{"sig": "MEUCICD14SkGkVUIXObRNnnRkQSbWAny2nLxFkoRM316z8SRAiEAtejCD2hUKfikN0DplkowLsiEZOWQ6YfMPe51W8MYgwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137931}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.1.0.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.12.0", "pnpm": "9.15.1"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/9ce6c24fb3197b1be8a8c7f4478a6b72/tinybench-3.1.0.tgz", "_integrity": "sha512-Km+oMh2xqNCxuyoUsqbRmHgFSd8sATh7v7xreP+kHN6x67w28Pawr83WmBxcaORvxkc0Ex6zgqK951yBnTFaaQ==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml) [![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinyb", "directories": {}, "_nodeVersion": "22.12.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.1.0_1734950984280_0.9409223836137799", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "tinybench", "version": "3.1.1", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@3.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "0dcba68676d7304dedbefa1eb3b16ea16b00a823", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-74pmf47HY/bHqamcCMGris+1AtGGsqTZ3Hc/UK4QvSmRuf/9PIF9753+c8XBh7JfX2r9KeZtVjOYjd6vFpc0qQ==", "signatures": [{"sig": "MEUCIDfjD8ZM0ZMANzi3IcajJawd0bel/NBS8+wJS8LorZ9wAiEA5tpSaboHxSeJ04PT3kJNvhkCaG/nHjFieVcNarvhYrQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138519}, "main": "./dist/index.cjs", "type": "module", "_from": "file:tinybench-3.1.1.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.13.1", "pnpm": "9.15.4"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/ee2f83de6b0f7be301578542f722e052/tinybench-3.1.1.tgz", "_integrity": "sha512-74pmf47HY/bHqamcCMGris+1AtGGsqTZ3Hc/UK4QvSmRuf/9PIF9753+c8XBh7JfX2r9KeZtVjOYjd6vFpc0qQ==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml) [![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinyb", "directories": {}, "_nodeVersion": "22.13.1", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_3.1.1_1738177045773_0.11414486428526338", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0": {"name": "tinybench", "version": "4.0.0", "keywords": ["benchmark", "tinylibs", "tiny"], "license": "MIT", "_id": "tinybench@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinybench#readme", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "dist": {"shasum": "75a1752db7e6157444ddfd9b270e664b7decc593", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-Dkt4VXM/fhVSIN/y+35j0QKAGmLmebLA2XeQw0vVzWwH+EmqIdHCtzk5MINbJeywb9ZCGn29JiC46Bi1CllZ3g==", "signatures": [{"sig": "MEQCIGmNBGNzAt/PxVhfyeB1TlY8Vv4s4KMAffXhm6NC1+1OAiAbtSPoBucPZJNGj0aPuwM19c5O7CdpiJ3TQzFZjwn+9g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 73833}, "main": "./dist/index.js", "type": "module", "_from": "file:tinybench-4.0.0.tgz", "types": "./dist/index.d.ts", "volta": {"node": "22.14.0", "pnpm": "10.6.4"}, "module": "./dist/index.js", "engines": {"node": ">=18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/be732905e75a543a63dff99348570177/tinybench-4.0.0.tgz", "_integrity": "sha512-Dkt4VXM/fhVSIN/y+35j0QKAGmLmebLA2XeQw0vVzWwH+EmqIdHCtzk5MINbJeywb9ZCGn29JiC46Bi1CllZ3g==", "repository": {"url": "git+https://github.com/tinylibs/tinybench.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml) [![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinyb", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"directory": "package"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tinybench_4.0.0_1742295222330_0.9775356088361948", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.1": {"name": "tinybench", "version": "4.0.1", "type": "module", "volta": {"node": "22.14.0", "pnpm": "10.6.4"}, "engines": {"node": ">=18.0.0"}, "publishConfig": {"directory": "package"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinybench.git"}, "license": "MIT", "keywords": ["benchmark", "tinylibs", "tiny"], "scripts": {}, "_id": "tinybench@4.0.1", "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml) [![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinyb", "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "homepage": "https://github.com/tinylibs/tinybench#readme", "_integrity": "sha512-Nb1srn7dvzkVx0J5h1vq8f48e3TIcbrS7e/UfAI/cDSef/n8yLh4zsAEsFkfpw6auTY+ZaspEvam/xs8nMnotQ==", "_resolved": "/tmp/4ea5ec6d449c35f3eac575a383bd9428/tinybench-4.0.1.tgz", "_from": "file:tinybench-4.0.1.tgz", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Nb1srn7dvzkVx0J5h1vq8f48e3TIcbrS7e/UfAI/cDSef/n8yLh4zsAEsFkfpw6auTY+ZaspEvam/xs8nMnotQ==", "shasum": "ff5940b4e4a63892ef0cad3daf148d5fd8a3725b", "tarball": "https://registry.npmjs.org/tinybench/-/tinybench-4.0.1.tgz", "fileCount": 5, "unpackedSize": 73846, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCeUGnytvEmdTjwQpo1CwbLxy/LizS4eAEX/poCST9D4QIgF+kN9LruDuKsmgbSAmM6xt2Nq8xvLi+mCnUb6VSrTzo="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinybench_4.0.1_1742296092715_0.46818686713346525"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-04-29T11:34:05.073Z", "modified": "2025-03-18T11:08:13.049Z", "1.0.0": "2022-04-29T11:34:05.220Z", "1.0.1": "2022-05-15T15:16:40.053Z", "1.0.2": "2022-05-17T12:05:39.664Z", "2.0.1": "2022-08-24T11:19:40.825Z", "2.0.4": "2022-08-24T11:25:23.881Z", "2.0.5": "2022-08-24T11:35:19.754Z", "2.0.6": "2022-08-24T11:42:51.111Z", "2.0.7": "2022-08-24T12:11:32.837Z", "2.0.8": "2022-08-24T13:36:05.174Z", "2.0.9": "2022-08-25T02:47:19.290Z", "2.1.0": "2022-08-26T09:25:18.246Z", "2.1.1": "2022-08-27T05:53:11.730Z", "2.1.2": "2022-08-29T12:45:37.090Z", "2.1.3": "2022-08-29T14:26:39.817Z", "2.1.4": "2022-09-02T07:36:04.556Z", "2.1.5": "2022-09-06T11:01:56.164Z", "2.2.0": "2022-09-28T20:02:31.277Z", "2.2.1": "2022-09-29T06:57:12.386Z", "2.3.0": "2022-10-04T18:58:14.676Z", "2.3.1": "2022-10-18T15:17:04.899Z", "2.4.0": "2023-03-04T05:38:50.515Z", "2.5.0": "2023-04-29T14:27:37.603Z", "2.5.1": "2023-09-12T04:47:32.088Z", "2.6.0": "2024-01-16T04:37:55.296Z", "2.7.0": "2024-04-14T09:43:29.227Z", "2.8.0": "2024-04-21T08:31:59.668Z", "2.9.0": "2024-08-02T15:09:44.961Z", "3.0.0-rc.4": "2024-10-26T11:26:32.958Z", "3.0.0-rc.5": "2024-10-26T11:31:52.575Z", "3.0.0-rc.6": "2024-10-26T11:47:19.636Z", "3.0.0-rc.7": "2024-10-26T12:00:28.999Z", "3.0.0-rc.8": "2024-10-26T12:17:30.432Z", "3.0.0": "2024-10-27T17:11:32.741Z", "3.0.1": "2024-10-29T21:39:49.348Z", "3.0.2": "2024-10-29T21:44:11.494Z", "3.0.3": "2024-10-29T22:15:48.613Z", "3.0.4-rc.1": "2024-11-11T21:02:40.309Z", "3.0.4-rc.2": "2024-11-11T21:12:24.522Z", "3.0.4-rc.3": "2024-11-11T21:28:28.737Z", "3.0.4-rc.4": "2024-11-11T21:49:11.653Z", "3.0.4": "2024-11-11T21:53:48.778Z", "3.0.5": "2024-11-13T17:06:41.590Z", "3.0.6": "2024-11-14T18:52:08.062Z", "3.0.7": "2024-12-02T11:54:35.773Z", "3.1.0": "2024-12-23T10:49:44.504Z", "3.1.1": "2025-01-29T18:57:25.965Z", "4.0.0": "2025-03-18T10:53:42.626Z", "4.0.1": "2025-03-18T11:08:12.888Z"}, "bugs": {"url": "https://github.com/tinylibs/tinybench/issues"}, "license": "MIT", "homepage": "https://github.com/tinylibs/tinybench#readme", "keywords": ["benchmark", "tinylibs", "tiny"], "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinybench.git"}, "description": "[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml) [![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinyb", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "uzlopak", "email": "<EMAIL>"}, {"name": "fraggle", "email": "<EMAIL>"}], "readme": "# Tinybench 🔎\n\n[![CI](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml/badge.svg?branch=main)](https://github.com/tinylibs/tinybench/actions/workflows/qa.yml)\n[![NPM version](https://badgen.net/npm/v/tinybench?icon=npm)](https://www.npmjs.com/package/tinybench)\n[![Discord](https://badgen.net/discord/online-members/c3UUYNcHrU?icon=discord&label=discord&color=green)](https://discord.gg/c3UUYNcHrU)\n[![neostandard Javascript Code Style](<https://badgen.net/static/code style/neostandard/green>)](https://github.com/neostandard/neostandard)\n\nBenchmark your code easily with Tinybench, a simple, tiny and light-weight `10KB` (`2KB` minified and gzipped) benchmarking library!\nYou can run your benchmarks in multiple JavaScript runtimes, Tinybench is completely based on the Web APIs with proper timing using\n`process.hrtime` or `performance.now`.\n\n- Accurate and precise timing based on the environment\n- Statistically analyzed latency and throughput values: standard deviation, margin of error, variance, percentiles, etc.\n- Concurrency support\n- `Event` and `EventTarget` compatible events\n- No dependencies\n\n_In case you need more tiny libraries like tinypool or tinyspy, please consider submitting an [RFC](https://github.com/tinylibs/rfcs)_\n\n## Installing\n\n```shell\n$ npm install -D tinybench\n```\n\n## Usage\n\nYou can start benchmarking by instantiating the `Bench` class and adding benchmark tasks to it.\n\n```js\nimport { Bench } from 'tinybench'\n\nconst bench = new Bench({ name: 'simple benchmark', time: 100 })\n\nbench\n  .add('faster task', () => {\n    console.log('I am faster')\n  })\n  .add('slower task', async () => {\n    await new Promise(resolve => setTimeout(resolve, 1)) // we wait 1ms :)\n    console.log('I am slower')\n  })\n\nawait bench.run()\n\nconsole.log(bench.name)\nconsole.table(bench.table())\n\n// Output:\n// simple benchmark\n// ┌─────────┬───────────────┬───────────────────┬───────────────────────┬────────────────────────┬────────────────────────┬─────────┐\n// │ (index) │ Task name     │ Latency avg (ns)  │ Latency med (ns)      │ Throughput avg (ops/s) │ Throughput med (ops/s) │ Samples │\n// ├─────────┼───────────────┼───────────────────┼───────────────────────┼────────────────────────┼────────────────────────┼─────────┤\n// │ 0       │ 'faster task' │ '63768 ± 4.02%'   │ '58954 ± 15255.00'    │ '18562 ± 1.67%'        │ '16962 ± 4849'         │ 1569    │\n// │ 1       │ 'slower task' │ '1542543 ± 7.14%' │ '1652502 ± 167851.00' │ '808 ± 19.65%'         │ '605 ± 67'             │ 65      │\n// └─────────┴───────────────┴───────────────────┴───────────────────────┴────────────────────────┴────────────────────────┴─────────┘\n```\n\nThe `add` method accepts a task name and a task function, so it can benchmark\nit! This method returns a reference to the Bench instance, so it's possible to\nuse it to create an another task for that instance.\n\nNote that the task name should always be unique in an instance, because Tinybench stores the tasks based\non their names in a `Map`.\n\nAlso note that `tinybench` does not log any result by default. You can extract the relevant stats\nfrom `bench.tasks` or any other API after running the benchmark, and process them however you want.\n\nMore usage examples can be found in the [examples](./examples/) directory.\n\n## Docs\n\n### [`Bench`](https://tinylibs.github.io/tinybench/classes/Bench.html)\n\n### [`Task`](https://tinylibs.github.io/tinybench/classes/Task.html)\n\n### [`TaskResult`](https://tinylibs.github.io/tinybench/interfaces/TaskResult.html)\n\n### `Events`\n\nBoth the `Task` and `Bench` classes extend the `EventTarget` object. So you can attach listeners to different types of events in each class instance using the universal `addEventListener` and `removeEventListener` methods.\n\n#### [`BenchEvents`](https://tinylibs.github.io/tinybench/types/BenchEvents.html)\n\n```js\n// runs on each benchmark task's cycle\nbench.addEventListener('cycle', (evt) => {\n  const task = evt.task!;\n});\n```\n\n#### [`TaskEvents`](https://tinylibs.github.io/tinybench/types/TaskEvents.html)\n\n```js\n// runs only on this benchmark task's cycle\ntask.addEventListener('cycle', (evt) => {\n  const task = evt.task!;\n});\n```\n\n### [`BenchEvent`](https://tinylibs.github.io/tinybench/types/BenchEvent.html)\n\n## `process.hrtime`\n\nif you want more accurate results for nodejs with `process.hrtime`, then import\nthe `hrtimeNow` function from the library and pass it to the `Bench` options.\n\n```ts\nimport { hrtimeNow } from 'tinybench'\n```\n\nIt may make your benchmarks slower.\n\n## Concurrency\n\n- When `mode` is set to `null` (default), concurrency is disabled.\n- When `mode` is set to 'task', each task's iterations (calls of a task function) run concurrently.\n- When `mode` is set to 'bench', different tasks within the bench run concurrently. Concurrent cycles.\n\n```ts\nbench.threshold = 10 // The maximum number of concurrent tasks to run. Defaults to Number.POSITIVE_INFINITY.\nbench.concurrency = 'task' // The concurrency mode to determine how tasks are run.\nawait bench.run()\n```\n\n## Prior art\n\n- [Benchmark.js](https://github.com/bestiejs/benchmark.js)\n- [mitata](https://github.com/evanwashere/mitata/)\n- [tatami-ng](https://github.com/poolifier/tatami-ng)\n- [Bema](https://github.com/prisma-labs/bema)\n\n## Authors\n\n| <a href=\"https://github.com/Aslemammad\"> <img width='150' src=\"https://avatars.githubusercontent.com/u/37929992?v=4\" /><br> Mohammad Bagher </a> |\n| ------------------------------------------------------------------------------------------------------------------------------------------------ |\n\n## Credits\n\n| <a href=\"https://github.com/uzlopak\"> <img width='150' src=\"https://avatars.githubusercontent.com/u/5059100?v=4\" /><br> Uzlopak </a> | <a href=\"https://github.com/poyoho\"> <img width='150' src=\"https://avatars.githubusercontent.com/u/36070057?v=4\" /><br> poyoho </a> |\n| ------------------------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------- |\n\n## Contributing\n\nFeel free to create issues/discussions and then PRs for the project!\n\n## Sponsors\n\nYour sponsorship can make a huge difference in continuing our work in open source!\n\n<p align=\"center\">\n  <a href=\"https://cdn.jsdelivr.net/gh/aslemammad/static/sponsors.svg\">\n    <img src='https://cdn.jsdelivr.net/gh/aslemammad/static/sponsors.svg'/>\n  </a>\n</p>\n", "readmeFilename": "README.md"}