{"_id": "@csstools/color-helpers", "_rev": "13-d71e1fa9e4b473f7cd1be91c8bc9308c", "name": "@csstools/color-helpers", "dist-tags": {"latest": "5.0.2"}, "versions": {"1.0.0": {"name": "@csstools/color-helpers", "version": "1.0.0", "keywords": ["colors", "css"], "license": "CC0-1.0", "_id": "@csstools/color-helpers@1.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "7097722a51da1e9e622345ca000261f1ae6e8f58", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-1.0.0.tgz", "fileCount": 77, "integrity": "sha512-tgqtiV8sU/VaWYjOB3O7PWs7HR/MmOLl2kTYRW2qSsTSEniJq7xmyAYFB1LPpXvvQcE5u2ih2dK9fyc8BnrAGQ==", "signatures": [{"sig": "MEUCIQDQdy22U9Eq1hXMI4+g5z92DtP+D3hnwNCDaRp26ozcrwIgCgjYHhpfbKvG4nkBvLmuaxgljCtOZJniUHppkk2KmH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj23dfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO+hAAo2M/WSutMafKdqWl7fVDYx3AXgvJNZHT/b1T+quSZGUmfU8w\r\nfKwuKLE9R29O+MNKTzIQSQFywQro1a5fz6j5Y5bx2ymyKxZg3uM5tyqPckRg\r\n4BwLI4tOofBG93aRXvlm5/ciRGwtOf2mI0YX528MksgceOoJmqvrgGosvEAv\r\nPSsvcFuvFNNtpiZbIbuV3uzNasjMFMjCPSJ0UKBCjVlB1vGBsMLykTlHlIJW\r\nRQaB3cDm+raNHekqsweeR6yFAisisxdaRENAyC4W6/L53aHVaPplgQjq85as\r\nGWIcYypRw8q/3Ffi7ILQ9d8YJMF0xBUEcmNL4R99k/X+s6w6Z8Yt0faknQmu\r\n0LXp3AsJboDZeoDWPb0p8wM69GaHfxvJ8Ms1iDMjL0TPHaNAjmS0uLRIrpxM\r\nghT0oi/vS/4SXcDEyc5alRomT7Ty+badXTWiH3j821xkLGC/lktB60PUNJ5F\r\nupixbzqJUQG36nw3s9jIP632EsGnEiWgKegal7FIlKaxoecriGioS8SxBep6\r\nCzMZtUYZVdzYeGGhth9li/4RiK14Cswz7NzAyDhmb54bvS047tBdRNg1qPYP\r\nGYc0J1FJsMs/xQ54CTT5MllfVL+hCXs4XYex7Ou+npkuRhfmUEhBK5O9+lZL\r\ns95QBYADC3gJ4lRDkMC9BYmTUnde8SWMSTw=\r\n=uhvV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "e2063baeecc582fe2b7d9c5388a34881f0d587b6", "scripts": {"lint": "npm run lint:eslint && npm run lint:package-json", "test": "npm run test:exports", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "stryker": "stryker run --logLevel error", "prebuild": "npm run clean", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.4.1", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "16.13.1", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_1.0.0_1675327326924_0.9949640753896947", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@csstools/color-helpers", "version": "2.0.0", "keywords": ["colors", "css"], "license": "CC0-1.0", "_id": "@csstools/color-helpers@2.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "4ac578cb00b4e853b94f2250267d85ba957c4fc9", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-2.0.0.tgz", "fileCount": 59, "integrity": "sha512-VcPjEnp07RNgz/D+oI2uIALg+IPCSl6mj0XhA3pl3F2bM2B95vgzatExmmzSg/X0zkh+R2v+jFY/J2pV/bnwpw==", "signatures": [{"sig": "MEUCIDm1+YucQL4WVEHjPp3sVlCbcqCOPeoxRZ+TRfsylRVnAiEAo739WtHcQ20vdQFBCqzAh2NNQ4RXe7XN9JYy2tOa8pI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGmQ//fOTEEuybVyjUctQUWl1ztgx1kB4HU+biqalRFgQ6sHCoSFnR\r\nFH1mh+Gu22P9YY8RuDK4QGOqXL+Lx1WgrIfPtYJ5hTDX1a218bN3u/pKzkFU\r\nEpuCoCwl86T5CgvlEYsEERX9GdVsFYvcrPvYNPVGmlwVtrijhldng9zzK0f4\r\neOt6UjXT1AicC0yEUM0quCt4vwI2Tb+SBbDDRWfumicVbkkugMhBtWqb1a5c\r\nKhNxctlagphiSuq+Nd+bADloIC3GKdoPOvx9IpK1/Qx9bDMhkQ1F7ocffxpA\r\nixYf4IH/Vn9U6mg8V87/n5fM5rsNkWUEOH1ykZU8dPxh35iSxvq6eO25MXJY\r\nM9aYpx9uZxhH9hbjThXbzzdaHtRflZHL0nqb6QKhRSyTuNPLRFqmOHCg8Ai0\r\npiMMuucvfHXP3wFnZk4sDE+JCk6zpl4V97e9oKmagogIvd5/T9n5Ivt5NXT2\r\nGIOMbHKHfHtuVehMjAN4CTpT0fe6RqsjMrZ3ye0lsNU4NjqqKK0+4kdOl2qG\r\n9i+o8oPfFbjWNtNWK4ObFJw/meyjGWi0R4TAYtTCyxgRzgF1p7dJ4/IvaYyz\r\n/Pt0gEV9ou9HlZL+HSK26puHFgxu09/91KOCdqcBDl1Fo6HQG57HswuFgErq\r\nHzKmBALOGJtWn1/cWd+vojDqodL4zbxP2/A=\r\n=gNq+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}, "gitHead": "f747878ca88d9f7a19a95b4e690aa4d1c5bdc238", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "prepublishOnly": "npm run build && npm run test", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.4.1", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "18.13.0", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_2.0.0_1679729993054_0.8729418550863257", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@csstools/color-helpers", "version": "2.1.0", "keywords": ["colors", "css"], "license": "CC0-1.0", "_id": "@csstools/color-helpers@2.1.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "b27d8376e9e8a947878f10967481c22bf046976a", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-2.1.0.tgz", "fileCount": 60, "integrity": "sha512-OWkqBa7PDzZuJ3Ha7T5bxdSVfSCfTq6K1mbAhbO1MD+GSULGjrp45i5RudyJOedstSarN/3mdwu9upJE7gDXfw==", "signatures": [{"sig": "MEYCIQC6aZyGLsctQv8pANMWUxhqGkH9eS1rLMvucNNoVs7TkgIhAMOTB55YY6cxEWAYYsFkCncj96MtHzwOz8im4Y86Gae3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114601}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "ef5ea8e98db61bb357f8f224def072c7833cd1bf", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "prepublishOnly": "npm run build && npm run test", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.5.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "18.15.0", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_2.1.0_1684511460036_0.12492927028470024", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@csstools/color-helpers", "version": "3.0.0", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@3.0.0", "maintainers": [{"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "b64a9d86663b6d843b169f5da300f78c0242efc2", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.0.tgz", "fileCount": 60, "integrity": "sha512-rBODd1rY01QcenD34QxbQxLc1g+Uh7z1X/uzTHNQzJUnFCT9/EZYI7KWq+j0YfWMXJsRJ8lVkqBcB0R/qLr+yg==", "signatures": [{"sig": "MEUCIQC/Rx2bVbMHkBJOTix57v5LLmQRWYygMyeOXqDcRH5/8gIgJF79/ZhYMhclhK53nVm5oK/tsIlP+WEtP04A7XwSwpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108888}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "aaf88074689f64fdca1db391fffc5f3c337ef2da", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "prepublishOnly": "npm run build && npm run test", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "alaguna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.5.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "18.15.0", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_3.0.0_1688371656762_0.9594461298485648", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "@csstools/color-helpers", "version": "3.0.1", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@3.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "16013825e8c730de164a5dad27c299abae2eee18", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.1.tgz", "fileCount": 60, "integrity": "sha512-Tsp6FcSPaPN/+4T7iBPxBVopJUs7bimnNx4yuWeGXkH084Vro/y8fmrGg1LGSWH8SU6YuH20fP5Rtqtb979jyw==", "signatures": [{"sig": "MEUCIQChTxVjhSavE18CnpkmUPp3wvCVQTbgGpsgGwub5Dj2pAIgbSSBhQ9oW5XbaXm55aPpEDLvGJJBROC8ScnniDoAzNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108988}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "df86f626f2b10320861bfa6658c7fb95daa47aae", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "prepublishOnly": "npm run build && npm run test", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.8.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "20.5.0", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_3.0.1_1693221047645_0.4237220844766991", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "@csstools/color-helpers", "version": "3.0.2", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@3.0.2", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "6571d289af8bfcc3a8d75357b35e6d17a8ba6848", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.2.tgz", "fileCount": 60, "integrity": "sha512-NMVs/l7Y9eIKL5XjbCHEgGcG8LOUT2qVcRjX6EzkCdlvftHVKr2tHIPzHavfrULRZ5Q2gxrJ9f44dAlj6fX97Q==", "signatures": [{"sig": "MEQCIGTtA3l+yw6cKqx+ljvUK+4oBWwD/Y7Uelz8GR+dSf8IAiAQoW7UX0ywdZBYzRI9KXLv9avtgyh+1Oi39CFpKmbNzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109030}, "main": "dist/index.cjs", "types": "dist/index.d.ts", "volta": {"extends": "../../package.json"}, "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": "./dist/index.mjs", "default": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "353863ad856180965ad6af59b57b0c63d7ad640b", "scripts": {"lint": "node ../../.github/bin/format-package-json.mjs", "test": "node ./test/_import.mjs && node ./test/_require.cjs", "build": "rollup -c ../../rollup/default.mjs", "prepublishOnly": "npm run build && npm run test", "check-sources-integrity": "node ./scripts/check-changes.mjs", "check-sources-integrity:rewrite": "REWRITE_HASHES=true node ./scripts/check-changes.mjs"}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "9.8.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "20.5.0", "eslintConfig": {"rules": {"@typescript-eslint/no-loss-of-precision": "off"}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_3.0.2_1693674428535_0.04427146799026693", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@csstools/color-helpers", "version": "4.0.0", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@4.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "a1d6ffcefe5c1d389cbcca15f46da3cdaf241443", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-wjyXB22/h2OvxAr3jldPB7R7kjTUEzopvjitS8jWtyd8fN6xJ8vy1HnHu0ZNfEkqpBJgQ76Q+sBDshWcMvTa/w==", "signatures": [{"sig": "MEQCIAYsGLA5Z3TNGqYAhEeCJBnhpxqWhHMMoPbM+90rpgs/AiBK/Jp07tzHfOXQNc6nZgFdB69ORN+usTygIWYoJRznUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70585}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "b33ce02ea9ae4173743813909458022cafd550fd", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.2.3", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_4.0.0_1702682078705_0.4681135823108544", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@csstools/color-helpers", "version": "4.1.0", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@4.1.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "801977ec22c8eb23f9627a4f602e48beaa963bc2", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-pWRKF6cDwget8HowIIf2MqEmqIca/cf8/jO4b3PRtUF5EfQXYMtBIKycXB4yXTCUmwLKOoRZAzh/hjnc7ywOIg==", "signatures": [{"sig": "MEUCIQD+F0ive55jo0Jec2TQovTu1xtxRfCIQFhPDqi6RniGHwIgDxCrAXcqmsfNW0Y891FytsQqje5fFkt/ph8EQaYtDW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72226}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "cc76797a2915cd9ed06142b151c31b7c9daef328", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.2.4", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_4.1.0_1711891061081_0.902354077408263", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@csstools/color-helpers", "version": "4.2.0", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@4.2.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "e8629ca9dce03a3a309506e7892b7f862673cf85", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-hJJrSBzbfGxUsaR6X4Bzd/FLx0F1ulKnR5ljY9AiXCtsR+H+zSWQDFWlKES1BRaVZTDHLpIIHS9K2o0h+JLlrg==", "signatures": [{"sig": "MEUCIEyFRT3FN7B67/6j8vScjV7QiHIvL6hz8DFRJ/2kpJqZAiEA1qzrPzj1JGhzsGeWRyO4c6nbEWrjbONzAXB9RYlt2dY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74039}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "f31d623426f13970247a716f22e2346aff96c808", "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.2.4", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_4.2.0_1713709569021_0.6446575671890562", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "@csstools/color-helpers", "version": "4.2.1", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@4.2.1", "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "da573554220ccb59757f12de62bf70c6b15645d4", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.2.1.tgz", "fileCount": 7, "integrity": "sha512-CEypeeykO9AN7JWkr1OEOQb0HRzZlPWGwV0Ya6DuVgFdDi6g3ma/cPZ5ZPZM4AWQikDpq/0llnGGlIL+j8afzw==", "signatures": [{"sig": "MEQCIDLNr6NQ1wjFJ5ijQNpe4TsKJP6NDds6AersKwArtDOQAiBbSJeCDouKeXW58Lv/6S8tV+ZNFkK3Eu2gXtnRg0qQig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76191}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "2de97fd948f1b12ea679ca43518cfbdfb5aa0ebf", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.7.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_4.2.1_1719698256359_0.5871402801050429", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "@csstools/color-helpers", "version": "5.0.0", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@5.0.0", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "f6d31d00ed4467608974d0df7ea7fa46a2265758", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-4NemgIXEFNi7mKVUt5vt9O06UFQfpIQSjIyRbrYsfb+BLxKecasENHyIzn//lrI+Za7bcDznBSjKLajgeKmJ9A==", "signatures": [{"sig": "MEUCIAGbeDMYxGYG0LaopXojksLXOuTtS8nildv6FMAu24SAAiEAmNbB7pDsLkb4ajOv/1Fzka8/i+bSm+gX1nY3Ag9pvkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79356}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "859257dac64216361f43d12ba05ddf3cf71dd5f8", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.7.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_5.0.0_1722720016151_0.5378370828388142", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "@csstools/color-helpers", "version": "5.0.1", "keywords": ["colors", "css"], "license": "MIT-0", "_id": "@csstools/color-helpers@5.0.1", "maintainers": [{"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}], "contributors": [{"url": "https://antonio.laguna.es", "name": "Antonio <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "dist": {"shasum": "829f1c76f5800b79c51c709e2f36821b728e0e10", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-MKtmkA0BX87PKaO1NFRTFH+UnkgnmySQOvNxJubsadusqPEC2aJ9MOQiMceZJJ6oitUl/i0L6u0M1IrmAOmgBA==", "signatures": [{"sig": "MEUCIQCvVKl95+YmjPPZnY3iHpbxCP5Biupqo84AXHH1xS1mcgIgZpyTfXJl8OJtnYOQQFc6P/ZatXys8wmaqgvIlhQH2tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79827}, "main": "dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}], "gitHead": "f19dc3955a3e1a7cd16cb2b542f4ee66fc75f1a8", "scripts": {}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/csstools/postcss-plugins.git", "type": "git", "directory": "packages/color-helpers"}, "_npmVersion": "10.7.0", "description": "Color helpers to ease transformation between formats, gamut, etc", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/color-helpers_5.0.1_1723638258077_0.5625690180644618", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "@csstools/color-helpers", "description": "Color helpers to ease transformation between formats, gamut, etc", "version": "5.0.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/color-helpers"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "keywords": ["colors", "css"], "_id": "@csstools/color-helpers@5.0.2", "gitHead": "d8c7bd71349022f4da87dd27acb9b0561ba15a61", "types": "./dist/index.d.ts", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA==", "shasum": "82592c9a7c2b83c293d9161894e2a6471feb97b8", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.2.tgz", "fileCount": 7, "unpackedSize": 79946, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICC/s+gvTJaeAPt1DJt5wIQLpcLtzty0zdK3lyQkp/pYAiEAwQtUs/ba6C1H/aqkrNtMz50OdWV4BoVcvX98UbODc0E="}]}, "_npmUser": {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/color-helpers_5.0.2_1740330306724_0.20346043600407815"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-02T08:42:06.851Z", "modified": "2025-02-23T17:05:07.173Z", "1.0.0": "2023-02-02T08:42:07.108Z", "2.0.0": "2023-03-25T07:39:53.332Z", "2.1.0": "2023-05-19T15:51:00.216Z", "3.0.0": "2023-07-03T08:07:36.984Z", "3.0.1": "2023-08-28T11:10:47.857Z", "3.0.2": "2023-09-02T17:07:08.769Z", "4.0.0": "2023-12-15T23:14:38.893Z", "4.1.0": "2024-03-31T13:17:41.251Z", "4.2.0": "2024-04-21T14:26:09.194Z", "4.2.1": "2024-06-29T21:57:36.542Z", "5.0.0": "2024-08-03T21:20:16.335Z", "5.0.1": "2024-08-14T12:24:18.258Z", "5.0.2": "2025-02-23T17:05:06.953Z"}, "bugs": {"url": "https://github.com/csstools/postcss-plugins/issues"}, "license": "MIT-0", "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers#readme", "keywords": ["colors", "css"], "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/color-helpers"}, "description": "Color helpers to ease transformation between formats, gamut, etc", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "jonathan<PERSON><PERSON>l", "email": "<EMAIL>"}, {"name": "alaguna", "email": "<EMAIL>"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Color Helpers <img src=\"https://cssdb.org/images/css.svg\" alt=\"for CSS\" width=\"90\" height=\"90\" align=\"right\">\n\n[<img alt=\"npm version\" src=\"https://img.shields.io/npm/v/@csstools/color-helpers.svg\" height=\"20\">][npm-url]\n[<img alt=\"Build Status\" src=\"https://github.com/csstools/postcss-plugins/workflows/test/badge.svg\" height=\"20\">][cli-url]\n[<img alt=\"Discord\" src=\"https://shields.io/badge/Discord-5865F2?logo=discord&logoColor=white\">][discord]\n\n## Usage\n\nAdd [Color Helpers] to your project:\n\n```bash\nnpm install @csstools/color-helpers --save-dev\n```\n\nThis package exists to join all the different color functions scattered among the Colors 4 and Colors 5 plugins we maintain such as:\n\n* [PostCSS Color Function]\n* [PostCSS Lab Function]\n* [PostCSS OKLab Function]\n\n## Copyright\n\nThis software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/tree/main/css-color-4. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).\n\n[cli-url]: https://github.com/csstools/postcss-plugins/actions/workflows/test.yml?query=workflow/test\n[discord]: https://discord.gg/bUadyRwkJS\n[npm-url]: https://www.npmjs.com/package/@csstools/color-helpers\n\n[Color Helpers]: https://github.com/csstools/postcss-plugins/tree/main/packages/color-helpers\n[PostCSS Color Function]: https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-function\n[PostCSS Lab Function]: https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-lab-functionw\n[PostCSS OKLab Function]: https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-oklab-function\n", "readmeFilename": "README.md"}