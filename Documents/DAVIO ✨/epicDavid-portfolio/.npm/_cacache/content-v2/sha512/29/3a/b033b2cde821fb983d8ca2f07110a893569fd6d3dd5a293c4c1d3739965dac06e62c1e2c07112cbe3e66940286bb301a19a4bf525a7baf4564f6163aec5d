{"_id": "tinyexec", "_rev": "13-ed12842e3f783d2ecbc134e9aa267160", "name": "tinyexec", "dist-tags": {"next": "0.1.3-beta.0", "latest": "1.0.1"}, "versions": {"0.0.1": {"name": "tinyexec", "version": "0.0.1", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.0.1", "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "56cf07574d3f95f1cc9b4c456e070ebc66ab1e8b", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.0.1.tgz", "fileCount": 7, "integrity": "sha512-xsWh3Fki7DzGDREHY32cUIxpmN/vGpNCNY5FDEfRgl7gI8U3r3CACItDeDKeE2aymmYFXHuh1x1Vg1E0K8Ry7g==", "signatures": [{"sig": "MEUCIQDJTVMDz/PYg5YeAGOxqSt6MGxK7AVyTNrUg5uFfNInXAIgBiy5BqJd/2/+9jmW+WLtoGb5KAT6tKGZrx2D2Ss6X6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43457}, "main": "./dist/commonjs/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"default": "./dist/main.js"}, "require": {"default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "bf0e11f6de3a37248694e3614404f65657d83fb6", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.0.1_1719867528191_0.19699486343823014", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "tinyexec", "version": "0.1.0", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.0", "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "bd10b38e99ad88c6fcdd0dbe1ba8ce422e6cf52b", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-tYj54mRhCxlENHRIwG7yq4cVeAcxgUAvawJFJd3p8lhHyIrIOnEXSi7ZQKaXZuykKNoAXwIqMjCMDAWHEGLtpg==", "signatures": [{"sig": "MEQCIDhNH7gHU/EjUFAHJ3tzylfbSBnTVehIMiV4n2AU1qyCAiBoCkxPoX2pqqGg36n5jV/GivuaNNprmwFdHXlUaOlUNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 43457}, "main": "./dist/commonjs/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"default": "./dist/main.js"}, "require": {"default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "f92433592b7646a66ee6b629f60213a7fb39426f", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.0_1719867856592_0.3056875343632972", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "tinyexec", "version": "0.1.1", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.1", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "a791534941988f8f244829c1869a77d311f944c1", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.1.tgz", "fileCount": 7, "integrity": "sha512-e/NnZmEGwoPDWPdLIsQPsi0QL321W42t/QzZf2e97L5mMj+UGoq2a9QmC+le6h6SEYaD4sSVZIfC0QYqcjw8fQ==", "signatures": [{"sig": "MEUCIQCm1RNsou7bqPMIhKzpyvGEx1ga/QyiwNHWkq27ZgunfwIgIqUYtI9oR3rzOaChynvFVEEj/AXytI7uK+8xGNapuzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 43528}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "614666fbe03e1352b6d510dedd64cbd0ddfbbe8c", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.1_1719952338788_0.3790071711975689", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "tinyexec", "version": "0.1.2", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.2", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "7b661acd40bfd928c45f67e7eb57c041063f7443", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.2.tgz", "fileCount": 7, "integrity": "sha512-aVXhKcB7XIe0zjjpVhU9vJ7AAPF8VsGpzx0IpmVeYLILRZUWcEQ4Pq/dnUodF5cAYZohjzCP1HkC8hChqZ3pNA==", "signatures": [{"sig": "MEUCIDEcRtf857QfUAiOSC7vRdVaxO/i5NSyrs/rNjag58E6AiEAmyvfMPNzEDMtPKnXiCVXe7bg4BY15rvZ77oA5aUxRuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 43528}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "9bd1f9f9337ddb92ca6473edf641bc50574dfa34", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.2_1719955675931_0.8608628099393332", "host": "s3://npm-registry-packages"}}, "0.1.3-beta.0": {"name": "tinyexec", "version": "0.1.3-beta.0", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.3-beta.0", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "f72550e94a9386ac48fbb48b74d0131a74dfd0f5", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.3-beta.0.tgz", "fileCount": 7, "integrity": "sha512-b3qjOT5idKiLWYSKcRZDsQDXimXW56oqi6FEkOL9gfDcDA7DkirtzUa2nIdzn33JDlQAveRfaoG0IcJFoXxSbQ==", "signatures": [{"sig": "MEUCIQCQHTJr/K5Z3vzwgKQekT0Tkwg7f14ZofbMA3x4IV4zBwIgLnBTSNmYliOG0AeWS8942gwRa1Gfu+61v2F8Uil+dNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.3-beta.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 42732}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "ea20f57acb179cf1a4de3367ef0d734630c0d1bc", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.3-beta.0_1723057017662_0.4054801210550636", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "tinyexec", "version": "0.1.3", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.3", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "fbbcecbbb41bf44644f454056c6663aedcf7e7d1", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-4fWrRHlTfAhQi3ORSSLVA7OUAA7KwV9eJnRekNZsswe0JXv4kHe9ma91CW6MikrycIXkbmtFOwc6GzcajyPq5g==", "signatures": [{"sig": "MEUCIQCLyKLNEkfKCObVCkcxzVNBetmzyOqKUIXcNMqKH59R2QIgDH+2yu20XgcEeUphSow5GeCsPakXW5vwzaOSxhqszkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 42725}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "ea20f57acb179cf1a4de3367ef0d734630c0d1bc", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.3_1723057146896_0.26576345460452466", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "tinyexec", "version": "0.1.4", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.1.4", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "29d24b14294bddcd35d78cab9730e05b725a9fd8", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.1.4.tgz", "fileCount": 7, "integrity": "sha512-Ba2ELcNnnWkgqnAJBouhcsDsYitbD9LIAVNSz3746u50f+tlF3wO0uB3uqyz8NHFSTpv23qtT47XGDw8pXW5DA==", "signatures": [{"sig": "MEUCIQDtzy7WqcyisFz/ZOhpTxsljQyBM2dY5+3IQDR/4iiragIgUcD2erPYj+XAKVB1Jxvk462h0fqmKvMGDQ2XYWl48Mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 42550}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "9bbc2b46287644b2473489cc57d8b6df6da163b8", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.1.4_1723126869720_0.8363746377526404", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "tinyexec", "version": "0.2.0", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.2.0", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "85411bbec5964a8cb1f7ebe4e3850860809d04fb", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-au8dwv4xKSDR+Fw52csDo3wcDztPdne2oM1o/7LFro4h6bdFmvyUAeAfX40pwDtzHgRFqz1XWaUqgKS2G83/ig==", "signatures": [{"sig": "MEUCICnfpuVYT8YxINR+g1K0ArwmRfUkXfqXEt5XFmB5PFfZAiEAsDOCyhaIqGs4UZesTk+X9ytGoSFwTlGVgMMBBKOnL7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 42556}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "66fd9f7da7d94c73efd56423dfbc1abda2906308", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.2.0_1724062203022_0.8401953744818931", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "tinyexec", "version": "0.3.0", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.3.0", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "ed60cfce19c17799d4a241e06b31b0ec2bee69e6", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-tVGE0mVJPGb0chKhqmsoosjsS+qUnJVGJpZgsHYQcGoPlG3B51R3PouqTgEGH2Dc9jjFyOqOpix6ZHNMXp1FZg==", "signatures": [{"sig": "MEUCIQDDlVqMkm4W1ZEbeBpyTZbbc7qKJfpDIbD0BTiuvYYWIAIgJ7PKoD0gW1YUupIEZdU+kk0cgOA4YclFJOUreQ/3VNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45385}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "f11e42bf5a25b1af9ff84215bb1693da6537d433", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.3.0_1724608259025_0.17306436428305738", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "tinyexec", "version": "0.3.1", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.3.1", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "0ab0daf93b43e2c211212396bdb836b468c97c98", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.1.tgz", "fileCount": 7, "integrity": "sha512-WiCJLEECkO18gwqIp6+hJg0//p23HXp4S+gGtAKu3mI2F2/sXC4FvHvXvB0zJVVaTPhx1/tOwdbRsa1sOBIKqQ==", "signatures": [{"sig": "MEQCIADJbDQr4efdUoq9Ke0Qk9TWykhcagqXhw/pOm8GhaXVAiBVWS4lidR6q0AA9L7Nq657XiZbzP4mwFSZxcH4c7emYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 45566}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "24e98613798c024ccd7742f538e0958ebe6efd2d", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.3.1_1729050207370_0.9794424491420484", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "tinyexec", "version": "0.3.2", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@0.3.2", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "941794e657a85e496577995c6eef66f53f42b3d2", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.2.tgz", "fileCount": 7, "integrity": "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==", "signatures": [{"sig": "MEUCIQDGq/jpugCgrO6ZfrVkj8pfYLeEXcC1108/GlSF6Tje9gIgb3uHTCSqKSgO7XUEJq7Xcg+lf2+mMsbOX/lr2T8OBzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@0.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 46086}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}}, "./package.json": "./package.json"}, "gitHead": "2adbaf722a3a3f252345ad3609c67a6165badd42", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_0.3.2_1735473824459_0.7386943974161095", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0": {"name": "tinyexec", "version": "1.0.0", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"url": "https://github.com/43081j", "name": "<PERSON>"}, "license": "MIT", "_id": "tinyexec@1.0.0", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/tinylibs/tinyexec#readme", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "dist": {"shasum": "04421c6e5074c38c93e5bea1c55ef1ab526ed0f9", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-djtKaAR3lnRo0uMT/IKYwj3s4E86IE/SdwCE3XXIJcozbDFKsfgAmi8IBIpIDmTd0Y6YKyfqo3vQ33OloPJPaQ==", "signatures": [{"sig": "MEQCICsYXVoQHSyuJtQzlJOmGUvGVUTw3c3p5cUsGmKrDRQ+AiAfStgTOrqtSJ0dO9VHQ2sWoczNwCRV8ALW2pKMjqeOwA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27023}, "main": "./dist/main.js", "type": "module", "types": "./dist/main.d.ts", "exports": {".": {"import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}}, "./package.json": "./package.json"}, "gitHead": "da5f619de2ac6745a6b936a75bf5c2c03e831e36", "scripts": {"dev": "tsup --watch", "lint": "eslint src", "test": "npm run build && c8 node --test", "build": "npm run build:types && tsup", "format": "prettier --write src", "prepare": "npm run build", "build:types": "tsc", "format:check": "prettier --check src"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/tinylibs/tinyexec.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A minimal library for executing processes in Node", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tsup": "^8.1.0", "prettier": "^3.2.5", "@eslint/js": "^9.0.0", "typescript": "^5.4.5", "@types/node": "^20.12.7", "cross-spawn": "^7.0.3", "typescript-eslint": "^7.7.0", "@types/cross-spawn": "^6.0.6", "eslint-config-google": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/tinyexec_1.0.0_1742332699311_0.012030529097709852", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "tinyexec", "version": "1.0.1", "type": "module", "description": "A minimal library for executing processes in Node", "main": "./dist/main.js", "scripts": {"build": "npm run build:types && tsup", "build:types": "tsc", "dev": "tsup --watch", "format": "prettier --write src", "format:check": "prettier --check src", "lint": "eslint src", "prepare": "npm run build", "test": "npm run build && c8 node --test"}, "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyexec.git"}, "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "author": {"name": "<PERSON>", "url": "https://github.com/43081j"}, "license": "MIT", "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "homepage": "https://github.com/tinylibs/tinyexec#readme", "devDependencies": {"@eslint/js": "^9.0.0", "@types/cross-spawn": "^6.0.6", "@types/node": "^20.12.7", "c8": "^9.1.0", "cross-spawn": "^7.0.3", "eslint-config-google": "^0.14.0", "prettier": "^3.2.5", "tsup": "^8.1.0", "typescript": "^5.4.5", "typescript-eslint": "^7.7.0"}, "exports": {".": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "./package.json": "./package.json"}, "types": "./dist/main.d.ts", "_id": "tinyexec@1.0.1", "gitHead": "2b396de55e187c7cba133a8cdce5a1681ec44a7e", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-5uC6DDlmeqiOwCPmK9jMSdOuZTh8bU39Ys6yidB+UTt5hfZUPGAypSgFRiEp+jbi9qH40BLDvy85jIU88wKSqw==", "shasum": "70c31ab7abbb4aea0a24f55d120e5990bfa1e0b1", "tarball": "https://registry.npmjs.org/tinyexec/-/tinyexec-1.0.1.tgz", "fileCount": 5, "unpackedSize": 26993, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tinyexec@1.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCAozd2iH8LplTOVJeMEKzXyz1mzlB2HagxbYmUSnJU2QIhAN0Ww0a4Wm9KfL44DvdXaYk4KHBzN459qowjNmpZj5uU"}]}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tinyexec_1.0.1_1743084435830_0.24406807370760397"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-07-01T20:58:48.190Z", "modified": "2025-03-27T14:07:16.488Z", "0.0.1": "2024-07-01T20:58:48.375Z", "0.1.0": "2024-07-01T21:04:16.813Z", "0.1.1": "2024-07-02T20:32:18.976Z", "0.1.2": "2024-07-02T21:27:56.144Z", "0.1.3-beta.0": "2024-08-07T18:56:57.877Z", "0.1.3": "2024-08-07T18:59:07.092Z", "0.1.4": "2024-08-08T14:21:09.935Z", "0.2.0": "2024-08-19T10:10:03.259Z", "0.3.0": "2024-08-25T17:50:59.179Z", "0.3.1": "2024-10-16T03:43:27.600Z", "0.3.2": "2024-12-29T12:03:44.675Z", "1.0.0": "2025-03-18T21:18:19.504Z", "1.0.1": "2025-03-27T14:07:16.058Z"}, "bugs": {"url": "https://github.com/tinylibs/tinyexec/issues"}, "author": {"name": "<PERSON>", "url": "https://github.com/43081j"}, "license": "MIT", "homepage": "https://github.com/tinylibs/tinyexec#readme", "keywords": ["execa", "exec", "tiny", "child_process", "spawn"], "repository": {"type": "git", "url": "git+https://github.com/tinylibs/tinyexec.git"}, "description": "A minimal library for executing processes in Node", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}], "readme": "# tinyexec 📟\n\n> A minimal package for executing commands\n\nThis package was created to provide a minimal way of interacting with child\nprocesses without having to manually deal with streams, piping, etc.\n\n## Installing\n\n```sh\n$ npm i -S tinyexec\n```\n\n## Usage\n\nA process can be spawned and awaited like so:\n\n```ts\nimport {x} from 'tinyexec';\n\nconst result = await x('ls', ['-l']);\n\n// result.stdout - the stdout as a string\n// result.stderr - the stderr as a string\n// result.exitCode - the process exit code as a number\n```\n\nYou may also iterate over the lines of output via an async loop:\n\n```ts\nimport {x} from 'tinyexec';\n\nconst proc = x('ls', ['-l']);\n\nfor await (const line of proc) {\n  // line will be from stderr/stdout in the order you'd see it in a term\n}\n```\n\n### Options\n\nOptions can be passed to have finer control over spawning of the process:\n\n```ts\nawait x('ls', [], {\n  timeout: 1000\n});\n```\n\nThe options object can have the following properties:\n\n- `signal` - an `AbortSignal` to allow aborting of the execution\n- `timeout` - time in milliseconds at which the process will be forceably killed\n- `persist` - if `true`, the process will continue after the host exits\n- `stdin` - another `Result` can be used as the input to this process\n- `nodeOptions` - any valid options to node's underlying `spawn` function\n- `throwOnError` - if true, non-zero exit codes will throw an error\n\n### Piping to another process\n\nYou can pipe a process to another via the `pipe` method:\n\n```ts\nconst proc1 = x('ls', ['-l']);\nconst proc2 = proc1.pipe('grep', ['.js']);\nconst result = await proc2;\n\nconsole.log(result.stdout);\n```\n\n`pipe` takes the same options as a regular execution. For example, you can\npass a timeout to the pipe call:\n\n```ts\nproc1.pipe('grep', ['.js'], {\n  timeout: 2000\n});\n```\n\n### Killing a process\n\nYou can kill the process via the `kill` method:\n\n```ts\nconst proc = x('ls');\n\nproc.kill();\n\n// or with a signal\nproc.kill('SIGHUP');\n```\n\n### Node modules/binaries\n\nBy default, node's available binaries from `node_modules` will be accessible\nin your command.\n\nFor example, in a repo which has `eslint` installed:\n\n```ts\nawait x('eslint', ['.']);\n```\n\nIn this example, `eslint` will come from the locally installed `node_modules`.\n\n### Using an abort signal\n\nAn abort signal can be passed to a process in order to abort it at a later\ntime. This will result in the process being killed and `aborted` being set\nto `true`.\n\n```ts\nconst aborter = new AbortController();\nconst proc = x('node', ['./foo.mjs'], {\n  signal: aborter.signal\n});\n\n// elsewhere...\naborter.abort();\n\nawait proc;\n\nproc.aborted; // true\nproc.killed; // true\n```\n\n### Using with command strings\n\nIf you need to continue supporting commands as strings (e.g. \"command arg0 arg1\"),\nyou can use [args-tokenizer](https://github.com/TrySound/args-tokenizer),\na lightweight library for parsing shell command strings into an array.\n\n```ts\nimport {x} from 'tinyexec';\nimport {tokenizeArgs} from 'args-tokenizer';\n\nconst commandString = 'echo \"Hello, World!\"';\nconst [command, ...args] = tokenizeArgs(commandString);\nconst result = await x(command, args);\n\nresult.stdout; // Hello, World!\n```\n\n## API\n\nCalling `x(command[, args])` returns an awaitable `Result` which has the\nfollowing API methods and properties available:\n\n### `pipe(command[, args[, options]])`\n\nPipes the current command to another. For example:\n\n```ts\nx('ls', ['-l'])\n  .pipe('grep', ['js']);\n```\n\nThe parameters are as follows:\n\n- `command` - the command to execute (_without any arguments_)\n- `args` - an array of arguments\n- `options` - options object\n\n### `process`\n\nThe underlying node `ChildProcess`. For example:\n\n```ts\nconst proc = x('ls');\n\nproc.process; // ChildProcess;\n```\n\n### `kill([signal])`\n\nKills the current process with the specified signal. By default, this will\nuse the `SIGTERM` signal.\n\nFor example:\n\n```ts\nconst proc = x('ls');\n\nproc.kill();\n```\n\n### `pid`\n\nThe current process ID. For example:\n\n```ts\nconst proc = x('ls');\n\nproc.pid; // number\n```\n\n### `aborted`\n\nWhether the process has been aborted or not (via the `signal` originally\npassed in the options object).\n\nFor example:\n\n```ts\nconst proc = x('ls');\n\nproc.aborted; // bool\n```\n\n### `killed`\n\nWhether the process has been killed or not (e.g. via `kill()` or an abort\nsignal).\n\nFor example:\n\n```ts\nconst proc = x('ls');\n\nproc.killed; // bool\n```\n\n### `exitCode`\n\nThe exit code received when the process completed execution.\n\nFor example:\n\n```ts\nconst proc = x('ls');\n\nproc.exitCode; // number (e.g. 1)\n```\n\n## Comparison with other libraries\n\n`tinyexec` aims to provide a lightweight layer on top of Node's own\n`child_process` API.\n\nSome clear benefits compared to other libraries are that `tinyexec` will be much lighter, have a much\nsmaller footprint and will have a less abstract interface (less \"magic\"). It\nwill also have equal security and cross-platform support to popular\nalternatives.\n\nThere are various features other libraries include which we are unlikely\nto ever implement, as they would prevent us from providing a lightweight layer.\n\nFor example, if you'd like write scripts rather than individual commands, and\nprefer to use templating, we'd definitely recommend\n[zx](https://github.com/google/zx). zx is a much higher level library which\ndoes some of the same work `tinyexec` does but behind a template string\ninterface.\n\nSimilarly, libraries like `execa` will provide helpers for various things\nlike passing files as input to processes. We opt not to support features like\nthis since many of them are easy to do yourself (using Node's own APIs).\n", "readmeFilename": "README.md"}