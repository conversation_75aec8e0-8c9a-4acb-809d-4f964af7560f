{"_id": "wrap-ansi", "_rev": "49-55b981b4b2fddb2cd3336411f28aca4b", "name": "wrap-ansi", "description": "Wordwrap a string with ANSI escape codes", "dist-tags": {"latest": "9.0.0", "next": "2.1.0-candidate"}, "versions": {"0.1.0": {"name": "wrap-ansi", "version": "0.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/wrap-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"splice-string": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "chalk": "^1.1.0", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "52d7c57e230b90797129efa0bdf939a942ca1747", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi", "_id": "wrap-ansi@0.1.0", "_shasum": "8eefd8f9cd51946fdd8167db4287def2b6450edd", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8eefd8f9cd51946fdd8167db4287def2b6450edd", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-0.1.0.tgz", "integrity": "sha512-Wbtiy5xldl1Tnv+cAgs24GGfzM+lb5LmZFtLAvYJKVIbpc49LEYettezk7B3QLBWKzYBgBeX5qtSaDu48KE1LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBggGH8aFDfKvkYUC2HJWPwdXvqEhdshzJzSzytjJEhHAiEA+YoAgNaa3hxiNQF3LYDLULhPR0c3osAZpbj8xY6Q1h8="}]}, "directories": {}}, "0.2.0": {"name": "wrap-ansi", "version": "0.2.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && node test.js"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"splice-string": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "chalk": "^1.1.0", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "4ce39165471dda82264cf1b125a3473eafb256dc", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@0.2.0", "_shasum": "d479162c9f93cc95eb09c5bd6256a305d4194c50", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d479162c9f93cc95eb09c5bd6256a305d4194c50", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-0.2.0.tgz", "integrity": "sha512-I/LR9/3AY6ntS0DjMGGxL5Cpz99PMWM19Ldx05UPon0nXlOV1Z2CzmNasIPuHQA957m5i3n8Fi6r8mWTva7J5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYIjrAuQZN9fluHuOYYBEvg2yxNndFcTWvP4mvpO+WWwIgQYQXFI5Ee5s78/g0m3dhZ1X7ZDyxksxHOHAZKidIDko="}]}, "directories": {}}, "0.3.0": {"name": "wrap-ansi", "version": "0.3.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/wrap-ansi"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc node test.js", "coverage": "nyc --reporter=text-lcov node test.js | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"ava": "0.0.4", "chalk": "^1.1.0", "coveralls": "^2.11.4", "nyc": "^3.2.2", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "c2c16800cd58b603f98a00fd1958b3c88b837ec0", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi", "_id": "wrap-ansi@0.3.0", "_shasum": "8b6f065943dac7aae11640f7b65e2b4f3bc6375a", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8b6f065943dac7aae11640f7b65e2b4f3bc6375a", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-0.3.0.tgz", "integrity": "sha512-9Vf1NjmlMPMPmXCNooikWa3i+Z+dUBOybOn9kNbQVY9ObPzn7n/G4p0wgE7J2SAJzdwUjYAv1vYLtPe5rzje0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG+po1aGWQe7+OD4YzlIulzX5iheKk/WQUr9DwYNp6BbAiEAkF8E0Pwbr0u64Ml11GVBIuTfnmHj8Pm5L0220ZEEL0c="}]}, "directories": {}}, "1.0.0": {"name": "wrap-ansi", "version": "1.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc node test.js", "coverage": "nyc --reporter=text-lcov node test.js | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"ava": "0.0.4", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^3.2.2", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "c890e95ea671779012fa73fd77b74198e8c5d09b", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@1.0.0", "_shasum": "f573bb9ee23cf43891f3362f5f359a1dfa38fc34", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "1.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "f573bb9ee23cf43891f3362f5f359a1dfa38fc34", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-1.0.0.tgz", "integrity": "sha512-6ywRrmmbSfxQayqXINAi4IFuEi9iJAmp1Y2Y2FYYISd0EPpFkn0yj+89Fj+tvq+HZimRoeWOOUljEQLzs6fx0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5ZbihS8iA++bl6LnVyaQNJuy/R2S6FexjoazYFn404AiAWu9RWUgp/iOjUb6fBshLSq+ticUY6QqOg33AXYxgS5w=="}]}, "directories": {}}, "2.0.0": {"name": "wrap-ansi", "version": "2.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "dthree", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc node test.js", "coverage": "nyc --reporter=text-lcov node test.js | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"ava": "0.0.4", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^3.2.2", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "89e220072d4768ecd8c5d176ff50944d66ddcc2f", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@2.0.0", "_shasum": "7d30f8f873f9a5bbc3a64dabc8d177e071ae426f", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.2.0", "_npmUser": {"name": "dthree", "email": "<EMAIL>"}, "dist": {"shasum": "7d30f8f873f9a5bbc3a64dabc8d177e071ae426f", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.0.0.tgz", "integrity": "sha512-HG2fWLyu61fAbCvUqlNNF2lOEk+WZDsUkAn5+dEtAF7dl89viTM5DyS/OxWROhJN8bRkPHT3/ww8XQVu9UQwrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQrkpNJwcNgdH8pxUAbIocymHRQXkNcIHBl4Ldq16PwQIgX5rnKg1Ff2rMS6v7S8+DPmLicGi809Fc0Rb3rGsIXMs="}]}, "directories": {}}, "2.1.0-candidate": {"name": "wrap-ansi", "version": "2.1.0-candidate", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "devDependencies": {"ava": "*", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^6.2.1", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "ff0335d8123106efa3d6f6da5637c8a6c763ebe0", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@2.1.0-candidate", "_shasum": "774ecb66c894d931899a8204d22ae990d098c3e8", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "774ecb66c894d931899a8204d22ae990d098c3e8", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0-candidate.tgz", "integrity": "sha512-iPbiwh6cS+YDyiRFTdTZLSvYq/1MHZMQnhRpDlOBM0JNSEpLexkh0MVNX3nAN0fyLkYiLf4e3kH7cL2DXrWH/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC23uujX94UmVYMmGQx0dYOCOU9htxb4ZleINPbOV5a1AIhAJ8F4zm6gu+/ZMHbSM28cxTwdhxTNqb3PPvcEDg9Fjq8"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/wrap-ansi-2.1.0-candidate.tgz_1478241256104_0.632478907937184"}, "directories": {}}, "2.1.0": {"name": "wrap-ansi", "version": "2.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "devDependencies": {"ava": "^0.16.0", "chalk": "^1.1.0", "coveralls": "^2.11.4", "has-ansi": "^2.0.0", "nyc": "^6.2.1", "strip-ansi": "^3.0.0", "xo": "*"}, "gitHead": "a731af5a3461d92f2af302e81e05ea698a3c8c1a", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@2.1.0", "_shasum": "d8fc3d284dd05794fe84973caecdd1cf824fdd85", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d8fc3d284dd05794fe84973caecdd1cf824fdd85", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "integrity": "sha512-vAaEaDM946gbNpH5pLVNR+vX2ht6n0Bt3GXwVB1AuAqZosOvHNF3P7wDnh8KLkSqgUh0uh77le7Owgoz+Z9XBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHBdT/Tcvs1NkDqqLQx6BA/YKZGLigWIIJ2AB08exiq+AiByXrKo7Fbg2UgDlh1fnaJbLdjH7CHyDbWITm4Xz984VQ=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/wrap-ansi-2.1.0.tgz_1480440082575_0.23112521297298372"}, "directories": {}}, "3.0.0": {"name": "wrap-ansi", "version": "3.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0"}, "devDependencies": {"ava": "^0.21.0", "chalk": "^2.0.1", "coveralls": "^2.11.4", "has-ansi": "^3.0.0", "nyc": "^11.0.3", "strip-ansi": "^4.0.0", "xo": "^0.18.2"}, "gitHead": "c0b4bfd24b6cc852b343cc712a3e5607c61fb511", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@3.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+l/sP6sHBT8XklwVWSF0//49Nrz5ZuM5R7TTHpXQr2AY3Zjbm5Y+7Gr2ErB/2zxcqVhXG3C8vC9nLLSHRDoLKA==", "shasum": "d357270e4a7278cf5335133b9a3cd5bb46c0ea7d", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxTwtZcHm4I5/AdnewAzYm3k54nXgwJURLOVLM2tJABgIhAOkqxcMZTnALfYsHROCO1+DnPZq82m/K393r+zo5s3Eg"}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi-3.0.0.tgz_1500809267145_0.8778855202253908"}, "directories": {}}, "3.0.1": {"name": "wrap-ansi", "version": "3.0.1", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0"}, "devDependencies": {"ava": "^0.21.0", "chalk": "^2.0.1", "coveralls": "^2.11.4", "has-ansi": "^3.0.0", "nyc": "^11.0.3", "strip-ansi": "^4.0.0", "xo": "^0.18.2"}, "gitHead": "165504b9d6a88f5b1295df6afccc16592ed6fb80", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@3.0.1", "_shasum": "288a04d87eda5c286e060dfe8f135ce8d007f8ba", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "288a04d87eda5c286e060dfe8f135ce8d007f8ba", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-3.0.1.tgz", "integrity": "sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEafeNjU8Fhf01E2KTAUEzqTwIlSz2kQCf9kg9r+PsjAIge4J31iR4KKttvTeT3IiGgJnEUZCnLW+Ml8MsvB6Tsek="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi-3.0.1.tgz_1500839932578_0.0051989073399454355"}, "directories": {}}, "4.0.0": {"name": "wrap-ansi", "version": "4.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^2.1.1", "strip-ansi": "^4.0.0"}, "devDependencies": {"ava": "^0.25.0", "chalk": "^2.0.1", "coveralls": "^3.0.0", "has-ansi": "^3.0.0", "nyc": "^13.0.1", "strip-ansi": "^4.0.0", "xo": "^0.22.0"}, "gitHead": "8640dc3fcffe1a5a643d029849123846f3b58fd3", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@4.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uMTsj9rDb0/7kk1PbcbCcwvHUxp60fGDB/NNXpVa0Q+ic/e7y5+BwTxKfQ33VYgDppSwi/FBzpetYzo8s6tfbg==", "shasum": "b3570d7c70156159a2d42be5cc942e957f7b1131", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-4.0.0.tgz", "fileCount": 4, "unpackedSize": 8296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiaBjCRA9TVsSAnZWagAAflgP/1cOaUtNjBFv9cPH0Eth\ne3L97qA7/u9buxvoqW3LmJGK33OD83k7M/X2Vso/JJ2COAc7YgGkxe0AS20w\nY8PEGJ2caCrHf0tmvNd/+I0As63R3sL4c+BLJotXRdlE0EZPCpsAopsxTjMT\nOH7JOvqSVbUpgpCTcHFUpqK3MjQ6QyOAx8zhq/1CUNtgqbIx+DIQXOlNYFSx\nRKYqFWS4sY4Bl0KMVN+xtCQEvT+iRyEajxG49DkybDwfmgWqzm1aiYX4Z9CY\nBKllxIkZiz1cAZvMkU1bX0d8OsCPluM5Nw9n0bXy6JXWXoMMZbZirYkwSGDW\nKZ+uSPSYeSLibNps8YC9Kj5e7q2fxEH2eNik9/g4OpmER23o8L9hDMfOV1zm\nxO5vRZdQH+GN94qF2POxHN2cFhENprNCRJKaeOpV7yR0T++3nYZrbX2iGL9T\nZA1LkhXXrg+IDLSD5bsewD+mQAcqeJsOx0wLAJ856tCNP3iX4q6BXpXfAUXf\nlmzvAh2T4vcegPtXPSQHJWTZbXnRKEFYnuPI0W4xdyhYtner8qERcoUShgyE\nVSXPqO2IbHn4oqo9Ed9pKTPwmbp1Z3qUJEpVQBsNcau5cqb3TGtIggwD0cdK\n7XP3HQI2n1NAH9tMU4DjX174eInf//UvXmNj1BrreObN63aWOigWlqNpAlDP\nbM6Z\r\n=HMpM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBu4xKhDbAF8rp5mSZ+L9APvg80QYdZNgp5sWKR+4/5iAiEAsm5iM1kL4asxQmAZqka+3LAg/HBXGDK/s5r/4AFDvHI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_4.0.0_1535746146948_0.29666265844247475"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "wrap-ansi", "version": "5.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "devDependencies": {"ava": "^1.2.1", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^13.3.0", "xo": "^0.24.0"}, "gitHead": "bf27a9b1ee28cfe21d148942850028cda848f14d", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@5.0.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3ThemJUfTTju0SKG2gjGExzGRHxT5l/KEM5sff3TQReaVWe/bFTiF1GEr8DKr/j0LxGt8qPzx0yhd2RLyqgy2Q==", "shasum": "c3838a85fbac6a647558ca97024d41d7631721dc", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-5.0.0.tgz", "fileCount": 4, "unpackedSize": 8527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJce3ZrCRA9TVsSAnZWagAAbwkP/0TOh8VZqeCjrd7r5GDq\ngG01Xzx0CySMJNeCyOl8LYlwsAMHvs6gEpZDsAarTYifZGPK8Kf8boF6OifZ\npPShtdYvTfyaet5sqH64a7O60DEtFX6Nu2kXHEWYUEfUZK6jmB4nTM6tZfIF\nGDTbWU7Am7U3I4iIwfvCd0OqVILmAHobiX8e8JcjMUKf/5ZIdtBuaPgGCe7z\nzn6dbQ+MICYwN7RxOdOC5o4t1bJ0VNIAByrCa1z7lI9MVDdOzsW03QNRF9YB\njAotq1bzuvrCxW6ncyVo/khy0GqUuAD7EmrdqlpH+T7Egg5lK9rPRMkqaU8L\noqtnbni4qrD4D0xV6LelGkuOwqxVL6bJSIJ5/8WXIzV2uAvKGAaOnIRmZa1f\np1Gno9HEvR8CvROXX40oW0kn2E+Te+48ZF1k+b9eFKIZSC1YXMLJVj2OPv1G\nQ2Ouz71p59SizwPxy7Z1hgQBgthMxcNsmrD9seb57ReQmr8piAOkM26QKkah\nQJGY2koTxRnasqGoo8xti4TLS4/njgU/P9hNMqkSzKbLQiwKzRBTVYZNfJCW\nz2oYkPtklqXWpkbb2iyw2z4sMw9mG77segHN6M9fz50EebrCKTjjWwAtJ8Tu\nKV15E7p292CdmOtvQ89ZN5TR/ctDRPI+lPJ2LHaOCU+jJnTFt5+/CqaJDTvC\n/jS/\r\n=Koio\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCB2dQoiatakgwEOoMYhOzjK1N4wB7zXm8S3Jk3o3m53gIhAKg1qmCvGhJBPEIV6jM4THbkVG55HeLQaz5escs1eHEZ"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_5.0.0_1551595114817_0.6160302313338133"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "wrap-ansi", "version": "5.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "devDependencies": {"ava": "^1.2.1", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^13.3.0", "xo": "^0.24.0"}, "gitHead": "2a1a55446d67c55a29e84173e99eb6abc91c937c", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@5.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QC1/iN/2/RPVJ5jYK8BGttj5z83LmSKmvbvrXPNCLZSEb32KKVDJDl/MOt2N01qU2H/FkzEa9PKto1BqDjtd7Q==", "shasum": "1fd1f67235d5b6d0fee781056001bfb694c03b09", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "fileCount": 4, "unpackedSize": 9624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco3NgCRA9TVsSAnZWagAANkMP/1dLcbFDrPlZw4UNK/ew\n7jqKwOmMkxslxJNQSXTWAFuQhjSyL4Z14ht32rE4oAmjCiWYvVl8CS7N4XFw\nqq6ziZ/fXmu3CgRAuTN/RUA1LAgF7Z0aPOdWpKUHwLoN4//oR3SjmVhQIuIE\neEqwF+J1sywFS+Lme9OGSixEHgXzfIlMW6LwPinesp5b0HsbP2xN+WNi/zrW\n388wosnle1fxgG+NIgyziiE6YHJ/NCjBwKWpD//ruM1g3ozPv3neRKCpqX3I\n0VxdFsuNF1YWrMj8VDsuYWf6056HBfulMLnhUz6cN/azBnaT6V3h8BKk+aqf\no350frloEPQjG7g6RnGnVp2i/8fED0TqughaNV7h0BEXpFJJRs6N2PToh58v\nFhaUnNJ25qpWHLS1+czhCTx/xSf42ZylZ63r7NyK+6w5v1LvtvPo+QrxieFr\nKRLHe2Kr40KyV0fTbXGjrdZBrDGfN013JuTALRKh0C00sdWx213ECpWi2tw2\nlIEn6e7WALtKVqUeiQXCU8C3w/5HexgjYX41Klbdt/N5leiE3gXGxDHonO/8\nppqp8m5777IZQgfLQXd0Ph44a8w083/5a6nNE/1C9lbjMPRnoLiueMO3YLeY\nnzdgk95Wz9rsd2XKXcb3NQcbe/oUAMx7S8X7HcG6cIcEdWWcZNmp0hHgYeRn\nVBQ9\r\n=YpBI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmX1BMJJK5wdK8FbfJGKaUBhtoIbDa7U3bxYUgL+9sxwIhAJkvk/DIhdExkqEdxetbvJpbQ3xKp3tX9RDjW3Z/cLD0"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_5.1.0_1554215774805_0.4805495385509633"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "wrap-ansi", "version": "6.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^5.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^14.1.1", "xo": "^0.24.0"}, "gitHead": "7bcd854fb5ca90e5e8c4963a1398d46a306a4a66", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@6.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8YwLklVkHe4QNpGFrK6Mxm+BaMY7da6C9GlDED3xs3XwThyJHSbVwg9qC4s1N8tBFcnM1S0s8I390RC6SgGe+g==", "shasum": "47c7b7329e0b8000f5756b0693a861e357e4043e", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.0.0.tgz", "fileCount": 4, "unpackedSize": 9427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/+h7CRA9TVsSAnZWagAAl/kP/Aqy8DXxlN3OZM9Jppbj\nXRHFNXUHANlhzAE+q1RyOADwAsEwFP+3Qyp1vkyLxBBE8AZsbscsb1S26ox5\nf01AjmNZkk14Ylu3jQOl8X+IBEXulRXAgCAnJlmIlzIrevAIN6FmtLXfG2oZ\n2CRFz3V/GEPFI+QU3eUOd+TSjRtuIKL6dUS0W5pYOZzubrRaXgSUMIYrtQV+\nlt/6KBrT8pikRuXPztk+QwbbIyAhFBz2CVE4el12qApohWx9Ox+W80eRCIGN\n6VAJdtwYIxIJ4cU8cbjDj1zKtoxUk8aqV3/+6UXdnpLOxPKmTdN2ocW/E3ZN\nYgT2FNngQ9sR7wxZKVlg85LSU2H1h5OvzTaaoPWIJ19x20MoSGHNR06dBPwd\nMxn54+vt8yDCWtEnsrnDT+7PcLVmQD4+M5AMNPyj06gZVlkkjQKfxc2eWBvH\nHufvAP3QFlTbpmSH0m9IEmik+X/Omuv9FojKrwl535XeGZLGMvLC3/wIROrZ\nH9fUX42GQX2vPR2kLxoP2XEEhzn404+7HoLsfjSoByXbMcHu+xbwBMMwycTw\nZJx8cuzuqOC34Lctt8cfxdMLmngEEyu7zzcwOQPp/CzrRqdoIyDdNieVcTC1\nb9XgNGcLpIjTdxJNzsHZEUXPUvKxRzP1hFuZ6UqWqeK3DUDnpwr0AxmjNu22\nA9kO\r\n=ZvPj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAyLMEYpyAT3mTt6L7BCfypHOZndX9hEQHIxusyMt9uvAiEApiLYvtyLxFjeqej/zeixUBiPIw+ypjKK7ja95+lFWHg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_6.0.0_1560275066854_0.46827515320625523"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "wrap-ansi", "version": "6.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^5.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^14.1.1", "xo": "^0.24.0"}, "gitHead": "9dec39ed35793eed0786c7742bd20ec870252ac0", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@6.1.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y8j9eJaotnWgJkysmwld5GkLH2KE9srRvqQE2bu1tZb0O9Qgk1mLyz4Q4KIWyjZAi2+6NRqkM/A580IsUseDdw==", "shasum": "36981960d42ba7352db05bc607c563e130857ff9", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.1.0.tgz", "fileCount": 4, "unpackedSize": 9500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsAcbCRA9TVsSAnZWagAAGqcP/33dpQnHlOYJPi2KVva5\nkI+wB2a6a6cXbIqeh9gi6fUogCYRCLU7PJ9YRI/aWBNq56papdgwYDpB7eJy\n/yam58gszr5LvPGx0U1Ldkta3T61caAQq0lFxEa71SyvEeLpk3q7CoJ4MYZY\nPkY/inFZftppE/lqD2XnHhameQHT2IzRQ61D3tf3gNa0eX15C/zP42IZ1/t8\nWADN+xXE7Dv0KbZ/hFWd8u9ouCj+iHeM1BxXb2PSw4lAcU49YJkyg3QfWnkM\nuvFq8PIJhybqaLTnHMOMgq61L24qQ9xS6jwxM4ReCUKpKlI3ApVF68zjq7Qc\n/CC8QfTzqULEBemyTClcQMuNNr4GLHBUildLMZG8BP4K1gzNdJcwHo2LRnjP\nG0DqdFetr9T4qjutOqomaGnZ0V691gGfRx68M7CYs/uS1Y6G3GJIH5unGmJA\niRVAjRXSInNgI3FYNgX3BkXb8FJqtse5GavzH8V/2ydhQ5wTvw+4nwYuGsEK\nIhLTypY54lI+CE4tZ4NVX8m9gJDUPpVleXEXvHczUmGGQFenkiGN4XLrnjqP\n+axls4syZziT/dQDsi0dtQMbN3f78fSLUHdjEkID8mAyCT+L0YTPOHJErq2c\nh2zjOTROXyDWa2Ah/gvqVMuNHNiy3rAqd5p751osPl3EWc0nPwRBm13q+YHm\nOZKS\r\n=R6Ia\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0w7CbBOJ/C3xoM+9PcA/3m30ZkHTEEIalvopnB9r5kAiAvAWxupBKxCFmUPygVcNL95XmNFvHbkEUhoDIe+lZ5Hg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_6.1.0_1571817242959_0.603784195404024"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "wrap-ansi", "version": "6.2.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^14.1.1", "xo": "^0.24.0"}, "gitHead": "a28eb7d6cdbf91bccb56d04d095ca9463c15d3db", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@6.2.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "shasum": "e9393ba07102e6c91a3b221478f0257cd2856e53", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "fileCount": 4, "unpackedSize": 9500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyYdxCRA9TVsSAnZWagAA2dsQAIzMpppxZ5UnooIOBZqe\nHRhqc79iACxFpe+EFGO2Ft3Zba0Ccte4oQzRPUQ+r3iA/o0nDgLvaHMaSnTw\nK073n6Ak7y50s3jyhlEJIRUm9paQlrgc70odMBbU3jq1NshGK9g4ERdp1sfj\n8EohNgBs87ZTUc+P6jJ7KcqAswKxUwDmfB0G+0jPw+bko/aDJBv5Yv7bwHby\nfDSC1ONZySASjq/vf24TeqE44cvk7nH2A3b57x85ha5htcWUM+LdjDZg+QQe\nwvN/udVaKVZTQMwT1IIzf5SE8FZ3OEyCj6WnksQhHHOHmCWC70QSqQqh6eye\nWm8QZ+ZZh+s1d88Zf3iZvhyK0R16SNHznfILad19m9c89EEV6RsfFG4xnurs\nsEr3goedqs2ZDL057fLrtMnHf8ZXUXfuP5eI5TAaO6OtEJ3rEDNau9IlK5Nd\nXnxxJNHaGyDi9kPZAWz7ESyKu6uDiMlFSDn3mS10FPU5hRRkkHvaDEQYTEp3\nJk6QNOk/AlNubQVE/BzwBPLwgEhsDxUvEuUnyTGpmtPz6FPmBuhZJJ0CZIPB\nG1cZ7Bt/+WZkDRKzvqld81tJm4rBo68B+DmhJCsgDD+9Zkax+j6vJeDqt++y\nGJdaTsk/GvSwSHIW5dUi12utdn4Jy6RgPyJIxDb8TcFVI2Dm+JPuAiNJ0cQn\nBt9z\r\n=hCBi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBo3DJI+PdNrF7RgmAf56UxJWAD2qFlXFjEPQ0O5V/6wAiBP8UrlLK7UcyawI9lH3EiEvMkPVSAEMlg5XUyR0X4aBw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_6.2.0_1573488497133_0.0722102188481688"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "wrap-ansi", "version": "7.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^4.0.0", "coveralls": "^3.0.3", "has-ansi": "^4.0.0", "nyc": "^15.0.1", "xo": "^0.29.1"}, "gitHead": "c81bf7d6d0a9c1a49f7ce6413fbc8fbcf9938330", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@7.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "shasum": "67e145cff510a6a6984bdf1152911d69d2eb9e43", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "fileCount": 4, "unpackedSize": 10648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoHaECRA9TVsSAnZWagAAK24P/jwyeF34W9cqIv/B3ksz\n92WOZxBeOfH13jbFs4Ha3FhMDAtvLeTJeomi6vcxSMEm2QwClv4xWQB8zHom\nqOXcdDIZ0vOXXZIEtkdpRsYu+jYh5sH/Wqp7CZMtYbMuAPZb2JmmbW+8YL5H\nSOX9I1J8VUGZ0Nsz12tEYbWDaeeK1ALa6qp88TfiUtOGr8T+GhI1kIH9zSEq\nper8hpXgbaX14k+sAey2iVY4zZmfPQbKEbcpPUKLX3QHVYuRVWcI/eFgET9C\nyA39dJDetiFDdPrSKps8/5YA+jNbYWyip+ljT8/Em2PF5JJlOuAii+42EKyM\nUwxii3cKKQ4Q5Zuh5j2RnFutBOFdCLOQfYZAoJEeuxd2UQPZrTEOGD5mXbng\n7g3wf33Syyr+SVbHnRCI8NNrRu1agm4m/uIpugTuvPo1cg4I+hNshRGX1+1D\n5DJKE9KSpz5M67B6mZfsJfLC5ksGtrNBDFvjNAoqo69oSNaGfFJZNC0iXHE6\nIYkRDb/aN4w6JmnaO6vFIx4+5HozLd3iDLKzlDYmTloJNjN1r9kcdMnQUoAG\n5IGC8lGYuTQYyhemyJ92e+OaZQBXaPxBa64Atvj9psPTFko5m18FOqh0HMbp\nreGEUF3K+5wyUv+1C97ZAHbxJ5ZJ3s/qCeIzzWcxuEiqAAAiDxpPmOj9WXan\nRWNd\r\n=dAFV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKo7j2UdQx54mSbTdaaej2HDd+7W+ESXd3KKVPvJfvJAIhAMJ7QP5IP0+ZduLVTIUpiMK2pHSoyirpMJPR7ttSpH2z"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_7.0.0_1587574403732_0.6362097062222931"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "wrap-ansi", "version": "8.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^6.0.0", "string-width": "^5.0.0", "strip-ansi": "^7.0.0"}, "devDependencies": {"ava": "^3.15.0", "chalk": "^4.1.0", "coveralls": "^3.1.0", "has-ansi": "^5.0.0", "nyc": "^15.1.0", "xo": "^0.38.2"}, "gitHead": "03b1cdf921da59050d38da8681d20f3c39cbb206", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@8.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Z8D7Y4g4l2rDGMwV1NtKjqbhNoXWQK264I38RoO4eDKqOKIeL2HUi/Uf0OG9+4XRlfxZ0OPGL4c2DTrnV+G/RQ==", "shasum": "e88582542110439a7afe1a85315a1a6abd230ec3", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.0.0.tgz", "fileCount": 4, "unpackedSize": 10420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeT90CRA9TVsSAnZWagAAu5cP/RG92AzX1704nVFnjsjn\nOAyysWY9Of+wiWYouI+4mxUbot42pkHg2ZKZLo2cM/ZHA4GWJrBkuL7x4iii\n7A7lJ7WojA/GKBnMKyJQiBzqX7GkzNYiqNk0RruYIDYNjJio4uIjyZyoL4FG\nL6/K/r7SqNRI8NJsehOUtpzcEmmnJqwqfPFmzM1n391+jeUrW/KXV5Dn4amq\n52Y0MCN12esaByPtVrpgjG57KmSg6DZbk1LGaqs50e2/kWhQmglbdbQPMMhC\n22XZh5IMwADQG5ZCNus0ux+XPRTYKtiO09aypy+dgvyGhdkBPwntIAlgPGPg\niz7lt36zpiIpXjU/TPgTnkQ59Gc7iO7OWZ18JcLb1Abm1NS5lmP9HTpyEwbY\nIDT1z6ZlJTBkPmhYFYm27svgZ6c+/oPaL7t8wt0KvD466gcV1tQ4D8NCSG2n\na4svqREO1GJ1trsNXw6926LMbvp/2MKk+EfzJwhW0lUsgo+L2EM1KBU6g3GR\nrptuSPXNf6JIcVVj+Ff55Lmh0ZvtXFnh70FsTuUHE75OgMf7QAFpiRFl8rNR\nNOemKoUtAqRfjanaFNqEAVHyrz/EcLchKVV7wQsdGAiBmSMylJU8+85kx9sR\nPQNPdrlzI8rgrggXOg9Z8TbzMisx0jB/ZQnRO2cLLhn20YaWHHJLN/0vkfvg\n5rz+\r\n=18PU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPRRhVgvL4otkCQKUckS3PZB7k11QRnNCv6qSbA3ikjQIhAJn/cUatdy3SM+qfie5HE2OtIwmEiLbmhR0sydsATMHH"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_8.0.0_1618558835950_0.3500820492029739"}, "_hasShrinkwrap": false}, "8.0.1": {"name": "wrap-ansi", "version": "8.0.1", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "devDependencies": {"ava": "^3.15.0", "chalk": "^4.1.2", "coveralls": "^3.1.1", "has-ansi": "^5.0.1", "nyc": "^15.1.0", "xo": "^0.44.0"}, "gitHead": "99ada39978a880d8e0c060b6a972b552fddbb380", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@8.0.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-QFF+ufAqhoYHvoHdajT/Po7KoXVBPXS2bgjIam5isfWJPfIOnQZ50JtUiVvCv/sjgacf3yRrt2ZKUZ/V4itN4g==", "shasum": "2101e861777fec527d0ea90c57c6b03aac56a5b3", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.0.1.tgz", "fileCount": 4, "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP5XdCRA9TVsSAnZWagAALUUP/RuULtr0v0iIk2aD9HPa\nJUlxyBnp8V4S6vUe4mZv9HiLDHQigbWN0T//RwQGag5sqpB6a3JoC62Ng/ox\nnjQD6i0rMpwEe80Fa9Q1sG8PcHFtOqhz/4z8kAPkHwoinUHIqBL3VOiTEhQf\nr8hvb+1Xj+qR3TxZU5+8MYKklyOgDPk7PHcMg7Lrd36zpmghsEQP8fdxtBOA\neI74GOjOAIp1W8x8vaUAgsjzyYR3I7HwvVXgTk2S+LLyv0LVmME7LEtTZ2Sh\nBKraI9frXb2BxytVOjLJRx9MUKKGp4C8K3ABbYqjLLBskxUyQs/aITV8LHFz\nMFQh+EEHNlXug21ayqujTloAUc/WEOhc35OUNEBOgo+1ORJsLpPEQ5urUB/L\nl8gn8hGUpmEJdIkZRTwptgxiKM0q8sdm6+QTPoG4k5gV2bL3EPBfFSxDWTfX\nusZperIFsBM17CEh7vKoGeFxeai16EUvFcTQntbyWpbY234tya3DUNFPloKb\nYwOdwYa6KNFmZCMo3x6zFDCgPgIdIs0Y0/JYAq81hCBgzlSH9hMd+tSWvBvG\nvZy/ylZryPfDzj1Sm1bSza+yu4uPpCLiv1/DBmYvNhAq8zhHQ6Lpd1nxpDKb\n9VwG1vd6g8wpPEGclPcRNURp1t4ETmXynxSTL3ZUA9bs8/veeBveNm5pPLcF\nk6Nk\r\n=AlPj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE/WZj6QschkIWaF+IJ6e0GxQ7JGYmuRAyPTgJN2+r2jAiBzJstRh7paEOhUhFjpMcgA9Gq1k4mFSEtVLXEZoxC1ew=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_8.0.1_1631557085839_0.030283861860827432"}, "_hasShrinkwrap": false}, "8.1.0": {"name": "wrap-ansi", "version": "8.1.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "devDependencies": {"ava": "^3.15.0", "chalk": "^4.1.2", "coveralls": "^3.1.1", "has-ansi": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.25.0", "xo": "^0.44.0"}, "types": "./index.d.ts", "gitHead": "115090266b0ebb1797032582de78d617575778ab", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@8.1.0", "_nodeVersion": "14.21.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==", "shasum": "56dc22368ee570face1b49819975d9b9a5ead214", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "fileCount": 5, "unpackedSize": 11777, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTLHPWZRhU1psYEAvTz2AEDNsMLsR+G9tplhjk3/R3BAIgNOJvHTwK2n7NB5EqKVWO2XlPckE0YuQWw6rMaitomrA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzl3EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO/A//bYXkxwhhxDVoigdWOaNHiKW+majk5idpXfyGE5PoWIgsqFut\r\nP0aBrryVq2ghKQeknXKuOvpqP2Hp0sJSM6MoLDye3gfk+HNlyJzVdHalk6N8\r\n0DifL08xKkdsbtG0+anIaaI1PVoyL5S8I5FkLZrl+/SKenE5FnlT91rdJi2P\r\n+LiGCRYmA/t+8NbnCvy898l0ZOzQlfM9tdni7V/Sbn8Qgr2lUXJTEqhausHI\r\nIYBTWyW5MWIh4/pfVfFHDDMcdXBKvQ4VRhZC+aPQUjVCGPvW5CWZnP3OPm8a\r\nNfJIO35tUkGN002iswqVdTexuRp5nioJoqRmk2FLC5dnuiKi6Nb0BMB3yy+L\r\nJJXOY+0jF0G3JFFLm82UTCEVFX79uYflr+qaOyRI2GvJzWL9Xjva4VbKbBs5\r\n9o2NoZAbc4sgMlFvUU+xKav3rH+76mQJeJdL+eCKwZkeQomOnrhxrLv5Kc4j\r\nEN1g/vcLEJG/A8u6yWXpxl+/vZYWv6KeNnGhwLF5ffDY0ox1yOb2kHmH73bN\r\nmgavI+Qq7CP3bZFfTM5uJxk9L7ZocdlziHdRE+WllVG3199KvO63RKohUtNb\r\nvJs9XFEHlevDhHne+D0R7oRhN2j9ivImAyrz49hqE7o2UTldVv3qgFY0boeT\r\niPTeVHWKSmTLDzMTbyHNWObQWegh6j8+g9I=\r\n=Y4Bg\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_8.1.0_1674468804702_0.04919865508752208"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "wrap-ansi", "version": "9.0.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "funding": "https://github.com/chalk/wrap-ansi?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "devDependencies": {"ava": "^5.3.1", "chalk": "^5.3.0", "coveralls": "^3.1.1", "has-ansi": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}, "types": "./index.d.ts", "gitHead": "163b878a6eb5d8c32b7bbea65036eeadc0e4def9", "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "_id": "wrap-ansi@9.0.0", "_nodeVersion": "18.18.2", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==", "shasum": "1a3dc8b70d85eeb8398ddfb1e4a02cd186e58b3e", "tarball": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-9.0.0.tgz", "fileCount": 5, "unpackedSize": 11484, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF6qX6Zqmx66n8hHYBuoiZF+0YDFzvKIhYo+u9CqcJ7fAiBvt/DgWeV0JcFeXq2BYsC6C5i6gxMltw8Z8rwdQhKRyA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrap-ansi_9.0.0_1698514834127_0.8623080519517654"}, "_hasShrinkwrap": false}}, "readme": "# wrap-ansi\n\n> Wordwrap a string with [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code#Colors_and_Styles)\n\n## Install\n\n```sh\nnpm install wrap-ansi\n```\n\n## Usage\n\n```js\nimport chalk from 'chalk';\nimport wrapAnsi from 'wrap-ansi';\n\nconst input = 'The quick brown ' + chalk.red('fox jumped over ') +\n\t'the lazy ' + chalk.green('dog and then ran away with the unicorn.');\n\nconsole.log(wrapAnsi(input, 20));\n```\n\n<img width=\"331\" src=\"screenshot.png\">\n\n## API\n\n### wrapAnsi(string, columns, options?)\n\nWrap words to the specified column width.\n\n#### string\n\nType: `string`\n\nA string with ANSI escape codes, like one styled by [`chalk`](https://github.com/chalk/chalk).\n\nNewline characters will be normalized to `\\n`.\n\n#### columns\n\nType: `number`\n\nThe number of columns to wrap the text to.\n\n#### options\n\nType: `object`\n\n##### hard\n\nType: `boolean`\\\nDefault: `false`\n\nBy default the wrap is soft, meaning long words may extend past the column width. Setting this to `true` will make it hard wrap at the column width.\n\n##### wordWrap\n\nType: `boolean`\\\nDefault: `true`\n\nBy default, an attempt is made to split words at spaces, ensuring that they don't extend past the configured columns. If wordWrap is `false`, each column will instead be completely filled splitting words as necessary.\n\n##### trim\n\nType: `boolean`\\\nDefault: `true`\n\nWhitespace on all lines is removed by default. Set this option to `false` if you don't want to trim.\n\n## Related\n\n- [slice-ansi](https://github.com/chalk/slice-ansi) - Slice a string with ANSI escape codes\n- [cli-truncate](https://github.com/sindresorhus/cli-truncate) - Truncate a string to a specific width in the terminal\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n- [jsesc](https://github.com/mathiasbynens/jsesc) - Generate ASCII-only output from Unicode strings. Useful for creating test fixtures.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "time": {"modified": "2023-10-28T17:40:34.535Z", "created": "2015-08-19T18:56:22.141Z", "0.1.0": "2015-08-19T18:56:22.141Z", "0.2.0": "2015-08-28T07:07:49.929Z", "0.3.0": "2015-10-10T14:31:14.973Z", "1.0.0": "2015-10-13T05:08:13.341Z", "2.0.0": "2015-12-01T17:27:23.410Z", "2.1.0-candidate": "2016-11-04T06:34:16.631Z", "2.1.0": "2016-11-29T17:21:24.416Z", "3.0.0": "2017-07-23T11:27:48.128Z", "3.0.1": "2017-07-23T19:58:53.494Z", "4.0.0": "2018-08-31T20:09:07.083Z", "5.0.0": "2019-03-03T06:38:34.939Z", "5.1.0": "2019-04-02T14:36:15.008Z", "6.0.0": "2019-06-11T17:44:27.001Z", "6.1.0": "2019-10-23T07:54:03.081Z", "6.2.0": "2019-11-11T16:08:17.322Z", "7.0.0": "2020-04-22T16:53:23.889Z", "8.0.0": "2021-04-16T07:40:36.092Z", "8.0.1": "2021-09-13T18:18:05.982Z", "8.1.0": "2023-01-23T10:13:24.890Z", "9.0.0": "2023-10-28T17:40:34.341Z"}, "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"scottfreecode": true, "penglu": true, "michaelyurin": true, "flumpus-dev": true}}