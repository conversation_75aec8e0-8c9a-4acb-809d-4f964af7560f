{"_id": "strip-indent", "_rev": "21-9521074c27ab6ddd6f775648262ec070", "name": "strip-indent", "description": "Strip leading whitespace from each line in a string", "dist-tags": {"latest": "4.0.0"}, "versions": {"0.1.0": {"name": "strip-indent", "version": "0.1.0", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-indent"}, "bin": {"strip-indent": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "a=$npm_package_name; browserify -r ./index:$a -s $a index.js -o browser.js"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@0.1.0", "dist": {"shasum": "80b081845da5d3c0adc0d89e3b10199f8c6cff37", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-0.1.0.tgz", "integrity": "sha512-obewanenb818u9ruQkEaN69MwAEZqSnP7c0OFQcAjPcniDBDDgxWVA1ZYmIGgYHI2cbeL4NhIkQ/1//+pQ5e3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpLZDQbk7ycMznO/WiBoZcQtbmV4WSo+xMkPf41DZWgAIhAMHNIJNsU6+F1WNAgZ9f+e0JVQbCu9hgexH9VyfaWZiT"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "strip-indent", "version": "0.1.1", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-indent"}, "bin": {"strip-indent": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "a=$npm_package_name; browserify -r ./index:$a -s $a index.js -o browser.js"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@0.1.1", "dist": {"shasum": "634e8410a63e7ec2f90845a768110d70ad925fdf", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-0.1.1.tgz", "integrity": "sha512-x+/BsoaTyIODMBnQgQeyLkBDvBRDHtjJCYwuUcEv+7qcYEplQqK1yP82Tus4EoCPlcmEw5Bslce0GBRQRPtJAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGM2c1ZWcA9oOnPA//JiwHaR5cGpOEr9T8aHWKCsUjFFAiEAuH/TpuulUAUVibKqFcRXQf/urjFu59Ymq1z5ybJ6W2o="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "strip-indent", "version": "0.1.2", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-indent"}, "bin": {"strip-indent": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@0.1.2", "dist": {"shasum": "b48df2fc0b4ca737d5b3329d5ed2b1ba26a420a6", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-0.1.2.tgz", "integrity": "sha512-pTUSXtMaP/RsXmtw9nvs0MQd/RSkYoWK5ifEUOlzJ/dC1hxXk4KECCcqLlk4MfsiynK2VNmOyLYuL8xTWmVygA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID1Xvep4x3+5/gRQqKZVdtrPTNVgI0tBA/tRtrLOb1bnAiEAni/QIytqCqj0h1lIUDQUFTSu+mdRM3uBnDwt7RXR08g="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "strip-indent", "version": "0.1.3", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-indent"}, "bin": {"strip-indent": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "browser": "browserify -s $npm_package_name -o browser.js ."}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*", "browserify": "^3.0.0"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@0.1.3", "dist": {"shasum": "c43ccb66c24e49c4e27b2236d34fe2c4bbcf14dc", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-0.1.3.tgz", "integrity": "sha512-jX5TqOvZa+bSS8JeIvWXODBA7SH0PdpkbPOgt/EpPm0P1bdCPHpCnb27LweZWVT8tXFiKSQexb/Lg7xB7v1hTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC19tI9H9wR7qTNaH1Carzf9kPT9MABYtarMmiWg2+8SQIgaDqBUjwZkT1ubR03fDNv13wMxJZMk8s23mcVzUc9AIg="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "strip-indent", "version": "1.0.0", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/strip-indent"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-indent": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*"}, "dependencies": {"get-stdin": "^1.0.0"}, "gitHead": "0b11b5a23a7dc61b6363ef61939115a17f509167", "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@1.0.0", "_shasum": "c2425adedb2163f01ffd4ba5fdae1c9f80900285", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c2425adedb2163f01ffd4ba5fdae1c9f80900285", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.0.tgz", "integrity": "sha512-SttW4BE+TrOi0dabpeDDiD+puKiojf88scUXPO5Sg6WRe9LjEqvNkfj4fDF045aUHs49FERCD9smRJEBc3M1FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLLzugepafeJ89Rfbb4cEYRKfkWR89ZmAA7K0e8inL5wIhAMA5ghrwSeMCtal/WKJ6aIJ9x4VkYOSMofh+SYc7s3or"}]}, "directories": {}}, "1.0.1": {"name": "strip-indent", "version": "1.0.1", "description": "Strip leading whitespace from every line in a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/strip-indent"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"strip-indent": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "browser", "strip", "normalize", "remove", "indent", "indentation", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"mocha": "*"}, "dependencies": {"get-stdin": "^4.0.1"}, "gitHead": "addcf90a56001ea122e9f1254987016bc87e5b5f", "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent", "_id": "strip-indent@1.0.1", "_shasum": "0c7962a6adefa7bbd4ac366460a638552ae1a0a2", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0c7962a6adefa7bbd4ac366460a638552ae1a0a2", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "integrity": "sha512-I5iQq6aFMM62fBEAIB/hXzwJD6EEZ0xEGCX2t7oXqaKPIRgt4WruAQ285BISgdkP+HLGWyeGmNJcpIwFeRYRUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB33OHzuhUL2z2uNoKRfZDpiVbPwRKdzhsXoK0Voa+KzAiAv4hdVWB6DoIajXGVw1wZJTEAto/7gpCbdGvLMeyqzxg=="}]}, "directories": {}}, "2.0.0": {"name": "strip-indent", "version": "2.0.0", "description": "Strip leading whitespace from each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string", "str"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "459f98737dd57252ea63268c4686b939d40579ae", "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent#readme", "_id": "strip-indent@2.0.0", "_shasum": "5ef8db295d01e6ed6cbf7aab96998d7822527b68", "_from": ".", "_npmVersion": "3.8.7", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5ef8db295d01e6ed6cbf7aab96998d7822527b68", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-2.0.0.tgz", "integrity": "sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA2o8Ezf+0ipgh25D6gS8eLfC2F4vp3Gi00LWb4q8zoGAiAS/DopAoyUytwhKX4BSP0p3t71EDr2YTcHub//S1iQ6A=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/strip-indent-2.0.0.tgz_1461304042785_0.2992539571132511"}, "directories": {}}, "3.0.0": {"name": "strip-indent", "version": "3.0.0", "description": "Strip leading whitespace from each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string"], "dependencies": {"min-indent": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "b537c94fd8376e4205146fbbef0bc08a477c5eab", "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent#readme", "_id": "strip-indent@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==", "shasum": "c32e1cee940b6b3432c771bc2c54bcce73cd3001", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcto2+CRA9TVsSAnZWagAAX+wP/jptz4I9KxolBNAiwBjm\nFNeEjgqLO8ymtmDemE27QASIsRef2xriXz2f4i4tIF+QrjNp8WdHG7b8j3lq\n75uKMa++uDOoE0PSB/s2cdKE85fyaE+O6RPXmsFVrzyaBIYGA/jnhRiTcOy8\npgEpIGSe5jdV4vxhVGPbIpKvrTqs5b38ybMWelyIWsuMnM+XFAjm3dYupquB\nLS+rcMKpRoHlN2F7x0D0saz9H0On3tLcqSs7g158wlT5bvix7SWh6G+9wKWe\nOEZ68EabA6PjzoKc6nATlmNHNY3XqoBb59Bclsam+0bwFmWKm6N3/iD3hRmO\nZwS2n4siQwpXfr1hz2BaUbs5di6mPKCFJrusLglJyoCX1x06fWpQs6Rqsb1L\n3KPcAV0gf+X51+M/C0gC+31lL+8A8PyJIyfStZlbq3uJFokp8vWIN1k3BQt0\nMiSbi0artblf1l01ZrR8o81k1HYM/2jxur17TPEqe9B9XJ3Hp0XgZS/DYzy4\n7oTGj7gJhXYrNDtDUFtSkUfVbUS35nypRhCAMetZKNNdsgy8MvvZ7XBXwNCO\ny0Kf4auaoVXQT/lB9dIS/2AUYalrkV0pzln3byfZ4E6+T8LfEqS5PjAwJq0w\n2oWpBiybylH7kaD/C6h035dpGRhNeLfGGTVKSivBE6YuAxdguhmSvD735L+O\nXY2R\r\n=+/Gs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+Fg/YElUWTq2L8qX1YXHWJxOpsD29zzUeh/XVexJ7/wIhAPSIhkbkSkWC5MGJK6819eQyhr+UxDxUU135ptltgtWq"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-indent_3.0.0_1555467709598_0.2614391651361543"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "strip-indent", "version": "4.0.0", "description": "Strip leading whitespace from each line in a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string"], "dependencies": {"min-indent": "^1.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}, "gitHead": "10584084ff795abfb7b3ae1862d554eaaebb9696", "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "homepage": "https://github.com/sindresorhus/strip-indent#readme", "_id": "strip-indent@4.0.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-mnVSV2l+Zv6BLpSD/8V87CW/y9EmmbYzGCIavsnsI6/nwn26DwffM/yztm30Z/I2DY9wdS3vXVCMnHDgZaVNoA==", "shasum": "b41379433dd06f5eae805e21d631e07ee670d853", "tarball": "https://registry.npmjs.org/strip-indent/-/strip-indent-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkAnxCRA9TVsSAnZWagAARcoP/R7Onp0yJeJD7AXFxmoy\nvVhnzo8LIkhkOWodvKDe+VATudyIOLKOstCZIzC3b6en7iQ9fFQPbotkYO1D\nHRjeo+7O9EEBhvHUemLuYu80FKRarP1pMp4bcrsuqQj2FKM415dolzjzX93g\njqCDNPXPbcN1NctSQch2GMep/6R8tUv+KrJZDhmsGURvg1mVN4LmLkYoC/IV\nBbtjusk8AHmTVv1Kw+mBZnBzkxOPMNSwAQzoWy11wwNPJ6UdnVsDwHy+OASu\nx4w9kCBn18AVLNK2vKVI4eTpSDUIjpFxgvDpOORCPRIUdYLpame0+NC7xXpp\n5gD5GDWW8guOOxevMo6O4ShH8FJq4k6LkoLYJe7Gyzaedv5urpo/9WSXtFvV\nW/j2vkY1Din3nwr8qK/9071fUUykqLfGOkkzpk17eIhpvFzxuQSiL2eAUQ5i\nfp335wP2dZNrgNq/uOlg291oqiDCD10CwT7FOI1hVNyAU6dJCE/YqXVIeFu+\ngPHH7dOM7M+TTmDH4P2HfuMq0IUb/jCcJ+ftnTTM3wR6l19zf98JLw97SDJG\nmTHqmatrqSxpUZ8e1UOnMOx2nTAe+FdzHsiE7uz97IZIwzdZs40a8KIGAkpF\n7wM4dFO0zwCrGNyyuGAI1qRnSqudUK+ToJRa59cAnEjPA180C/HmrOOX6MA0\nb7h2\r\n=fZ45\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyYGEFsjKxEMDejjeNKdWPIlP8KgnjqDYiJFHHpuV1hQIhAKF4oBrvF4KgNRY9OaXLLun49dnRkAmxadAe3mA/bibW"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/strip-indent_4.0.0_1620052464737_0.051673866153164605"}, "_hasShrinkwrap": false}}, "readme": "# strip-indent\n\n> Strip leading whitespace from each line in a string\n\nThe line with the least number of leading whitespace, ignoring empty lines, determines the number to remove.\n\nUseful for removing redundant indentation.\n\n## Install\n\n```\n$ npm install strip-indent\n```\n\n## Usage\n\n```js\nimport stripIndent from 'strip-indent';\n\nconst string = '\\tunicorn\\n\\t\\tcake';\n/*\n\tunicorn\n\t\tcake\n*/\n\nstripIndent(string);\n/*\nunicorn\n\tcake\n*/\n```\n\n## Related\n\n- [strip-indent-cli](https://github.com/sindresorhus/strip-indent-cli) - CLI for this module\n- [indent-string](https://github.com/sindresorhus/indent-string) - Indent each line in a string\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-strip-indent?utm_source=npm-strip-indent&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-16T22:40:53.623Z", "created": "2014-03-29T14:17:34.079Z", "0.1.0": "2014-03-29T14:17:34.079Z", "0.1.1": "2014-04-06T22:49:58.455Z", "0.1.2": "2014-04-16T14:26:44.956Z", "0.1.3": "2014-04-29T19:53:29.543Z", "1.0.0": "2014-08-13T13:43:55.144Z", "1.0.1": "2015-02-16T18:02:14.906Z", "2.0.0": "2016-04-22T05:47:23.270Z", "3.0.0": "2019-04-17T02:21:49.701Z", "4.0.0": "2021-05-03T14:34:25.101Z"}, "homepage": "https://github.com/sindresorhus/strip-indent#readme", "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-indent.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-indent/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"mlm": true, "flumpus-dev": true}}