{"_id": "@babel/plugin-transform-nullish-coalescing-operator", "_rev": "35-54739c4a44106b7b268389a4eeee736f", "name": "@babel/plugin-transform-nullish-coalescing-operator", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "c24d022ca5dbdf9062c95a64cbe07a1258992f98", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-KU2Or7uQqYKcL6rVLh8jThUBAKy1H+mxPx4E1omUqdSL+hVM9NriMjGFnnv+9xSn3jUMV5FQHsLQxgGLr/MWTw==", "signatures": [{"sig": "MEUCIGTlRV6EBr250VY2t8W2TbKqOw82DfWerj2j/JCANJxrAiEAgpXlH9Ev6N3kc7cmYTzqR8jEiApgw07+M0rUde+29nI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.22.0_1685108715792_0.8127203500387181", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "8c519f8bf5af94a9ca6f65cf422a9d3396e542b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-CpaoNp16nX7ROtLONNuCyenYdY/l7ZsR6aoVa7rW7nMWisoNoQNIH5Iay/4LDyRjKMuElMqXiBoOQCDLTMGZiw==", "signatures": [{"sig": "MEQCIDicd9nWivA6kBP3Fmopsl102k39pLRtLvtCp+UUmEJmAiBKrsk4b7YI2jUiV/y9gI9xNILmG+GbCviNgmJzg9b66A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.22.3_1685182257366_0.44733168031644177", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "f8872c65776e0b552e0849d7596cddd416c3e381", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-6CF8g6z1dNYZ/VXok5uYkkBBICHZPiGEl7oDnAx2Mt1hlHVHOSIKWJaXHjQJA5VB43KZnXZDIexMchY4y2PGdA==", "signatures": [{"sig": "MEUCIQDpVAM2xnAF5z6m0cpDxT3hv+W9gAdDMIsqxCK+wm7oMQIgeZyQATKMGVBANyOIOmCI6KG91jJJhZB6jPUf9us2IkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.22.5_1686248478146_0.9932712195412967", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "7c4689e97019a17903a4e502f84214362294d62b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-GsUJzGNlJ36KvnvFyMNNrsS69AqU73nKulSpxBb9Tc5yghIhwVoqWkeJHA+xe/QUIB/fD54LFyqTHXq8KbGYag==", "signatures": [{"sig": "MEUCIQDDh0R0MwN0GK5WkmDZAd5avaD9EKFLhkQy0kHA0ZW7XgIgTtFrbokA+cWe5CSIqA6hZEFHxj9umKKqdYMo+L1Rgjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8856}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.0_1689861592769_0.04327941289185899", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "6bf886adf2359530888bc923a04c4e775a50b8dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-7vGZTJOH/Ptr4H0m1ar47UipQT9ITY0pm43AMe/vqS8t7mixQxhBDGJoBU78CziUh2zI8N7o14ocFI7coGERYw==", "signatures": [{"sig": "MEUCIQCsvLFpsqfGKen8/0CfGiCFGrx9ds/lpA04NMaWjtqGTwIgUEw+HFCx0gc3hUzxYrpTBjyG1WAxkQyPQtmaGtn3Xl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8856}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.1_1690221112622_0.27185818072896306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "9b972974137e5a232addc8cec01d89aff3a78815", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-k2h2H0s9HjYYe+mENFzEEnUc4sqaTXnvvk3X6uotiQolhAsvv/MEro8cN1DFmhj1B2J7lxzBsrJFIKo4M7LtgQ==", "signatures": [{"sig": "MEUCIQDEIRk2dV4ubwm3DVHUQn1Z8kq948t6KPpjB/7ymiSZbwIgf35eylCVWj3XkLOpGxwqTP3fAOlAfYxPVoPiI2NE2ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8650}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.2_1691594092546_0.3264369378131209", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "debef6c8ba795f5ac67cd861a81b744c5d38d9fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-YZWOw4HxXrotb5xsjMJUDlLgcDXSfO9eCmdl1bgW4+/lAGdkjaEvOnQ4p5WKKdUgSzO39dgPl0pTnfxm0OAXcg==", "signatures": [{"sig": "MEQCIERbYvde/sDqV8nxWW/n6HnHIyZbQllkja7/oxYg3NPhAiBawOAZPUX4wcHXTmj/FwaiIfM7/6gozIKwYFaAFayuFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9003}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.22.11_1692882519038_0.7731606334023813", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "e58480654f526fff1c3f605133cc5f6d184ad47d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-rygjon0ZNgtWXrsDQgjclTBpI4/+uxrEdwpq1/jInm2CcKXFLzu/6zlsHDGWAJxieehPqaMVh4pgUuHG28dvNA==", "signatures": [{"sig": "MEQCIHwZNof8SnnvH1C+2mnU0eN5tpIy0QzCSCZ+ibYp+4WUAiAX/Ril5B9MXG5AevKqxvS1TvLS3G5+5+q7duxHpwRO3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8692}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.3_1695740209211_0.618745058693055", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "b5ddfca779f5f732a38d12b677aa9532d55ff31b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-tpngcA2LG2adYRKPhiAjoVAqwRLJ+5cC+KpOvT4MW5QWwlwaTHVmwYyTYSG6VnFnOHicSyZ+LQzVvE32EQpK+A==", "signatures": [{"sig": "MEUCIQC3olBCpIBJi7rg3IWSG/zvRXGP06o/tZNzBZq7tUWohQIgZsigaa2J0Witb7VPOqxlCpPTS0iG+RBShDpRIzZ6heA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8692}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.4_1697076375355_0.6603536317713505", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "8a613d514b521b640344ed7c56afeff52f9413f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-xzg24Lnld4DYIdysyf07zJ1P+iIfJpxtVFOzX4g+bsJ3Ng5Le7rXx9KwqKzuyaUeRnt+I1EICwQITqc0E2PmpA==", "signatures": [{"sig": "MEUCIQD6rYxgROHpx+2K5kSM+Pyf5YC8frpEtroJqlXdl7/TZAIgNKRUAbb8HCz8pZ45Yu5deCA47j4RDzelPl675KIucqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9083}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.23.3_1699513433035_0.9852003647153502", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "45556aad123fc6e52189ea749e33ce090637346e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-jHE9EVVqHKAQx+VePv5LLGHjmHSJR76vawFPTdlxR/LVJPfOEGxREQwQfjuZEOPTwG92X3LINSh3M40Rv4zpVA==", "signatures": [{"sig": "MEUCIEpu+oYXj9TMaMiUiq4vSe9tcrsnFRVDhcW8zPc4TDujAiEAvkdw8csxGWltYV/6na4Ko7r0X3HgiXbAgj8efQsjhwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9089}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.23.4_1700490127604_0.6149948730262462", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "0106102988476599619410a4589f7d6b8d46dcff", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-r2RN82vqGfs7AtByqW+xknMrva3Bzd7t8jelnpIUhZ7jTwtVlBC+SbWmQSZHzg2eO8wrZJoLqq1WGrrgtoEnhg==", "signatures": [{"sig": "MEUCIQDjYF3FSyECMlR1vSjyYRnuPI7DQIHFCG+yRABksY3FeAIgSJ88kE9WMz7YtAX8soBlS78+F3WDeRT++LmZ6RrNf5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.5_1702307918887_0.029508279018401584", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "fa3e8a5b2148ef27a87fec38c1c23e152b445b6b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-/S/v+6PyS5WcO6NMEk+PsJfl6Y2ObLlC793DzHXAAMmCZioYKCs0Ge+Zn5Kpj6XK5FiYTL1XkaksDg2XaU9Nzw==", "signatures": [{"sig": "MEYCIQCBj3bPa3bLzJE6hQimtZP1RuYrvqcRgCwHvWMhbfWOIgIhALusMZs9T7wK97vl5co9Tt3j+HN67bVPhHyWk1nm2CP/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.6_1706285639126_0.5593954252972506", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "64deb1a9a8d0fe233d41b4962e77c5974cf5e760", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-NAQmQ0T8eaKPEETU9PF7h5IW9EcaMQSsTRxbUmMXKE6RAt5HBDddkQPeAnsw6+wV9VB8/iSxrBSCCtSuWXoNOw==", "signatures": [{"sig": "MEYCIQCDLBdBNaEjkvnKiZhRNJX/Hcx04oSkecX3d1wrn6nOtAIhAKS4M6CtClSUu4PCcZwWyqIZAMVL8+9vmtnBkmBHkVdt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.7_1709129088924_0.45919765340664487", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "0cd494bb97cb07d428bd651632cb9d4140513988", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-iQ+caew8wRrhCikO5DrUYx0mrmdhkaELgFa+7baMcVuhxIkN7oxt06CZ51D65ugIb1UWRQ8oQe+HXAVM6qHFjw==", "signatures": [{"sig": "MEYCIQCDigYXccx3ZN5t+TkkX9sa82LvdO94iIya306Q6QEQpAIhAPbh+RQMNBAsKaS9UF0Pn1ks3HL2aeB3wALKhv72kxxV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9150}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.24.1_1710841731267_0.7123062222841741", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "5e2d2a0ed3739d34cd0c58b00ee2ef71ac8370ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-gf7uB8dEo9f252GXK33DqED6pW8obTj/bmf2nl04EBA47pUNfgegaJtnzTBeQYaT/dGvKyUOD/TInugXMko78w==", "signatures": [{"sig": "MEQCIGlhEDQa/Y9a3mOE4YXUZ/qw/nGrh07gVfev/V9EgmJyAiB2lAlzpQqR4WHQtd6WJ+v3yVVR4OWbxpGAGEcC0zhn1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8735}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.8_1712236788095_0.3640452774406955", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "12b83b3cdfd1cd2066350e36e4fb912ab194545e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-+QlAiZBMsBK5NqrBWFXCYeXyiU1y7BQ/OYaiPAcQJMomn5Tyg+r5WuVtyEuvTbpV7L25ZSLfE+2E9ywj4FD48A==", "signatures": [{"sig": "MEUCIQDf3/8rqCBhA8K8znQODOmaTVu+WtJR7xBMz+LIj27CfgIgFB4XMQ6JyEbrduTjo8VSa6b1NIInpNbSzHmgWKy/ktY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75134}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.24.6_1716553469572_0.4501190157060688", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "0727a1cee482014e93e913381a06be2ee9577d89", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-uBJ4gOTI5RpwFIgAycPkqvp1CWeBQeHx3ogpOdCdpROGRBuqzqMAYepFJ83L35U0W4fD3mZ5H1BO40OnVV/UCA==", "signatures": [{"sig": "MEQCIFGPzRvW6XnjMAXSIMk9uwj2m+0GsjVA/d0oTt+VaznbAiA3Z/zenFGsXdzZUQ+rcgzM13Xe1PGbJ+NAOWU7/hSgEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75022}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.9_1717423454128_0.02677215985924586", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "9b639f8808b2e9989f95e12a03daa6a6cbbbd16b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-R+SPK/HbT27AriHdcsJoKxZ93w1SnxzhAAEmzx+kggyD/4YOIniP7CpuR0IVPcefgn/4ZLjKSWSakMRoVYcvfQ==", "signatures": [{"sig": "MEUCIQC+YDUQRmeCLbssbtbmd4GlGNvOW8jOmiIXFaaRxuxRngIgN70lfjssuBMc+dNkemEf6Xstvo3ypEqLK26Qma8qZr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75029}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.10_1717500002633_0.4355958316349182", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "1de4534c590af9596f53d67f52a92f12db984120", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-Ts7xQVk1OEocqzm8rHMXHlxvsfZ0cEF2yomUqpKENHWMF4zKk175Y4q8H5knJes6PgYad50uuRmt3UJuhBw8pQ==", "signatures": [{"sig": "MEUCIEk9kx4fqfAZgL2TO+V6WHi1wYgOof0ee6co+k7UgpBLAiEApbweSQyoCc4NesaLNCGP9wbZl3kYX7cEmqs99sq6uK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75130}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.24.7_1717593319658_0.3547736844471763", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "2344b7e6fd3b47a7f960a7370b6a2b734f039b31", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-v01v9yhdevsyFv6+w9f3dlCQy/uRiYkl+z+c333qgi+3PEvqW8ZmCTeqYiE/M06b/Azn2FcJUIhlKrl38ztFTA==", "signatures": [{"sig": "MEUCIQCbf+z09l3raXd8Kdegsuzx3kKLJBwPMAZuecv6l/sriwIgb6VxIuyZMkeSJinRKdua86mKqWsNJ2AYWl1MpwPiVNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74918}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.11_1717751729812_0.9284638610060807", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "49f17f44d77098be2eac20c2d5f546cb3127b0ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-vPwPkbWZZdPcHhfBuoxJnQJbYALvHt01NKwIf1ITQV/g5y0EV1VyByL6X9h6nSkgU3OJ2OpQUYvIAnwFBPl+zg==", "signatures": [{"sig": "MEQCIHpvLjhX94rErdmRHhBs5BTIi5E4H5RfphBZDXli/49MAiAyzu5xn6Z9isADBNgggNeHoAr11weCptRAfL3EcIOp7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71710}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.12_1722015206256_0.3224627179331194", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "0af84b86d4332654c43cf028dbdcf878b00ac168", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-FbuJ63/4LEL32mIxrxwYaqjJxpbzxPVQj5a+Ebrc8JICV6YX8nE53jY+K0RZT3um56GoNWgkS2BQ/uLGTjtwfw==", "signatures": [{"sig": "MEUCIBUUHM38CMjRnNm/TAnxaID+Ek3rIK8bY29bQFSxYlHwAiEAgGTAeiYad/33w9OiF0OcjW97s9dpmdUC5ccyBxBxZSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79668}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.25.7_1727882086081_0.1409580269017261", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "befb4900c130bd52fccf2b926314557987f1b552", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-Z7WJJWdQc8yCWgAmjI3hyC+5PXIubH9yRKzkl9ZEG647O9szl9zvmKLzpbItlijBnVhTUf1cpyWBsZ3+2wjWPQ==", "signatures": [{"sig": "MEUCIQDwNcRlC4dlTaN/rSKKQyVyvux8d72FJ04oLuLvOfj7cwIgdAuysBvIpxjrkKPCuSnUT5eIFZiL7lrS3DwSMp/6au8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79970}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.25.8_1728566709892_0.4886652355706478", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "bcb1b0d9e948168102d5f7104375ca21c3266949", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-ENfftpLZw5EItALAD4WsY/KUWvhUlZndm5GC7G3evUsVeSJB6p0pBeLQUnRnBCBx7zV0RKQjR9kCuwrsIrjWog==", "signatures": [{"sig": "MEUCIQD+lKPLgvvLRSYX9yjvEkhdP30Ea5RLu/NsqqfRbD5kpQIgZCtMV9n1hCS4ks15CkRbVtIJMunQSoe7vwkzvVhT49I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8986}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.25.9_1729610464315_0.9738155832001323", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "8211aac589916b70a5a9e76f5543bfaad797cbcb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-9G/ezcWwsNt0LSjOYxa3dJGoWzhI+kPKfnHQmyedw3HZq9QAMqsj3WlN/Qz2Yh+BdLNCWiLTNfKqLnLGJixOPA==", "signatures": [{"sig": "MEYCIQCpz4HYuoXV3RE2P9CxmKRjpnIjwpg2PSKYzV5qds2SLAIhAJP77YpgGA0/ubrthyrpCoH1j5mLNYpVnpx4Ni+cvpfN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9024}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.13_1729864447774_0.42927503537088785", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "edb12952853adb8aedb2926d301882c5d4f90be2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-H+vgelHWcRlYixQ7xirTC84RSAP0GDgDDEluRjpNnrn6XAEKmcVgWM9mOef9qOuup2KzT2pp5r8WkTWT4bMiFA==", "signatures": [{"sig": "MEUCIQDk2sbXtF1zRMgN7ZGn2EklnQIZyRWg+DIprCLlVNo0lwIgZaCr5y/kQVP5s8E144r5qHaG8OZaNXPDroqg+9awnUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9024}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.14_1733504038917_0.2901089517986146", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.26.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "b0e8943a8a4689c55e91eac573b1fe6bc105026a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.26.5.tgz", "fileCount": 5, "integrity": "sha512-OHqczNm4NTQlW1ghrVY43FPoiRzbmzNVbcgVnMKZN/RQYezHUSdjACjaX50CD3B7UIAjv39+MlsrVDb3v741FA==", "signatures": [{"sig": "MEUCIQCdPgkyq8fdtVcjsJkJ3GSmTN42V3UunrukycII5gCxjgIgbrqskQMziuZ1J9uXis9m71nw971QTzC0Xk/x7Tc4yHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10521}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.26.5_1736529111891_0.17851610928726136", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "4dc6a9b7ed6e2f8cffccaed3fe35e8d1b0f7347c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-Ck52MLUyziIjG1Q39BJXr8Z380c9KpW11y52Zd/q5qyWBouV2LrypVtL3sZTODNcyKR48BMypdANXNV6BRlhAg==", "signatures": [{"sig": "MEYCIQDhPymeglMX/rNUsCK++5D0JRbUHQq1lD/B4BxRbxr/UQIhAMh3mezcIxOySqv6n8mdiSwbzNCojQ8W+JD9xglIMq71", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10420}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.15_1736529864016_0.11422626976173134", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.6": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.26.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.26.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "fbf6b3c92cb509e7b319ee46e3da89c5bedd31fe", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.26.6.tgz", "fileCount": 5, "integrity": "sha512-CKW8Vu+uUZneQCPtXmSBUC6NCAUdya26hWCElAWh5mVSlSRsmiCPUUDKb3Z0szng1hiAJa098Hkhg9o4SE35Qw==", "signatures": [{"sig": "MEUCIFa8WAASd7wSNSsfrDUa1jdpGZwRNss+I2NGxzKYUaTdAiEA9pc0gX48FypNr4i1/KGwUTa1FeZUR3pPKeM/idlMqTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10396}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.26.6_1736790547355_0.7852443285580302", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "708f40a1afafc76e42f75e5a01eff8e0266f3611", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Lsvw47qp62E0UAryBxaXatlQJCUQ5Fdm5GSSVYFclBXVPAeuVoi6oWVOZiVP/OXs0Qj64fGw/QF+2Nui77rk6w==", "signatures": [{"sig": "MEQCIFj1hr1Jssgm21PM6GA/n/mxl+X784gKcQdsc3iPuIIFAiB73b3gT18AJ7tWsdt8PS5j1VxZNd/0ITBXwvG+PUMv3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10295}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.16_1739534340425_0.6145906399113503", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "f2da26960fbb924bc985022084644f9ca496b8ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-jS9PIntq1pcgSFJrpnVrnyRpmwtNM5C2a2kNKoW5dFa54xPnhFtkbUssFNzreAWM2Y7vovCFV02vUX+hmjpkLg==", "signatures": [{"sig": "MEUCIQDTpa2vjBnjNue8TH/AbvIy5bEggKQ+uQ6rzD9Z/5XFaQIgTAHYb2V4KFAf8ceIhirEBSCYGtd9sLvASBnPDFAceXQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10295}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-alpha.17_1741717492211_0.642204547437482", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "4f9d3153bf6782d73dd42785a9d22d03197bc91d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-aGZh6xMo6q9vq1JGcw58lZ1Z0+i0xB2x0XaauNIUXd6O1xXc3RwoWEBlsTQrY4KQ9Jf0s5rgD6SiNkaUdJegTA==", "signatures": [{"sig": "MEYCIQD/IZBjUoyoHbwLgC1VEcxCR+DEw6pUBcfW/LF5ngtaKgIhALtqnqPNNmdZhlEpf2GoDNw2VmGciybu+3BgJu4oPZqU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10396}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_7.27.1_1746025730487_0.6165095864419716", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "dist": {"shasum": "1ab0ee664241999ddd0dcca3e22795ad493244a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-hi80pkj3fyC3dRJPRE8YCDECKq4dBqzwt6bn5m20h6JBwursBz9GYaUmaj2iYbhyWF33+VyWytsXdG0jOgvKJw==", "signatures": [{"sig": "MEUCIDZohz3rSu4DtrL//yLkHg66B5K5f6jTqQgxOSCBpY+vAiEA9T70KNNN/u9EiPva+zybnwNDcKjLeEJuJX/AQlM19bk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10271}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-beta.0_1748620261864_0.2052906230425564", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-nullish-coalescing-operator", "version": "8.0.0-beta.1", "description": "Remove nullish coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-nullish-coalescing-operator@8.0.0-beta.1", "dist": {"shasum": "a5bd65b00c010285709ca4eb80e962595e87cef1", "integrity": "sha512-86ps+d+x4EuKE85DbdaCtFLVhjl/whiPKgSduXOYz0JCr6MCx8/PVjPgvn2KashkSszEyoto/xf5JtXnj2dPTA==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 10271, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICHIRaO/5LIPTV2atknYq2PxeS5nVzEca5G5zDc69itjAiB8zVE6pbgdE+5vuu1KlTPg5/iO9p40qr3WjyU8CQvjhw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-nullish-coalescing-operator_8.0.0-beta.1_1751447054882_0.5701998440407028"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:15.733Z", "modified": "2025-07-02T09:04:15.270Z", "7.22.0": "2023-05-26T13:45:16.014Z", "7.22.3": "2023-05-27T10:10:57.552Z", "7.22.5": "2023-06-08T18:21:18.305Z", "8.0.0-alpha.0": "2023-07-20T13:59:52.918Z", "8.0.0-alpha.1": "2023-07-24T17:51:52.796Z", "8.0.0-alpha.2": "2023-08-09T15:14:52.733Z", "7.22.11": "2023-08-24T13:08:39.220Z", "8.0.0-alpha.3": "2023-09-26T14:56:49.388Z", "8.0.0-alpha.4": "2023-10-12T02:06:15.571Z", "7.23.3": "2023-11-09T07:03:53.216Z", "7.23.4": "2023-11-20T14:22:07.760Z", "8.0.0-alpha.5": "2023-12-11T15:18:39.078Z", "8.0.0-alpha.6": "2024-01-26T16:13:59.362Z", "8.0.0-alpha.7": "2024-02-28T14:04:49.091Z", "7.24.1": "2024-03-19T09:48:51.429Z", "8.0.0-alpha.8": "2024-04-04T13:19:48.239Z", "7.24.6": "2024-05-24T12:24:29.713Z", "8.0.0-alpha.9": "2024-06-03T14:04:14.292Z", "8.0.0-alpha.10": "2024-06-04T11:20:02.795Z", "7.24.7": "2024-06-05T13:15:19.861Z", "8.0.0-alpha.11": "2024-06-07T09:15:29.967Z", "8.0.0-alpha.12": "2024-07-26T17:33:26.590Z", "7.25.7": "2024-10-02T15:14:46.326Z", "7.25.8": "2024-10-10T13:25:10.092Z", "7.25.9": "2024-10-22T15:21:04.491Z", "8.0.0-alpha.13": "2024-10-25T13:54:07.940Z", "8.0.0-alpha.14": "2024-12-06T16:53:59.141Z", "7.26.5": "2025-01-10T17:11:52.069Z", "8.0.0-alpha.15": "2025-01-10T17:24:24.177Z", "7.26.6": "2025-01-13T17:49:07.502Z", "8.0.0-alpha.16": "2025-02-14T11:59:00.591Z", "8.0.0-alpha.17": "2025-03-11T18:24:52.387Z", "7.27.1": "2025-04-30T15:08:50.710Z", "8.0.0-beta.0": "2025-05-30T15:51:02.046Z", "8.0.0-beta.1": "2025-07-02T09:04:15.079Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-nullish-coalescing-operator", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-nullish-coalescing-operator"}, "description": "Remove nullish coalescing operator", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}