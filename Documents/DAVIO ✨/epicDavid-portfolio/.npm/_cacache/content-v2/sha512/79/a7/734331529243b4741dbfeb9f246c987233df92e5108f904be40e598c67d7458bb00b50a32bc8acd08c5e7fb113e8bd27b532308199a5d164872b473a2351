{"_id": "parse5", "_rev": "140-b0cb51efc326975f1dd4a3e7c3b8fa05", "name": "parse5", "dist-tags": {"test": "4.0.0-test", "latest": "7.3.0"}, "versions": {"0.5.0": {"name": "parse5", "version": "0.5.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bf0632c61d7424fffa961bc2a17ac3dc0653e7a8", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.5.0.tgz", "integrity": "sha512-4w8hlYfWYLPKiqnl414V0Y5xEeSX47AaE7RS4l9XXBvD7SNokQawBH9sHQZVUybZk+kGu/guXD5ZBzn6gC45qA==", "signatures": [{"sig": "MEQCIBs+woNWojSCXeP50e8L5J9gIyVSZe/vMRgK6BO6jHURAiAkxPQPgah1oYEl4rAFzsYk34aauX0WSz0ndVxYv1LJkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.5.1": {"name": "parse5", "version": "0.5.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ded248d56a780379b51ff81ac18e5018c34e1183", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.5.1.tgz", "integrity": "sha512-QQY8i7/NJLo5k6JJmN9N18fpImVAXg19kCAIUQb4CxrcMALQUaJx41wMKTejVmoVg/kqEuE1244zZiljAVlc6A==", "signatures": [{"sig": "MEYCIQDsqwsHrbIX2jczGhXaH60RhuSQA2d9IeovRgZnZZ3EPAIhAJM7Tom+AzUrTxcb5K9hIxhmEjIP7CA/3l1w7m1fOPRC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.5.2": {"name": "parse5", "version": "0.5.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a824319b7f6ab03caa6c5eff35e4baaeafae3da0", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.5.2.tgz", "integrity": "sha512-aGb0VooXQCYfJle0PDJeyJvxKMzdAZgNLUSfsOjyaI4QQv413sax4aovjVUY+JGmodWzhC8vd7+LPkwcih7NKw==", "signatures": [{"sig": "MEUCIQDCAU/o+ir2REGZUefVBf/05lwNx3f+n5NN7D5KnCtwuAIgHWkuMSfK6JrnzPjew6n91zluokH1+wiEVjqHSdYFtuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.5.3": {"name": "parse5", "version": "0.5.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "edfac62ed224ecbbbfd138a92154197801804e5b", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.5.3.tgz", "integrity": "sha512-h1t6oVpHJ1JMbIhAB44eNfRaxtjWvihh5eNMKkmpvwIRidYTNCUwK5U0ObnkThUbV0M161fBIAnLdPB01r6Xkg==", "signatures": [{"sig": "MEYCIQDGBWNRqIpguFcthGheeDiroHG7wQGucu1M8E4zDBZ3NQIhAIy+7jOn6M06c3TSV3OhJeQldpP2E+oqRkgwg7E7hcNp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.5.4": {"name": "parse5", "version": "0.5.4", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b3891fdbf72ca008f7168326831031311a04e60f", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.5.4.tgz", "integrity": "sha512-8z21wjkmlU+8n9KEk4zYC3Iz/IdW0nTYLGlLwGxmjBbBddGa5uJ2XvENVo6eCdG2IqSXrxorShOg3qoioEHWeA==", "signatures": [{"sig": "MEUCIQCUVXTekhHtrA/Lws6uDuI2HEnEO9TP9GoAERhmgbMgmwIgVRAAmZejz7oXMtI3MgUIGxFQTqQ0IJcyf1iKk7ZJibI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.6.0": {"name": "parse5", "version": "0.6.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "637b1c2ba52f407f80041d376809598b16cc241c", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.6.0.tgz", "integrity": "sha512-xyf0Gaxee20JPaa+o/YPLVtdbyRkxSGZP7NkTKr/swEmOXcYHw9watjy0p5srr39efDi4+n637nX4vFGlf8Ksg==", "signatures": [{"sig": "MEUCIG9zSTKdMV9Reep8gDNsylqEWw/l3v1OBZZyRty20S+lAiEAwbqhegxpbQQaMthpih7lNj7NkBakiL2zSJm1JyNyOmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/parser.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.6.1": {"name": "parse5", "version": "0.6.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5963bb8114fd8ad4929753bbfeff3dd59966225c", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.6.1.tgz", "integrity": "sha512-UKbkCrrag2QA7fb1Vzazx0Mx2VuZr+g84pFUP465zNZuUZQm4O83fNsuyRYDmWPfW5htY7HoRg+WYEX/qAK5rg==", "signatures": [{"sig": "MEUCIQDtTtJN3pfVWB2cFlknjhxtvYCZ7m2P0EOVKiU3VVyc8wIgBg7k988A156vTJpvQlaR7pdszmmG4Q6SmKg/mY+EboI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/parser.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.8.1": {"name": "parse5", "version": "0.8.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "ca17a7e1089917df7066dc04b92b0cdac90998c0", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.8.1.tgz", "integrity": "sha512-LQBs8GVD4/fPk6rMno6elTHFb6Gf1hXfViuOWDnGmrnyTNUR9Mdbeyu1oqhrniDwDaK2RaBTvvll36ggivQjcQ==", "signatures": [{"sig": "MEYCIQDWCL6altvDrKCY3PB59e6Ceu9QPwZvuAqlTLkFILT7tQIhAN8R1F7gmZSxo0HzbM/zJ4YwRo2jOoC66XY3g1WY9m4R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.8.2": {"name": "parse5", "version": "0.8.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "70f59e6202e90685d33368f7379604cd2af61695", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.8.2.tgz", "integrity": "sha512-8fPTG8tnmIoJJKYEDdi76lzzAL98KKxG/Ko8sliTbqkuSYfuNY55r4UvsyS/3OM7jOK1e0EWBLPa8awIHO2qFQ==", "signatures": [{"sig": "MEQCIAwl3Y/t22tjspnnbtP8X8+GMlkAOiHc3krlpcxiM0vjAiBjHQFYjSAdJx4E+JqSBBu/oT2eRrzqNreviZbuUuIGAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "0.8.3": {"name": "parse5", "version": "0.8.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@0.8.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "59547a360c5e2ec1535d262b02f6f731fcf1db3c", "tarball": "https://registry.npmjs.org/parse5/-/parse5-0.8.3.tgz", "integrity": "sha512-Z09sPrkHSd6mhghipzBfDjVaOlCNEum6PyIm14O9iOKeOwRiLbKxgVddrJP1bxSgylQH9cGlx69/Qhb16qjyVQ==", "signatures": [{"sig": "MEQCICgm3tRndtzYchslFQOgq0cooLXW5ajLKwW4y4VR92J8AiBQI7LVs6jDOZouAS4Ls6YgVhxMET2SYw/Lc8ysk6ukMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.0.0": {"name": "parse5", "version": "1.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "d53ec053a2054e7538424ea9a6eeb9b265261279", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.0.0.tgz", "integrity": "sha512-NNdCAcwvTGOapmA/TF14m4HE4IMWiZBkCYKwaIyT5aq3COGzxdTn/u2Cvr/8lOYkwpaXvXEVx8RATtfzqOSYdw==", "signatures": [{"sig": "MEQCIAdJkY8npnF5VnqmAxYe0LDEu1kcBZsglSowdGSo72+uAiA2P4XinzV6GOhjQ3wT+6SqP2R7Y4MbzYsi8WHIQ4L5YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.0.1": {"name": "parse5", "version": "1.0.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "5e9e07d93e489f5c35539a3fc31fe1c2f418fe09", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.0.1.tgz", "integrity": "sha512-Ds/2xRVGDiKbKo16cC8hwtNEiLEJEHThABOAIPB0l1Cs/1IoKrUFX2AOAlKAUjwNM9n1m4oESi1kpYSgMeeqpA==", "signatures": [{"sig": "MEYCIQDjG7y4ilzqlAgLdLhAH2SYfHcpwV1O2oWAbCoNtG5hywIhAMyBLg9EH/AUAUhzNLkYILbpd7NmQbQ7Kdl/vWMf/+hN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.0": {"name": "parse5", "version": "1.1.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "47bdf81079727390d1af2f263f4f178b189ac8a8", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.0.tgz", "integrity": "sha512-N1743V/vS+wPH86LzFJzpe6JOrwB3egUU87vR29G0rtsKMG976rHFQvOIrQrQ4gUjOAXQV5NeZ6XuMhiU+gGLA==", "signatures": [{"sig": "MEUCIQCOwwjKHS2fo50Jb5rY4c5wd0y+QSzE5Yesm26+fTK4HgIgTJ4A/2diomo5o+OfbfryxtFCKoEUXGCmaM1A15vsxG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.1": {"name": "parse5", "version": "1.1.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "<EMAIL>, https://github.com/inikulin", "name": "<PERSON>"}, "_id": "parse5@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "86908db8894fbe67cb011f93c2eb28bf4bc0f776", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.1.tgz", "integrity": "sha512-ARzNZkumL8tFyqKG+uwYKYPMFBg04tPep6ey6gSP2a10M/XGvm4YB2WgSndSIAaZdyW3M2tBsDNPli9PT4wFSQ==", "signatures": [{"sig": "MEUCIAlsmfO7iMJUKPpBGo0L7s8Lab0GPOANmJxDlEuPs+u+AiEAjEFQg9dEzeN7KsQ3V4189i7/+bcBd5Cs+LBvg1yhv7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.2": {"name": "parse5", "version": "1.1.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "498dfd0cf31ef74d9a029ea69e972349c9372f79", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.2.tgz", "integrity": "sha512-wSCp56p/dBlRKEfcermRVOZhTn+mGK8AmlkHmJf5P8hea5fWG9u9JOWOwTFFYF7X/eEDn79bHZLH9xCBY+WfIw==", "signatures": [{"sig": "MEUCIQDFUnm+yEodoY4VdKNxWaImFz/ZvCUkM1v0/e9lj1eWtAIgMiDMIFlbkEQexWwyDqCV7xvgISuIQga/BsuKdqWjueI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parser for Node. Based on WHATWG HTML5 specification.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.3": {"name": "parse5", "version": "1.1.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "8bab58d06525f00e4e37d755116eb82e7241f142", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.3.tgz", "integrity": "sha512-Dh9KTpTUsDvI4Ny44C+cmvGOltwG730iupD8e2Fr7mCQv8WxXdb6twa1IKgc7/IkRrtrjTB1p4YtsHbeyhfsYA==", "signatures": [{"sig": "MEUCIGbD8qHaHjjuz+ZNnErckHJTiZ/9dcDgrchfH6hfbHdaAiEA0YEUWSSs4zsaj9/F54jyfwwzm7HuW+/Xd/fJp5lU2mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Fast full-featured HTML parsing/serialization toolset for Node. Based on WHATWG HTML5 specification. ", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.4": {"name": "parse5", "version": "1.1.4", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "61dcf2eb0ddf09a3edccc117843bb0c1e1fc250b", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.4.tgz", "integrity": "sha512-OjzdkfA7aevdQL2a4HICNy4MKf2Dg6upK15K7wos4m+ZP4h223dBjO/4eGn5U2tFiIDeKhPyHYpupPQ55DG2hA==", "signatures": [{"sig": "MEYCIQCZNH640J742mbH3JGMs5vFTuUI+7igAdoGZV/bw61a6AIhAMMoGIY7POtWcx5d+vMTrn0bRTEIsmpiN7FaeN4IBLqr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"nodeunit": "0.8.0"}}, "1.1.5": {"name": "parse5", "version": "1.1.5", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "6334727b80b880ef843c940bbd5324630f76f4d6", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.5.tgz", "integrity": "sha512-3xTMH95VlC9429zwzFvwlMhbIb/bONuxD7wT/y5JVrj1mR0+dJodNhtAbIe8mfYhs0JVbLMfeu/vq13xpjN1Sg==", "signatures": [{"sig": "MEYCIQC2AJRHlDI1YgSx/KOJKn25kwLPlE3aGZZXgn2d87DX0gIhALdX5nXCwsnqZgeGW3uXd21m+2LCXmR46rm86YQv4TqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6334727b80b880ef843c940bbd5324630f76f4d6", "gitHead": "7c76d920aa6dfca9a62509d490be27fcea2a0a46", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.1.6": {"name": "parse5", "version": "1.1.6", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "6be42d79ae662d72d72705c26a3cfb291fed18e6", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.1.6.tgz", "integrity": "sha512-kx1oN9QCWcIaQrbPIMKRJCr37k5GF0vSAvvJp521amKyEdEkmxhnEEt9TpUv5RxcLK4xE1oor+iBBPwRk2PUkg==", "signatures": [{"sig": "MEUCIQC6aRqRJbkgwsUBAHUx/rWsFJOtrM1ISFkFnfjoxXSeSQIgCL22MTjfeNy2lBbq5P0mwL9Am9SDu6s0LKRpHaXg7po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "6be42d79ae662d72d72705c26a3cfb291fed18e6", "gitHead": "5fb83c8b08d8bd827984fdb766fe941bfd38e3a0", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.2.0": {"name": "parse5", "version": "1.2.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "f571d50a16f252503e02f6052bec3b87f603cae1", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.2.0.tgz", "integrity": "sha512-EZLI+mS12jINbRHyF0clNmI64TRP1AHuxZI8mxVj5Z8SUOqRv7dr8jFtQI2MCQpmkGYuQgksgTyUSN3jjbj9Zg==", "signatures": [{"sig": "MEMCIE+bQp7mdpFep/ZyCIcteL7H8mMV82+mj7VWVnpYRuI9Ah8pbMnnFtxTqBCMHs6McjB9efYcf85W8UAEu9Xosdf5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "f571d50a16f252503e02f6052bec3b87f603cae1", "gitHead": "6c39a28000d2469057c330e2b74a56da7d488ff0", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.3.0": {"name": "parse5", "version": "1.3.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "08c04444e8ea328e8446562c083f2972a85bdeb1", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.3.0.tgz", "integrity": "sha512-bC7N2j3hryEG5gQ74DkyfPfdumAzGPnt4zQskJmOVTu58HmmrBLHT1zwosIuIhnYakpR5v1Vc6vWyU3IXUDyjA==", "signatures": [{"sig": "MEUCIQDPYwk44idGv+v/OJI4ZuQ/VUgh1ZECgXvszFSgDtfztgIgFour5SU03ON4iLdGa7lUoy4EmjJP5lAhANYDM/Yiv1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "08c04444e8ea328e8446562c083f2972a85bdeb1", "gitHead": "efe9080348a84402de3de580e654bb31e6572b3c", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.3.1": {"name": "parse5", "version": "1.3.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "bab47d0c423a2fcb30e16e5ab44d3e0bab5a5c80", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.3.1.tgz", "integrity": "sha512-DpNNU78mbxZYldFOBVymN9hFo+2ZDp/HnOvqeJ9lK/Lp/9yziFjvt16lhAYh6/wdl+L2FKpEJdHWvt5nXjh2bw==", "signatures": [{"sig": "MEUCIQDnptWfm3hgkkGn9FcqgiryQHI0+yzH1EdFuiTsl+FsswIgSECWT8mn3eUT3A/JHbO0WFcuWm4He1nTiWQ+nI3UKyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "bab47d0c423a2fcb30e16e5ab44d3e0bab5a5c80", "gitHead": "c195fdb902cf7f0a95fc0f77aa8272ccdd67c28e", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.3.2": {"name": "parse5", "version": "1.3.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "d9c9fca0aa7ee7be0f9690426dd4f9d1852af8ee", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.3.2.tgz", "integrity": "sha512-lgyPPrJg3QhWRFUFH97YlSvpQsiKHKdB/UaAmDgtFEkS4yRDFSGYirFei9F9cBGf7+xUHMbItyFubjRDOQaEyw==", "signatures": [{"sig": "MEYCIQDridASpJj0aHEAyvjevsSZScPDEk8HlbOA8uZKic/yIQIhAO5w5ZmD5jcEZJ6smSo0b+jn9C3GPyIyIoL+ehFCXg6h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "d9c9fca0aa7ee7be0f9690426dd4f9d1852af8ee", "gitHead": "2512565e01e6a2e1f4d8a83a0f9dc764376a6987", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.4.0": {"name": "parse5", "version": "1.4.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "d654747c61b17c4470447b4efa04630921c76228", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.4.0.tgz", "integrity": "sha512-Rf5PKWCYaAAuYjLumeKjZLZU5Let2D05aKJA1Ddq1ikDdgsUDKeqz2Gj3hwtR/U6WeGDpzVg2CzmczxMe4e2Tw==", "signatures": [{"sig": "MEYCIQCbcg6Yy+LyprnESXE2Cv+o9wA5CnaZ7xvLUPtnZSJgcAIhAN6pGd8wQd+ujKlt5UTTJ2Qj4yq0FfPV85LTWN+TbNiu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "d654747c61b17c4470447b4efa04630921c76228", "gitHead": "a60fa1fa2c614a0e5b4749b9123a98cc3994a425", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.4.1": {"name": "parse5", "version": "1.4.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "fbfe11c8bbe9fbdc581f646dc0e783a069350ea8", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.4.1.tgz", "integrity": "sha512-mhSt8YFzHRjWGDrBb7Mk42hr78gGu0QqmyC68D/AG/BupfLAU/Uns2WG1k4C9q/B4l0R23ZPwrqm94Dy8EuY0w==", "signatures": [{"sig": "MEUCIQDs4V2zExoezV6h8D7T2XWwzKaZVPjMRd8oQ/1uOv4WhAIgCVbUZrAixrhkE/A+Ct+tXHQY2OyUhkBQQqUioWP2GPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "fbfe11c8bbe9fbdc581f646dc0e783a069350ea8", "gitHead": "e12a14d9d153e8713a2cd6d5fe1467180cc67520", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.4.2": {"name": "parse5", "version": "1.4.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/alanclarke", "name": "<PERSON>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "43241c1609cc94bd007f5dcfa21218b37dbcfbe7", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.4.2.tgz", "integrity": "sha512-bxV+P0JVSDHHuMnhqdgG/4XM+jZcstgTFTCiu7IbGzGITjDQzo3nD4a8WCNE5oGHlwv9JQIjcAvdPq/LMNcxXg==", "signatures": [{"sig": "MEUCIQCKnlazKbBc2cdJBgtsVu02zdiGwXgrxQAkLY3eDdvzlAIgH56A6TrZdNWDuy6MsCfLQRjNYk7uo3my46A5tvfwzIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "43241c1609cc94bd007f5dcfa21218b37dbcfbe7", "gitHead": "bc8862ca7c94ad7704fd134d7208223814825acc", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node and io.js.", "directories": {}, "devDependencies": {"mocha": "1.21.4"}}, "1.5.0": {"name": "parse5", "version": "1.5.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "7b6fb373b5abba8605113d8de2be5b83f6de82f7", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.5.0.tgz", "integrity": "sha512-nNyry3T4e1niY3FPa6Hs30+hwBxu1E7j4szVhHQi5ZNev723/7xAdldCzTj/Z3KQhpVABuXCGbEykefrQjCxVA==", "signatures": [{"sig": "MEQCIBtX37sUCTB3QHYrhjNYur1MlDXKockt70Fu9mmc89WWAiBRy4Pev9DNIVAW20CBf4om2/Qzfuin3/xfAQsKkiN2bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "7b6fb373b5abba8605113d8de2be5b83f6de82f7", "gitHead": "74ff897882952810d362af8cad9f3eb7a1147dec", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node and io.js.", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "1.21.4"}}, "1.5.1": {"name": "parse5", "version": "1.5.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "parse5@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://inikulin.github.io/parse5/", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "9b7f3b0de32be78dc2401b17573ccaf0f6f59d94", "tarball": "https://registry.npmjs.org/parse5/-/parse5-1.5.1.tgz", "integrity": "sha512-w2jx/0tJzvgKwZa58sj2vAYq/S/K1QJfIB3cWYea/Iu1scFPDQQ3IQiVZTHWtRBwAjv2Yd7S/xeZf3XqLDb3bA==", "signatures": [{"sig": "MEYCIQC+lnOVvL9VcvOUwkJsmV4triX/+N1H9XZKTNn1C4gaFAIhAKZgsOSsE/QPv7kIw1dk1UBeLAA4abcLa91LLCdvnkPy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "9b7f3b0de32be78dc2401b17573ccaf0f6f59d94", "gitHead": "9cbf3b578ec7f703cc344aa1c564719781550701", "scripts": {"test": "node test/run_tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/inikulin/parse5/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node and io.js.", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"mocha": "1.21.4"}}, "2.0.0": {"name": "parse5", "version": "2.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "11b07272debc71aec8214182ae0499b9a5d44b0a", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.0.0.tgz", "integrity": "sha512-2j6qH5Nb1mDMhZ5CN0gakzS2E6FJCk2d6kAwxWdEBpoKAWNTzOG0InuYJ5ucVTmnX7epCN0F1cY7BdwNPQ4CLQ==", "signatures": [{"sig": "MEUCIGO6PnrnryQos5cXZJvRdTwwo/NJ5NYcZQijUYlOGJ+rAiEAmIVezoSE5REpoQKvqGuE9L4IUJ7zkIB0ugkrTa8jM/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "11b07272debc71aec8214182ae0499b9a5d44b0a", "gitHead": "e1a4cb5dd242c797082a036d5b9d9defe6265f10", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node and io.js.", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.0.1": {"name": "parse5", "version": "2.0.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "76718e2cf563c7fb601309c4d85e71077dbb3667", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.0.1.tgz", "integrity": "sha512-MxbvH04RoKAUmYgytyPV7M0DeNjkKhi17/T0+XlgC3LrOWPBUKydM7bdDWKEI7Dm2O2mutGhW8f1+Xm2+ESjrw==", "signatures": [{"sig": "MEQCICAyp3bOL8fQUZFXdC9k59vnK/0tM26KiLlaK4IecpiKAiBwjK35a8s6jgreh5BLia+E9RELl+io8Bo8SxDMohR8rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "76718e2cf563c7fb601309c4d85e71077dbb3667", "gitHead": "efbf0cfef25d3316a51e0d4bf81619c89b968930", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.0.2": {"name": "parse5", "version": "2.0.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "804f78d4677c0a77f60ae7947d467f43b3181cd4", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.0.2.tgz", "integrity": "sha512-XiZzZf4ZFthFYkBEJQv68F844bR0ewC7iFRVshmLGX0FTqYlmAaWNN9JJQ8ng40j0iTvchXoz/IBg20UTOVqYA==", "signatures": [{"sig": "MEUCIB0uj9noAtzFbTqq/uCfwWH8Q+9XZeh+0MWphKMsFexnAiEA+qaTP1Qmrh2MbG5Saq5MKucWAJKePWPrJe5fM/7/7tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "804f78d4677c0a77f60ae7947d467f43b3181cd4", "gitHead": "a3f2ea81138959639fb169a10e6853d027ace7f4", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.0": {"name": "parse5", "version": "2.1.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "bfdaddde4888d7a8d0f10696bb06502940759c7a", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.0.tgz", "integrity": "sha512-hS837cbhkqC+wZWP5hKtlDBGX3CE6BbbXNQ6s48MZLR9MIrEmJxr9ehZazrgf0yhtFjS3ek+6TYUaQq5cGx6nw==", "signatures": [{"sig": "MEUCIQCrv7gDlrEPlURdvHOOjaIMF4fjSAW+ETqBx5SnM4NwkwIgDARudhlE6n49FS9oDLm7YzTQxgEJHfLuDZCKPnuI2es=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "bfdaddde4888d7a8d0f10696bb06502940759c7a", "gitHead": "78e84d962861b7bdfda1692bb76ac1167b5a1c30", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.1": {"name": "parse5", "version": "2.1.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "ff222b8a14e77bae39ef0839bccf269f10c12080", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.1.tgz", "integrity": "sha512-8QmdMmEA0FsJaflv95VFuxwBbJmFxCdZztpOqOV7lOAtHaGSMYlsagsWwunnJscPhY/cZu6lozjjaD1eEqxzMg==", "signatures": [{"sig": "MEYCIQDpMZd/vDp4yftyUgtJpQGPsf8n8lSJsSJuhHzaYnqs7wIhAJ8p5pPmgnuAIr0PuwoOpX3UfQblTzJNio1unujVVfE3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "ff222b8a14e77bae39ef0839bccf269f10c12080", "gitHead": "2b97a93d2699497bf8b169fc283e8261301d9e5c", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.2": {"name": "parse5", "version": "2.1.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "ea353f92136d903e242ce500c8e42d9abaae4e02", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.2.tgz", "integrity": "sha512-IXXQTfq6oDLHvh22hwZKiVcllx7CHlayjIR0e+T+MiAnCE+JgoHaD3LCDvnx/d4SSM06qtW6OTleo3OiAhJ8TQ==", "signatures": [{"sig": "MEYCIQChPI5kOTSznnDMPGv2YKwEw5IUhQLBLskX3xAN7Ym18QIhAJP2YSaJQ/iY5UNSZja6UFSM3jsLXldl5kFVQmU6P8Nn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "ea353f92136d903e242ce500c8e42d9abaae4e02", "gitHead": "0fa927a5b663ba276c393f500eccd9015164236f", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.3": {"name": "parse5", "version": "2.1.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "bd30ea83354be85282a51d3bcbe937b3a1199b79", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.3.tgz", "integrity": "sha512-6aS7n9j1nc5dm/Haz+SgPjw6gcerZqBgILqbsexmbpAaE/NbUodTvy4Qk/v1G9jC4SmY3ISfzVMhUpQ6KUwzJQ==", "signatures": [{"sig": "MEUCICjk+2ltUAIXFpqoq2HiiSqDjjMys9zEGbXjAbKSuV+hAiEA57DBJ1P7/S4qjpnIOQV1QpIm15ijS3+JLDxRrei44Z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "bd30ea83354be85282a51d3bcbe937b3a1199b79", "gitHead": "0a6880e13e42869ffebbd4dbb6711a7567266455", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.4": {"name": "parse5", "version": "2.1.4", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "96618211d90345498dd8e06f3a36b7be33721100", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.4.tgz", "integrity": "sha512-5m9wNgQu5/XUFPk1z7Z+Pw0in2/segirnAQVGHzeaVxEkYhB6EbSWfjNRjpVUxlNws59/kc51XwYW3aNMSmrng==", "signatures": [{"sig": "MEQCIDXQYZvqfpW3trZ9iojeCppJdPApcJg59hJ5HUys3/GRAiARLzS7nuxuGlThu8sdpRN9ShG9fTdLvmH0q6qIXKHL4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "96618211d90345498dd8e06f3a36b7be33721100", "gitHead": "1eec3cf9b989ab0320034b57c59615241a137189", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.1.5": {"name": "parse5", "version": "2.1.5", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "7a8677ade25ddac04237905f7be54645572dcf05", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.1.5.tgz", "integrity": "sha512-zIVNDHQZKD0S1pAYkbg4EpHOKDcCBgtf63bsrZO8l9OO8uMDGsjZw5SEpe+hYRWfBxS3N2YoYNbYi5ABJtediw==", "signatures": [{"sig": "MEQCIG4rXSvHhHFqNgJuJpwAeCjeqHHPFqumg8fsEJn7fwmIAiAKs1I6t+Vj6hL4smShPbnRxVk1dG2E+WYuM9rrp0tx2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "7a8677ade25ddac04237905f7be54645572dcf05", "gitHead": "3c195ec374422497fdce1f98528dab9e7ebbeb9b", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^1.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.5.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^1.1.0", "gulp-jsdoc-to-markdown": "^1.1.1"}}, "2.2.0": {"name": "parse5", "version": "2.2.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "e2378b541d552047cb3abc5bd2f2d05a1ae36170", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.2.0.tgz", "integrity": "sha512-i82Ca/aNDmLairjYLAkwYKAkUH47iINPLihIaqBR4TP1vKyR9krbH6tR4SA6LoSO3gMvAfVljKRWLdKul+WoOA==", "signatures": [{"sig": "MEQCIBDhzi/UNI02nq9kNV+qmGYItJQU1dmGbi2PqPikRf7dAiAjzUpkuvd/OyFgwOxKZszsJkiJ2V65DxZBbS2aKmMcDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "e2378b541d552047cb3abc5bd2f2d05a1ae36170", "gitHead": "6f9b2a311e4dc1f210ec439412ae6125ed05d631", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^2.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-jsdoc-to-markdown": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-2.2.0.tgz_1470915407245_0.07531618792563677", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.1": {"name": "parse5", "version": "2.2.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "48dcafe712b0dc583f9ddabf1a4a50d8898e821c", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.2.1.tgz", "integrity": "sha512-OaEBejtAn2uUT8e7jJ/5930Zb43Ix0w2o22MQMNMIRpgeIRxitc0TfWT0EG/XqAI71FgbJ59YVBIGLYOow1Jcw==", "signatures": [{"sig": "MEQCIAe9oSTP/nngeKkj0oOvR9kcuHJoKV+D3kkyXWglJQqWAiA879+9BSA07I5FdpciYjc9QsI2w3Y03Y/7cPCgIePwJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "48dcafe712b0dc583f9ddabf1a4a50d8898e821c", "gitHead": "389d01761f1f072089cdd2b4f7dd112c96e4b531", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "promise": "^7.0.4", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^2.0.0", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-jsdoc-to-markdown": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-2.2.1.tgz_1472728719480_0.1344913737848401", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.2": {"name": "parse5", "version": "2.2.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "27f04785e9aee58aa824bc0a31c3049c3e39c51e", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.2.2.tgz", "integrity": "sha512-ygCP4C3iqvKaZRD8fFry44243AATg/ndsn+ya/MFh8sVyzHHSCJkMyMqBbo4FaRrb7s6XQVRmInNz5MP5XbhzA==", "signatures": [{"sig": "MEYCIQDlsZWlsAxg4zj4W/W+cU0/EhpHwgTx9s5Q+MDX1TDVSQIhAJJu4RYahtOTjdN/75UjKbuXdkq9O2xzXRgRidGuDwaA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "27f04785e9aee58aa824bc0a31c3049c3e39c51e", "gitHead": "8dd98e7391cac56a18ff92cc99b2c28fe8b0ad7d", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^3.0.1", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-jsdoc-to-markdown": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-2.2.2.tgz_1475157650056_0.757071032654494", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.3": {"name": "parse5", "version": "2.2.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@2.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/alanclarke", "name": "<PERSON>"}, {"url": "http://evanyou.me", "name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://blog.smayr.name", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://slang.cx", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "0c4fc41c1000c5e6b93d48b03f8083837834e9f6", "tarball": "https://registry.npmjs.org/parse5/-/parse5-2.2.3.tgz", "integrity": "sha512-yJQdbcT+hCt6HD+BuuUvjHUdNwerQIKSJSm7tXjtp6oIH5Mxbzlt/VIIeWxblsgcDt1+E7kxPeilD5McWswStA==", "signatures": [{"sig": "MEUCIQDa1GONGPf6AfHZIn/aNp7MvM39r6CeDy2XDD2MPYExngIgG+wo7HXAxpu51eM8yBZDtTqepTgmSwja9AGQuSmxWmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "0c4fc41c1000c5e6b93d48b03f8083837834e9f6", "gitHead": "a06ec5006ee88f55d124d195b32ba3d261791acf", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "WHATWG HTML5 specification-compliant, fast and ready for production HTML parsing/serialization toolset for Node.js", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-concat": "^2.6.0", "gulp-eslint": "^3.0.1", "gulp-insert": "^0.5.0", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-jsdoc-to-markdown": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-2.2.3.tgz_1476879539034_0.8995897020213306", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "parse5", "version": "3.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "2bf6709e7f4b80040c1373abafe4beb3ad4f40fe", "tarball": "https://registry.npmjs.org/parse5/-/parse5-3.0.0.tgz", "integrity": "sha512-O0fkWMTocWjL7CNx+fZuXgAe6GMeENiiu5aR/Ix4bmvS/fIOT43xgg5s+XfhmABL29b9vOpQRrFx9ZKrLODQug==", "signatures": [{"sig": "MEUCID2RMpbPQ+hE5uVTTb67PLnlVyKNCHDXHDwKdFzTEwIaAiEA1WpRfbo7U135VKmrUn34CorCV77552JNdf0tSOR/M6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "types": "./lib/index.d.ts", "_shasum": "2bf6709e7f4b80040c1373abafe4beb3ad4f40fe", "gitHead": "e8937057a2ee0ea9f723296cc0e173a723b6d0e2", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"@types/node": "^6.0.46"}, "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-3.0.0.tgz_1480949581766_0.13455631234683096", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.1": {"name": "parse5", "version": "3.0.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "f59ceae1e0c74d0b1f6e51ca317c054cab484f79", "tarball": "https://registry.npmjs.org/parse5/-/parse5-3.0.1.tgz", "integrity": "sha512-YPp1Ls/evTfmwP2KvIL7CNn75BJ9VHJAQQBUQeuUj5+2yhlTBl2u0o3sDg2Fpmok25hgucFWsvpbFRSxjtbNrw==", "signatures": [{"sig": "MEYCIQC7KJIGRFsTzK6qGLK3bYd5WTvYHwhAkXoWP6Ve2fxgzwIhANCB4Ffiy/+gqoGPzcWR4+jpNCXPMaWpmFGRXebZJEMe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "types": "./lib/index.d.ts", "_shasum": "f59ceae1e0c74d0b1f6e51ca317c054cab484f79", "gitHead": "f7de0284e19d168f140bd373e4e736050567af95", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"@types/node": "^6.0.46"}, "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-3.0.1.tgz_1481881800392_0.13169157248921692", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.2": {"name": "parse5", "version": "3.0.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "05eff57f0ef4577fb144a79f8b9a967a6cc44510", "tarball": "https://registry.npmjs.org/parse5/-/parse5-3.0.2.tgz", "integrity": "sha512-yQW05f47bKFJa0WdnyzP7vh7+B+w8jhVsFBBiaEbIfNDSSt8GADBhcQgsdYxatQ7rVs1nU9cmsYXURGWBH3Siw==", "signatures": [{"sig": "MEQCIDWiWJCEnznDYuBI77PR59alaC+eFMVyCPhxAIlLJeluAiBeVRKOSTAGwhFxgOZIB3QF3aYhc7pihGJ8IApbrp1Plg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib"], "types": "./lib/index.d.ts", "_shasum": "05eff57f0ef4577fb144a79f8b9a967a6cc44510", "gitHead": "969ed22f53db38c101449e05a1b804ea456d4e04", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "5.11.1", "dependencies": {"@types/node": "^6.0.46"}, "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-3.0.2.tgz_1488053397430_0.9636669352184981", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.3": {"name": "parse5", "version": "3.0.3", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "042f792ffdd36851551cf4e9e066b3874ab45b5c", "tarball": "https://registry.npmjs.org/parse5/-/parse5-3.0.3.tgz", "integrity": "sha512-rgO9Zg5LLLkfJF9E6CCmXlSE4UVceloys8JrFqCcHloC3usd/kJCyPDwH2SOlzix2j3xaP9sUX3e8+kvkuleAA==", "signatures": [{"sig": "MEYCIQDmFhpyX5gZkQso6vR1AdHXSbCe7Eazs99W8t9gNke96QIhALIgHk5LCu/s54Q8Nc1Yu2uewnrmIvDHjVYvbcD4kOsm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib"], "types": "./lib/index.d.ts", "gitHead": "723d782abee65aaab9c50f92e73ac2029ae853df", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "5.5.0", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"@types/node": "*"}, "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-3.0.3.tgz_1509843822001_0.3522953314241022", "host": "s3://npm-registry-packages"}}, "4.0.0-test": {"name": "parse5", "version": "4.0.0-test", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@4.0.0-test", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "20909b0910990f8065d34c8211016b389e48c238", "tarball": "https://registry.npmjs.org/parse5/-/parse5-4.0.0-test.tgz", "integrity": "sha512-4qhc9/TYSdJ+KzT1KvC7b7FqzQ9SM6eRFltO9BXevk+uD8bCqJb3yAUHRxX4Yo+gy6YkJhf3QeS1N8d6LCb4VA==", "signatures": [{"sig": "MEYCIQCunXyurOIXjBTX47kCjWI/92SufWqKk6LLVcngrLoDEQIhAJUROU4/4LNNR5rLuMBToe8Dx6/f/mutRbOHhy2l7mav", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib"], "gitHead": "7ff63847be19a2b27c2cd3f4181ee16518b3bcd1", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "5.5.0", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "@types/node": "*", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-4.0.0-test.tgz_1515873108826_0.4832727333996445", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "parse5", "version": "4.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "6d78656e3da8d78b4ec0b906f7c08ef1dfe3f608", "tarball": "https://registry.npmjs.org/parse5/-/parse5-4.0.0.tgz", "integrity": "sha512-VrZ7eOd3T1Fk4XWNXMgiGBK/z0MG48BWG2uQNU4I72fkQuKUTZpl+u9k+CxEG0twMVzSmXEEz12z5Fnw1jIQFA==", "signatures": [{"sig": "MEQCIDmRW4eLYmBsXRrR1aO71RXGayOvbBDdCVzINK1w4NwGAiB9MpokcA2XYNhwP8dTy9tTCvUuewVqWT1I3R8rznYDxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib"], "gitHead": "baf400c8b30703a473fb1a014844c68e5435b19c", "scripts": {"test": "gulp test", "prepublish": "publish-please guard", "publish-please": "publish-please"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "5.5.0", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "typedoc": "^0.5.1", "through2": "^2.0.0", "gulp-mocha": "^2.1.3", "typescript": "^2.0.6", "@types/node": "*", "gulp-eslint": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-install": "^0.6.0", "gulp-typedoc": "^2.0.0", "gulp-download": "0.0.1", "gulp-benchmark": "^1.1.1", "publish-please": "^2.2.0", "gulp-typescript": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/parse5-4.0.0.tgz_1515876127435_0.5900823713745922", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "parse5", "version": "5.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "4d02710d44f3c3846197a11e205d4ef17842b81a", "tarball": "https://registry.npmjs.org/parse5/-/parse5-5.0.0.tgz", "fileCount": 26, "integrity": "sha512-0ywuiUOnpWWeil5grH2rxjyTJoeQVwyBuO2si6QIU9dWtj2npjuyK1HaY1RbLnVfDhEbhyAPNUBKRK0Xj2xE0w==", "signatures": [{"sig": "MEUCIHVRHwWePYAKqu38fJuEWcx2hT1WRFk5/Z4HrmuSjNbHAiEAsxcT8emryvYEWo5mZ5XjCyzBujp7CvmGVdGrnqDK18s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBWOdCRA9TVsSAnZWagAAW6kQAKTyABwtSriSkoeOu3He\njw2XOcEd/xZwJ3ustxLfAqKxA4DaDy7/n2oA0vBf4B7TZwyeybJQOS/JJUTT\nB2Jm1rS6ie0KxT2RUBsA8NUbUwOlJG7VQx3txYg4OMo3Pknw2/FQuLCnsZDr\nW5AEgw4Ptd/MpwBLLedGOAnSVWDOz8ZsMi4FzZ4rmcZ8pHNrJiugJA+I9Q8x\nVNK01Wv/yeWDyEXwuE1DguRIObUp1aBxDpj6QdyN6sItvcLiIUkums8LIqV+\nlkW9qgJPfCAFbn+Fon9DprTwWtuG+D5yNptGdCeTi3UFLnq6ZSpL5j/guEXr\nwR138PbFWlslDZfLCFrxffj4Wb+84qqAVSVzxHQ79l9G86QZRJRRBEprXAN0\nvBR39hVAbAXvSi8SOyQWMO9nPeBBNKC3DDvDSOrCo+NXQECthHFTDztd5aVO\nWB5DqESVl8GpNGAllpV5pW9fAk6TLwrs1itCnmj0XmuTe4on6d9iLORf2ime\nPAL/qSZzFAwpu06iGdQQJ4uQj7Kg1/x0+5hoKrjWweSPO7SUtPrBzvocevGk\nlShx9xjgfGWV+6hvsNyMckzy8hk9qcvFBmVjPSWhhxbIFlpt79PcbUhKhCrK\nAOV9b4rGDlUNGoqqF+T0fCDrHQcHOHmBiuhnRVAL6DTmu8x3J25JOUrVs//F\nT9o+\r\n=P0Eh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "files": ["lib"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "8.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_5.0.0_1527079835780_0.3314002299884877", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "parse5", "version": "5.1.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@5.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "c59341c9723f414c452975564c7c00a68d58acd2", "tarball": "https://registry.npmjs.org/parse5/-/parse5-5.1.0.tgz", "fileCount": 26, "integrity": "sha512-fxNG2sQjHvlVAYmzBZS9YlDp6PTSSDwa98vkD4QgVDDCAo84z5X1t5XyJQ62ImdLXx5NdIIfihey6xpum9/gRQ==", "signatures": [{"sig": "MEYCIQDXCo+ASJ5SLlKuX53lD4JTjonOxfz6PuJjKiActagFtQIhAJOS8NVyveurgwCP7g3BWrWt2avwqkTDwLnvv7DwgH4h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbca+KCRA9TVsSAnZWagAAd6wP/0U7INo2cXFaTvFmFaUL\nHrjUijFYTWc3cV7m7bISRlg65YNxbHop8LJd5sqIQ3fZQxWQIwXDiZ99Yfbn\nj5qmSxguA1izqK+fCuaJ3zajrN69PQNblB1NuqrJ2I9DRfsA/YtDz8F00Q3d\na9lDMOz1XMegg5RqO36UdisDV9+zb0sF+Cr9eTvIyA3WV67Kp78Nub5tbNTa\nXD5del6k1F2ISbPMm2SLZWHYg6Pcx+Do8SEL123k0/+LuIkpfeOQrT3XqNnt\nyy6UBJUnUM7ql1xlICDj8pEx455yTh+88lRx5EG1EGJvDHuT0hLunsSTkH6e\n+ma1kLs84SK7nBrOPGl5YUWytLECDDjsey9JiparMraCRdIdRqBUGPC8geHD\n3EvMP/KKehliORH4vKmuD6fvmCuYdF4w88kItdD76n5w5CQxBhpQswpVTuRA\nLBRUzUPdPmhoSrNweezvsnwbMI1jajdlgz9QnuIV9gYze/eaHLBF5TAQbnOO\nOP34nL8trpxUGEiy0v1AFSiK98UB607Y3+p5gOm1QtJDMudn6cZ6VHhdamvH\nMaG/6ri/BQzwzX95ByV5ZFQ/cC02VkLE+iQNWdVnyBrIrTX0MqHiaYdJFiPx\nfE1SgisJwLv/fNAGTsVgHmVEOtqgO/DG+FvXNqwygOiZi6lDULi+hQ1gSMEt\n54Db\r\n=IdmB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "files": ["lib"], "_npmUser": {"name": "rreverser", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_5.1.0_1534177161517_0.8100876604392573", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "parse5", "version": "5.1.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@5.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "f68e4e5ba1852ac2cadc00f4555fff6c2abb6178", "tarball": "https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz", "fileCount": 27, "integrity": "sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==", "signatures": [{"sig": "MEQCIANgn5qqSrYfWpcpw2RMFzFsIJTjBS9C0uAaSRcCU9UfAiBGHmR0ZmXuQyYF7YYIgykOSAbeX6UMTNrKLOF/mVkRSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwqKkCRA9TVsSAnZWagAAO8AP/jlUN51zwvyh66Q14h5t\nPLoLuKyniu97tyBmNo6cThXiVbOrTRZPajdSNVWw1rCXOmTIpZqStoN5Iq5z\nz8eKUrkL2snSMi6sGXTJR26vu9oAvYahviOF65Ry0gTtZ1aNEnMNzVPeJ4J5\nNGw6ZM72WqQ/9HnPJ7wv1iF9NBHarcFJlC/9DIyk01pbYRfHwwIFwSE6GAnA\nBN2jbfjuHLH3XQ1j7dyVLuhYSXDDyyvfH4vZM85EexokH3Qd4Zxw3MyPZsfP\n0MwL8jpEtsoJejiOuySIUGXRyOtqVi2e6b9oYm82BZCoTNkAMtx4p1YA+H4I\nuwOdb7QBR/TqE+X9e45eqJU4VoId5gt+OTroXQpbvhxpvuIqkf86ilmkEDww\nKjEuxI49fihMHxhnIHDYxwJkwfyHAlajkA3QCTL3jxVqI3jyi5aD5YlwXPFQ\numuMuI2uOACc15eEWhgURAFY9BFv9b6Hk99sQfiwOIq4RtH/lGYwlzy/XC0n\nyeGtmSihBl0lsIkp1dT2R7VFhB6VdX/YbYmLaX/RVpt/4pj2yDsIuvJ7ewZO\niHzVTcYwTY+TYckUYYK+zbBc2ZO1pjNpkBTcrDp5B+Nv39fbHQfVLOj87bKw\nAZAnXEUnYITnqc2H08oX2JgCQCHksLkA4jTfIAtnphO7kzdlHEMpmJS2jojp\nYyJ+\r\n=LO0U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "9c7556ed05e4ff4d884ab2447e27ce3817c42e79", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "lerna/3.18.3/node@v10.16.0+x64 (darwin)", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_5.1.1_1573036707825_0.6115376841727742", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "parse5", "version": "6.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "d2ac3448289c84b49947d49a39f7bef6200fa6ba", "tarball": "https://registry.npmjs.org/parse5/-/parse5-6.0.0.tgz", "fileCount": 27, "integrity": "sha512-lC0A+4DefTdRr+DLQlEwwZqndL9VzEjiuegI5bj3hp4bnzzwQldSqCpHv7+msRpSOHGJyJvkcCa4q15LMUJ8rg==", "signatures": [{"sig": "MEQCIDd//GSdyxUUl7K/HN66Y+dMIJTbdQ8xsZvN35ZHUqReAiASkGUL7iQT+Fp1G/tTPl/7BtVtBmBYql9CGkEJdF6mZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJel5BhCRA9TVsSAnZWagAA4SoQAJWSvT/8ajGYE5v+Y3L1\nk2x8xc1c/kDvCGShRn212JTD26YLM7MA4R+IjIvhdMQksPfrz0lQdl7XjoSv\n/uMBjJSiiifTYGlOJRS2V7kA/Ibf6nGnz55jyaBe44WvOPH4vn3pXWXxACLQ\nJ8/6SwXLv+NG8rHpgiOQRNL5hWz6CZMH8mO1VpblW8RjlTLuBEpesRbGQ3G+\n1jB3h5hSiNpqU3wPfFKBTsEJpEkpqU1lhTd8y7bvdrXvQtJ3CmdMvBuKzIe6\ngnS4zmSOjTv9EiJfUn3Wh4N+HcUgurnlUGq1OUW+C3KfCMFx5Ll+npr3Pevl\n+53dmG70dabba02du/8W4/qAFVk8hOMIBCYCdWU1BnxgZbUpCr9C6pc+LvDI\nyFffE8l2fPJ2wzTgRtZ0YXoZxC7sLpDOTeeT9hZlOPCYuRiphqtbLdYIiI4q\nQ61DiP9nKQBWkAmtvST+pxBziu6D2drFrqU/HoDUlROXjHeS50AGj4WBsN8M\nDDaeNvqgDADlT8iCwjHVy/KgHVsjhVsUj4Ntu1CcVzf0QnIzjTCbebqRyqwO\nlIvgnENOFbCOpp+FNPbxEC8urRyHAFfxvfsO0ExK8T77U6elfrZk6jF3XUsO\n72HEBcw0FD7LkvkoR3j5IV4uvqAZxhkreIYYH9K1v9mJ0+w5Reeuxh5vVbhS\nUHiW\r\n=+x7S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "0c260f942dd40fda586caf98eda3883ad3035172", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "lerna/3.18.3/node@v12.5.0+x64 (darwin)", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "12.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_6.0.0_1586991201144_0.31578510387945236", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "parse5", "version": "6.0.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "e1a1c085c569b3dc08321184f19a39cc27f7c30b", "tarball": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz", "fileCount": 27, "integrity": "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==", "signatures": [{"sig": "MEQCIBD7mWdX4DsnNtYzRJX/kKAGG4yW19dQtup75WoqaY3NAiBJ5NQZAX2QQkjJ3Cro7e+5YPmyFKdHJ0r/X6zkajfnYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGvufCRA9TVsSAnZWagAA/cUP+gL7ARpMrXdCRJjyWbI3\nS8JsPOB+jaRS8Pk1sopIyF3MVFN9UiB4O1wMdBfnQHdwpEa6OYeyOcRCVhRQ\nUvEqbxl6myTJbjbzD634xtXWGkfGhvNsYCoAOV6LzGSm3kcvU2pD9U9D3Swd\nF1YpXJKGeyDrnhx5A+Br+ZbdFsRpO8fg6wsY6Dn2ME43pndeXstLw8/QTrqy\n5VqdW07Z+dXBWFqU+hrxC4agADn4TbLc8p7yZYPvz5uhuhD3++rnufxdPtkR\nFeFDquzRqw30KwIFv5mkIPDDx5SsU1zy0Dmf15pX7FNAsinfNWE4MC6HbYxy\nzStLRFZjIoWg0xOvmBIB7IIYVZzeNONTEdDUs5qfdo7HvFWwczhXWxxYldm6\nCQxk5PdPo4bDiX3NiZvuAAalEllQgarSM5nFEDkiLh7e32vcjWOyPINttlx8\nvQEe3vBu1dHbFZatwDuIBUdBIPIVf2/eQ+1tbseU6s28HxhqkUXs0jHAYI8W\nXG3p/eBZ6RDnO3aeyaodIk4tLPkH79EBb6gPRbl8sp93NB8YVdbMlnKJWWYC\nJO6uQVp3gGkWotyNE8F5ZJhivdK+7qbYNP4FgKZhHAVjhYeXP3DrXKsTLZQ4\ngyNYvyxwJnzBV+UfEIiCj42co3XIRTfpS48hGcPZzg4GZYu9Wf1NUFVffPUf\n4fZh\r\n=2dq4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "37227a3429584903cbd1799dade995266fc2dbe6", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "lerna/3.18.3/node@v13.12.0+x64 (darwin)", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "13.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_6.0.1_1595603871193_0.15343657328280247", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "parse5", "version": "7.0.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.0.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "51f74a5257f5fcc536389e8c2d0b3802e1bfa91a", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.0.0.tgz", "fileCount": 64, "integrity": "sha512-y/t8IXSPWTuRZqXc0ajH/UwDj4mnqLEbSttNbThcFhGrZuOyoyvNBO85PBp2jQa55wY9d07PBNjsK8ZP3K5U6g==", "signatures": [{"sig": "MEQCIBWESYRtPBdWN3ddxA4b5KpAb5BlzGz+DGcvXSe+yhS9AiBlMfk7wLXMUPAV3kKnUhWp/B+51NquISiBPIxLy4tdMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 699220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX+uXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCKA/7B7i0l7O6XHYHmSvGYwlKiTX0amj+L4l4ccwVJh+Kq6ixAbeQ\r\ntLWq+YKpn6EXevIS1ydkAe9adCKpsoVlDIBTnxozCPul3XFq2qPN2Mp3B4NZ\r\np1YnWY8O2xn5Gmorsn+w5jO+vgcrNTSc/jGJoyANrHqSO91EyO4VPXgsKlrl\r\nhiuxQ51iuokiRKC/KbYSl7lDMuFHFnB5YsGEMwCLvQxgew7KXS76SqJVVEXW\r\nKLtr+gFhkmU94jkcqjvESXpf8hywwetoJJb8eUuAkEupcFImAPdYVlX4X4ia\r\nLlU6Sw4Ni09H4DCDE5xGU4vH+DPjPWXsQ60/3YaYUE/p8zDOYheaxXKd/v1C\r\nJ0GkQ4VtZdheaKu4aYjUvDHTrOiSvRasUeJThLaMSWJoarlxKTW6NN31fJcq\r\nmienbIHxLE194txhJZFNtH9YkRjzCQVc1KGSNmRyIRSXgGrDZkIyZThwiVUG\r\n16rTdY5oEJhxdy6J86+RzlrGfpSF4UdkQZALLcDZQiAJTUtRjl80q2nklm/y\r\naOYKzq29ai+ZvjjW2n9AcYuk6VVK+M0dTnHD3euaERQ2/vXUQQf2+ncNmAwx\r\nY10kd6ZPE+T9FgLLJlI7Ymw2CsLggLG790liHHv36buKRu5q9/xSSIkGdaeI\r\nH6Q9fQ+KTnSBtVf56DatWaKLd0n0dTxyt2o=\r\n=ACNL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "abec4c2be40f09dc4e3d0fd6a64eb10bbb24cca0", "scripts": {"build:cjs": "tsc --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "17.9.0", "dependencies": {"entities": "^4.3.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.0.0_1650453398923_0.6246359176389757", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "parse5", "version": "7.1.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.1.0", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "6c15741dcd187730d0ce0a41eeb95e6cb587cb93", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.1.0.tgz", "fileCount": 63, "integrity": "sha512-jo4pIv8LVI1IO2QxismoCv/+qZC6V3VSxOHqEQIpm5kDSzJNLNIVUwdStdUnezexjwdZXgYkxP5nanlZgfcCHg==", "signatures": [{"sig": "MEYCIQDXxelD1igXG8p19142XC04nCXLl6z8vbDPbzvARlfRSgIhAL8vsOZf5KN48+pF0xI0edKObr12GdO3bcQPJNGXo4hP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 697986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEiSCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP+Q//XEaIJZPYhp2a2IOq33py8pkvDOhU036wZ4MbV+zIjeIJkKBa\r\nOQoz5EOB6oeCkF8gVqOXpFArNsdE1C0mLljEBoR2bV+lp1RfY+kPKDycwtNn\r\nLieU5XVKXn9cQCpX5F4PWcoSZ5L9sEgZXBgrGb8lU0YWZMi+6G761EK3M2U6\r\nLQWIU/koOko3/TZ0CjLYEpZ7kj/MzSRB2+uSlU4QiyiRuPdqzwxM+9Zyn6az\r\n1Mq3KIDedQhZg4Km8pvmhFy2KpoKWIxndsO5yaAHUKKvFNTAYlv3y2viO5pz\r\nrzYclYMgr0fdXRTpoxEtlLdedyZOr0CnRbvAp8x/p2aEpZm8AAcJFY4KGCoG\r\nEyH42W50It7bT/HG3QvnFSdOkvUt5LDRs32CHOX1bxzacHLixhxEMzodIvDh\r\nchRCw6wa3p8YLzTjlB0y7DVBzlTcooag0l9kPYZ++qaySfWLuPKyeG0USNQS\r\nvdrfSIoWM1DaYL4v/OsAF4eG7ZW7SJhtCaIHfISJ/M6GQMmp9Zc5ozmgPRNu\r\nAnyX1TuMgcf5Qr+eLKEm8nX8BtnoO6O9OfLKqsS5E04esfH1eiYnq9PI3n5j\r\nmT6AdwIqQ/sBXzRk7S/KD0bDho6QvrRX54swjCvPrUYCNew/m9b6BX7XH4Jx\r\nmWbhQklo+Mb4xLI25W8xN+706cgUChgGXhw=\r\n=in2D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "a4806247f2a9e61ba66a1b8ef94afd2b747399a5", "scripts": {"build:cjs": "tsc --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"entities": "^4.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.1.0_1662133377981_0.6596724193120107", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "parse5", "version": "7.1.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.1.1", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "4649f940ccfb95d8754f37f73078ea20afe0c746", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.1.1.tgz", "fileCount": 64, "integrity": "sha512-kwpuwzB+px5WUg9pyK0IcK/shltJN5/OVhQagxhCQNtT9Y9QRZqNY2e1cmbu/paRh5LMnz/oVTVLBpjFmMZhSg==", "signatures": [{"sig": "MEUCIAP58ZE75XeogwoyDLjY6bjk0b2ANbnZq5B9EaVL6T+PAiEAlZkYAEyOYzLfj2AiSSF3eDQ5L0CuO/c8CirlzNzBFi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 698039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEjlfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnVw//bMH/uTESLFiOJfmyfmOjcrfSZgWBqUt+jBlxshuZ5dFBRsug\r\njor2WQOgKbSqNlrfmrIXw8K3QnkQjjaue88dMrXUgPsqtl8+UK+WtnYIB7hA\r\nRNuG6+lvGAVuw7ybx/KKFrMLl+kGNGWnSBoeyGremsxeoCcQhHKbHE/r7ivD\r\n3geW8POepVZ01vNQB4y9HhEHDnpr8tkW+BI/DLpJYQMQEyGYINsVYz6CXyeN\r\nNkVZiR8UN/WP/sQK0lm0dk1nYLOW0I3k7NajUd68bFiUWVZNNTdEUBxJFXH4\r\npK8SesbawNctM8HMWO9ABw4YRlFP0QM5pgOZcb7JBlPtQffEK/U1mOayZEk7\r\n8WpbgWALAXRqFnfbHvqG1RyVYA3IfrjJTKJ6VYXUL+ZlwSVHeDtWUnNLKGYf\r\nedfAmqc+5KNum6i3gYBSJhUaiiMWX2RXIWs5d7brXDoOCqxYuP1LmcSbHzMs\r\nFAbCYAEV75rKFQgQO4GpeY+GfGxpA7aKnfTqrHBKAOjnijM/Y+Z2BfRNZQRJ\r\n+qgyvasX7GOkP9Ewz83ngEZW1gEInYhtT5ETsqO9zWtqIp76jFY9LFdv+xFh\r\nBh4T4sNNWG+A9GHPpAufu2YNj6goUiHnEhahg4dJ2y5i7/RT9fq9OOElMZ+g\r\ntA8IiWIeQ27ghPVtiq0Pd81vyXuHCbz+JmU=\r\n=QwQy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "d867cdccebb05acd9088948e911f6da1b018284c", "scripts": {"build:cjs": "tsc --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "18.7.0", "dependencies": {"entities": "^4.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.1.1_1662138719018_0.02837814956977036", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "parse5", "version": "7.1.2", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.1.2", "maintainers": [{"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "0736bebbfd77793823240a23b7fc5e010b7f8e32", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.1.2.tgz", "fileCount": 64, "integrity": "sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==", "signatures": [{"sig": "MEUCIA4piUqkSWBcy+73mHzlgdPrcMqBSzUv5hGgqsh35/O3AiEAihzVTOWPFrVx9qIjvZUUoRWodvT5Zr8i8M+a6HWIYqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjeqLWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSaQ//WbgQ780StHfDAoCjZSpq2UYfI0bxr2ac/lFGaId3706J7/Ox\r\nWosVznwU5Sns/pvbjVviNRpS3ESW0kibl8XPDleyDgJwe85niIJY/fLPp6HY\r\nQ631KZlDhY1o1jco/TrAiIhW/F0ya3WGd8gVgiBtmEm+mLEsbB3qAXwAGSYC\r\nlYKH6ghyQkuVeBI+zBuwIbH5ZDMOlD/XV0ym6+HoiLIfU1ISgEp5eCTPUWMr\r\nG1Iw721tx6px6fvMQ0zrK8jALjljny33Wg12Up5lvZcPTdIMaRBCFrZBc1+W\r\njFCKTaFGeHAe+MQspHXqmdVHk9hiURH1vWJePOH7ss2zJV1R3IdR9qut0ajn\r\nB66/rT2+CbRGqxINg1wOcFZud5E/XZLwKgpGi4aRBlhh9cjiBSxlPQqUPG89\r\nZ1JJ7KAKTrN4XP7sXaSgjM5235+OJD34d2ixHQZwOCcGwq75Wu07ejWIwlEo\r\nDWJ5YY1RFpSNKjDLnl2tj3rW9UId4KxeN8QJ7zZ7sY7u6FNs5G1BRU5qHskW\r\ngz85rrP3AN2BCWn8MbcgOtv//OXsl8vpf4soKH7m4+svGbYsSpbQgvLk8mmS\r\nS5t1MKJq5qC4lLCHNfsvNKp9kEMccs2D2Bzb4t8JDkJpUH0F9xcSuFIfTyiF\r\nc1LXqNvr3VX1m6hDhOsKmfTyIQnimBIjZeo=\r\n=bPo4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "1f34d804e5f4719585667e6284921534a465691b", "scripts": {"build:cjs": "tsc --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"entities": "^4.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.1.2_1668981462311_0.28777931479600904", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "parse5", "version": "7.2.0", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.2.0", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://parse5.js.org", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "8a0591ce9b7c5e2027173ab737d4d3fc3d826fab", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.2.0.tgz", "fileCount": 64, "integrity": "sha512-ZkDsAOcxsUMZ4Lz5fVciOehNcJ+Gb8gTzcA4yl3wnc273BAybYWrQ+Ks/OjCjSEpjvQkDSeZbybK9qj2VHHdGA==", "signatures": [{"sig": "MEYCIQD4QIO/UlweoZVf2VZ9c0OljIdDZwNfDky0i4aOEICftAIhANZTQCr5dL4JHh1vrEjOZrdKdS5dLczcIvoAz9UpaxnJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693361}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "beed7d7e266d8b04cbb555695b23389a475c7914", "scripts": {"build:cjs": "tsc --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"entities": "^4.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.2.0_1728725383155_0.0419699794713162", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "parse5", "version": "7.2.1", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "author": {"url": "https://github.com/inikulin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "parse5@7.2.1", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://parse5.js.org", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "dist": {"shasum": "8928f55915e6125f430cc44309765bf17556a33a", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.2.1.tgz", "fileCount": 64, "integrity": "sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==", "signatures": [{"sig": "MEQCID2o5fBcbiS4QbbPUUB83uta+ygA70luOOaps5wKKRN/AiBxOUR7DOnW191YgZ7F36MYZa1m8IoIJQzFTMiWrpuLnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 694639}, "main": "dist/cjs/index.js", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "funding": "https://github.com/inikulin/parse5?sponsor=1", "gitHead": "8822d8dcb115dbafec49a724fab53d55092305b7", "scripts": {"build:cjs": "tsc --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/inikulin/parse5.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "HTML parser and serializer.", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"entities": "^4.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/parse5_7.2.1_1730110943409_0.5451151418187632", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "parse5", "type": "module", "description": "HTML parser and serializer.", "version": "7.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/inikulin"}, "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://parse5.js.org", "funding": "https://github.com/inikulin/parse5?sponsor=1", "dependencies": {"entities": "^6.0.0"}, "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "scripts": {"build:cjs": "tsc --noCheck --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "_id": "parse5@7.3.0", "gitHead": "d34155ca54783a382f6b5ecc1803c770553c19e6", "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==", "shasum": "d7e224fa72399c7a175099f45fc2ad024b05ec05", "tarball": "https://registry.npmjs.org/parse5/-/parse5-7.3.0.tgz", "fileCount": 64, "unpackedSize": 695873, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDRY8XjVSuEbsWQGNFp6XX94SGLYiswz0ZysTJJ0+IaHAIgXa3mPnb63A7KGC1mW7E5pBxlGHRPysneJl38Zz/HRnc="}]}, "_npmUser": {"name": "43081j", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "43081j", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/parse5_7.3.0_1745341473452_0.7667762666951292"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-03T09:19:28.186Z", "modified": "2025-04-22T17:04:33.848Z", "0.5.0": "2013-07-03T09:19:33.333Z", "0.5.1": "2013-07-03T10:20:57.843Z", "0.5.2": "2013-07-03T13:48:17.234Z", "0.5.3": "2013-07-18T14:00:54.334Z", "0.5.4": "2013-07-18T14:11:52.609Z", "0.6.0": "2013-08-05T12:57:45.429Z", "0.6.1": "2013-10-17T13:27:26.286Z", "0.8.1": "2014-02-28T14:13:12.690Z", "0.8.2": "2014-02-28T14:13:41.420Z", "0.8.3": "2014-03-03T11:17:35.305Z", "1.0.0": "2014-07-07T08:30:14.877Z", "1.0.1": "2014-08-05T13:38:02.431Z", "1.1.0": "2014-08-18T15:32:52.610Z", "1.1.1": "2014-08-18T15:47:20.380Z", "1.1.2": "2014-08-19T06:58:02.338Z", "1.1.3": "2014-09-08T09:05:52.437Z", "1.1.4": "2014-09-18T07:23:10.711Z", "1.1.5": "2014-10-18T11:33:24.740Z", "1.1.6": "2014-10-18T13:20:36.587Z", "1.2.0": "2014-11-06T11:43:12.409Z", "1.3.0": "2014-12-24T12:03:23.173Z", "1.3.1": "2015-01-21T19:50:33.733Z", "1.3.2": "2015-02-03T13:12:53.994Z", "1.4.0": "2015-02-24T14:53:54.752Z", "1.4.1": "2015-02-25T08:37:39.691Z", "1.4.2": "2015-04-08T14:44:12.399Z", "1.5.0": "2015-06-24T13:32:35.875Z", "1.5.1": "2015-11-28T23:01:56.405Z", "2.0.0": "2015-12-07T13:40:01.100Z", "2.0.1": "2015-12-11T10:16:18.861Z", "2.0.2": "2015-12-12T17:23:24.805Z", "2.1.0": "2016-01-05T18:11:20.283Z", "2.1.1": "2016-01-14T08:42:13.118Z", "2.1.2": "2016-01-15T14:42:21.580Z", "2.1.3": "2016-01-19T20:13:11.235Z", "2.1.4": "2016-01-26T11:06:56.243Z", "2.1.5": "2016-01-27T13:35:46.571Z", "2.2.0": "2016-08-11T11:36:48.278Z", "2.2.1": "2016-09-01T11:18:41.462Z", "2.2.2": "2016-09-29T14:00:51.728Z", "2.2.3": "2016-10-19T12:19:01.042Z", "3.0.0": "2016-12-05T14:53:02.401Z", "3.0.1": "2016-12-16T09:50:02.296Z", "3.0.2": "2017-02-25T20:09:59.546Z", "3.0.3": "2017-11-05T01:03:43.353Z", "4.0.0-test": "2018-01-13T19:51:50.101Z", "4.0.0": "2018-01-13T20:42:08.539Z", "5.0.0": "2018-05-23T12:50:36.240Z", "5.1.0": "2018-08-13T16:19:21.658Z", "5.1.1": "2019-11-06T10:38:27.963Z", "6.0.0": "2020-04-15T22:53:21.311Z", "6.0.1": "2020-07-24T15:17:51.325Z", "7.0.0": "2022-04-20T11:16:39.188Z", "7.1.0": "2022-09-02T15:42:58.130Z", "7.1.1": "2022-09-02T17:11:59.218Z", "7.1.2": "2022-11-20T21:57:42.503Z", "7.2.0": "2024-10-12T09:29:43.511Z", "7.2.1": "2024-10-28T10:22:23.641Z", "7.3.0": "2025-04-22T17:04:33.658Z"}, "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/inikulin"}, "license": "MIT", "homepage": "https://parse5.js.org", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "description": "HTML parser and serializer.", "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "maintainers": [{"name": "43081j", "email": "<EMAIL>"}, {"name": "rreverser", "email": "<EMAIL>"}, {"name": "wooorm", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n    <a href=\"https://github.com/inikulin/parse5\">\n        <img src=\"https://raw.github.com/inikulin/parse5/master/media/logo.png\" alt=\"parse5\" />\n    </a>\n</p>\n\n<div align=\"center\">\n<h1>parse5</h1>\n<i><b>HTML parser and serializer.</b></i>\n</div>\n<br>\n\n<div align=\"center\">\n<code>npm install --save parse5</code>\n</div>\n<br>\n\n<p align=\"center\">\n  📖 <a href=\"https://parse5.js.org/modules/parse5.html\"><b>Documentation</b></a> 📖\n</p>\n\n---\n\n<p align=\"center\">\n  <a href=\"https://github.com/inikulin/parse5/tree/master/docs/list-of-packages.md\">List of parse5 toolset packages</a>\n</p>\n\n<p align=\"center\">\n    <a href=\"https://github.com/inikulin/parse5\">GitHub</a>\n</p>\n\n<p align=\"center\">\n  <a href=\"http://astexplorer.net/#/1CHlCXc4n4\">Online playground</a>\n</p>\n\n<p align=\"center\">\n    <a href=\"https://github.com/inikulin/parse5/releases\">Changelog</a>\n</p>\n", "readmeFilename": "README.md", "users": {"285858315": true, "csj": true, "dralc": true, "eerne": true, "jarib": true, "slang": true, "nestof": true, "ziflex": true, "tsxuehu": true, "xtx1130": true, "cmtegner": true, "poosanth": true, "qddegtya": true, "ruyan768": true, "tkalfigo": true, "wangfeia": true, "allen_lyu": true, "imoore4ud": true, "sunnylost": true, "tetotechy": true, "wolfram77": true, "xumakjosh": true, "leizongmin": true, "leon740727": true, "shuoshubao": true, "codeinpixel": true, "flumpus-dev": true, "henryheleine": true, "stevenvachon": true, "warcrydoggie": true, "shrimpseaweed": true}}