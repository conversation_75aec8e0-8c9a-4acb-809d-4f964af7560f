{"_id": "whatwg-encoding", "_rev": "14-62b2344f16a0bae4a043aedddb3c631a", "name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "dist-tags": {"latest": "3.1.1"}, "versions": {"1.0.0": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "update": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.13"}, "devDependencies": {"eslint": "^3.8.0", "got": "^6.5.0", "mocha": "^3.1.2"}, "gitHead": "848161b330020027bf94f30502dc63e02df9b6e1", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.0", "_shasum": "f6a582bafe28d5bbeb5aac4829871e96dc0d6b97", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "f6a582bafe28d5bbeb5aac4829871e96dc0d6b97", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.0.tgz", "integrity": "sha512-YQvfP33L0p55JvQjtVzQfXhNW9smv41Rn6LOtsUWGGmGu/YyTV1d/hu1RIQbOGnp1qtA7tmFGDfki4j/N0UeAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGcH30wsQwh9OrxSCwA49zKQcl2Slbtht1RegRqBP0R8AiEAxn2wohjxrp+gv+EXUL9C5AW0WNe8IfdlvKaOI0Hd9cI="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/whatwg-encoding-1.0.0.tgz_1476583625739_0.5463150741998106"}, "directories": {}}, "1.0.1": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "WTFPL", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "update": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.13"}, "devDependencies": {"eslint": "^3.8.0", "got": "^6.5.0", "mocha": "^3.1.2"}, "gitHead": "22ef4432e223dd4736651b1db7c43fb532352ea7", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.1", "_shasum": "3c6c451a198ee7aec55b1ec61d0920c67801a5f4", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "3c6c451a198ee7aec55b1ec61d0920c67801a5f4", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.1.tgz", "integrity": "sha512-vNmnbXn9p8aTkdOFh5lVbEyT1xXFFCDvrGIJyAnKdWYU1F4HN/4OgP7j3QVl4EIk7bhMg6pIopJexnKeWCisxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkaU0Y5TxQeTFd6L20hxudY+jQQQWXUwuZK8gzBFeW3AiEA30HrSVImZXRUBkrf/wa5PhcwJjcGGm7uCid7k8bmaaI="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/whatwg-encoding-1.0.1.tgz_1476585270143_0.7966783917509019"}, "directories": {}}, "1.0.2": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.2", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "update": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.13"}, "devDependencies": {"eslint": "^3.8.0", "got": "^6.5.0", "mocha": "^3.1.2"}, "gitHead": "269bb00bb1277784cbfeccbb82aff4727ad11af9", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.2", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9WQ+6BvuD7A1vaGMqMjyR5zhHnR/VXKrs2WHobV/YCfeKXKEk0SJbgwg4kjdpRRrenEQbYwZ/P9vQAVUEVAzUg==", "shasum": "bd68ad169c3cf55080562257714bf012e668a165", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEzzPTRTQIUqIvHEhzDL9WrQWmWqErAjSZxE306HgONjAiAIGASOsxK5QoVR5OXUukmr78xdQgM9dFxveWscyfMkSg=="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding-1.0.2.tgz_1508717616198_0.12424216908402741"}, "directories": {}}, "1.0.3": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.3", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.19"}, "devDependencies": {"eslint": "^3.8.0", "got": "^6.5.0", "mocha": "^3.1.2"}, "gitHead": "04a110a0b202ecf435aacf8617fd4a568e85964e", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jLBwwKUhi8WtBfsMQlL4bUUcT8sMkAtQinscJAe/M4KHCkHuUJAF6vuB0tueNIw4c8ziO6AkRmgY+jL3a0iiPw==", "shasum": "57c235bc8657e914d24e1a397d3c82daee0a6ba3", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFBN2w5+u4Kc+/TVi4SJ7Ul+DYbk9m12phGKzrzec3TRAiEA7qRMgiVOaUOMloGCsAG3LyuomilKG968xWT2M2iKdUg="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding-1.0.3.tgz_1509241494617_0.47145224804989994"}, "directories": {}}, "1.0.4": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.4", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint lib test", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.23"}, "devDependencies": {"eslint": "^5.3.0", "got": "^9.0.0", "mocha": "^5.2.0"}, "gitHead": "42904015c67e254c378139a75953130cceeb5bbc", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.4", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vM9KWN6MP2mIHZ86ytcyIv7e8Cj3KTfO2nd2c8PFDqcI4bxFmQp83ibq4wadq7rL9l9sZV6o9B0LTt8ygGAAXg==", "shasum": "63fb016b7435b795d9025632c086a5209dbd2621", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.4.tgz", "fileCount": 6, "unpackedSize": 12341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcM1vCRA9TVsSAnZWagAAolQP/ij7Mnlaz0uCFaArbvQo\ndk+LK0cHNppxsBLwfpGuvCSYeg6liGs5PpPqGW77ruySc9V8XZdBQFHe2uQW\ntWwj5AvKDlTmz35Qd2HVUZzAVQ9TF3lw/5XvB/8E8GrcvVzJgDb9WVOl5zIn\n+eMLdYWw3YJ7AgErQlekEy4KbDVZYFcp4uGZVdGLFHOmtmMfvncWx/KOeAt+\nA06JEuFHNl1nfq7Iob45qpuc0RaUGQJSmlKqMfXJRfNEs2fZ0SFMKAjKRuXW\nk1k7bho4NoU15PccoURvfsygUt2qz3ITX532KfzvVCzY963PLNb+WOYagW7S\n29gctAlAkayONYaeTU4WkMGL5/XvOlgNgib7YyWmU/PmqNRHRtQRrNGYahwR\n08qbZs+Cul6n5Vl4F0MPAyT7aMirFMHJySXsHFG3YctY9j4Rxa96/T8USzKF\nrIuRxMlXNVMNi8GheSQnsskWiTJMmGCJ0vUwhinkRCKhkH0K0vaTANwyJ58z\noigYrwuAIod6bGdO0pBzhA82unx5vl2qTbcBGOqz3DGiotqFJkCTv7I8zCZ6\nFNN4DJaJ2nKl7pfGuqAmDHBrxZ3iDW3U2TFFLrVxEiJDC2Z3ze++nAH11Tso\nwnFKOVHSGwUOo+a+nwW0PPqZqr3cxK75GTwD5ZJIGiVHqtY7FmcPgbfAffbd\nT2zb\r\n=TnGd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAk0PC/vL8A2fTQmbZxhUj4fF5WI9fpkplJEOBxS8YyoAiBzPknp1mvHywy3qdTXF3gnmNCOH5TzOLQvqbFRiVdSmw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_1.0.4_1534119278902_0.5422639817294805"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "1.0.5", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "scripts": {"test": "mocha", "lint": "eslint lib test", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.4.24"}, "devDependencies": {"eslint": "^5.3.0", "got": "^9.0.0", "mocha": "^5.2.0"}, "gitHead": "04c383e309e0d3f198e19b050295995028b6cc48", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@1.0.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==", "shasum": "5abacf777c32166a51d085d6b4f3e7d27113ddb0", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz", "fileCount": 6, "unpackedSize": 12341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsE8rCRA9TVsSAnZWagAA6pAP/RS0eY8EOd5eJylAFtGv\nN2T/qYDvss32MimfZRj0vJaNygsYoCT6w3d1puF5XXYq65rgiKyDKIyp/pjg\nA2/UpbCNHLzxjDxYRUdAAJ5MJ2Ca/5EfQMH6+BVZabzQCuOX2VHSlO9lxnZ6\nEaamRs1qvHgL1V/jx0hYPPbTS1aEg3ig5HNu80+gi+QLqO4FnH3hCQ5cdXgY\nL90LeGEZQrxuyNrGArm4zPXmAHYDHQoZZ5N4A0nvBVRiW8YcSK/G27EM9Gi6\nIgV+mu9NurXdGasQ5QyZuIlt5qLbA2vu5D+bH2tKFd4rhg03MCR8fYs8TRcV\n2yaaT0djRJ+D9ftSb152G0Sa9V78vLiLUwuCDsarIm0As9HXJOQK9PrjXqW3\n7kmbS3PJyMbBnYFdMq9tPL5hMMzdcolt2Xz10KHt0zGCGQa7bmFsb7sfDvjE\ndk3QHm3h28LDSa9eo2BH2y0d/z9O9fyWFuZtCQNlvBF8nr8t2jchWB3KNJSQ\nrAJVubg3mwZrf96sPpJKQEcvrfgp1rSTWLrEylGVIx91Wr8OjlauSi2dl0PO\n8MQTMOaRhoiGMggpy/nZtNDZLZP7lbDDx181Qxjc9gmXRChQ2g7q5FnAPaRr\nTtQaFk/PSZ4Xah1JGRCa9W0gL+2kWl2vzpHddBBLVgNqgkgWGYJYCFe0BFNh\n/ibz\r\n=jpru\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH88v8EgWsSn3op+uWQ3vtaEmcz7dt4Ey5Em59aKDTFdAiAfiG2PS2cINPupyscc5uPN8szk7HJvmUUOBmYnEoOsPA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_1.0.5_1538281259420_0.48990278300644907"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "2.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "scripts": {"test": "mocha", "lint": "eslint .", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.6.3"}, "devDependencies": {"@domenic/eslint-config": "^1.3.0", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}, "gitHead": "8d9446b437a613d6337044b60e2d1f2267ea1ddd", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_id": "whatwg-encoding@2.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "shasum": "e7635f597fd87020858626805a2729fa7698ac53", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "fileCount": 6, "unpackedSize": 12840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPmXKCRA9TVsSAnZWagAAmIsP/jJIgz7s+kXxIDdn9v3w\nxJ0BFisFDUGHYNQPlUjoUgqXmhZXFhY+XwnSSMu4RxAjhxKnNBYsN71pS/Q+\nEVV0Qsz2M2DvdgQfVvtoQDLqNBcEF4v0QJu+RdjrGBhXrIncOyxkVIRJG45v\nDlsf5PvprQAYpShxkW14WivQZqGH+g8wx7tj2Vmgu6w//AQGyL6aPx+qNFN0\nje7XvJJ5AVCvzN657wGr4HYGM/Agg3IZ2ROhf17bNLDZHDNqsrN0GB/zg9IH\nJcKzHZ/Ylw0FN/aOosJ4WQTlhZ42KbGR9xV9fcWS1dxfhqapuYPspTLeJzzM\nEJ0Ryjs6QTNqqDu6S6KN44KI0/qSEiPqiCwONHvGo06l7ZSEH9aybBG6H/QM\nbTrgyEf+ocbOeAw84MaUkQ7xmN/AQSOX+cUYQOZgfkphUSnEuNz+v1ykoAVG\n6YiM9VAcsPbZrBqmqzksnY0qIuarNgg74wkt0Ui6woqzBZWM2T2HGvh8M+4a\ni4+bipw/6gg+r2f8YlgEKYf17naXLPQ/Zqj6GwUH4ZHIVc/gynQpheIpmSSq\nOjSxq0ZJ/G1uAGN4eyGkT3Zs5qLMUqqKXG3yV+zPvgPNPXQZhRDxHaEn2koj\nqQDu0JcYi4JXjL1eGj12bM0baJRFwfwsHFSg1g7MSazxJjfQ1FzbBRFTIzIc\nMaQx\r\n=Ng2T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZV9AGK5nlVZ8RHcTYsFRDcg97JkMZ75NtvbZwu2rpLwIhAK1RZr/hvSnBychJ4cTTvgpsrQwYd7Ll++BiZon+Ybjh"}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_2.0.0_1631479242718_0.11208519588071741"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "3.0.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "scripts": {"test": "node --test", "lint": "eslint .", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.6.3"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "eslint": "^8.53.0"}, "engines": {"node": ">=18"}, "_id": "whatwg-encoding@3.0.0", "gitHead": "85d1367d8da2e5558673ea8dd7b6e02942f250c3", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-88G7S/pRxgJ2Bwz/CSHDojeOmsOkX2arGulkxhgPCiqE/URAasedYk8Tgonb1GDq/viM0vQkKUsk1faMw6m15A==", "shasum": "ac73693dd2bed56708d62e8710f0a489be65e320", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.0.0.tgz", "fileCount": 6, "unpackedSize": 12791, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNgGijzaouG7ZZ4/jRSxUCEP0H/K3i8NB8+cHcU3HiWAIgO7JTwhJ7yD2dXo0RU7QirjwPpG2vhSp6Al652AOqXIk="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_3.0.0_1699364441828_0.25096566103214624"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "3.1.0", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "scripts": {"test": "node --test", "lint": "eslint .", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.6.3"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "eslint": "^8.53.0"}, "engines": {"node": ">=18"}, "_id": "whatwg-encoding@3.1.0", "gitHead": "27ffb33983f2d316a3a726fb2138c128767122fa", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-Cho2yzijT2fyoZ03zVpgoL68SXH+4MFH3H2FCth+DYawT9U7kwT0C4aK4Bky7jQMRbBZxgDJecMd7bDt/5/bgQ==", "shasum": "1c0a958db1247643b1b6c63bad3b8675d02def6c", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.1.0.tgz", "fileCount": 6, "unpackedSize": 13270, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDi4d/8MXPmSIQr1NSorB774+rS87gHo0MwDYzoCZLflAIhAP8USRgpSdDE3aZqp1MPCierwfYonqnPkRgiZIxvrYg+"}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_3.1.0_1699773546488_0.5979334566451937"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "3.1.1", "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "main": "lib/whatwg-encoding.js", "scripts": {"pretest": "npm run prepare", "test": "node --test", "lint": "eslint .", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.6.3"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "eslint": "^8.53.0"}, "engines": {"node": ">=18"}, "_id": "whatwg-encoding@3.1.1", "gitHead": "2231abe9ebd6e665da998eb57274d1e595020a2f", "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==", "shasum": "d0f4ef769905d426e1688f3e34381a99b60b76e5", "tarball": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz", "fileCount": 6, "unpackedSize": 13339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChcmpvLSWtc9Rwn6hn9VpNM/8paRJ1KE18DjgYFcOF2wIgJJoeYs6ltMh0R4K9WAoOffVsc68hva36Ec2P42c6ccw="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/whatwg-encoding_3.1.1_1699774161573_0.5510908684104276"}, "_hasShrinkwrap": false}}, "readme": "# Decode According to the WHATWG Encoding Standard\n\nThis package provides a thin layer on top of [iconv-lite](https://github.com/ashtuchkin/iconv-lite) which makes it expose some of the same primitives as the [Encoding Standard](https://encoding.spec.whatwg.org/).\n\n```js\nconst whatwgEncoding = require(\"whatwg-encoding\");\n\nconsole.assert(whatwgEncoding.labelToName(\"latin1\") === \"windows-1252\");\nconsole.assert(whatwgEncoding.labelToName(\"  CYRILLic \") === \"ISO-8859-5\");\n\nconsole.assert(whatwgEncoding.isSupported(\"IBM866\") === true);\n\n// Not supported by the Encoding Standard\nconsole.assert(whatwgEncoding.isSupported(\"UTF-32\") === false);\n\n// In the Encoding Standard, but this package can't decode it\nconsole.assert(whatwgEncoding.isSupported(\"x-mac-cyrillic\") === false);\n\nconsole.assert(whatwgEncoding.getBOMEncoding(new Uint8Array([0xFE, 0xFF])) === \"UTF-16BE\");\nconsole.assert(whatwgEncoding.getBOMEncoding(new Uint8Array([0x48, 0x69])) === null);\n\nconsole.assert(whatwgEncoding.decode(new Uint8Array([0x48, 0x69]), \"UTF-8\") === \"Hi\");\n```\n\n## API\n\n- `decode(uint8Array, fallbackEncodingName)`: performs the [decode](https://encoding.spec.whatwg.org/#decode) algorithm (in which any BOM will override the passed fallback encoding), and returns the resulting string\n- `labelToName(label)`: performs the [get an encoding](https://encoding.spec.whatwg.org/#concept-encoding-get) algorithm and returns the resulting encoding's name, or `null` for failure\n- `isSupported(name)`: returns whether the encoding is one of [the encodings](https://encoding.spec.whatwg.org/#names-and-labels) of the Encoding Standard, _and_ is an encoding that this package can decode (via iconv-lite)\n- `getBOMEncoding(uint8Array)`: sniffs the first 2–3 bytes of the supplied `Uint8Array`, returning one of the encoding names `\"UTF-8\"`, `\"UTF-16LE\"`, or `\"UTF-16BE\"` if the appropriate BOM is present, or `null` if no BOM is present\n\n## Unsupported encodings\n\nSince we rely on iconv-lite, we are limited to support only the encodings that they support. Currently we are missing support for:\n\n- ISO-2022-JP\n- ISO-8859-8-I\n- replacement\n- x-mac-cyrillic\n- x-user-defined\n\nPassing these encoding names will return `false` when calling `isSupported`, and passing any of the possible labels for these encodings to `labelToName` will return `null`.\n\n## Credits\n\nThis package was originally based on the excellent work of [@nicolashenry](https://github.com/nicolashenry), [in jsdom](https://github.com/tmpvar/jsdom/blob/7ce11776ce161e8d5921a7a183585327400f786b/lib/jsdom/living/helpers/encoding.js). It has since been pulled out into this separate package.\n\n## Alternatives\n\nIf you are looking for a JavaScript implementation of the Encoding Standard's `TextEncoder` and `TextDecoder` APIs, you'll want [@inexorabletash](https://github.com/inexorabletash)'s [text-encoding](https://github.com/inexorabletash/text-encoding) package. Node.js also has them [built-in](https://nodejs.org/dist/latest/docs/api/globals.html#globals_textdecoder).\n", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "time": {"modified": "2023-11-12T07:29:21.977Z", "created": "2016-10-16T02:07:07.293Z", "1.0.0": "2016-10-16T02:07:07.293Z", "1.0.1": "2016-10-16T02:34:31.658Z", "1.0.2": "2017-10-23T00:13:37.174Z", "1.0.3": "2017-10-29T01:44:55.546Z", "1.0.4": "2018-08-13T00:14:39.035Z", "1.0.5": "2018-09-30T04:20:59.538Z", "2.0.0": "2021-09-12T20:40:42.859Z", "3.0.0": "2023-11-07T13:40:42.176Z", "3.1.0": "2023-11-12T07:19:06.687Z", "3.1.1": "2023-11-12T07:29:21.747Z"}, "homepage": "https://github.com/jsdom/whatwg-encoding#readme", "keywords": ["encoding", "whatwg"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-encoding.git"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "bugs": {"url": "https://github.com/jsdom/whatwg-encoding/issues"}, "license": "MIT", "readmeFilename": "README.md"}