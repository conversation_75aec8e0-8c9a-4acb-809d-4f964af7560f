{"_id": "babel-preset-current-node-syntax", "_rev": "9-c76a3eac3ee90e66035a62be7ab3e205", "name": "babel-preset-current-node-syntax", "dist-tags": {"next": "0.1.4-0", "latest": "1.1.0"}, "versions": {"0.1.0": {"name": "babel-preset-current-node-syntax", "version": "0.1.0", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "744b43eda7a164e5f90ad416dcd9615e8b7842c1", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.0.tgz", "fileCount": 5, "integrity": "sha512-gVdIosHhVr8ZFaMo8FVOihafV0UOj26/v4A1etZAPy/XqrJO5dvTLfxGaIApumZ66n7AEkFy3e+SPGOv46YhKQ==", "signatures": [{"sig": "MEQCIDGPs2e4Jz8MPuNPgA7m8J24aQwEkyTS8+UVI8OUsTQxAiABQMEW3dvdTqmW/L1obswWdNCBV3rrG57uA7kFR0qb3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeikTwCRA9TVsSAnZWagAANQ8P/17qc7nwY5CbE1V8y9EJ\njpVC8go54jsoGYBQ/ZBsw1uArRit0cYHqzUXMoxjdYLB4eXsWi+8ng/6Jf2u\nBfyUIBQvUR+6YaZP1nH/CLz5MebwzkjrIs3SIL4bxSuqLB5Wi1Op3xp3a4A/\nI04a+buOhhJBDZtRI4tWHAd3Z2MOSOtV/HRmVv2w0198A8sZN090SdwdD0/z\n/XrqshwhNIG2BMyEEaOcIGMeUn84kM70o4qNFKZBY2t9f3/OEXCdSfd1jCRO\nrgCD8/kkaqpj4Yd3/SKFsLBq+SRc9DLSgMriMwmGZEm+Ai8HhuaOi4qqjLAq\nYkq/Nk+NEZbuMY7gOhr7xTcCzM/c7Hp59yCxf9cBw9CHLyioJzlAlO/UzzQy\nGjm+k9QRA+ZlJBN60HFh9UWsl6mx1JWd1rtO40Yzm6qYCRy3WGuoLnaJp/Gg\nCFWN2rNcqIIzkUoZ+F+joP3XoBAIGGs7ICBvXMRUQkgy83DhTgGxp7+XJj3F\nPYSkGNzZ7VbG5RlurNc2CTDtgAecPJ5y08ko6kzMuaByaI+Qj2wQ2OVql4Vn\nO4/CCS2kddq9GPtf/bZiBk+ryK1Ch/uJ5zFVbRjt7HiWuZUe0kSBHK7FnbCB\nnkWifsDPNscgJz4oMHLf9FM+JXpsAnbPa2yCVeGMb7JBZWADbPuQzp2EP5mL\n7yDe\r\n=920m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax", "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/core/@babel/parser": "7.9.0"}, "dependencies": {"@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.9.0"}, "peerDependencies": {"@babel/core": "^7.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.0_1586119919901_0.19304523510181282", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "babel-preset-current-node-syntax", "version": "0.1.1", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "5ee07a4150561245dceb3c36579113613f339e0b", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-1ez+0fDblFswjSvdnxBQpKd6UQgKKR91SA2NILzRdQf6u41cupRVrgV+Qc57PENcK+bzx5cYd65z9OO1NkPisg==", "signatures": [{"sig": "MEUCIER4yHiy2SttYMWXVgC8iAf0Gqkq0MQKegR/XXvRwi6WAiEAmPrbWLOOddkCallFmOztSvbhMgGFQOWER4cHqlrPjY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei2KuCRA9TVsSAnZWagAAPiEQAJ9UwZ9Z7qTlKGWbYtFr\nIMSlM+I9IykCr5bI+XSoDJBuwkMtzZqiGC+vfdY9gHSbH6hIYJ0hgf3wokRc\n2W6Q1TJL4sJdWKpA3KuMn5SmkD7R18jgt31KA7R/1VDiqGY5/0NY0G8IgRZC\nAXMnapwz23c+pUlXEWOIZ4jREQe4IBPhWABFPo4DFeEJO80mo3XAuRhMawZk\nxIAlPhk39j0ThyLVPvn+F+LcBfH1jidg2G79rYi0JdCSEJ8KuB0xl71p/uAP\ncWHu3sjs3YS/UXH90GAX1IEeQA5pe42yePNKYFFMyo2DeVKufVDrsxVW6Tny\nilWJ0l6QRmjxiat6IQwcTLcLrzMIlvYA9nU5Pma4GdGmbBWutMB6bTzgCmLk\nbOVg5iploUBRhCoH5Z6Q7d0nIyOenDJfj+/lHdmksae1zQYAtE7OFbQv0WoQ\nvlZ29zLjk3BKLIQFYclUcfL5Frd4jCXhX42NhFaqZaei1P10822kmXb29BOw\n11dNJPnLeRSJXkLaNrDQY+4QUHx5dMnNmqH8x+E2kc2z67DzM7TazuPs3BRB\nN0h2eJEVt3K/nc1CdFaxC8ly/FdXwu9u6s6jbpewRJlTdr7AYqVUquelM790\nBBWb6WtqdvztwKJs6bFg5BIwDUgxPeM/+EkeYlVBQfRz6p7M0lgw8oB3Heyi\nhUfT\r\n=PRrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax", "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/parser": "7.0.0"}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.1_1586193070399_0.7951513001974342", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "babel-preset-current-node-syntax", "version": "0.1.2", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.2", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "fb4a4c51fe38ca60fede1dc74ab35eb843cb41d6", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.2.tgz", "fileCount": 4, "integrity": "sha512-u/8cS+dEiK1SFILbOC8/rUI3ml9lboKuuMvZ/4aQnQmhecQAgPw5ew066C1ObnEAUmlx7dv/s2z52psWEtLNiw==", "signatures": [{"sig": "MEQCIGIWoO1RSnJjwQiVZaM7tWv67G06n2DvBslA7I6rc3GEAiB2n7PImjLNykq424eLjv8szuxilbyfoYCidL1vUAuN6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejJ6ZCRA9TVsSAnZWagAAokcP/iS5rb6txpjc4zXFMthb\nP1EnXWGwFZxZxCdyfjQ1DR/tys2F4mi3orbmwioN8//emPy0kwHm2AR9PVzV\ng8hirObpkXqQFTTEd6e8tnhr7ZcV6ExXk/Z+rPtsSGeDQTlq8WyvCD2qHvKj\nQVFKqfluLvr0a3u5yWjdJJUrPMzMiwzV7GTlkYAlUWukO1bTKcy+yUSkrjze\nHTiIIHFEQePcs0e9YJztI9YzsxWEedRk3ByKAfScxICW+H/fIXxhsgsO+DgE\nNB8Nn7Kb0Wx0V1/Z5/MOX3QijCeuVhkZRtEwVcXQggdpwO6C5pFhi1n7s1iS\nurkCrOOCs8XdaicIO3Tz3ydmRRMD/kYnx1sVnrXlF+8GfToP6kM3DumrtHjS\ntvh6unjAIXsXoBV6KQc5XvjI6SNzzbCzx/Gjiw7MzvjvxLWOac6cEWnfCx1w\nVd5/7EGlQypTMIZdS5XpNGVJB7ewPuJR86Dp1Pf0xWpGmZZMR9k26YgLP/Ic\nq8OH+2GSg1JLFNiaxRMUiKRT55Sjm8ScJEvK3lKcNFiBWbys0BZqVzb65KHj\nTMzvQyiFy7I09OWmnQ0wTl9UjTs9NgajqXx50bpSu3/Wp1VkY0bW91wtclid\nwUsgpL8TGVkwTGumr40xBdEOaNb9s4iJKE8tpFehGIyyapbbSP1yFhj/ZH88\nrDdt\r\n=ZldW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax", "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/parser": "7.0.0"}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.2_1586273945359_0.18740512566755574", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "babel-preset-current-node-syntax", "version": "0.1.3", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.3", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b4b547acddbf963cba555ba9f9cbbb70bfd044da", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.3.tgz", "fileCount": 4, "integrity": "sha512-uyexu1sVwcdFnyq9o8UQYsXwXflIh8LvrF5+cKrYam93ned1CStffB3+BEcsxGSgagoA3GEyjDqO4a/58hyPYQ==", "signatures": [{"sig": "MEQCICMaWXtcuxhhoL2EgSRJqJtaIM7WKCRUBMnXnKDtaqSNAiAAu0KTtFexGLocVjJnrGw297AHipxjdM+mUVh+gcAm6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5/N/CRA9TVsSAnZWagAAg2EP/3y/fcpbx5sRcxVf2s9U\nWmfL9W6GErVT1CVnQtRteaqp2pqd6hW+VDQr4tQTAv1jBicUgC1c5DOJclKm\nJm8X6RjdnJoF2T1ZKrwMa2wuFwKcdJ7Nn1xHZ6rPy4OwNWOcr+fZwfnO1jOW\n1Gm3q7NMCUrzOjQ1VU6aLPN83kBP/24YU0qGP1SDY5cruJXhOHxli5XxCaaI\nAnPlV6o8Bx2atDYs75HNwWb2H/Epla9Kyw+ElVKy+lHapxoQi1SUjnJIyrJQ\nflvc1gdSmnspu9Hp670/3x42W62wPuor5QyQdXKEVhGaD4ac1QyabRMMmWWO\n4z6MjbNvYI2C82XmHCkDH38E6SULGwM0971d4PAaePpdK7UKtMdj0gFarFpt\nSNAQ1YmesO/fV4ddKdfWsYws+ABAhyuQPK9jAPTm34rg+Tnp9Yi+RIMfuMy/\nia5xnAg+IE/Kxks4OG1kng+N42jpww9U6ZAu7kvN7BddvB4ZCxuup9EeORUr\nD01pVCMPYaFJ58cg3u5Q2pV+Blygbsz12ToaskG5Ji4XMl2RhWZEmYkJ+a6L\nNYDjLTSGbUV8PnjxiKsYYmTid4Fj5IIpLqnqZLk2MkxMnWbQASNXaC7ox6Bo\nzP/fJSAvYABaypVA2QCfkwt5q/5q/fSnJIFDGRIyiUJcrXUFlit+hM/eDWfi\nVzin\r\n=2whb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "node ./test/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git", "type": "git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/parser": "7.0.0"}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.3_1592259454752_0.7883891299755987", "host": "s3://npm-registry-packages"}}, "0.1.4-0": {"name": "babel-preset-current-node-syntax", "version": "0.1.4-0", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.4-0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "183332a498dbbef733b07d55eee8865420e095db", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.4-0.tgz", "fileCount": 4, "integrity": "sha512-gRRf0y4qUI8icDuf9tVSFwAje5mM7iEANi8SqHAF2exH7HiLeVHcKzDecElDK6VnJgvRpFSjyXQuDfGXIMe7fA==", "signatures": [{"sig": "MEUCIQCIFaUcGeF7FCA6tp0KpmRDmi9g5lS4LWieQ/V1jqKaPwIgF/B7swiCyzVCIBPW1C1rBwk+HUMwCfdrpE4DNba4nKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb2XcCRA9TVsSAnZWagAAOZ8P/1re0IQOz92em+vzQWa3\nufAw8qK9AoPoQkoJ6wjRS++SXhD2ck2WUO+JzqdoIqN5GBskwt/hPh03eMag\np0RTQJUp1fliuy/jyNKO2XJTjcovO6Zgb6x4uPQtPTcTKhmXNyBYJnBfufJ9\n/C8cs9bj6rdN9rLQUXoPpUHVe98NOUcnUUeUD/RiO4nMxMllGn0LrYoPydd+\nGs0q8zzU5+RS9BQS6QJvc/MgmTIzJ8i4gzbImfELLkOV4zAlHKv6KULng1dv\nciGjl9vgyQlmG7QnMGD7vgyK0PIjQZ4NgHQEMURUa0v85JdRP5RstkxRo1ry\nBO7vT10mVgtRaHmsaXuMW+xh4gu7a5osqXJnY+qMe33TaE0b1KrizSpMaeid\nxb5sj0XdcO03lTQMBSkhr+F6Oz9MUILRxDq+kFBWWX7yFqS4a1HZsh32n2TW\nq63zd+mVgm6JGM2EJXmTsiqsrqqDysNe7H+PIPUQJVVWf+28B82V4JaOWA8Q\n40arDqHKY95lgMoslBVgZKBlp8xU0C97gYuxVn6q1U0sJ9ZzjmHI1F4Q4N6T\nOBGeiOoXk3DseBIyIGQFYMRX7FeYrvwoImNjAZ5tAJXvg4VO9lCDbjmeTN4C\nHmVcGapqVvFb0g5byd92X/Bx5+XnSWwBMTEiYRRJUBDx1NLWrYL/tJ2bkhWl\n0nol\r\n=CSVW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "node ./test/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git", "type": "git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/parser": "7.0.0"}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.4-0_1601136091731_0.6273060929629952", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "babel-preset-current-node-syntax", "version": "0.1.4", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@0.1.4", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "826f1f8e7245ad534714ba001f84f7e906c3b615", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-0.1.4.tgz", "fileCount": 4, "integrity": "sha512-5/INNCYhUGqw7VbVjT/hb3ucjgkVHKXY7lX3ZjlN4gm565VyFmJUrJ/h+h16ECVB38R/9SF6aACydpKMLZ/c9w==", "signatures": [{"sig": "MEYCIQCSLHd3TZwCEu37iRGHdWpmGGhomXt9GboV/nxQnTL6twIhAKKxxMQaEJ49XksjGQzJ9/d6mGwqLCky9DPWiTNtIP+z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfedyzCRA9TVsSAnZWagAAxWUP/3Kn4cDvuEMMDq54L2MR\nFJbMmfdOZ7KbSJgsq3xVY8ooQYyKla1WmQ9KzA8BFpbEb09BJvZlkHvfV/Wc\nmNdLFUVPHKLJ/rtTrRhuOewtKO9OwRl1xlr8Ac6f2BUV5CGSly7WmXf91pdn\nky51+A/tvnF3ldWOn7PHI64F3jlMeKvflRds0g7jIcBpJzi+iaPylZ4fh1iA\nMsTOaurCbicxDbV8KJE8QJhZmKGJz2iFnK0+cqUZx9OBZclFDUhdnU62qXx7\nnZFrz88vonwKUAsV3oIx+23lRIFMpqZS+eQiC6HhINd742aLTvzgs9Jpq9wz\nUkla2SkPzrDXReKIojZPAF43vU1EGqHH5+F016xT1diq9fKkaAW+/0DK0b7f\naQU0io5YDVzM+sXL6cZcHEkfZyF00mgwT52vRTfAZ+pwzSa2xFnZFztl9dPi\nE6wyssxaQ4vtPiIy9n5NYKPLvgTv4pIetNFp3IYKsgwTsFqNXCvzj71yxBbX\n/KSi7sZ+XliWynREZw12utVwGvQ94b5l3nIx1c6r0BGWjXYYM9IyKzULcNsE\nyIWG4zlzIiHQAp+voVYVVhcGvxtvZpN04iiBoiYlG6dlYs2g22iCY6xhJ+KO\nVDDPx4gFodVXhLmx8r4Url3raUc0x8KEPUpTb/Bw+CSTnrZHYpKA6V6w5zEg\nu8kn\r\n=tZrh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "node ./test/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git", "type": "git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "resolutions": {"@babel/parser": "7.0.0"}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_0.1.4_1601821874769_0.2952285767281637", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "babel-preset-current-node-syntax", "version": "1.0.0", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@1.0.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "cf5feef29551253471cfa82fc8e0f5063df07a77", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-mGkvkpocWJes1CmMKtgGUwCeeq0pOhALyymozzDWYomHTbDLwueDYG6p4TK1YOeYHCzBzYPsWkgTto10JubI1Q==", "signatures": [{"sig": "MEYCIQDCy01hcMycNz9BhIhklpV7cDc+g6aafnxYvOqcfq6o8wIhAIdJSChTOjhbn5/WdYibKz0CQJjtDY2n61ihy3XUP/eE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnWFBCRA9TVsSAnZWagAA42gP/3GgT++pkaY57SExBrto\nUIO8wYLU/wCTjQDQG6hoBCxbczFDBtglkg3jDyuXI8x/a5ZXtfGJ1ccFHapH\n5bfS6JJUo/bpuBTv4NdL4+6qKRiCDC6tnDSEw3cZV7ojZonDRyB8cWhFX0hg\nb0yd1en2oKsLyHK9fM8XrxpoWhXrLS8RAzvGQ3J3XnzXGXhpelhaoFQIcdMV\nwoOXd20qUq8URkDwPAAgv5RXGQUOCEQtfhOHsBl+v4edGQoyLIUoZlj4opMe\nZjIQVPCe66FsOCSxhbANDuxg4Ki0vyPDc7ueN6vGtmaSWcR+Q5XxxFkYKC4g\nQ0nOtfNA5NR6RbvbLr18cdt41REPMS9kBt5VYzUZ/H7xdF9b4qZkZgGKrVhk\njkHzPo4f+XwAc9+zL05Bu4U41I1vhYsdpxETKYm40TlLJXnEhm+eYmtIy4mA\nNieLAyKbShAScHxn06rUiCJE4wzmaPH0nTb/hA+HmZGRcXKJSDNk3t+h4Jup\n/A2x1SadwWDjiU8s61OWkezW1aW+XcuHK9HBBbMo3L+Xqwn2wgEZO7DooiJS\ndaRsddVXfUkSGQG0MuOhLEfWT8Nl6yfCVndiIQH9k8WH/lXZRJMc+68E+cla\nd3RwmIZqlZeCoc5KmYB/1ShRqAiCYWDXy+dt9ZiqpqOQAEQF34b+3kbHs9+r\nYgS/\r\n=l8T6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "node ./test/index.js"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git", "type": "git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0", "@babel/parser-7.0.0": "npm:@babel/parser@7.0.0", "@babel/parser-7.9.0": "npm:@babel/parser@7.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_1.0.0_1604149568296_0.6050077738126012", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "babel-preset-current-node-syntax", "version": "1.0.1", "author": {"url": "https://github.com/nicolo-ribaudo", "name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@1.0.1", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b4399239b89b2a011f9ddbe3e4f401fc40cff73b", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==", "signatures": [{"sig": "MEUCIFrOY4E5Mmds0LZCGQRgUNsxTlLqmGaEJB5HY6HCkhEqAiEA9oyW7/9/9PA/L0n5Yb4fi1EW9fG7chP6IB7HUcXyo0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2LWqCRA9TVsSAnZWagAA+TUP/igtW3SQ0BeTsVVuQxRL\n72dhAeCqQdx3Y9PKjODYORf7+SYh5Rkk7DptixzDkLp8kZlS6A38d0ec4E3+\n3EtqwiifsaFAjulRIHl//fNjanLWWX5ubRAevaxvQ0a52JArApK8UYCRF7wF\nGTxNgji4KT7pU2jeVTF9TwoGEwV8D3VeJLcpVgnY26zSyibh6n9Ly6Rd628O\nuhM4YRYXgR7MhPgrhcFhAxGvHbCZOsvg04iLoJo187SdQIFyrZ32BFnK+ZkP\nq54QaNOZ4KUj+GqOCiBbVSQyBuVQTBMdyd2rzBxeQj65O0PG+1O+EiQW5mOr\ntxdxYNShDJKem+HVeAg3+0ZJIVcUmdgfYBDLF9c0GGRZofiIRc9lvgL8tEl8\nJno6h4qd6rD7Hdld/FzbcXMAujIKA56A7OS/RAAB5dSr8ISrDGzX/ruFePe4\nIrvsaShewVco2ordJa3hD//NS9mKtV5CkY0jBI/XrtjcpwhPcOMYgNxyGFLe\nNpKdpoFspMx2EgoSjkV4CeDA4+D7X2ZqTXjMV7oU5oSoJAzD6z2cBzFC/562\n4be8+op5M9X4Lpedw02eSFcRBkrN+2lkUKHtUvBdmkYEtu3dEHOJMyEL50RB\nEYq+53UH8c8HbZa4xUlLpkJn2/0RT75JjQ+Cc7zHCobAO1nBL6gsOJ0qNlQa\nY+ue\r\n=Id/q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "node ./test/index.js", "prepublish": "./scripts/check-yarn-bug.sh"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git", "type": "git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "directories": {}, "dependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0", "@babel/parser-7.0.0": "npm:@babel/parser@7.0.0", "@babel/parser-7.9.0": "npm:@babel/parser@7.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-preset-current-node-syntax_1.0.1_1608037801553_0.11189396800069717", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "babel-preset-current-node-syntax", "version": "1.1.0", "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "main": "src/index.js", "repository": {"type": "git", "url": "git+https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nicolo-ribaudo"}, "scripts": {"test": "node ./test/index.js"}, "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/parser-7.0.0": "npm:@babel/parser@7.0.0", "@babel/parser-7.12.0": "npm:@babel/parser@7.12.0", "@babel/parser-7.22.0": "npm:@babel/parser@7.22.0", "@babel/parser-7.9.0": "npm:@babel/parser@7.9.0"}, "license": "MIT", "_id": "babel-preset-current-node-syntax@1.1.0", "gitHead": "2c7c2dd2bc2f9a7845f2922bd9695ed4ecd4b47f", "bugs": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax/issues"}, "homepage": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax#readme", "_nodeVersion": "20.16.0", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==", "shasum": "9a929eafece419612ef4ae4f60b1862ebad8ef30", "tarball": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "fileCount": 6, "unpackedSize": 7129, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFhspt0h6qUFdTHfP4erihkqXXmnHLPusZKKWQnShb+mAiEA4l7/F+GNGIGJCzJzUT4XcBDyrY9Ky9QFkdlRCod2qQY="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-preset-current-node-syntax_1.1.0_1723563058296_0.3343828448564634"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-04-05T20:51:59.663Z", "modified": "2024-08-13T15:30:58.714Z", "0.1.0": "2020-04-05T20:52:00.101Z", "0.1.1": "2020-04-06T17:11:10.543Z", "0.1.2": "2020-04-07T15:39:05.445Z", "0.1.3": "2020-06-15T22:17:34.853Z", "0.1.4-0": "2020-09-26T16:01:31.893Z", "0.1.4": "2020-10-04T14:31:14.887Z", "1.0.0": "2020-10-31T13:06:08.456Z", "1.0.1": "2020-12-15T13:10:01.815Z", "1.1.0": "2024-08-13T15:30:58.523Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nicolo-ribaudo"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git"}, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "readme": "# `babel-preset-current-node-syntax`\n\n> A Babel preset that enables parsing of proposals supported by the current Node.js version.\n\n## Installation\n\nIf you are using yarn:\n```\nyarn add --dev babel-preset-current-node-syntax\n```\n\nIf you are using npm:\n```\nnpm install --save-dev babel-preset-current-node-syntax\n```\n\n## Contributing\n\nPRs are welcome! The codebase is so small that I didn't setup a linter, but try\nto match the style of the existing code.\n\nYou can run tests with the following command:\n```\nyarn node test/index.js\n```\n\nThe `test/fixtures.json` file contains a bunch of syntax tests, alongside with\nthe minimum supported node version for each of them. <PERSON><PERSON> should throw on\nolder versions, without support for that given syntax.\nAll the tests are run using `@babel/parser@7.0.0`.\n", "readmeFilename": "README.md", "homepage": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax#readme", "bugs": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax/issues"}}