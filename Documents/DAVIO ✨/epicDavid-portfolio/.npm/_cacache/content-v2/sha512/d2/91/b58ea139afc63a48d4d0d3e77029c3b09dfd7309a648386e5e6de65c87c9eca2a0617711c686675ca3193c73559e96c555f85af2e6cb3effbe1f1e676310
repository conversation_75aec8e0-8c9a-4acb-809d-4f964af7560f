{"_id": "@rollup/rollup-linux-riscv64-musl", "_rev": "14-faecf2cb8f3406adfa3f1698b22d9d71", "name": "@rollup/rollup-linux-riscv64-musl", "dist-tags": {"latest": "4.44.2"}, "versions": {"4.37.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "71cc5ca7be1ed263357618bfe4f8f50c09725a7e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-DTNwl6a3CfhGTAOYZ4KtYbdS8b+275LSLqJVJIrPa5/JuIufWWZ/QFvkxp52gpmguN95eujrM68ZG+zVxa8zHA==", "signatures": [{"sig": "MEYCIQCIi2v9cFrPggFDrZapqHTJ0nqwv1h471pMqWwO2z0OLAIhAIXKUtiGgRA5uH/Qy5/OGCHfIzt/NZqXvHXHF7QGlo41", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2349795}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.37.0_1742741850459_0.4912719262992571", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "8bc00b75fd07b15c35a54b41a5f052c01dbf925b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-9EYTX+Gus2EGPbfs+fh7l95wVADtSQyYw4DfSBcYdUEAmP2lqSZY0Y17yX/3m5VKGGJ4UmIH5LHLkMJft3bYoA==", "signatures": [{"sig": "MEYCIQDTcpBcDd3pWew3e43eCGGV9QYucaCRfdMFh9zsjD5MGwIhAPgxy98cfl2CnR3e7OqVl4m0bR0fDqW7NZg4zis+v7Ug", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2345699}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.38.0_1743229774396_0.19809589141193595", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "4d66be1ce3cfd40a7910eb34dddc7cbd4c2dd2a5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-m6cwI86IvQ7M93MQ2RF5SP8tUjD39Y7rjb1qjHgYh28uAPVU8+k/xYWvxRO3/tBN2pZkSMa5RjnPuUIbrwVxeA==", "signatures": [{"sig": "MEUCIAxyxvxFOs59YnBMiHZL9QU9zyVoW5yun1uNmgrHwGnlAiEA7w++F/NuzYH6n/ZCaMwL+CL5P4ODnizs99tvncSbNhQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2345699}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.39.0_1743569399682_0.13441677361179316", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "973aaaf4adef4531375c36616de4e01647f90039", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-rKmSj6EXQRnhSkE22+WvrqOqRtk733x3p5sWpZilhmjnkHkpeCgWsFFo0dGnUGeA+OZjRl3+VYq+HyCOEuwcxQ==", "signatures": [{"sig": "MEYCIQDV1He0ToWQPQsZVp6RTEyarZVwx6wurfycnN/L1YB7XQIhAICfHB4xEUAs1w8M39xLGKQ7ZmY1P1hy4D7aLqYZg4mk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2371595}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.40.0_1744447201922_0.7765867276623832", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d75b4d54d46439bb5c6c13762788f57e798f5670", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-n/vQ4xRZXKuIpqukkMXZt9RWdl+2zgGNx7Uda8NtmLJ06NL8jiHxUawbwC+hdSq1rrw/9CghCpEONor+l1e2gA==", "signatures": [{"sig": "MEYCIQDyiz2zmje0rb1FKpYd5lDMc0xzdbkb/EPH2dIU2r4W4AIhAJkPdcetWw63jgB5KijjUiLL23m0V9UXeC0QQEr6dPDx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2363427}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.40.1_1745814950043_0.7087460344752301", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "e065405e67d8bd64a7d0126c931bd9f03910817f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-5W6vNYkhgfh7URiXTO1E9a0cy4fSgfE4+Hl5agb/U1sa0kjOLMLC1wObxwKxecE17j0URxuTrYZZME4/VH57Hg==", "signatures": [{"sig": "MEQCIGH229AsYe24dUe9GLy4pLZHAwQPTBUwFR/HcGDU11JEAiBIiqJ8Uwbj8h7Zp4y+OceJDba3M8a5Y42htDyVDwSCEg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2367523}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.40.2_1746516440532_0.6375109139772084", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "9f4884c5955a7cd39b396f6e27aa59b3269988eb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-ieQljaZKuJpmWvd8gW87ZmSFwid6AxMDk5bhONJ57U8zT77zpZ/TPKkU9HpnnFrM4zsgr4kiGuzbIbZTGi7u9A==", "signatures": [{"sig": "MEYCIQDRbdjDEnHFSUIMOaIOTmcA2s2yQsgoYQ3YxN/Lxc10UgIhAM0vcy4FOkTacXBu7HO9NbN9XNv3w0jsV1SwAIRIqpZS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2363427}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.41.0_1747546435266_0.5582056442392063", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "054911fab40dc83fafc21e470193c058108f19d8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-LdIUOb3gvfmpkgFZuccNa2uYiqtgZAz3PTzjuM5bH3nvuy9ty6RGc/Q0+HDFrHrizJGVpjnTZ1yS5TNNjFlklw==", "signatures": [{"sig": "MEQCIApcLm/9JbVYBbFgh0C9m3XlMUHR/FYv/BPIl8gf2lTCAiBwDcYuLOIVt8G6oqUtQ6H1iPGnLm7ASFAuVZN/YlXl5A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2449675}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.41.1_1748067303703_0.9990274920044044", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "13e0841d0d10b6a8df9a9992a6fc80c8feb719ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-qqwpXqu6mV6Vm+EsU2NQk/xLiQfCv+NYmYNCYF8FFIf7kzlHvDoqiNaqqOw0hAr8O5nRBQO+H6X4qEw4Y/0b8w==", "signatures": [{"sig": "MEQCIFcUvtjjlHwZbQQH8C9st8hZjIrB3yf0P2k8eEdf5YPZAiBtxHmqjed4HKCUz/LdoHxgYOTpFFUb1VeZw6Mch8iiTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2437387}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.41.2_1749210060176_0.9260744112995654", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "478a23f0fa0d832a0a6fa858a9f3d2eb201d44de", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-qMvnyjcU37sCo/tuC+JqeDKSuukGAd+pVlRl/oyDbkvPJ3awk6G6ua7tyum02O3lI+fio+eM5wsVd66X0jQtxw==", "signatures": [{"sig": "MEYCIQDdReKEAVHGCeapXlhOhhdmN7CHe2/9ajJGEUzlIViFXAIhAIFv41K+AsK7t64abN5E4aoQXU0Ug68OWT0CJWUk8IlR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2437387}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.42.0_1749221319572_0.19311842771228038", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "d76ad93a7f4c0b2855a024d8d859196acf38acf5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==", "signatures": [{"sig": "MEYCIQDmIlIA124I+pjRZd6ZudBMEulX/3XMLzhzQM3QBquEYQIhAMPWYHAJDI2/JbwOxcZ/zn+jElpF3aDXPYNV6t1FnBrU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2437387}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.43.0_1749619388776_0.9456354589824463", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "878bfb158b2cf6671b7611fd58e5c80d9144ac6c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-TDKO8KlHJuvTEdfw5YYFBjhFts2TR0VpZsnLLSYmB7AaohJhM8ctDSdDnUGq77hUh4m/djRafw+9zQpkOanE2Q==", "signatures": [{"sig": "MEYCIQDQ8ub2ZCiap0pyj2K4Kbzhkvv8xjvHkzOfDk4QzfLLkQIhAPtpE+QI3kOp8t00n2yOrtnWrHPDZb5abv9MEdVGG1zh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2457883}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.44.0_1750314206716_0.3664089312265326", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-riscv64-musl@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["riscv64"], "dist": {"shasum": "095ad5e53a54ba475979f1b3226b92440c95c892", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-ppn5llVGgrZw7yxbIm8TTvtj1EoPgYUAbfw0uDjIOzzoqlZlZrLJ/KuiE7uf5EpTpCTrNt1EdtzF0naMm0wGYg==", "signatures": [{"sig": "MEYCIQDWoTa6EWmBxyHAaBD+hJynAwbxY8/NqRIaMZWWjgEomAIhAP4MkPVqChN0auJXUbz64dBWTx8G97tFbmAJxJXGABKL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2457883}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-riscv64-musl_4.44.1_1750912484547_0.4555628115802406", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-riscv64-musl", "version": "4.44.2", "os": ["linux"], "cpu": ["riscv64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["musl"], "main": "./rollup.linux-riscv64-musl.node", "_id": "@rollup/rollup-linux-riscv64-musl@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-e6vEbgaaqz2yEHqtkPXa28fFuBGmUJ0N2dOJK8YUfijejInt9gfCSA7YDdJ4nYlv67JfP3+PSWFX4IVw/xRIPg==", "shasum": "3e943bae9b8b4637c573c1922392beb8a5e81acb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2457883, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDlOro97pJlF+lKXOE9ntoWf/LnbaMHFPCI7Xd7Cs+KzQIhALqvlGXEwD7uqvK3c6idsCAH3rUWy5/ETePel5DzseQz"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-riscv64-musl_4.44.2_1751633795750_0.4365563829544554"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-03-23T14:57:30.377Z", "modified": "2025-07-04T12:56:36.177Z", "4.37.0": "2025-03-23T14:57:30.680Z", "4.38.0": "2025-03-29T06:29:34.653Z", "4.39.0": "2025-04-02T04:49:59.961Z", "4.40.0": "2025-04-12T08:40:02.115Z", "4.40.1": "2025-04-28T04:35:50.289Z", "4.40.2": "2025-05-06T07:27:20.730Z", "4.41.0": "2025-05-18T05:33:55.511Z", "4.41.1": "2025-05-24T06:15:03.930Z", "4.41.2": "2025-06-06T11:41:00.449Z", "4.42.0": "2025-06-06T14:48:39.787Z", "4.43.0": "2025-06-11T05:23:09.006Z", "4.44.0": "2025-06-19T06:23:26.908Z", "4.44.1": "2025-06-26T04:34:44.803Z", "4.44.2": "2025-07-04T12:56:35.964Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-riscv64-musl`\n\nThis is the **riscv64gc-unknown-linux-musl** binary for `rollup`\n", "readmeFilename": "README.md"}