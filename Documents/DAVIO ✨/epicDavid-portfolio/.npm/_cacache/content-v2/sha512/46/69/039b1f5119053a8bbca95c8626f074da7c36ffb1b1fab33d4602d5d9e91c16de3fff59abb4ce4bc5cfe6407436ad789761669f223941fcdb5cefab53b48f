{"_id": "@vitest/spy", "_rev": "140-b7b58ea8b3327f3b58d3fab601920633", "name": "@vitest/spy", "dist-tags": {"latest": "3.2.4", "beta": "4.0.0-beta.2"}, "versions": {"0.27.0": {"name": "@vitest/spy", "version": "0.27.0", "license": "MIT", "_id": "@vitest/spy@0.27.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ed344edc5c30b8ed50c114aeb50976fa01925e4b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.27.0.tgz", "fileCount": 5, "integrity": "sha512-L6L/gW+Oy891MqSObQ2vyv6btw1BmuFlrt3JkQFNuO5m4/235KPh5aBYnEtR7qjo3iz7Ayen5oqY7QihXMfsUw==", "signatures": [{"sig": "MEUCIQCktPhcjprzKxZIsHCJqnSmUWE2Guwo1Py34oZFmhRjQgIgD1c2JlS9yk9wPLjZSjuP1jiX2gkrT+TkB28va3iX6FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvAmLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHFRAAlwOrYC3dHyOhMwX4SRXyUijhHOLV3DcVXQE/YdLa91PUbfia\r\nxPoXf2tj7ZuIMuaUvcD4K/vYr6wYNW79nvxUeYCPltUIG6FpEKTpTN81EsFp\r\nK1M4Hd8XD4OUi3vNnLR3w0w7ESOz0nwempEBGselu4tPwILVY7Q4CJ6RaihV\r\nX5q567zZeeghCm1r1nmzDVBDK8Xhod3FbfOaLlHgaz6NgfKDEEy0lgEZluWz\r\nH/1M6WPeXjXCWUVAz6L/fR89vsa3wvY4MMDnvh7UjpQRYrD/oqXzHNLVwtDt\r\nTD6+9H0iz19YUu3LDLQcWfo8+5qtjQ+mhtsQoD/ERGz8uGXLFOeMGjagb1SY\r\nOyiI8y6qO14ZyMPQ2n3VzR9Mcq5gWleuX55jT6QF+320yglX8IsHexMi0O4D\r\nZO+ji6Ju1oJjJsFl17RVdSSFgcyQEhEHK6iCxEV0od/wyWVtjTbbOjl+AnCD\r\nXUIDrzT+0OQudaBF04omdySrQUlPsFFz5DEghv/bIsjHQADfptVBbe+VCT4+\r\n8KiNjadcltefNX6jd68o9AhNFdCZEyJmVz5kCycNXxXJb5yqA0jmzu0SIrvu\r\nO0RrtJKv6mKDPUmiiuE+rL6pFX9znBfE7R8EJvXtw0ozQiU1qmXwmkwxD7T6\r\nRf5YyQCC/wEkufuOze0YhBhI1GHD1Fvzz2s=\r\n=OrTe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.27.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6a50bc36862decbadd45548ce7807b22/vitest-spy-0.27.0.tgz", "_integrity": "sha512-L6L/gW+Oy891MqSObQ2vyv6btw1BmuFlrt3JkQFNuO5m4/235KPh5aBYnEtR7qjo3iz7Ayen5oqY7QihXMfsUw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.27.0_1673267595419_0.8593253222055073", "host": "s3://npm-registry-packages"}}, "0.27.1": {"name": "@vitest/spy", "version": "0.27.1", "license": "MIT", "_id": "@vitest/spy@0.27.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1b3d9b0450396de6ddf7f7b6e201fbc4fd4ef0d0", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.27.1.tgz", "fileCount": 5, "integrity": "sha512-PR/AIQYDNa2mnRbUCkStHnCmO7IYlm14xFW+kF7nlqrmjNkDNfX6D3lAjgljqioVinPGXHnrUABdlj2CHVcpAA==", "signatures": [{"sig": "MEUCICk2vnjYW9ml7I4FpSkf+2fTXjgIv+YNPO99C+xyXxGsAiEA14nmzHF8Dt3acLreUfMWrE5YgUWaAR3MJXEN60B7gHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvuSIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrv+A//Xv976vi/YrM7TkBWb8rzvW9fG8imVHuue9fi/PyO3kjSHwWZ\r\no0X6jlvyPnEHAHc38OgtKMmCE/N38IDUbAkTben/gZ1SSehTo6Ezo7Qx98Jo\r\n0oaaxxXZHjSH7DF/9SIbYBqCaabwFhyG0KyuAhBMsBjgNNkXw5sKtG2leCa6\r\nGOd6Zng9+T6WmR1t+BClYQthcGm3lK3wyCTRIstxyU3OZuGNOsS/u3lcM3e5\r\nXmUA3xXW2Y0jz3Plka/smilg7EQWaJQbpLTpeKI6gn9/5TCBFOn8qHAN3/iX\r\nlDCog85v1beECNRBleQlMMsk71YzkNApJrjFxv4rRHamkdhuzePArmZC7Rq7\r\nxJFYGbtXPXBJu3seXdip/mdX+Dnqt2bmAkQzJBcN2uqpdELILyI+FXYbEABY\r\ndsdJ9zxAmKt07afNWTDQcSQLBiiIkBiCC6D4xuslpy/JRF/mvYgLkV8FG26x\r\nKDEN4EhhklZeG95CzJHT409ODXuCsUWRrhJPfJBts9xWi8ZuZKnV37b385uv\r\nNZxEQQEdKI1+AOlKUqHZOvvn2EkQuVMkUAJs0j2kcBINb/cOrUyd4RqeN3dM\r\nWdWLMbn8I3ja9BoNeFV7X5A+Zktj+7Rq8/UhPlGZQwd8a8nMy93+PIoV1Tz1\r\nAVvYyc7JTBiCKMHq0NMg9fSg09O6vu7Sfnk=\r\n=pVqD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.27.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/8b9a664252738a82864e2c5bbc038ba7/vitest-spy-0.27.1.tgz", "_integrity": "sha512-PR/AIQYDNa2mnRbUCkStHnCmO7IYlm14xFW+kF7nlqrmjNkDNfX6D3lAjgljqioVinPGXHnrUABdlj2CHVcpAA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.27.1_1673454728105_0.8487842577015678", "host": "s3://npm-registry-packages"}}, "0.27.2": {"name": "@vitest/spy", "version": "0.27.2", "license": "MIT", "_id": "@vitest/spy@0.27.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4c830bf79fd780b82692eef068654b4e327ccf19", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.27.2.tgz", "fileCount": 5, "integrity": "sha512-sWzxxLEjK9ys0hLekxFb0B35tIlNZzFOh5g+oImuRCrE2nV2f9ef+74oLTrorGlvZuW5wAPwdquRpvWD5ggtGg==", "signatures": [{"sig": "MEYCIQCF42lvA2n/qesKnzY733Ms4MbOU9EPCqzQOAkV7IDTeQIhALhUHRIjYIvfdyFOrFlZ2FA/eFidMKI8S7cAuk/S/GcQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxlDnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoDQ/+I/+EnHQY1bIUsOwMZTbjo2Q/W0w6OTm9aiZ1/4df5jT6XFy2\r\n98JggN5VL785w4yY58TP7S+X/cxmn5D3Z8K4RMxpY12OiTVpGkaL9AY+Aw4a\r\nkCqo70+3vzhi5Kjx7hCIj6nEI5eKY9qHlff2x3DFyX2/aaG3I7UaZDhWcNtp\r\nGg1QNQRGzX6hkuFSpAeaJYMZbWtPj8bigaXBuqYKtauouNamOml13IrFRogv\r\n451vpDrSFGaT50EeA2WYg7ePModdIesgWzCK7TGrfNpzQR84FkoAxhZ2F6ES\r\nkxHt7YVXNe3/mMfAHyEQq959SgOCRAYYoeX1JPVawiSE3F+gS/MtPUc7ORHj\r\ng0AZavTyhu/P8ZUULTG6b08G05XjnLFnIQG/nLpGDlcbNkv6HwSBvBpHHinF\r\nu/d+xQyibjENC1R229Mkjn6XhU3D8jCIiq7FcBN411QdwIjNMMsq2PCorR0c\r\n3n/2EQpFiUj5/oEQB4xw8DqPKlHjBlxMNI/HpGt2eFNGXBPFBf7Iq/ikIxFm\r\nom28jKg7AUUb4lkENacanRP0xCoynTzh3t4/quz8M2Hgo6wZQILt63LWMUU6\r\nwnnjRWTZhMCqS+ARcG6vxMNgjynChhHO4h+DHNqjbFvhdRcYxg9EFw0SSFFK\r\nRjPdZ23jrPPJTGaOD8J4UdxjAFHZTfoSmkU=\r\n=17KT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.27.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6380067ce936028a1117c8111fba4f8c/vitest-spy-0.27.2.tgz", "_integrity": "sha512-sWzxxLEjK9ys0hLekxFb0B35tIlNZzFOh5g+oImuRCrE2nV2f9ef+74oLTrorGlvZuW5wAPwdquRpvWD5ggtGg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.27.2_1673941223292_0.2908816718483096", "host": "s3://npm-registry-packages"}}, "0.27.3": {"name": "@vitest/spy", "version": "0.27.3", "license": "MIT", "_id": "@vitest/spy@0.27.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a62e7334506a6b61868ffbda9f7a4fe051d9e689", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.27.3.tgz", "fileCount": 5, "integrity": "sha512-3eYErtAjLAZpgAJ9qg+GPgI92EwEZC5nxpDrNnl5XkKvCjdfYRP15n7lSjjNSl0+srpQ9RxAKrRBEaT4VYrsBg==", "signatures": [{"sig": "MEYCIQDqPyDgnWSecAkX/9g/Wm+hN/aPUCHZMqTCXPK+WXWuVQIhAKRLXawVfCTszNimFrOI12gsuGZA4/9X8PEPH8wPOfAe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzBFbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU9A/+LwL89u/TlYWTFs9mjULiZhkPgClYQ4626GeaefHeq1XCF+BI\r\nxcoqpu2wkSO6vCN7nSD/tob1X+lOprGDEhZCZ7BhZqedd75hkbbwu4fbkdZ5\r\nY4x0Nv2HkTf7XwrpHGqXzaoXWybTpm7yJSsqxqsLWwEFY/HolJVFdUSZI+hE\r\njA7hM0JP536rPdBWMWLi/CkiyVzJ7O0p4wGM1UWzBV/d08L4tWeQdhoVm0Fa\r\nsF97LlwAyWSPPc4Q7gKqSdVR/Dbwj63kGe5JB5Eyp/LsFMnXgdTv1sn74ghL\r\nEWzf8Dws16kBlM16nvlGYYQa323bTILxNIbr42sWuQq3DzWhqC1iU7bxG3F7\r\n7FZkvVGHFvohLlbgLtDf0zkHevG/DRSg9C3piuYQqMR6YHe6k/UvbFpVIUpW\r\ncCr7tEZgNYshCA6FRaeXB/vE/h6hGDRI9Ck3CiOXB7Saytme4IZ3OrYr3Ald\r\ndbI4LNObycZafpk7adgJspSpVr/1fMG/cqXD9ZcdaPt6W4YBGWJNzjsM3EcD\r\nO/QdFk2wpuTQHGsMnka54Wsn+Kf7zF0MuaJnpJoof5KVX3900X/mSbr1rWXo\r\nseH1Zz7C3ocj23QCBrSTEDic62ha+gaNuiQi+3E9u5PF7tksPhh+qqHgQT7I\r\nbwqkGOAMypvyNvZe9r4JVjF1O87OSbyGD9U=\r\n=34sM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.27.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/367ae2f9f1ec8f34f5d440c79399c3ac/vitest-spy-0.27.3.tgz", "_integrity": "sha512-3eYErtAjLAZpgAJ9qg+GPgI92EwEZC5nxpDrNnl5XkKvCjdfYRP15n7lSjjNSl0+srpQ9RxAKrRBEaT4VYrsBg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.27.3_1674318170808_0.7097382000709394", "host": "s3://npm-registry-packages"}}, "0.28.0": {"name": "@vitest/spy", "version": "0.28.0", "license": "MIT", "_id": "@vitest/spy@0.28.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d66bec89077aab96443d18a59bdbe92594dfe811", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.0.tgz", "fileCount": 5, "integrity": "sha512-gYBDQIP0QDvxrscl2Id0BTbzLUbuAzFiFur3eHxH9Yt5cM6YCH/kxBrSHhmXTbu92UenLx53Gwq17u5N0zGNDQ==", "signatures": [{"sig": "MEQCIAlRSDLGy4PDwl99kaOnU1h2apBqwJt70yZ1Ifl+NIv/AiBY6k8Z3GGck6JpgIjJZkukFYEPfrdWuOeDgd0nCEgB9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzlMPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwOw/7BUk6tM2dSO5puKLA8oWf3LvpIFzNeOXuQZatZsPV4doqpkvd\r\nyGcKVKHGg1LvwTYYU4CkRMwR1hiGsltolabA1D65DkaxdWJJ21MSsmsJkxm8\r\n33ya+7nFP3KpRXtFQFDM/KlyGDWoYyD/nV+67ZY00ctY789sBFKq7r554aTU\r\nywO5LMas5UxsaVVpZnEaVNr3fotvWFjd5o4TRwUv/S+vLxLf+0/LY04fy1F4\r\nJCL0Fr4P94n15YUIoGZIFzcxfsYpzWiGIP+9xXzdRJyCP7Lv5cVFdCPFLQ3U\r\nAiRfuuEz4FnuU9jfJyUISrbjaPI/xEMKFfJqH4/kQCw/gWzhN9+kEuXEhk7G\r\nJDcFA7dX29FEsrTQ7kZQslKUcBJ6li1j+S+AcaKzp7oo5nIblGM1mZgW8i2L\r\n6HopOXsW4pgFs69PsUDwlcx/HdomK6ORjwq/izofAgWt/8S82gz6CWKQKhAs\r\nZxXZmdzDWtFm6tWI1EoDhHPwTtR9/lpU4ztpYImQeu/37Q5XMoRxyCcK7U8d\r\nndSg44ReqpDoiMb5wJHr8DwT0e4TFZ6nZcs1d+1bOQwOkr4+ZYPybI3HUnci\r\nNSB6mO5E3KUCLY/RAii0ZhAj95a/cHU+i67YG7BG9lLl42zhiVB2rgXbqgMn\r\nbhQZGeW7fkho5tu47G73FlFY74pHILePdlA=\r\n=nikv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/877dad11a2091f9c40195005cf372f8b/vitest-spy-0.28.0.tgz", "_integrity": "sha512-gYBDQIP0QDvxrscl2Id0BTbzLUbuAzFiFur3eHxH9Yt5cM6YCH/kxBrSHhmXTbu92UenLx53Gwq17u5N0zGNDQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.0_1674466062777_0.5590035201735544", "host": "s3://npm-registry-packages"}}, "0.28.1": {"name": "@vitest/spy", "version": "0.28.1", "license": "MIT", "_id": "@vitest/spy@0.28.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9efdce05273161cd9036f3f520d0e836602b926d", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.1.tgz", "fileCount": 5, "integrity": "sha512-XGlD78cG3IxXNnGwEF121l0MfTNlHSdI25gS2ik0z6f/D9wWUOru849QkJbuNl4CMlZCtNkx3b5IS6MRwKGKuA==", "signatures": [{"sig": "MEUCIGYtzxj4QE+KRM0ak4nV+Y5R080gktIyV057R/bbeOn8AiEAmccIX9RJZ9RW+JwGGaz3eRfmGVHqztnu0wPuBMsZg9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzlh5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqtuw//QkhhPaXAAFFqcqgstWbnnWkadJ3MzZO122ilsFM+G2LI/z61\r\n0OEMiiCU5I9+V57L3fcSutxwuUWZEDsvSt4WJ++W5+/tiQt59ONmiBBMKnwI\r\nXEIJGDH+Uac6sgdFOsNqWDPwGj8Q+G6wJ32Kx6hh5t30Yc1qSeZ3LD6p9Bws\r\nSV77gB4j46R1lG0pjqp2RGo5kW7nPh1ReYKzXoJSsUHNzbDGazFFEUc7HyUZ\r\n9Vx9WU/KYcLAD1B0RiJvH1Xplj2jGQypXoxh6PeST89/pbyptku/l3TvIl/u\r\nh/Z9Vv7HHRq9oZII5zRRSiOqTxVNU5sEFxrpsAzzGDQbepUVp/3xd6gCN28o\r\nEDzL0AHsQ4GNEIE6x/B+q+J1MTz/aLbXd/K/X0FUL5Myj5LGUX22prYYPsZH\r\noyT86y9cjSUVp0LqOWUaxRH0c9SAfzKfl9rjALs7cdsbMf6takoF1lBiYOsK\r\nuiLtS8qPBka66TZr9Ew78X/oTxVRTZSe+iIVHzcD8IMfX9Oadv8OJIafoUQi\r\nZevd22ER2LzucBknHUk//3KHtt8gkj0oKIdHpZzdcNlQ/DiotwlKg5VXQofX\r\nFTdpOvTALAsH97c5hcNidx9oMK4yk0BeqTE2wUkmXHwOItNpfp4ZKRR3rCW7\r\nZCzjWfu9yHhSKs6Pj/NU2TVbj02s6WOKb0k=\r\n=MI6T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b5c0e1da41650b908d77e0c8c823f272/vitest-spy-0.28.1.tgz", "_integrity": "sha512-XGlD78cG3IxXNnGwEF121l0MfTNlHSdI25gS2ik0z6f/D9wWUOru849QkJbuNl4CMlZCtNkx3b5IS6MRwKGKuA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.1_1674467449685_0.02038106253698735", "host": "s3://npm-registry-packages"}}, "0.28.2": {"name": "@vitest/spy", "version": "0.28.2", "license": "MIT", "_id": "@vitest/spy@0.28.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "74d970601eebc41d48e685323b2853b2c88f8db4", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.2.tgz", "fileCount": 5, "integrity": "sha512-KlLzTzi5E6tHcI12VT+brlY1Pdi7sUzLf9+YXgh80+CfLu9DqPZi38doBBAUhqEnW/emoLCMinPMMoJlNAQZXA==", "signatures": [{"sig": "MEUCIQCqcfSOWzhVUk1tHMVu2BSHlEnWPnLe4PYW3u9OTfQ5CQIgB3i/HGNhYAUsgZ3hd+L5tCfRQW+xAKdY44CQcdfY+YA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0RCQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp43w//RcNY8ZssGdel/dS2YiKux/IHz4Hsyw7JHms8cCsz7QinXC4o\r\nX9tyFVLnsu/B1Zgaepv8UJ7Inlratso9DmaYgiy+YVBbRdlhJT05w93EOiCQ\r\npYbkJHBtS6PvZfKevGiWw6uNUpHQBp7vATR7aG+N+Q+NkW4Bmuox+/w3O/2u\r\nH7qXdX2oj3vQIdMTeENgvz/u8ZYE84sRamoiksFFVZq6Q4r1qo34wb8rqmvi\r\nldiWE92fysB2lrKXmDKuzPjqidkDQJ7nuASPHGprYva7hT7nFfE81O1qZrGq\r\n5/hi2m1kxBSu+Svpu8Rb1FYTLSyWQsJAP/qujSrtiX4DYokCnn9DGp1kcSlg\r\nF4ZfUTFgeUKNKFMLk1Y1mvvDotSstaWKDpoU6MgFsZcP72SFzIetYFCxMKiP\r\n7X8i4orNMV1Psgqt5/5eR93n10rIEjz5bFtc0q7hfGwaJK0Cg4kEHFArwBf0\r\n6Jq9GJcqyPlXEQ2L1hUJcFDA+32u41/F4By4jt9iX8OnvbJKzlo3H+zmBpEk\r\nuJ7Xu5vSMZIfc5eiC8e6jpwNiXJnxzCWDiqgkWC61PuvA+1o52R128nCgZbp\r\n6FQT7jqDBoGirMFYjFeDO7OQvQ4SO3irBl/nZMYK0BEpchnxgy778o30NW0f\r\nXwa3WufHnKDAeWthAOwIVwAydPUoyrJH59Y=\r\n=2fMV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/30/nymxcyb909ggq2j5lwn7b_600000gn/T/cd45e30804ae119d0828a56f73d18f09/vitest-spy-0.28.2.tgz", "_integrity": "sha512-KlLzTzi5E6tHcI12VT+brlY1Pdi7sUzLf9+YXgh80+CfLu9DqPZi38doBBAUhqEnW/emoLCMinPMMoJlNAQZXA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.3", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.13.0", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.2_1674645648486_0.313500**********", "host": "s3://npm-registry-packages"}}, "0.28.3": {"name": "@vitest/spy", "version": "0.28.3", "license": "MIT", "_id": "@vitest/spy@0.28.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6f6f7ecdeefecb023a96e69b6083e0314ea6f04c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.3.tgz", "fileCount": 5, "integrity": "sha512-jULA6suS6CCr9VZfr7/9x97pZ0hC55prnUNHNrg5/q16ARBY38RsjsfhuUXt6QOwvIN3BhSS0QqPzyh5Di8g6w==", "signatures": [{"sig": "MEUCIQCvvDVsRnmF9MMkw4MLzPG1lN047uc37jhDGs5/h+pBvAIgSfbBg7iXvftm+0SxjiyjDFHrtaAerh/dL8knbqrY158=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj077+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXpg//Vh6ebJMIYSwndIOBZs9rwjV+t89vCxZMYQBr63hwjwIMrvug\r\n3ONpjuduegkGva3m4eoU9Anx1zH3vjeNbg1KeP6R6iYjxXEoDy2Le7Lo3kiE\r\nVRnA0VtebJP6My+6/cPyvB1JFwsrF3cIbBCG3aJxrzXWcft4+84b3uaZiUpD\r\nbRQWztbJ5+1doy+skvNZ6KmvYylWn789MGF61Q/fyMIaRkHVFEITeN7RxwOP\r\nxYOzNsLJaX7R4bRs1SSomjvONhfbbUbaCkna2aEnvfvnbx278LosvCdKawsZ\r\nkpj7VbTEdL1ET7hGPEuwSTkDukyNZ9mPwefieOPyJuaqESD2UnK7MGpDV+/Q\r\nycsOYgdW9W5L7EI0diA6M3Bpo74+XtfsIUP5VUkfVHIUdqEgrqbu5wXW1XP6\r\nhQW/ZdYhpMusxlh46wlDRWyZWzOzHzBtS8VhKnjuoanwwUHBxh1zd1QAr9he\r\niGBQteqhJgnIElcYAziiAXiDw8t77qeYUn//MX0IO7m43psqOtVKsCNrkq3c\r\n2aDQ3TyXe9jsjq5cJ9jB4pvKxaOwlA1oRLzfUDN73XOjtbIl7C5IlKtHJcVj\r\nuPYL/I8W2gxli2Z7c3KaE9B+9UmBPfOmq4z+V+fg78yQ1bKSQJlinr5bYBTU\r\nTvgEI58QICRvnawyGqdJRo60TVW6AuJWw34=\r\n=tpIp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5293e2def9c34e6bca21b57ede2fe160/vitest-spy-0.28.3.tgz", "_integrity": "sha512-jULA6suS6CCr9VZfr7/9x97pZ0hC55prnUNHNrg5/q16ARBY38RsjsfhuUXt6QOwvIN3BhSS0QqPzyh5Di8g6w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.3_1674821374094_0.6623842970444065", "host": "s3://npm-registry-packages"}}, "0.28.4": {"name": "@vitest/spy", "version": "0.28.4", "license": "MIT", "_id": "@vitest/spy@0.28.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "beb994b7d46edee4966160eb1363e0493f9d9ef1", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.4.tgz", "fileCount": 5, "integrity": "sha512-8WuhfXLlvCXpNXEGJW6Gc+IKWI32435fQJLh43u70HnZ1otJOa2Cmg2Wy2Aym47ZnNCP4NolF+8cUPwd0MigKQ==", "signatures": [{"sig": "MEUCIF1YITceQqWFgB7LLYgwvjPODM2HofSN+vI1sFmxtnvuAiEA6INHSp21c4NBrDA3vpKoeTt686mMbpyQQu6XGepwPGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3NwyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOPhAAm6UwnHctY/WFDCvSreRdTjgiTHo6n3uoVJ2CDPzdy5kUuxhZ\r\nkXHVNBD0tovf6LjsME8BhS7upkAwXM0zhKmy6POkez7pt4fKRZfVTI7JNlFF\r\nuZsGkE20hVESSUg48HifZxJ40JnMm8FA0gW00tGozSIvAbdM8sAASSKbTc0b\r\nBvCdRgWjXYV2al8rLVD82u54Fbdtwsqye6XUSpzshmkeUf24ykTLJ5NDNY55\r\nbKHO3baXdUE0Dwc43M9TF+wap9c4KUJiLV9tVxs4rpFKtFhYU0O9rrBA7cDC\r\niokRGX0gfPUuW6xQqzli6FW85ojaHnM9sQRdXFkHfZu7Sq9nV2y+7tOdWMro\r\ntGs44Eo6Rw5Go/LgcTgXFXUUzhkI51qapn55tas0cm7vgvtp1zSmrjpUWh5z\r\nU3qi/C83mOVCGQDw5tJAgIr5KQa0IUAA2+69BresbBAvucDFzxTzlrKjoLz0\r\nOYMW7+nzsidcvuIXsT9hIEMkG5w1diy1tBgDnWubldzjP2v1oJbI+X3TT83H\r\ny+AXn4TDcTikyED787KEzYoP374VNrcyMWkXe8Kz6CgVTJo6R+VbDtPmPJbn\r\nnZBY1XspqqNzkULHUzoFy0Oxv+k4iEtkBzGRY5uV61L6TTOskT6VeuFtTPVy\r\n5sCThEUvpwdPhfR02jlejH5VNdDfK859y54=\r\n=YEZn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4e04bb01f0ddaf52ea052fab347b3c19/vitest-spy-0.28.4.tgz", "_integrity": "sha512-8WuhfXLlvCXpNXEGJW6Gc+IKWI32435fQJLh43u70HnZ1otJOa2Cmg2Wy2Aym47ZnNCP4NolF+8cUPwd0MigKQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.4_1675418673826_0.29293664514913487", "host": "s3://npm-registry-packages"}}, "0.28.5": {"name": "@vitest/spy", "version": "0.28.5", "license": "MIT", "_id": "@vitest/spy@0.28.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b69affa0786200251b9e5aac5c58bbfb1b3273c9", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.28.5.tgz", "fileCount": 5, "integrity": "sha512-7if6rsHQr9zbmvxN7h+gGh2L9eIIErgf8nSKYDlg07HHimCxp4H6I/X/DPXktVPPLQfiZ1Cw2cbDIx9fSqDjGw==", "signatures": [{"sig": "MEUCIQDPYlGT9dQ2vYbb872SQT3sB7gs8Gl+gW2ivspP1M2blQIgE2bAkKpGnLbS/IFoEwpNEbBzEs6cho1PcAjEyhLXz4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6iu4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpd7w/5ABBOeJz2M1y4dplpCpSEOwjPeGU8ZZYl/G6zE+V2lDU9eVEi\r\nr4lU8AhA6uoACSa5+qWgowZKVg26BwvTAax6ANHzs5ZSKEBVMselo4u1/OUN\r\n/MGK3oEZqs4aXh/RZ9/8OUAOtlM7kqgmvRL1iYmfeZWwIA8lo/xZR6IKotsd\r\nYv5/BaleFNvIRB4aa3sx4+305kkAwUZBGHwoV9GorNp881hl18VbCe6SQyjW\r\nDEepGlV2xXWRkfTh2tNqHqjj+H2OYKU/M2Apuhx+/n0ekS+l+VZIPwnMfzhY\r\nIAvrxUGsx3MTdYoMazhl4YMRJ1dmQMHJdtq/HQVuKfZAw792BNHRQeSH0SoQ\r\neU8178ji7MlLnXCacuqeiXk45HRWIjcnB5qf7N45Zgz1abN/QB5hma0A5DbV\r\najJPmBH9J+01drJ9YK7X/sQb1eHQVRnr5C+CdZ41O1Yokt8iIlF5NXhpCLob\r\nR5ejtbaalmrt7Ja9WTfQ11N6+biE9fuDtSVl+sXOZmzjZJ4E3a+j+a0hvTMz\r\nT31YNrsCbMmk5/GPLyVrosCaKbLL0UTtdP4Fw/vJcSHD1tVR5i3sMh0/s9L6\r\nPAeaOuo/J3flJtxjoCsh8ca4jC2LzVNXg98pkb05KzJW/cklDHVnO3ykiG+6\r\njnxNFa4o3KXrANv/sOQYdYXBx3arWFs7XoM=\r\n=J+eY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.28.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/603fb48382af896c0a70e7080110a20d/vitest-spy-0.28.5.tgz", "_integrity": "sha512-7if6rsHQr9zbmvxN7h+gGh2L9eIIErgf8nSKYDlg07HHimCxp4H6I/X/DPXktVPPLQfiZ1Cw2cbDIx9fSqDjGw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.28.5_1676291000192_0.583744197031898", "host": "s3://npm-registry-packages"}}, "0.29.0": {"name": "@vitest/spy", "version": "0.29.0", "license": "MIT", "_id": "@vitest/spy@0.29.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8867aeb52d4b11b93424dab2a4531077b894fe4f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.0.tgz", "fileCount": 5, "integrity": "sha512-h8xSW5ZBI9dwM3p854LCDJCeKGkiyZzI9CtctOBDgj5AQzDFu7xI0kAGcrwm8z/sBbcibDJ6gzz2/gyr6T0kPQ==", "signatures": [{"sig": "MEQCIAj4ZwGDaTou37JRlNtr4lBEF01By5Xpy/GYvpHWffYkAiBRVtxi5YLP8UHSSMNJwxS/IphDfmgKLhYl4H6e2s+zXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+cXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnMg/5AOjWGDRMbDxQt/jZ8o6vMvykPs4rW8njm+diVh52M2Gd4rF4\r\nNFNCKZgOSaBcIcLh1L3/kCXBSzD7dXBzcTNQFJ1YvU24StGFMxRs0Oq1JNOe\r\nAJUKuupF0NCBPGgguHdd/Y1LKtM+nxIh+SRo7yvqH4XfFKirw6OJIfLqbCLd\r\nAo9HwPo+WueuN3jvRNle9LbfgjTqCtMaaxf6Cq3IDauDJconM4zwqChVgfr3\r\nw6UdA6JJb+K1jFyMi3G/6enPP62titEOAALWBqlKf+As+5+MhlHTd3/Z7oEc\r\nSXFiuViYT/IfPeY7PPlrFOinJzIt4CaoMKTPh2EWzMB/GiizH+oMPGgR2Y9h\r\n2cf5B2YuyNGiAbK9NFDZiBm4GYc9SEKGEhlmOOUm/OSMwW57O4lqaeCaFgOY\r\n+PeY5Z5YvOMOxplM2608zA16bNEIHVbrwcwBDbpOajAYb4he0+fPKr6si7XR\r\nKzo+3wUAxYe4cahNejR2btVQEtxojssp/KsfF2coRSUFP+mcOlv0sy1/9p+i\r\nzNwptAIDmbMHR5x9iSN/XTCIMKNoFZTQwvcz3uSZ5A0pqzGuM0wx/v4/+n9Y\r\nixh/WVbtFBg4zpyza5xmJ2PJfLO2YQSQIf4slWhDXjtu/QeES2agBeyKcEQT\r\n9GsyLcPxBzLTBW4Xi2n2cr6IQrt94HbIN2I=\r\n=e9B8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/71f98a9093a8e2ade6305aa21750ff7c/vitest-spy-0.29.0.tgz", "_integrity": "sha512-h8xSW5ZBI9dwM3p854LCDJCeKGkiyZzI9CtctOBDgj5AQzDFu7xI0kAGcrwm8z/sBbcibDJ6gzz2/gyr6T0kPQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.0_1677313520980_0.5055553403297994", "host": "s3://npm-registry-packages"}}, "0.29.1": {"name": "@vitest/spy", "version": "0.29.1", "license": "MIT", "_id": "@vitest/spy@0.29.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e84f4d96a268e555e8140aeddcda65d6fd4e1d7b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.1.tgz", "fileCount": 5, "integrity": "sha512-sRXXK44pPzaizpiZOIQP7YMhxIs80J/b6v1yR3SItpxG952c8tdA7n0O2j4OsVkjiO/ZDrjAYFrXL3gq6hLx6Q==", "signatures": [{"sig": "MEYCIQCGNAxmoK6LLTzc5Wf6evj0i4wVi+tfMdgjTtlFcxua6wIhAIqbFZAC+jE4gYKRdyOI6C3zu3IKapLpHaiUJPvNOkZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+dPSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvqxAAozAb8gqhCYrdqJ6e5AsS/36er5E+r9JMOCcn7BkI+lGzBla2\r\nbVr1OchNQzSuBYDM9ZgOtREPRy/UAtzWMrOQe98WG9XQEBGq/YxJlzMQJx92\r\nkllC9t0Ua3G92G5EA8zhBkacNueyvXX4DI5VBy9g7hWCwqccj+q10AWEfkvK\r\nqVZ5SMj9ApHWg8hfLlo+d2k0yh7Gj6DmUccc6VyEyBV8joeK+mnTIqCpQo9A\r\nArnxqhRPpmQhAt3Kwud25Ymta2gBSeuZeVGS1WpNwa3Cx/DmZJsb0mSzWb6P\r\nPY6ggSfxsIWDp5uXT2NrWhQxoc2+blH287VV086ET6EjFDe8W4ceXXmrzz2K\r\nLwpsSUDr62tePAJ9L0Vcvz5HswivwWe3LsQxYAYmKl018BjeIyKsrHPACOsf\r\nDphz+NrpdHTb2GbtFrtzRiwryr1VwlP2AXGZpmFmxPBQ9cVgMcpt4iugY6ZT\r\nz6zlycxOLIn+8YfhDBRfnexCj2Q2qzJbCB+xruTk5XpjNvpPOYrRYbUI88jM\r\nuQYm83da9b/qSTmxptMzad9suVJHGxCR0zFHe9cyyhHQM7ShA3SvM3S2un1l\r\nkL9TBkSvVXI5uiNcVrYhGDM5/PoMTJhHqAI5ihC5WpexNfs7sf9MSpjJcEeY\r\nTbcaw929alvcvlPLabKJ9yaWyUIAjDBBrvQ=\r\n=sl0R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/27a1142ccde61d8e04314cfab4ba67ba/vitest-spy-0.29.1.tgz", "_integrity": "sha512-sRXXK44pPzaizpiZOIQP7YMhxIs80J/b6v1yR3SItpxG952c8tdA7n0O2j4OsVkjiO/ZDrjAYFrXL3gq6hLx6Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.1_1677317074092_0.5714435257518053", "host": "s3://npm-registry-packages"}}, "0.29.2": {"name": "@vitest/spy", "version": "0.29.2", "license": "MIT", "_id": "@vitest/spy@0.29.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4210d844fabd9a68a1d2932d6a26c051bd089021", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.2.tgz", "fileCount": 5, "integrity": "sha512-Hc44ft5kaAytlGL2PyFwdAsufjbdOvHklwjNy/gy/saRbg9Kfkxfh+PklLm1H2Ib/p586RkQeNFKYuJInUssyw==", "signatures": [{"sig": "MEYCIQCgFYGNHGNjYWI8llgBFDxn8/DHBHLpxymEf2/VMbwjjwIhANlP7q+WWr8nHlf+yuQ70LrV46DRhNVdgWKSGSHM5Q40", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/howACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHpRAAiccsWjIXQUs/HWvRHOvv8jQecOhRbMdi+5O3ghO6W8iuk9Oh\r\nY4pmj+EXsdgOTyHyepPCodCNaVc7EOUdhoLJ4QV8U0jmvpkBO/7K38c/xhEs\r\nYc/dqcgjdDJeSXQt+5PUTQdvqWtCODMcoc8OPi8azVqRC/Q647O6KPDk14xT\r\n6wM6C1Fklk8SHKmvLU9hd9jkXMCD4nbo2Y4HrMz56oogLCuhMaldGpB9wXBT\r\ng6dY0qg00WAfJ7Eup0pt6Avp19VjnwIxyFbClaHhLB2FUeJyH2YoSCTwZ5u9\r\nAcJBbhJpKIpfxcFLmV35UCeDNp6s9XNISjMryxSalop6C7frdLSYnZ60rpJI\r\nt3t6f4ps0BoCu/VU7HXeJ2tdXdzLeBHBNTox2cxdBAifVVWIXBE7aNsAoqM3\r\nnh9w0rFRhtg4ExdSC1/B4sE+UI4rjcQVzP6NQuJ9UV3foZbaLBEqvKeVHr+4\r\nzrlc7Av3r6XQ+9xoNpJRaqZU6EvK4jQbrHhdkysHBnDk/HxW9GoIV4FRvLya\r\n1/9gBpB1s0+0Cmol2zOSbnIeisOQgfBKGSMWNKxXaPeODskw0x08O7o3yjXH\r\nh7E/EtS2GTuO1NHjvOVdxCbWZQKzbnHmVd0K+UlATBhqFqf6rzBVmqbUE12W\r\n7PqMmDkkMu+ghBv7+nD5e/wH/tzlnUwgnW8=\r\n=GZJ0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4f083ebac4bc9260cb1084780b7734fc/vitest-spy-0.29.2.tgz", "_integrity": "sha512-Hc44ft5kaAytlGL2PyFwdAsufjbdOvHklwjNy/gy/saRbg9Kfkxfh+PklLm1H2Ib/p586RkQeNFKYuJInUssyw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.2_1677597232438_0.5611652076143949", "host": "s3://npm-registry-packages"}}, "0.29.3": {"name": "@vitest/spy", "version": "0.29.3", "license": "MIT", "_id": "@vitest/spy@0.29.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2260daa94da036d44a1c91c56cb8e08ca1c189df", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.3.tgz", "fileCount": 5, "integrity": "sha512-LLpCb1oOCOZcBm0/Oxbr1DQTuKLRBsSIHyLYof7z4QVE8/v8NcZKdORjMUq645fcfX55+nLXwU/1AQ+c2rND+w==", "signatures": [{"sig": "MEQCIB3RdN3vdMOWHi9jsq6ySISzrQ0/QkTGlmbKqgEZbfweAiAFkXSRpVBFcP1Ysv1ZFzebgwdSDyByWGzQ+VFoqR8iHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEiSRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdlg/+Kk540TyqpvkAbNl+sjJHKcnMld1NicP3o2BFCHYWqs9WE2WP\r\nRZUGV7BkuDyF0UB4ZhEy/McsrQj6zjbC16MQekoKfEwhmu2eaZkx0WQqJcKP\r\nO5+5RmlHry3Vt2iddJVikPmgfFKx1K2RNIiT2RTF8nrFcLG4hQR4XagdazjU\r\nQwnDs+ao1Q7cvHUmOnnm1LVGxURW+Cp1CFJ1f8KTcGYMZuBUDllk+TbmF1y5\r\ns8fxEuP5I2PKCIIDh5fkR3zyOmab8ET2esiMJFc19ttrJ/LZ/R4r44gJx4Hj\r\nT7qNIheRtA+0zxRdMcdC90RjdPDhW5GYgrGs8Sma13mZIoHLYUAFbFbcl8uR\r\nj6xjk4YxeuZyC++AE0L/63l0YTjCs4gPB3A2O5hoO5eau+fqHhOo3bTnkTf5\r\nJkc2WTLJa+UVLlZYTPgXhBCZdhJQMzEGQRY+fnzvvWFTvwMzQSNeOKvb2cZb\r\n+0z/tgQjomizb3JqNleqfVI8BZS0yN5+Vu8nQLPX822HBIfYzBcXgIs5PqcP\r\nok1chVCPBgIMseQkquE5deM04C0QLFItg8dgp6x7FTFEcYT19352ynBsxaA2\r\nlLNa5XlCL3NvZCIYu2uFAFBxW3gsxQZwvgPU3s3FSW40pwG3VvKNu42uyw3F\r\nhnvhx1szjZZh7JaWNaoMxHkK62d7WgVz8eA=\r\n=cUMJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d9f43d6dc579c9180e12c99c298c58b7/vitest-spy-0.29.3.tgz", "_integrity": "sha512-LLpCb1oOCOZcBm0/Oxbr1DQTuKLRBsSIHyLYof7z4QVE8/v8NcZKdORjMUq645fcfX55+nLXwU/1AQ+c2rND+w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.3_1678910609168_0.8905469308406027", "host": "s3://npm-registry-packages"}}, "0.29.4": {"name": "@vitest/spy", "version": "0.29.4", "license": "MIT", "_id": "@vitest/spy@0.29.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b19fbae3cd51a0e0ddd9c1ac48e6d7b25b755bba", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.4.tgz", "fileCount": 5, "integrity": "sha512-gXSotZUpV13votTAHQpFQZ/KBLzWlmYh1Hhp7mvAGZ5kQrnLyU4VB15OgpsNpsCbJPNCpdMzVZWb2+WMDbLRag==", "signatures": [{"sig": "MEQCIEA3fOmHljxJbwBDbwLVM6YVhMBsXEUCASN0tsScFuHNAiB5yWh3xLhdmFEQ0jl9fEHocbtUwYGigEz4HuU/OHqOJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGI5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJjxAAgBKTLDTrpVDPkd9rRRdB/4N9IPdzc8NskIkxzZWRBMN5UhdB\r\nbtjfCE6UrH4y73ncECJCWEcKBMZCY5zvaWBJeqJckYNB2AhYHO7o93U1HzkT\r\nnS/PDiaGGA23VQSpP3Jp/uhgIlYf/woTp1vF02HYuFDpkgVCupNlw+JOuKZR\r\nhphLgsItV/n+VENwqWOmkxeb8opDuSRsZ67z48coPHu+ZwvoWXMxDnIIFSKm\r\nXlvwX9xUPQiAOWctqG2A7XHNDrQSapFnAOSficyE27xwjDvZ4k2uEGssXj1b\r\nJC3bkM/fbOHODSbdBMelvz9XF3SW914+Q5Wci75MwToBu4AvwiZ0fdE7LS4r\r\nu0NXv+6HhiY8FyaKmC1zhL1xTKratDpmCS3duguGWFu+S3hnpP7F2KdwTMej\r\nv4koo+OMFXj1wwQlNcTJB2sg2VySCmQ2ECPBYu+5vi7ItPAX4I/j2Jnuz6Ot\r\nhobvlNToHvndFgk7NKm18La0TJrHuiaxp0o+09L8fxQbu6vA7JgRRvnds5T9\r\n/omx35kSjMLpmj6RrWwVLJ1ukBW8glRf/rjM0Q3Ro92Ndl+ceVWBFwudfl/r\r\ng72NVNs64zP5jReIrGzsThtgNSzmTUeR3BB6ZqIaJlKTnKZtwIoZK/0s+dX3\r\n4REq2Re6XJH+P1j70hpgAAXzmBGr4OtFDFo=\r\n=o/Pr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/52a86428145c67190af8c8f1697b73e2/vitest-spy-0.29.4.tgz", "_integrity": "sha512-gXSotZUpV13votTAHQpFQZ/KBLzWlmYh1Hhp7mvAGZ5kQrnLyU4VB15OgpsNpsCbJPNCpdMzVZWb2+WMDbLRag==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.4_1679319608899_0.40041336310937115", "host": "s3://npm-registry-packages"}}, "0.29.5": {"name": "@vitest/spy", "version": "0.29.5", "license": "MIT", "_id": "@vitest/spy@0.29.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "24edcf375b69655a4dd5787394b0b7882aa2b568", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.5.tgz", "fileCount": 5, "integrity": "sha512-Qei5fNbRq4gsqxJEsjpKrgGMzzpIo0b10OoLM1bYJvsYb7e35xcge4A+VFtFXk/x182iAPsx8tK7ViMQ/mlflQ==", "signatures": [{"sig": "MEQCIBzRaHshBiF5FfqscTRTRkgis+zgzCxEWpbnAvmP7wyzAiAl+MSorxw033c+9k8/2WiLAOqMJn8qieDQ5sgcg7NWJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGGmpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotlhAApBJvONb2Fa8MKoHj9flkIPCvitBdYHISdooJ2ytJRI+75RiQ\r\nacv2c0ij7Xyw0QuVU6akqf2CeEdiGOXr8raLVos79Bx/XE9mJzBMj8QX0Wf+\r\nEOtxW40h9qwfewZI3eIRXPOHMgxL3ZA11v4Vb1Gaz2Ae72Qq/p6JA4lfQPj5\r\nRaoRPRv0dO/jI7VYWZc5qZC/aAB/1Uibb+hP8wfw2d3v1+5P8TpzGz0/biwB\r\nDE21xRiD3E+uljcW7t4536IjJAhIoV66IxfMb1CEkY+JEbp+wNq0yJ084CFo\r\nRYRXT4cg/1Wj8rzdn6+/prJt7Jj3ZCVQLFX4eWbEosSW47cGB0Pl9kBv5xcP\r\ntLRBwDERCEf2IEiXKC647QNI3qG1i1h2YxT/8VqdDfvDThOHiq9aFEEWBbgS\r\nOnEfa04cZJb1L22gaYDIFXAEDUL3ryUehLIYs+wUVGz7EFNXdhea7EthoApZ\r\nDiTLSGpfFo8KLsIR0sppraARLOdqWY3EU2mULv+wIYlMqJZUWt34CQHbtOAq\r\nyNpFdzp7bn2OUucPdNJ5Dh4336VVpVxaiK48nTiSE8O2jDgnCml15IA3NJog\r\nNL3uIM1YVuugjBd/f+6CxBpoDuXmGQC8e0MAHd1zoSxCrl4F5rsfRTCvoAAl\r\nIHUzmMywBw2UBhZdFB4/0vUVj9GuM7r2lw4=\r\n=kIXT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/7ee1a4a54d7a2c669c24ee0f61313dec/vitest-spy-0.29.5.tgz", "_integrity": "sha512-Qei5fNbRq4gsqxJEsjpKrgGMzzpIo0b10OoLM1bYJvsYb7e35xcge4A+VFtFXk/x182iAPsx8tK7ViMQ/mlflQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.5_1679321513254_0.8361337795381305", "host": "s3://npm-registry-packages"}}, "0.29.6": {"name": "@vitest/spy", "version": "0.29.6", "license": "MIT", "_id": "@vitest/spy@0.29.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2714be2f970cbf2497b499dcbd5ae2e1da2cfb71", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.6.tgz", "fileCount": 5, "integrity": "sha512-xHDHbSR4zNdLtK7U/HUZpniGsGc8dKHGF/ADdarednsWcka8a7KjuCHJ9YIGaTubwxa05spcoX6B4KFDRYJ8Tg==", "signatures": [{"sig": "MEYCIQCRwZkJNW3sqx9fXaTNAmcQL3a8YcbpVIud3mDHazW9rwIhAMDCg5U3FLdeKUTkmq/nvLFaXcGc1VV667jnZCgrBZ5z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMC6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo7Q//QykANkJwgodnJWsWxrAoDVU0NHyq5Mxk6X72qyMExtrTkdB1\r\nXOcQs3TWJj194U2G3j6w/o7xAUvWVjAPQmynHrLwdIISgr1otxuQ4J78KqoN\r\nbN6Bqd/rVBftWscCIjU+GvmK05rSmGZhJ2bdY8FTNQRlwsVbLsUi+NWWT2vh\r\nQss9DC5j8sV6NWiUSNvnkkOf0/yJRMt8ehEcog4UQdueQecwj5WkeYutZkdM\r\nex4O5GUoCfwz7OvdXkKfcJ5DHvvPzA/6zNeXMdctxs7lNTNj63SDV9i4VLmD\r\nxBtKHqMy/rIfCNIOJqPnew2m1uEneYG2cnCSghaV5teLNawZFbDP2wbdkkgY\r\nXKBtby/L7l3Onk6T92BPfRXpbnW36TPoJNPdKafm+Kop4Jd8JgJ/EWNded/e\r\nwsPtUvz9NDNdq5Hw6sO8//KDdsfUqLF9Ff7rGt87nkBkLJJVvtrYYONBWtx+\r\nCR8DVPQp9jyQRhk8A0d1Wq+TyF5wgBp75Nf3mb2pro7KT9lsUMOU6jA65MyA\r\nVEvyNZ7pHozttr7s1NcbH7OdzjDJjEFAEU7VgXFP4tOsay2k6pzY8wWzsIZd\r\nYPcnQ8Nyp1FhdjVKBrLP/Gpiuir+BspQy8jhVDxwB2I0SjX7Go1s7MrrEpZA\r\nmEIYybeWHVg8tLUv9eyrbcy9cHFOMogaHqQ=\r\n=98Qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f7fd11af6753b1a23e774f1474e8289a/vitest-spy-0.29.6.tgz", "_integrity": "sha512-xHDHbSR4zNdLtK7U/HUZpniGsGc8dKHGF/ADdarednsWcka8a7KjuCHJ9YIGaTubwxa05spcoX6B4KFDRYJ8Tg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.6_1679343802090_0.9619787902520527", "host": "s3://npm-registry-packages"}}, "0.29.7": {"name": "@vitest/spy", "version": "0.29.7", "license": "MIT", "_id": "@vitest/spy@0.29.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a2506b4c546973fcad4a1ec6810d7975b95a107c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.7.tgz", "fileCount": 5, "integrity": "sha512-IalL0iO6A6Xz8hthR8sctk6ZS//zVBX48EiNwQguYACdgdei9ZhwMaBFV70mpmeYAFCRAm+DpoFHM5470Im78A==", "signatures": [{"sig": "MEYCIQCxFmidSlv9JmzDh/MpdOQC+epYLnn9PiZPOBZcr+2+hgIhALWeZCVD+LUCGcj9xqsIX7QARYJrpmgTqTqewbewAL1I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGMP+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2pw/9Ez3RTCj/DH9o0fFy/GJInYjdta8j6vnlekVu4rkclnnr5ojN\r\nYQU52E+H9dHW1LCBRiaoz+orKsZCbdkrpj/zpcyHBZ983t2b5erWbgPNtk+x\r\n8lECahHimH9svZ49cvZgdeiGo8M55hMmkRvRDH7xmq47AhI8AtBzCrH5bkUY\r\nWj+qsXa3EwMG09Zrqw3LA+FGBIJNADDGgYNlyoX09TKQ5K3IXchEdg5MKnLb\r\nh/l8ebukBKIYTvRFhrkv/P5SjYl4655gM9L/TY5gqxmtCMSgVFHafGw7PahB\r\nngAIu7ecFmfNl/AVRVWlm+Ey/qTCthypTlRiS0QxYgIYwiYzvi3qwn9lgjpG\r\nepD2e/ADpnVs8ncQ6YrmYR2G75AEXWaXL/V5XCyYE3sPXMaWqEJk51WI/e7z\r\nRz7xCPv0xeIvhskAkod+1AXyMhSkVOShpY4oblrpOhRxauUJ91NTIr+oQOQj\r\nAjc37l9l2yFE1xkTa7joJAgHzK1ZtF+ALUSGyTQprIZaM+nNAFLogJQcaOtx\r\nlduJMXV/mo6OiAbqjxHtaIIh2iRk6qDKcJYcDJAeTBzFD1D1jRHJwInkgtGC\r\ngttZqQnWk4sBWX/0rcGmUe6RNGrvmRpZhMYqjeGg1PTCpPwQRC9N6OolLs4G\r\nVBVTv0FqJSP3J3xpI8MxGmHPFRiK+TjRV24=\r\n=r14q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/143f59b083fdd5bc62c8950658aa4924/vitest-spy-0.29.7.tgz", "_integrity": "sha512-IalL0iO6A6Xz8hthR8sctk6ZS//zVBX48EiNwQguYACdgdei9ZhwMaBFV70mpmeYAFCRAm+DpoFHM5470Im78A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.7_1679344638554_0.011345332396129004", "host": "s3://npm-registry-packages"}}, "0.29.8": {"name": "@vitest/spy", "version": "0.29.8", "license": "MIT", "_id": "@vitest/spy@0.29.8", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2e0c3b30e04d317b2197e3356234448aa432e131", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.29.8.tgz", "fileCount": 5, "integrity": "sha512-VdjBe9w34vOMl5I5mYEzNX8inTxrZ+tYUVk9jxaZJmHFwmDFC/GV3KBFTA/JKswr3XHvZL+FE/yq5EVhb6pSAw==", "signatures": [{"sig": "MEQCIBk1JMMMupEY/dRCCus4svYekfJJJq8PIt5GONprTJAGAiBg2u1uI8KEPOV+BIW0USE5NTcB14bXxtVdLnKu1Q5Viw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIueWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9zA//fcncMA+U8NohHSrbKCwSwENvJ1bzd1fB7tiiJQfhRR7wRSQQ\r\nA63EogzOAIozUu3xpk81TwQCnr0pGMF4cv6wYKi/G0jLPS2zPVHYdhm5Wx5z\r\nBHRhZ/oHhs6HF1p/6wIq4HPdo2Z0LYq96bkkWyMlQk6hEEfChgqcosL/823t\r\noz7807AM/l8txA2lo0EkGyZrlYWv66avgiLBPpQ8RuQt0khDH7iN7qmuL+fC\r\nwmA/Fw8CVUqrBQh1ZoHcqYBSkxnpKAKyOToRGsCzo+8qN5FkfIQUxwB4Fp3G\r\nyYmAkUBaXYrE9PMwDPMxOfqPYMtRETdS9Yd1BFK9PbbSwNrt560ANszQ8oFt\r\nZ2QlTts3E4bMoLEo2/+kJ/sFBj+POv1LrST78PJDwKfOl6zzT0M0VsshPtnF\r\nNjZwkjcayGVceei1MlzjDTz4CXVudFnjaiBV0wnZj5L2m1gwdj+dteRc4+w6\r\nvy+cZKWXqwasPa6jcVyQKq9JE7Nk+1vBdpHsmEn4qZ5GuEQOB/fXuRtrD2Ff\r\n83es4qPscw/avZPuifBJAjrEJL9AMTv88Ad9u1zgTSGJNJ67s1M71auLv8xo\r\nfEYwCC2o1IwboWL6OLs5OZL7cpYKYzK86FSNuQAwVWcKLWLlblF7K0p6B+PT\r\nL3KsGMxy1brDsv2XkvkawMenpYwrwZhIJaI=\r\n=iHJK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.29.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/adc7ae8143d6585ba5355443e3c869d0/vitest-spy-0.29.8.tgz", "_integrity": "sha512-VdjBe9w34vOMl5I5mYEzNX8inTxrZ+tYUVk9jxaZJmHFwmDFC/GV3KBFTA/JKswr3XHvZL+FE/yq5EVhb6pSAw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.29.8_1680009110124_0.888670581566551", "host": "s3://npm-registry-packages"}}, "0.30.0": {"name": "@vitest/spy", "version": "0.30.0", "license": "MIT", "_id": "@vitest/spy@0.30.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9e1e81d7b8ea3cdd0d67a20334b63b9b52b7a2f9", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.30.0.tgz", "fileCount": 6, "integrity": "sha512-olTWyG5gVWdfhCrdgxWQb2K3JYtj1/ZwInFFOb4GZ2HFI91PUWHWHhLRPORxwRwVvoXD1MS1162vPJZuHlKJkg==", "signatures": [{"sig": "MEUCIQCVrmvcO0uY4GP/wDvGLSKGoPDSscS+PQkggz8eXjD+CgIgDENCtTsiUMy42DbdGC/2T8a196Q38w1EiVrm831lw7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMr/UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRwg//YgWXseKSYZ5RY5yvais8YRRt0QFPASQuzl0XrcCt/KS1bq3T\r\nBx+civADiTBv3jXBXoun246eHUhlJcPkHVeIdvEiwH69ehi/UoCpCtFCbWtA\r\neQaXhH+3UJGUQqFHOn8QCC6dj9WfHcwGlpemGk/6TPSWfq4no2ujN4E2OCPi\r\nBOJ6ohX+tFBuYQ6/g5uYBnfWU3SM80Avl8IPk/ZYKdcIwro9CvEciU/xIRFd\r\nQsP9+nkI1Ue2KdLpqiAieqVt9q2WUxqDVVGxxMpq9CVG8JT9ufW95xGQMtKx\r\ngAJfTsM4zCs1KoeOls7CyGIvDm2E+XYgsoSMi9RBzelQnGk8Rd94l6Yqnzq0\r\nrWIruEGjlrulczK4VUXqIoTFHhUO/wrQbzIMWVU1LaWBj53tyTKZgN3R5Rxi\r\nsG6ysFvOF6mGh2X/622qVLdPu5F5NTso8V3T0ncWEqhGMMy0/n4M1J30efWd\r\npupuFOA5BF09YeOXhY3HF2DzGuv4vlQZIq0hsKlwbsWG613fjTX0gnlKXPfw\r\n28OzuF1qUim/Fd3aJRLEIZVqqgQxSa+UoEkO3HorBxRA2jnAe2ugAMaU1P9Z\r\njgW/BHV6RnWwu4f5e8dvGSVxAsvTyR527sFpA2K4OcZW5+If2yQJKM1PLhjo\r\n2dUJ9RKTk/YAxoRQm3fUuBlCJMHyVuiL0lQ=\r\n=zuT/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.30.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/57429af8813b5a4b55154146292aff97/vitest-spy-0.30.0.tgz", "_integrity": "sha512-olTWyG5gVWdfhCrdgxWQb2K3JYtj1/ZwInFFOb4GZ2HFI91PUWHWHhLRPORxwRwVvoXD1MS1162vPJZuHlKJkg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.30.0_1681047508529_0.9970077639319108", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "@vitest/spy", "version": "0.30.1", "license": "MIT", "_id": "@vitest/spy@0.30.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e3344d4513407afd922963737fb9733a7787a2bf", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.30.1.tgz", "fileCount": 6, "integrity": "sha512-YfJeIf37GvTZe04ZKxzJfnNNuNSmTEGnla2OdL60C8od16f3zOfv9q9K0nNii0NfjDJRt/CVN/POuY5/zTS+BA==", "signatures": [{"sig": "MEQCIDJ39Ix7bzpFiZ4VbybZLzMglGw6lCG7KYCNDTai3wbzAiBiVEa4yOdeYKuAlLCbvKSTkNuqbhBa45j+3HStojY5pA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUPWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrntA/8D+PMFq0EcYc40AglNxqR/8nNnx9u/igfnh7uE9tfCVyIoDIt\r\nhUdNC14vwBFc4KwcGtxagjUK+oB3taHkzEn/J9Xo1Kms7hrphrR9Thdylmea\r\nIBZbgHYdwa9G9fxOfOHqAbCPFok8q5Pea4D78dC79uMx7ca1ArtQV+1OrHg8\r\ndCLuccgnOO8fTMgaRxFYWgG0uNVShmjqHexzgbaO37FaiEw8ZnX6PW7ceIoA\r\nRgk6RR384cQ+RPll+68D0eRrIazLbfCnSq9E2oLdjrAA71C5c/dd64gC3G6q\r\njOpF6iGzA1s6O74j2A1R7Ynmuh3FsJUvix/YQ2kJaGI842XIu9ZUkohggDAy\r\ncoT6UhfsnS8f95OCQDqpRshTJWFDZrUNquxzZnePWb7mPZ87CG8lAo71oF/y\r\nUTvbHmXIMcOVlF+LOOYCx7D2PTDCDciaEKnVkxTWKm87d4w85YOzjvQMXqAB\r\n6/s13EHG7246rPdL3WSIi17E8VK2vBcRBhHGTE724kXiVjagr72IT6QkJOu+\r\nLLYMIYt62t7NdWxrsTc3r1ez6WU/7wcQe2Z3jvyTdZg8ae4z98D7sPCBJ4i4\r\nC//kmizH4UK11Wmk4M25TfWyRAtXf9UUEVHHoNTp6riIUijMkAVSJSom+UJh\r\n4jaJo9L711SpIbnFEFDn9pXnKGyy/9LIDUQ=\r\n=SmET\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.30.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/e96ef9bb36b1b572832b1c659db22923/vitest-spy-0.30.1.tgz", "_integrity": "sha512-YfJeIf37GvTZe04ZKxzJfnNNuNSmTEGnla2OdL60C8od16f3zOfv9q9K0nNii0NfjDJRt/CVN/POuY5/zTS+BA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.30.1_1681212374067_0.1254356667168417", "host": "s3://npm-registry-packages"}}, "0.31.0": {"name": "@vitest/spy", "version": "0.31.0", "license": "MIT", "_id": "@vitest/spy@0.31.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "98cb19046c0bd2673a73d6c90ee1533d1be82136", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.31.0.tgz", "fileCount": 6, "integrity": "sha512-IzCEQ85RN26GqjQNkYahgVLLkULOxOm5H/t364LG0JYb3Apg0PsYCHLBYGA006+SVRMWhQvHlBBCyuByAMFmkg==", "signatures": [{"sig": "MEUCICIz7fgcTeIP+eZSkIaPgIlaEtsQ1yE2S1MybB1QaiAhAiEAtw4HwbsOFQzRbh5O7Iyzqvn9BaawrzBE1mOCmVPnYjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqL3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7Uw/+OlDFVFAlB7dXqkdbg06exLWKGccl51sce/js/UpzF7vmdzpB\r\nzu7uXUlLBR61fbMFVX9S5ZVhPm9i2xFw7UJavJtVzZx1VUvkpYjro4DfBLOj\r\nkUTny6OWjybbeX4lhS7lWdLYcV8kO2fplq8uPZr4/F9gEGazSq+kLSf5v7OR\r\nqKrBZvhE6yMyWkHQzIkTBAyekc89gH/lGcwFQ06yXzfbx7wyF/LYoWSZha//\r\nBtkz/T0mMNZ1aEKx3VRxl7W0Tf/LmkevmnYfrfhT9ImYFTPqWUVgrySHvq5w\r\nVU8dYMweUv7IXYDdJtIJGFgQ6DN4ZOMbNKr7ErqxCK1h5JT5/X1CoMI1Sc6o\r\nTiWGaCFhOecYxK5Uppj80fuNgenWpPQXS9JX0EK3tOitY+b5B6vYF4qyNegR\r\nIpBowt85CKK6ipNynyRyHDFeGDRBCIipoRvgJ+bK8H491FjGhUHDL/nWvgib\r\nClYBNHGSEp44FYT/Z6Fzy+4lGHVlX7HeE6vOz0WVtsATviWiZCk7VOAUnCdy\r\n9BwUk5aa7ypyr5xNp68yjGnsVUCOy7e67jQuKvEIviAvhn7bdsy7ZhllfwiW\r\nUM7isajUUIViOpxPHWLWFf+g4kN/ZhfZgKVtwR1C5TJn3Jito8xZ726cI7P+\r\ncYryqnWYF1pJaZHg7adZ9UZntGsG+q2Z4TU=\r\n=QrBj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.31.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b3164e5127c8731318723845a38facba/vitest-spy-0.31.0.tgz", "_integrity": "sha512-IzCEQ85RN26GqjQNkYahgVLLkULOxOm5H/t364LG0JYb3Apg0PsYCHLBYGA006+SVRMWhQvHlBBCyuByAMFmkg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.31.0_1683137271560_0.84617066809229", "host": "s3://npm-registry-packages"}}, "0.31.1": {"name": "@vitest/spy", "version": "0.31.1", "license": "MIT", "_id": "@vitest/spy@0.31.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1c3b6a3eec4ce81b8889e19c7fac6a603b600b14", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.31.1.tgz", "fileCount": 6, "integrity": "sha512-1cTpt2m9mdo3hRLDyCG2hDQvRrePTDgEJBFQQNz1ydHHZy03EiA6EpFxY+7ODaY7vMRCie+WlFZBZ0/dQWyssQ==", "signatures": [{"sig": "MEUCIQD2KAalp6COwdDswUn8vDe/hd7LOnmGvN5/wN3WR50zcAIgZz2BWfCf/KTyRHVv+x1Hkpl/EyazoYKP9vc5tVi0/Vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15299}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.31.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3c18064d920f7f833c4b5a686339ccf9/vitest-spy-0.31.1.tgz", "_integrity": "sha512-1cTpt2m9mdo3hRLDyCG2hDQvRrePTDgEJBFQQNz1ydHHZy03EiA6EpFxY+7ODaY7vMRCie+WlFZBZ0/dQWyssQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.15.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "16.17.1", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.31.1_1684333385627_0.9951309636446786", "host": "s3://npm-registry-packages"}}, "0.31.2": {"name": "@vitest/spy", "version": "0.31.2", "license": "MIT", "_id": "@vitest/spy@0.31.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "43a25ed3eab4c4c326a3ca375cb34d34c727742c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.31.2.tgz", "fileCount": 6, "integrity": "sha512-81zcAkCCgAc1gA7UvLOWCvkIwrgzaqHBdv9sskOt2xh1+l+RMX9G7sVYj3AOsib3UDR0MCSXit49xKILTMnikw==", "signatures": [{"sig": "MEQCIDdEpnHEA/XyFoCjVnapg2XddOwSdeNuUEccy78xFaOQAiAkUGD6Qn+jRM2wGW7XzieGjU4nnf7NRV78XR1Q8V+XLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.31.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9336d7d4720e11cd9f4348e38d5eb1e9/vitest-spy-0.31.2.tgz", "_integrity": "sha512-81zcAkCCgAc1gA7UvLOWCvkIwrgzaqHBdv9sskOt2xh1+l+RMX9G7sVYj3AOsib3UDR0MCSXit49xKILTMnikw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.31.2_1685451865132_0.7436328422331357", "host": "s3://npm-registry-packages"}}, "0.31.3": {"name": "@vitest/spy", "version": "0.31.3", "license": "MIT", "_id": "@vitest/spy@0.31.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "399bf7280db6ec864b96041535e24daff740c5c0", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.31.3.tgz", "fileCount": 6, "integrity": "sha512-tXGIYPdqjBXLhOWSUhWV/eBWBllfD8f6ppQXR0YMBt95bZrCNV7pM29pkam2M/3GAlg7hS+I8wnKZVkIxgXz9A==", "signatures": [{"sig": "MEUCIDAfUwSe8/3hk5G6ErbYTebnH4UQ2xWmOa/tm15ZE/1OAiEAu/tViUiiqC1sVnehJt871HQSSpgssyCdAYtPmfrYUpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.31.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b3b38e7e0079669de2a95e44573855be/vitest-spy-0.31.3.tgz", "_integrity": "sha512-tXGIYPdqjBXLhOWSUhWV/eBWBllfD8f6ppQXR0YMBt95bZrCNV7pM29pkam2M/3GAlg7hS+I8wnKZVkIxgXz9A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.31.3_1685544526662_0.7911532803935144", "host": "s3://npm-registry-packages"}}, "0.31.4": {"name": "@vitest/spy", "version": "0.31.4", "license": "MIT", "_id": "@vitest/spy@0.31.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fce8e348cea32deff79996d116c67893b19cc47d", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.31.4.tgz", "fileCount": 6, "integrity": "sha512-3ei5ZH1s3aqbEyftPAzSuunGICRuhE+IXOmpURFdkm5ybUADk+viyQfejNk6q8M5QGX8/EVKw+QWMEP3DTJDag==", "signatures": [{"sig": "MEUCIC08oXY6DCuOMjukQdrtLsML2kUIizhb2vQBmudaVt9oAiEAtGJpQbZy7AY4fWl4R216xnOO5QoBwrvhsqamxDEDheM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.31.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3f7dda907022bc0f6e83f8231699750b/vitest-spy-0.31.4.tgz", "_integrity": "sha512-3ei5ZH1s3aqbEyftPAzSuunGICRuhE+IXOmpURFdkm5ybUADk+viyQfejNk6q8M5QGX8/EVKw+QWMEP3DTJDag==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.31.4_1685613290954_0.9174162990974557", "host": "s3://npm-registry-packages"}}, "0.32.0": {"name": "@vitest/spy", "version": "0.32.0", "license": "MIT", "_id": "@vitest/spy@0.32.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3ccb4e6dd4cbda7bb837c467d8405e2f8cc107a2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.32.0.tgz", "fileCount": 6, "integrity": "sha512-MruAPlM0uyiq3d53BkwTeShXY0rYEfhNGQzVO5GHBmmX3clsxcWp79mMnkOVcV244sNTeDcHbcPFWIjOI4tZvw==", "signatures": [{"sig": "MEYCIQCYY2OgM7Qc0q1xpu9dd+Jw00W/uEegvse0NqzznmGJ5wIhAIuSqwNPApCOynvoxFFPp0hms+qVNQEtsasfqmN2PMJ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.32.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/2d46a96bc1597f03f478ce469f071d89/vitest-spy-0.32.0.tgz", "_integrity": "sha512-MruAPlM0uyiq3d53BkwTeShXY0rYEfhNGQzVO5GHBmmX3clsxcWp79mMnkOVcV244sNTeDcHbcPFWIjOI4tZvw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.32.0_1686071030612_0.7390971079375181", "host": "s3://npm-registry-packages"}}, "0.32.1": {"name": "@vitest/spy", "version": "0.32.1", "license": "MIT", "_id": "@vitest/spy@0.32.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2f2d138ab50aa8f7dcd36a10399e45a21aa5f7bc", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.32.1.tgz", "fileCount": 6, "integrity": "sha512-ibbPbGOvSP8Wce6hdv7BAIoVMweFDCPTvxitR7d6VtrR8n5SoPuvV93oi0WQRgJJ+ciUUI2UtUqRsQMU7+FIqQ==", "signatures": [{"sig": "MEQCIC0I4F4joFa4qGMDR5rGrDWDr6jDtiraon91+aEv66/bAiApDauXKq8Xw+w4x9bOTyshPEHvV41TI4kvTvxg/oxeNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.32.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b202cf687b945e6658680c939ebb14ce/vitest-spy-0.32.1.tgz", "_integrity": "sha512-ibbPbGOvSP8Wce6hdv7BAIoVMweFDCPTvxitR7d6VtrR8n5SoPuvV93oi0WQRgJJ+ciUUI2UtUqRsQMU7+FIqQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.32.1_1686918109894_0.8093273291449619", "host": "s3://npm-registry-packages"}}, "0.32.2": {"name": "@vitest/spy", "version": "0.32.2", "license": "MIT", "_id": "@vitest/spy@0.32.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f3ef7afe0d34e863b90df7c959fa5af540a6aaf9", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.32.2.tgz", "fileCount": 6, "integrity": "sha512-Q/ZNILJ4ca/VzQbRM8ur3Si5Sardsh1HofatG9wsJY1RfEaw0XKP8IVax2lI1qnrk9YPuG9LA2LkZ0EI/3d4ug==", "signatures": [{"sig": "MEUCIDOnGPGkQUUCqRsDxEolNoVMHSbOzBskT7ud4N2/pQyjAiEAwLy48neB6UUdAFYSNZPC3qU7Se+gM1kbk3948WijTgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.32.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/822668a1c680c36d16e5e59683436649/vitest-spy-0.32.2.tgz", "_integrity": "sha512-Q/ZNILJ4ca/VzQbRM8ur3Si5Sardsh1HofatG9wsJY1RfEaw0XKP8IVax2lI1qnrk9YPuG9LA2LkZ0EI/3d4ug==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.32.2_1686931524914_0.8861583506156825", "host": "s3://npm-registry-packages"}}, "0.32.3": {"name": "@vitest/spy", "version": "0.32.3", "license": "MIT", "_id": "@vitest/spy@0.32.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "361a3350c565b250e451a2b326b0c41438052978", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.32.3.tgz", "fileCount": 6, "integrity": "sha512-PfyHKb2AnHRPiofPxJdsdAhF36PUhmnMfe1Bwczqgmr7LEHxHKtXBlZR2ay1uM6Hc8Uuaet7T0ap2pVL0vls9A==", "signatures": [{"sig": "MEUCIQCuId49vvOI+UF01FoApbdgEYYZqIn9EJm2TQTjStfnrgIgPzMn1MkrOb/GbGeYNQkC+26d6lhHhQBhyuDTV1zCkLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.32.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9c27027c561bcfdcdada9889e493fdb2/vitest-spy-0.32.3.tgz", "_integrity": "sha512-PfyHKb2AnHRPiofPxJdsdAhF36PUhmnMfe1Bwczqgmr7LEHxHKtXBlZR2ay1uM6Hc8Uuaet7T0ap2pVL0vls9A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.32.3_1688373317339_0.9072532219642051", "host": "s3://npm-registry-packages"}}, "0.32.4": {"name": "@vitest/spy", "version": "0.32.4", "license": "MIT", "_id": "@vitest/spy@0.32.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c3212bc60c1430c3b5c39d6a384a75458b8f1e80", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.32.4.tgz", "fileCount": 6, "integrity": "sha512-oA7rCOqVOOpE6rEoXuCOADX7Lla1LIa4hljI2MSccbpec54q+oifhziZIJXxlE/CvI2E+ElhBHzVu0VEvJGQKQ==", "signatures": [{"sig": "MEUCIBcHg9TuOm125/Vyvv8mBcmE7b2sFq4qJlTYaFZIenANAiEAjHecljtU09vG6Y3BTOqC0r7KThmeGmicMFhC8jsP0io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.32.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/5a512f2f1d594053aa46607a3f9d14a1/vitest-spy-0.32.4.tgz", "_integrity": "sha512-oA7rCOqVOOpE6rEoXuCOADX7Lla1LIa4hljI2MSccbpec54q+oifhziZIJXxlE/CvI2E+ElhBHzVu0VEvJGQKQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.32.4_1688382332270_0.5322094128095352", "host": "s3://npm-registry-packages"}}, "0.33.0": {"name": "@vitest/spy", "version": "0.33.0", "license": "MIT", "_id": "@vitest/spy@0.33.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "366074d3cf9cf1ed8aeaa76e50e78c799fb242eb", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.33.0.tgz", "fileCount": 6, "integrity": "sha512-Kv+yZ4hnH1WdiAkPUQTpRxW8kGtH8VRTnus7ZTGovFYM1ZezJpvGtb9nPIjPnptHbsyIAxYZsEpVPYgtpjGnrg==", "signatures": [{"sig": "MEUCID/cREoxapq/KFV/PX/UBBUG/ggZevSBdxk4qJhtKSg0AiEAgWkDDDGX1HdtPOD2YEJfHtPcbvOWEUqtllWXx4CUiBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.33.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/3d33d868271820f4f3ec41c8df226ce6/vitest-spy-0.33.0.tgz", "_integrity": "sha512-Kv+yZ4hnH1WdiAkPUQTpRxW8kGtH8VRTnus7ZTGovFYM1ZezJpvGtb9nPIjPnptHbsyIAxYZsEpVPYgtpjGnrg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "8.19.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.10.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.33.0_1688652646557_0.09722285872774905", "host": "s3://npm-registry-packages"}}, "0.34.0": {"name": "@vitest/spy", "version": "0.34.0", "license": "MIT", "_id": "@vitest/spy@0.34.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a155bb0a3460fc3c8eb64dccd208959a27d60dd6", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.0.tgz", "fileCount": 6, "integrity": "sha512-0SZaWrQvL9ZiF/uJvyWSvsKjfuMvD1M6dE5BbE4Dmt8Vh3k4htwCV8g3ce8YOYmJSxkbh6TNOpippD6NVsxW6w==", "signatures": [{"sig": "MEUCIDC/szYcXbiaNhqaXTrznyps49F4va+3NU4vFBGVubOAAiEA+wck+HCUrSuqOTkdISkhaqLtnwXQIyl2DqnMnZJg46Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ed6c57ed9eab96b8e0e7b411646fdb11/vitest-spy-0.34.0.tgz", "_integrity": "sha512-0SZaWrQvL9ZiF/uJvyWSvsKjfuMvD1M6dE5BbE4Dmt8Vh3k4htwCV8g3ce8YOYmJSxkbh6TNOpippD6NVsxW6w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.0_1690904485374_0.9286740192562775", "host": "s3://npm-registry-packages"}}, "0.34.1": {"name": "@vitest/spy", "version": "0.34.1", "license": "MIT", "_id": "@vitest/spy@0.34.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2f77234a3d554c5dea664943f2caaab92d304f3c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.1.tgz", "fileCount": 6, "integrity": "sha512-UT4WcI3EAPUNO8n6y9QoEqynGGEPmmRxC+cLzneFFXpmacivjHZsNbiKD88KUScv5DCHVDgdBsLD7O7s1enFcQ==", "signatures": [{"sig": "MEUCIEshnvVcxEL8nS04xx4lGGFWkRfhIR9T9IubaOKTmmXsAiEAwPYpKyJ2B6QK2++eB4c8sO+n4M9z8Oq+hgXJfyNP71U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/da2945adf200adc8a8cb1f4b33408135/vitest-spy-0.34.1.tgz", "_integrity": "sha512-UT4WcI3EAPUNO8n6y9QoEqynGGEPmmRxC+cLzneFFXpmacivjHZsNbiKD88KUScv5DCHVDgdBsLD7O7s1enFcQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.1_1690908816508_0.5129276800395266", "host": "s3://npm-registry-packages"}}, "0.34.2": {"name": "@vitest/spy", "version": "0.34.2", "license": "MIT", "_id": "@vitest/spy@0.34.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5c483d71e4c2d42f90bef29cdf6e5f5559c52827", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.2.tgz", "fileCount": 6, "integrity": "sha512-yd4L9OhfH6l0Av7iK3sPb3MykhtcRN5c5K5vm1nTbuN7gYn+yvUVVsyvzpHrjqS7EWqn9WsPJb7+0c3iuY60tA==", "signatures": [{"sig": "MEYCIQC4EZeEmCiQxclka0bj7ZTYW9GnSFZbcAqoJ2a8Ye0oHgIhALi66/knqdtaqyl6lUaVhuLpHFlv1cvTsnBU/FTCFYyh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6a4a26cadab8a77c697fbef126c0aa67/vitest-spy-0.34.2.tgz", "_integrity": "sha512-yd4L9OhfH6l0Av7iK3sPb3MykhtcRN5c5K5vm1nTbuN7gYn+yvUVVsyvzpHrjqS7EWqn9WsPJb7+0c3iuY60tA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.2_1692266980523_0.9360406925713056", "host": "s3://npm-registry-packages"}}, "0.34.3": {"name": "@vitest/spy", "version": "0.34.3", "license": "MIT", "_id": "@vitest/spy@0.34.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d4cf25e6ca9230991a0223ecd4ec2df30f0784ff", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.3.tgz", "fileCount": 6, "integrity": "sha512-N1V0RFQ6AI7CPgzBq9kzjRdPIgThC340DGjdKdPSE8r86aUSmeliTUgkTqLSgtEwWWsGfBQ+UetZWhK0BgJmkQ==", "signatures": [{"sig": "MEUCIFxnMnRV/Knb2T+WqRVXk83OvsLURTcB45f7RSisq9oYAiEAisjIj8y0by4rFziahDvXhfjnYZD21eO55ggapr3rKEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4c2fd7560ec564268dbd8ecde2a29224/vitest-spy-0.34.3.tgz", "_integrity": "sha512-N1V0RFQ6AI7CPgzBq9kzjRdPIgThC340DGjdKdPSE8r86aUSmeliTUgkTqLSgtEwWWsGfBQ+UetZWhK0BgJmkQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.3_1692948585406_0.7820089501241079", "host": "s3://npm-registry-packages"}}, "0.34.4": {"name": "@vitest/spy", "version": "0.34.4", "license": "MIT", "_id": "@vitest/spy@0.34.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "181ad9f32ce426ac33eb66ed35b880ee9b457035", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.4.tgz", "fileCount": 6, "integrity": "sha512-PNU+fd7DUPgA3Ya924b1qKuQkonAW6hL7YUjkON3wmBwSTIlhOSpy04SJ0NrRsEbrXgMMj6Morh04BMf8k+w0g==", "signatures": [{"sig": "MEUCIH4GxUkMuz49soZQ3CXPFcqgZ3tOqvK/YicgS/JYTYjBAiEAzaECiADVNBTelbnDDln8Syric5TUxaP8jkx9+Pz/qFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/4aee57d86f7e7018a84c668b988cd065/vitest-spy-0.34.4.tgz", "_integrity": "sha512-PNU+fd7DUPgA3Ya924b1qKuQkonAW6hL7YUjkON3wmBwSTIlhOSpy04SJ0NrRsEbrXgMMj6Morh04BMf8k+w0g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.4_1694169247371_0.6330479396035147", "host": "s3://npm-registry-packages"}}, "0.34.5": {"name": "@vitest/spy", "version": "0.34.5", "license": "MIT", "_id": "@vitest/spy@0.34.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2d32993b18eeb50f682e5dde089e390cbb387cb8", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.5.tgz", "fileCount": 6, "integrity": "sha512-epsicsfhvBjRjCMOC/3k00mP/TBGQy8/P0DxOFiWyLt55gnZ99dqCfCiAsKO17BWVjn4eZRIjKvcqNmSz8gvmg==", "signatures": [{"sig": "MEQCIAVo+RdmQbH4Aw2vbpXXPeTd4KW9t3faxL6n/jai326+AiB8E8eNbn6e9J2bDBbws8wHboJ6x/HcldVzh8IdLLYGSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/241a55958fd54049118ee0e83fe51ce4/vitest-spy-0.34.5.tgz", "_integrity": "sha512-epsicsfhvBjRjCMOC/3k00mP/TBGQy8/P0DxOFiWyLt55gnZ99dqCfCiAsKO17BWVjn4eZRIjKvcqNmSz8gvmg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.5_1695304219548_0.037922757060685264", "host": "s3://npm-registry-packages"}}, "0.34.6": {"name": "@vitest/spy", "version": "0.34.6", "license": "MIT", "_id": "@vitest/spy@0.34.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b5e8642a84aad12896c915bce9b3cc8cdaf821df", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.6.tgz", "fileCount": 6, "integrity": "sha512-xaCvneSaeBw/cz8ySmF7ZwGvL0lBjfvqc1LpQ/vcdHEvpLn3Ff1vAvjw+CoGn0802l++5L/pxb7whwcWAw+DUQ==", "signatures": [{"sig": "MEQCIE0z6M0dfvLyy+NFyPzhkUwq7PF3Hfc+Ey19Y2rGl0ZhAiBU741po0/26XRT5+GIrTpejSL42riFhbOlNf3yUsJXng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/77a854fdc6127c0a9f8befc807cc3032/vitest-spy-0.34.6.tgz", "_integrity": "sha512-xaCvneSaeBw/cz8ySmF7ZwGvL0lBjfvqc1LpQ/vcdHEvpLn3Ff1vAvjw+CoGn0802l++5L/pxb7whwcWAw+DUQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.6_1695972784179_0.9500809317447159", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "@vitest/spy", "version": "1.0.0-beta.0", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8d65676d57c13bbae1c5d79611a2d370567320f1", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-TpfQDtetSvFkzrJYMoNG55ip3Q/UfTxlJQ0Ue47Pom0of++6SJJw2WgXbIIPg6vM6yY0pH89+vmhUWTTVJnJrQ==", "signatures": [{"sig": "MEUCIQCoWeHG2ERAJPMq57qnvEgnvJoyBbxI/VslCZzSd5AsfwIgA/l9lbYMSWu34gfq+MT2cBJNdKSVIKpEK6Rae4CGkX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15389}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/ed3e6d7b9acab1d547a484ea0a8a0b3c/vitest-spy-1.0.0-beta.0.tgz", "_integrity": "sha512-TpfQDtetSvFkzrJYMoNG55ip3Q/UfTxlJQ0Ue47Pom0of++6SJJw2WgXbIIPg6vM6yY0pH89+vmhUWTTVJnJrQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.0_1696264812075_0.40012205953982183", "host": "s3://npm-registry-packages"}}, "0.34.7": {"name": "@vitest/spy", "version": "0.34.7", "license": "MIT", "_id": "@vitest/spy@0.34.7", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c414ef3f48a7a0d36f1f59718cd2742d9a0219dd", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-0.34.7.tgz", "fileCount": 6, "integrity": "sha512-NMMSzOY2d8L0mcOt4XcliDOS1ISyGlAXuQtERWVOoVHnKwmG+kKhinAiGw3dTtMQWybfa89FG8Ucg9tiC/FhTQ==", "signatures": [{"sig": "MEUCICliM6l2eVLMu4HlD2JkBrV8KY4ZH+zWGhyxm+BEMf7gAiEA4Q0UGkTgjMqJrNIrWlwnYhUgB8urZLVEoY535UykeSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15383}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-0.34.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/84b353538ddf767b119ae749dcd65ec6/vitest-spy-0.34.7.tgz", "_integrity": "sha512-NMMSzOY2d8L0mcOt4XcliDOS1ISyGlAXuQtERWVOoVHnKwmG+kKhinAiGw3dTtMQWybfa89FG8Ucg9tiC/FhTQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.1.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_0.34.7_1696266182079_0.9582924421891537", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "@vitest/spy", "version": "1.0.0-beta.1", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3b8d761ad4a580b90304edd0764be2ad5c9cbb79", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-oRUzAGwIVRxrIy/0HZHPEkftcTDWlgBk1tR5yBywOewUZTtoXACCpaaht4sY/DExcPbrsROhrJB4vw0d2Jittw==", "signatures": [{"sig": "MEYCIQDLVTIiUxcBIyjxT0j8f3nO5wC+EkDah8xfsOI9mC/J8AIhAKvLDx+lSOPf6Qun4KBCDndfxwWreYXEP82PNr6ziJR0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15389}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a7714eed00eda39b6dbbcf37366b5582/vitest-spy-1.0.0-beta.1.tgz", "_integrity": "sha512-oRUzAGwIVRxrIy/0HZHPEkftcTDWlgBk1tR5yBywOewUZTtoXACCpaaht4sY/DExcPbrsROhrJB4vw0d2Jittw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.1_1696332673773_0.27177908157733555", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "@vitest/spy", "version": "1.0.0-beta.2", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f54248d249703f01efc9af99f12f3fe8b7a4bb43", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-mamPkcuSa2RtOfZnYOayLT8UAOd9/ok3TFKzGemir2rUYK0abjKGjMhKx9A6MXGHI7yeyrHbAdK3p9BeP6lS7g==", "signatures": [{"sig": "MEUCIDe92uxBOHGk1E8Lrvc/DFDYeND1zZl2a1xggTZLtgbnAiEA87hYtW5pm/0sj+wXcTlg985HvnzOFyGc8FQ942vuLLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15484}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/56d21d1ad4c8b017f7e8db52da382f56/vitest-spy-1.0.0-beta.2.tgz", "_integrity": "sha512-mamPkcuSa2RtOfZnYOayLT8UAOd9/ok3TFKzGemir2rUYK0abjKGjMhKx9A6MXGHI7yeyrHbAdK3p9BeP6lS7g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.2_1697138805417_0.5632485296302181", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "@vitest/spy", "version": "1.0.0-beta.3", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b0c0e2141acd3dc64908340488a42142d6a5eaa2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-ybwJagwd1f3awJixX9YWdGtZNHXbJozzEekRfYdHmTudPgIX5bxFCcH4cG/gtGY6VbVuvExwk0Es04bFx2YkLA==", "signatures": [{"sig": "MEUCIEz/k1E337uDLuFgdgaIY/RIzWsbtWl1VDtSEFjNSS58AiEAvYiCjZrpWCQx9/nNrXEDu6rBNExpIjQP1HKwl/lJDZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15484}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/45f9a6850198ea9818042a57dc7851d3/vitest-spy-1.0.0-beta.3.tgz", "_integrity": "sha512-ybwJagwd1f3awJixX9YWdGtZNHXbJozzEekRfYdHmTudPgIX5bxFCcH4cG/gtGY6VbVuvExwk0Es04bFx2YkLA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.3_1698410721385_0.5542453283700619", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "@vitest/spy", "version": "1.0.0-beta.4", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "686b43e56ae790555695f3cde54daf9542066d2b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.4.tgz", "fileCount": 6, "integrity": "sha512-YvKUUl7KucKzLJb8+RTd8H3G24EVPGk+CVMFawwtD/KuYjBzM8RCh3oJTTba6ktLpB8JLVy8NVTNL4Oeigqs8A==", "signatures": [{"sig": "MEUCIG/pQNnQ6zQdG/FHBWjjNsUgCtp0D2nw4m2pt/IsN7UDAiEAnIn6/EQdMIlAgD2d8Y5Pdm3Av1Wnl9NPoD+IJdzQFe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21915}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d99fdcf1fcdbb6314b245240487a99fc/vitest-spy-1.0.0-beta.4.tgz", "_integrity": "sha512-YvKUUl7KucKzLJb8+RTd8H3G24EVPGk+CVMFawwtD/KuYjBzM8RCh3oJTTba6ktLpB8JLVy8NVTNL4Oeigqs8A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.4_1699524796940_0.7432791766524371", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "@vitest/spy", "version": "1.0.0-beta.5", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.5", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d2c06b196e685ccd7f5484a6cf1e4f488c057f08", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-B5dx87eCiJidWGdURMS/etHE9P3JRdFEQj8HQRGI3OhMy5XcSrdAwg5oEADoqXm32GUGc7bC8Dw/q9PiCJSBIQ==", "signatures": [{"sig": "MEQCIFjtUu97ZNOmiK/hPYaQJndH6Ckddo3LzAYSWnhW2RyzAiB8om8JrMd3SIbZw44mcyJTlPwIgoO0BSB+IZgtNffpIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18085}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/bd592ce75b2c6cfe53410d1862a75b97/vitest-spy-1.0.0-beta.5.tgz", "_integrity": "sha512-B5dx87eCiJidWGdURMS/etHE9P3JRdFEQj8HQRGI3OhMy5XcSrdAwg5oEADoqXm32GUGc7bC8Dw/q9PiCJSBIQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.5_1700300655059_0.41114053183688504", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "@vitest/spy", "version": "1.0.0-beta.6", "license": "MIT", "_id": "@vitest/spy@1.0.0-beta.6", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b4c6f4872c8d7a7ebb0ebe6f028cbee1d890ec5b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-AaR9a/ecsJ4lMeC5ZdsTfXqBVxA7ZrmT3q/ooUGXYvAtSxETn39gWC6h7wxUCtKwTLClq+5FmA03Co5/zmGMBw==", "signatures": [{"sig": "MEQCIDRqXXky3HBTEdOu8y4ftw4ZPlG71RUMxhIv43m7nlY/AiAxI1c3uUK+JJ6u2ElWQvE2t4R7oUIJAnZGxomLqOQ3ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18070}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/6305d957d7cdf8711da8165cc0ad4f52/vitest-spy-1.0.0-beta.6.tgz", "_integrity": "sha512-AaR9a/ecsJ4lMeC5ZdsTfXqBVxA7ZrmT3q/ooUGXYvAtSxETn39gWC6h7wxUCtKwTLClq+5FmA03Co5/zmGMBw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0-beta.6_1701192416288_0.603784543983994", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@vitest/spy", "version": "1.0.0", "license": "MIT", "_id": "@vitest/spy@1.0.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cd08b8c6247bfec0d377a83036f9832cc5c26cf2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-k2gZwSi7nkwcYMj1RNgb45jNUDV/opAGlsVvcmYrRXu2QljMSlyAa0Yut+n3S39XoEKp0I4ggVLABj0xVInynw==", "signatures": [{"sig": "MEUCIQDqmAwL/p1cDncKPvNbD86OviEXXEAOQ76ZfMyu9cV0LwIgU4VG0DBGlLB5zr86KRdw9QojLf4QhXoslzovsX/52AE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18063}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/130e49d380f07ad57bab8c0e5ef8eda3/vitest-spy-1.0.0.tgz", "_integrity": "sha512-k2gZwSi7nkwcYMj1RNgb45jNUDV/opAGlsVvcmYrRXu2QljMSlyAa0Yut+n3S39XoEKp0I4ggVLABj0xVInynw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.0_1701704742081_0.5607927022542951", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vitest/spy", "version": "1.0.1", "license": "MIT", "_id": "@vitest/spy@1.0.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d82af1c4d935e08443bf20432ba55afd001ac71f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-yXwm1uKhBVr/5MhVeSmtNqK+0q2RXIchJt8kokEKdrWLtkPeDgdbZ6SjR1VQGZuNdWL6sSBnLayIyVvcS0qLfA==", "signatures": [{"sig": "MEYCIQCcVCwaBfWEE2+wHmym+QVxDf/oBn8qLd5l4HEE7/VKGwIhANu386bpIOOkOtzXSqhPQ9fkyq8iUb67nM7fLLxpUzjQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18063}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/1982c54226ff792d63a9b23a85a13d66/vitest-spy-1.0.1.tgz", "_integrity": "sha512-yXwm1uKhBVr/5MhVeSmtNqK+0q2RXIchJt8kokEKdrWLtkPeDgdbZ6SjR1VQGZuNdWL6sSBnLayIyVvcS0qLfA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.1_1701713068326_0.2891781016318373", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@vitest/spy", "version": "1.0.2", "license": "MIT", "_id": "@vitest/spy@1.0.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c28205427e77e589e3f0e6017f55d1c5b9defee3", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-YlnHmDntp+zNV3QoTVFI5EVHV0AXpiThd7+xnDEbWnD6fw0TH/J4/+3GFPClLimR39h6nA5m0W4Bjm5Edg4A/A==", "signatures": [{"sig": "MEUCIQCBaAuCpONuI/5edYuZONOnG5zsmry3ull4BD/JjZJFCgIgQ92Vwipj1FZK35pk1mMWiRpr/AiNTosDmSuYwD/3ZY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18063}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/f5071257831d9dd5a42b38b6cf400444/vitest-spy-1.0.2.tgz", "_integrity": "sha512-YlnHmDntp+zNV3QoTVFI5EVHV0AXpiThd7+xnDEbWnD6fw0TH/J4/+3GFPClLimR39h6nA5m0W4Bjm5Edg4A/A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.2_1701943958893_0.014994934417262629", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@vitest/spy", "version": "1.0.3", "license": "MIT", "_id": "@vitest/spy@1.0.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a5fa98bc31f2e07589892b1c94fe2aa813af5499", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.3.tgz", "fileCount": 5, "integrity": "sha512-aMd7kvqJuZ/h27Q5XqNOh9fRX7cQJ9fcaPX8q/lk5h2MkAqvq/HuqZ7n1xjm2SDOlDqg3xMaEqP/4inNlNG62A==", "signatures": [{"sig": "MEQCIDoDnxc88dIPkDaJACfEgCZlB7iNmQXlxs/poE3nSSVOAiAEYp4KPf/5f8TNEQ4r43UA69bswpijWCTFU+B152br9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18061}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/d0e53fe427d47a524eee82500720cc54/vitest-spy-1.0.3.tgz", "_integrity": "sha512-aMd7kvqJuZ/h27Q5XqNOh9fRX7cQJ9fcaPX8q/lk5h2MkAqvq/HuqZ7n1xjm2SDOlDqg3xMaEqP/4inNlNG62A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.3_1702127130687_0.15070234325985488", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@vitest/spy", "version": "1.0.4", "license": "MIT", "_id": "@vitest/spy@1.0.4", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e182c78fb9b1178ff789ad7eb4560ba6750e6e9b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.0.4.tgz", "fileCount": 5, "integrity": "sha512-9ojTFRL1AJVh0hvfzAQpm0QS6xIS+1HFIw94kl/1ucTfGCaj1LV/iuJU4Y6cdR03EzPDygxTHwE1JOm+5RCcvA==", "signatures": [{"sig": "MEYCIQC8QjTPI7tDIL/+FXSRjUF0AEWgYKl1wto0sDcgwmIHaAIhALxxXW1j2a+xQdvHkWqaLoyo0EneMdCvQvQvtUhDQCfR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18061}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/c94e13d1a1b70cf6d61be0c97f410898/vitest-spy-1.0.4.tgz", "_integrity": "sha512-9ojTFRL1AJVh0hvfzAQpm0QS6xIS+1HFIw94kl/1ucTfGCaj1LV/iuJU4Y6cdR03EzPDygxTHwE1JOm+5RCcvA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.0.4_1702148700713_0.5066796328515011", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@vitest/spy", "version": "1.1.0", "license": "MIT", "_id": "@vitest/spy@1.1.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7f40697e4fc217ac8c3cc89a865d1751b263f561", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-sNOVSU/GE+7+P76qYo+VXdXhXffzWZcYIPQfmkiRxaNCSPiLANvQx5Mx6ZURJ/ndtEkUJEpvKLXqAYTKEY+lTg==", "signatures": [{"sig": "MEQCIEk+CJOnkhNgX4xOKaoSxjeKHHu3Z5bs+qfEsuwFewPbAiB0DQGOkA4dJIIrE/eg7JOw2OzaITRM34EQlIKN6nw14g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18061}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/346b781ba8370871e14898cd965f87e5/vitest-spy-1.1.0.tgz", "_integrity": "sha512-sNOVSU/GE+7+P76qYo+VXdXhXffzWZcYIPQfmkiRxaNCSPiLANvQx5Mx6ZURJ/ndtEkUJEpvKLXqAYTKEY+lTg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.1.0_1702994773030_0.3186174638131689", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@vitest/spy", "version": "1.1.1", "license": "MIT", "_id": "@vitest/spy@1.1.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "49a9c3f9b86f07b86333fc14d1667691b9a77a5c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-hDU2KkOTfFp4WFFPWwHFauddwcKuGQ7gF6Un/ZZkCogoAiTMN7/7YKvUDbywPZZ754iCQGjdUmXN3t4k0jm1IQ==", "signatures": [{"sig": "MEQCIFcBqEkAC1+O0oeG1tCixQ+7O1euUNDcsAv3PS1tx0p9AiBvr1ir3W57gnJR1N8Ijs+J2tQCsnskEJzWH8y/n6HHHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18061}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/764b31f2aa00b51dcb7ef1b920e5a1be/vitest-spy-1.1.1.tgz", "_integrity": "sha512-hDU2KkOTfFp4WFFPWwHFauddwcKuGQ7gF6Un/ZZkCogoAiTMN7/7YKvUDbywPZZ754iCQGjdUmXN3t4k0jm1IQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.1.1_1704029861735_0.9069008340497562", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "@vitest/spy", "version": "1.1.2", "license": "MIT", "_id": "@vitest/spy@1.1.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "e012c63d519b83adf409662ab3d61355c8f969f5", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.1.2.tgz", "fileCount": 5, "integrity": "sha512-1Nn70K3oY00lhThDXsVQxjslUvJij1YQDzH/4FMxMLgjYxB5u4Aw4yXeICNSSap04wyV2dtGL3RqdBGwoR3sPA==", "signatures": [{"sig": "MEQCIEtYKhcecRL+3D6eFrNt+v6TSJAGSQwGRh874O/0Ms2UAiA604gsy3ws1gnZ8ndS3AMtnyWqz7XlitHmL+AziZ94nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/b83d8553c388210f841a907fe28fd1e8/vitest-spy-1.1.2.tgz", "_integrity": "sha512-1Nn70K3oY00lhThDXsVQxjslUvJij1YQDzH/4FMxMLgjYxB5u4Aw4yXeICNSSap04wyV2dtGL3RqdBGwoR3sPA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.1.2_1704387504850_0.2446297459717406", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "@vitest/spy", "version": "1.1.3", "license": "MIT", "_id": "@vitest/spy@1.1.3", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "244e4e049cd0a5b126a475af327df8b7ffa6b3b5", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.1.3.tgz", "fileCount": 5, "integrity": "sha512-Ec0qWyGS5LhATFQtldvChPTAHv08yHIOZfiNcjwRQbFPHpkih0md9KAbs7TfeIfL7OFKoe7B/6ukBTqByubXkQ==", "signatures": [{"sig": "MEUCIFYypjzFDxzOLCDSk9XF5qhbkVOTgGnlEBSIhCp2ZHMTAiEA6/u3cgJp+eymqiSPeF/BBYl952wBm4tAPxYjpwvxhJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a519b9ebaccb0d87a6a711ff8873e521/vitest-spy-1.1.3.tgz", "_integrity": "sha512-Ec0qWyGS5LhATFQtldvChPTAHv08yHIOZfiNcjwRQbFPHpkih0md9KAbs7TfeIfL7OFKoe7B/6ukBTqByubXkQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "9.6.7", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.17.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.1.3_1704442725485_0.48720904787867636", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@vitest/spy", "version": "1.2.0", "license": "MIT", "_id": "@vitest/spy@1.2.0", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "61104de4c19a3addefff021d884c9e20dc17ebcd", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-MNxSAfxUaCeowqyyGwC293yZgk7cECZU9wGb8N1pYQ0yOn/SIr8t0l9XnGRdQZvNV/ZHBYu6GO/W3tj5K3VN1Q==", "signatures": [{"sig": "MEUCIQCcA/Zq82aWM/FWdRXx9oUbzFdUuVfhz/tJL13Q0MZQHQIgSRwrK9ANffVEIOgEkvGPQUHNhcXxBvKId9rqpvD3tiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/7454eb94894861f059702ecd0381e620/vitest-spy-1.2.0.tgz", "_integrity": "sha512-MNxSAfxUaCeowqyyGwC293yZgk7cECZU9wGb8N1pYQ0yOn/SIr8t0l9XnGRdQZvNV/ZHBYu6GO/W3tj5K3VN1Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.3", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.2.0_1705075613872_0.3419032439954286", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@vitest/spy", "version": "1.2.1", "license": "MIT", "_id": "@vitest/spy@1.2.1", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2777444890de9d32e55e600e34a13b2074cabc18", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-vG3a/b7INKH7L49Lbp0IWrG6sw9j4waWAucwnksPB1r1FTJgV7nkBByd9ufzu6VWya/QTvQW4V9FShZbZIB2UQ==", "signatures": [{"sig": "MEUCIQDKoQwT4d5fX6O6ZCn6CEaom6QMwRtonCIyZ+tRDR3S+AIgcuAeJxZc1WSHynjwQQoA9CAYHx0z4KHu9oS6DotrFjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/a83be5410e3f7d5c0171598effc2ac4c/vitest-spy-1.2.1.tgz", "_integrity": "sha512-vG3a/b7INKH7L49Lbp0IWrG6sw9j4waWAucwnksPB1r1FTJgV7nkBByd9ufzu6VWya/QTvQW4V9FShZbZIB2UQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.3", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.2.1_1705508618309_0.3802898900781235", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@vitest/spy", "version": "1.2.2", "license": "MIT", "_id": "@vitest/spy@1.2.2", "maintainers": [{"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8fc2aeccb96cecbbdd192c643729bd5f97a01c86", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.2.2.tgz", "fileCount": 5, "integrity": "sha512-k9Gcahssw8d7X3pSLq3e3XEu/0L78mUkCjivUqCQeXJm9clfXR/Td8+AP+VC1O6fKPIDLcHDTAmBOINVuv6+7g==", "signatures": [{"sig": "MEUCIQCqcllnl99zJbR5XigmU8P9Psi8VjlhRLZQkh20vmAuOAIgGa5JZVXKUCVas9+9LOgIPCWjHBrE5VjAx2hQ9YeJw/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18005}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "oreann<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/vt/_y31xx1x41j89s4svb4fnhfw0000gn/T/9a3557d7aa8f9c046ca5ca8b1efcdc96/vitest-spy-1.2.2.tgz", "_integrity": "sha512-k9Gcahssw8d7X3pSLq3e3XEu/0L78mUkCjivUqCQeXJm9clfXR/Td8+AP+VC1O6fKPIDLcHDTAmBOINVuv6+7g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.3", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.2.2_1706286332106_0.1704504500279127", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@vitest/spy", "version": "1.3.0", "license": "MIT", "_id": "@vitest/spy@1.3.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1faab30e364324e9826887d479e71c03299fe77b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.3.0.tgz", "fileCount": 5, "integrity": "sha512-AkCU0ThZunMvblDpPKgjIi025UxR8V7MZ/g/EwmAGpjIujLVV2X6rGYGmxE2D4FJbAy0/ijdROHMWa2M/6JVMw==", "signatures": [{"sig": "MEUCIBFYbkdXo75TeKaWQAigz+Iq9OvncDGRE0ytA+yoFViEAiEAo/OvMJFXGwm9Nf4MESCDpBGWaf5ms7bi1G6JmIqQVGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18069}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.3.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7c0be67322c8e1da3a49eda6093ce3a9/vitest-spy-1.3.0.tgz", "_integrity": "sha512-AkCU0ThZunMvblDpPKgjIi025UxR8V7MZ/g/EwmAGpjIujLVV2X6rGYGmxE2D4FJbAy0/ijdROHMWa2M/6JVMw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.3", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.3.0_1708104536367_0.509494382967425", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@vitest/spy", "version": "1.3.1", "license": "MIT", "_id": "@vitest/spy@1.3.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "814245d46d011b99edd1c7528f5725c64e85a88b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.3.1.tgz", "fileCount": 5, "integrity": "sha512-xAcW+S099ylC9VLU7eZfdT9myV67Nor9w9zhf0mGCYJSO+zM2839tOeROTdikOi/8Qeusffvxb/MyBSOja1Uig==", "signatures": [{"sig": "MEQCIF9Y6ctNR8sAot6nWGSvX9Gnf3B+bPVidfvJ10h7HJfLAiAdJXZ8SxBwwFJppYuoFUTi9aqrT7BEnWu6jnyAAi5rHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.3.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dccc633d45bab105b1080bb68de1bf7d/vitest-spy-1.3.1.tgz", "_integrity": "sha512-xAcW+S099ylC9VLU7eZfdT9myV67Nor9w9zhf0mGCYJSO+zM2839tOeROTdikOi/8Qeusffvxb/MyBSOja1Uig==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.4", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.3.1_1708436895113_0.1479153523808161", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@vitest/spy", "version": "1.4.0", "license": "MIT", "_id": "@vitest/spy@1.4.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cf953c93ae54885e801cbe6b408a547ae613f26c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.4.0.tgz", "fileCount": 5, "integrity": "sha512-Ywau/Qs1DzM/8Uc+yA77CwSegizMlcgTJuYGAi0jujOteJOUf1ujunHThYo243KG9nAyWT3L9ifPYZ5+As/+6Q==", "signatures": [{"sig": "MEYCIQCFiv+bujSRTfnnhgIJkLKCQhBRR7FyGqOSECfQq3K6EgIhAPmyU8xzP4DXYtUg/gChn4a/iPjVGLHzH9BT6UlBgQSX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.4.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8730ca2a34bc30a71949d10abdb04ea3/vitest-spy-1.4.0.tgz", "_integrity": "sha512-Ywau/Qs1DzM/8Uc+yA77CwSegizMlcgTJuYGAi0jujOteJOUf1ujunHThYo243KG9nAyWT3L9ifPYZ5+As/+6Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.2.4", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.1", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.4.0_1710498636536_0.02477763791223464", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@vitest/spy", "version": "1.5.0", "license": "MIT", "_id": "@vitest/spy@1.5.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1369a1bec47f46f18eccfa45f1e8fbb9b5e15e77", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.5.0.tgz", "fileCount": 5, "integrity": "sha512-vu6vi6ew5N5MMHJjD5PoakMRKYdmIrNJmyfkhRpQt5d9Ewhw9nZ5Aqynbi3N61bvk9UvZ5UysMT6ayIrZ8GA9w==", "signatures": [{"sig": "MEUCIDfIAjjUvoJ4mSUzBY1xJJDVw4FgEbmTIP1VO7nd+Sr7AiEA+n75XlMYru7lFbO3NQL4Qi832j2obK/4oZRo2vazm2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.5.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9d9ae10bdaa1cbd57d462d243547a9ad/vitest-spy-1.5.0.tgz", "_integrity": "sha512-vu6vi6ew5N5MMHJjD5PoakMRKYdmIrNJmyfkhRpQt5d9Ewhw9nZ5Aqynbi3N61bvk9UvZ5UysMT6ayIrZ8GA9w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.1", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.5.0_1712857668303_0.4830063687867534", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@vitest/spy", "version": "1.5.1", "license": "MIT", "_id": "@vitest/spy@1.5.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a0f96a3441afe6e0c5e7c8068d60b2086b0384d3", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.5.1.tgz", "fileCount": 5, "integrity": "sha512-vsqczk6uPJjmPLy6AEtqfbFqgLYcGBe9BTY+XL8L6y8vrGOhyE23CJN9P/hPimKXnScbqiZ/r/UtUSOQ2jIDGg==", "signatures": [{"sig": "MEYCIQDMGpPjSJQygaeVrdI/0gbLjZR2VI4KylfGedVVbhQjiAIhAM2lxyyK+ELG7z4qErxHnHgwB/fQ73UG8ffOzUbF7EjG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.5.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/89c5915014f448bbd093e23b1d9f281f/vitest-spy-1.5.1.tgz", "_integrity": "sha512-vsqczk6uPJjmPLy6AEtqfbFqgLYcGBe9BTY+XL8L6y8vrGOhyE23CJN9P/hPimKXnScbqiZ/r/UtUSOQ2jIDGg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.5.1_1713957736811_0.07693545318665218", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@vitest/spy", "version": "1.5.2", "license": "MIT", "_id": "@vitest/spy@1.5.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6b439a933b64522edbb8da878fa5b5b6361140ef", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.5.2.tgz", "fileCount": 5, "integrity": "sha512-xCcPvI8JpCtgikT9nLpHPL1/81AYqZy1GCy4+MCHBE7xi8jgsYkULpW5hrx5PGLgOQjUpb6fd15lqcriJ40tfQ==", "signatures": [{"sig": "MEYCIQDQsZylvi4Uq4nYgswPqBDm48CGWfEXB2vXbuMGPh9g7gIhAIqcCVzPgm8mjx7JdPmZpvSJYsUZ9vODY35hBwddrj03", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.5.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6329ed08609cae05f7429688d5df989b/vitest-spy-1.5.2.tgz", "_integrity": "sha512-xCcPvI8JpCtgikT9nLpHPL1/81AYqZy1GCy4+MCHBE7xi8jgsYkULpW5hrx5PGLgOQjUpb6fd15lqcriJ40tfQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.5.2_1714036312917_0.8257956101824655", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@vitest/spy", "version": "1.5.3", "license": "MIT", "_id": "@vitest/spy@1.5.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a81dfa87e4af3fe2c5d6f84cc81be31580b05e2c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.5.3.tgz", "fileCount": 5, "integrity": "sha512-Llj7Jgs6lbnL55WoshJUUacdJfjU2honvGcAJBxhra5TPEzTJH8ZuhI3p/JwqqfnTr4PmP7nDmOXP53MS7GJlg==", "signatures": [{"sig": "MEYCIQD2p2pOE1dJmhHJ+I4c7ZY5dizVeIhrGe+/XCMQBd5nhgIhAOzVHYGI+jivcbXjr3n1T+IneIUINi8Zm6YRgB1fgL0Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.5.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/eebc8b28f4a0dbb92f695476c8dfe804/vitest-spy-1.5.3.tgz", "_integrity": "sha512-Llj7Jgs6lbnL55WoshJUUacdJfjU2honvGcAJBxhra5TPEzTJH8ZuhI3p/JwqqfnTr4PmP7nDmOXP53MS7GJlg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.5.3_1714466418740_0.3726087934024318", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@vitest/spy", "version": "1.6.0", "license": "MIT", "_id": "@vitest/spy@1.6.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "362cbd42ccdb03f1613798fde99799649516906d", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.6.0.tgz", "fileCount": 5, "integrity": "sha512-leUTap6B/cqi/bQkXUu6bQV5TZPx7pmMBKBQiI0rJA8c3pB56ZsaTbREnF7CJfmvAS4V2cXIBAh/3rVwrrCYgw==", "signatures": [{"sig": "MEQCIBEeK2E/0Rcom0DHI/csmjdCsjA9NEmy3yXQAO+5c8gJAiAe+ZKs/prRZORYXjZhdbrIg6oX0+beIeVh8MJHxSVSnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.6.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c0f4ef0d46a97687c4f32466a6019bad/vitest-spy-1.6.0.tgz", "_integrity": "sha512-leUTap6B/cqi/bQkXUu6bQV5TZPx7pmMBKBQiI0rJA8c3pB56ZsaTbREnF7CJfmvAS4V2cXIBAh/3rVwrrCYgw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.6.0_1714749724423_0.1450537682345856", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "@vitest/spy", "version": "2.0.0-beta.1", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "440166b345089b4845ecbf14874e191e2f0c9e9e", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-SLu/HTlN/lFgLLgUg5tDkAOLFXdWvREhwT7M1CFpyn4fYnqNgdRIqCVMY3M6d0AEwWezohMGSC84roxC4ni1eQ==", "signatures": [{"sig": "MEUCIBHLZHl2FBcHLxQvTCwr5nFtq6q8PzIgJGWbMoreTySZAiEAgaOCxnb1mDNJazd5Q4XazmoYGPKfqcVnTXABmXChu4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17975}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/97690e2242dd21e0954411505190db28/vitest-spy-2.0.0-beta.1.tgz", "_integrity": "sha512-SLu/HTlN/lFgLLgUg5tDkAOLFXdWvREhwT7M1CFpyn4fYnqNgdRIqCVMY3M6d0AEwWezohMGSC84roxC4ni1eQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.1_1715265138788_0.7181344766704731", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "@vitest/spy", "version": "2.0.0-beta.2", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "44ea2320f7213bdcf44b1051bb0543ba09b0593f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-/pI9J+CP+cXsCXxg8lxbLR1AHgN7nv5MazzBk3xttj+B5qemmdCaVfVU7Dpcm5d4MwU16qDqvdpvDK1FFBkd4Q==", "signatures": [{"sig": "MEYCIQCE+aB6+6K7yg/bdw0RZYa/oZPOQdpPvExCiFdHV3u2ogIhAJ22n1zxsiYqjV2YKlHmrf9+OzWAqBW4iwPdVMvTCLyI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17975}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/32b2909a4b1d876b09c8ee2b1a534550/vitest-spy-2.0.0-beta.2.tgz", "_integrity": "sha512-/pI9J+CP+cXsCXxg8lxbLR1AHgN7nv5MazzBk3xttj+B5qemmdCaVfVU7Dpcm5d4MwU16qDqvdpvDK1FFBkd4Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.2_1715268672772_0.2724323778655935", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "@vitest/spy", "version": "2.0.0-beta.3", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "391d95b3061cf7babf6eeee3253789a48ce74127", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-3qIAJhJ0rgZ5R540G0Xwy/8OYHyydv4A1gx+IA7PA8IQbMTfJenmvvJE6JWDYqn1jit0C17XFCtXxYGrIqKY3w==", "signatures": [{"sig": "MEUCIQC29i1g5QvC8kLra9NepwTn20nbQcJ68j9p5qJeKocIpwIgDN2fVn/QQ82fO5msl/yuMf4/lQR8RixiYwkPHxH0PF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17975}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d02aabc7c6da404e842641cf3b5e2f30/vitest-spy-2.0.0-beta.3.tgz", "_integrity": "sha512-3qIAJhJ0rgZ5R540G0Xwy/8OYHyydv4A1gx+IA7PA8IQbMTfJenmvvJE6JWDYqn1jit0C17XFCtXxYGrIqKY3w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"tinyspy": "^2.2.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.3_1715712272277_0.06169430473500537", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "@vitest/spy", "version": "2.0.0-beta.4", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4f5c51312553d6d631fc143b48d7d0e8e82823fb", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-jDr36lHMYWCPchMx7a6AnDZJLHut/vel6ItodjKQdQ75ZZ1SS4OXIbtNBGH7nprXbxGzFO2lyZtAbmcyN2N+XQ==", "signatures": [{"sig": "MEUCIFNlicEeUFPYQA9F6TeS5dEVFda1arphfd9vQ/sWWbXTAiEAyHV4hqZFlJE4x3YwLLLhUX860rXg9WuEnKwB/Ma5uFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/508363bd7737aa62c31cc32950964aa1/vitest-spy-2.0.0-beta.4.tgz", "_integrity": "sha512-jDr36lHMYWCPchMx7a6AnDZJLHut/vel6ItodjKQdQ75ZZ1SS4OXIbtNBGH7nprXbxGzFO2lyZtAbmcyN2N+XQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.4_1717330547188_0.960485206107363", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "@vitest/spy", "version": "2.0.0-beta.5", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d74b3036b975f14bae66a46f0459a5616b0cd3ee", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-O0S70iwmNOjkqGHGk+EO3jRL4sr8xa+6NKCJtK2adkEK9ozEmzZgde0l4n9eLydCIWLw+fXREfIFtqafac6NJw==", "signatures": [{"sig": "MEQCIDu4NjW0Dg//Sos5RbS2ZUzOzDk8Zd7mCuR5vWRAq0YTAiAmsWw/PLsF5+OoLT2yMgw+KcjqRhshydNFpN0//cY9Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/aefcc2949acf1bb6c24e669cbf105e42/vitest-spy-2.0.0-beta.5.tgz", "_integrity": "sha512-O0S70iwmNOjkqGHGk+EO3jRL4sr8xa+6NKCJtK2adkEK9ozEmzZgde0l4n9eLydCIWLw+fXREfIFtqafac6NJw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.5_1717331260474_0.8906543129320681", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "@vitest/spy", "version": "2.0.0-beta.6", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "555c91575683cc2b28474ca79cc6f31263d7971c", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-GNzIEzt15v/oviXFrQ39z7zPuPWbVztLnsosAZA59KxmpZGLNamCauEY7xfBJdiqiP7auGYawfOvSHFfwvxKJQ==", "signatures": [{"sig": "MEUCIQCqsKd3t7BeKepmi9dt7iShhk0J/EjIiWK4Ebx5g4piiwIgVYLgprAxX/8BGCktStn43ro0wvLKrqolopF1ZlUK8rQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b5bf9a2df16aa74f384ebcc06508f060/vitest-spy-2.0.0-beta.6.tgz", "_integrity": "sha512-GNzIEzt15v/oviXFrQ39z7zPuPWbVztLnsosAZA59KxmpZGLNamCauEY7xfBJdiqiP7auGYawfOvSHFfwvxKJQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.6_1717355835662_0.6098971096452275", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "@vitest/spy", "version": "2.0.0-beta.7", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "01ba4908da0cd544cd0f63dbdc0133e43e446461", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-dTKef50NsBTs1xA/5J9ARF87IWAPm/TEjop+5qG81kDh9IR/Q1tI0iEqA44DyIg+WSM0ypxzZLilxCsGLvONHQ==", "signatures": [{"sig": "MEYCIQCc+qn6zdp4poWz9szsqOgLqii8LpZ9C+aDjp2t29QZ1wIhAPs3zC4hnZdJgSO13WZ2sJdV3n35tfQzheyMM8857jqp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/70b649fd642eecf0d9d5b5b755da44eb/vitest-spy-2.0.0-beta.7.tgz", "_integrity": "sha512-dTKef50NsBTs1xA/5J9ARF87IWAPm/TEjop+5qG81kDh9IR/Q1tI0iEqA44DyIg+WSM0ypxzZLilxCsGLvONHQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.7_1717414532359_0.46650461571112634", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "@vitest/spy", "version": "2.0.0-beta.8", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.8", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "67979a613a49fc1d47503124f0f66d17c7f19a97", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-eYnayQ48XxSrUkoeaprUqXEqJJvBIwAGKXAXeTZpQ7vFDLjG0hTLgTt1slw92kwR1qYvS4seKjpSwa5AsTUFdA==", "signatures": [{"sig": "MEQCIHJ9bZ8/lx65i/jwgXvjrdhmqB+dOtjLTcrShQpwbcwWAiBNSpqdKpdnaRoYTSF/8F2T53NcjSfersXw1jv5xvYpuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ccae135b1ae9138db8c8375b848e7c48/vitest-spy-2.0.0-beta.8.tgz", "_integrity": "sha512-eYnayQ48XxSrUkoeaprUqXEqJJvBIwAGKXAXeTZpQ7vFDLjG0hTLgTt1slw92kwR1qYvS4seKjpSwa5AsTUFdA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.8_1717504757114_0.6049240153165032", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "@vitest/spy", "version": "2.0.0-beta.9", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.9", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "454682c07a4b77e422acd98845bc236353d53acd", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.9.tgz", "fileCount": 5, "integrity": "sha512-zh7pZfMvCmyF13zuf+FWIZQvCE5v+El5M07asLuwk0xPhStZhXc+4GwMYb3sesm3eE4KpAP8xkQxjcrWC45Qdg==", "signatures": [{"sig": "MEYCIQDzWfN7OJvBDeFNogVYBtGVTeKlvW7G/kgGbnGlIotADwIhANJ1yFZbdCSKa7j2OTL7T1flJybg/flLl6vbfvL29ymx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19142}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a9dc2813d13e42243b4571588d435edb/vitest-spy-2.0.0-beta.9.tgz", "_integrity": "sha512-zh7pZfMvCmyF13zuf+FWIZQvCE5v+El5M07asLuwk0xPhStZhXc+4GwMYb3sesm3eE4KpAP8xkQxjcrWC45Qdg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.5.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.9_1717574446158_0.20283933002616505", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "@vitest/spy", "version": "2.0.0-beta.10", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.10", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "de29ded2f48929cfde6ecc5eaa384390a115e189", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.10.tgz", "fileCount": 5, "integrity": "sha512-vqkBBxQr5F+ufquHPMMpwfe/aPDWgHx7f6WyL7w6tIswZuCa9QSdwARldr8i2lEpCQC7htWA3UnmxQz5faYLxw==", "signatures": [{"sig": "MEYCIQDPPF12sAeM0eU3zWWEi5NMMu0GAAGic9D+aQm8hviCjgIhAPh59CzqVPMWTWjrxuXXfKpW4EdZq0eLlW1zA88LOnpv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19143}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d7c9fb40044491f911e25db4cc156ebc/vitest-spy-2.0.0-beta.10.tgz", "_integrity": "sha512-vqkBBxQr5F+ufquHPMMpwfe/aPDWgHx7f6WyL7w6tIswZuCa9QSdwARldr8i2lEpCQC7htWA3UnmxQz5faYLxw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.10_1718194289119_0.8282083662063009", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "@vitest/spy", "version": "2.0.0-beta.11", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.11", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8084c41ca09ad1223b31a202d2d39901c087fbf7", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.11.tgz", "fileCount": 5, "integrity": "sha512-2i5DKrHgFvMPb0+2jfOFwrrB5/64Hk2py2BCR/ixHgxgN6u0d75262PY/gD6sdwuSgDzApxgi4lfq8jnVX/h9g==", "signatures": [{"sig": "MEUCIDWEaNJWc7yXP5YrVKy24i6ehGcPgqOJ4z5PNyN/amFzAiEAm8UEA/+0feli7fK8C4xEHtVlIcze1yVoFfn8GcuWTnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19149}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.11.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b96b161261e6b3adaa5794ab199ca54c/vitest-spy-2.0.0-beta.11.tgz", "_integrity": "sha512-2i5DKrHgFvMPb0+2jfOFwrrB5/64Hk2py2BCR/ixHgxgN6u0d75262PY/gD6sdwuSgDzApxgi4lfq8jnVX/h9g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.11_1718828030339_0.6243961680154102", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.12": {"name": "@vitest/spy", "version": "2.0.0-beta.12", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.12", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "642367ea635b7ed2a17e29610bd14decdd3f7d86", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.12.tgz", "fileCount": 5, "integrity": "sha512-o9Di4HtCMY/81YZr13ozhvkEdF2cI/4VmkOO0rC5s4v1kTcM4PpvkkSut/Cwj5LfeENRQI6JINvDaKNgBPSXhA==", "signatures": [{"sig": "MEUCIHIDSkG8qyYmV4yyHIVeY7d8v/KH/dfVL7MAYOWE80PdAiEA3rH2m8d6whYQLdliLn9z8QygueEoT4w59OO1s67YFtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.12.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/85909f640fad3d2e8c7507aa917a5e86/vitest-spy-2.0.0-beta.12.tgz", "_integrity": "sha512-o9Di4HtCMY/81YZr13ozhvkEdF2cI/4VmkOO0rC5s4v1kTcM4PpvkkSut/Cwj5LfeENRQI6JINvDaKNgBPSXhA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.14.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.12_1719346567593_0.3807338756747256", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.13": {"name": "@vitest/spy", "version": "2.0.0-beta.13", "license": "MIT", "_id": "@vitest/spy@2.0.0-beta.13", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "a8bd7ea40abc6d736cf66a3b39da4c0b8a2841ff", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0-beta.13.tgz", "fileCount": 5, "integrity": "sha512-gQTFVKTspb7zJ10R2EDEdD7f85cu199BdTThTwzlu3rtcj9ifJ/s5gkwFMeXgO8xRwTtW89Hjl9Zb92SqE2wfg==", "signatures": [{"sig": "MEYCIQCjyIxpvkJk//OR8y2KZOrzButs1n0Sn3r0z29JSIBMGAIhANSJwexBjG7zK8jf4S77epfg7FPx43+TAuHTf7/AHKWL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18929}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0-beta.13.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7f1ce810616900d254c9bb397d3e811b/vitest-spy-2.0.0-beta.13.tgz", "_integrity": "sha512-gQTFVKTspb7zJ10R2EDEdD7f85cu199BdTThTwzlu3rtcj9ifJ/s5gkwFMeXgO8xRwTtW89Hjl9Zb92SqE2wfg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0-beta.13_1720101813443_0.7445880569554475", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vitest/spy", "version": "2.0.0", "license": "MIT", "_id": "@vitest/spy@2.0.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d29927862aaf3a1b45ec4cc5222f55c772dbb9f3", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-0g7ho4wBK09wq8iNZFtUcQZcUcbPmbLWFotL0GXel0fvk5yPi4nTEKpIvZ+wA5eRyqPUCIfIUl10AWzLr67cmA==", "signatures": [{"sig": "MEUCIFNpQvJx7965HtykC3huCaoeSBp8h0eW3ffA5oroHoQXAiEAswcYjqYZlcEcq7+kywHvff0bXpHRZaizuuDr/+Nhahg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18930}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ac246c643fb24efa29d1ed59638d4616/vitest-spy-2.0.0.tgz", "_integrity": "sha512-0g7ho4wBK09wq8iNZFtUcQZcUcbPmbLWFotL0GXel0fvk5yPi4nTEKpIvZ+wA5eRyqPUCIfIUl10AWzLr67cmA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.0_1720438755917_0.5780035753572936", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vitest/spy", "version": "2.0.1", "license": "MIT", "_id": "@vitest/spy@2.0.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "5f1d47c19a6518d6a2a3cc56eff9e586364390b5", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-NLkdxbSefAtJN56GtCNcB4GiHFb5i9q1uh4V229lrlTZt2fnwsTyjLuWIli1xwK2fQspJJmHXHyWx0Of3KTXWA==", "signatures": [{"sig": "MEQCIFfewCoCIU5ksNxwFTp0U+js+QOgQOI39JRmHjA2qbFaAiB7iqufbBuYMJWWhBHwRWjCpnsJII5mQMpH+xW8xk7zBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18930}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/06e7b8d44662733b73979b5a73206563/vitest-spy-2.0.1.tgz", "_integrity": "sha512-NLkdxbSefAtJN56GtCNcB4GiHFb5i9q1uh4V229lrlTZt2fnwsTyjLuWIli1xwK2fQspJJmHXHyWx0Of3KTXWA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.1_1720452770947_0.10297541561234813", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "@vitest/spy", "version": "2.0.2", "license": "MIT", "_id": "@vitest/spy@2.0.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "505b70978ae5f9db7a923bf8d62e4bfa6d89725f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.2.tgz", "fileCount": 5, "integrity": "sha512-MgwJ4AZtCgqyp2d7WcQVE8aNG5vQ9zu9qMPYQHjsld/QVsrvg78beNrXdO4HYkP0lDahCO3P4F27aagIag+SGQ==", "signatures": [{"sig": "MEYCIQCaUqxDeuQOGQqFI+uYPM4fIfr6QOdaHsyt1r7slnLDSAIhAPZV9MOqa8XSlkpNncFrG8LQUAqKITIZitKe7THI53/9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18930}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6bfcf0f4616927b640c2eaeaddfbb3cb/vitest-spy-2.0.2.tgz", "_integrity": "sha512-MgwJ4AZtCgqyp2d7WcQVE8aNG5vQ9zu9qMPYQHjsld/QVsrvg78beNrXdO4HYkP0lDahCO3P4F27aagIag+SGQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.2_1720626387779_0.264700206026524", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "@vitest/spy", "version": "2.0.3", "license": "MIT", "_id": "@vitest/spy@2.0.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "62a14f6d7ec4f13caeeecac42d37f903f68c83c1", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-sfqyAw/ypOXlaj4S+w8689qKM1OyPOqnonqOc9T91DsoHbfN5mU7FdifWWv3MtQFf0lEUstEwR9L/q/M390C+A==", "signatures": [{"sig": "MEYCIQCdfoOBvSFqY/6DVbtju4DGsd0Ek5jE218x9+xuYZG0KgIhAIqi8EJ5cftMsXuYB5q8WA/7xPnS/bdWwwoW9DxV2FZS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18942}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d947c168d3dde0f436c300017f1b014b/vitest-spy-2.0.3.tgz", "_integrity": "sha512-sfqyAw/ypOXlaj4S+w8689qKM1OyPOqnonqOc9T91DsoHbfN5mU7FdifWWv3MtQFf0lEUstEwR9L/q/M390C+A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.3_1721037805128_0.5273668967991254", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "@vitest/spy", "version": "2.0.4", "license": "MIT", "_id": "@vitest/spy@2.0.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "19083386a741a158c2f142beffe43be68b1375cf", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.4.tgz", "fileCount": 5, "integrity": "sha512-uTXU56TNoYrTohb+6CseP8IqNwlNdtPwEO0AWl+5j7NelS6x0xZZtP0bDWaLvOfUbaYwhhWp1guzXUxkC7mW7Q==", "signatures": [{"sig": "MEUCIE1PeewmEPYWZmZkBTmREztDG4mccjDBas/3iJCSjFsiAiEA439KXPjWTCuV9XhnFKbd4VXFXf5UwYwxcwVtHQj0ERM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1661b53c6fe8245fba9a08f5c0725848/vitest-spy-2.0.4.tgz", "_integrity": "sha512-uTXU56TNoYrTohb+6CseP8IqNwlNdtPwEO0AWl+5j7NelS6x0xZZtP0bDWaLvOfUbaYwhhWp1guzXUxkC7mW7Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.4_1721639599861_0.3919767295870058", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "@vitest/spy", "version": "2.0.5", "license": "MIT", "_id": "@vitest/spy@2.0.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "590fc07df84a78b8e9dd976ec2090920084a2b9f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-c/jdthAhvJdpfVuaexSrnawxZz6pywlTPe84LUB2m/4t3rl2fTo9NFGBG4oWgaD+FTgDDV8hJ/nibT7IfH3JfA==", "signatures": [{"sig": "MEUCIQCHBsziQQ5rSeOch2a48b2y0etVvWiNIXSBsdzFk3gW1AIgf6mWClrPoLaKxxavZlujztx+ms+WrNyBPd2rCaTfjR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/71eecc588edc3fda042a2d9b714496eb/vitest-spy-2.0.5.tgz", "_integrity": "sha512-c/jdthAhvJdpfVuaexSrnawxZz6pywlTPe84LUB2m/4t3rl2fTo9NFGBG4oWgaD+FTgDDV8hJ/nibT7IfH3JfA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.7.0", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.15.1", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.0.5_1722422391682_0.787241719567561", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.1": {"name": "@vitest/spy", "version": "2.1.0-beta.1", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2fe675d995377bfe9bb492ab73d040d0e6d61bbf", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-P+D0r0CufmKKVusY/9W26TaP4E7D/5R6iz+q3oO+wDwyVSqZZQxqkYhZYWqfzggBTTDSHqnHzZMTUV7Ur137uQ==", "signatures": [{"sig": "MEUCIQCkpWL+J26mXDPGX4zH35act7qX9C2XcuYs0r94MkSSFAIgPFcc8yfJzbCSf9AzHcUZW6zM7yWfgyRLYl4osH6SALM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bdc38e024d7718c2de81a91f2b9e0a88/vitest-spy-2.1.0-beta.1.tgz", "_integrity": "sha512-P+D0r0CufmKKVusY/9W26TaP4E7D/5R6iz+q3oO+wDwyVSqZZQxqkYhZYWqfzggBTTDSHqnHzZMTUV7Ur137uQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.1_1723011679649_0.10949002990919787", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.2": {"name": "@vitest/spy", "version": "2.1.0-beta.2", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "b09fd420c0e18c6fd38d52e2e4ef0e7c9b2515f2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-Lg28OBUEkuHBL8JMQoKnj0eH6JfoEMIwravTe1Zu7p7iqbucm0wwvho2WbTfFjXCmdGd3qOf93JU/gAzZGY+3g==", "signatures": [{"sig": "MEYCIQCiA8JHb5Ach3smL8lbuwzZb8FTXSCVGHtkA3L/vtWd4QIhAMmDufTAZrlSSvG7QmsCuoVfzMZBr62RmJrf6LfvpuUN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b9810fa05e6a8224c26b88e0aeae5982/vitest-spy-2.1.0-beta.2.tgz", "_integrity": "sha512-Lg28OBUEkuHBL8JMQoKnj0eH6JfoEMIwravTe1Zu7p7iqbucm0wwvho2WbTfFjXCmdGd3qOf93JU/gAzZGY+3g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.2_1723017403865_0.6083214137686181", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.3": {"name": "@vitest/spy", "version": "2.1.0-beta.3", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fadb85443581e1dab3e9649563ac6a792a861123", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-+vDCtmxpmt1vKrofUJs96ggXiNVat2HwSj/FejuTZlrj20SkKE+RlCSUdRzq6TqU4eP3wiGpCLhqOXtzYOC95Q==", "signatures": [{"sig": "MEQCIBGWBdtzVDvPGk7HxHyLeLa+zrsXUR76LfIY+jms4ePDAiBjvrtVhu8AsxBnYmfE8S5Ua/LtoiMKCyRfJonGcjjoLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7b157f5b31eeaa8cff74f08ba3e76448/vitest-spy-2.1.0-beta.3.tgz", "_integrity": "sha512-+vDCtmxpmt1vKrofUJs96ggXiNVat2HwSj/FejuTZlrj20SkKE+RlCSUdRzq6TqU4eP3wiGpCLhqOXtzYOC95Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.3_1723018615818_0.9411652359704907", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.4": {"name": "@vitest/spy", "version": "2.1.0-beta.4", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "631d2e4e66ca04e80be78f0db4d5d1422ede719b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-Xvvt242KbvXiO7mEng/oOKc9l8mRiARXYfYjkbuVMxf2+QOETZTZvLTvPMGIr9Vj/h0jyPt+Mk355Hc69VcfXQ==", "signatures": [{"sig": "MEYCIQD+b2d4bbf2EiIPmicRslw0y7F9SUsem7ojpUIP29fKsAIhANNd4OFnWW6rhxQ+Fp2OOXdwHchK1+H3U09vsvi4SC/B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/995b7feb90504bf2ed7710a20735f784/vitest-spy-2.1.0-beta.4.tgz", "_integrity": "sha512-Xvvt242KbvXiO7mEng/oOKc9l8mRiARXYfYjkbuVMxf2+QOETZTZvLTvPMGIr9Vj/h0jyPt+Mk355Hc69VcfXQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.4_1723030965251_0.03691579562246017", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.5": {"name": "@vitest/spy", "version": "2.1.0-beta.5", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "1606811a8b6cd19985f2363e428034562331679e", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-hkWDPd/QDz6geLReZ5pn0Vqnrd4iMoKYzjUcKN/e88iH+1hjq+a/S6A1LjnUsro8s9nwXBDwuffKi9ppfPZtQg==", "signatures": [{"sig": "MEUCIQDw95sOCLBNCBpacTrYBEdcxlTxtra228Hj0gwx//Ii4gIgKBF7FjkrKuZ1GxVzp5k4Nc/BcAVB8VV/lxuA/Vf54NY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/3e4550923b63e77e1afec3cfa403e051/vitest-spy-2.1.0-beta.5.tgz", "_integrity": "sha512-hkWDPd/QDz6geLReZ5pn0Vqnrd4iMoKYzjUcKN/e88iH+1hjq+a/S6A1LjnUsro8s9nwXBDwuffKi9ppfPZtQg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.5_1723462512781_0.9317969538809703", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.6": {"name": "@vitest/spy", "version": "2.1.0-beta.6", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.6", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "83d9de3d2383b56b7d3b612939bdef765bddbb28", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-9oQdGFcznIT9URFqeU4cwHM3Bjj4Sd0haKS+Lj8WPU0RRqP6Cw79W7foV32dlO2MWgYxj/rUxUtkNV5Cu/cwAw==", "signatures": [{"sig": "MEYCIQCLjI+nLQboSpL3LCZeAbEAlN8d9n3epa3yBgkFzT0ntwIhALwkg/dI6kGlLpadHFzA+t4Wjxv46XDvAQiWd0lLZyuJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/44e29dc50fa10334be4dfc1b36371815/vitest-spy-2.1.0-beta.6.tgz", "_integrity": "sha512-9oQdGFcznIT9URFqeU4cwHM3Bjj4Sd0haKS+Lj8WPU0RRqP6Cw79W7foV32dlO2MWgYxj/rUxUtkNV5Cu/cwAw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.1", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.6_1724159903976_0.38219091919793", "host": "s3://npm-registry-packages"}}, "2.1.0-beta.7": {"name": "@vitest/spy", "version": "2.1.0-beta.7", "license": "MIT", "_id": "@vitest/spy@2.1.0-beta.7", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cc7ab9f748766b024b0abc39624f8651041f401a", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-STBi7BSsYQwnaClEaXGUs1Jk+CvJUnjQVKPU6B4qtPcbt8NfdOg5rQRY7R8XMSCfhTHBVdukeQs6+vFMo/hyKw==", "signatures": [{"sig": "MEUCIQD2p8VBuVZe+SCXSuoQMr91ReqFbkbB5VLTZmLrJJavuQIgUfds64YrS95fRAN3n5UrHi44+POumlsE4ey7HPHlJDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19113}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0-beta.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/aaa6b7285894734f6a09d210f1d29a5f/vitest-spy-2.1.0-beta.7.tgz", "_integrity": "sha512-STBi7BSsYQwnaClEaXGUs1Jk+CvJUnjQVKPU6B4qtPcbt8NfdOg5rQRY7R8XMSCfhTHBVdukeQs6+vFMo/hyKw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0-beta.7_1725894786885_0.6183716331568787", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vitest/spy", "version": "2.1.0", "license": "MIT", "_id": "@vitest/spy@2.1.0", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9a72d6a463db1148c4d76bdbd0ed8438ec17dd87", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-IXX5NkbdgTYTog3F14i2LgnBc+20YmkXMx0IWai84mcxySUDRgm0ihbOfR4L0EVRBDFG85GjmQQEZNNKVVpkZw==", "signatures": [{"sig": "MEYCIQDJW6F/E/Sa1HNoF0Uo5o85pB1wEMYiPw5bT5A8VdwrYAIhALITtofpTvZJS44vr13Ho9ZKrKVBVgcnKvu60TjNG/Kj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1d3e6a8500b88ecb65e85914cd4cad9b/vitest-spy-2.1.0.tgz", "_integrity": "sha512-IXX5NkbdgTYTog3F14i2LgnBc+20YmkXMx0IWai84mcxySUDRgm0ihbOfR4L0EVRBDFG85GjmQQEZNNKVVpkZw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.0_1726149794563_0.8517728757991851", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vitest/spy", "version": "2.1.1", "license": "MIT", "_id": "@vitest/spy@2.1.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "20891f7421a994256ea0d739ed72f05532c78488", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-ZM39BnZ9t/xZ/nF4UwRH5il0Sw93QnZXd9NAZGRpIgj0yvVwPpLd702s/Cx955rGaMlyBQkZJ2Ir7qyY48VZ+g==", "signatures": [{"sig": "MEQCIHVBZS8LQS8Hv+pAW5NkEpbo6CF1KczNfN8z529TqHLKAiBPoseqmRM35Por/n0hF4bDBKjZEqOlh+LevQRHw8FLbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c459a7f788c44412190c48de80bae0c2/vitest-spy-2.1.1.tgz", "_integrity": "sha512-ZM39BnZ9t/xZ/nF4UwRH5il0Sw93QnZXd9NAZGRpIgj0yvVwPpLd702s/Cx955rGaMlyBQkZJ2Ir7qyY48VZ+g==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.1_1726241549098_0.02780686928252507", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vitest/spy", "version": "2.1.2", "license": "MIT", "_id": "@vitest/spy@2.1.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bccdeca597c8fc3777302889e8c98cec9264df44", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.2.tgz", "fileCount": 5, "integrity": "sha512-GSUi5zoy+abNRJwmFhBDC0yRuVUn8WMlQscvnbbXdKLXX9dE59YbfwXxuJ/mth6eeqIzofU8BB5XDo/Ns/qK2A==", "signatures": [{"sig": "MEQCICYumON3QuBtXKZEtJdlIUTlOneIilbDmDGYUBnQNDUcAiA4I3Wac4YkvEHWgSyh5DktVr4Ql/BIFchEg+KDxXatyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e5ff3367a035596c4524a3a425a2bfd1/vitest-spy-2.1.2.tgz", "_integrity": "sha512-GSUi5zoy+abNRJwmFhBDC0yRuVUn8WMlQscvnbbXdKLXX9dE59YbfwXxuJ/mth6eeqIzofU8BB5XDo/Ns/qK2A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.2_1727885995103_0.43916193814395554", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vitest/spy", "version": "2.1.3", "license": "MIT", "_id": "@vitest/spy@2.1.3", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2c8a457673094ec4c1ab7c50cb11c58e3624ada2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.3.tgz", "fileCount": 5, "integrity": "sha512-Nb2UzbcUswzeSP7JksMDaqsI43Sj5+Kry6ry6jQJT4b5gAK+NS9NED6mDb8FlMRCX8m5guaHCDZmqYMMWRy5nQ==", "signatures": [{"sig": "MEMCHyFVgzMTys7GC3hw48oETeWWZYpSNwqjMQOnejF2lJMCIGgMLUAElE61cWODltfEukDcNzXvYorzvTaJrJ/macx8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a2320ee1d5cff8b370fdb17ff701731e/vitest-spy-2.1.3.tgz", "_integrity": "sha512-Nb2UzbcUswzeSP7JksMDaqsI43Sj5+Kry6ry6jQJT4b5gAK+NS9NED6mDb8FlMRCX8m5guaHCDZmqYMMWRy5nQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"tinyspy": "^3.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.3_1728903920522_0.8763778122294152", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vitest/spy", "version": "2.1.4", "license": "MIT", "_id": "@vitest/spy@2.1.4", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4e90f9783437c5841a27c80f8fd84d7289a6100a", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.4.tgz", "fileCount": 5, "integrity": "sha512-4JOxa+UAizJgpZfaCPKK2smq9d8mmjZVPMt2kOsg/R8QkoRzydHH1qHxIYNvr1zlEaFj4SXiaaJWxq/LPLKaLg==", "signatures": [{"sig": "MEQCIB3wbmjEo7V5Q71c8Inq0RUN+m6Z9gH/jSWX22AyEu63AiA5XrXlteH9chr9xO5Oeat1PiraMpSZPI7GzOZVlmDc2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19106}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/590411e515d72a9a86a03d3612a8ce3e/vitest-spy-2.1.4.tgz", "_integrity": "sha512-4JOxa+UAizJgpZfaCPKK2smq9d8mmjZVPMt2kOsg/R8QkoRzydHH1qHxIYNvr1zlEaFj4SXiaaJWxq/LPLKaLg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.4_1730118429174_0.61469767578133", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "@vitest/spy", "version": "2.1.5", "license": "MIT", "_id": "@vitest/spy@2.1.5", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "f790d1394a5030644217ce73562e92465e83147e", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.5.tgz", "fileCount": 5, "integrity": "sha512-aWZF3P0r3w6DiYTVskOYuhBc7EMc3jvn1TkBg8ttylFFRqNN2XGD7V5a4aQdk6QiUzZQ4klNBSpCLJgWNdIiNw==", "signatures": [{"sig": "MEQCIGdflur/a+2Gg4hFyr2gMVAdeNNsiEqwULd7G/h7smv9AiBzWCJSK8SLkP45YqkwgYwtFkJcr/4WXY97Gi3L+/Hokg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5d458abda8d6730f9730736f099aaa59/vitest-spy-2.1.5.tgz", "_integrity": "sha512-aWZF3P0r3w6DiYTVskOYuhBc7EMc3jvn1TkBg8ttylFFRqNN2XGD7V5a4aQdk6QiUzZQ4klNBSpCLJgWNdIiNw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.5_1731511435822_0.23474517360467217", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.1": {"name": "@vitest/spy", "version": "2.2.0-beta.1", "license": "MIT", "_id": "@vitest/spy@2.2.0-beta.1", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0ff32a3506c6446657b6fa0c5086a69817e43161", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.2.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-sbWFBqEyHc3DF5BnZ8SDWfjEeq4NyGr1/5Awc9T+lrr+wxC0P0QQfECdXyVWV/lglpLBGmDFwJ8NSl0MkaT2Cw==", "signatures": [{"sig": "MEUCIGVpjV8k7ALbpAOqSduqBH2ELxNK50ilT7WlMp8ASfm4AiEApJ90YUae4qZKsGOAV58SatGIKO+BFkLrf1IqxIHozUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21747}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/f61dd1b1edbe70ecf28fd96b435d6789/vitest-spy-2.2.0-beta.1.tgz", "_integrity": "sha512-sbWFBqEyHc3DF5BnZ8SDWfjEeq4NyGr1/5Awc9T+lrr+wxC0P0QQfECdXyVWV/lglpLBGmDFwJ8NSl0MkaT2Cw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.2.0-beta.1_1731518228783_0.8399946091851296", "host": "s3://npm-registry-packages"}}, "2.2.0-beta.2": {"name": "@vitest/spy", "version": "2.2.0-beta.2", "license": "MIT", "_id": "@vitest/spy@2.2.0-beta.2", "maintainers": [{"name": "vitestbot", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "fdf0924848f6fa34b07de153fb68c7ef0a8cb27a", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.2.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-AeQPFsEgaxnAzcK37CxA58WFyEMdbDOqXg+vZMJ8oPcL9CHzr3JhyxVaSWOpxnCv9+LXlMLyNSp7EbrUonEDJw==", "signatures": [{"sig": "MEYCIQCs/45P9M8ln3Nt6g5X0bazLh/1XkTZe+O6JrwhTt0HeQIhALMcFE6vMWoGfSrdQZ6+0Xeub/BomyWNQIqDONDBtZj9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21747}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b2221f750e8d57c45ed71857fb293108/vitest-spy-2.2.0-beta.2.tgz", "_integrity": "sha512-AeQPFsEgaxnAzcK37CxA58WFyEMdbDOqXg+vZMJ8oPcL9CHzr3JhyxVaSWOpxnCv9+LXlMLyNSp7EbrUonEDJw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_2.2.0-beta.2_1731939478852_0.3840440443977089", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "@vitest/spy", "version": "2.1.6", "license": "MIT", "_id": "@vitest/spy@2.1.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "229f9d48b90b8bdd6573723bdec0699915598080", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.6.tgz", "fileCount": 5, "integrity": "sha512-oTFObV8bd4SDdRka5O+mSh5w9irgx5IetrD5i+OsUUsk/shsBoHifwCzy45SAORzAhtNiprUVaK3hSCCzZh1jQ==", "signatures": [{"sig": "MEYCIQDgyjVHgVxJVMILEUPgUHCu+MbEX8ZTOQnyVh8xFTbJsAIhAMEmY2pYTMdP9pBpPIu8//A2rOzY7MUHXyE9r0KxlXlE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d0b580cf598ed0582470c501ae58b6d2/vitest-spy-2.1.6.tgz", "_integrity": "sha512-oTFObV8bd4SDdRka5O+mSh5w9irgx5IetrD5i+OsUUsk/shsBoHifwCzy45SAORzAhtNiprUVaK3hSCCzZh1jQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.6_1732623825568_0.9685371454902707", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "@vitest/spy", "version": "2.1.7", "license": "MIT", "_id": "@vitest/spy@2.1.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0d2a8fbdf6e9e75282fa764348e50cd48019414b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.7.tgz", "fileCount": 5, "integrity": "sha512-e5pzIaIC0LBrb/j1FaF7HXlPJLGtltiAkwXTMqNEHALJc7USSLEwziJ+aIWTmjsWNg89zazg37h7oZITnublsQ==", "signatures": [{"sig": "MEQCIFg2A4GYE5NsIpXHxYkzgNS2qOWD6jHQ2dqfkBQnHAfzAiAgLVDz/F0qekgtak7zOIxfjVdazQf0CpF6r14ZxrRUww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e2e0c05ae8e3cea274cfe25de2463c48/vitest-spy-2.1.7.tgz", "_integrity": "sha512-e5pzIaIC0LBrb/j1FaF7HXlPJLGtltiAkwXTMqNEHALJc7USSLEwziJ+aIWTmjsWNg89zazg37h7oZITnublsQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.7_1733132935298_0.8255542238715652", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "@vitest/spy", "version": "2.1.8", "license": "MIT", "_id": "@vitest/spy@2.1.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bc41af3e1e6a41ae3b67e51f09724136b88fa447", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.8.tgz", "fileCount": 5, "integrity": "sha512-5swjf2q95gXeYPevtW0BLk6H8+bPlMb4Vw/9Em4hFxDcaOxS+e0LOX4yqNxoHzMR2akEB2xfpnWUzkZokmgWDg==", "signatures": [{"sig": "MEUCIQDdpmDadiBesdBD0CdrF2hMzbfro8+uILDc0sZlmIe4mgIgbLvXqK+hCtZYgwJeUOtYHQH+LGVlRNuUcg0wQ6/Mdok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e360404521ace2b7b86b4a263945a31a/vitest-spy-2.1.8.tgz", "_integrity": "sha512-5swjf2q95gXeYPevtW0BLk6H8+bPlMb4Vw/9Em4hFxDcaOxS+e0LOX4yqNxoHzMR2akEB2xfpnWUzkZokmgWDg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.8_1733150770517_0.264156722689461", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "@vitest/spy", "version": "3.0.0-beta.1", "license": "MIT", "_id": "@vitest/spy@3.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d516f46ca7b20951c9e924e319da45827cff6146", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-QsOYySOzYMTCpCoQvCspUDz4YfmBahokzNlDYCUaLHVFZTtyeOukAwTv56hyUrOTStKvkuWeg4RIbr/PeNJuNw==", "signatures": [{"sig": "MEYCIQD7ferZNHBGOQ4vsBvWRtU4k2E0Bm92zP6tH0kr6sgK4gIhAMQFlwwRLHciOUIqMezG31As3388rdIXJtCfXbXht7iB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21863}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/dd4dc83bd789c32ea75db6bb88517c29/vitest-spy-3.0.0-beta.1.tgz", "_integrity": "sha512-QsOYySOzYMTCpCoQvCspUDz4YfmBahokzNlDYCUaLHVFZTtyeOukAwTv56hyUrOTStKvkuWeg4RIbr/PeNJuNw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.0-beta.1_1733420010439_0.861332483711537", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "@vitest/spy", "version": "3.0.0-beta.2", "license": "MIT", "_id": "@vitest/spy@3.0.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "4614a8fa023d8d0d82b7605705648f01b32a20e8", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-tSxQfS/wDWRtyx/a3smGuQr/YFaZk1iUsPbKkEvd6jIsrWBb747MSpdn9xfLgIhI68tXquCzruXiMQG0kHdILA==", "signatures": [{"sig": "MEQCIBp7/o/PwsJ3MdP/jNm4U/tuPs9I50vORmq96pAIW5e1AiBmgSnHiHc8lKOpfM5LUCyL9A0N9j/9ICrIt4uiraj0kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22349}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c481b9e5bbf947e6b9ecd012428399e9/vitest-spy-3.0.0-beta.2.tgz", "_integrity": "sha512-tSxQfS/wDWRtyx/a3smGuQr/YFaZk1iUsPbKkEvd6jIsrWBb747MSpdn9xfLgIhI68tXquCzruXiMQG0kHdILA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.0-beta.2_1733826092095_0.31392974014796793", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.3": {"name": "@vitest/spy", "version": "3.0.0-beta.3", "license": "MIT", "_id": "@vitest/spy@3.0.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "bdcfc711c96b61a06f80d99ce8217e26e54906e2", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-IXvZL//Aq5UVIMFtvw1iYnk1iAjk9lGmQUw/fDqN7qMnb9SHfSRbhgxAph11n9nudgBlLiYPSY0nVfCpH/TQsA==", "signatures": [{"sig": "MEQCIEswdzL3M27PblBv8hP1x+GFijG1y51j8rBPRWBnRLXHAiBkLv3b11XCUNStNo54TCweSSO8csNH9B7Zrde4wazpZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22349}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8d6e56dbeccf8a018c5713671267e319/vitest-spy-3.0.0-beta.3.tgz", "_integrity": "sha512-IXvZL//Aq5UVIMFtvw1iYnk1iAjk9lGmQUw/fDqN7qMnb9SHfSRbhgxAph11n9nudgBlLiYPSY0nVfCpH/TQsA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.0-beta.3_1734712356735_0.6114587110604006", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0-beta.4": {"name": "@vitest/spy", "version": "3.0.0-beta.4", "license": "MIT", "_id": "@vitest/spy@3.0.0-beta.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "d082e38316416cd56cba94661d64f8f6310c8e9f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-3Re3ofS3cYq0rCgyiwk51gOzAZyQQ15caJHkuqjcF7dzK7zMGKZpjwQFDaMZq0eAq+AZg+Qo39qrDI8S1dIYSg==", "signatures": [{"sig": "MEQCIHjWKyZziNK0WBuQbM0c2gUpefCHBgNgjfco28L/RafFAiABjB+vGQjdz2uSu3ymjw+OaNVq9mT4p6EX1TsIAUCulw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22168}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.0-beta.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/66581adddc7e19ff1171f81535d7f942/vitest-spy-3.0.0-beta.4.tgz", "_integrity": "sha512-3Re3ofS3cYq0rCgyiwk51gOzAZyQQ15caJHkuqjcF7dzK7zMGKZpjwQFDaMZq0eAq+AZg+Qo39qrDI8S1dIYSg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.0-beta.4_1736346232157_0.7197620533891209", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "@vitest/spy", "version": "3.0.0", "license": "MIT", "_id": "@vitest/spy@3.0.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2612a9e61685080caf7550e00a18145c020cd695", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-pfK5O3lRqeCG8mbV+Lr8lLUBicFRm5TlggF7bLZpzpo111LKhMN/tZRXvyOGOgbktxAR9bTf4x8U6RtHuFBTVA==", "signatures": [{"sig": "MEYCIQCr6LuyRaorbGjZIYdogJYkHqiU0vqWmJgkUdiFt4CquAIhAKRJIh4GyRRkP5ZplBlShjOrGMQyhdBIsVLNQT8Pgl5r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22161}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/77a8fa2181dcc23c8e7f9282297622b6/vitest-spy-3.0.0.tgz", "_integrity": "sha512-pfK5O3lRqeCG8mbV+Lr8lLUBicFRm5TlggF7bLZpzpo111LKhMN/tZRXvyOGOgbktxAR9bTf4x8U6RtHuFBTVA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.0_1737036449988_0.812615872305815", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@vitest/spy", "version": "3.0.1", "license": "MIT", "_id": "@vitest/spy@3.0.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "071931118a12f0e50ce713424c4914743f43d0d0", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-HnGJB3JFflnlka4u7aD0CfqrEtX3FgNaZAar18/KIhfo0r/WADn9PhBfiqAmNw4R/xaRcLzLPFXDwEQV1vHlJA==", "signatures": [{"sig": "MEQCIAy42DvKn1LwMyS4LxuVfv/aClN1DqKob8+5eLmNw7iEAiAbrZBgKj+r0GPjx1VPVX6h1Vk+0Vlbgpn0BWqNurrtyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22164}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/39f5f9dc8cbe518eace7c599b00067dc/vitest-spy-3.0.1.tgz", "_integrity": "sha512-HnGJB3JFflnlka4u7aD0CfqrEtX3FgNaZAar18/KIhfo0r/WADn9PhBfiqAmNw4R/xaRcLzLPFXDwEQV1vHlJA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.1_1737055958994_0.20295671699625228", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "@vitest/spy", "version": "3.0.2", "license": "MIT", "_id": "@vitest/spy@3.0.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "0e306746cc56943db75b69449747483ba466f74a", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-8mI2iUn+PJFMT44e3ISA1R+K6ALVs47W6eriDTfXe6lFqlflID05MB4+rIFhmDSLBj8iBsZkzBYlgSkinxLzSQ==", "signatures": [{"sig": "MEUCIFdOtftVgGztE3FtozuXDaG1/QpwR3FQ1FvNS6l/UaKIAiEAxXUiFeHlZQvn5AGZTwsw1YIfs8PcAXFGXhG6faPm7u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22164}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4829bcf534aa42439ccca475ad4d586f/vitest-spy-3.0.2.tgz", "_integrity": "sha512-8mI2iUn+PJFMT44e3ISA1R+K6ALVs47W6eriDTfXe6lFqlflID05MB4+rIFhmDSLBj8iBsZkzBYlgSkinxLzSQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.2_1737123968977_0.507898862159261", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.3": {"name": "@vitest/spy", "version": "3.0.3", "license": "MIT", "_id": "@vitest/spy@3.0.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ea4e5f7f8b3513e3ac0e556557e4ed339edc82e8", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-7/dgux8ZBbF7lEIKNnEqQlyRaER9nkAL9eTmdKJkDO3hS8p59ATGwKOCUDHcBLKr7h/oi/6hP+7djQk8049T2A==", "signatures": [{"sig": "MEUCIGsSgPNGZyvq30VW8+53mpQLvq4X28QvcV9q0c1tdvF2AiEA7CRg+J+j6hy2HqkSw85Hu8XWHIeH9I1ToOv5PTbkVZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22164}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2317cbe79b9ecf82126d004fc7ea8f55/vitest-spy-3.0.3.tgz", "_integrity": "sha512-7/dgux8ZBbF7lEIKNnEqQlyRaER9nkAL9eTmdKJkDO3hS8p59ATGwKOCUDHcBLKr7h/oi/6hP+7djQk8049T2A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.3_1737467920107_0.29972386999853295", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.4": {"name": "@vitest/spy", "version": "3.0.4", "license": "MIT", "_id": "@vitest/spy@3.0.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "966fd3422ba093568a6a33c437751a91061f8622", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.4.tgz", "fileCount": 5, "integrity": "sha512-sXIMF0oauYyUy2hN49VFTYodzEAu744MmGcPR3ZBsPM20G+1/cSW/n1U+3Yu/zHxX2bIDe1oJASOkml+osTU6Q==", "signatures": [{"sig": "MEUCIQDyxnN7Cmx63oMIo/6VPpLdyTZhuNpFcytYpoQ87PQBiwIgZoNwXfygSAkPbCNgPPYRZPkCnlM1ZdNn2gpS2L+gnf0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22164}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b17dc5a882a951bb5c2f3b89495b2e71/vitest-spy-3.0.4.tgz", "_integrity": "sha512-sXIMF0oauYyUy2hN49VFTYodzEAu744MmGcPR3ZBsPM20G+1/cSW/n1U+3Yu/zHxX2bIDe1oJASOkml+osTU6Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.4_1737639697877_0.793441689226301", "host": "s3://npm-registry-packages-npm-production"}}, "1.6.1": {"name": "@vitest/spy", "version": "1.6.1", "license": "MIT", "_id": "@vitest/spy@1.6.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "33376be38a5ed1ecd829eb986edaecc3e798c95d", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-1.6.1.tgz", "fileCount": 5, "integrity": "sha512-MGcMmpGkZebsMZhbQKkAf9CX5zGvjkBTqf8Zx3ApYWXr3wG+QvEu2eXWfnIIWYSJExIp4V9FCKDEeygzkYrXMw==", "signatures": [{"sig": "MEYCIQDA0fndQ6UNxuvcgs+Kst7jlZ+I/rwbkvyl0QmMsQFxcQIhAIsUxLXnUOAbMXWCVSOsniXTIVWMeTIUnDdtT9Q16HUG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17968}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-1.6.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ed9c9f2ac02f1d0026bfbef6f2b52aeb/vitest-spy-1.6.1.tgz", "_integrity": "sha512-MGcMmpGkZebsMZhbQKkAf9CX5zGvjkBTqf8Zx3ApYWXr3wG+QvEu2eXWfnIIWYSJExIp4V9FCKDEeygzkYrXMw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyspy": "^2.2.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_1.6.1_1738589762809_0.919982130015212", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.9": {"name": "@vitest/spy", "version": "2.1.9", "license": "MIT", "_id": "@vitest/spy@2.1.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cb28538c5039d09818b8bfa8edb4043c94727c60", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-2.1.9.tgz", "fileCount": 5, "integrity": "sha512-E1B35FwzXXTs9FHNK6bDszs7mtydNi5MIfUWpceJ8Xbfb1gBMscAnwLbEu+B44ed6W3XjL9/ehLPHR1fkf1KLQ==", "signatures": [{"sig": "MEYCIQD1Z+jJcGv8n5XRlGbHlClI/X1jFBV68jKowoQjt3R1jQIhAPxFUB5QfYnp0gZlq705MhTAGH8Pwrzp7rvZda6lvrdq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21740}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-2.1.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b386ab2b7094f822878eb04d1a9e259d/vitest-spy-2.1.9.tgz", "_integrity": "sha512-E1B35FwzXXTs9FHNK6bDszs7mtydNi5MIfUWpceJ8Xbfb1gBMscAnwLbEu+B44ed6W3XjL9/ehLPHR1fkf1KLQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_2.1.9_1738590247791_0.6959024644970813", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.5": {"name": "@vitest/spy", "version": "3.0.5", "license": "MIT", "_id": "@vitest/spy@3.0.5", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "7bb5d84ec21cc0d62170fda4e31cd0b46c1aeb8b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.5.tgz", "fileCount": 5, "integrity": "sha512-5fOzHj0WbUNqPK6blI/8VzZdkBlQLnT25knX0r4dbZI9qoZDf3qAdjoMmDcLG5A83W6oUUFJgUd0EYBc2P5xqg==", "signatures": [{"sig": "MEQCIDlhNhBh+wi4uskC/Q16bR/NziPu5kWCX90QIuUsVvp2AiA+OAY4KW7XNcEepiyzUPNgQEoPRKnJTJ1eZUdShm48Vw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22164}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0f848cd43f8c37a3ad49e3cf9a72e532/vitest-spy-3.0.5.tgz", "_integrity": "sha512-5fOzHj0WbUNqPK6blI/8VzZdkBlQLnT25knX0r4dbZI9qoZDf3qAdjoMmDcLG5A83W6oUUFJgUd0EYBc2P5xqg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.5_1738591314753_0.968324781975826", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.6": {"name": "@vitest/spy", "version": "3.0.6", "license": "MIT", "_id": "@vitest/spy@3.0.6", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3a50ec0ab11e8f729cdaa938a6d271b12547cea1", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.6.tgz", "fileCount": 5, "integrity": "sha512-HfOGx/bXtjy24fDlTOpgiAEJbRfFxoX3zIGagCqACkFKKZ/TTOE6gYMKXlqecvxEndKFuNHcHqP081ggZ2yM0Q==", "signatures": [{"sig": "MEQCIFqmg/gLyuc09MKeSh71Np2zxiIzOOmqN6zApQC21pnIAiAicrh8JFmqWDUpdO6ygCg0DdbmpD8jgEigGYc1z8pqJw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22161}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c870dfbfd0ce62af406dbc49963c0e12/vitest-spy-3.0.6.tgz", "_integrity": "sha512-HfOGx/bXtjy24fDlTOpgiAEJbRfFxoX3zIGagCqACkFKKZ/TTOE6gYMKXlqecvxEndKFuNHcHqP081ggZ2yM0Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.6_1739885917970_0.8609943201578285", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.7": {"name": "@vitest/spy", "version": "3.0.7", "license": "MIT", "_id": "@vitest/spy@3.0.7", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "6fcc100c23fb50b5e2d1d09a333245586364f67b", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.7.tgz", "fileCount": 5, "integrity": "sha512-4T4WcsibB0B6hrKdAZTM37ekuyFZt2cGbEGd2+L0P8ov15J1/HUsUaqkXEQPNAWr4BtPPe1gI+FYfMHhEKfR8w==", "signatures": [{"sig": "MEYCIQDGbB5ROz5eoCIVSgzs5lSlwRoyp6ggbFJOic5S9OmoRwIhAJDrVIpGORVk4FuvsSHPVxXR1AKvFCy2b/MXg05eN6UL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21675}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/665f22ea08aa91f87d36a61891e4da63/vitest-spy-3.0.7.tgz", "_integrity": "sha512-4T4WcsibB0B6hrKdAZTM37ekuyFZt2cGbEGd2+L0P8ov15J1/HUsUaqkXEQPNAWr4BtPPe1gI+FYfMHhEKfR8w==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.2", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.7_1740419444064_0.07365759302645913", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "@vitest/spy", "version": "3.0.8", "license": "MIT", "_id": "@vitest/spy@3.0.8", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "2a31ce28858aae50286644d64f886c72d55ae2ce", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.8.tgz", "fileCount": 5, "integrity": "sha512-MR+PzJa+22vFKYb934CejhR4BeRpMSoxkvNoDit68GQxRLSf11aT6CTj3XaqUU9rxgWJFnqicN/wxw6yBRkI1Q==", "signatures": [{"sig": "MEUCIQDP8CpVQ0FeECp3vagOtoaAbgXr02lAXpZzpNbc5Io/7QIgZUF9Jg/HP7S/PtMPN+h7chnggjoMZyp5YcIN4fq4RpI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21675}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cab23cd43d5eb9b4013d24510bdcb058/vitest-spy-3.0.8.tgz", "_integrity": "sha512-MR+PzJa+22vFKYb934CejhR4BeRpMSoxkvNoDit68GQxRLSf11aT6CTj3XaqUU9rxgWJFnqicN/wxw6yBRkI1Q==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.8_1741274171350_0.8382398723556013", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "@vitest/spy", "version": "3.0.9", "license": "MIT", "_id": "@vitest/spy@3.0.9", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c3e5d47ceff7c1cb9fdfb9b2f168056bbc625534", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.0.9.tgz", "fileCount": 5, "integrity": "sha512-/CcK2UDl0aQ2wtkp3YVWldrpLRNCfVcIOFGlVGKO4R5eajsH393Z1yiXLVQ7vWsj26JOEjeZI0x5sm5P4OGUNQ==", "signatures": [{"sig": "MEQCIHCk6AqJjsIF/wFFOzDEgOyt6ZXthZjxzDzvbbCGvJqQAiBfQ2EdYkrLOt1vydd7xQ4jzGGAAO8uAunNjmcK8rlrEQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20586}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.0.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9561844fdb0a557de0a24298308f60a6/vitest-spy-3.0.9.tgz", "_integrity": "sha512-/CcK2UDl0aQ2wtkp3YVWldrpLRNCfVcIOFGlVGKO4R5eajsH393Z1yiXLVQ7vWsj26JOEjeZI0x5sm5P4OGUNQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.0.9_1742212750512_0.36250750179400004", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.1": {"name": "@vitest/spy", "version": "3.1.0-beta.1", "license": "MIT", "_id": "@vitest/spy@3.1.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "8e11f5e85ed403775db9581640390b63444212ce", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-zWo4KSAffWs5u2LAghiiKObEQktAag82S2mulyKL9DyuFz6mSXeo/NgnWv5lDJYvJ9AnaxxciWYgHaUFIlPbFg==", "signatures": [{"sig": "MEUCIQDFEVy5wiA2J/LGrbARMHHTiK6Rlr5kSC8Pq52nZWNpOQIgbI88uPyFZj4+sU+uDYJS9SvO7MyfRFLqOjSes1kLJNo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20593}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/38398b72a2b76084cc06074c3c948418/vitest-spy-3.1.0-beta.1.tgz", "_integrity": "sha512-zWo4KSAffWs5u2LAghiiKObEQktAag82S2mulyKL9DyuFz6mSXeo/NgnWv5lDJYvJ9AnaxxciWYgHaUFIlPbFg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.3", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.0-beta.1_1742213844673_0.5606471540916078", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0-beta.2": {"name": "@vitest/spy", "version": "3.1.0-beta.2", "license": "MIT", "_id": "@vitest/spy@3.1.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c5ae0924f722719b9f2e2d6407404228c524c091", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-aqDAf5mPxXu7CzKOXBX/StpaioR+5VfSmbD49o9d4UpZfmg5iX/eYc1PG0MW5cGybKhVMCxZ5ENwHbpEhe3piA==", "signatures": [{"sig": "MEUCIQD3nM2lErAsikAmAAEo3zzIhseZVlOPJmOBF/z0lN9YhgIgPYIa+28ugMUeUoP63yeR5ey23T2lYlsvrdZuSA0Iwgk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20355}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/74b9f6b3b380279358f0f6ddbee9b853/vitest-spy-3.1.0-beta.2.tgz", "_integrity": "sha512-aqDAf5mPxXu7CzKOXBX/StpaioR+5VfSmbD49o9d4UpZfmg5iX/eYc1PG0MW5cGybKhVMCxZ5ENwHbpEhe3piA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.0-beta.2_1742545690296_0.7031307635236888", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "@vitest/spy", "version": "3.1.0", "license": "MIT", "_id": "@vitest/spy@3.1.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "9052b6415df3ea7b5739edfb4a589c0658bb3761", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-B4mywDSynOT6zKKFKd4QuqQ2UyhwK0OfUbCjMHQZSv1Knxm4LwKLBaQemu84YKl9+Vgbc7heLlAjZZOwsmogtA==", "signatures": [{"sig": "MEUCIQDt2EDf8JZHZNAbhyo2ExTcEHcKe5TxCQCAM4zW3zD5GwIgR2/9/0XSFuodWomB8ck/JzqqK/82S7lK9J+5tZMEfvo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20269}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/530bd535513572acabcc2dbcafc38816/vitest-spy-3.1.0.tgz", "_integrity": "sha512-B4mywDSynOT6zKKFKd4QuqQ2UyhwK0OfUbCjMHQZSv1Knxm4LwKLBaQemu84YKl9+Vgbc7heLlAjZZOwsmogtA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.0_1743414339387_0.4505400229743237", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.1": {"name": "@vitest/spy", "version": "3.1.1", "license": "MIT", "_id": "@vitest/spy@3.1.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "deca0b025e151302ab514f38390fd7777e294837", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-+EmrUOOXbKzLkTDwlsc/xrwOlPDXyVk3Z6P6K4oiCndxz7YLpp/0R0UsWVOKT0IXWjjBJuSMk6D27qipaupcvQ==", "signatures": [{"sig": "MEYCIQDkxdtkqdgiYS8Qt9IGhph9gUuCHd/UsHl+8esahLQBRAIhAM0qfRzFOAmaNnOJ8LVVZ3ZmkGlR4fWgTkvsVh3NX6M6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20269}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d4f8b77feb9d4c855a64ee4d533f8800/vitest-spy-3.1.1.tgz", "_integrity": "sha512-+EmrUOOXbKzLkTDwlsc/xrwOlPDXyVk3Z6P6K4oiCndxz7YLpp/0R0UsWVOKT0IXWjjBJuSMk6D27qipaupcvQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.1_1743416334348_0.038286960282853055", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.2": {"name": "@vitest/spy", "version": "3.1.2", "license": "MIT", "_id": "@vitest/spy@3.1.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "3a5be04d71c4a458c8d6859503626e2aed61bcbf", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-OEc5fSXMws6sHVe4kOFyDSj/+4MSwst0ib4un0DlcYgQvRuYQ0+M2HyqGaauUMnjq87tmUaMNDxKQx7wNfVqPA==", "signatures": [{"sig": "MEUCIFaEY6n13ANsUePzxGD9gEJYqhg6SnczsYl+4TyzS0LvAiEAqkf9icm2H9MtT4JwqCRR/A7C5A4jSFiMyr+V91a8Vws=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20269}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/04ca9f00f0b03c62413170c63cb650b7/vitest-spy-3.1.2.tgz", "_integrity": "sha512-OEc5fSXMws6sHVe4kOFyDSj/+4MSwst0ib4un0DlcYgQvRuYQ0+M2HyqGaauUMnjq87tmUaMNDxKQx7wNfVqPA==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.0", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.2_1745225908474_0.9725391460239374", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.3": {"name": "@vitest/spy", "version": "3.1.3", "license": "MIT", "_id": "@vitest/spy@3.1.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ca81e2b4f0c3d6c75f35defa77c3336f39c8f605", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-x6w+ctOEmEXdWaa6TO4ilb7l9DxPR5bwEb6hILKuxfU1NqWT2mpJD9NJN7t3OTfxmVlOMrvtoFJGdgyzZ605lQ==", "signatures": [{"sig": "MEQCIBSSN3YUm4GPkegLoRk0mseAEYj6TpcX1FBGMwb7LEsWAiAbPryQicg7roh5p6Y37Rg9aUgIiXzBFRqo2w1M9I1V0A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20269}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0d6cc9de09525179dab45ac7f885ee4f/vitest-spy-3.1.3.tgz", "_integrity": "sha512-x6w+ctOEmEXdWaa6TO4ilb7l9DxPR5bwEb6hILKuxfU1NqWT2mpJD9NJN7t3OTfxmVlOMrvtoFJGdgyzZ605lQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.3_1746452696653_0.8555582753837963", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.1": {"name": "@vitest/spy", "version": "3.2.0-beta.1", "license": "MIT", "_id": "@vitest/spy@3.2.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ef1fe5b6594848abb530466b19637072bd79f5d8", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-QVYX4WiQzA0U0eCD7aHLMYXIG4iDNZGjT13HQI1xyClgKRIPru+DfZdMLkPjGOq2JRddJuWJGVlvX48Seob5+A==", "signatures": [{"sig": "MEUCIDlx30db6v+EcxhEBKwwOLZmLLcW85MS6/DZ/OP2orNvAiEA02FbwrNuylT502A0JrFSNpEjTW2pnEsLx4Dih4absrQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20276}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/90c11117b5b1814f1c6f7d91557547a9/vitest-spy-3.2.0-beta.1.tgz", "_integrity": "sha512-QVYX4WiQzA0U0eCD7aHLMYXIG4iDNZGjT13HQI1xyClgKRIPru+DfZdMLkPjGOq2JRddJuWJGVlvX48Seob5+A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.0-beta.1_1746463870710_0.10457092283422131", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.2": {"name": "@vitest/spy", "version": "3.2.0-beta.2", "license": "MIT", "_id": "@vitest/spy@3.2.0-beta.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "140409b766834b6965e561579a0af6999902cedf", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-yMDJPMyiqs8bt/qSZ3qytK81twkBnT0qsSLWNAqhrzUpMwHZ9hNMJsXNDyjhDhTZ9fg/nczpoqYRfYYatfIImg==", "signatures": [{"sig": "MEYCIQCzIatLhaCjcHc1m2mVtw4/GSgl5gJqqQHkr0ZQvkIe4gIhAKhb0Sx1LyoY/dKmKvq6rM3SSAKt7S0Tpv1/o7VLcH3f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20372}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.0-beta.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/057ee521bb2610dc3e81a2c4e070fac3/vitest-spy-3.2.0-beta.2.tgz", "_integrity": "sha512-yMDJPMyiqs8bt/qSZ3qytK81twkBnT0qsSLWNAqhrzUpMwHZ9hNMJsXNDyjhDhTZ9fg/nczpoqYRfYYatfIImg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.0-beta.2_1747658318333_0.054764470299341284", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.4": {"name": "@vitest/spy", "version": "3.1.4", "license": "MIT", "_id": "@vitest/spy@3.1.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "94bb566da7ef6deb7c4e1fd79b78f19aa5465b9f", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.4.tgz", "fileCount": 5, "integrity": "sha512-Xg1bXhu+vtPXIodYN369M86K8shGLouNjoVI78g8iAq2rFoHFdajNvJJ5A/9bPMFcfQqdaCpOgWKEoMQg/s0Yg==", "signatures": [{"sig": "MEYCIQCcoVTBv33LcMjJBNsOeBsxw9ZcbBFpUHK2x+YpV3vYXQIhAJ8z8ep+8HmwlSph0v4MuQVwM5L5yBQ840wrwbTLB+b+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20269}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.1.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6104a0cad01690ac1c15584c99c094a3/vitest-spy-3.1.4.tgz", "_integrity": "sha512-Xg1bXhu+vtPXIodYN369M86K8shGLouNjoVI78g8iAq2rFoHFdajNvJJ5A/9bPMFcfQqdaCpOgWKEoMQg/s0Yg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.1.4_1747671829027_0.6598312057028255", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0-beta.3": {"name": "@vitest/spy", "version": "3.2.0-beta.3", "license": "MIT", "_id": "@vitest/spy@3.2.0-beta.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "aedbaa4f3dbe0b3317e4a61083db817c2c103f60", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-pfAqZ3VFdg2pPe64UfEGUUrv+CGgv1Wvt6GZ6rSFtbMsHiE+eiz7ggFDbUewDnxbmJRA/Kg+MiUF5jkw7s358A==", "signatures": [{"sig": "MEYCIQDx9Nep8C+pyQFxC9EdphUW2FHpFyBGDw9NlGf5qnij0QIhAJMBTMS0DeRCamch6XtXJ75Zqx6EFTpkRLyMZhbPV44j", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20903}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.0-beta.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6bbdf662c6ebffa1315a6e789466aa41/vitest-spy-3.2.0-beta.3.tgz", "_integrity": "sha512-pfAqZ3VFdg2pPe64UfEGUUrv+CGgv1Wvt6GZ6rSFtbMsHiE+eiz7ggFDbUewDnxbmJRA/Kg+MiUF5jkw7s358A==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^3.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.0-beta.3_1748442508662_0.6928370239137316", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "@vitest/spy", "version": "3.2.0", "license": "MIT", "_id": "@vitest/spy@3.2.0", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "553b61838a1aabc33228b1193c1cf80a00c0c315", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-s3+TkCNUIEOX99S0JwNDfsHRaZDDZZR/n8F0mop0PmsEbQGKZikCGpTGZ6JRiHuONKew3Fb5//EPwCP+pUX9cw==", "signatures": [{"sig": "MEQCIDSQGFiiVIWlieswvL9dOjmz3ifte6d716HuK7UchJjJAiBGRnxMG0QRsByrqVaYYB6MKLE3r/PQJ1sRXk4zyulWHw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/5815f9da3c1d73d540b4306ad0a3ff54/vitest-spy-3.2.0.tgz", "_integrity": "sha512-s3+TkCNUIEOX99S0JwNDfsHRaZDDZZR/n8F0mop0PmsEbQGKZikCGpTGZ6JRiHuONKew3Fb5//EPwCP+pUX9cw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.0_1748862640083_0.023080629957306087", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.1": {"name": "@vitest/spy", "version": "3.2.1", "license": "MIT", "_id": "@vitest/spy@3.2.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "ed704923f7d628afa1a0c40c7c7d26a9845b0274", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.1.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON><PERSON>b34Z2rfcJGSetMxjDCznn4pCYPZOtQYox2kzebIJcgH75yheIKd5QYSFmR8DIZf2M8fwOm66qSDIfRFFfQ==", "signatures": [{"sig": "MEUCIC/k6uJamEDiZybq4XBmxzzV7V6kFOF0RJRZRZr5CCwjAiEA4KHfAw14uCtrZgCOHINu8ZrjFYBs8kkwySLKpVXRWtw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/bd3bbc98daff153842ea3e818a653cc2/vitest-spy-3.2.1.tgz", "_integrity": "sha512-<PERSON><PERSON><PERSON>b34Z2rfcJGSetMxjDCznn4pCYPZOtQYox2kzebIJcgH75yheIKd5QYSFmR8DIZf2M8fwOm66qSDIfRFFfQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.1_1748970435987_0.20787976443679468", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.2": {"name": "@vitest/spy", "version": "3.2.2", "license": "MIT", "_id": "@vitest/spy@3.2.2", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "aee82909c0255f9b14d9a27892a194514200538a", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.2.tgz", "fileCount": 5, "integrity": "sha512-6Utxlx3o7pcTxvp0u8kUiXtRFScMrUg28KjB3R2hon7w4YqOFAEA9QwzPVVS1QNL3smo4xRNOpNZClRVfpMcYg==", "signatures": [{"sig": "MEUCIQDhJ6t7iC2rHq5ks3UBBAsju2WeMvGak3x1kN54gkmbagIgKtQBD3oygRWZ+fSi1tkR2VASM9it60dcIU/JTrUhZNI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b8c7af903867adb24b6cc95b1e1aeb57/vitest-spy-3.2.2.tgz", "_integrity": "sha512-6Utxlx3o7pcTxvp0u8kUiXtRFScMrUg28KjB3R2hon7w4YqOFAEA9QwzPVVS1QNL3smo4xRNOpNZClRVfpMcYg==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.2_1749130929832_0.9306977120765216", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.3": {"name": "@vitest/spy", "version": "3.2.3", "license": "MIT", "_id": "@vitest/spy@3.2.3", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "c91715ca4db58a1f0dec636d393a76cf9945b695", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.3.tgz", "fileCount": 5, "integrity": "sha512-JHu9Wl+7bf6FEejTCREy+DmgWe+rQKbK+y32C/k5f4TBIAlijhJbRBIRIOCEpVevgRsCQR2iHRUH2/qKVM/plw==", "signatures": [{"sig": "MEYCIQCEuEN9w3n7crgRrH7uuCjfqc+F1zMBJUFBXAiRAGp8NAIhAMTL1+IGHo14P4HPqKu1vSRQ8ZR9BzZtrvHRrn1tG+Ab", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21979}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1e5d1b792e367b354c8cb794748ee33c/vitest-spy-3.2.3.tgz", "_integrity": "sha512-JHu9Wl+7bf6FEejTCREy+DmgWe+rQKbK+y32C/k5f4TBIAlijhJbRBIRIOCEpVevgRsCQR2iHRUH2/qKVM/plw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.1", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.3_1749468735953_0.6814791239461857", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.4": {"name": "@vitest/spy", "version": "3.2.4", "license": "MIT", "_id": "@vitest/spy@3.2.4", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "cc18f26f40f3f028da6620046881f4e4518c2599", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.4.tgz", "fileCount": 5, "integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "signatures": [{"sig": "MEYCIQDDflPs81ka9rDEr2cLd7JL7AVL/IxD0HCk3D4YC/mGRQIhAPf5UqqyQp+/qMk+7bvUy32lEub5Y0XaViwtk+8wysU+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@3.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22504}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-3.2.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/b4dd7f1aac03df9c409f7772433e4096/vitest-spy-3.2.4.tgz", "_integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/spy_3.2.4_1750182835616_0.32635551342327584", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.1": {"name": "@vitest/spy", "version": "4.0.0-beta.1", "license": "MIT", "_id": "@vitest/spy@4.0.0-beta.1", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "dist": {"shasum": "97b28c7f30cc440d19fb7e03f1125e393926c611", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-4.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-Ooz09Y1YdD5eIBYPzKV8LVJk5cVb5FIcPPKfTVdagv+r1l5abyJWIj9PYWbO5/5oTDHZJUCP5iCb1EinyI3raQ==", "signatures": [{"sig": "MEQCIBmqw9Xn2URaG5liXNF/mQohE4qmM2QpCoyavGkPC23/AiBl3Y0pKZPME197Kqn8+nXc9z/TAbAkeoui2KX7OEwczA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@4.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 22511}, "main": "./dist/index.js", "type": "module", "_from": "file:vitest-spy-4.0.0-beta.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "funding": "https://opencollective.com/vitest", "scripts": {"dev": "rollup -c --watch", "build": "rimraf dist && rollup -c"}, "_npmUser": {"name": "vitestbot", "actor": {"name": "vitestbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/1e9446490650349a62c181dca2c62baf/vitest-spy-4.0.0-beta.1.tgz", "_integrity": "sha512-Ooz09Y1YdD5eIBYPzKV8LVJk5cVb5FIcPPKfTVdagv+r1l5abyJWIj9PYWbO5/5oTDHZJUCP5iCb1EinyI3raQ==", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "_npmVersion": "10.8.2", "description": "Lightweight Jest compatible spy implementation", "directories": {}, "sideEffects": false, "_nodeVersion": "20.19.2", "dependencies": {"tinyspy": "^4.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/spy_4.0.0-beta.1_1750433309733_0.16399163519878424", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0-beta.2": {"name": "@vitest/spy", "type": "module", "version": "4.0.0-beta.2", "description": "Lightweight Jest compatible spy implementation", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/spy"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"tinyspy": "^4.0.3"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}, "_id": "@vitest/spy@4.0.0-beta.2", "readmeFilename": "README.md", "_integrity": "sha512-S8N2U+Z5LRyl9Mfp+0ITACIuAF6CfCpNLKjv9uT9X8TJXf1MJDN1dy5QWHIOU2eNg7B2B2c3Jabbn7Re/PfzLA==", "_resolved": "/tmp/93becae2a6be16892ec2626de15d3f92/vitest-spy-4.0.0-beta.2.tgz", "_from": "file:vitest-spy-4.0.0-beta.2.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-S8N2U+Z5LRyl9Mfp+0ITACIuAF6CfCpNLKjv9uT9X8TJXf1MJDN1dy5QWHIOU2eNg7B2B2c3Jabbn7Re/PfzLA==", "shasum": "ae53e7218025bb233894a56286b83b6120ac589d", "tarball": "https://registry.npmjs.org/@vitest/spy/-/spy-4.0.0-beta.2.tgz", "fileCount": 5, "unpackedSize": 23976, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fspy@4.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCRC9Nr541v3f8V7AMfgATIkch/yaMnilyKbFpNfMjJCQIhAIamtCXMT3aOBJXPW1MSZnWxyZv8Rpnxrz5GwWv3tKYJ"}]}, "_npmUser": {"name": "vitestbot", "email": "<EMAIL>", "actor": {"name": "vitestbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/spy_4.0.0-beta.2_1750775087967_0.3072625927434045"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-01-09T12:33:15.277Z", "modified": "2025-06-24T14:24:48.598Z", "0.27.0": "2023-01-09T12:33:15.575Z", "0.27.1": "2023-01-11T16:32:08.272Z", "0.27.2": "2023-01-17T07:40:23.483Z", "0.27.3": "2023-01-21T16:22:50.973Z", "0.28.0": "2023-01-23T09:27:42.961Z", "0.28.1": "2023-01-23T09:50:49.864Z", "0.28.2": "2023-01-25T11:20:48.618Z", "0.28.3": "2023-01-27T12:09:34.304Z", "0.28.4": "2023-02-03T10:04:34.009Z", "0.28.5": "2023-02-13T12:23:20.339Z", "0.29.0": "2023-02-25T08:25:21.161Z", "0.29.1": "2023-02-25T09:24:34.289Z", "0.29.2": "2023-02-28T15:13:52.617Z", "0.29.3": "2023-03-15T20:03:29.359Z", "0.29.4": "2023-03-20T13:40:09.078Z", "0.29.5": "2023-03-20T14:11:53.464Z", "0.29.6": "2023-03-20T20:23:22.278Z", "0.29.7": "2023-03-20T20:37:18.688Z", "0.29.8": "2023-03-28T13:11:50.311Z", "0.30.0": "2023-04-09T13:38:28.706Z", "0.30.1": "2023-04-11T11:26:14.263Z", "0.31.0": "2023-05-03T18:07:51.718Z", "0.31.1": "2023-05-17T14:23:05.833Z", "0.31.2": "2023-05-30T13:04:25.301Z", "0.31.3": "2023-05-31T14:48:46.863Z", "0.31.4": "2023-06-01T09:54:51.148Z", "0.32.0": "2023-06-06T17:03:50.813Z", "0.32.1": "2023-06-16T12:21:50.077Z", "0.32.2": "2023-06-16T16:05:25.144Z", "0.32.3": "2023-07-03T08:35:17.571Z", "0.32.4": "2023-07-03T11:05:32.565Z", "0.33.0": "2023-07-06T14:10:46.783Z", "0.34.0": "2023-08-01T15:41:25.521Z", "0.34.1": "2023-08-01T16:53:36.697Z", "0.34.2": "2023-08-17T10:09:40.758Z", "0.34.3": "2023-08-25T07:29:45.571Z", "0.34.4": "2023-09-08T10:34:07.577Z", "0.34.5": "2023-09-21T13:50:19.725Z", "0.34.6": "2023-09-29T07:33:04.353Z", "1.0.0-beta.0": "2023-10-02T16:40:12.273Z", "0.34.7": "2023-10-02T17:03:02.360Z", "1.0.0-beta.1": "2023-10-03T11:31:13.968Z", "1.0.0-beta.2": "2023-10-12T19:26:45.679Z", "1.0.0-beta.3": "2023-10-27T12:45:21.562Z", "1.0.0-beta.4": "2023-11-09T10:13:17.124Z", "1.0.0-beta.5": "2023-11-18T09:44:15.231Z", "1.0.0-beta.6": "2023-11-28T17:26:56.501Z", "1.0.0": "2023-12-04T15:45:42.223Z", "1.0.1": "2023-12-04T18:04:28.508Z", "1.0.2": "2023-12-07T10:12:39.107Z", "1.0.3": "2023-12-09T13:05:30.946Z", "1.0.4": "2023-12-09T19:05:00.955Z", "1.1.0": "2023-12-19T14:06:13.251Z", "1.1.1": "2023-12-31T13:37:41.880Z", "1.1.2": "2024-01-04T16:58:25.004Z", "1.1.3": "2024-01-05T08:18:45.669Z", "1.2.0": "2024-01-12T16:06:54.011Z", "1.2.1": "2024-01-17T16:23:38.458Z", "1.2.2": "2024-01-26T16:25:32.256Z", "1.3.0": "2024-02-16T17:28:56.525Z", "1.3.1": "2024-02-20T13:48:15.261Z", "1.4.0": "2024-03-15T10:30:36.690Z", "1.5.0": "2024-04-11T17:47:48.488Z", "1.5.1": "2024-04-24T11:22:16.948Z", "1.5.2": "2024-04-25T09:11:53.051Z", "1.5.3": "2024-04-30T08:40:18.868Z", "1.6.0": "2024-05-03T15:22:04.579Z", "2.0.0-beta.1": "2024-05-09T14:32:18.958Z", "2.0.0-beta.2": "2024-05-09T15:31:12.963Z", "2.0.0-beta.3": "2024-05-14T18:44:32.440Z", "2.0.0-beta.4": "2024-06-02T12:15:47.318Z", "2.0.0-beta.5": "2024-06-02T12:27:40.621Z", "2.0.0-beta.6": "2024-06-02T19:17:15.805Z", "2.0.0-beta.7": "2024-06-03T11:35:32.492Z", "2.0.0-beta.8": "2024-06-04T12:39:17.294Z", "2.0.0-beta.9": "2024-06-05T08:00:46.338Z", "2.0.0-beta.10": "2024-06-12T12:11:29.280Z", "2.0.0-beta.11": "2024-06-19T20:13:50.530Z", "2.0.0-beta.12": "2024-06-25T20:16:07.761Z", "2.0.0-beta.13": "2024-07-04T14:03:33.592Z", "2.0.0": "2024-07-08T11:39:16.068Z", "2.0.1": "2024-07-08T15:32:51.076Z", "2.0.2": "2024-07-10T15:46:27.930Z", "2.0.3": "2024-07-15T10:03:25.307Z", "2.0.4": "2024-07-22T09:13:19.994Z", "2.0.5": "2024-07-31T10:39:51.822Z", "2.1.0-beta.1": "2024-08-07T06:21:19.817Z", "2.1.0-beta.2": "2024-08-07T07:56:44.025Z", "2.1.0-beta.3": "2024-08-07T08:16:55.997Z", "2.1.0-beta.4": "2024-08-07T11:42:45.428Z", "2.1.0-beta.5": "2024-08-12T11:35:12.928Z", "2.1.0-beta.6": "2024-08-20T13:18:24.155Z", "2.1.0-beta.7": "2024-09-09T15:13:07.043Z", "2.1.0": "2024-09-12T14:03:14.786Z", "2.1.1": "2024-09-13T15:32:29.276Z", "2.1.2": "2024-10-02T16:19:55.323Z", "2.1.3": "2024-10-14T11:05:20.688Z", "2.1.4": "2024-10-28T12:27:09.357Z", "2.1.5": "2024-11-13T15:23:56.060Z", "2.2.0-beta.1": "2024-11-13T17:17:08.947Z", "2.2.0-beta.2": "2024-11-18T14:17:59.105Z", "2.1.6": "2024-11-26T12:23:45.760Z", "2.1.7": "2024-12-02T09:48:55.456Z", "2.1.8": "2024-12-02T14:46:10.680Z", "3.0.0-beta.1": "2024-12-05T17:33:30.639Z", "3.0.0-beta.2": "2024-12-10T10:21:32.256Z", "3.0.0-beta.3": "2024-12-20T16:32:36.957Z", "3.0.0-beta.4": "2025-01-08T14:23:52.418Z", "3.0.0": "2025-01-16T14:07:30.164Z", "3.0.1": "2025-01-16T19:32:39.218Z", "3.0.2": "2025-01-17T14:26:09.140Z", "3.0.3": "2025-01-21T13:58:40.294Z", "3.0.4": "2025-01-23T13:41:38.090Z", "1.6.1": "2025-02-03T13:36:02.988Z", "2.1.9": "2025-02-03T13:44:07.965Z", "3.0.5": "2025-02-03T14:01:54.992Z", "3.0.6": "2025-02-18T13:38:38.142Z", "3.0.7": "2025-02-24T17:50:44.246Z", "3.0.8": "2025-03-06T15:16:11.576Z", "3.0.9": "2025-03-17T11:59:10.660Z", "3.1.0-beta.1": "2025-03-17T12:17:24.858Z", "3.1.0-beta.2": "2025-03-21T08:28:10.490Z", "3.1.0": "2025-03-31T09:45:39.558Z", "3.1.1": "2025-03-31T10:18:54.581Z", "3.1.2": "2025-04-21T08:58:28.636Z", "3.1.3": "2025-05-05T13:44:56.822Z", "3.2.0-beta.1": "2025-05-05T16:51:10.888Z", "3.2.0-beta.2": "2025-05-19T12:38:38.488Z", "3.1.4": "2025-05-19T16:23:49.249Z", "3.2.0-beta.3": "2025-05-28T14:28:28.838Z", "3.2.0": "2025-06-02T11:10:40.254Z", "3.2.1": "2025-06-03T17:07:16.211Z", "3.2.2": "2025-06-05T13:42:10.020Z", "3.2.3": "2025-06-09T11:32:16.161Z", "3.2.4": "2025-06-17T17:53:55.791Z", "4.0.0-beta.1": "2025-06-20T15:28:29.910Z", "4.0.0-beta.2": "2025-06-24T14:24:48.178Z"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "license": "MIT", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/spy#readme", "repository": {"url": "git+https://github.com/vitest-dev/vitest.git", "type": "git", "directory": "packages/spy"}, "description": "Lightweight Jest compatible spy implementation", "maintainers": [{"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "oreann<PERSON>", "email": "<EMAIL>"}, {"name": "vitestbot", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}