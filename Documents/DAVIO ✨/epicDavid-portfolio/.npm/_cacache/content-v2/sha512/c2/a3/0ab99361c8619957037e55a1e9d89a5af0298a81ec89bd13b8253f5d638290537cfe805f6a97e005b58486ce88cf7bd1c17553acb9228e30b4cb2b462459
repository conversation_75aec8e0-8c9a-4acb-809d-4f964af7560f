{"_id": "@jest/test-sequencer", "_rev": "163-94170b36aaee693ace05628724e4cfea", "name": "@jest/test-sequencer", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.4"}, "versions": {"24.7.0": {"name": "@jest/test-sequencer", "version": "24.7.0", "license": "MIT", "_id": "@jest/test-sequencer@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05a53b29a78269143489d9653da3b31f6e32c702", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-24.7.0.tgz", "fileCount": 7, "integrity": "sha512-+i7aeDimhwDVzk6pt5r7ZPNMMJ6/p9jaIu6nVumXQjDR2UmuH+/QOnQcKml7+9/U/TEX9Fl61n+OoH+Ds0PTxw==", "signatures": [{"sig": "MEUCIQCJvwNRbuY3m2jIBh/+gF9GPJsSRB/6bUdMcMaluk+i+QIgUGgPtvgqncfKZFnGMkowUmDVWzqPGZRbhPxwBn5znic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC7cCRA9TVsSAnZWagAAhSAQAIqZYyxN1r6iqx7/QbSt\nSvBuMbSnmlLTtzmLkgeFCLLr01T+41hSNlVlxVonliIrmTDnsmZ6TnymLiTf\nEp1th+EipXf/JZJtXFXqXOXmXKUO/XtY+3RNVUMo9am+Zl+4MtYxYhGPMsc3\nTYskkmcbmaFzQe38wn9wyMgehvV/0RiOfkiatVcjwzHxVABwQ8ziFourf9UA\n+bxnjbemaQ/BZRes9Xpc11yc497yvusv7op/i6eXsMn4D2s5d0ohmIFiwSto\nGMysTbbJN1vPkHO+6AeYZ1ZGdnrO1t+uAe25CoceJhHn41fwUBzBLpuhQz1a\nJyiTVEvs3N73Aql6v9FM9iCt/0xeux5aoG6+gLlOEKU8uzJVybCGKbj3ReaY\nJV2kBOw2Lu8AeYwiihm131wiLnzE7b+3lqBw9tookZJV1rVDQ5iwPbX0vG95\nIdOpF/1y5nnadPQowdVBzZLJ9z0iMIb6huz2NaDfkwqtB0fQRZfUoP1F2Zo8\nvfaNj5gZuOozaGAe2z9v2rqhQNoemhhvrshijNUPQzi7bfYbEbZmzFioMZDL\nWbRhy6/aZ109Mv3UCOIJtVN9TUyANrEDSUjVBiZAZd6il9iBOpdx2GaLnlZq\nVzAAr529KNPY41h7YOzXDjBL3pjWmKTLIkhG1dsHJoUrVS/OE5XCi/ZiAjTY\npB6+\r\n=DBvm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-runner": "^24.7.0", "jest-runtime": "^24.7.0", "jest-haste-map": "^24.7.0", "@jest/test-result": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_24.7.0_1554263772034_0.3153364433137045", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/test-sequencer", "version": "24.7.1", "license": "MIT", "_id": "@jest/test-sequencer@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9c18e428e1ad945fa74f6233a9d35745ca0e63e0", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-24.7.1.tgz", "fileCount": 7, "integrity": "sha512-84HQkCpVZI/G1zq53gHJvSmhUer4aMYp9tTaffW28Ih5OxfCg8hGr3nTSbL1OhVDRrFZwvF+/R9gY6JRkDUpUA==", "signatures": [{"sig": "MEYCIQDCeGxkG6lDlbAc1s342A2g4cKAspR9M+oKqdmVxDS1cgIhAI+YurROL6WqlHX7Vsw1mvfjAUG5Da/q44A9K1ILdQ67", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVugCRA9TVsSAnZWagAAULcP/2UAAxqz+qJlm+aQ/zRf\n4S2gRWmcl0CGKZO+ScHoYBy89wg5Z4VbLHzqjcQIQA8YW2DnVaHl1hqhBYk7\nLKwpW+8MhHt8TvoHtI3hTYeqdjjO4mUBz3euZOzBDXQ1ur4HT6ku729Gto/I\nW5VrxB0u9reosY3wHoGIbAQSKrYpDsbgSXwkdwObmex+4KGtjwXdV49RYPhT\nh30aavJQ+AvFRmtNHvVTxC/9Lzx2yNslswQF+xokchYWlqxVmOKnitizZ3Or\nCn+zi<PERSON>orxiGicOaK7Wj4fUKIa+zGOUFNnRtwZ+xoKILBfC3ZpSRUPHqMVkhp\nU/XgVxgSyurZXfKy3kYj8vHMqCX7zAX6Wb3ssUK98mNZLxv+NcXu7nWg+9p9\nDYJfAJ66+GexU/lLKr7AhV2d111t6z08cxHGuBHTV2Ln6B/Ix9Tfh/q6TGU+\nSKGGtOQtN1k7X/W5y/QDNiT4iNiq7jThUvSvZqM8U1i3j5/yZt2+J9MUul9y\n6jI2AVaCeu2Xyr5aSNSOnROCyq4GJpZUZeU1dr3shIBP54/zqvymcp/DHe9c\nHA/QCddm7eyXjf0psFcuETkreyv33KTe83jBnqB3zNu3TfbvPzmh0yBJlxBd\nCfsSy95Z/YItQeWmK5ejM/AocUkN9VOZNJivjBF2cd5TO98YuTuDY8wvkAUM\ngcC7\r\n=lvEl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-runner": "^24.7.1", "jest-runtime": "^24.7.1", "jest-haste-map": "^24.7.1", "@jest/test-result": "^24.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_24.7.1_1554340768161_0.7853978032931281", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/test-sequencer", "version": "24.8.0", "license": "MIT", "_id": "@jest/test-sequencer@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2f993bcf6ef5eb4e65e8233a95a3320248cf994b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-24.8.0.tgz", "fileCount": 7, "integrity": "sha512-OzL/2yHyPdCHXEzhoBuq37CE99nkme15eHkAzXRVqthreWZamEMA0WoetwstsQBCXABhczpK03JNbc4L01vvLg==", "signatures": [{"sig": "MEQCIBkdjcYw5GCHU2m6EkvQDHLP2NqXuHzzP0GWhFjN1G8sAiAHYkALVZx6/VEbEduH6QUkaQv7KnBXIWthQ57GHinmfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkReCRA9TVsSAnZWagAA1gMP/3vs6qMYx/ofW6ukbYFJ\nRmUDGekAb9zcNFgDBPlGSGzj4gt5+DlPWjBQ08CSQyJXjSwnxpXm1OJcPz2Y\nZWBm3Pjcvo7OL6NYxIXmsJFanPJBqA3BYLNZNmGom04LjRcsuEdyTt1zKRmq\nsSQty0pM7DoADLRczaTyOs8zOQ9icnv9m3CX1bVBpDauS1SrcJPYp+1o6gbR\neWe8OH+kTK6xynbkoonJPiDKQzf8I5ruG0A79lDsMDoPLhD1e1pJge8ijr78\nGkTFJhOX62ahO4L/a+7SlyUyM6XsW0wwYstetSy/Fs87PxtiKf9rworEgvdB\n9KCd8Xa44mC4pgBJkTKNHelil/ytr6CCCIJJbJBu78ZoQhzL4SzbcPPh023V\naCcRAdBBJ4N8RsDvwbAfyEK1cLcmEHPrv2XT5NqX2JaRTCgkCX6SR0D3u9Py\nGGFFKhdAMu/NDEKqDCcRngLrX5eDuDai0UfG1f9KMJmm6SAs5yrmYbGsWgWs\n71U6DoidzOSrKmwI2kj+tkPr+U4M+5TFLJP7zkChntHbUHiTFCVNueXz1qAn\nVLeIam1iP6j8MHkdU+zvGwDK7AQClz2d42lo6FhSwFNuqKbs8jn4E35iaOem\nrU+BFhsMTycLtJYE6WNDn+Xyfn2cx2T+1uEc+gvn2vEu7QjaCK6qvT6negsz\nHUDL\r\n=dlqo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-runner": "^24.8.0", "jest-runtime": "^24.8.0", "jest-haste-map": "^24.8.0", "@jest/test-result": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_24.8.0_1557021789656_0.4790410719285476", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/test-sequencer", "version": "24.9.0", "license": "MIT", "_id": "@jest/test-sequencer@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f8f334f35b625a4f2f355f2fe7e6036dad2e6b31", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-24.9.0.tgz", "fileCount": 6, "integrity": "sha512-6qqsU4o0kW1dvA95qfNog8v8gkRN9ph6Lz7r96IvZpHdNipP2cBcb07J1Z45mz/VIS01OHJ3pY8T5fUY38tg4A==", "signatures": [{"sig": "MEUCICswpvSi45TcHi83I5sAVDv1GXcvcIjADwtfUKXzwOvfAiEArlI8u9EdVfImQxVfDQ/z4MTjGdzZT4fRhZtAd0gJDXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkqTCRA9TVsSAnZWagAAI0EP/1S3TyxuwE75FhcUePJC\nyiQr3aqvF+KK7exOkt088/wBnWLTy81k/u1HRv9IxE6PSLe9B8ZaTRX8nmli\neQCfhqcTrDzChyQJbwpiyOrwLyOrNqDkKkRUYi2QUbWmGBCEAS/Lo4UtoriZ\n9commLLNnyg5ONxtGTCWcA2KgGM22IobEZdAhxmL3OZ64MwaXT+/+8Lo+/nx\nxCH1buUp20pMABDgkjf/uCI626TeAHI/fJuBgGO3o46+siZ0IKOHB3GYG/Bn\n1W2iCg2bUyZh6UqxEyKmpXBP2kHtJjKQETnpBvN5Uu03ZzIa+R4OD4nuRs/v\n3x1s5Wud5pa5jj34HmjmvOmRlhPWvCF5Qk/pWg63eYnVOOTxIFe7kXHoV0Jx\nPcTykO4szD3PdiIqKPWjArq/kJXmLtzssmD2dNqQSovf1d7w2gaUZgrlaoMO\nqrsdtdxF1NlAT6LzFgSqu3aaUXVEwP4vf/UbNBlSUEMIqWwV2Cz21MFZ1Qyb\nq9uxtjPe/3vpNiweLEpjk6RXe2/Iw3ar1v76vuTUMf4EqLnNaGAwwgcPiotF\nw1JuZTZ5rpuRjfn9tT4yxp0Ou7m4EYyKfgZZU3a3tyPYE4xecGItUlbUfNcH\nrTEOKAOHfNGvho8wXHj0EdZppgCvwdKNKc3qCOPdLpqWUNTO4DMQf9bA7Eiw\nFk7Z\r\n=hvwi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "directories": {}, "dependencies": {"jest-runner": "^24.9.0", "jest-runtime": "^24.9.0", "jest-haste-map": "^24.9.0", "@jest/test-result": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_24.9.0_1565936274376_0.01038340967376472", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/test-sequencer", "version": "25.0.0", "license": "MIT", "_id": "@jest/test-sequencer@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9c7eeb2683533dfd5c9104e34a2d8590b9ccbeda", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.0.0.tgz", "fileCount": 5, "integrity": "sha512-+RXqXVSrrgjg7FwAkUOnQPlHljiHLrTA2mhPm4r5KLyFQt/sPdgH6ECeD55Aw5U/1kzt5/MUt6n6uOjpFzT6ag==", "signatures": [{"sig": "MEUCIQCkWVvhBGJXpNnstkC502EGlgnos5RV5H5iD3obmqJ1RQIgQrNL9OqUgUEGCTtDJ55oZvzPLLxrHSBy/B3J9rYEgKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgr6CRA9TVsSAnZWagAAurEP/Ryw7b27Uz/H1gtnTNbV\npLzRVjvwsOdQbENIUKYZ4dCHZ9F4KIPyEPkNmu4smMdmumWbpE0OFS0jVNRx\ndLRXRQSydPROppLHYKLXfyuDTIYyuDT/fTzwV627sEI//UY4jI1/QmZaXxe0\nu40byrZ72nSdwZNzFOe2263i1SQ5sgs/JGmsfIR3P9NgqqNvqZ8ynwX4EIF/\nS3Ssi9RiJvRiMTAiZxOm/SK4igNUfOJFbfwlDKPlQIMc65azGAJFioAfQDih\nfVZYJwmtJi+UcHwz46e+rzHNDil/gMHFs5ng5HZZK508G4PkcxEujIsc2hTP\nTHzksIFP+2+reH/Ga/TIMdfThl9MgyajK1TacOX/nmhy2YvwzxrSovQr+9tG\n07SGICFRuBzxaW0JB7EsoDpOR0uuAOg8q8UD+aTeTGxOKDTv+FHTDjglwvx+\ngJuFK/kDkIVS9RTGBuTcB0raAxYS9hTLTwixvCVEHE2qBlNM/MVDyhDrNsgR\nL2kiMVH663WLKCA6eYoX3IRMOthrfMFSpAjC76SEjWrCZ6975FKgHn7CnJ/+\nyE5QTuopwViK4NU1WJhNADOvmuzWUXYToDquO+eJkbXjipecLIMwCuvEdiuo\nbXIF1Xq2BAzIMn/zJ9wq3gSyM/V1pb0RvYJG4COL60KhyGviULdkwlHMlme2\nRgAm\r\n=GlA+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-runner": "^25.0.0", "jest-runtime": "^25.0.0", "jest-haste-map": "^25.0.0", "@jest/test-result": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.0.0_1566444282411_0.38292601194061593", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/test-sequencer", "version": "25.1.0", "license": "MIT", "_id": "@jest/test-sequencer@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4df47208542f0065f356fcdb80026e3c042851ab", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.1.0.tgz", "fileCount": 5, "integrity": "sha512-WgZLRgVr2b4l/7ED1J1RJQBOharxS11EFhmwDqknpknE0Pm87HLZVS2Asuuw+HQdfQvm2aXL2FvvBLxOD1D0iw==", "signatures": [{"sig": "MEYCIQDtQhw/8oNyJBi8rzbVKF8kk59C0EK42ZFcvnBrKMMdlQIhAJe3q127RX0j1yI/M3Vkv6QP8M7d6tTV8W+MEQE+hsB5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56sCRA9TVsSAnZWagAAgqUP/jYOUpyiB6Fjc3cx5f8f\nQEdCR2vGfrzoy4OLuNTnaXMAaFhBeK2BG2sS88EtOissYA5o4PajyBql1HY8\nqC+ob+6B6I17msuEOPpmOzfdNTMLQi7iGyhGJB8m0BEmM6d3Pp5/KSWLxjaV\nhfofjgW57BfpAM24p3JuCBtHhVKN9JpP1vD/MRv1gORJPCgUd9iIiTEwKkmR\nnIbbgnYqo3CsEzOfRz8mVPxMwMy+fTKcVANrjf+fDWt6zC/nOu7Cgzsl2YZ7\nrU+y+xJWnykD1u30GJ30wJsHjwG4JvD0UPRs0pZpelJrgVlO/paXnUr/vgiT\n4XeByBAprsG83XNUs4kQrP607iwDhjQ1MdvKxaazXIbO+JvB0yA6JwJIeHgw\nIBHPAMWv7MLEvDV9EHWN9ErpabzhP68KcsqaLxLsOvaon3sa/PDlbfwO5SfA\n7F3ARVCn+mF2wsXAHAKdb4nXNDZoLDpOalNT9EOYRx4eMQRDAj4xOa+IGNH0\n6wjm0UDaHdmx7Mk04tu/fupbHKCUOWDxJRl/LDr9nOM0UgTlhp6wKANagwCB\nS+sFFc77Kd0OV2j6jcHgqlI/xlBBwwNWWNyuP3or6L2lmMCcBagUScA+4bcl\nv2qfLN3LPKwVdqFzvvNJ/t707SWKBpNsONuoH3kZrc4gS7MJxIWFYlKza44e\n4h2e\r\n=8kWz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"jest-runner": "^25.1.0", "jest-runtime": "^25.1.0", "jest-haste-map": "^25.1.0", "@jest/test-result": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.1.0_1579654828423_0.47183928097789307", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/test-sequencer", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/test-sequencer@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6a763fc252564c8e1767839f3a04208b715b9c4b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.0-alpha.86.tgz", "fileCount": 5, "integrity": "sha512-chC+ciwdnntV3p1gPei9PZkpoJ8uRneHwjglArzZwbPyBFaVi+1U1b90Kb9TuhvKVEJ19PKOlrC4sUURkZpJRQ==", "signatures": [{"sig": "MEQCID3wg+7VZLj9yTpqHO4GVPIvYUzo8xAIQ+xhaEm6X5W4AiBgNof8RovLSVozGUgPOH+dhrgd4lH7o87aOVU+ZdCySg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5IPCRA9TVsSAnZWagAAVKkP+wfqqNoOG8t7gqEjOP5l\n0lka9TD35493iR5yP4W82HM+7hv+RQmgi4CTVwlnhUkyg2hNqto5hVMM2kaT\n9L4t1li+EmVoTawkP9sK0nX0uzpQTRSZExBFF4hS4taJOsZwtmTUHU9ox6D4\nXGMuJ5/sh0xtb4Jy/z+LF93hXqghPSqqmL4ad5NUWX5CZw/ouHLQe/pIJkAj\nMXd+bPKqYI+h+uOKeB2SOpDOIZ6KkPJcYaVhfMQr2ZXGyB6uNruThbLXKMdD\nJCQV0s/7q7o0+KDVgbWELsH4KMiTPomtlcimvbaH96K4uTxqoU1R8i1dutxg\n3qF2tMi5wMmHgTRxMcI0NXIMJR8A2GWuBScf2BU7mqHcMIKjpsoSAJZUDEyz\nApHnOLCwKrAmDLHiffoNuMaPtsLai3QJ3q0UpxxRo1E8kaMDiJdjl4j/dfVA\nZ31rGQfLhZ7faw//trg9TKK9hixS1u11NKFFUCCw8LGW4jsfJ5hscaQ9X5Tq\nqJt+DCLpKMvod21NPRHImAwW+hREFKEFGnPjmTTAi+1VYHuyyfTEeO1HQi63\n20fnuD1dHqVz9emCcI75kXEuQkO1yKhmqrE+uD2OLkLW9JJzzgARZjbat5JJ\nuud8G+tE3P1xw88WfJJcA0EX5SxHl8CCRO0w7Q+Klee2Lg5/vHnBhS5iinFX\nuCCH\r\n=cghB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.0-alpha.86+cd98198c9", "jest-runtime": "^25.2.0-alpha.86+cd98198c9", "jest-haste-map": "^25.2.0-alpha.86+cd98198c9", "@jest/test-result": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.0-alpha.86_1585156623302_0.9582268726036136", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/test-sequencer", "version": "25.2.0", "license": "MIT", "_id": "@jest/test-sequencer@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9d700bdd3296ee093c4797fb31fe4a705aad86bf", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.0.tgz", "fileCount": 5, "integrity": "sha512-eYbitqvFZo1S6nJIEoyeqcIK4WY6Z/Dz+8/7DoloYpgxUFHvJVK95UPGFMC36zvA9nB4Xpr7ICqOzrOvg2iVRg==", "signatures": [{"sig": "MEUCIQClE69Ypj+2sZ8FLgyTnDuDS3ApL3MnBgbEZFiARDzKNQIgQxUiQnPvLokOvnaZcKaXsyXBqojI17/waux4TfDVs4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vgCRA9TVsSAnZWagAAwccQAIfkyEtllnKby7qseVE7\nK09o3uTERczZnBGnYmna/cWJXK1ktkH8eJwXxWLkJqy6P3Ex0nbvAK9nrvBb\nN2UqUG3N8nFKRN8cwmluP/gtMe8nxuELpRWkG8DPDNClGhwx1/NTTwzIdwvf\nyFbCsYX7WtCiGgoeNrUit7LDShVSi7pdlZE12ys0sEe842OjiV6Ec+WS0ANS\n7sqwsIZhCglMH9JnBkt01AqoRnjFb5oufEQ1iFigzR67Y0hlM0D/CF6x18dB\n+cd+2p1XGoHNXepNQz5X55SGJLsjCklRRWk5OSN0jFUoBKzv5y+rQEH9b5uL\nIL4YBGlEIqhmkAkOWMo9yTYycuvyR5TRbddp5Ag3/rn5Ibq+qn6TkpIRVtQ6\nzajiY0yX2MIX6H5ZtR3W+C7C2ar1tD5+TqI8lSdIT9Qlg+h5R/o4J7pWm+xT\nVA289iYowni3wAt4xPFNN3nCJWve9Tkd7b9QnISDvnHxRWP6GTga0uIL0tcX\nfUKYYFZqzKOHnNStoOyNk4mKZh/Z2GRO3uz8l2Y8W1LI96CLRdNJxY3prq6w\nJYNa2fJ5Eohmr9SY3ypFfIQvQDFvh47KqwLypF7Vmdye2CZSJlpTTjbWS3OK\noZh4U0K2A91utr68KfQfKBeKxHzga165k5VYNlWN57JXom3Pp8Yj/y+3Xhb2\n3e02\r\n=dydI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.0", "jest-runtime": "^25.2.0", "jest-haste-map": "^25.2.0", "@jest/test-result": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.0_1585159135830_0.7156978915357777", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/test-sequencer", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/test-sequencer@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7e0d4938fc7a56e24b1b0ae2cc4555a9e4f7160e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.1-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-BUGKlqB9R9BMRcWy0tNbO7b9dgho/cwV7DtC6jtF8h0JEWBXoYKpoYqGMR6mhfgWiONr5AJyV9i95q++RkTpZQ==", "signatures": [{"sig": "MEUCIQDZRi6tUNzejXsTdBPWAKYUapWXT7teNvFDqKtV4SZRNwIgVF1rJzSHnjvL5saWimX+DsHlzdPluAJBKAdeG5h1PyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF/PCRA9TVsSAnZWagAA1iMQAJdhECg2E7dJZMmOXcj+\nn0ID3+DZwH0PvxEc8tpt1gIzM0b6u8YQQCKqJwgUEzI3kGV2kX3EuhegQwhW\nM7gjFlO1keauI+HpS/+e+gK6cTmI5kaApiTjTzLwLpLmy9cFZ2O+5sieaSDR\nlBRDfP//7EHvAwjrzrrizsfRcvzxVREEgWQ1V9xkoQgol3G9m3cyL7G409lo\nd4SyItWb8A+yMrnjx53wjY2O8dBQNZqEGUD1//XQ70A0rgQXoHDAInIwcQEH\nXh0QQ6Nv4YQbE8o76v4Ku04aBmqM6PX3MbXD1dg2C3U6NnVORcy6mJePybEr\n4b+7hNM8WYGPKwGl9g2NnYe3CmWRCTED3Wwu1JaJIuMZ9miYRj0vSyi1b9ZP\nCVIJIpM75MiFCn9XxFEN74/hLkIlW7vQct9fFgPIeEM5TwWcKXyeCao6UIKi\nGsB9gdmdsw5IkKrvkzCtHw56neLSACwFzMSiTtzv9XbQAUHA0fC52csHFD/g\ncqSi8hceP9WHEg2zltXESobU7mGU2c04JK0wS6BthUaEziQxpAsM+nLgbH6g\n1fs1+cqeDPmrjb8Tc2iQnp3OI8dI2WmcHP8EUPxpB3lG7LBl7pNLAGbPXsqe\nNMmIavLwAZH3iLlE6Qv0n8iBEJA8RIB0ihS3R+IfyKBa7eotmaGCi+0gjuIL\nNKro\r\n=CdXv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.1-alpha.1+5cc2ccdac", "jest-runtime": "^25.2.1-alpha.1+5cc2ccdac", "jest-haste-map": "^25.2.1-alpha.1+5cc2ccdac", "@jest/test-result": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.1-alpha.1_1585209294894_0.16990239684254327", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/test-sequencer", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/test-sequencer@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "39f5946b1634c4fab35a095a9cbbf42b3118db24", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.1-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-sgKUVQuvrUybJP8qjHdRdN6a1aHeFEqsmQSbO/ssjgHTBO9z0fUw67N7qubXfPFluo4vlCp/UzgWkTu8CVFQgQ==", "signatures": [{"sig": "MEUCIQCJSTX87vsU0M88JG/n9KajOelAMqAIOpgZaNRwZuTPsQIgINIyWXaV7VoBV5IA+AzafsoAAFqyDRmcJ5YKQYIAl5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGOYCRA9TVsSAnZWagAA1tcP/ArpM7ai2KPW+d3zqeOA\nJqeDW6QhadZEfU0sHKokr8I7qikn+ljcJiPVP/f/j7C51k4yu6hBpxMMqo1V\nBHOGsPD7H9Stbf1m3XsprUj5z5BDsoRMNSdtzRMvsYuZKa3dGRBU4ubVjYMI\n+rpbXCAmn91eU6wj6BY9wq8nw9kahvToNWiduD8x2QpcQhNDOCsMnPNWdJJx\nlxPEVVBMrYV5EepMmHVrDScb8V+lOtNdMCY4AFJtMzD+FoHqGPRwnZL0Avy4\nXGirLL8IExFJT/VEFZIIvh0hYkRZLrqXMaBhPohFVWv2Yvzl3jklkH6UbBj5\nuPsowcU5Z+7qky6dnyurFblDA/fqVeJ9bOah/ukR8Iu9lz2KCwAOdXLLEdWo\ne7vcYtPtqz9olF8gaAS8nrrBPSl21nW017Rk1+aTkrRFLWr52nwXDvdwcClk\nyBD4/hHWVCjJQQNfoMXPIEfbaL5tx4Y/qla6shMjDZhQrHMmkIxjZu3CjFde\nTo1GzmKPqlWGOL8/n9elus00ObL+gRd3uFiFZ0JUUw9p6BU1CZcJVhE6oJCs\nq+vRyrmyLgr414PRGlcpGt8SKQsvBZJxCPxAZbNgXLqObrjut8yg+gtkFf+I\nOxXuSXoRcE0Fegbj4eyA0V/zO5M+FjgoGWkzJHIZS5Cy4rbP20BsKdc4x9Re\nsj6Z\r\n=H1uI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.1-alpha.2+79b7ab67c", "jest-runtime": "^25.2.1-alpha.2+79b7ab67c", "jest-haste-map": "^25.2.1-alpha.2+79b7ab67c", "@jest/test-result": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.1-alpha.2_1585210264310_0.72488554151685", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/test-sequencer", "version": "25.2.1", "license": "MIT", "_id": "@jest/test-sequencer@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4e75d46aacabbf24cbf4b0a900253c899980856", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.1.tgz", "fileCount": 6, "integrity": "sha512-yEhVlBRS7pg3MGBIQQafYfm2NT5trFa/qoxtLftQoZmyzKx3rPy0oJ+d/8QljK4X2RGY/i7mmQDxE6sGR9UqeQ==", "signatures": [{"sig": "MEQCICVkTEToA9mcdzwoJsYDxxA09piZPfbP22mbz7o1xa6GAiAz8FakySb6ewctUKpzF5nqeJ3jWR4DjYkpLCyD2UkRIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9+CRA9TVsSAnZWagAAl60QAJA2KO8TDHzHV/JWUtub\nHKPCzjmIoR02N+tKZ3Ydu+RY0UBJps8OC+RIBxhD9PzBfzmDwfdDZH5bl0iM\nEfoxElaeVHfjQZX0NhiYlcKVgQCdAeNeCzrg6SUG2JCm8RjAPTPQ8Ja+V/Mz\n7Vltl3e/tYZjuvjIMJxWF/1oWXkxv/GaYrJQ+d6ehrae9IRv6RLanQchyjga\nZ020ObZJp6/6TiHhfh/TGN2GuoEuwY4AzlEnvUDffDh6Y2ssM+wGQPNZPZHG\nVE94BB6EUWWZH4mT8IgHHkAv4i/W4W2RgZFiqPGF6n6ficd/lo2nrDSpbDoC\nDZEFVBNimKOtfta0BjeAT6XU+feniyWQVYyEBtQokAs39tXmbnh40qpUYS8C\nnUa67znSV7457jRJGlIFcBXDMg72f3eaaJBhXJUcdr3k+CTponzCWBqBBQEm\nyaTxIK0QWcZpdXBZiPjAJNN8u4uCVrLuhQyFLQ7WD//d7eE+dmibvUN2LLGy\nYjWeZl0hK/7/isfceCDMfrOFcKuSkam/9J5vn8SK6ucGxBgEJvcBtUWOs4r/\nKs1FGGvmIJgSCaNIp8PuIq8H867R8ebkApu8irrUTneRT4mYYhpHqjIa9PEU\nbbDJ39stOjZGCZlot+JsGWPQiWsA6+FtY07+TllKStYmdZpFCmi9DvFn8b9O\ninka\r\n=gNIx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.1", "jest-runtime": "^25.2.1", "jest-haste-map": "^25.2.1", "@jest/test-result": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.1_1585213309921_0.4428619749409597", "host": "s3://npm-registry-packages"}}, "25.2.2": {"name": "@jest/test-sequencer", "version": "25.2.2", "license": "MIT", "_id": "@jest/test-sequencer@25.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "22132174af970527e856c96d78bf446828eb6bde", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.2.tgz", "fileCount": 6, "integrity": "sha512-bN1LH30EygCNrZS7gkKBmqBxQwilYiMlAV+wY9oWFRY4VZAxvPwqg5/f0DrZQc60vTW1fXWrkfslSa87sO/P5A==", "signatures": [{"sig": "MEUCIHUNE/SAYHkOqGtTz+Av7jffxJqZ8dIA8eu6hXFiSO60AiEA7TiutRiZoMS6ngyMgeyY0yBvMrjXxv7ZCSFnqaFkruw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefNCXCRA9TVsSAnZWagAAtpEP/ig82e4HNcSuzY5D/4Dk\n6+h1ukgh2unmvGKJdQnnVxd/5nKILx7Ou/PyBf2CTPrdA2mr+eYmZ1J6nXow\nEpalrJr1ebC/zKPWY4trUh52TjzJ47+H5Lv26cGLzIHyibT5ZNy9r/IF81G2\nwid0cAdEvnwvlR9RDXO7z+eGSqBCNXRTiMmPAi7KVCIRMIrZlEmHkbNeXRKf\nktmoZdixP1ubqCrYH1y4ynMSEpsoYmOVhn3D5swKk8QwJvobv0iMARBacZ+/\nnDZFq7uPzSaczCjWi1mdxNDlrZXLYvzZryrKB9PlDdkaUiW3Gv+99m+UQKSd\nVUXxfa61AbMJkatevmrqr+XLdKASH7b5GVe3LpcsuIXWM2Dyo5ow9oVmh7Si\nPTY1VhnC061e4wuv1aQzuzMvTrrPjHLipoZ58S29KdB3gyHIUS4Mboa8W2VH\nLhpSFc7xS78piWn1Xrpr9Dt4hMxArGRQ0AZ3tpjjyssPD1EKQ7SEgpDKgY02\nUvGpkO7keJ28ny8EdBD7mKPPSk2ei0jWyBHTFabt7rvF+/lmXOiU6BubLHfZ\nzHIIz4dpKOsjdViy+3E3nBpqmN8pqE5in5f0bNHHk8wiqVDdo/lrT/KL+hCx\n3ZsDRyyZgnNNSuq2NyqxJ81O1hJTVdEA1LEFFSZTgZ3nDlwnKc4V/0ArA6/d\nJADI\r\n=jtx5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "96aaf892bb90ac5054b6e57a7661c35ff9ab6873", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.2", "jest-runtime": "^25.2.2", "jest-haste-map": "^25.2.1", "@jest/test-result": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.2_1585238166869_0.8257399773186844", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/test-sequencer", "version": "25.2.3", "license": "MIT", "_id": "@jest/test-sequencer@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1400e0e994904844567e6e33c87062cbdf1f3e99", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.3.tgz", "fileCount": 6, "integrity": "sha512-trHwV/wCrxWyZyNyNBUQExsaHyBVQxJwH3butpEcR+KBJPfaTUxtpXaxfs38IXXAhH68J4kPZgAaRRfkFTLunA==", "signatures": [{"sig": "MEYCIQCRcpAXyPgyin3e8SKFpcLdGKA2ryzwsmmPGqhe3ngb9gIhAOx9iOZNRMfsSIFf9widNmEVlCX5CBta3fgwsN/1KpDK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+2CRA9TVsSAnZWagAAiGUP/3pE6xK/92zsh6mX4bXt\nRWYTjYHCWRhv/5HdvXefmjnS7/1jzUtzNKkZz1NhsiE42VLCYWxN8lBR2sFK\nDgsMNGIUKlCwUx32ML4j16IIRdUvWuoF5eVxHBAWS5Zvs5Lw0PFniESFL6wG\n4HSV2Ylk1sXD7g94cqnPPjI3ZastztIpUsPXUGz6FNjbDHbCOJjRU2Ae+BsB\nwdeh7RLbGJjgSdu3feEF/xpb7oGT8pa62UE1ltlW6tEySNeVCMfETTLcMpUF\n3uxmsgS+TV6sZGDcdQijkb7urn1DzvlgCv5rctu7y2jlZXFLBXgHSodvkuuF\nQyLs+nNSybks2Yae5+D0r99x4Q6eSeOV2EpKy7xll6Y3VVj0zuhgiA0Ds1mn\nFzH7hfhitVq8pOCd8pDippPexo1mTgvXVmuh8REPkf62zvqPfaCl3UQw4Mbe\nWukcbZHi+BWAgZBNvTQGi4/IDJQt4fkYpTfqtTmSQKIKk2zDkm5vsAB7PgPw\nBLA5FXXjIVqbtiM9YmQCOvi+4NAqnleSRXZ4Fic57uw64gCK0fuN9GjDYbxB\njmJiypn8UpzyhfrTiBeFeh33rVM2oGob4652dHv7saG3aXleG32mnE6IQFNM\nweNrdriCXgL9Tu9ELPKAvPm2UK5QiJsO6q0IpM53kn8z9zwu55NfHXHfjJln\nTtsZ\r\n=hdNm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.3", "jest-runtime": "^25.2.3", "jest-haste-map": "^25.2.3", "@jest/test-result": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.3_1585254325876_0.13201593985222448", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "@jest/test-sequencer", "version": "25.2.4", "license": "MIT", "_id": "@jest/test-sequencer@25.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "28364aeddec140c696324114f63570f3de536c87", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.4.tgz", "fileCount": 6, "integrity": "sha512-TEZm/Rkd6YgskdpTJdYLBtu6Gc11tfWPuSpatq0duH77ekjU8dpqX2zkPdY/ayuHxztV5LTJoV5BLtI9mZfXew==", "signatures": [{"sig": "MEUCIFMW5LJ6/Gj9ml/s40jhInpwAcaDpOeFYBYns1mPcXkQAiEAqrLDhI609ga3odvxuugl0YJMxMI9xyzkOiYPjXxCX0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPlSCRA9TVsSAnZWagAA5V0QAInFTP/zqGnCwUXxKnEI\nnQ39CHVRo95dLbilIGssUFv3ON4KUwQUr4dxaG65QpkQ4HLpu9qjN4UCVSxG\n3Liwo3oYkCT4lm68EGnFOIbpUWitCbJ1WqSPZjMuHajUycs5lciU6tMWntLI\nek8futwfN7GIaXBtKDlFEemyvsxlJ1JCZO/wJhvZbeWdqZ8zW1gv7NZnN2ew\nDF4XnT047icszCtTf+kCrbD+f21ilntu3SiB03UuzOW5IS8MUqWjFOUrWMVo\nwBSwLetrb6BSPB1jshzOhy5J/XdY4gveX2+u/AbJ7W6lmQM68XB9k7bk1/cT\n/bU+pulfG/7LT5upZw1zgsYyvxzIexDcOtpVGrdSURLKgaiQeVGTKxBqAex0\nMAjCKfpr3XYA5PYtTu7otJBZeqclZ33oei6+Nj2mOqQNgrjSTYzhKfiwWbcH\nhwph2pD6b1s3B34Rw0el7dtE3ETKcjKhOGwuRb5CGw9fJGjD4po+3nsj0H9H\nSR0doPq7wxEzzDaLvOiWRdfMTUWvr79ld7bYSs70DoDM54YfmuKa+CiGz0cG\ncgxR5hv8ud5DgiC8fdZ5luge6EfsR9fK6OmHQEUcCwFtIHReCZBlp0nc6yxS\n5iZ2zIlf4km4lCjKhAHRB4wu7reEcTUyTZ4A4qWmzgi0SPvfNjsFe4ouZaXn\n3kMu\r\n=304R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.4", "jest-runtime": "^25.2.4", "jest-haste-map": "^25.2.3", "@jest/test-result": "^25.2.4"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.4_1585510737621_0.7352564366111842", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/test-sequencer", "version": "25.2.6", "license": "MIT", "_id": "@jest/test-sequencer@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "62026007610b0323e646ad70db59c69c7ed4785c", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.6.tgz", "fileCount": 6, "integrity": "sha512-6sHqVeXbEapfxoGb77NKCywNn9jc4WlIPtFqhwCKGhigGnpl42AuyLxclRWxbFx+V63ozzfjnemYxqHlkcoikQ==", "signatures": [{"sig": "MEQCIDgzX8oHe14WrFwOj2pRzUyfZazXNcw2eH9Vribe8K/OAiAJydZjz8NVMe54T+ifnJtoRv4WoueXfAAZ7IA3t9iGYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6hCRA9TVsSAnZWagAAi40P/RvHEzra6+AfB82hM5Ko\nfivK4uE8A0TMnUgBTHVQs//qkj2yx8L1jQhmZ9Ebjz5PIU/Z+8frPv1Plgai\nyqWHOFUgNQRAg/gRGrvk3vcz6lf/IhqBmLKrODdn+oKcBIfr5UZRl0rMlO8N\nFhoP0koNAT8kFlxn6ukswstlATIIa1YI+uFdPTzOAe/rGNqenYMrsSQfNjbF\nlkxWJfMv1nPUnv66BLsGfHOvz5u0IKIAEbMneL1zDeiWUwb+FyE09Vt4Qg7W\nz5Fwnz5D90ON18KOGpw4jTnQQjr5qPBBK08h+C2re/WbLNhAHYpERUK78p77\nvbCJvY0AeE0Zbc2TnrNEC9puGzl87iKYqxIRsOdPFxDZh5Wv26npPVB3r89a\nrkasgJAgHf2PGvnVQ0M9tB0Mbh/b3ks/guqAyJmnnxjUNJaoBjDfxzL4m6rw\n5FxzvR15/zeyWiUt/pakwmct1NTrPZBCtDuh72pgDvWQtWs4ihGtSv9U/A1O\nbMh4seF+FTAAeqiUikBL5ibZBV1xtPYPqvGOeF+Ut2nNK4O4nFNuFaUchXBn\nn+jYCGS9jo0Ml2JVIyJk04LaAQMW6W3sZ/ZuiyE+sYke231flEiRe4hO27oF\n5/itEp3be7Nwn1ZpjldnpwpYU+Q3/fhnN5pcSAiK1+I/o0XMx8tuUtOuvwVx\niFtB\r\n=zPKY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.6", "jest-runtime": "^25.2.6", "jest-haste-map": "^25.2.6", "@jest/test-result": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.6_1585823392820_0.6777566727109396", "host": "s3://npm-registry-packages"}}, "25.2.7": {"name": "@jest/test-sequencer", "version": "25.2.7", "license": "MIT", "_id": "@jest/test-sequencer@25.2.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4331f7b4850e34289b9a5c8ec8a2c03b400da8f", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.2.7.tgz", "fileCount": 6, "integrity": "sha512-s2uYGOXONDSTJQcZJ9A3Zkg3hwe53RlX1HjUNqjUy3HIqwgwCKJbnAKYsORPbhxXi3ARMKA7JNBi9arsTxXoYw==", "signatures": [{"sig": "MEQCIA/9PnZGe+dnTCQi4jA4j3mZuk8ED1nKPbxCrDA2u/h/AiBS1vj9NcNCjsilxBw0NZrARoqZXQJnsKikO/WSjAdF2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehur/CRA9TVsSAnZWagAAqaQQAJhas/5lWtrTPaVor8LM\nsEverZMCG0o0eq8+ksE4iUrLX4seOX7FlhEuj9Kfx/KJw8AP3GhBDRqkZCcN\nedj9V2aeawIoVyh1TANpoZwW6AlDnTE1XmDiIZzDJf+95naKq7KLFlG42iDg\nU3aHFUfgn8UWtXFSStFQeF71+05MqQyXBu3Z9Zyo2pks01MQ1oI2ROMtaJZT\nlWJ6NGDGu2IoJ7uwd8ue+mRuM/+UMm2rqv7FZlA1oCjHoshRC2GDY3QoKFa+\nwkNDOGBYOzZhcscHDex2CuTqTnHTI87PWJbwdAD2Cw5VwaW6LBwWiyWlG9mS\nzTacZ9oNn7ikbBDoH+i68kaAF8O0ihyrk33CH2Yo8t9Pz+j3hvN9iv7kmg30\nFlHuYfbDXc5fMSYOSEB3jGHJzg8JOdVBfW8hB3dH3guQQ/OakVG2nFx7rngS\nEo0rzVglcsCjRWQQjvNMdYh8+CsaEK242DqW5jkzb4rHWIWLvFNdcXVwNkb6\nbUNDEzh6/X6MyN1Jzbkmb524ANr8RAOcURdBHjGJ0lXafXU2WdZx0VEtvnkA\noO1qtNtEBX4yrpjp9jpbcuV4cyBKO3uVGTC+TevLUPyb1FtiWifzhvbpdwwT\nFLDJMZxW8oRO693F4y5Jcr/V0BH5qSF1lf3gU6v8redZ2bC3KbwE9Y3kCAxH\nLMK2\r\n=ssWf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "3c2fa9347b86460b5dfc558f033b8d4eec0ff8e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-runner": "^25.2.7", "jest-runtime": "^25.2.7", "jest-haste-map": "^25.2.6", "@jest/test-result": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.2.7_1585900287305_0.6951585505814346", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/test-sequencer", "version": "25.3.0", "license": "MIT", "_id": "@jest/test-sequencer@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "271ad5f2b8f8137d092ccedc87e16a50f8676209", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.3.0.tgz", "fileCount": 6, "integrity": "sha512-Xvns3xbji7JCvVcDGvqJ/pf4IpmohPODumoPEZJ0/VgC5gI4XaNVIBET2Dq5Czu6Gk3xFcmhtthh/MBOTljdNg==", "signatures": [{"sig": "MEQCIEULc2rizWcsiwWAEFOJzPx9mWDho/jFBBsn/2c57NoXAiAtOFRKbH1/u2EvHhdOw7XvaUha2Jr+sAIPY7o0UZx/EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/xCRA9TVsSAnZWagAAvGUP/j5KjjfwyfUDqMS53Qvh\n+Q5f+FayNBztjC2PCryAQHfbaAaCNVTodQHxAK4ZSheEMhtKTHkRKviJ0gd9\nszzBl5rJ/TnvUo2FSn7exJT1af6ru9SxTpsGl4lIxNp0HZFk7a/g/+AaFcQ/\n6A5+Xgv8SJEw/DRR/gpdWWofxXGcB9F49W0SmddG37hv4sVyyOooIRRvOyIX\nwb4DHlsD7v4CMj+epXG/ewOZqi+RFCOotaKtN8dHJgtS6/9L63KTmvGPtO1p\n1dArARosozxz55y7IYD/EFhvouPO4ZTCAX8vGbX+w35yGAlpVYlfmk1TAOlU\n7BY614UIOW9Ban8bP2NHH8teLGBZOFkSYZPEa7BsSu3UGiMR8e70LqnzUHrU\nOvLDQnm+EefNZA3Sey2DxviZd10MgFHxnMnJjEP7/OPjGMfhbJzVKQuo54I7\nlIuT9R6VvY02i7xvYjHh5Yemk9Lml3f4w81WvHWRWCdPda0wiM5E9WaHwQ6M\nKQF2C185AfX+1sOWvGmCSV6AjkSvudY+VX9uCO9LxcbELklm2+p05NwPHkDe\nkplApj5Ui1Z3hJQJVItzcyO30t7jBPOYTdNG9pM2C5YLlqqu5uZ1y0gX1CTe\n1Ml3iK2IJX27nXc2c53rJSs05wvFgjk/W9ySmt8bLya6Nuxz7qgLVmNH6jIR\n2+7e\r\n=kmft\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-runner": "^25.3.0", "jest-runtime": "^25.3.0", "jest-haste-map": "^25.3.0", "@jest/test-result": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.3.0_1586352112804_0.5322780668196585", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/test-sequencer", "version": "25.4.0", "license": "MIT", "_id": "@jest/test-sequencer@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2b96f9d37f18dc3336b28e3c8070f97f9f55f43b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.4.0.tgz", "fileCount": 5, "integrity": "sha512-240cI+nsM3attx2bMp9uGjjHrwrpvxxrZi8Tyqp/cfOzl98oZXVakXBgxODGyBYAy/UGXPKXLvNc2GaqItrsJg==", "signatures": [{"sig": "MEUCIE+V2mSO8vf7vyhjAllPjvp7r2iKod4QmB84DCLKVGWrAiEAlIJk3Wxba39EeFOhOfTrGlUE9TkK0xGulp51D7VtY6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMfACRA9TVsSAnZWagAAnHgP/3yRidk9dOQzEVUItjGD\nRD0gETipNvp6DvFCH5Ut6401vINloPUhpNXbqvPkyaHxxagC015/rNsLFKbb\nUKXs5r2l4WzEPqk+zAfIaa4l8hkPJWEfa5NNuboA7jaJYsHN93q46MwmHbLb\nDOPXbxhl73CtcwIj335Of+fXWT4auhh06T7EbDgTIH9METL4bO975q+e+KaK\nOxa2doItsOI0NEU8KTqLEbAwUiQJr3e6VkGYJOLu+fpfDnc1bTv+HpzFkLW1\nVLHxf/chX5EdCaKJoSPS6/4pEa6or9pkFF1MzQydeejj8uVICQHY4V/8g/+B\n0usk+EiSydmRjH9ceYz1rjd1R1jz4NDcVHxI1jDgZ/ru0Q9NXeu52LFCuoPt\n1iiKquhkpihiTCrNvOc1pLXy5HyrrtbMJ8fiJXzh7TeHDkzVp4lMVIyvhe+0\n6PC/sDS3M/Aq2HLzo102Oq7FaBjVXPZXp56TO7T8fw10YjOs7I5nJXilH/nw\nrMZWEQcR48yvgP61M3q3IpMjU/6bFpJ3ecENRIjUznz8FniLzOEMgLeqaRE0\nSJnq0qexH/VJICFozh6majkKDCU/Bib2IlHs+2DUpqJx9JYxSggxiARt8Cv9\npeBVeSr/CBwM+AA4Ebn+iw4W2jylduUwbV6j8xSbIAMZnn3AVaPkPjvDXJa3\n3Af1\r\n=91mJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-runner": "^25.4.0", "jest-runtime": "^25.4.0", "jest-haste-map": "^25.4.0", "@jest/test-result": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.4.0_1587333056014_0.11148591249022277", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/test-sequencer", "version": "25.5.0", "license": "MIT", "_id": "@jest/test-sequencer@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "edc249d26f73804172dbf30a140d04b494a9dc38", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.5.0.tgz", "fileCount": 5, "integrity": "sha512-c9Go3EK4+5erD2HKibIyt8JqImV23iGTWfaqMKdtD3aish8CDcXlq1X+L/CMFPOORJDV63quN4obR6iHpARapg==", "signatures": [{"sig": "MEQCIC0e6lESEIONm4HAvhoRLiIon2gXElJIRgzSRT1269CTAiALUUTZkxp9jjTMrxpG2ptkxKPlX9Mg4gtlMxuAOQ+vRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfzCRA9TVsSAnZWagAAUu0P/2BtiJTz6ER70M7YQlco\nEiu6tKkVx7qoFr9gRWaTbV3q5GDr40qymsTc+BVkldmQ/8G+KcqnVZYX8G6I\n6AkNFHIzl4jZag3KAN2b4K192qK/fh/DFRepUIgxf/JAKAXkg0MjsjTj1Ymu\ns4fB+zWF/rLsRLlbWC9JacFc0FDjJ+ytbaHgrwQpWO2h+xLSe25HMAdlqrIk\nPwYdltRAf3nQuus93FM7aQ/AmO/xi1DemD5yKPg0qYonEthAVGB8blEC8A0R\n8cqHSYLqiKz6D+fP979AvpBIf9G1GnqgkQ//SeohKiJQlXfgh9LwZGo3Akl0\nErSDLQbV+KpyuF7QRGLnEB0ONGRW7XhenYqmTuivTx/IE4oJ1ux7c8oKXxug\nHeJnT9mXDX3cbRuTUbXoc9d7RSENo8uepjg/l0/h2rgPrQd/TI0Ya5ej/gqX\n/UyOtElidpRnk+IQGT6oGrmoQq2Mdjli9Byx2dR5i7ElwOVpW+Q3yop2+GGU\nZN4sxgtgHcKReVZ5Qb3xyJs7l99wc5Vzge6VvWRus3mt1JqKeTOuAGszU2HB\nEHalDLrRKQO2yOHSfRu/DCJbAA0JOj6ymG5PJewaV/HTufHRFhvWOfSgwMmv\nI8OK+PvuL8K9a4mh1dzPvyEQJFaQigymzShuIwKAu0cq4F7b99y0GomSGyd6\nAO3o\r\n=8BLI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^25.5.0", "jest-runtime": "^25.5.0", "jest-haste-map": "^25.5.0", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.5.0_1588103154572_0.36152741997219673", "host": "s3://npm-registry-packages"}}, "25.5.1": {"name": "@jest/test-sequencer", "version": "25.5.1", "license": "MIT", "_id": "@jest/test-sequencer@25.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70ab0f73a38474e004662857a067c9fe5cc64918", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.5.1.tgz", "fileCount": 5, "integrity": "sha512-XcnGwXFfs194AKO3M8xRsDLwo72LzuLuhOjd9e7AaBe5kEZk8fbGRxE7pQSqHyL49xV/NCCXUtBJ9lDqt1NLnw==", "signatures": [{"sig": "MEUCIDfmnODz1RAB+Hb7L4Xr0vq6sliQ/OVJN3mIzHn0hT0sAiEA1IkIvWI38gOkkoKJkL9O+23SDaOgaHYy7GmaccoDr9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqV0XCRA9TVsSAnZWagAAwu0P/2jwwVqtTwDyIMMT4I7e\ndiPNWGcD4dumKPDPKXM5ortCxWukRRexa6KIhvsOU3AoQ7RgDurFOCCb9XNh\n9cLEmpNEBrapnXAchQ7wsXm8j3Z5Mm3Lk7UMnN2hHPX41Jk1710pdyYK8Rff\neJcLeYFlDix7TsfxUR6mP/PP9KtHcCDYS85Y83ZjukUMSAcj6ceW/afVj5Pf\nXrOQBRjQp9FHgaNQBJQmQfavOe99wZFQ1VmyRpOXJWEKa7At8VTECUaKPLIg\n2ipnB7vaOOlnt4DCJpGp3fRv5WOdwkJFz/Z2vjtCckpyL3PlwzY/v0+efJqy\nGqQgcCsyzoC/qwpcRpENHXv4yZ9EOAHjg6AIiu0owCFbIrnRL/2DtQkSCC1I\nAARf0z6nobYvJNKJlvCDnczMpTDmhaJtdG1xeWI7o2cB51bP4JankWESq6td\nul3H1naXn9aEZsZ+AoOTR07/Peum+VsL1F5O8lyP39UREkpG/t2sKNK5cf0r\nV9q9gKz2NPTft4nVUdSgLet/5+NRhBOMlMpASfbFNjcwzA6kHVN9nGLhXIYE\nG3Ioy4vhBFH1MSDb85lUTu8Fnzg/TM+/U2jf8Ubr9HrS3a6XdhdpUgKpctjI\nL+kyY4rOOWfaOMn+60Cf2E01zRyLGOrPThP0VIp7uYlreY/lgEXrNrQbgFFT\nWDsN\r\n=ls0h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "c5f2fd756331895b8177a19304feb49657687e22", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^25.5.1", "jest-runtime": "^25.5.1", "jest-haste-map": "^25.5.1", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.5.1_1588157718758_0.8945080074453335", "host": "s3://npm-registry-packages"}}, "25.5.2": {"name": "@jest/test-sequencer", "version": "25.5.2", "license": "MIT", "_id": "@jest/test-sequencer@25.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d291179de020f42835469fff1de496d6309848a", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.5.2.tgz", "fileCount": 5, "integrity": "sha512-spQjGJ+QTjqB2NcZclkEpStF4uXxfpMfGAsW12dtxfjR9nsxTyTEYt8JUtrpxfYk8R1iTbcwkayekxZPB2MEiw==", "signatures": [{"sig": "MEYCIQDsXq+QI4XXxjFNz79iqk/o2p6Ne4hwMJXelayzjMyePQIhAPtwVJyiWGdEZhVpk5cZ5StL8HZjQeRab1+RtAGjvY74", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqfaRCRA9TVsSAnZWagAAYM0P/2AZD4Q9ZxRfLkFkAsQ5\nvcVEwjy6COEwXPCWdpy3kAQlGGH5JrVBIx4dtsvpgvBBY/1mJTxA0OGIkEVi\n9yWK68RSasSCGGEGCLdXCh0vVSkooruMZs9+RJdxMJMCMXrBdxeruJlds49W\nBZQ5eoyMZXM0gxyLgK2c33hvfBNlI+fP2leqC3KtdAVvSnyYSu5ra921plSV\nNX7p06RJUT0VuZbRfUNuuxVoILAmRHUK/HR6JOTdfl5aeqrfDhCf4SOHr93t\nAGTrT/g7w8Syv0vcp8w3rrRbNsbQAwxfyT8PcZmZq5o3OPqLvCiFfAp0yANj\n5YrzNGiQaQlUrH2lH7m9kfdKvthhvZTAX4HBw07yw4zbYeRluGUW8zGCQZWD\nZEdWCsK5zTLuokRtltGeKIP2jWKtQNhMpjwcTSqQifgzb5ovcH5EAYDOtkHJ\nFF4gfRqoVOdjEkqwSg/xKnDFdJvlgEDKM/l20bUkTA3+6yqpmPH39c+e++DX\nfZcxj554D75YvPV5mwVDKLDbNga/x02YKNdspk354ghsDpgXR+GtwaTjoUiQ\njLrxfwlFQPe9EJtXfWue/I4c7TG6MDcgVQrbakSA17Uyf0F17AEloLFw0YUe\n2jURYCXrnrcG1kSvtn146voTiYc66UYRWuHtYVkeijYO2syofLLfi88YegD3\nQBeM\r\n=0GA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ad1b9dc090922a8ed5632ec7382ac999e6b8cac1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^25.5.2", "jest-runtime": "^25.5.2", "jest-haste-map": "^25.5.1", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.5.2_1588197008523_0.6889241495685561", "host": "s3://npm-registry-packages"}}, "25.5.3": {"name": "@jest/test-sequencer", "version": "25.5.3", "license": "MIT", "_id": "@jest/test-sequencer@25.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b6638d5118a1f8a7cb3f49fe59087326a589fa36", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.5.3.tgz", "fileCount": 5, "integrity": "sha512-/xvmwp+7eTsgmbyFp649WCpyf6x+c7CMERYOnZtIYIqpOW2p0vDbY1TNyr1wum4c/xSe+KR8iBIzsZKa6csmeg==", "signatures": [{"sig": "MEUCIC/IWr0SmgZS3CrskzrWVZVld9EXZFkcUatuJkk38VRHAiEA8gHQWB5CC+FKxDNK9GcqtA01jlxDHFd/fLyGAGxNLKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeq02OCRA9TVsSAnZWagAASOcP/iaw+dsan9pBG1SAxtsQ\nfc6H00mSGnkVPxb3+ctsyZKgYpc2uxwR9+5fujpEFp44cRcXYWEsKvHcBLDT\nsvNUpCPMJkUVOAcaPuJr4rQVFubKqNKXL2Z+1LuJ7MK+y1IyYCEKXuONbpGG\nSVMhWgsQ/E8Xs1ANClwRaYCBtElENf7d1rCYJRHp8RI9Gfb63dRMamS+18j2\nk9f/DhWapbs2PsO2hjOcoex1V63u/BD/01EHZIFvuiYqejY2+tsC+MN7wrwe\ndJzVJGq2K5At8/Cx9+Ppb2JMfZSyTpFU25hrbbwQkf5AWUXZXBOeAJfE3lsG\nRqxmf7ZxJjyEyNSW7EZkmyI+DYmJv4zYYA8nTeO6EFp4fkFXN1gqecbT8pgJ\nNt0zy3PQs2k8GnIfKBbuAzV4uzr+1juHgcAAfurZ+l2Kl3pD80Ozq5PdFLgf\nUgjt5L7oOoFdHACwCil21ul49tisoaoL/onkonaGRg9ruS81pdfeTfSyGtd1\n4JUlorCHVEfDIlOn1ieumLK87DbxCiZoa1HDRGGIWVAWeXKuNISvC9SVF7gv\n9ZYmbU3B4Sfbuc/9QPNR9VzgpLjpoeAqsDlZLafYisK2ll+2TeRrfy1+APXG\n1q2BgGK1YfUDrXjUXpk9nCSgs9EuGnjGLbcDE8Xk7hb1PrvijuL9RyKUkJ7M\ndHhH\r\n=QSiQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "80b69051271602aa62ed8de896ba80ca3150f4a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^25.5.3", "jest-runtime": "^25.5.3", "jest-haste-map": "^25.5.1", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.5.3_1588284811828_0.7658556159830832", "host": "s3://npm-registry-packages"}}, "25.5.4": {"name": "@jest/test-sequencer", "version": "25.5.4", "license": "MIT", "_id": "@jest/test-sequencer@25.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b4e685b36954c38d0f052e596d28161bdc8b737", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-25.5.4.tgz", "fileCount": 5, "integrity": "sha512-pTJGEkSeg1EkCO2YWq6hbFvKNXk8ejqlxiOg1jBNLnWrgXOkdY6UmqZpwGFXNnRt9B8nO1uWMzLLZ4eCmhkPNA==", "signatures": [{"sig": "MEUCIDCsFIOlaGIWiCjqkMsduYTr1X6cEb/2Ds/KGXSly5DfAiEA7s+af7CzjbJjAPxpTMNhcoT0nkF18/Zkc9EvRAaNrKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerSxvCRA9TVsSAnZWagAAVVQP/A+xWwDUogeesVoMYu4z\nrXlfDXks9YYkHmnBF6Kw+LCkEBa8vAx36qAKTNiQwfI6iJQ6vOJlzyZ7HbYh\n0nOqXfNkhF3Ey90eE71gIPNivrKEuA5kkDX0P/ZUkYfLiitM4pGYqutQiCE4\n2fDxyC8bh/9jZk5AbgfkEoQdvYWgt9fwLc54YJ9Trw5c1yqxo15DrcWoswc0\nXtsSc2GBbSm1yE1Wqf55yefaTwQ1k/cfUNX2wC4noLW8MUINmXo6uLeutSmn\n85Lc4RlPtuLJoHwOcSTlk99jLGNw8Bvx9xMR+cYK673r/eShXC9LIMt8tjDQ\nkp7z9X6NMnQ/khXZkdomoecyuZcCPxICqUHeeHrNEMwY/MOJZinYXb435N+l\nE6ksm39TlqHDJksmry2+dtmWebKyAqG6dfOIjH0pOMCvRO2SH4wBTHHpwzZ/\nk3oUvhANCr3ysLz+gVwke+sM8Zle2e7ZbhsILhfJrm0/3AFi4TScrxuEujlR\nfynDgYH/MB1MNFizorA4lyflUtJLSyZ0wVTjy5dIOI/D8rPjwFhPA7wjS7qL\noTHYQEhO+0Bbp8DpmOYtv65YbRom/jqxxLaR4eeV+2VibPpr8NWe0Y9vEK0b\n4twSW6QKqeT3iY1OA+iK86LVjpUfLVxMwqW0G6nLzGYG1KGeNaNWYTtPhKR7\nBmIr\r\n=PJ8l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "389d13724bbf6bb64dcde9700a6ecea3333942db", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^25.5.4", "jest-runtime": "^25.5.4", "jest-haste-map": "^25.5.1", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_25.5.4_1588407407484_0.9599236479004447", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/test-sequencer", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-sequencer@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c38006ae8f720c4d0b1deb95e251eb4d595bf08", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-GH9B6F6c8xtDZ+EueEWhOk/GZ8OJSeNPY86a7HAJfFdF9QqYFKJ6NOcgsDJIdTCpXJXyPatgf2QzbqSofn0Wzw==", "signatures": [{"sig": "MEYCIQCccKWv6lCXpiAK9qMD/73Dg267DlWIiZcRlowjiMF6NwIhAILDSXiSvUuplCHHQJoW4lvfw3WEQr4X1nJWpjp3n8gN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPsCRA9TVsSAnZWagAAGfsP/RNp3eEzy1f22MkFxWiX\nNwxzVNe1dxzgOrgjiY3X3WEC1ErAy5UX9mDpyaEIrrYBWlKv9VWH9T5J871q\nIsitmOkxtOGDBH/JFYy5u6u6vAgZWcMOdWF0mIphwnssXie/rTXb1OLjRETc\nirDTHd51FSWBGN8sV7CQYUZEfUk+US61X3wYetgItVyvKg6OV+jG5dUeWOtt\nbiarOf+g/3zkrb/WTsklW5Q+TPHgq4odQ6p8/Sw/WOFZaKQ8SCzgf67HyX0m\nXN2ujBjrr56drcnLEQtV7YLlV4Fqy3ma85ksC5RE5YE2u4QqEXqi28oWFrqm\nR+DHc27tdRlwNpu8J6JvoUY4rpPBjPS45EkeDImzGsEjlWMFgmRKgKyC++sP\ndqNY2eySAJ6UqrnLsOEEXzGmlap2xHOvVVixfQGSuZGkql53grvFeqc1ESUN\nGo8Ue55QZRuanm4+4wE642SNCAySXEZ/4hFAF1oNIdDdP05H9XiedNK0sFa9\nyS8uwdhOuLUtu7APLmf6PQN02YG1hCOYL6wSFhI8UkpIKh/+IK34VO0ljm0S\npk09s/Q4AR8/QbwYGjySxLEe0M0y+FULlbgxzLPOKRqoxNsF4TKpV0N4oBIy\n9pNYAW4geP+QapckOYhPmJlFLoiJ5P7yFqiDnIHBFInCzvcb1oEuRcukjS0t\nBQUp\r\n=apII\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.0-alpha.0", "jest-runtime": "^26.0.0-alpha.0", "jest-haste-map": "^26.0.0-alpha.0", "@jest/test-result": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.0-alpha.0_1588421611665_0.043100377133761025", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/test-sequencer", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-sequencer@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8928de67d05ef6eec01d6fb51408064c8c263b14", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-/eDkW3o6JfeQmtahNwR91BG1Boqfl2BfUQI/jmnvfYRlUkrCUqsje2K7qvxGDNfMiIqtmTYc/MggHJM1w9kHVA==", "signatures": [{"sig": "MEUCIC9HNRdMaW/wKd7IlUqj3LtEMvXw9FIgn9iMkmxOpNsGAiEAwz/Gmf4ObnORxNjOjakqMzQAO5w/WJNvqVOgJiZqm0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxIFCRA9TVsSAnZWagAA4u0P/0z9FZR+5a1aS9Jie3yR\nBL6N4Z35GHFYIfLfH9JY5rT6yR6zrO3kKLeMWw+3Ixo62FearMcuo/7Cdl2h\nzdslztqBr54zbNfVmIdNrMJpS5hFuOuZgeEdLP8OLhZHS5lGIf2dXUw3PQvK\nzgOIYdkUoKX7UKDA3trRpmT+JHLjOzgxzG3/qZJzcNmGBdj1dN7XVH+dOB6g\nl6YwFZcJQgR8VJKEfV1an+yAFCK3oaVX/SdEv9DrtAHSoHwoNMe+iemvJKPL\nWsekgyMmORrYaW3ilTHUZQ7kEiaeJfAv+AZqhuRaUR6U1pA0HvTenWyES8aw\nPh9eWa/zbc9K1hWXxKU3AKzOY/dz2QiSriWgdtdxwaUNnbbev39nWGcm4QH5\nzaO7q54UVlmOK18xppuhQbh72jGwJhVYmy/SRQ8xaqy+kcQvzFrqpPr/wLDM\n/rU7ZQSMgtUDZviaL7RnkP0uhFP4BgOb4nI0fBcjGYSRUNN/QobtMZjXFBFx\nSep5BnTIhoZnJFBohi4Rv+hNWVYJjAP3GtiflQJTQhkL2ZJ40uLgjyBTBr4m\n8bKZRxkX6E1B+8Dhm286ReSymHLL0DGV4B6qAtU4s8Ax+NkSPX1ziZU7olrH\nJLE4n/S0Uo5miFXwMrwLNOxvg6rVnz5DGoDdbhSmiBAUTqXyo0r/NR5mSHeU\nhFPM\r\n=6jSk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.0-alpha.1", "jest-runtime": "^26.0.0-alpha.1", "jest-haste-map": "^26.0.0-alpha.1", "@jest/test-result": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.0-alpha.1_1588531717501_0.5404117339081624", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/test-sequencer", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-sequencer@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7cc92d963cfa89a485a4b7d4e998f9884a7e8b00", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-aym06EcMvauiEHHlkOL4g7yzLOc9j8YIQ57JX4HoyPt2G7sscG0wnG8rnV+Lteg2CcyscK8BMfr1J5ZgW+oF3Q==", "signatures": [{"sig": "MEQCIDH61f/XYKlqmzRqdbRnfdmymBmMuRVqPdpYbMn0ZY5dAiBSu0BarfZEOk+VGVXvM4bS/XQrzgccVtTVv6Alpj2Deg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1oCRA9TVsSAnZWagAAqRAP/iWWqps+igdDLHgY9wSg\nxL657/Xsu3ZNGsc/jOoBGRCLrxoHDjn+uUOeDLZJtzITYJTc9Kd+uV11Rjaw\ncxAn6EstxAEla5tY/JWW03p2ivQKKiBRXJnIICKXLCjNxLgTE5boexriLX3e\n1egc2mabd/4MrL0LTFI60d/NvdYOYn3ybxIcikSUJSQFpy9qOaPN6Dv/kOgq\nl3Q0X2OiV1dvsSvazbzZk3XyIuCmb5GsCUozNEDckvnm+71LFjIKZ+DXLsur\nVnPZoPXz+78SV6ATvFP6SRbaiIkIHItsCG6bEBmeKWvOEPwkhZZJyS/aYO2H\np7tymfc86A77Ri9lfOllQyjK29mkSCyVL38Aqtvj5opP9v0xugF07WNKwUHJ\nI59YJhZqXfNXRErNJ9BskHFXbrLpDYyxz+LTNFzK7o4UkdUEP1WPiEhf8WqJ\nt/D1LHQn91blEzTxm+/K6q3Xbnj77pbCxcqGwdwFMVBYPfQ65WIFYhytYEGy\nVPFdjrHGlHI2Pw4ytRpbnsKbetlqRnvILghBuWrxcRVxxUA4bRb0at6dTT1K\nOqFz5mVOhI0kce/W9cS2apd2ejM6wvOuJhnkgtIki2FwUROe5LnElLh5sgvC\nMk4mFP4Gla8UXd8XO6C6sKPJQ6AtnkDmmxSMGwBp04Gav7vl4UCF5aXZ/DX2\nNJkp\r\n=acEC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.0-alpha.2", "jest-runtime": "^26.0.0-alpha.2", "jest-haste-map": "^26.0.0-alpha.2", "@jest/test-result": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.0-alpha.2_1588608360181_0.7147068031296997", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/test-sequencer", "version": "26.0.0", "license": "MIT", "_id": "@jest/test-sequencer@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9133520167322b558700edfbc56d41aad1beb6ef", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-26aSNxQqYvlc+eDSzaWbMfNdo78g5khZhgeRlAzMrKtQQtcMHzbWbdk80p5lbAlKVcMzJD0ymaebt/B++2BPDA==", "signatures": [{"sig": "MEQCIEhGsALnJ2tprC60V+ifhHPRF5amhYWcHbPPCauzX6E8AiBV9Hl6J9pslyStHuZ1F1JLSA5Yh0WgyuGwh6a24vDbvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaqCRA9TVsSAnZWagAA8Y8P/0CVA8nq9SHQ4rS45/58\nboF+6BeuEI+joLSxNecbZakPem0pwfJbxSl5zzFnbI4I4fBfBU5XzzvwgiJD\niVCp98w8dkjOB4AdGcAzCn5aAh0V9quQapiLgMMGZVK3ibdUnp9yy7xQtLVP\n6+S3GlPXYeXEfL7PcBXsBdhZYVwiuBXcImoCW6hnOvlp72+BElg+iaJJZB+u\nYocF5slcJNlEjoHWlH4DQg/i7W9x1+Sc+q3XAhp6r+oBHfR7HHSm94I0Jjog\nM2vtwbUAhL7aRmRT73T06T29rYfGQ808ipRmVgESguJXrSIcRh9BFTMTGJRI\nbV1snEyh/vkRj9WKOtAqnJH6elxMeZrvQcG2nMCAtUJJW2wSEVrWKY5++miQ\nvq8jVSqsiLhyotDiFOsXds4WhPN5qyrZgiOlVYkN6Bv2O5BpXBcy31HNaemt\nU+bqAlCIE/TElZdf+/Jq/u8O94bDuL3gzN7wPhwmjF5pBdSBF4tx6lkYyYE/\ncpo2mWVjZVaEWGpfzZsRmS0oyhFoKIK/nFyIwZQN+GnOYj143zWmVzfOkjGN\nS2KkL3amM3y+O2HSdk/YlZ4XwNLGP6TA5nOq1qth9j+QSrDAcVIGahk0v+yE\nMu7SrjMlgxC437uT1TaRhz2izhmqPY0uM2jYrEkEbv1P2t2JvDqHQpTP7S8D\n/ppu\r\n=WEuU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.0", "jest-runtime": "^26.0.0", "jest-haste-map": "^26.0.0", "@jest/test-result": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.0_1588614826005_0.18695432471659834", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/test-sequencer", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/test-sequencer@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d5ea253f8f7caaf816d5a47cc26c320fb192e42", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.1-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-qO5lac34M5ZwXmYIaqAcQ7VdRn7XFxl4q/taSFeFzEaHVHMvxu/wJALi2CWmDG5h6PmNCE8LTEz+E+GVhhJS5w==", "signatures": [{"sig": "MEQCIBR/f6Kp0zGGZUHCxl0iSte/6ij4hZS3M4oohk1gjP5CAiBj4GWCMWa2zqlcaYL3o4ni3lnRrfYbVMHRjDnlzxrwug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQ4CRA9TVsSAnZWagAA+5AP/jrXXDBgZIsgg4yUVWsn\nUJ1BPbv11XgebanyScwx3W5MowJeOvRNvJ+8CsqhfSaE1RaAq92H0E+6OK4o\nP2sA9/YDPKWR62WP9CAIchSMQx9l4Y0weIPEIOVZ9vfueY5KuGj8a47bbF7v\ngobj0W5n70UNS/95RmXP0fz6yPomtckV8Ov/p8CHpBCHTQnD4hD7m8dHOQxG\nnKYTCW4gFSqiqCflR0/DkGM9eiUnbh3Bg8vl9ne6LkQuE4OKrJZ2YnHuhfRv\nhGte21fz8kteLrM+Yot3v32xbAjTWvDJQrW+NBO1W1Y7z5pLODjkDDBZoxfq\nN55OVwXSQUBIQbuUSzz1vMIDK8rRh5vmfEneGu3eOQmpUB9So65dZSlcLVg6\n6vJxzKgqnPtSxHRDFKrIPvyPcHH8J1k+PCoN6lZ5GAKgseZ5alpCUFEiFRIc\nNNBwxf6nZr8W9kKimGsg97cWzMZK1q776f/BKtSK57EsgziD9k80Jb3zUZS0\nTWwDaK23ooIJ03l3D90jLX7TNCgPaVKQnufrNnVNQNiCS2CgScrgK0tilD2t\niW99GtYd4sUmQabEHGQ3pcoC1rBUIDDkWUeSNKNnOHcaUmRvRrjeMW/Ic2G7\nDOfqxGnomVN6ONFUPqtmIIsaNCxEhBEn+PLs4Ooz1J2R0pYLHvqyVvkqZUay\noKR0\r\n=i65s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.1-alpha.0", "jest-runtime": "^26.0.1-alpha.0", "jest-haste-map": "^26.0.1-alpha.0", "@jest/test-result": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.1-alpha.0_1588630584252_0.1144741443895283", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/test-sequencer", "version": "26.0.1", "license": "MIT", "_id": "@jest/test-sequencer@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b0563424728f3fe9e75d1442b9ae4c11da73f090", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.0.1.tgz", "fileCount": 4, "integrity": "sha512-ssga8XlwfP8YjbDcmVhwNlrmblddMfgUeAkWIXts1V22equp2GMIHxm7cyeD5Q/B0ZgKPK/tngt45sH99yLLGg==", "signatures": [{"sig": "MEUCIAN4brvamhvqBaQSiXEby/1rEhl7b24JGGWCrL9oK5lDAiEAtm+lRRUhhK34pqXdezts3AffROQ77iNmN9EnLXZXCpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesULcCRA9TVsSAnZWagAAUn8QAJxJLoh6qfNH+552h6m/\nOzheZtgi+iIU1sKrwccCKHugp/mP8ac5j1r7o9v637fEYm7Ay1SySQlnvvGz\n5/VozDyy5bTQwTFL5utpzXT3qivuyR/vJsZH2HJ0sizAdjILWE3MJ6WPn2kR\nmNE1m7W9anVpiroFRrTc+upWp57ahMseVqitWnpzFHlijzYqYcbCsC/AtdXA\niU9zlsxRMtgxh89bMpIq+DNU9u7y2ZXztGzWEZqnGIN1/2Lrb1X1g09DQEnm\nBU38u3gDDkfrof7s2Niw376wuxDyQZhZqBCZvPdMVE0vNGy4iaqHWTLpdSsl\naKKwKq6/S0nHSLrrpokIZFVwhtPWP94UxX7Oji0tLKEeaIhZi+BVYuSKlQld\nZdwI+bgKUMkUm3raayq/QZ/lLgjITkrgc1kNc0y2DNHxeCShp7a9zhsS3C5r\n2AEn/waH/oRU3JuWQXDcST2L/FIRXXUbhanarRG/MVcmAMMWjM2TlE1eo41P\ne9blZVf91jb6uWe5hW7pOphIq2tBfVppFUt8QrxAJMNR5++5XlcuybHofZMe\nQg3xnife5qA6ceV+4sq3CJEaahi1bj3DJTvUapthms8jRE9UryR+xBZlANZ5\nRpLLNZolP/fV4Ui5I0QdhVWtAz49WRJRJkbrJaSom84ZgWomJfnoSbK6Gp+s\n7zHN\r\n=i0OT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.0.1", "jest-runtime": "^26.0.1", "jest-haste-map": "^26.0.1", "@jest/test-result": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.0.1_1588675292114_0.07259686290616241", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/test-sequencer", "version": "26.1.0", "license": "MIT", "_id": "@jest/test-sequencer@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41a6fc8b850c3f33f48288ea9ea517c047e7f14e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.1.0.tgz", "fileCount": 4, "integrity": "sha512-Z/hcK+rTq56E6sBwMoQhSRDVjqrGtj1y14e2bIgcowARaIE1SgOanwx6gvY4Q9gTKMoZQXbXvptji+q5GYxa6Q==", "signatures": [{"sig": "MEYCIQCt4MQI9S/kOXP4CGUG+vJC52gLhldETkrYlK+QuedrPgIhAPpiGJJq1DQ2gTgJkGNAj08j9oq0Gb4N2KwprrPAec6i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hymCRA9TVsSAnZWagAAhW8QAJz8Jzu9mV8S/1marCiq\nW3q6ZPO25UOoXx8A2TS9D7B0Y7oHo/8vvYLbRMouvsVqjmb6by/8JXOxxdeC\n6kZT7Ay+JTbyNkViK/h8uhzX4w1f1Wy3AODcNtWV+9WupwUdTrv2hD8j5ny7\n2S5fw7HeKQ9+0z7LwH7hbfrpGdcPtyaq1Gd0RP4RZ9c5zkQQN7z0UOwW6NBR\nRgGCOFRmKHpSrsE3n+4T6vgNPdBE7MAajs0S6IshLExzdKllVIwc7c0M7MfK\n9pjfG7zAlSndCPu0TyPVOu6bIJ+iliWMu7ecuO1mqjuF42FOlolu9+i2YabT\n9U1YZp67h45/HAvJiROLWbMotJNACLPNhvV0zA9s1BVKAZ2JuH8yqifPJRyB\ndSY/hvic1zIgFYzHLe7fENdg3FieLVRZqsNDOeurzunGKGdQXyJerCLZHdrL\nGxytYgz3ml1kza+VV2VKEuK6PDqnQmLcPhUoiyNhQH86ukVnqoGzgFJ0UVrD\nPC9i6hfMmGpet6LR/RjzHs/vsmcY/p7ZBi8438BKzU2bv6ugFteKPu9/hJMo\nUIcCs0fO+n5GQHsZiew7iOGFbH+Kb7oQRdi4a4KjIIepYu++8dg1R9cAQVN4\nzC9dFsVK1SYeyXPJutizNC0v7mol1G/sXbygWfm0nl0K2AuccrxOHUnPC/37\nd3uK\r\n=MnWN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.1.0", "jest-runtime": "^26.1.0", "jest-haste-map": "^26.1.0", "@jest/test-result": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.1.0_1592925350229_0.04260693821530004", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/test-sequencer", "version": "26.2.0", "license": "MIT", "_id": "@jest/test-sequencer@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9aab49ca6b7c05efc47da378a5fc3d896cd2bf8d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.2.0.tgz", "fileCount": 4, "integrity": "sha512-xix2i2B9glWgUliCnm8HApVDw8FWs/WlH4OnGvtIoxrcZLUl4mY9U2NxcGf8NDUC0LMeqW/AtIvtgX2JRRbXUQ==", "signatures": [{"sig": "MEYCIQDowX1yY/hNshx1fHBa1YbPiT+7owrbJ4/wOkzEJ5zV7wIhAIbo9qnw1qwNAEMbhApaWs4FxHyFtFr5+/i990LU30Wk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9556, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpz9CRA9TVsSAnZWagAAT3sP/jvler4taPS7IinwDtMY\nsTFZTlgxXpNC+9H/78iqmFAYELmyXdGchCnZd+MPssvtR5+C0u2wWO6TCQac\n5Ske3JZUvkm/tFoWvAA/mTnOHGWnn7kizY257ymqSK5I3R14Hq1cYb9AfxHq\n/6i7L4RwabIWmPAurIzaInUWd/DYuQJj/0p5HLdL2Kun+GIzbXAys1r7z/O2\nyZFEYppi05murLem3qmRmk9G1DMs3/KfVFAnlDjIUKZQjLVAzNf3TGeCeY7n\ncvN0+23+Dp4j3HxA4e8kUDtOTO7V0+uxNF1kRrH+jv2ok3OFKCyTfmeoTXSb\n5gmTnwJ1FidpeTKf0NKX3rF1P5Eu49a9kGnoPrH3GzfTV01Xu5/arOB4qFg6\nGqtvVDs8By+MWrkulL8Jmd8xZQ5LEyINuDIQzT23+G2HMGuOXt9Bj/0MnuAa\neyPzEzwkgorv/oK6PG+kZmL5JJ2eUXYBHmNlu1KOpNPjp7r+EN6XJYviRis8\neOpbVXHA04loS2l745kbEHd/arAFB5qzmWVyCjHXg4rN14RM89smIVjdaI5s\nujNmrSk7EFxAxI7Op36wPQP0p8sSIrMOiAD71ef9SCV5sCB1im9CxMzv+K8A\nyi3nm1/q84lmdygBwGIqlpCDYAhqfRlGiS4AssVX1wfucDeHuxGFATwLzSQg\nIum0\r\n=/sF4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.2.0", "jest-runtime": "^26.2.0", "jest-haste-map": "^26.2.0", "@jest/test-result": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.2.0_1596103932911_0.22783922220194608", "host": "s3://npm-registry-packages"}}, "26.2.1": {"name": "@jest/test-sequencer", "version": "26.2.1", "license": "MIT", "_id": "@jest/test-sequencer@26.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7d91e55f884794c1bc4d7136cda828ac9ab6ff8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.2.1.tgz", "fileCount": 4, "integrity": "sha512-H1sl/efQeJC2/agSJWEos2Vi1F1lkVfbO2WYtx7YAEfWD5vOz1jOnBd6AY1ER9nlT1Mh7r5MAKlUx1CdsbS7Pg==", "signatures": [{"sig": "MEUCIEZlGT3T9wEkROsvjzq+v2sdbxw09a1wfppOg7fnA7g4AiEAkIjEzhz3Y7QTwOq02GzjSdQF7bJvuk0cLC2HZVLN11Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIrCWCRA9TVsSAnZWagAA4jgQAImFguKpz8EYM6yxQiQ5\nv/5HT2LiHp9oaEzDjrXTRjMmkUGJw+6q2amf4rKtZ/mU4Me+i4jUV4kMsAwD\nHfhaCLHxksoJB1196Ey7CC1UQM6V2noIABIRXNQheQrRHQ05z9EpCCKiUWl5\nj28kjvCmE0kVvggFzOcMKw/SgJgXmndHRm22XXOLv1fiCLHrGKnQ2PeEEpVm\nQr0DDgX7IOE54pQiZl+8vo3sa8wMZf9fcfHMBCPbItV+xRI7fu34qnoJo9PI\nPsjHgJEVOKSgkbbrGVzQ1YrLW4sKdbFNgSV+AFjvoCncQsgEH5GeBDTDycSr\naBMPnUova4pLPHzY6ycF+O2mH1hdkbQAuqQj0g1LtbEayTxJI1GjaXG0Pz7l\nSehVKwiNjVR7JsznoNSyENeRzvNhM8qGeCCQQxyPeVPsObecQs3ECK3+F7mS\n2LvCGdypgkV5MrtyLLAGIPy/oGOOtPipV1cq3gk5glU0da6itGayPZlhiVqi\ndqNBw3NfkLoV/Wi6/SvwZhiuNzenFaeL5i4cDGrdxTIA0cCX1Vz7g5XLNN/O\nE8opjf/LuaYTimjTn+ECo+Q4sbsBdECS42lMJtVBvNgcx39fUXWayv8tGvH4\nOAtU3gwq8q+u0QzSlLuitSwzbQRAzXjG2C9Mt/W1ouzmI+CawFNza1rZ9vY1\n5Cuf\r\n=O7d5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "da61421faee6fdbf2a2b355b10d4e6eb1a842233", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.2.1", "jest-runtime": "^26.2.1", "jest-haste-map": "^26.2.1", "@jest/test-result": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.2.1_1596108949620_0.5570376179669188", "host": "s3://npm-registry-packages"}}, "26.2.2": {"name": "@jest/test-sequencer", "version": "26.2.2", "license": "MIT", "_id": "@jest/test-sequencer@26.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5e8091f2e6c61fdf242af566cb820a4eadc6c4af", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.2.2.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>liZWon5LNqV/lVXkeowSU6L8++FGOu3f43T01L1Gv6wnFDP00ER0utV9jyK9dVNdXqfMNCN66sfcyar/o7BNw==", "signatures": [{"sig": "MEUCIA/NV112nWUUa/B7w/OvvEaraPfRowZZlxkJEwWBB3AGAiEAjnLgtc6Do0zWP9+18RrPF/lASedWBjDBLyC0Fwl67Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfI/hMCRA9TVsSAnZWagAAnxsQAJ9qd3mBT0wLQEcZwvnT\nhSWNk8uDVlMjR7M/7u9Pnh5tu3V+4FPcle3rb0TJALJM6YRv2pBzX+UyFAjc\nSyleO7r3grIHychCxYb5YmvQ72B8hGep48t9vh9SqT2HgNv+xFr0hCdiIZ2R\nXsrayTgDknCMS+ET3qxJOBrgkhu0MSKLd0MrTYOsjByOPThhDfIn/lEUkAdg\nosBMV+55VTsrYpvHcga36zaOOO7O3Bf/yfaugdl43fURC33etE5NF6j6KWVG\ncIMvF8Gx7dCTEfOW1vGgcMaiOlG2h2DGshu5PWxDerSshIhWFYuwW0/ndG3A\nssZeWOit6FuVans3UDFrGCIFDK8FrxCyYut0L6D5sE79rLxC5KYA+d4yNc/v\nhuSownWyubELdccb5cA7kqLCD14QiMU1KNEmMAFUlaA+wj41teAbH1DOl54D\nlLWu/LsDr/MXtT6yAetPx9Jj4Gy8xe+2S3cO28cbprFMqGj/pxh/NEBASVgE\nq/UQWY+n6y4PF1La7ZQMdecBpYMHN3MxlnQNh47EBKIyqv5SSSfXE//OiORl\nZGuK5gSKzn6Vgm23qjTpwPt0TXlNFDZLxCibYMJMWUZbluWFZwAnQ04ukAeK\n7r9aKN0QCJ0hX450BWxiMsTv16pZSovgW8sFtueMwUMtiX+42YFUZOACWRuO\nCnx1\r\n=1+b4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "a6a5abb7ef47ed2c31bf8987771a79f97ae65430", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.2.2", "jest-runtime": "^26.2.2", "jest-haste-map": "^26.2.2", "@jest/test-result": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.2.2_1596192843985_0.9013483987384363", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/test-sequencer", "version": "26.3.0", "license": "MIT", "_id": "@jest/test-sequencer@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f22b4927f8eef391ebaba6205d6aba328af9fda9", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.3.0.tgz", "fileCount": 4, "integrity": "sha512-G7TA0Z85uj5l1m9UKZ/nXbArn0y+MeLKbojNLDHgjb1PpNNFDAOO6FJhk9We34m/hadcciMcJFnxV94dV2TX+w==", "signatures": [{"sig": "MEYCIQD2iSzkBSISWAFT03VM4GHoMBnDHUkkU0Jt1b5+F7w8+AIhAJdWNJdbM7WNoFOWosBnqezIOh0F8ze/Cl4WyflxoE06", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTBECRA9TVsSAnZWagAAaTgQAIpXSMvtKpzFdHQJJp/z\nCW0nwdeYFTmoR/8UtVov1XnpJS68B846hBIQztIa4QCtwMWwvx/R6jOJ2D4P\nozdew0x8YXB4rKFsn+YhSrtk1vysqGjT1oSxP/vCtw5FQomuv5lPVhMFg1VO\nzW+zFfhzTfsuoMsK2EeWIHDBD39BklCyRxSJIEKJ3v8wElKUMX9coOaKwApo\nKBO1lUdzj3GzLJyLpk675RAkx1axWi0R6Ic4A8QLc26gFmt1AgbSEjXHFSS+\ngB8HejF1NqPVAtVE9azKr982flSG8pfv7mKgmPufo2gJ/IJKxUdJUuE0MYWO\nQ1NVM55IQGQ9mndg1dzVwhz4Rzfcj5xZDyRxKAl4u9y/AvCx4FNCoCgv4sjT\nNh1PlrozNBixtFsR34ZNiTNCBxt1XFnLkjTTDe6CSuuJvcjB/zIYy3iGZgwV\nErHcvJI4bD8tn9LqDLGqGtS2boVdBpmE49tVk9bZV6LGq5aP4Nl+ON6UIscU\nqQWIaUpwlDrm7ELDLVnZeT78OuwI0sAQyQ1BhFVK9JuKKc0n69ScWQRXGO1r\neVCqAHa0TPt86X+hfc+PBaNLGAqUTLglt+pNavjFcYHO1mzk21QF/qtp94h2\n/PH9Essa5+2GFtsyT3ViqeOLqjWsmdEqlL3LI8mZarojcdB1745uiOiVb66b\nZIKU\r\n=6bpw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.3.0", "jest-runtime": "^26.3.0", "jest-haste-map": "^26.3.0", "@jest/test-result": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.3.0_1597059140298_0.04161816625251902", "host": "s3://npm-registry-packages"}}, "26.4.0": {"name": "@jest/test-sequencer", "version": "26.4.0", "license": "MIT", "_id": "@jest/test-sequencer@26.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f4902772392d478d310dd6fd3b6818fb4bcc4c82", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.4.0.tgz", "fileCount": 4, "integrity": "sha512-9Z7lCShS7vERp+DRwIVNH/6sHMWwJK1DPnGCpGeVLGJJWJ4Y08sQI3vIKdmKHu2KmwlUBpRM+BFf7NlVUkl5XA==", "signatures": [{"sig": "MEUCIQCzPJD/fGGnUDgE1rnBniTS8ZsA+PCkhzte3ybHx27AYgIgc1g6oxG8L0GgYTlyT6m0gAs5i2Mg7KSFIiq9IkK1VCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNFiACRA9TVsSAnZWagAADs0QAIZL5R3yrknnABHWBufM\neShrCbdMv6wAN0h1FQsgOImf1e5n+jXtJ6kw/lkzsSekDW/2fLlMvrus/I14\nxaiICQaojMplmDJ9+Rfm0y91LxvQ77dpAQoAIzpfItAmSPkt1P3F8AZb70eG\nzLkW9HOEog0yF2+ltN7lSZWD55+I1OWKNtfRuwARbrd8Foap6StkgdP61EqR\nGSXr0BsNoPFWZOfr3nMJIUdbYxDV0cxi5pip4UOQ+pYjgfAR8AgQtwci/8Zp\nW/yStgv+x5pEtU0mJkTcrh+aM6dgvoH7a1Mb3u57vj+UVuqwXhOGJ7bJZaDu\nUWdp/SeNsZUHq7Amp1a1GLmDIM3VUYVVeBL56wG5oyB8b3LAWE9Oww3mcW3f\n1AM3DHmvcBY+6ZKZ21C326yh6sSQKf7WLD6jCJyVNSG9Okv34t6MFoL8z9gL\nltfjkTCuaKJiXb4AekUl90TrgrM5JKh41xmxCH8ueaN1gi5fTaSVaAN7OiJ6\n4c41PNxsh5iwxvJGAibqXKBoH54eyWvdpF0XEVsgBdgntkYlNyiDDOH5F5ze\nQCy+EHIRe8Xe3olWcBhNOq8nebJJShhVw4iqfsd73BH7K4sFNNEWhyEvzmH3\nemwJxnmmqlG+M9nNsthVqrT9REmpsA8SlewMgVcE7hkpzDwYXlZ1khQDIYQF\n4fnK\r\n=X8Ds\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "0b1e41d1d93ce4d15646f4a39fd5a7ffae5f43c3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.4.0", "jest-runtime": "^26.4.0", "jest-haste-map": "^26.3.0", "@jest/test-result": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.4.0_1597266048386_0.24699684380352394", "host": "s3://npm-registry-packages"}}, "26.4.1": {"name": "@jest/test-sequencer", "version": "26.4.1", "license": "MIT", "_id": "@jest/test-sequencer@26.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7cd13fedf4c1c20364bd40c134876b003f742e1", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.4.1.tgz", "fileCount": 4, "integrity": "sha512-YR4PNPu1RVHxyv/HSQMjc+pBEWa6wuM7xbEX/u5M5FFg6ZM6m00m7Jf0fjRxGN6hZlY5vECmNhJu/kvJLrxR8w==", "signatures": [{"sig": "MEUCIA9YlLEnPD0qEJVAbrWFERea1wKePzAaIS4N0D15yvrzAiEAx9egfm302b09xi9PnivqNDIpto9QlT/PxkecdKHn7mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPjT0CRA9TVsSAnZWagAA/VYP/00w+LRV8KS+Vm4PWV8m\ny1mn8V6s2isnN9kBnoseu81DFl6ooedQDHDbNd1EjqCSI5IUYpNlGIJr9Izf\nIhDeCxFc32eZiuwEBR+k1GKX+Inj8KQlJIyEMmsQoF452Jjipf0BS1CU1MmP\nwSLTdFmHa7sF49XODOaRU6RxsOTXmheJZkPXJcnDNahYXuoPNZtOX+8Aiixc\np19fZp3T50ilqfie6AXDDBbcasK6DIhiaMx9U0c3tiT2HYYz6XogPmdyMrWH\naglTyM59LCBYFTZOBp45rKfgr24oX6ZggTgL62NxYR47/Ayvp1jzmcEEgv0r\nsdBgRJe2c0mqcsSnMYJtTUiExtHr9NdQipJQpG7uLrohzCNLSlwCNtsbhFvv\nDLJHmc4kbrPLqvfgWWBHGjmlt246SxwH/ozhDKb2IAFl7bYv+Cnpu7PL7s8r\nVGxGI8UKdqzQ9ZCyxXBWNn5n6H1jCmdu9cd76BNAgP3FMMQB/9mw7yBjQuI7\n23nwvs8crqN9dEQt+EClylBGuJ4tNsql0UAA5EKZLIAXOSfneFQObAke+52e\nXH7XPmiFk0wRwZnEfwaIqTw2Eb3S0L48G6rRRNRlqKHu40zsBwKf6bmg7nHp\nfBEIjDsnnaVCJUmw0PRumqmR6WTZ6OdbPdGJkp72hzQqmv5tVYGKMSdxPG2k\ncYDy\r\n=f5LH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "e1d51deea8d089a06f28b4dbe9287a4428508610", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.4.1", "jest-runtime": "^26.4.1", "jest-haste-map": "^26.3.0", "@jest/test-result": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.4.1_1597912307678_0.0315464921120554", "host": "s3://npm-registry-packages"}}, "26.4.2": {"name": "@jest/test-sequencer", "version": "26.4.2", "license": "MIT", "_id": "@jest/test-sequencer@26.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "58a3760a61eec758a2ce6080201424580d97cbba", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.4.2.tgz", "fileCount": 4, "integrity": "sha512-83DRD8N3M0tOhz9h0bn6Kl6dSp+US6DazuVF8J9m21WAp5x7CqSMaNycMP0aemC/SH/pDQQddbsfHRTBXVUgog==", "signatures": [{"sig": "MEUCIF+G5W5GH7MeAEg0Df2sVypy+aIhtGVWWz7uawQdn+EQAiEAv7rF60hhihIp931GIWecCvgH7ZIjUy11TGwoxEHy9vU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQQsrCRA9TVsSAnZWagAAUXsP/0+8XWY2R+SGEwxLj2TA\nmcIvUovuLKy76ayfjXu9BQQb5rYSLCCQBWEl4zg99X6PeQTXakm4CYmSn48q\ng7snpeAs/yh++HiLE2upAKFodzlLqLa7c3dayLFdvEAevr84xeqLECkHQZIi\nAWPkHFsHtP7jVA93tdx+GHEdyzBaxgjN4pSTobO7rzukgbkoAYuN/YwYnC/x\nrK9scDHDnp6t4OCEfdC4u5TSxuaoK3fPW0MeNwb2tHM976l4c7JPvPnaWe/8\nS/6qvZ7YJRGj5ICc1/2ZlBLQQ6Jl+9Jn1sq/n3O4aLvYoLTyIwSeJpm5WO+w\nzUGW9xc54tpSaU2zoDlGQS5je1uWZZWLTdVUqE3GYvQH3nUIZZVNjrKnVN+8\niieXjNvct9hsbrHEEPMGnpfrUz4ppv7Dy42cjssX95wr9KwydYEB9zkIIP2N\nbhhTnwrPOWMrclk7KzchBfVFSMHQPuyOA8Yg7npa/QC28UCaNwPZvkyLaz7D\niEmPrkorLCG0f+ft6voyWR72RUSsw1WkjDR1NgGrb0nMp0C5DaUeON0Wo6+X\n4I6IIMBm1WmT4Oi0XCn9viw+EX8DcNM/mVRiVeErBbL3zWIV3/iR5PR22SeN\nOl8kMWPnWtiZ4fxtawJ33gicKOJ++NTgEuONHc9+IDlmpgfBWtFID1nvcuu2\n2NZj\r\n=e403\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2586a798260886c28b6d28256cdfe354e039d5d1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.4.2", "jest-runtime": "^26.4.2", "jest-haste-map": "^26.3.0", "@jest/test-result": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.4.2_1598098219052_0.9515143839089115", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/test-sequencer", "version": "26.5.0", "license": "MIT", "_id": "@jest/test-sequencer@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "617808a1fa869c5181e43a5b841db084746b667e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.5.0.tgz", "fileCount": 4, "integrity": "sha512-23oofRXqPEy37HyHWIYf7lzzOqtGBkai5erZiL6RgxlyXE7a0lCihf6b5DfAvcD3yUtbXmh3EzpjJDVH57zQrg==", "signatures": [{"sig": "MEQCICNDrtAkKXYknjdEk48Q4MSqQzvcWdoRjv/dE2+1Z/nNAiANUmi4cR8gsJJuRz1aZX3BOBzYTKF/5WaWf+tE9S3XaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeudJCRA9TVsSAnZWagAA+ZIQAIrE6Yrp6LQfS4Z6ZYyT\nfU/V2BpycvI47s8FHKqY0GYBi0fI4HOlIi+cd6BHzbOJXPaNM4EDo5nHrt+9\nq7cKuykXFA3NoLPxWKY1xqRrD5M66/EQ8mWd1SKNU/xxrQ4fSJevBN2iEBBA\njqPVe/ZhjT/YLR/0fhI56jZPFt11MAGaih2oPP2mqD+5b9HLIoDyx5FKfKBk\ns4BDh++5AJnhEpPjUZERzjq7ALDF/1dnDfp1Tt90zhuW8aUqt/aXnNan7K20\nqdhisSSBo08Ld6bAXBuWPZUv3t3AiIPPZ1i1syt7h2Qzcpsq/tK55pJUcWc2\nJMoECaO6GkYeMNU2GYDXTtsqeih0wrjTpu6YCv9+V4l3YSSwe3sTUl2NvSSp\nq5qp4MLLsIUz9MbZppuNaqzHxlG+GRCA3W8XIUpPx891y9V9kyW9IaHL7ril\nxL9sM0LN3qrV4rl7Tbopa1D8w+KGCD+Fkd82Ku4BVnQyLKTn7Vc/iQuNgNMW\n8G9gRAJvlBof4X2vulbPuJw9ES58UQaxli5brT58OQ/ASu5WsSOMbA1TeGQ/\n+/lsqgeraU+ch6eWM/gV/ZsAC1Er+1rx8JzDVp1SXnjHzFu5MQrleMKkB2Lr\nEQHOxdll/WqTdqB7wzpKpUAqOWrzE9EmsvWUcWyQZF5qbMjoJSWdzDEEKnwM\nGlNj\r\n=lrhv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.5.0", "jest-runtime": "^26.5.0", "jest-haste-map": "^26.5.0", "@jest/test-result": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.5.0_1601890121124_0.2315347701233741", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/test-sequencer", "version": "26.5.2", "license": "MIT", "_id": "@jest/test-sequencer@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c4559c7e134b27b020317303ee5399bf62917a4b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.5.2.tgz", "fileCount": 4, "integrity": "sha512-XmGEh7hh07H2B8mHLFCIgr7gA5Y6Hw1ZATIsbz2fOhpnQ5AnQtZk0gmP0Q5/+mVB2xygO64tVFQxOajzoptkNA==", "signatures": [{"sig": "MEUCIBY3TgwjkkAeE5uH1I9w0wkTkqbwNw+VmmMM3y5R+XXCAiEA/7gNsMvLeoD14jxLfWut576QF9h1SJtrBbZCRSxlhaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEygCRA9TVsSAnZWagAAANwP/1jzhFwzQ1F26AmWsNzC\ncMVm6hHA0v5xaxDYHCrDqOiZ7LR1Zb9ZXJrzfr324FS7FLGqMqzrCJRmgrdh\nRO+bH0FsyknGhio8jwGGfoUpnydRppFEkUyEPx9eCC1GfyGxYbSL9SrvVxyr\nq9TCI/jUEGFm1WZMO6E1s6gs8dV/QBQdkTHu9JC/b7gW8FEBLJFadzR+kQzN\nd6ceCNahHBIz8boE7qlLXkans1hRmQgs7VZL0bmXusxrhI79AvaU+DQLhEFF\nn3He0MeOrDnNd+H3OgjGGWKIaf6UdgqWfmYF2XqnYt7d1BWwFYz1PUWaaRGJ\nteu86oPVbvEmCs7+fZb1Qz6y4jGigZTKOy7pNxlydAPQzvfPRXx+PiKZiPMP\n9V3Bc3gPxRrC0fqJs2VCuQv/xQ9YVDc7O0QRn8fu+ZPVwuwj1wSSt9LI/2Ej\niP80HBzgmOmJOfga/mH20q0sb43KBrGaSbzTbkZAtdQnvS+8Bwl5ZJrPuwR7\nTOc6h7UUaQxjOd6HuwN7UYFVNgnpyCB1G2YEHx8ddRbfnFfBRYTs1LCWAOy0\nYXCxJCABh7UlyTo5Rmm6b46HcJjqr8GHD+p2kvldSswOHRbXvTz43OUtlBH1\nP6CtG8nxS2HDdsWGpwF+Q91zU1DdJTXP9I76N045369sOBWTxKgHzwOy6CRU\npR5i\r\n=WDQr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.5.2", "jest-runtime": "^26.5.2", "jest-haste-map": "^26.5.2", "@jest/test-result": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.5.2_1601981600525_0.5861454593552451", "host": "s3://npm-registry-packages"}}, "26.5.3": {"name": "@jest/test-sequencer", "version": "26.5.3", "license": "MIT", "_id": "@jest/test-sequencer@26.5.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9ae0ab9bc37d5171b28424029192e50229814f8d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.5.3.tgz", "fileCount": 4, "integrity": "sha512-Wqzb7aQ13L3T47xHdpUqYMOpiqz6Dx2QDDghp5AV/eUDXR7JieY+E1s233TQlNyl+PqtqgjVokmyjzX/HA51BA==", "signatures": [{"sig": "MEUCIQDJMcVzzPRYLWUgy6Ix3d85EViTGbmuAl9GYEr4jU6+dAIgeYJYKBA9AYuRZn3RAYvqcm6UuNzYaN08a3xocq1OSC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfg0WrCRA9TVsSAnZWagAA630P/A6Tp7ksrMm8n9Vd/FpG\nr41Ml4qq/mc0MFrGa9yAkEYEdWBjxzmpcoLa30Xa6w+d+S8JaJCPX3mxsmcW\n6FYVElNvGwiACO/6CzFy5y52PiHeVXFaC3yC2jd/5moMxJR0CJRDgVbB6iy0\nrj1DnArGcQ9SrJw7uFB4LwK001DaRRRAQ43okarLDS6CDE4MRkUKXNWnkk+O\nYb2/ylp35SSqV2wD/5nII0KeREQmdIvyAK5jXLnYxm0RPSv+D8eGBRsqwGWx\nuM7bLTWkt3pLcDCik2x8GV9jn/kUoilC40Rqv/xtU1yLuNJFRsNld2MSBjbi\n3A6hdbmgKFCuy0tHEE9TgWHRmrCwa/U/2n4qlnJkKJBfe9KFD/OdG1u8pdox\nGX6x325kxrwZ4TP85s7fau/aHPdEr9xewccPMh5Lgr/Uq34TPDWFFmF+y5yp\npYBExXgxQ5erDnJ3qXy5HjHHCpDOCTiCui7cRs/xEy+W9WD4JKikxU+JYcgP\nTYTcoNDzSeZS3jy+XCnWWXjs5FcOzYeS9jNhUD4Q2r0IshOgHxoWdkRqyjMI\nBgfWq4TqtAiJ4k/PswVGae8x9GFgmKx/dS3jSRnpy6EjvvPP6VF4f2zcxjPv\nncaiQ2Ay+nl/Z6SqEHlPDefsh6hmK8z4wwuS8XIzJGzOk6Rrn0uVm5VNdVFs\naSnA\r\n=2cCI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "71152afbbda76fd09ddb2527b54c365d753f42aa", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.5.3", "jest-runtime": "^26.5.3", "jest-haste-map": "^26.5.2", "@jest/test-result": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.5.3_1602438570951_0.4925643848780812", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/test-sequencer", "version": "26.6.0", "license": "MIT", "_id": "@jest/test-sequencer@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a9dbc6545b1c59e7f375b05466e172126609906d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.0.tgz", "fileCount": 4, "integrity": "sha512-rWPTMa+8rejvePZnJmnKkmKWh0qILFDPpN0qbSif+KNGvFxqqDGafMo4P2Y8+I9XWrZQBeXL9IxPL4ZzDgRlbw==", "signatures": [{"sig": "MEUCIQCP/myYCHUPb/eYMYSnE9W0pHu7lOhVTYKhQEGuhDDQPgIgef9hG0fqmYqxCAE/xABZ2QzrFnbiSfjX9IUtoGn67Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX+QCRA9TVsSAnZWagAA46AP/11nTF/gTcfReawQfZZL\nCMxp2iL1WXlusjyC/HPo1tqmFkrbqGEhGlfngrl/ZoRJjXfIabFhcq9JzFCE\nMAow/e6gg/i6pOkZlLNcqzw5OqkLCXY5tyK6m7pm4vo9teDoy4eRTckiReGn\nBB4FuoAtoD7Uh1I4/noUFTJsjS42VVG7P58HwFKXLhVTYmKKlhvDU+wRoflz\nFfCcE9dBLgPQObhqmfWCiMX1TU/AfmbS3M78ce2tYH5+tKFvoXHhAmjzK8XU\n18z8kXOelhpJFlHeU5zMQf2o/OKIMzmh4yIJnlimAl4iRGQJ8d2OYZCHpv8I\ncma227xLPaRwc2ji3anrtCw1YsHsJ3r2mmfItcZn9HGuopkWdJ/Tr2AzX+Qa\ntzD4hEdZQ58HZxrehcQcYEBC9r+jrHqO+Ri2wIKoLcdWjXlA8Pqn8RGxSOUT\nYablXe9WWTh69Z0HAgQthZQVTe1HL1jSo8euvfAGAUftYGU0WfypbnMJ10T2\ncdOXse/ZdCMNtKpZwbnNcjb9fMcXOG+q+Oc6FLvJ77si8dMu0K+Nu8hCITb3\nnQz7NpanXzIiSW1hHPqqaMJeCflScqe/lnQChSRwrxhG+Sx/gr5aLQNMGsKm\nImqtWwtGLTKceI1AnJvN4WzUf/WHzlStcPJeINCblFfsWZLTXzuBKVcY0Ut1\nWZoG\r\n=BUii\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.6.0", "jest-runtime": "^26.6.0", "jest-haste-map": "^26.6.0", "@jest/test-result": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.6.0_1603108751971_0.203370916097233", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/test-sequencer", "version": "26.6.1", "license": "MIT", "_id": "@jest/test-sequencer@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34216ac2c194b0eeebde30d25424d1134703fd2e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.1.tgz", "fileCount": 4, "integrity": "sha512-0csqA/XApZiNeTIPYh6koIDCACSoR6hi29T61tKJMtCZdEC+tF3PoNt7MS0oK/zKC6daBgCbqXxia5ztr/NyCQ==", "signatures": [{"sig": "MEQCIEoSbWS6hBT3KEQ3oWWP7s4U7/3t+WQvIk7YJBkoPJGwAiBt3fUnrLdZqaEn0xuq0cXCtfGBr0V4jZDCDP5HzH10wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp07CRA9TVsSAnZWagAAZdcP/A5g5xT6eXl2Jtb9sUE5\nD0CxioVD5lpdONwdtVORlGT5KAVMx1YUpIrsMh7c0fCXcX6D2mJgKa/vWOVo\nmgUSCRF/ROxC4fqR2l6V2Mls80STx84hcrhusyW9EFt5TxhBBY3IJk4DfbmV\ncuXeF6e9pNsrpTsgzUcPhJN4AoFIEqqCfPZkN3PgUCBn+c841313EnaHBluy\nNqr0p8JWINzmX3ihXesbeh406yuqbij5rhL5g4RRD5y2y47c1c1OyAWIPzcC\nl3aqdk1Yj9KB+Ic7U/ROL5feplOO5KBBI+aOuXnjuVQPlux05tAJ449Ice2O\naVGiKQDIXwfQJChqsHQwN+EU0pXjtQ22cdS6lQZaXlFhhS+pEUN9ypED1pXs\n20Ihl+yNgk9RnDOHRHAdANrxNQyb9fJYoM4vU8T4AYSxFicHKcqPriZEYL+5\nZyqohqAkMGFv/XdRhSfI3JM0hpKduahWj8jStYRmkXub5mJ7TPpAJ0k+Q+xb\nHPJRceo2hZkhF2WLi2PjTDGMz3RqRAvJnstyBOfE2U3yavTZqvsae85oIrpx\nOGKp5DEvTn+6eSIZsy0m899s1RVOYNq3Ms4p8PWO0SL8r3wqWby/ANVXug3R\nBJCt+K6LyOJfVSD2d0c/uFHRcGbJ+FzaWfLd8gOwWIA3J7wxiyX9ZXlLEiQt\nhFh8\r\n=qYth\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.6.1", "jest-runtime": "^26.6.1", "jest-haste-map": "^26.6.1", "@jest/test-result": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.6.1_1603444026705_0.057964491546757424", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/test-sequencer", "version": "26.6.2", "license": "MIT", "_id": "@jest/test-sequencer@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f9a705d0368f61a820bd9a281c8ce83a1facaf3", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.2.tgz", "fileCount": 4, "integrity": "sha512-iHiEXLMP69Ohe6kFMOVz6geADRxwK+OkLGg0VIGfZrUdkJGiCpghkMb2946FLh7jvzOwwZGyQoMi+kaHiOdM5g==", "signatures": [{"sig": "MEUCIQDAO+WATexFAdhduo/JpSGkRslbEyHCzBV2BE5/X+viwQIgGCsIhlI+2h15gVx7E6CIMzPs1+6Xi6R3d/S+fWFpMG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADwCRA9TVsSAnZWagAArFAP/jIxz0STDhbxDtMZrF5U\nrjaPEIuMpaPWyVm2EaVjguqazujbqt6gWp3dtsesnIPstJkIHQd1K7xTcJBp\nbj0ncHf37HJxs+sCfhhZSuGmV5idivDkojIyIbroUqMeKUBkxTUyYk8IdM2E\n1vuAkROUO+ubcdtTJPBSGGIJHbq9YfJewIUEKSODYw1zDV3ZdNmKuCaeV5HG\nTe5x23aRM9y8GW7QMH8+LqzBGFN/eR9s6X5yil3j2IjV3asULgepH/oWzZw9\nYvVriG+u2CDNaUkhME20wP+AQFZKH/dtz8F2Zw5CnbZ/9Qns/jaM8oxmC1wF\nZQste0r3ZSz3foH3p3zM+3+bZIvO9sXHGTeYNCSu1PumqzAXNGdRaxCadFzO\ni1+3+Torbp7vmbMbdDRxuH8mHLoEgsVWcdEByUuIWtiyPwZYbeVurDQ/sElB\nkOZqdZbdGIspwitDjWNU9siixMeKrs0pXNfmHRTjxsz6rKBHX6AlGYbQ+Kyc\nYql4NIqeLskHPNKSLR0Se5Sxjrq1NigMJLiYtCJP4w/VQ8OrPHBwPIpgDRHR\n+pBe7jO+dVXVctnGJs00QwDdRcYIDUsRiMPqILAaV7MfgSRRR50alAO1luwN\noyYVCOsqC24lcCXzb3JPjD+E+QKE1wEqcR7l1U4qx5/3UoM1NLvStwUbDTlD\nnC6r\r\n=UOll\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.6.2", "jest-runtime": "^26.6.2", "jest-haste-map": "^26.6.2", "@jest/test-result": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.6.2_1604321520372_0.8585382002509601", "host": "s3://npm-registry-packages"}}, "26.6.3": {"name": "@jest/test-sequencer", "version": "26.6.3", "license": "MIT", "_id": "@jest/test-sequencer@26.6.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "98e8a45100863886d074205e8ffdc5a7eb582b17", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-26.6.3.tgz", "fileCount": 4, "integrity": "sha512-YHlVIjP5nfEyjlrSr8t/YdNfU/1XEt7c5b4OxcXCjyRhjzLYu/rO69/WHPuYcbCWkz8kAeZVZp2N2+IOLLEPGw==", "signatures": [{"sig": "MEYCIQDRCCIVmSXohKCBARDDiVjsAzbZ+vO7Yw21hVmjeN4tEAIhAL71KOQofXCVe/g5+6Jpi+ll/GT+cOqo7b7xiCfMvj0S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfokeZCRA9TVsSAnZWagAAXmkP/39j0QrcYKZ4vgDGWeFM\nANfgfxFrATyWW9O0Qbe/2XTjEBQXKnKREH8YuNa3+VdpMVrNqgzb6jLjMco3\nl7LOp6MrwEIXlyxGXT5Wf+4999RBVzhWp7EIQrRLlq0yilHhv/FcwK31ikSS\nyuFOp28fQ4zZpjum3IBwHhszb/0ZWoQdCtHqCLxM2wriefdncQwzFx2/nH05\ngyNxkOuxuI/PicLfjKwcIONvkkoCm0hMIb/Jz3CVUYXqDNNY9XPs96N7tkI4\nNVJ9TGwvdIo/ETNcFKO3UgzmNFggyTJUqdp4zXqjntwZE+bYxymIoUKQEXWN\nhS9DRS8REwC5skn/CYqMCtfeKrljGhn1/92ane6wB5FcS2ROQL1sUCePT7FV\nLJ4qbjNS8FNBE1wOvdAn3RXVLhEjasyy//QulQ+7mQhHo8+DlOR/oAlC8lBL\ngEN8uO44I0OuVxDK4km9/PPFs/jV1KW9X5BPNxL4THUxAWnEVKRlR1EQKSY4\nSyVGSDNY+rQczbEPuORYhrYx8BnBr13KfoqvJiJKQgTPb6xawAOEUcAnKxDX\nrKSHbd5XVPlmS8SUiAoPUYdcvpL0qeKa9HYwdsdKcvoaRMDS0wuuyEROtCAy\nC8FhcQOlYQu33wd8OITtc3Icle/FpHBoIgeq8ouWjJGiVvw+9RJ7K7cbH3eT\nlrO8\r\n=Dwb1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2f6931e91d5ab126de70caf150c68709752e7f6c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^26.6.3", "jest-runtime": "^26.6.3", "jest-haste-map": "^26.6.2", "@jest/test-result": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_26.6.3_1604470681044_0.4984498260636734", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/test-sequencer", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3b4397b956c10a61077098de3cafc795577ee052", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-oqXXqTR4DbZhadztJS07iqCVOuZkeKcWWqI2IUg4i5MTJEI1NE57uMGRWxxL8MT9LnVttOnuMMdPyuvRdHCpxw==", "signatures": [{"sig": "MEYCIQC3DgcWLR66hv/tFDZPoeNj9/LGxVgI56O3e49fX0bPOAIhAIEBMX3RuK8Ybhy2wk39d7vVLN9oagLONXEsgyDJPEVv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KiCRA9TVsSAnZWagAAdBkP/ioHqxGU8tSWUFby5UvU\nqaRI+QUdBw6BTw+yRMAeyjbiITIe/9Snr/3B05s7fhgf8HPIlgcf3QiZtwa1\ny0hW3cbBX9l/FJXGWgAa2XgCp0PzVtaJbXfkl31Rxvn9ziEL1/79TpapTFs+\nauEm1liwAi/i67nOTyUa/4ijK0+cXjs9EDA0ThCBWbnuLHBsb/pOB162jGje\nSL3tNZZLsuNcyGd/E2lYi8ZbhYveb8Dirt1mDTGLP56jicb7T0w0C+kx+5hY\nqtKlKFHYCenkD50TX2vm6WkdqF6SQPKXPixV5M8rY7HVwJUKGsc7P0BOa4si\ndYuA2uplgGfurf6YYowS3RnwdkRQVOpTh0NUTytKhY300wOq0/Ce8iTF472k\n/RsjoZl80KhGztwRWK+74Ti3pqtf+WlSLvzvkhl2QK27bSdEeaJoTV50vu5R\nf2apYXxx5JpZHiB1pmlW7nH/sz+6hHmUf4uzo3vSPBU8qckQfCK1kxNBT6jF\ngn2fZ20gErvVgnw7BSqtZ8yqK2gF1DhAlJjix/qxeN9t4WN4WbsNnAcpKJ9s\nP3KwEfCuqr0LnXvcmYzE+9a54KxwG9+rygPCvnJdsEcQIcz+OV/TimgVxhSs\nXgg7sWXWtgEQk2xDUii+NTHKhUb+0kzUnKEC9fWhtsW86pjuiZithTLqN4wT\n5rOD\r\n=V8/X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.0", "jest-runtime": "^27.0.0-next.0", "jest-haste-map": "^27.0.0-next.0", "@jest/test-result": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.0_1607189154452_0.7095030090440955", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/test-sequencer", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6d12fb57a02236e04e7a8bbe645908007ba4fc45", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.1.tgz", "fileCount": 4, "integrity": "sha512-O+V6fsIj5e75q9OCZ+2R4DKiLDsT/KYaMZ/flMp0BUMiicZFEDIeDC/4lNrZLvWtanxHHkV2OYic3uinQrLxqQ==", "signatures": [{"sig": "MEUCIQDciOWlZNH8FpzAODdGvC2R81g2LYksMRRVOXCFgPv/JwIgLh63N0+lvka31+Y4B9rFuJg2qMGaf+yH8PzCPhAu5xQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziOSCRA9TVsSAnZWagAA3EQP/3IkgqBUok0hu517uEii\nWOwGfvn3q77BtMXVIsw8GlnwJYtJqz/gBKtY7ZBoEsVRQbqmfepOpRU6gG3M\nldd71oJaKBiRsJD0Ar2JQN6jOeW1ssW1QxWSW+vjs8aBMnTPyomchNS55zpK\n8Tp4e7wKghtWhomECfHwbsEm1C0YxDlj0yf95a/U5DHdjSBKABQSHsQ6nRun\nfuFTuVaDFrMUkHsoOfw6bjhZe19RHK/9tTdrGAhSR69DWRHzpSFhQ9dhbazi\natsTBK5u8DNX9/X9Ix8cdrBxZN0PeD4iTwKhYI7utxdFT7Ak22K/8x55lW19\nI3rkaIL19DounmdHreLuPBkrcM9m2boc0CFUMngtB8jn71LN7W4RzNYn+tXG\nFAS8l6o9fqZ3RMWdWJEgUfiubpfOe5feICZ4NDemwdnZL1fWlC5mRXQ2EU8n\nPr0HOquP2r+GwWKQS3IbxVJuKtDK7teYxK/YBL2Z0VMBaTygu+FMsucyFUQq\n6G9T1UrjMFXnf8ue5l/FDhaYDIt4pFt4pTVbomR1sfrFdDctuAJokaTbQfdV\nHOkwrf/J1tipYsaDs57owGXVLWr10kEiHJC4Sd55VhalbLHzOJ5SIeSp8os3\ndATXkN5HEKUUMcXAjuLZkneUwwqZ1y9u6L3befpbzR5NQkckHS4y68vwpIug\nfQ8H\r\n=LNFw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.1", "jest-runtime": "^27.0.0-next.1", "jest-haste-map": "^27.0.0-next.1", "@jest/test-result": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.1_1607345041623_0.5483895203815596", "host": "s3://npm-registry-packages"}}, "27.0.0-next.2": {"name": "@jest/test-sequencer", "version": "27.0.0-next.2", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1fa97936e1a2e9dbde8fc99eafc470f892a448e8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.2.tgz", "fileCount": 4, "integrity": "sha512-ayciaiZ+aGTU+wG9uQ7G0wtfcIB/mNpp16zzgUY/yQnycGWz5OHdCSiaxkcu6YOXnBIeZjxP0ab2ApBCANUSuQ==", "signatures": [{"sig": "MEUCICZ7wt1UEZw29q0C2q7aJnzHRVSuSEFQHvzDYm6+FCLGAiEA9ktb6QyZ8qjtuIXoN1zoE3uf6+2vHc3o22yCL16ZOfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzj3VCRA9TVsSAnZWagAA5ooP/j73CukSQ+yQrsAx9jWJ\nGqQTvWRlnSdRirt2IPVaTtQGdHQAOaGPc7fu7CDTAYK4+bZkaJnTSHx+Uz93\nkxcXPz1oQhfdCd4ZUWDPgcMchk3hSF3giGdTL5nqn+oJopf1aQ3QDpOjgWME\nX82/OWvodaT0Kv13WdS0GVBCbOOkXL6OlVoJEnqzEhWzVkGe61lJllLysiMu\nkGsq0ldzGgbFD2sEWnPjevhUWZpyc3x9wOIMxMT69AMnv+r9AjsGrgygIVHs\nDi5cgK9peSBTtrJ+znvKyMe70c0pjDJ3nuhMssIeSg6lTEyRE96XEcZ9APeY\n+rrkWizwbET+YpVeGkub1RVDIg/GFfq0SFSn1OMC2Z4E/lFzgrAAqQF0daU9\n7ARx9pjLVYoD8nNsluq7r0VOEHRDrymBe+hxugt6XZ5/h1L953NMh9++ANAe\nnCgaxreCj0Dl18QCqXe62XuILkDpthZ9ohHO5KuOy8Iy2cSbqaxs2EamflgA\ne9ipQOUnKh1j8AE+fvfHnAgUHyaDYKZIIjyakpXWW1fF1PQq0to3SiK6dosR\njMB82lPzb9IvrK0Trnti/hjxGxIe62XEvCw7/IA4uQJ+FBt5Ku6ItB3Y+n+7\njp2xAgq7E3MD/XV4w6cvpmDQ04ubTZwdDwtsqYXf8ybLI+lCdsfb9Ui0Yrkr\nluME\r\n=vhmn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0006b152354237416ffbbc26d78c0b10375c0a49", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.2", "jest-runtime": "^27.0.0-next.2", "jest-haste-map": "^27.0.0-next.2", "@jest/test-result": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.2_1607351765210_0.0868240421687303", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/test-sequencer", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f237c82252d340c094b26838ae248a51aded8d79", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.3.tgz", "fileCount": 4, "integrity": "sha512-iM19Fr0CC6YgKZTQMJ4tQZyV9aGS14igSHaPXtfnkaN0Zv9RkC49yeXfTUOPBYu0VDkp5OCLKjHqqdkLohiP6g==", "signatures": [{"sig": "MEQCIFscS3QpriPGzV3i2uNmcdNItnO9BQb0BaTYSq+JHohSAiA36AczOfo6y5cF+TtxZAR6fb/12ndfqZkWo4vODtUUjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuXZCRA9TVsSAnZWagAACEEP/32QKU/x0zEwfBU3e+ER\nS2dm/0is2R3PjOHaqD2m5hNk2eBVtRhFWmKV5ZYAI+m9NuCV4YoiUH8RPdOF\n93Ju/Ibvf7dxRLte4fmDa8HsyKmdLzxt2gieVFJeIh3gSlA6TB/taPG4wsV/\nybOcNNJ/YdKP65S0Hat5BaI0VNNzrXV3AfrStPZzh999DP/3Z0Kx04P20CYY\n09iyylrrapWReb0R77aOeF3nDphJPCrx2B7AhjRNLkQ4rXdnygAj3Fg5/MLO\nM0Nti9PcOtCaFVTztNvzW3Zdc3t5S2NYb9hMyhSnY4zG28BvJ+LYtdIq6hwy\nVn9t/UYNi7AfZUcZj4Xv1kI/DJ2BpxWyQRTjnvGlxuj8eO3hE3fHqbYS2MUk\ntoND/r+dXCtV/4PX5VuPpiNM9i5fVgtu3r+f6w9ilGEGMuackxy5Xn2oF/Re\nrmdxP75ltyPhtRpsNOkmSiP0jVp2FVF17xzyTYnt8Z/3vO2wXvdEa0P31wzt\n09RlOw4SH+ysUE7VYsII28km+CA4olSUhtl7vpB5JfrGH/8E39yS+cXljgak\nvhaNNhEIxxe49crjmOFlKRlp+dXrHGzf1gbBCETPzQfanJaQ2HmzZLUeddp1\nnllCjd8kv3OKzztV69F8kFf4uXVlwFt1o4mgj6MkLwcQJY++OewD3Jq7mqmN\n+ac/\r\n=duwv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.3", "jest-runtime": "^27.0.0-next.3", "jest-haste-map": "^27.0.0-next.3", "@jest/test-result": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.3_1613686233179_0.3628877865915714", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "@jest/test-sequencer", "version": "27.0.0-next.4", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20ea8edef05302e374fbfef6987e295cf5e2fe42", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.4.tgz", "fileCount": 4, "integrity": "sha512-dkArRQwFYjRiMXpwSgfHwK1YuFMUZbf+WiWRz/WOEGKKc7Zr9CHAFEvq2+bCFZnWh1I9XkqHzWqK5a3EbvjS2g==", "signatures": [{"sig": "MEYCIQDBDOg39g+Gx2rXeS1gdxqTUPIuBJ1xOs51oMFbUpV/1AIhAMckkKq70Fubc7ilR4+LkEtnElDzfOTpXYR1GMWDL5mt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRip8CRA9TVsSAnZWagAAFI8QAKFVaQSVJIlalTn8Esda\nZMO4em6cVZ/ilX5ze26d449S0xNPczreccq33PcjKCnandwzm0zChXzBrfPy\n5BPcXiJ+UWDlu/FbDdf2qkde4ItqTOFrGUaQz4o3iTADLzS91DjJYKqw9MJv\nkFrjQ2mZ0XgNz7qXVZjeq8qXG9NoLajDcaaZumgo/FbTx9niMnW9r1Qp6Iwn\njJNO7jg2WOKmtgX2Rtrrl3kE0YOHRDxGwOEVlaqFqhvJyhcqmc4bmz8X+QVo\n0ELS0O7/PNWG+Uq4SKgmmCGF4mJIgFgbRd9joqFCQbOUsKWg+rwPeMMTUVYq\n6B32EYzKCreK1gaFidHoUTMgDj4TxKzlKJ6p7W1rh3olQyXMABnt+W/BQxdo\nZ2Lge1hJ23NqITGlAeD2mUyasNEaDyQwkgqp+287U4/6oIhCCokeVgUjUazD\nIeG/SdobB8+bD33XFAwkqj/1wVVayVrXT84Vuj5RN3+yoZBOKJM00Q/HrNI6\nYjrBCYnCs00HA6i74ohrMEFEFWnjt5YO33oyIn8oVzQBLmMVoc6ZaG4q8WTW\nI0RWUC6B9CoFJRZDEBR12QLH0n4HHldDs3FEdQ0aflTG0DtGjKTu72X/Qa80\nST7qyZauGmlm/Ze488WTzYXaYOJyzhg5cvCgz3xbLIVlE8HqLCXGiYt4GLH4\nGzrK\r\n=RBof\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.4", "jest-runtime": "^27.0.0-next.4", "jest-haste-map": "^27.0.0-next.4", "@jest/test-result": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.4_1615211132063_0.5357418720062141", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/test-sequencer", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82f80b6f7e5dfd2e8c30275777766e2ecc2e56f5", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.5.tgz", "fileCount": 4, "integrity": "sha512-ojlwiRJJeNRfmbnehf6nbC9nqBM650Ms9RhnjozjiP9JUTJlujbNnfh/LZ0WP2NBVhqnDovRbzt6LWvtkx2KjA==", "signatures": [{"sig": "MEQCIC0WcL/9snRFH/+TwDL7AVUGftIEI/XxPNx86RdFWTNlAiByqUUY+/uCbpqIRJkT5dJNT0W75b1rze8Fr1QLyS+nLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1tCCRA9TVsSAnZWagAAQroQAJ0a8yGsYTKTG/HoqlIh\naK1tK8nRMbjLH1fho3YXqFKdYLGuRQ9R+Ac5HdbcBrDkd1avwBLNBWlSEgBC\nYD5ZKzAH+T/AmbLPpomcbMsSzrWFCDYbkcoO5RqpsHykIXCwyjeGudPOJPyw\nFbiUGZ9pvoOWds7AQgsTz+B5hPy0B7u+9d8jemiuQAL/6Um+sXI85w6/JnFc\noudmB4OG3a4H5uGssdOwtozU+ql51I+mCOUxbsOuCzyoYezzJk7NOsAA/8m8\nUgMzGMcKXr1MS1nWbUQ6ESgMMHfu1MoEF59osxScsg0QSOTpkmZ9ZtZrstKB\nyT8ozfr8DFAmnvc6limoPmSqfUbbNrlgpaLF32SI0fc5BOuyamRdr3Am3Lck\nEx7DbhZ/b6mcOiL9k9B/yS7acQu2CkIFj8+kDNNiylfolHNVGlVkF2GpIQSJ\nOJINOUYu8XHlx7txWmo85xYEkQCMNg/u1vOxjrUaBy68eKwihBrrP1DK33Ta\nfmsQuTPcCF6GsCNeVBD63aSr1nxvf1jRKMLff5iFjAqHfn0+gViD4sC9MJXR\nmJ5CNIEw09u1ZM/cjHrSdTpewQK5S5HWcPWFxcU8v/EbJ6mMrmeNA4eF/eB7\n50lVGlHlk+SNy2dyU4paKgdlxdaVQqDAuHlvyowGLOtMIg/DVJOZu4kiOMBy\ntxo2\r\n=t644\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.5", "jest-runtime": "^27.0.0-next.5", "jest-haste-map": "^27.0.0-next.5", "@jest/test-result": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.5_1615813442504_0.5791499204447099", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/test-sequencer", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a23c494247b444675f080d5994594b144bb19b2", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.6.tgz", "fileCount": 4, "integrity": "sha512-hfpbdAkonOr+EQXJR6rxfx/R+JIJRpgZ5//LhiHFUkYbGKKKoXY146HnmyZRFpzrMtX3wcVvzDxFCoQXnYBNGA==", "signatures": [{"sig": "MEQCIARZ+BqF8kootYHmbUx8pBK/1pnWg2Su4YfwMjxGoRYUAiAckQyUh0YxXB8kboUJDKayvRZErsB2RwxlkVgK/MDEcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcyCRA9TVsSAnZWagAAGQUP/jjCBW3WbU6shv51e7Wl\nHlj2vYU9OcDFMWxjor969pVMvjdncfPLd2h98rqJsyQWVw6pzwscaCfcZvak\nlXhgctS5k94Of05ilb5j98CtyMU3Vmg70dwnDeyNTUEJ4moji5n6eqU/uqzP\nMUhDLTea/MfvCkOvXwiQjEyFeEsVDvqGvdM761AqjvHBX3SbAhJ/B/nrQXPV\nzaO44CId/I6ntSpBU9Uw/P3AG0wEFtenbdqjPdZQz6vCCYPD/c/XiBZmsKXX\nV7iLc1mRX8oznWE3eX2Ff2d/I5gk/9DKy9q8v0UyDtQsl3OzD7vbgSkKzHv5\nNCv4WEjpW9rvL7P3Y08l1hF/taLzCiNUX0nYPzcg7FNqjlq9iULhEqToIHTR\n64oJfSFtb3/aJZT2miFT3uaxlVTvGkDB5K6mQqi6nTyo9beL4XRX8uUzQ78x\nAZYTxtFleoFOgtFkK+6i0o4xchVLLxEYRo0L+G+2MPG76aYVZNnUnJ563NT7\nYl7LuqS293hL3QaHkhIcgWGk7bayTDoYZ3hIaEPo/WgoWyr+buoV7iGZBqOq\nMcys+QkjaPQw6deKlpETMn7oFO+59fSlSSAxmcNfcPMnE2/2jOCZICxOis8S\ngLM4ONQ9RIbe+F91HF+3MpEfeYAoh02vcBfkhfxcbUmXgOCVlvOjXuXb90H/\nV5rg\r\n=t04x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.6", "jest-runtime": "^27.0.0-next.6", "jest-haste-map": "^27.0.0-next.6", "@jest/test-result": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.6_1616701234214_0.49385789928685675", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/test-sequencer", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b413ab543e196fab0cbe046181314ed477846e26", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.7.tgz", "fileCount": 4, "integrity": "sha512-ZpzPVXSa3NCXolLb7Kkni8+TsUGnjWIgm8hcGj49fE6i2V7IY2RNbzGPy8UbT4GRfFmnsDNZ3Y8ohb5fiD/QOQ==", "signatures": [{"sig": "MEQCIH/hK/ecDrZkYV9JQEGbMFxtfgkd+hZEW4knx29iQkG8AiAU61OrjocfKbCIy+7DV9TI2A4ZzamAPX6hG3asT6GJUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCuCRA9TVsSAnZWagAADeEP/AozyCReVx8txPxXPdRi\nRvk0HzHW6hm3rfAC2wI9P2AgtJbEXOWNivhaRTMk4Jbw4WTOnzXjhT8jS1w7\nQdpGKQmuwQZD7ZrbuEOaqnuB8EmioqSkLNPIhbNpcSQ/BlwoPWBOfV/4pwya\n44BQXqLccMK9KyBDUzCcQmsB5PC0ZmGkGxxrL3oZoP6N7J5yfj5lx5DRfNrS\nUa0x+G+KGZOKlzCCHOvHQ5icty69zeGgpseSm+rTmZKWrJh9hkxMsFWCkq7m\nnBSppGbN9edrnoCjqD+97jzPzMs/DK3y1TLYsfiDa737gTiJbUGkKmEJ7XQa\nyNQFfcbHOk3h7dm6Jrx4vuQOi/jTbbDJwNfQnKPsyMffmy5Z3vq58icLEf0/\nPiEA+6uuoLoFM0qSnN5Xn1EHDtjy4Lg99ltf2h6pIabfzy5cuxfqzoqyCpLq\nV2UsUFa/01tb/sWnXH21i/Tw47B3z2T4ey8BsjdGcRn62leIpgp+OEoTS26B\n7eGKhLVK9WCJxLVyUnHkLxVgFrQ822rGbxlI7ijgzbj0pH87+0jfS5e73xZp\n9gR1P2aTBbhmI3LuT3sUVEK0I2l453QffByICs+fRaXwVwrnEdp473/jYS5Y\neJYtgXO5kMRJh+FcSzpdN3wyAIIF0cYpIWHtJA8p/Xihc4RdqY9zPd4E46UI\nbhfi\r\n=pOuK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.7", "jest-runtime": "^27.0.0-next.7", "jest-haste-map": "^27.0.0-next.7", "@jest/test-result": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.7_1617371310034_0.3318176287949215", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/test-sequencer", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "49946f0a2f5fd5c7b553b8e09b812c5197fc6a91", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.8.tgz", "fileCount": 4, "integrity": "sha512-goqCkNT06BduFXEnX0ua1EvS8wuDHRe4SwD3DsYfOz4BRDVtlSjQegBDOo/J2LRzOiHJATE7TS8J5BcBHCdBEw==", "signatures": [{"sig": "MEUCIBB/e/VKCT+/no+3XLG+CUIE0DNDvZRSr8YKgziWzf6VAiEAuavSKTa9dkkNscqWen6dHMMdFL/qXW2Z1O+k46mT2As=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzxCRA9TVsSAnZWagAASG8QAKPEDh/1dim/dtV1I6XN\nG/FwUhMkgxX5GGxT4H2x6vPI/nPmt63JcS3do34NM/sClZ+3PFaUtetGIVDV\nrqM5b5t+izg2JhigtoyYmCjlgpM4qN5tjAGVSJXd3Dt41PfUYXhAzpi9US8g\nt2avREHwWXxijm6X6T2G+mNenZqkLd4cpPrqxlDalVZJM1cE9RoSvvmP3SsM\n7uweG5x5TPqR5aYIMftE6HW84yZa7NZ3ydWQWBpT9WizDGI4SvIx+BQLWANr\nPE5KRQQWaUyI7uKZJmIIm1Gn6wQSfd7pQTfDwrAisgFv7J7gH/RVLUU6hBBZ\nHjJAFxkeTZrogF4cnqonw/Y8UwMnBqeo07NWCCwURI2HzCafkzWWBAgHwSHS\nShng7FsfBnBEE66lePm7ohF1N09JveGRYdiZTGpdRY9GDWGPTIK4jJyDNSOF\nTaOpy2VWrDZMncmSu4iziR8sh3i44B7o1Ddq3mVZIdqmEiC83Y3xIdPNgE2g\nYnA+RC52cSrVP4LtxBZbsXAhXhNOaMgZ8qjXGSeVFQHTGpGuhIlGg1F/dZS6\nCmlWxaGZ6PacHnMFG4evlIKAQBtVD2IzRuFBihziGcXFyTzTWi8ErrSLciaf\n1XOwCDobm4K27sAownrV4vdSmWXCu06INN0xbGvj1lZGhepNj8eZWqHOSoKV\nmr0g\r\n=bNVR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.8", "jest-runtime": "^27.0.0-next.8", "jest-haste-map": "^27.0.0-next.8", "@jest/test-result": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.8_1618267376695_0.9783637778388965", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/test-sequencer", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad8bacc3f0ab251df220016707cce2eead89473b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.9.tgz", "fileCount": 4, "integrity": "sha512-pjJQqfllduxbXfFahjQpAuDIOt18EsOcTySSacdXelVvc/ePJOYu1gRduItzFEpJuZX1og7qJbwRWmS3XwcQaA==", "signatures": [{"sig": "MEUCIQC2t4ZpHl9JOjXo0yBtJXsjb912TJizDZieeqfIEx1+0AIgF+jriBjEf2BFMH8sdZi3sWpo9KJjAJTUoGcVn/By0oY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjiCRA9TVsSAnZWagAAsbsP/0JErvZzZ4VwpIspVXDI\ntDkZFFms9YO6et68FfwftIZ7OTZr1sNuzz8dbYM3pPqCLCig2Xxnn5tvRQ0m\nJU2+HXd0YByW+3P2St7mxLAd99PFC9UN9HwrB9mQ6247X2pbArUaZ6vzEdek\n9serEFLUbPSUXlSTGxAAno+B1WVGiXJ/x8brcZdKvlDmMitJp96AvGTRx+Qt\nFKd9+iSNMEeiCnvL140o+FuvA8l06xYiA6k05rZ9penuRm2K8Lu600cogGAW\ngFY0StCUm4ZbZ1+1rDGAo8StliLqXlbMy6IGQ9osMr5P7pKwkjiTGntQygIW\nz1B98Sht0SMQj0x9Szm1ln689Mo18Bu7M/itVqY53uPxI4HaHFmo9C48zKBc\nBQLb3eIRO+Dr2kuqlHXQt58YNjPxu8qEIS4Jcf3hlKnz4sNcxKM1doZyQXTC\nyD4PbfUJtyBxqXAYRo1IsfR6fMkyvFZNKUtBnjOwj8Pn6v4CON21enmogIgu\nZHZjYCeFBfenQv+oNaXpHpOJXybgVHomJwEAeNj2gKL0T0k3T/CoI/uircUc\n7ePlOV64vFa9tnnUZvhfRxKIYC8i9G8nPMXrWnpFzWCw0WEWmaCN5ekhvGb4\nQo+cHJJ0ZwL0KsiFkLBRBGXoQ1VGEnpiricDpgtaGZQc1w82szYydrojo1wZ\nCgh5\r\n=2eia\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.9", "jest-runtime": "^27.0.0-next.9", "jest-haste-map": "^27.0.0-next.9", "@jest/test-result": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.9_1620109537950_0.09286733205495046", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/test-sequencer", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a07ea16706e75b3d16626e70adf036e564e37c58", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.10.tgz", "fileCount": 4, "integrity": "sha512-2gQ5VKldiYI1bcJsPWF4/aKykRrpXu6mbs9B7GQIpgm/deVQTbUnyBlwC7UBRQI/EVoX/2hP35xeSmiIPSGzTw==", "signatures": [{"sig": "MEUCICxFLU3sGBe09mr2SvmFvl2n9XkzxRdMRJSn9XT8reGiAiEAu7sEIyizVFxJCL75++PSyPzc0hjfPDSFNvPzYJYTFAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4sCRA9TVsSAnZWagAAWFgQAJbKxVwAydqQyWGgI8h+\nxL19ujdMWg0I9BWrHMQgbj0TuG8vYwQ7b4NHXHvkFgj9j92ZSUpwWbUtNWpZ\nSb18itg3lD2eCHAdqrgIhPs/unSmWf/LuIRC00NrJydg96W4s40TEf71FL+e\nGlwedXZNMjPpDNLhptPqECAta13Dn0hL/1LRPwjhT8aTet1q5Ye2vdMo2SYf\nX2MImY6gA/7U6NClJiqQhuKa8ZZX3oVvB4RkN1qts6L1EeVKJXh4rTlHTZFc\n3JqS31qY9eFUMV3xq2AXR3rpqwd9Z19odZRSgeR28Y4e9DYh1LUJ7fVjLGU1\n6fgDplXoI0gPe9aOJAhSFl0YYN8XilnR33mGpskv6gU8tJ2k8HHpfa8LGi0e\nQFaeSNU4VuZzuoW/bIGmbwuYQRPpU0ui3q1XJNHLI0uvlXuSONrLSQKsS/AN\n+gMS0MRqYdaNv0eoQ6EfUCBZ6H7adw3p0H6Ny9cACWAp3kVZlK6wg6KFKXeZ\nq1RfDF0Jn7ZXVgBakGYxZIzYq61A6VgC7Gir8Z2kL8oSh6tWvqngllUPKfSu\nNgLuPvlCG3uaoUHGSoqorV5L5JHKvwlk7qnVIGCqRqSMQn8WnxVVG3/K2RnC\nBhouZPFvF268VE1qTvIGRU4g0R3KfjiJlaNaJXa5kwjJUHixD/PL5C7ayKUm\nw/5p\r\n=B1fH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.10", "jest-runtime": "^27.0.0-next.10", "jest-haste-map": "^27.0.0-next.10", "@jest/test-result": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.10_1621519915579_0.9050281500199562", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/test-sequencer", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe788cbf84078220bc92dae016fef75a82c728b6", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0-next.11.tgz", "fileCount": 4, "integrity": "sha512-5TbZ7ncVsTa59N44cfQBTSi1mG0U1xiius/kCBWdyKYMiMLhzwoIeJePNjFy0mVZ7eN/145DcCiYeCVb7t3m3Q==", "signatures": [{"sig": "MEUCIEr1oLJzp9YNVQ/e3ijtQ6qUPH3Lw0Hb37vm98jX+YOTAiEA9TRPo8o59ZremzvUDePAsMX9IgBfhoJhw5XtFnzOSZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuK9CRA9TVsSAnZWagAAnHQP/AmndDvb3SO0Jm3a3TKI\nKI06gFCNoR+UIr8j8PpWIUW9cSwPJ8GlHT1MkUDDskY7guCxRaySmrJXXvfE\n+KJ/BBH7M0YlMUaCvAOPi3Pzms0WxDtMEr3UjQdkcTY5eCITvlpbRuzARLKF\nvwGnKJtG4QeWs2nidX2WBJBSJf3VT+H4WvlOd7qmCPS7HjJzNYjuYFTYkGm/\nRhIvc3QnFpdxyQQkCjDHQDlXpS84LEQjPKZSuXGFk/y8/Ey/9KgVyid4yu8A\nXDw3HTrEK/qJmc2tlN4lj/YHmsmEfRl7cYyImjSs48ygvllwtRYyWe987sL9\nvIwkh2BA83yYt75P6t3KCke4JZRPFw51DehS1jpTK8ZLVR1xjkfGkTWFZuDS\nIWcb8rAUe3hax/5H5x4pYmOeAD1WZtpXUZTCXgcSeDfO7sLUuOoM+euyT8uK\nf+sbA5LIeXHhWCwR4rf9r5HWExjOnoHiOnmTazyZxtwyTicRPYsax05aLBZs\n3hcCcRZ+aQvREMse+yBCEqagsdJpggEdPPLZ0Zl+rgXnWgal/t9Itg1IyxoA\nyIqhiC8tnf958wToGokNI9Z8mcPApB5/mdR2o6Zq20WtGgAr45ACugwER+hu\ndlQJMPeWzpJ4nPQa1yEIfoIx+v5du4pl0S4Dhkjr6Xcycp14YRPACdqTSaR0\nGpP6\r\n=DS1x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0-next.11", "jest-runtime": "^27.0.0-next.11", "jest-haste-map": "^27.0.0-next.11", "@jest/test-result": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0-next.11_1621549756969_0.6709122618058929", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/test-sequencer", "version": "27.0.0", "license": "MIT", "_id": "@jest/test-sequencer@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0177ecaacec929cbe92ca83d34c8119b04ba4e52", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.0.tgz", "fileCount": 4, "integrity": "sha512-beHnjmgoTx8jZPhRUcmzxCWeXHnU2xl5sYYzMJ+d2ksi+/mYPAc0gcBLoC5UZ4ngjRDygVqdFH4JZkM9ynlixg==", "signatures": [{"sig": "MEQCIGEQ/q4QNGAvV9rHgfjtVX7crIYW33JlergGqPvhgJdWAiBGYlgkS7xvU93ffS+B1Z3tpOPcQeTFGLs8AGyKfe1NXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLI/CRA9TVsSAnZWagAAfeEP/2O97uVijTyWsh48W4Lw\nuA40V+l7iSnv+ZAPIT4XAG2aHLVN8ouIkdACnTEd3V9wWzGA7iTELkwVt3w/\nskZNNTCy6HBMY6Mq6U+F7V13YVYj5T2n6bF+aPYEGGlqScITUjDkmZmSuBvR\nGCNI3fU7UiOWWsXuCE1Cl29r0UiqJxcYYNUQbVyiHoDtoH/TlPeq7pp+BoMu\n9fz4r9wJ9FxwQBBWGwPdbCXgmZfHU4LW/RL2bwbMFNBLPlzmODAQA+sK1sey\njKf+14Wv2TYnwZNOhOjszvZyX8lt0JYPt8K9Kier21vhT4WMub/qtz8DVYjz\nTfp+xMaNLDPhmKFzpSdR8QHfN5RhiH8X0hYN5c4al0M4XRaOCDTytbAkH5Zg\nRP0qFKy7JrJE9N6+NALQvGmE+ukegXtC9AywjkYmVJQfk3alg04Dr4u7bKt7\nmIohsw+1jPKk14vFpG6EDD91GTu0f1lXqqSjGFxYkwIz1JTfOk4qnaD6usfl\n/MjLjnztVYeUQCaV1tNI8rp9WMswDXcBrFmj2+h2EcSAK8r3xH1GFazlrFLh\nR7G+VnNmkjuRF/rFaiMdHBtCuBuo4M0zaFGkCeadDSOqyLmQm7B+5et5kBJo\nJh5j41gam75QLoov7Lf7A8LjAmqJQ4dhqlLOFaTqO2m04SFtg8xTSsdP+opm\nJJDP\r\n=039z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.0", "jest-runtime": "^27.0.0", "jest-haste-map": "^27.0.0", "@jest/test-result": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.0_1621930558604_0.7787201671904249", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/test-sequencer", "version": "27.0.1", "license": "MIT", "_id": "@jest/test-sequencer@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a3b85130978fc545d8ee6c34d65ff4231dbad86", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-yK2c2iruJ35WgH4KH8whS72uH+FASJUrzwxzNKTzLAEWmNpWKNEPOsSEKsHynvz78bLHafrTg4adN7RrYNbEOA==", "signatures": [{"sig": "MEUCIQDlG9iP7fk7sKPGTPTa0pmyeV/GiM3Utvw/x/jKg1xqCQIgXtCb/DZZhSEtq4ZtG7Y5hNNtBjV1yGi9j9EVGh6ib/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMxICRA9TVsSAnZWagAA6+wP/2WduvmBlxT+jPCEXRi7\n5yL8NlJds8aIV2i5+dLhXc/f0lDWi22SwxO2aKNgA+olSIju8Sj1sFGODegh\nSB/Miyd8O79aXQPHOT8GLfCPHdqfONtp1HyskgOP7gjO5F7CHg5wM86BwZBN\nA4pur0Ylq1VQvVqjSyWW7VcrQrfluCwkUO5ydZRPWWq0W9YzUIoS3/XBstVn\nta1NFRDe8RD96F6t86YOYzUudyW/WNKFnZ/aNEJXZpMdfQ3LfME5csDuOvT2\n33OcXslsq8rKAgxInx+0QPpLmNnm/2Vs0gXigd2a6uKrvxwoYWttiS6x4Abr\nIRnex+Ii6xj7bHvRDafyD3g5XuNf2PT0qDmIIvAntlA57OolKkNC4k9aTWM7\nRatv/b1XN8MoumcwuzrTtQJtVSuzW/jdqeEmrNH+kLlPNu1uFAtL4O+HcVdo\nrfIUDtK6AFDM6LQ6HrdLYI0otgA+4Fy5pEPx1T7u5huAmwCDHV6S2BrgEosH\nwSr1Y4VeqnLB8pnnxnapGTpU80aXA6yPETP39axeRXpS7dPd/S+kVmiR33ua\nhDJluQcExGJ3UdiZDNZKw7XVbbD8QdNneIur2gTAkw2pHiP9kko0BNnoq/4I\nvyeKeEhccuH5JRqSqHUHPRRzESkxIpSd4qRlkxwNOHvwzZMegF0zSBwtCki5\nhuII\r\n=kX6w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runner": "^27.0.1", "jest-runtime": "^27.0.1", "jest-haste-map": "^27.0.1", "@jest/test-result": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.1_1621937223763_0.7125265423776885", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/test-sequencer", "version": "27.0.2", "license": "MIT", "_id": "@jest/test-sequencer@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e63005025ab0145ce1a38a8b2abd5e32fcf74306", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.2.tgz", "fileCount": 4, "integrity": "sha512-TOcauHKjyB7MrR74mnHmv/a4QhT4z/MBOMgC791ZLUVI8uA2M9AqjJIduSrPhvt8GRnlaD2Rpr4TmQGuNHov2w==", "signatures": [{"sig": "MEQCIDZy9XEXqpWsyh9WRV03JVIvX8DczaQ1mlGkNq9a7S/pAiAZ4/dzRX092vXEh1NI7WqwZ1BVb3kOIm6F6+Dv2jn9Fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi6bCRA9TVsSAnZWagAAuO0P/A8clcj3m9Dl3fJuqEhT\nTjH7TO/c7DKU7Y7Hc093Fp884xHXC33yZHdyUKUxXxk27Ml9NLSO+0xh5kpb\nV1s9H09YE3NbERQlUFFtDFP+kABBHdZkES7ew8hTyI3vpe0UXhCQQuIgvUbe\nsA6vwhx3yNyMqkkTVZkBSOvcwvL5ZnSGFPq+9Jk0y3WNlc5q9vUlgXgyZ1cU\nbk3Kmzr2J/JzdGYCV3VtSbWsoKOSM/+k3UFG/0y4vdDnRkBnLsEQIxEnI7ow\nzuEw/HFWrF9/+vxupFlG2/gvUe5s8AA27CNp+WGAEgb4N4sY8wDtIq+Kk3N6\nLcaAIrHne6xZqI/hLCRxuj4lWEPkPCDVPu0Eq4LmVaPUA08EmQ95/m2CX4eB\nc3ALkRBgdiSBkTacfge/Z7QkWN+hkMjuBmFyDas2atFhDJ/LtXLD9GlDO9TT\nyJwmwd4dxde33GjjYDVhud+vWC0XoAbxryedQVnOGKUb2DU8/AzwobJZgdBR\ngjZ0A+hu31cUkggvL2p8lzS9Alw7rsWeViYMgxUW7folGr09riL8ONk6+pzg\nJKp/M3A72ioDl/yqddT40Z7Co0tn99Xlu3lxVXQHI7aKYn0Dumt0p3I7nPyn\nhlj0AUy/yDnG+8cU4AZkdjUFs6Qb7uJaiePr6o3SPdYleq3rJBpjBfOR3TAh\nkwer\r\n=nWbm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.0.2", "jest-haste-map": "^27.0.2", "@jest/test-result": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.2_1622290074988_0.33623434537263464", "host": "s3://npm-registry-packages"}}, "27.0.3": {"name": "@jest/test-sequencer", "version": "27.0.3", "license": "MIT", "_id": "@jest/test-sequencer@27.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a8632b86a9a6f8900e514917cdab6a062e71049", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.3.tgz", "fileCount": 4, "integrity": "sha512-DcLTzraZ8xLr5fcIl+CF14vKeBBpBrn55wFxI9Ju+dhEBdjRdJQ/Z/pLkMehkPZWIQ+rR23J8e+wFDkfjree0Q==", "signatures": [{"sig": "MEQCIDOr9/qC4fQQA7G9ert0jVsvbDBWfD/xLJBgXD/3SIo4AiA1fSbMPVCow3UakvRr+xBuv+LPrxu6pr7tBCcbU26OZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsn5ICRA9TVsSAnZWagAAL5IQAIiEU2eNft8V0d15/FYO\nR5rHKgBSaRjZIs+YX/No14dG2leUBF2gnSgcEmp4g7h+ox623Mo7LQ1/VomS\nXvtvkNFe18GoorDcwSjioMGOw6ttLDFbBxFUpA1tCWWsCBr+QOQX+fanmUUN\nYpTAnq8OVItxuus8iOjwR0hur62h4P+zSXVZQNIl/7QpcDNmxZqOgByk41IB\nmENCnid1hl5aTt3sd0luDgKuJQTjLgiFkAxZGVA6o68mZdxA4eCYmCOTaTOd\nWnSkBrFLImcohXeTWDJIrHip7VKQpIGDDZFalpf+zGarglCjOmqkK4VOa4qo\n1QWDASseLcdhqubnGnE8gBoBK5PZnuJhLiCacZKjG0OBgEo8yc9ZSYTq2/W6\nCV8RQWPAQ5Rf9gZYybP5yrT3uZsoWfg3SAxCJOMuwNIoxkuLCgLOfn52V/7k\nEqEME/aYUhtHJme9qnaFKpYGnDlawFon+Z2ahamJY2af8pbKV+WL7yRPbTWA\nZZB8nbLJBLcXSNt/ClIGosf+f+u4760q3YxgzcdFFkD3RDTyDNG9Eu9fzoKR\nczEl8A3dMXUkWOc5gYprmWcaIEDLLmaKwb4KZgn/pn8shTTWWdDaN2UD3Ln+\ndq+7Qkuj15wTbHmINRA/9uJrKxE0hOOt7kuejaqfEd28PilU7m6Fs8jgShe5\niBHR\r\n=nwFO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d10f645b04ba9febb7308392e635c0351d0f027c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.0.3", "jest-haste-map": "^27.0.2", "@jest/test-result": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.3_1622310472212_0.830323390205796", "host": "s3://npm-registry-packages"}}, "27.0.4": {"name": "@jest/test-sequencer", "version": "27.0.4", "license": "MIT", "_id": "@jest/test-sequencer@27.0.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "976493b277594d81e589896f0ed21f198308928a", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.4.tgz", "fileCount": 4, "integrity": "sha512-6UFEVwdmxYdyNffBxVVZxmXEdBE4riSddXYSnFNH0ELFQFk/bvagizim8WfgJTqF4EKd+j1yFxvhb8BMHfOjSQ==", "signatures": [{"sig": "MEUCIQDuUf0SnQP5XyJql+j6FqqeP7VAGyAHMuM5OHe9A1Xm6AIgJUrxhtLmQXizIPW2kPlRQ386tsZMnvfelarxeYB1ciY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguJLaCRA9TVsSAnZWagAAn2oP/3IpyTlSW9CHGJoHkwgN\n16bnZdrva9MkfcikkX68qusTU3GdbbAnHw3AI8DvEfH1pO14786PFhObjU8f\n8OJythxOADKoqnYw5lUs1AQc8Ol+c5e/6mWknwdzYge/juiyRhnvcjhM1iX1\n20OermO7v51swOXlH6QCDZe1BwHF1Zg/5ojSOnJfegA4LmMD1+fK6xLhUgsd\nzICNENxCvb0Kz0/ssMRSzcKW1mtxVQMmQ0wHraJl3NcF5AouTMbpYmyNYsPe\nAVw66RgxaIrvN6RU6ZlxNG4CD8phDjceHFIFPLPJw25rFJHwEgzY3l7GCjQe\nFsYqDMbwGJ56NgB7x4yxfies0S5pYstqaddTm7Rrt40CuNZZKXB1wKxZtvCb\nF3kN6KMY4QxBPbhpILBGtZhqM48FoVppv9TFYHuK+r4Rp0IDK9Hd3XdO1mcN\nMx862caoPv3NM/D6zrX0vm0/Tr/64d/28euMvR4igp0n0awCESFRtkjTtwMs\nNIvhqMY3MawlxDWMBsDYEzdTCFK7ZpIxEyFgk56cZh/fZ3EXaHEWhjBhRth3\nfGCL33fdZoHgY7O+3Q1hjnUQmu6JKOHb5HAGJKJyoYQ9weDFTGKTmeCf33D7\n6+5h8eCZbeBzB7V2c5/XMGZNZyS4XQ8RUcW6vtqBlt51J6OYQikmKMfaUWqD\n/nRZ\r\n=yp9n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b29acb355ae23ccdb2a6248fbbd933fc93f320b9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.0.4", "jest-haste-map": "^27.0.2", "@jest/test-result": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.4_1622708954657_0.2552893985101452", "host": "s3://npm-registry-packages"}}, "27.0.5": {"name": "@jest/test-sequencer", "version": "27.0.5", "license": "MIT", "_id": "@jest/test-sequencer@27.0.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c58b21db49afc36c0e3921d7ddf1fb7954abfded", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.5.tgz", "fileCount": 4, "integrity": "sha512-opztnGs+cXzZ5txFG2+omBaV5ge/0yuJNKbhE3DREMiXE0YxBuzyEa6pNv3kk2JuucIlH2Xvgmn9kEEHSNt/SA==", "signatures": [{"sig": "MEUCIQDN0FX6qijy3qsvmsqjR2GhrhvpDL68ixh9U4r7wTZhrwIgKcCz/EvTauYKlG7z54h6KumAuymLDfTTGvX+W3C0KAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cVDCRA9TVsSAnZWagAAQ4YQAIQN/DDlCfaWjQd9i9Vp\n149zMclcwbJFXcRddjElKeIST9hxbnbnXxto6817H8xgXHogQO3g26N7djIo\nKiNLhiRBKwIz8vpxduymH/hq0oDQnYHTt+l14woNjLjTxgQ7BT0CktXucJsT\nnxIHJ1/LVUQprYqWX+J8UrhuPWYPYblwlmuSNEgPmqF2XwF0qpf95xSS91Rx\nrKhK+caKvvOAilLW6cdrjedbhf6rQeyEd6XYV4N45Sn/mqc48DOUwhWVCXFg\nBaH5JNlsl5yRQHGmluk9kOnAqrtGi/dEbu7+1P45ZDLu7rUQZTqDyzyprIdx\nZVbzvBDzs4Vbu/8yol8Y4TK7zSiuX2UTkeMM55wOQXGHLnNo/ebqxQaOdnth\nzs+94rujuXiVwM0YmxvvQ8flaIZmUwgKJ0vvj8ou2ZD9pbprqSRdaUj7xW7V\nDVWEmdjSnSIjJT/yqfpXfXhCUEF8auwan8uMYYWj157ktVjJwhiSy5c0x/8M\nH6JHRvdOd2EFaR20xGHM6qJo2C1ha5KTADq7/PfT2CvJvo0fOVgTTS2Wf0u/\nedoQYJsQVimmxLFifEiX/JdHMU7LSoPCqb/ZdeF1mFI50t8TV1xsm3oAzzXj\nm+O1oAS5mloG6wRNgh6z6izD4H2tKXgR/w7p17nZmYeCk6JIz5PUDR8z0d+c\n2BOf\r\n=IC8M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "dafa4737dc02887314fd99e4be92781c9f8f9415", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.0.5", "jest-haste-map": "^27.0.5", "@jest/test-result": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.5_1624360258774_0.041544163599145145", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/test-sequencer", "version": "27.0.6", "license": "MIT", "_id": "@jest/test-sequencer@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "80a913ed7a1130545b1cd777ff2735dd3af5d34b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-bISzNIApazYOlTHDum9PwW22NOyDa6VI31n6JucpjTVM0jD6JDgqEZ9+yn575nDdPF0+4csYDxNNW13NvFQGZA==", "signatures": [{"sig": "MEUCIEoRt8Eu8uWDC+wcmDP0MInZdV3uq0j26Dwem0UcZ5MuAiEA6TxYUmlmOB/q2XSW3klzUZVloSWoxGwLTLEv3o7T4LQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gF7CRA9TVsSAnZWagAA7T4QAKGQbTG95O2W62K//q6A\n1vJsbFRxe1SZbj/YLV1dyJ0mKslwNS5ExaQ12D9zvA7ESTFvD64VJFtEgH8q\nked43viy5MGRjtCGbS2B6aCdO23/MDOXxSOcsMNYid0hrcLpUEjR6bhSW+pf\nzbJPm5MDWW0pZc3dXGTpDTjZ5OkI29Xrvp1DyzbA/GC4DU7cDX4y92IUrpJX\nfbrwvjarg59b+T13DLASy3rpwY00MjbF3/QsdmolCIAd1AuZl5wQ3mi0LF4P\nhDXh+23DRc6f6dxs0W57oH0Qqig8lMiMOJddgnGXMSyELWlDQZRcsAK7gQlt\nUgzOcFh2LSSzowsG9/bYWvaztd1HE/a1eJvUCrM3VYlijOjF7/RGu8dDU1eK\nzIUgC0wHYok4DGbOl5aHqEZDfDsUlVj6MtpD+dr/lH8BO0pI3+1JnWseE+q8\nDg8bsVLr8J6366/HPCF/tqgc0z7KXQaJ9By8RM03xbZwsubgHGgLbJ1ZGo1Z\nZFORbr16gpkwloGdipnBeAgUnJSKCf0FOygxdXbyLbPeW4Y0eRCFVuXwklaw\nUcZK7vhMNbHUBs06MxUPo3E/KtaiwImhgVKs7UgSfUCHOwm5y0f7Uof0UyFk\nRQ6PiryCCT8vRCVaxGRuS+57uO7u7TcQLeg4Md7Jfhoq3EkzkfiAZ1n/SX0J\nigfv\r\n=CU3a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.0.6", "jest-haste-map": "^27.0.6", "@jest/test-result": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.0.6_1624899963624_0.14976470917308737", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/test-sequencer", "version": "27.1.0", "license": "MIT", "_id": "@jest/test-sequencer@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04e8b3bd735570d3d48865e74977a14dc99bff2d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.1.0.tgz", "fileCount": 4, "integrity": "sha512-lnCWawDr6Z1DAAK9l25o3AjmKGgcutq1iIbp+hC10s/HxnB8ZkUsYq1FzjOoxxZ5hW+1+AthBtvS4x9yno3V1A==", "signatures": [{"sig": "MEUCIH5yiGi+r4hDuOov34Jz/s66BfN1BjkniTL7yi/pFiQjAiEAvoQTIcLPjoxPBgw1qvLZq6lRk+AMhaecrKmu1rgyepI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLejCRA9TVsSAnZWagAAGxsP/0s18G7RV0gxS3DkOE+C\nkOCnGvg0BRG89Iexs+iE2EjbS2lchchAjF1gw+GDAJzxtOiIrF8ymKI/Aq0/\nPizCfjH0RnrEAj0MyAaSFZXGxavLsaWrlzXLsNWEIt9lLs/y8zmCV1EGLEAN\nc/PE9pjVBdtix0Y+PJUMiHbHEml1qTyo866ew/aOHX4DoqydgNpplREws35G\nUpXrj507P5B+HwRZuz2lD9xOFKP31gqp63O2DhXccq0yHz67/XYu9b84gUov\n974dEPYidhLxRxcslMjMY+2EKbP0nIB8sls29flys1yBriXcsEoeDuoBc/0q\nB7SyyOuy+UuzrCCvcvkaXCn+eImVHgA0DTc00OC/ckdO0EyoTauKjUB83hk4\nJU48Unh8WSdh0byEZZkNBENhR37YAGzO7FbVMYHYod/1IOsf0Hpl0nn+HipD\nl5UDiGBIwzvDb9uwFQVV1WEHlUrk8vLwIJVjeEKJp/ZnHZieSs9ZDHHC4Pm6\ncqbtgj/LRkqvV/N1OfHSUpKLrUMlf8g2CFBKkZbpUPY6wace2NTaea85yMss\npGEKAXmnKmx718MYr0Yo3CpGyUinrQ4423KSHXlq8A+8GiW3RAkl7Cs1pJsU\nUl3VXbthBpKKjvlEk5UfSNiBvIdZqx+luqzofY1dnhg1Aqo6WtzJgGx8X6/O\nflTz\r\n=3I2a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.1.0", "jest-haste-map": "^27.1.0", "@jest/test-result": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.1.0_1630058402269_0.5420760279938843", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/test-sequencer", "version": "27.1.1", "license": "MIT", "_id": "@jest/test-sequencer@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cea3722ec6f6330000240fd999ad3123adaf5992", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.1.1.tgz", "fileCount": 4, "integrity": "sha512-l8zD3EdeixvwmLNlJoMX3hhj8iIze95okj4sqmBzOq/zW8gZLElUveH4bpKEMuR+Nweazjlwc7L6g4C26M/y6Q==", "signatures": [{"sig": "MEUCIDT7IiLTQPGiPPwzCMu/cvb8WaWt9Rzyh2amWnR6kcgHAiEA106NcuKRiOD7iTKzl6JL68MH34NJKVZtacJYDFJDvNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIySCRA9TVsSAnZWagAAjR4QAKO9/GqS5gGQkaRrhVKj\nZF80KhthME7t5XZMV+XpUQL/Ax6DF/ilpSmpJcx4PGExZ5Jq2XlRDZ24hH0k\n2hxdGH/4JfnYIK2l+Xyjt1DeQSW5Y/+Qmu2iyaog8sewFzcrhBZuzMA/iC0w\nt1y8K7WEuFL0xlaUnOQzMYtRzQzOTq6XWN3V5N1nEC9dpusPN5mdq7bfeFP2\nx5UAo1OovGV3Ws5wbLX71I9Q4eYKtihjmDV9bbhPIJjBhZcAsXji4IKKNX2A\nWzbTpzhg/M1zme1wc4fJlKAODs1kdRS743xinyUs9YWTNMKa3PFiz83mA/8s\nEvU3ezb20iSSI3bHbsClr6lNKt2yS2qiftRRY0GYTOQwilKTuhdRacU1NUnw\nA4BriSAXfZm8abPG+4gr3tJOLKQ6etniovcjGgdxdwbFyJEtWtCKW6Qev7ce\nVPmdr2/fHrBZESoQuCfT/QF1GfpXBWHY5J51+n9yc8PsXxD/GKwov0GnttXy\ne3OknvG2VkyyTK1kMewjoaURPXKmSdSpj34nb3vsVjLA869VKdJ5EQz6lD+4\nrqdHcsP9yNWgiyiKxypw9yAubONbsioZsqAVUe1XkJB3Dw/gBbwqTmWZXcbX\n8MtT+dZFMCWi/RNFi/CDG3qei+fitpCMD6y0UARlaTa/iWnKyNWgOs9vEqgc\nzS2A\r\n=Aye5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.1.1", "jest-haste-map": "^27.1.1", "@jest/test-result": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.1.1_1631095954197_0.15554718248726918", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/test-sequencer", "version": "27.2.0", "license": "MIT", "_id": "@jest/test-sequencer@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b02b507687825af2fdc84e90c539d36fd8cf7bc9", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.0.tgz", "fileCount": 4, "integrity": "sha512-PrqarcpzOU1KSAK7aPwfL8nnpaqTMwPe7JBPnaOYRDSe/C6AoJiL5Kbnonqf1+DregxZIRAoDg69R9/DXMGqXA==", "signatures": [{"sig": "MEUCIQCPAkiXvmSo+eJvPWFhrxydd1gdfxtyQqsqi2coI76rAwIgd+cUkbSggBzQ7P8yUkWyvjxqvyEXu4tXxEbYLZIrhA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaqCRA9TVsSAnZWagAASfUP/A9saFy9LB3zwn8iOAMQ\n7pOF9mtem3720MFsiKD+T++eu9K5Gcd/49ADR+zSETmwds4nGh04R4k+IKfN\n37g7A/hzNbLeIrPyjMXieNonthHLZSYbLNNt48VSulGjEvdzr23mRTxGP4J3\n36fOezGeiYSx6td2Bu+NFD1uFhreZSLyE4938gIfCcx9kpkmmlDtVrjq5IG/\n2ObUmwpFeDiWUBbDu3PQdyVOFtCA1YzaXpS1PRaNwZYnD5Uy3bA7qzzfJS0X\n/LWWTKB037lNnEHhwrTyFY3+VfLweBLYE14CERgZVdYI6ndmnj0Bngdjtnf1\nj2ugr4wXCWzeXWUJkKN/sbO6MyCE68ECODR12p/msts825zZAaEkhYLTvTsn\nHD5cI12q2ZglpeC1/8BgOxIMzKqwTeJllec4VJi9c35hs9YNH8L3/YuAEhec\nan0QJvVGInHUUv0fB+EUl1Sd9wGjpMjUIMqZAL8qPJ844u17lbjJOPeDZIlO\nPplTRlca1fpiRXI08P5X+X5hz6J1A4nYeak5xPFOBWeKBEifEwB4H9bdUv4p\n1i1NafMT58knUIWzyAO9HYR7uh71kyVUxuMYfKF+JeawuHc3VcPDy/mU0X40\n+2DKbkmh+h06unsDkTOVY9H4/L2oEkVrJT0Ufnwjb0Iae0IIxS6DhKa3edXB\nKeSg\r\n=wuZI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.0", "jest-haste-map": "^27.2.0", "@jest/test-result": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.0_1631520426786_0.24395644941270955", "host": "s3://npm-registry-packages"}}, "27.2.1": {"name": "@jest/test-sequencer", "version": "27.2.1", "license": "MIT", "_id": "@jest/test-sequencer@27.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1682cd3a16198fa358ff9565b0d2792919f36562", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.1.tgz", "fileCount": 4, "integrity": "sha512-fWcEgWQXgvU4DFY5YHfQsGwqfJWyuCUzdOzLZTYtyLB3WK1mFPQGYAszM7mCEZjyVon5XRuCa+2/+hif/uMucQ==", "signatures": [{"sig": "MEUCIQDOSWij/JL5JCHbY9OAK08GiITsiUd+hCMspsZMuCdLDgIgeQ+CYThLmXCVDu2KnvQZS7JchgQeSimwWWyGzQwOgPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "50862b410c358107ec893cfdd9bb9a689ad8e622", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.1", "jest-haste-map": "^27.2.0", "@jest/test-result": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.1_1632144485447_0.10225300892627942", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/test-sequencer", "version": "27.2.2", "license": "MIT", "_id": "@jest/test-sequencer@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a6d735317f525741a5913ee3cdefeffc9b0aba6", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.2.tgz", "fileCount": 4, "integrity": "sha512-YnJqwNQP2Zeu0S4TMqkxg6NN7Y1EFq715n/nThNKrvIS9wmRZjDt2XYqsHbuvhAFjshi0iKDQ813NewFITBH+Q==", "signatures": [{"sig": "MEYCIQCEPW58/1uTr06WzNuY2Im3du/5wZTbCW3VpxpS2J7k2AIhAJ9CiRmwy2asPcd0hBaKlw4YjtuWAbCuZJqBlpfuAKWO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.2", "jest-haste-map": "^27.2.2", "@jest/test-result": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.2_1632576914900_0.3196030035816586", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/test-sequencer", "version": "27.2.3", "license": "MIT", "_id": "@jest/test-sequencer@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a9e376b91a64c6f5ab37f05e9d304340609125d7", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.3.tgz", "fileCount": 4, "integrity": "sha512-QskUVqLU2zzRYchI2Q9I9A2xnbDqGo70WIWkKf4+tD+BAkohDxOF46Q7iYxznPiRTcoYtqttSZiNSS4rgQDxrQ==", "signatures": [{"sig": "MEQCIEsjP+jXguGEFq5P7wqnf+mk9v9eUFIslkQbUpa0OxJZAiBuW+lTBdFbRHjU3Fzq2GCCN7FbICqtv6PEOWgy6W5WQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.3", "jest-haste-map": "^27.2.3", "@jest/test-result": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.3_1632823891674_0.8049811172863028", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/test-sequencer", "version": "27.2.4", "license": "MIT", "_id": "@jest/test-sequencer@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "df66422a3e9e7440ce8b7498e255fa6b52c0bc03", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.4.tgz", "fileCount": 4, "integrity": "sha512-fpk5eknU3/DXE2QCCG1wv/a468+cfPo3Asu6d6yUtM9LOPh709ubZqrhuUOYfM8hXMrIpIdrv1CdCrWWabX0rQ==", "signatures": [{"sig": "MEUCIQC49rRIhgMOPWHfka/htZ7CYtDIBP0iW6uUKc09lgAJpgIgbPjjBsvDwAQDwvBEXFBIPZDhMa7FyQh/ZXdsKqHyEtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.4", "jest-haste-map": "^27.2.4", "@jest/test-result": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.4_1632924302841_0.4901904311925862", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/test-sequencer", "version": "27.2.5", "license": "MIT", "_id": "@jest/test-sequencer@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed5ae91c00e623fb719111d58e380395e16cefbb", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.2.5.tgz", "fileCount": 4, "integrity": "sha512-8j8fHZRfnjbbdMitMAGFKaBZ6YqvFRFJlMJzcy3v75edTOqc7RY65S9JpMY6wT260zAcL2sTQRga/P4PglCu3Q==", "signatures": [{"sig": "MEUCIQDsPTycQ33bIBqERJMQR6pIcCbRqDOC00g9mX2vnZsLwAIgOyOV0mCBotrGhrgx6GrzO4BEg2D7QGBnmmgzaHp6gKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.2.5", "jest-haste-map": "^27.2.5", "@jest/test-result": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.2.5_1633700372962_0.3071630746820342", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/test-sequencer", "version": "27.3.0", "license": "MIT", "_id": "@jest/test-sequencer@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac245f4f29ce7f81ae5afa441e5bf7bbdd342ef4", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.3.0.tgz", "fileCount": 4, "integrity": "sha512-6eQHyBUCtK06sPfsufzEVijZtAtT7yGR1qaAZBlcz6P+FGJ569VW2O5o7mZc+L++uZc7BH4X2Ks7SMIgy1npJw==", "signatures": [{"sig": "MEYCIQCXeCdLF3g7kxVWbP3rX8PJPBlH/QmjjSb03H4gPZL+4wIhAN5U1tlm6cBmzEYxq8XSCHM9nf+2k1URysgmrUyPcU5i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.3.0", "jest-haste-map": "^27.3.0", "@jest/test-result": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.3.0_1634495693263_0.8166508522277245", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/test-sequencer", "version": "27.3.1", "license": "MIT", "_id": "@jest/test-sequencer@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4b3bde2dbb05ee74afdae608cf0768e3354683b1", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.3.1.tgz", "fileCount": 4, "integrity": "sha512-siySLo07IMEdSjA4fqEnxfIX8lB/lWYsBPwNFtkOvsFQvmBrL3yj3k3uFNZv/JDyApTakRpxbKLJ3CT8UGVCrA==", "signatures": [{"sig": "MEUCIQCwxDRW6+gBH7cLct3t5KasVM6IbZFX/r/7DDAVw/5lxgIgYVsapjhvDYhB3C3q5SEOQPkVZ3NFFfy3oLBTrX2LvZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10421}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.3.1", "jest-haste-map": "^27.3.1", "@jest/test-result": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.3.1_1634626660367_0.1311123631697506", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/test-sequencer", "version": "27.4.0", "license": "MIT", "_id": "@jest/test-sequencer@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c42e2bdaadf5d197a107bd36a6316320cecca651", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-yKu+sjFgelc5zUf0kcbbsO86qV0NIMPyYFFRaWTaEsq+j7aueX/Zev+NcX+bm7BCwCMWeK7V5AUE6HUOblylHA==", "signatures": [{"sig": "MEUCIQDun/Qwxc+3Sp9INqG2VgKR11set0s5r0YfZbxY0dCu0AIgbzj4rNwnh97vtGrrxmQpPP7AlPpKphc13GJGZaxZSOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNetCRA9TVsSAnZWagAA74QP/0uTnup7ubcXWMwA1MiU\nL9WMIr+/IqQoaxQ5ghweRPA9meQ0DIWTiN+/1NnDyrjn7VUYo2IBuSCMImPK\n6shsw023HYcCbPXLwaGDVH+GNS6CEPWIc/JAVHXVwPzfKP+d5pJgN73mRyiG\nj4fXx7shniUpKrb0O70V8fUqg/v6hwdctI+ANs2qSHBeuYKeT7aJg3GuDuNf\nQ/5awFkX3QYE3z4vLot7q8WJ3nSAHBBoJlZpRQHxHcAtJjxjxTt8cqIGmYsg\nXqTACJC2acrSqjK0WpzB5AN9YYrcFMQdAdIhwk02PLCWb8oZ9JeyB/X3OGOx\ngxlGVGRYVEWHX2SFJoeRh3BlAVRmPLRz/9hhMr6IcIEcPRTsfputYa8M9CK5\nw33Lxfb+larbQUdfUDdn/s3Gi7hYmBp+oX7Rr+0kq8lAhFsTNkakud8y8Gz9\nIJt8KFMT2uXlGq8lxIijeHm+vCTtzBu8PBVWh0JCJc6D2l6+/MsKLGa3b5wH\nThBPfb5mzsVAF13cjvHgq71ZB0sqHhrXv4yUhsRVIk5xePLzbxMqyAZ2UqUb\njNPiw2YNiYO205li3tWmrg5f/jbsg9mpY2n58/rwChALXqttxJRmBLAzm8c4\n3kHINy3F8kZHWy0WV47/lzTt+kPWlYC0bI/odVxXWFDQGnSVk9wrqWyg6Uzu\n1zm1\r\n=uLIk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.0", "jest-haste-map": "^27.4.0", "@jest/test-result": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.0_1638193069794_0.49915670705562776", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/test-sequencer", "version": "27.4.1", "license": "MIT", "_id": "@jest/test-sequencer@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0c99df361f40953bccc27e29aa8fa1758fa0d80", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.1.tgz", "fileCount": 4, "integrity": "sha512-YX4ccvhazkrduLGvJPXBfmVLrhoWPKgbbDtHm623nrm5znYOvjjmMZKFWDRXCmwVgJCQo29LNxlZW6jJh2QtjQ==", "signatures": [{"sig": "MEQCIEp7t+k04NMr4uCMHszlgnbPz1T6ZlICrhbHWPjIi8bWAiA6QP5e8mTi9YHIZ6m1W1jgmemA6wQfGKRPPPFTQcE/KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeLJCRA9TVsSAnZWagAAea4P/Rq9LtRNwmVY2GDZw9qN\nMBsLt0LN7WYeUKEMtCBsPCjawbjuYUwIDkFMFayYsF40rSTDUtjagyPjaxZK\ntcdOAwb51TU0ZTc0c8jHOSYWb7yg3yg6ZN0ElYk4QJZFcgcsHB4hGn03bjQn\nx3qBHOeKSXxrQn/WumUb8WnKGYLfiblu3wqc359voA8+f2oJF0BWvqrWXApm\n5l9fIX8PJuXUOUSz1q+F2iPSPzIOPh2llmHPV7+XQX99SCScdB6JszOSfC5k\nROLMQnZW01fg3lk2h235N20iyPH105TObi21jvZjRGL1piglcdfiksw1185a\n28xwJ0e/grLXf1inbZ75I666u3yVgimI/zkjQejT4lbm/wWuOYok6qppPX0j\n1Pam6QpIUvdo5qPVhvv50Nlf7cm7TFweeq7CIZVL7MTnKWDG2gFbVKveJemQ\nUJt69YP7aurianz3oLlJp3mngnAeUosbTXr+6h7QtV2bJ8vTHujr2grs+zEl\nnX9+MZgQERy710NCkJrhijETHVAGFlHGljUEqQFhXPNC8THKDQ3sEsLbk4eV\npGqLRPvdiwnXI0unLQwkP3sDQDsM4OMnPBX8JVt7KFZxRw7/vDAaJ9uP348S\n8BLUqK9uZACbqeDCjh5IMGCRM66NLtc38iHXT54MrXIZ8MIkyD8i7cCEXQTF\nFmjV\r\n=XAI4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.1", "jest-haste-map": "^27.4.1", "@jest/test-result": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.1_1638261449543_0.8817472639373762", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/test-sequencer", "version": "27.4.2", "license": "MIT", "_id": "@jest/test-sequencer@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "94bb7e5412d59ae2a8a4b8f9925bb16b6dc82b4c", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.2.tgz", "fileCount": 4, "integrity": "sha512-HmHp5mlh9f9GyNej5yCS1JZIFfUGnP9+jEOH5zoq5EmsuZeYD+dGULqyvGDPtuzzbyAFJ6R4+z4SS0VvnFwwGQ==", "signatures": [{"sig": "MEUCIQDthC9M2Cuu029v51eLUTjgayNne45NR0yLl9VkIyMBoAIgMLmZD8xkreCy7PSqskmBFLyDL+uj1oueGWxK7DUVuMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDeCRA9TVsSAnZWagAAtCEP/1Rr01+aXjmgJ6xM2qel\nN0S1ThiE3rvgxVtAWsZ3/vyChUn4VF4F8z2PDiEnc7gErC0fpxaomHLWJX0U\nDNRI/RHAnPfKBAwOKmFH8eXpsBpSpOqGpNaBU8N9M5dbJv0IBRIhKkZMdOJS\niXMV9jTV1i432jDsss4pddg4UFnI7buuNpegXB4WWScoROxbWOBlcGC6KZbd\nql4aWo/pV6c2S3/aOIXAtRrVoWjTbZi5bmyhk1x5DeYS/i0hqKc6D3A9rJAS\n6JpuxjPutnpL5662yT1RqX/Jf/eB0REWr2uZNGSmOBqamNPNWSI7ePhyXPS6\nmqKPxfMKI7d4g7rnsTWnzw6zgxq4ZeEfxtETxC2TyTXBvbkdgMrfg1pERYTQ\nmS2iWFkyq2mBUIV9BUKvEHrSqP/JZNdKQWuCAyrhZa+8BITxDeI17yYs1Q4a\nf+7OV34Ud/AHJ1xiCLOMua8nuscdUwGGQcLp6HwduwZN2GeAJ6aJvsPlUqeX\nLybgHNhvcR64ZzWs+2KXsFX2tlJVuLJzHyTJT4edGTDBBjIj/GfSEtNTnSJY\nLjIOtx3gBbiup9+0tGIYo1eg1RFaUIXbJGH8nzbv9sFRXZ8cPfW6GzojlupR\n0BtYmi/F5DLDfc20Z4aJ/3NjEwY2N4IcOCFiPDc5qSREFi0zTnWQIdhWVMQQ\ndQm2\r\n=ieXb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.2", "jest-haste-map": "^27.4.2", "@jest/test-result": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.2_1638273246777_0.75373066814948", "host": "s3://npm-registry-packages"}}, "27.4.4": {"name": "@jest/test-sequencer", "version": "27.4.4", "license": "MIT", "_id": "@jest/test-sequencer@27.4.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "60be14369b2702e42d6042e71b8ab3fc69f5ce68", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.4.tgz", "fileCount": 4, "integrity": "sha512-mCh+d4JTGTtX7vr13d7q2GHJy33nAobEwtEJ8X3u7R8+0ImVO2eAsQzsLfX8lyvdYHBxYABhqbYuaUNo42/pQw==", "signatures": [{"sig": "MEUCIQDUE/AshmLlyIcssAoAB97pVyN5M4T2BR4q7tCWfzcwdwIgHHLWGe4oK6rJXWBihtZKI2ilYImbza1vDCkyoA5312Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhstrrCRA9TVsSAnZWagAAt+cP/10hNhlreRTLuqxlXK3X\n015MA73Ah03I54WL+y54DC7SeMRaudX2eVi121w7ABe01HP8oYauxVYgrOoG\nAXhP7ew9HIMnI/7ppBdlC9ynKiiBegwTMvj5DxdPxilR4/7cxJWCeUg3pO/l\nACQyCNCb7iDj1asXcnHkUA6o9xIQ8J2TdX8PNDb018tMJprVKOcY238Il6Ru\nQf14m56GGjR/m0yxG3wKR2wVx9pASyOBX8XFnPmbBVp/zvIWYcrXg5Rme6GW\nO4ncdxRfKlWrFecIYGo9u33B0G28H8MnaINSqqtz3kDQwLTr6D+eiv0aO1ZU\nKchnYnTMyAGWqPNnGcjffGp1juI1D86HpQt3LhiKTobHpQ2qXT9WftQyt9aP\nEfvQ/LvO3tErksYEuR6p+C7578NXJmt2Dwry23TfABuVlrOjvC2+KsvHhNdQ\nHTjSeNWNiJNoxCW6pe30B2Lg7WgfDorwwfi2BKjPp28GBE33nKR0Do1FqXjZ\nUevDSeOClY2UOn3folUXQU0RoJa0qg1k2lxe5PWLk2jVImVag485ZpYJFoqG\n5tfuUr89Uxz5l6cRM/ppy8auFxiM6dY8kZHkTMeOLdS8yfs6RbYq2q2lyL5t\nSX2kUU1Zq/JIoD8Gf720mT3+qgQdXTBgx1K2y0DgHv0nNbNCjei2CxmyX3Yb\nLeL3\r\n=K+uI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e2316126b2e4b9b4272e5a0b651c3cb5b0306369", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.4", "jest-haste-map": "^27.4.4", "@jest/test-result": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.4_1639111402966_0.8286642925946541", "host": "s3://npm-registry-packages"}}, "27.4.5": {"name": "@jest/test-sequencer", "version": "27.4.5", "license": "MIT", "_id": "@jest/test-sequencer@27.4.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1d7e026844d343b60d2ca7fd82c579a17b445d7d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.5.tgz", "fileCount": 4, "integrity": "sha512-n5woIn/1v+FT+9hniymHPARA9upYUmfi5Pw9ewVwXCDlK4F5/Gkees9v8vdjGdAIJ2MPHLHodiajLpZZanWzEQ==", "signatures": [{"sig": "MEUCICaFOhv6JOl5wMAQ0R5huvjKOX9yPFhF1DHPoMCYRkMbAiEAzm1YiDFL5mbICYADJX32Xi0Bt25g9yXqTS3efyZh0O8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht6DWCRA9TVsSAnZWagAAgBEP/3MWCHwYmBZzZXvA+OH1\n7fBY214lw8Ieb24qXiwdUxF2XQaVj+sHBSX3q7av+C7CvkTzc/3u0VbA55xx\ntdCKeQlXQKy1g71BjC7ed19kUUTnwa/ZVDAYj8/sKnUVg1BU+2ZtA+oORBuh\nYmieuAZmw5fB1EIeHRajNTDcTo5tk16ZsNjbCJmRM/Y+ZdWLsU3hih4d6QXi\nbXdhd1fBg9eOuY6AWB6AojBVqust3a6Re1YFIq4BcibOQOZ7wcfmhqaz4otA\n4WdIe4zG9r6AVGFSFe+IDLf3Q8+dZZcqNctjPTlPHaaqfsiDuwDpp7SiQIOa\nznqa/izEL6DHp6q2zuIXjlYs/nPhW/I5o4iOyyvcetLlrOg4MacfmdaYBIZG\niakhVr6kD6w0jCg0RJ8pymt2It8RTwzzIMdm56cgpcRFTR6c+QxnSdPu1evT\nUJQoVpxVLU4oJsGAmBbowMEIxRmgQve3DohYYPKf68iPQMto2vZDexRoihwJ\nO9Qlrj1YhlB5q0gX+uiZbBdR2MXKVaNyzfKMKXhqa2JnqqXiBHMbjPZs6Bd0\no2tWS/BotdRlK47fp3P9EYUI0eTisBR+TDhRMN2e6bDZgLQuJCoICjX6NrpB\n6RNXBffWKHwHn3VrksYPUfdfGnNnbyoGcsSAEZgpfbRRSQsMXsbX90h4x/X+\nuToe\r\n=rBd6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "73f3a5743b9e5b16c9b7a69e2705f07ba7a010ea", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.5", "jest-haste-map": "^27.4.5", "@jest/test-result": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.5_1639424214554_0.3686025839132929", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/test-sequencer", "version": "27.4.6", "license": "MIT", "_id": "@jest/test-sequencer@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "447339b8a3d7b5436f50934df30854e442a9d904", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.4.6.tgz", "fileCount": 4, "integrity": "sha512-3GL+nsf6E1PsyNsJuvPyIz+DwFuCtBdtvPpm/LMXVkBJbdFvQYCDpccYT56qq5BGniXWlE81n2qk1sdXfZebnw==", "signatures": [{"sig": "MEUCICjX3fjq+mJOuzpHdU6ybEgUDfUQN44WRD/+zLXp9jOfAiEA8WIm9gZd/KzN3wzuTzLl+NxCeJece+2NG+IFMxuHkzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJgCRA9TVsSAnZWagAAAjYP/AtwVkf2tebbGCRAmrPc\nowfRpX6gtwfLm3Dn/BPBlcB/ws84LYtmtkDCl6b5CWZ7rXHg+u4MT21lEqQG\nJjMFRcFbuRTj+5iWHtf8IGe/zafVC9cYBJYBgKtTvFHO1LVqQcRT4ns4I/aL\n+TuKlbsK6m8/YyLGYXBoQBXSF6OB/bPZ22OQ6imLRfomyJX/KvY6q7c3LsVL\nFostvW9MoO+8FSn7SDEbztjgJ3OrIJANSTc2wcOCp6oaaEelXWb7EUICecZG\nvTddz2Skgla/x4giVaB/oU7dScjjkQk3LuSiOAS84q4Mvn8C+BslKw3faHLZ\not7shk9yfohor6C+gDeKiPcoYJ2TLLCMBVEB3OmlflswRwOLJAvfJzSCopwH\n/9KCAqTUBXAV2AMRdBJGirr4Z/AtYIete6ubQRg5W9iKZOYGTVYW0yqj6BNd\nHUOQykQoBWjZfDBJq1EPaD6jLf51XP7eS8NluKhHFMBnkeDljXat7NFPQSbq\nwcLc5xdDCtInfpQjui2za/1c5jS+I4e+cB4+aCU6R0WDVcjF0jXvRzlTOAYM\n2LmLmvFUBPz1Zan7o5bd55Qs2+/DjYQxgbARVU4wXFoGczDXUswIqAtVr78D\nEWEqjTcLxkdp9qoUHiGsaTs1Wf2vw3VQHBKh8SIwhYDdxfx4DQJ9jA6IDtUZ\nqYCa\r\n=TAYa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"graceful-fs": "^4.2.4", "jest-runtime": "^27.4.6", "jest-haste-map": "^27.4.6", "@jest/test-result": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.4.6_1641337439905_0.06182291423312125", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/test-sequencer", "version": "27.5.0", "license": "MIT", "_id": "@jest/test-sequencer@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68beceb3de818dcb34fb3ea59be3c22c890bb6e5", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-WzjcDflqbpWe+SnJPCvB2gB6haGfrkzAgzY6Pb1aq+EPoVAj2mwBaKN0ROWI4H87aSslCjq2M+BUQFNJ8VpnDA==", "signatures": [{"sig": "MEYCIQDOVaAfbi2BbdUwQoY+kvatClIHxSFpeIWE/EJFm6T7QgIhAP29/XVL9t5QB75HtRzr5sF4aQ/j99Nlch8RyluaBXuP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kqMCRA9TVsSAnZWagAAv94P/0x++e13c81brqosfW1d\nGjDSgJzVJJhNXC8lazRY94Y2eM522/K2g5oyT80npTUhLLaXponTM3MxRRXX\n8ue0y9g+BTmDRSDzu9uGwqDZAr/XhgqiBRU4wZ/6fo7BXl5Zm+EnAFEJvp1M\nOWpQei0pfNt17rSwLuPkenTEqgzmRd3M6KfpnWWBl9jx2Yus7158nLFIfpeM\nAFbxtko33EksoFmCuOzljldyYUSiMMh4BVF3HFCJT32J3Q+Qf2u4cvahQInY\nmPq/lJi3lQZIFhd/CCPbjh8PPdekCiXsniYR8MyGX4UGj35nNkCPDppCSrt2\nZJSj9aYqQu7AuScj3i6pbI1zlTJseqzAxXXulWSZqcOxsq3dNI6Bhf8AMea2\nZRG8z7tvoJegdIeONMh27eDVZ5C6gzjfsrix8niYFnxYuKxRlS9EdRRpQC3w\naCnY0UTyOAhHhbuURC93MBqKFwYRwY4xQn5IoFuGD+qw7Qpsz2KwbFotu/CO\neWtu13doc8X3dyYLipOZVDBg4veiAU3A7z0o9IF0EMwOnM8OPLYY50io0tKW\nAx3DhISUZky/cpgLSOVmOUBY5CMRUJ56pKzdbkKJrcFX6DIRXhrfvCMdjRs6\nco5i54UICv3/kjUP1lF2TA6LTRhLuuiN3Vmtvx5L26o4CQPTEPreVLqXhvfe\nr/+r\r\n=7bn9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^27.5.0", "jest-haste-map": "^27.5.0", "@jest/test-result": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.5.0_1644055180824_0.3732205261373025", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/test-sequencer", "version": "27.5.1", "license": "MIT", "_id": "@jest/test-sequencer@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4057e0e9cea4439e544c6353c6affe58d095745b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-LCheJF7WB2+9JuCS7VB/EmGIdQuhtqjRNI9A43idHv3E4KltCTsPsLxvdaubFHSYwY/fNjMWjl6vNRhDiN7vpQ==", "signatures": [{"sig": "MEUCIDFVzFcjnnwtNl3v/FMLl/4E5STgoLJuEUI47x72z/0aAiEAlGgxXjQLSpUPAPdyy/Jj8uZ5yH3Qx3ISPr7NtRUuDXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAkt1CRA9TVsSAnZWagAAvdwP/RZ76QzQpIhc5VUZZ1D6\n6qx9AlhvNWCBeg4wcQ3PrxRi0Hmj6uOT2Y65EapQqeuRngDSVCOODjzvn2CQ\neG11eK3pyD567I8WLL85XcVjlqTdh+/iz/0sYIJxZPsbVMKlUNhowae8IKoj\nH8cVKDqqhTzS2zAO/tCUYxnoySwt9713Xo9VUQ6ZICxy8KnKmmmA6Sy+RpIl\nzQnrnh4+l3UzBGGK59H8BJ/Wbpp/52ysfeNJ1cGjOSzlFM/XD3PHgqBl+kPt\nVC+uIlPDDFLL8uCYQSJl8SuaFmOdEE+Mb6dPdMr6TXUNJCiEjpAUVL+acarm\nxQabI087CtKxjW30lE9Cs/Ha7vqvSQoh4z2qarbM5rQ8aQQB+PHfyrAtL90o\nOgvU+NaMhxpeRroJoXzk09pClDTtcvCG7dUIWnKbinwdo1IGfhiS0fQ5Ztd3\nQ1DaiQVVDlhHCZOcPzyj4PyIIMyzYG5yTKFaMYMQDCQ+j6znTLabRq0WvRwN\n8vMjytXarbZlvLAGu7bIvS7yMy9YnkgqsbRhLHb2jmI+SKy9GxJ3EfmWOysw\np5l39CKtTluMYiKuzvxSXRSXa6FXr0ME5zp/oEoCPxcOo5YXMLQqka9eOe1E\nUvsLELirPM2jllpdbll0nRmkybvucyc5J2LRKVbjCEXXmlC0VM7He2vEO9g2\n1SNQ\r\n=xkH+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^27.5.1", "jest-haste-map": "^27.5.1", "@jest/test-result": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_27.5.1_1644317557026_0.193124581635002", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "78adc55c55ef86bd006a40bf0f3d16b1f2712002", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-rr5+1+i+E9BEkPD45pWIYdk0tZzGPRHQ4XJzqnuHUY/xyJYf4V43q40Ket+sKZr+roolJqazPnis6tZhuzvZsQ==", "signatures": [{"sig": "MEUCIQDzcLgeUuTmsZEd/yJ4iMYpvyeYZy+a5lv8h8O4mWQMGgIgLkPZrCzbtPY4yWitwcSyuZ2RazIyzDMQR+cUlFyXNGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbQCRA9TVsSAnZWagAAEHYP/1o3zFKMfdY0inTIHlPA\nk4novZBLoMeJO9T2ejLhIGYBzNnmzgqV3DwmGRkRDPeFX+M4Yv9Ja5GizLVs\nnRNQ7UMYlQWJV96x/JRDHmXx0txab0IopNc1qpKa5XIvnHeFayPC1FLIO3ek\nUKfDCimYb8YphYVyotRTu36IFHHiOyAsuwV3M3ohaazvtRx+NBRoosRtq1oK\nmMEAZ/GQqDU1zMWU7v381DARS9zluMIPz6kh8xKWJ/Qg6N3+xXz+ZAIRp6IF\nDLIywiGP2lxC9QDOlbxhHYiZPJHAyiivLtG+rOQSyriEyYPova5/qkv1EXpJ\ncNbXlrfGOs5fOE052HzSb00Sn5fGUfYpAFkVFcczHE00Ltu0yx+o/KsIK1PZ\n09F5tXi02CSMLh4DMT0EXEh/dHpCTKERVCm2EoDVFBZdZlF26hfWZHO6Uc0X\nnX3Ais8fqMW+5fkgGBfQYQ4JrPFIx/jUtYyruQ7m8aZyM3tp0LDveULiRnRq\nF/cLrssEzixXQREGPxV4vF3W/0Yrvz7bJCr/nTMgwWsiqXfkCXltCPoz98wg\n6APrka99yRc9uABcvPLKYvLsSsHp3lbLcqC7by2iC/2Jv1LkV0+133MzBA+R\nMcS+oJ2WZiRdLfKOpbr8kzQAOioWLmaYZma0DVfBu66T/AzWjLwZgyK/sdsJ\nnGrz\r\n=LIxR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.0", "jest-haste-map": "^28.0.0-alpha.0", "@jest/test-result": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.0_1644517071987_0.899560174323595", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a1316886e7f27da23de94944fe334fc66817984", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-IEFlg29WUhVcZx+YeuGvORgaZx88Sm+3UXN/mNRFqMspc8T2TzXPQ98NmBPfCrM6lpsZIwiipfEUSBO33rIsVg==", "signatures": [{"sig": "MEUCIBzaFSSNI9+CqN9bmIKJHygBnGoM/HHC+XiZjjnP0JvEAiEAlGo6qFnUwPn7ueFj6eDBY+i321KuGSFUlV5TT4sLWWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBq2CRA9TVsSAnZWagAAPsgP/3bIUFwIQBnj9IT2TlQQ\n6JkIdEaakkzLY0e9GSmhIWEDAxcpmtYRr8js8USfGFhjven2+YfyJ4JFzWeQ\niMv9MaqiA84tAFkJBPscKHhaSnBXbHQOW4t6a6RWN8d8bjszzovgf5YYOFec\nb8yOnylWVdGOkanV561vJzjUEG8yrUgXJLbieAIl4zzNuxtJYZI1CW92LDlD\nuwGG6qrVB5PYcSqn5Yp9NsFyljtx59a0vUGruGw8z+vHfNLq56agL1egt98d\nmHcE4/IBJqwy2O3E/eYwMV5NfJBOzizVKsGGg9CZ5vBDqJwYLeOLsq0DEp6y\nHrPNZu1DnZ0FYgI6U8tQY9DkfDjrq412m8mDXkFc/tyPBi5M+HQxCGp2KdPf\n/umf1Ho3SNE5Pgc43TFF03MbF67g2E9itPhtcE4fqjZZkdYBP77DBuWAU/WY\n58eSJbaAu2/3y+cERs0AnoA8jw8VTpzl2TOqbLnKykdk/SOuOu+3nIr2M3jp\nBPUuHjQSVZ/HPbomz//uewjUSnNZ7pbP5Qnn0cfdiqOBfHV8Ukp20JtonEUS\n6UXTsmpbGq858WyWFAt4TmXCm0O9Fdu7UUN0Bhql+lyNb9JpVDbtHueXrB3r\nhtcqbHD39ALeRSdyZrdmvYR5htnQedu0xhZrD6ziTNHdNDPfYsuy4tVknFM1\nM+f4\r\n=81iX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.1", "jest-haste-map": "^28.0.0-alpha.1", "@jest/test-result": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.1_1644960438345_0.49654016516985466", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8132fdbc3ebdecbb55a06fbc7db5906290ca8ada", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-SZKvMBla6Wa2Yni0MLL/xRxiFwn9ranZp1VGsg3/eMowicLdTqgJCxZDTC1JwG3d6GOY8h24HoO1qpGeS3j7lA==", "signatures": [{"sig": "MEQCIHMaBuSZxh9JOxOkDg+//sR/IkYIqlK/5ensH03USU6nAiANwCSXPgAuwbjZ4/JnwUOa4JQ82k4GlilYwdOIl/k0rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT6VCRA9TVsSAnZWagAAlhYP+wdf+NdaIj7x6uDaBx6E\neIyXRRq4QVmsiF99woE08jhSRPCpYqaR3iNE461GgkozNZlt5A4tNqzxnn74\nD0//XTHWgPBzLfCPNr2FQ7Tof6g+FpmSQa2s5INJdDk1ZQN+SgopnzeMACDL\nf3cIopWcuK4gzIXOlCasTydgfitg8UtKlhak0a9ko9hVQm6tmH3AE+SDIhWN\nBfteH2f52ftcUxCzsxLR1/TrdmC8XZNzVlWdFeShEkIeACEQ9tip2XMUvEGt\nbCbvCD9zKYOQIXfY8FDOJ0uzLQpPwY5Fmwl4zgs86d5ZNy+bjSR2sDt2pJ/w\n/Xge3u3BbqnOqeNLC42haHX5++nOcFwhJkODz1hbC4nHIRVr+5YVBouBzImC\nOPerVoy148maADU8Sm1D7WOq5jmzduQ3umECar6NhGzitvRMi0ejKcHIFss6\nul6z0LcUn7St/xUo8lx5tVDm/bIqXHMDjma3dqtEZlc2cr6t9AkDu3SM829O\nvJ/UMUrdk9y4z/u5KU9OuMSAugYQXT1eh/bc/hTkVxfVC7TXIpY08MbQGYDm\nijvaHqb/0Gat9AKjKJodhGFjncMAZGUeJxP934L8qL/Dsbm17qav7oPJYnEq\nj9SnnYr6jWS04tXl80UNFy9uYctd12kP3HxzZVdU2Z+ZvRSdckqAq1d0jEwH\niXgi\r\n=OjAj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.2", "jest-haste-map": "^28.0.0-alpha.2", "@jest/test-result": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.2_1645035156869_0.10344718520550256", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "90badeadb8f542dfe75ebc4f20a543232effab1a", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-lScULsVLH1R9IlyejyvrWOJqnOkd9W1DSMok6Jyirq+fJ1CUU+U373s53/7Wp90lXkB6GdPvxZ/OnjT+qwQ5kQ==", "signatures": [{"sig": "MEQCIGf3T1J71a1abbKzoYJihyHDoyw1+W8+LVAgdT203/GcAiBs2Un56UODBLTJANAntTfm/zzUPdOgAJZ3u3pU+JwFzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7zg/+PpQ/EGtkFPa9xqHam9agvByDjSE7GFmb7pXVD6GJHoxKESNC\r\nW0smfuhB8LMP+d0PpxQm3bmkpIQzqnlANnEBRv7FH/1TIE2qhdeeJQ2Cjy1m\r\n/CUegzbhSXDB7MvK424BVeqOHzAZNYzWE4n4TU+7g887D5EY63n0ygBF+2Kk\r\nIXy5t1eYEY8cm1BjfAv0bVhOiQq8bTHqsSFCImBG0ePD77MSVqbZcy3Jc7i/\r\nixjInLMlptjC3s12eMjomZTEkhcN9cCBxB9QPkkEYTGIw9EGkIEKBruaCsiV\r\nYTksMYebBeI6UJP7hZcE08+NuPR1R1Q/uLzq7eruaY5KBrc6Jce1ercAhuMv\r\nv8GHIsD8arj4rFjRryygo1JmOnRYYUiJrG/PEyoqWcZRtZB95mV88FErH6vF\r\nHI6AmV3npUe0I/Dgd/PsUQlSPl/b7Jbx4pDBbvKrG+5Q3tbveZcyH1Vl66a+\r\nZUNR6BxT0UQeVpoPRSrxrzxuw1GfaQz4kpWDftTdA1VC0ayq4iDBUhUcQb8Y\r\nhwS38YWDmNUu7c7FczLtgFNzp/t1cHw0GWC4HaNbeoySHfd3gB0n6pCkpn40\r\nkLqJZZqh4yUf5AQuk/77h5WHVid1BfCKYZJwKmiYAj81byaBTfyEn0dFW280\r\nTUVvDg94lmLCs9RY9uGECAftHJVSK7RHW8c=\r\n=Oe1l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.3", "jest-haste-map": "^28.0.0-alpha.3", "@jest/test-result": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.3_1645112554820_0.6109122294983453", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dad2cecf8f1d95f78cf1f0d5945a817deeecc1e5", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-71I3WYLam5Na9S+PrlB3qFoq8OkM6wdeQtRFwFS4gOexTGs7mZdUr0GkpQLyKUwOQ49wo+2Ivsesw70uIU9Exw==", "signatures": [{"sig": "MEUCIQDta/eOjf98Zcc+7WNjxEv/8qPHNXdD8rGVO9H1Zp4i1QIgEzCXs9JB3o9GnvKfbKbe8HPusrHWs6UX9Us5LRPN880=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXdg//RSyKAdiac7qIisyk9KuzqLA+vE9Kz5+Px2KPdFAF1ib/bCnl\r\nFwOsDg/9s84P6M34XmpPTHCaHwhSF82dASOdtxKdsWcLmjLnKztGb7rZVC/z\r\n4/x9bQ7nMz8RrC/yIb0FP7R7OURwUoC3qaNInhaNPcvFnkkgV/ytZijOqFM7\r\nGd1R6sLmZLhmQRd/F+2dI6BjofxsuE3zU3a5ZtrXcu+FlgOrtk4OSeJDqdDQ\r\n3bQPMr9wxe4XtnQDw6EAW4fS1BhkK/ElP+2DJLGqCXT2Q3jOhADohyW0jqC7\r\n4V/Uebu9Na1JkmLC0zvjtmMzDEPt+RILeqpfTY75KUhNWDlC0pBPIgIrSNMF\r\n01XmOAuHogBdQmSfb4YRVIVpVSEGHRRIl81pcbHxvGJ7XTQ9YW9IM63xSH/4\r\ncTB7TLeMoK+da+oLionJAOVRJo6v/YocHpmNmRpWE0iZrhgS6cxtk7Ul6YJ4\r\nF5tjSmPvkwZ3zDGWGeIWMYffRnnXkO7+joc+bbZ56ysiJ0r+MqZO4V3tUGob\r\nJhHf4jQIKb9kkvHbfwvpD/57GJIsRaVv4XpZMg7lcL6ydBObSNrG/p7F2C/q\r\nnI8ygJPAyRvRNpVfL1Q/wJLWxO82vuEAvC+XvljDt76QLbmbhNnIEYG15njE\r\nigHw09D7jGb1xNYDyTJH8vBW3Ww31LhCzXs=\r\n=2gh2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.4", "jest-haste-map": "^28.0.0-alpha.4", "@jest/test-result": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.4_1645532044759_0.5393499887399762", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4cef950f7baaa0231b307169182b4870b18fec6f", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-Z7gEinefmuJ62Tq6n9F6NXpr/3b/nsZADorEzG/sRnmuteVXMHRHSk88T/EVv/E7vxK1aN3D1tCdsbJDsOd4AA==", "signatures": [{"sig": "MEUCIEFfD31pIGIbqX6MJ/TqHNdrRVhh+r6uQZzfNUD0acqsAiEA4CCIzUSjTPy5F3M7+pFD0RXZhJZgN848acSUPTvgb3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/E+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe+w/+IpAsNIXcHpnmqoqtlb9fo6C3epo47BvoYJzKEHfKP1j7II1a\r\n8avuGPmSDaacraBqwcxJA/2Hj8GBeGBMKhtc9oMwbOcX0UJdnv6+Fn2zA2NQ\r\nMTspuQCSp6IfXn+jEcZLh2CRNVudTnL9y9RavhWTDok5+NtrMAEa2dYA3nq/\r\nd8OgAHpTJDCAeD2AnRGKRFHLgLY0pRmb++/zIeUApgHTQTtPuUsT1fG9ck/9\r\nCHTQvd6oV5UDSXhWTQ1x4VdXbnkDTSvgvZMdbw4ponhvU2oOGpPXII3akVCh\r\nCxsM9+PYiIMobK/X/aVjUBbhN91M1tagEFubG4qUQPJjF2GXL79+NelTp/mN\r\nzgl8yCEd+KSTZeP88pFuaE1/UCNCuuBvedaWc/T1np8MPXZXsZj4wl8pD4Xg\r\nGnsdnfQRWzOKwbitHwY9vCF2rIuPGwUPVonoD4dHisWpaXNINJXs2HrAjvN5\r\nPwU4Kp4lvgODfVxcklnW34l9+4Ald1mQjL2tuIThdKatj15D7yrdq8R8Evs5\r\nFU8tLQV8yWtKud2SpvlAZIdVspmUtclwxtYx7nJEWeDfPeyLyZuAW8KMMDQ7\r\n+PuVN27um+qf4K4vwjIChqH4u/TiNqwDPJOOydIFrbFifjBGJeemOwhjaHf/\r\naScJTjr+SCav6oL2vtKVD9cU7bNTAqMJ72g=\r\n=yFFo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.5", "jest-haste-map": "^28.0.0-alpha.5", "@jest/test-result": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.5_1645736254409_0.9023601730929338", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae1b6bee48938e3d71d40d657d34c491442c38c3", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-fQGODCMXdob3BIUYuQI/pKmxEQiRTq9Ca68MsmhFBb60oIrPYa20VJTu2k3Q69WUf/y1xU5bNvyFA6TlSn1evw==", "signatures": [{"sig": "MEQCICjSOO+ZzZUcc7a1iBZSfaqwURjlJYh4kaAkJF4abbENAiBbc8Ac19i5u9aztgK4KZbAB2RGia4l3se+9A3jubdAPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdojACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8ZBAAgn9zPs0Vd1By8rDWbqiOogz7oKCzu8xNFUPLIi5bZBmS6BVD\r\n4QVhmwmf72XtMf6Swe9TefDV/jq2MMOY0zFmrbxpxBoNYkb7l4/UO6fgkbcU\r\nrDe5Da7lfynZjEXayPDSspgj1kEZD4a1D7H66lV/17DJTG0kiTspxbejLGUY\r\ntvE02XmI033yyIvoM+8Ekyy56ZkB3YV2cvr8smfdXnB/aZvwoqb1SQDm/7nQ\r\nIOFNCpvDYSh4ymW/yj/p78WHMCOjU3qy9QyJxh+dE6K+9suRQfEQnOiNBACe\r\ndQH5hg3xveuy58DBR1wvUoGV4P5PfWhYliLo411ZJHJMGU69IPaNhys+WoRd\r\nI01GAfVR6gGgijzAyeTAfYpTuUo31tuWclQii2PYxMwEtvguaA8KnG+81sE1\r\nE16LSOPdsPxHwTqqV0VD89yRDuqtCkMZUV2MSvzu/VSUVRvj8lofm/kPqYyk\r\nNGafPnB4aibenhWmGdNUs/eTn6ta1ojSiRUvAgyS1xMhAKamx4v7SKagVcks\r\nNA4jXDbyu53bCW3SljkESnH0Q8KX+GY/aSpJUcuRs4h3GYN8AffwX0uvPWMm\r\n/dYeeAZfn6QegvPT8gaTQ4P1jIx478fxHHvklHj+jiDSKkQB4+e+JPyF85Yy\r\nYXeoqu0d3iH0dkR+I1waXKJXR1CdxY08tCg=\r\n=5DRJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.6", "jest-haste-map": "^28.0.0-alpha.6", "@jest/test-result": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.6_1646123555029_0.5169326065715654", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e6913c23a7deb1e1ea42c0e2560ee562bd7d0fbd", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-xzzgWwuFbEy8r8mbczXR806meCv9SRew+5HQGUKFjrBtGhqtoM/vqVjSZyXOXxjxZlrCBQctWvqGwZAk9NpGIg==", "signatures": [{"sig": "MEQCIC+ko/4ZERbMww6CilBoIbKh41D6yjFMLQx83Ga4HGq+AiAVxG7fApi0UaW8CrGg0At/uekKy5tOpweDt6ygax7rqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwGw/9G54ATQUjCzjbbxxwwC2cIch4P59TdPE78YW9y3JL4Zhp6uFh\r\ncykEOxuNfyqLVm9TJbwnFI8OVc0OT0fZDrCEUo12jA0/N+UeWJDBksqRoU2I\r\nNzQm6dSeAncBO5ShSLOMiN2bPjOIzWeewBU8VYDwyA0/ySYvH2zBrQ5ctopB\r\nS9wZl4j0kO2VR+VuA/8TZZIBymJZtQHiEUiAsO7i/QD6thIqMCpqwkU1yCcl\r\n//odJ/UmtkdZSWqSii+1i+dK5CDGYkFRmYOZZwIpIegb30iFSik46B0038wN\r\npbZP43pcVaUAfFSJuj8q16t6bkgVEo5zms7Xr2Js2HXhuKKZynKTaY875gBn\r\nOfxwa0UUlO52o+VpFiAl8BmXqDeGMIyi6/V9r2YBPjTaW2XkmhU9dgIg6tdW\r\nZ6mpXgo4ZQaWcGQgLHakmo2WYV8u2N5SWFWlWffPrT30q/MtZy/MW1MdUOkO\r\nhSZgJ7KF9dZUiDXfYo3l/F8DzyKzG6My8QqyZ5athghK5NP0Bw79A+dtnrx1\r\nvE96Ji34WjsOmQJRXBBlzakPDRnZy48Akml8qm6EMYgSS2kXauS9ChqFWDzx\r\n5ObRNbVXD9YoczD8TJXjjtrQyeLF/fEVkCh1d9eT8jyaZiZt8O6frjstDKJH\r\nsHgt9V16qfum+t5AfO/npabiO7sTVaPd2Jg=\r\n=LTrN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.7", "jest-haste-map": "^28.0.0-alpha.7", "@jest/test-result": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.7", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.7_1646560972977_0.7523153461092698", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b6a1e0af47645271c68c04602246fee8f0e848d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.8.tgz", "fileCount": 4, "integrity": "sha512-Ek1C2qu8Npjtz6+9PqjyVu/XDfZbbezzZqY/bNzTyv3QYKlAYB7r+DxklqeDLLqp3gnQbJYU+AOPZMB51iQPSA==", "signatures": [{"sig": "MEUCIGK1gOpiws5HslfeX0KelSahzxHoo0Ux5/aEQagXh0TqAiEAo/dFnKEamzFzzzk0ITj3T3yJm+JrYRGUxXDw6zbbLLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFmGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplNQ/8DwXVBFV89T2OIMdUBlRlv8HgqnCFwv5icD8/SF0yXK651aO2\r\ntSawyrP9/t89LUaQPxVh0RMgvb2Cge7fhSROjP0pkfGLR6wgSMRD1/GX/Ex0\r\nwK+rxdq5Wx84XJtpTeioKFTKptGzLY4D0EPp/Hw4U6/sjV0zxfO6lDtto/CY\r\nTIdNOV6yWSyknJ77Tk6paYLMP8ahaP3jcgPR/M7Ot9xOarEAmylmR6doGKaB\r\n8JP3hFGwd/aXVYiSHmfi3F3ZYrcfadzKwyCpuO9+e0oMSlSMIhGjNh33Dx6z\r\nmEBTgAhEqDGL4oh3WRbNhx1iRuzZbdIZdOut1STVUI6NZL/Qrm6qN5M4b003\r\n+bYItwZz8dlU7YTHSV3/2OY1DD3nliRQOa5cy7DwGqXm3Xwq1BzQ/+BeIRAt\r\nt9l1Etrq7wUjcFOA0cew1hRNPxy9o9KyEfAmX8s3LWbft2xyuSedqpcAfQ2Y\r\n6zntgX7/UI5owWsRGi1Ta/IDJvOYiCIDATAcCAGIL1b6LtAkxWRQAfwQ0dFK\r\niVu81jfNsVEp2Oi0paTLHHuz6ZjLyMAySnh38yxANp5SMLjCsY0igcnO0Wdp\r\nI+jFuB43PDf1qawplvTOZdTUFqX5jcGrZqhWIZCzI6NR9LwbmtfEtlrIPG2h\r\nj1cUDZP+vAb4YRgBJ9lPbvOWXQQFexcKd8s=\r\n=9Qbr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-runtime": "^28.0.0-alpha.8", "jest-haste-map": "^28.0.0-alpha.8", "@jest/test-result": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.8", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.8_1649170821982_0.022934925160356334", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87aaadfb59a193e338311fe792489da1265983b1", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-j/oAzOtYoI+n+FgtZAbTSod5ZfpZTiFkkIM8JSdivRswxh/sQ4wJJWMl54WkxkhXwTcigVfi3+7V07Xmis8ZMg==", "signatures": [{"sig": "MEUCIFzqm1+7LVzhzqsYgcWmoRzxbpdPFCF3uCSWinp8mL0TAiEA3h/3YuTbPbQHu9gF7yOzDaC+5+tJ/nWBb1Vvxoj/9uE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDyBAAjGiXNtX+yfaseSNlj2flsyBtdUw9wEOXW7ZKoomZrsWeU6zq\r\nIzdFi0iUFH47xC8TZ+bK5Trj1CWlXKWxOBJcpgYcCRcTjr4LbJqY/65IuQUS\r\niv1l80JbGAKd0ua/KCwkeEn3Mj9jUEo+7gXktGrQhoqmWskukhimCq5NdUR2\r\n58PYbU/Lm1VSt3Q62XrzjBffuRum2ak4fPy8pZNcq2FIfkA7obiA73UQb/n3\r\nihHPZHudEqlEl4ncWC3X5bXupZ9YOggYjpVszfZ7j29O5AsF1Rofbe1ySB13\r\njV8wZibUdwnmN9bsIJlIYINtLEwP0jc3a/gWi5TAOg0VR47IoNFdB9XrDZV4\r\njgmmb/22BU7GytmsK6Guu/AJ4m9WW6i5g/OD7a8AdQagFz/ZHbFOEi5BB53D\r\nIkIBcrd3Hhs0+HI/H/Sn4p8gTz1l1X1jG11WEtE+XD8TUP/jnCK3cpHvSZ6x\r\nnXoMZI5fAkT8UuMzDm0ESDk+LxqLnHdzzUAtwnIGv4hKxTJybfXGbojh8UJD\r\ngNc90wPUhf5bKgJM98hPK99+kcdARyf/NTkR08ESW0Q4H8evu/3hYN76c0wo\r\nEpXtfnyix4c3iGGCa0CxF9J/aFYJ+hoHyzlR5lv6LjK2E4HYTT3G92UcWPrn\r\nxLtF732NRiGeaIZTn6Fsw9rlkBkmGdtVSjY=\r\n=C+LV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.9", "@jest/test-result": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.9", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.9_1650365958945_0.24250505021547353", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/test-sequencer", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3ebe6a0009b9412ad07e0299166ff7ace8f8da90", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-Em7UqjAGBzLSc1btJjmn0AtgGgU+Fe5kTeHTNpasQZAcLwC0ipO5aFdDJB5Ox1xM6xWNQKMK2ZgbHMp7bIjtsg==", "signatures": [{"sig": "MEYCIQCkVUX+Exb5tGqOQbf+wGadH81NPwk/jK5k6EP5dCt6uwIhAOQ5dC7r6qrRcZsu8KK2SH1OrL1mmFvpdUPxeMACHk8I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokMxAAh9uddzC3zGEgYQHNTtMtC+wyD0UoHg341h4ec1ZkLAVF//oN\r\n/jMyP5a4JB+PH3SUlT+FCLQSNi68L/dL3K90294KsRUlvD7zJyYeLgTyF8PT\r\n9tJ6+UXp4GsxBCZIAZPic6oMP66Hqmu0DJ4W79KwM8/HdMKTLsXICVYDdrfK\r\njGY0/mQoL93CcYsFLqUCZUmLdfx/phEX0HJz2Z+nk2l2vbeeSWjY4vZ/uDp5\r\n+w29H<PERSON>seiNxkU7mY/gnWTgZis2JnJyL6poVjQfGMHYszSL2fq24cvqYhjVxN\r\nv/XJf35QGVs8Iw+QrjCpdXU2sB7t8Gqz8GQrGcLexCRB0IYi6Q8zkzdRPGej\r\ndpnQ894K+GRnQJFr77TtQSutgaUxs2DFKVrD+KEwwGQkmuJD3ZyAmNI5Qc3x\r\nIlq40r6AgBNwOQ0efrhwN1lrxzGqBrv2mgakW56RKuUmGtgWxqWBO9jDIyWT\r\n3YcJ/R20oeaQvOYjEya5iD3cxkIr+fjXU26ZgHRGt3540dbQTyyLky04pY5Q\r\noGdUuFfFTDpA/qu0hWGZj0EkEKZ33F2E/SCb2AFI0rBSlw/4A513ZVsnv9dr\r\nFL0PRXIfFSD5lV3WTnY7h9GD69RuxUf/WIBZi1WGT5QreMJkdRx3mgQ7L7yc\r\n8Z4NgWCr/4EPdnajuSKrW9CtR+/qs5/voIA=\r\n=h7Yc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.11", "@jest/test-result": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0-alpha.9", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0-alpha.11_1650461460527_0.13893234002038324", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/test-sequencer", "version": "28.0.0", "license": "MIT", "_id": "@jest/test-sequencer@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2745535c996e106ebd02a01c90fdbd2bccb64c1e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-tRgs5JRVxodtDVddITgH0BRFwBAjbdWnnobuYWHgeYEM4sZVAsNZmF0oeZIaE9vK72xgdnjIoRg1+kppMorFCQ==", "signatures": [{"sig": "MEYCIQDhZsK0p+Iw0+YDnTp1ZiZX+QCxS3ObKVtL++qB5pIv0wIhALrPqAgKgma7Y2aPO9yCBQl/dQe9e99fPjukj9Yslwpf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCag//f0yrhlAEMZ0dJr+21xhoXOr3gV8hnI0gY8irn89WFH2HH+qC\r\nl31nd9TaMxj4qX6+XIoFRuDIrMN9fMXih7pzl3u9sliUvwkSMtHXmfQN9BOE\r\n9zAO0KBQ8lVPtkTBFa/J5bp0mY61FD7bp2gkmuNdN+q3BPBBpzx7HuUr/3iz\r\n7sIP1HuH4KFNU0N0H58s4bhByNzJGZr7Jz1XTqYkEMcVPRt3Aw4Qo9lKcpTp\r\ndCxABBqjXfznwVbLT+Y3pSjmBBHatOUdvR1xkOoc/eNRGLze4Y/fDCEOjx1t\r\nLec5Jkje8ckSUmw7YZNQiN03MDINXmQchgLxL1FBfwlapOlkJDz4Ai60hOAg\r\n2rxlirzukEG7QNo/Bw07YKp3XM6yTsGYKzwUNlT3+eMYReQwmA8yZveP4Qoe\r\ntx+efW536S6/b+9wdaO/IXt5uV4fgc6RCPhMwVDqrWHtJ87NS/2FKCOiL6/T\r\nDR2ZxOX2Y4KMwLc9o0A2kOZIu8gsgDcGm/pQ3p3wOwc9uIME2RGwzy1Qw02C\r\nr/ZY5RmI49g4kd260/c8Kh1LPtifSrrjrrPobEGcOpknxcEp0LUnIhQjn9j8\r\n8g2oJCycUigkF+gu6QJOo+4dK8aa53jW9H3zGdXCot1jEk1mE7hfDQt73ShT\r\nmqmRCpsJaWOEqL0DDJb5wPgfRNBTvTjPY+M=\r\n=05x4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0", "@jest/test-result": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.0_1650888494148_0.07564801183538528", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/test-sequencer", "version": "28.0.1", "license": "MIT", "_id": "@jest/test-sequencer@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7260db16c12f055f80809348740c2fd2d903f6e0", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.1.tgz", "fileCount": 4, "integrity": "sha512-PbXoEP9aovOC+KunEy65vuSAB/ZMLNcBVdMUIH2hsfFDWhQx/8OnHsz3dr3g1U6qNuCpXzD1fyM8/1TrUU0uFw==", "signatures": [{"sig": "MEYCIQCMhIYNCkrTfL5laiWeoSPhPuVhkkc7DExl49sD6a8HewIhAOIBoWkmKGSpwehFlr+EXD7z156b99+65B/dW5JKb4oR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfRhAAjF7BrqhDlnrDT56ruK4BSXFlPrMBe2NrdmyIEAwT6LKn/goA\r\nMGMpFXC2Of0E/vsYCIY0pT0M93puJ5A6+sybitnHdXbOmEcE3p8q9XN7XF7t\r\nEfqCjfxWdVoR6i58CDkZEjBYIvBSbWLVhka8hxmdZtFCe+lMnEHxEXMT1vV8\r\n+Z4KLwLQ/maa77Wg8LRFS9SB5JDZ93U9ruQHDdZwBJGjiBAf+AySnRip6Fvi\r\nNXxKeXhleuhEvhVrLA/+v4a5mwY9BYefwF+H3cmyKqj724b82N8wi+KvO/g2\r\nNXHlPOoUxwO2ymm3GddLW+fRD/eB+aaQV1zYPCgJNc4DdnN50XnTY0HrAeV5\r\nfxw6h0pbxtNMdcP9aCsb6H0/QDhX4YBDvV/aKXLX5A4UjhqU2W3zXuYcUUSe\r\nO74nw96dYF7KqbNVtLoBw0v1V95r2sAuWy/8DJpiLpOVbX0kxujrtJ7H7NAh\r\n816je7GTS9JD7bz7ek2ZYJlkY97+JaZvi3/+t0QNO1S+gxNd81EzB7GddLQX\r\nJp3DTxjZiRqOhfLFpNtWY68PPtC+OlpaFUci2V7bGcvab8VKuTty9kRGdLLB\r\nEG8ru6Kj58cVOkEgTeuv6qzOT0ArxV51oO68yr+5r0eM0eSHoLBUMRe2WBFt\r\nFzDyeVKbko676ErRlm0dKZQJwNinxjrgLgg=\r\n=En6h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.1", "@jest/test-result": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.1_1650967363883_0.07193348512732567", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/test-sequencer", "version": "28.0.2", "license": "MIT", "_id": "@jest/test-sequencer@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7669b7d8ff2aa7a8221b11bb37cce552de81b1bb", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-zhnZ8ydkZQTPL7YucB86eOlD79zPy5EGSUKiR2Iv93RVEDU6OEP33kwDBg70ywOcxeJGDRhyo09q7TafNCBiIg==", "signatures": [{"sig": "MEYCIQDQoQp6IYMzY1kSwI9kpQmw6nl895h9J4VzSL8cMQjntQIhAIo++zv5Mz6Sy63wksYDXSGSwtLSACmDbYoXIrwJ6rdp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmIA//b0K+6cM2TNn8qq6QFds97txETDoudF1mda5+j20nDJkUxRMy\r\ngTfdWF6I8b4g4zwigb2PfxAAebaqmnZE52ILFd4UQoMu0ecGs9V5Xt6kfBiX\r\nFNxMVUOoM6LXkxyym4eITKsn0PQ9+1lwRVnKGX/Vzb1AzRPYWVZt+HMRo3SB\r\nDDU34ohPq2U07dlcPPe0d5QOglUEbdV/xle3+q2cn5DcY6ldYHykv3S34PL0\r\ni5IjPIBRRQv0f8mva8axfaRkkjASLUFConE4A4Wnn6OPozIRezFDBg+hYoAV\r\nJDVuWWvbRHfoNmDxDFSx0iPhxq8N3aFy4w2CqsgVrCQI9E7JcC/YLF2+d5HL\r\nC6YC18Zyk5Zs2vqz8wJcnlGTNvYZyN9nip78WIX/vxb4ULPRo8zAFuypsm/i\r\nIbWGryIFrEjLio0252/HGpZcDASWMBV2R9PqbK9A7qhxxeYKq/4D3paRKj7y\r\nNyk3yo3MQNswXfENc4Em2AjpWkzQSsGwIjT/HXGF99xOyi9diUs1M6KRaJYE\r\nMJGSKwO6v7PgE65ddAsmEsMI8t5ohE9eRMefgt9o7BcDgI4Lvpe/Qyu8pKzL\r\nH6u14vGwGDfxDwHJnzwm6D5/hXDfLo5eRzJNTvhzgKZ9DN2nxz24QhtU27om\r\neyYRpSUh6FQQDInPfmtpjpdyLjc9VjShV5o=\r\n=3Qes\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.2", "@jest/test-result": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.0.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.0.2_1651045448336_0.28761413538121006", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/test-sequencer", "version": "28.1.0", "license": "MIT", "_id": "@jest/test-sequencer@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce7294bbe986415b9a30e218c7e705e6ebf2cdf2", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.1.0.tgz", "fileCount": 4, "integrity": "sha512-tZCEiVWlWNTs/2iK9yi6o3AlMfbbYgV4uuZInSVdzZ7ftpHZhCMuhvk2HLYhCZzLgPFQ9MnM1YaxMnh3TILFiQ==", "signatures": [{"sig": "MEUCIHuvD899zQwIITkyTOYEhZ4Ej2H4d5TAS0Cokmpqf62bAiEAynsQHPfvnOzgvRuv9c5CvwJuBGZnJkbI9La3/HsObJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA5A//cE8SjEJQie7lwaTsQ3m3nvfW+TOt09G71oyPOkCfLvTJr5Dt\r\nVa6Z7bhWmWc6tak8KWpEhYlwCvbzb0Om2ZnlKpxl4EOsFba7Rbpyu3X2YrIw\r\nNK4cnnavZIhLSRfOvvjacckB/dKBgBA9QyganF2H8dnT+utQbBkpyK6GmQPH\r\nSJZEzvVsdCf1t275T6Xfj8wIxTB/4P8raitbUyz/xo+lMD27MvtR0jkAdwg3\r\nMXNDlcx6KGmhpYcGEl5xO3EHRfNntOGHO3zON2SFhFgUo/l2s4KnPSAxRM7H\r\nG2kq/T3b+gpSjfjIg2sD2IeKxHTk6rmYICSwfom70f99GKvdfOeXXfvkU7HD\r\nF3QeTf5uaUcHeJfpQfTkvhd6eqtF96Fl0SqoLPT/KEPbZV441brnwUg7PK/z\r\nmrX20Tlp/OZ2VDTovZkw/Rx1PsFA0M7ULxR9yiKTdMQAt4/zG7paTCoDf5Rp\r\nAtoJF0ikCrj0Z99JwewwHNiMaJ8Xkkt1JMuJSQfJUqYuFy2WBP9Tcd1uUGqG\r\n8NOu6PHpNsUMrDNs4apr2Ol+RiNLQcoNypAhSivImkSze86tnsdnlZg2NLEX\r\nCj5Jz89ZndFbo35AfjMbnMMPnxz0bxkWgmy280JeqgVy5hmdDVpLn8zxtwgw\r\n4HLRzcej+kNihBMIIzWLlMwijwuvpCrxTsU=\r\n=ua0f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.0", "@jest/test-result": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.1.0_1651834138566_0.5259014410233664", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/test-sequencer", "version": "28.1.1", "license": "MIT", "_id": "@jest/test-sequencer@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f594ee2331df75000afe0d1ae3237630ecec732e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.1.1.tgz", "fileCount": 4, "integrity": "sha512-nuL+dNSVMcWB7OOtgb0EGH5AjO4UBCt68SLP08rwmC+iRhyuJWS9MtZ/MpipxFwKAlHFftbMsydXqWre8B0+XA==", "signatures": [{"sig": "MEUCIQC9rDUwzNgjMXIo/G25HXh+Dcq5JJs+Upy1JpdPSJs2RAIgDS07eYtftzlGT03KVk9IAAmOE2NQqmL/o1JE8Tu1fVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuukACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxZQ/5AUi6KpYeuvlEdi4DL/t5PiGeoiCK18jYso/F40oHNAJg2YAA\r\nEPpErIQN66VvY+ik2lR0KFMO5H11VSsYfytZj8bsbIYuwczZjbwgVYvCV4mC\r\nAxUpBPwhDdS9CpEiEq94MY8/AlhiR0eUtqlb5FqaxZ7DDGF+nQMYuClPCFiz\r\nWK0HCP1SFDmHgrRFgO8EU+lC6DsoNx7w1bvP5LB7vNJL2AT1oWVJSiJP4sX5\r\nupL6vT+OptlXWTEe9y6Di60+Z59TqtpoRmxVsdR6sevBFFgbu68CPJbn9rRc\r\nzljJrcTQQr89tgzk2PFV5OoAs4xXej9UW0dijU/4t3E1omrZYwZ5KRqg2CZx\r\nSC2hfCA3io5cAnDarO2yTWM28PSrL5qU4vGd8nsQtZwf69L64KR1omkw14fP\r\nJ37nqJeqI7xUaE9ruZqn9pK6uGnF9pRPAtUsDRWuCLwDZWKp/R+VryhZpDX2\r\nmwEuMbGis9AerMjLxAIcH6b9cWoNA0MUaRR8soRLo9OxBGRzyNW1Hzdgd/qQ\r\nmpROLroYBIpA1jfCkgMibJsoVIOurP8Kkho283eNx2lcj0OpKXFHkVykEOhx\r\nTj69PW55rGY5Bini9aHn+h337SITogUTJPO/3nuXybgZPPv2o+j+nTG8W8UZ\r\nUn9N5uNJDaLqDt1iX8tQ6Ii+1RqW7BwdgKo=\r\n=EBnS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.1", "@jest/test-result": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.1.1_1654582179986_0.2288702118965804", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/test-sequencer", "version": "28.1.3", "license": "MIT", "_id": "@jest/test-sequencer@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9d0c283d906ac599c74bde464bc0d7e6a82886c3", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.1.3.tgz", "fileCount": 4, "integrity": "sha512-NIMPEqqa59MWnDi1kvXXpYbqsfQmSJsIbnd85mdVGkiDfQ9WQQTXOLsvISUfonmnBT+w85WEgneCigEEdHDFxw==", "signatures": [{"sig": "MEYCIQDvV1r42HYFAkCGUqoqG93mtvYHuJGQZNk1FA8MTIOMwQIhAKMWR8B5Wu4ax+jBKOKOiH0jhUdoRGZ0yOyQHoHf5a2o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnIhAAi7KcjmRrIWCKIfkEyP/Vt49S8azi2XIFBP2C4dSgo+sBzL88\r\njom5mgrxRRiLayH7oQUCwjpv55i4ohnRcv3slkEYugVPW8RUbunhqtjpTcpZ\r\njcfBCNhrWvhJpr3zJuf8iiq2UlenXa5hs5YH9GIiRHtmaMH8voyU6kzMLvAB\r\ndU6dy6OnazF1jDthR24KwOQH/mjBzTzaaNElZsalm8/GNRqnwmejSqWXJVzZ\r\nPsgGtdFsE4eSx25J54ivj3eTHaAUmogQuU6ZnI81unp3mZ11XdWmkkvtEbDi\r\nuUpU84SMYBO1BEfY1fEcBSFuiaWyRH6IrQxaDm+cBysQGo71V9XMBUfhcBsQ\r\nGQxSnq1qQXGFHP3fIN4oc8v3/2MAgOonwC7UQh1ouuXILjnMul0zJsZR/3Y2\r\nDQGM2dIw8g9lZbdgcwbzRYL8npWF27W+hblTBUBF0DnNfAhn7lJ+AKnY8Hq1\r\nfmGGLdZBgVZT6+L1qTfHCWcnC2M6O8A2bnfpRMz1+FtPJzGAMV5Xe5g9DHvQ\r\nDuyFmoUTDoeGLSgEIWXL/+sI/X4S2FbjwOIz7G9d4/Sp1zZZPPzBthwSdPWK\r\nqVcy0pnXr7yxOMX3XzPZSatfiePapDwzVxCrwDlvvctsDnGCyjguzrxF/ykC\r\n+LfC3g11eRXVKdRFE1RRzc1dwKgP4ySTpLc=\r\n=O2su\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.3", "@jest/test-result": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^28.1.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_28.1.3_1657721553145_0.6053011635841934", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eeaac7297d9f46695f5a8060b5d79a7bbcf9e218", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-8PmUxL/Sm2zS7/RVjI6h2xhQCytNS2yXvoZ0u9/6/9MxSijX6tHdEKvMWmm5pVWsYp34cn/fVDYKk34M0hF37w==", "signatures": [{"sig": "MEUCIBSRQ/9aIeWMOJrQJ3DzB+yCU0O2GhmSLKfw2XclSc7KAiEAh+WExY3jJb0HQkDb1k9zgI83c0zM0r0Nyj0euijaYFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojRQ//d7Eams4IGx6sBWoYxsJMxudo049RYB+jpGaVrWvft1IZDuE/\r\n8wLg6F5HSSNTuybtSqLkhS0E2Nzt1Di9bgMZEC73HV0Ktq3qbzoq0V3e2tRL\r\n1D3sy6i1VXNQCkqBclIxW2Pz/PAcZUCf+Knpd2xBBzh0vxX2TLdS/Tt9m/jo\r\ng78QlDbe2UlTC1nTyngDBhQVSgg5f5oZre30m3FI3xt2FPUOZtm4tkQyhdkD\r\n6lGCXZif+WJP4/8S6YJnZDSUlnZSFYgF8DxTpcgMkCB7x1dbVU59jXoJ0n0b\r\nJHDd7dL28O8LCyDTFwmlocR9GELDB+fM/+IXcOXWSQCz5zq9M7aGvDPuqDh+\r\ncOr7/54onpawB9p+lqNTe2lR4Uq3Ipybb+sqALQgmtqobNW1KmysBNHSEHq4\r\nB/fvorkQc3MZ22+Z61OZe3m4ycVD3YAqqzNl7gs8sobPPnbDmmGAb+ET2PfV\r\nau02AOztDs42uP9Vpe/XSLaG+UDkpEJQIhfypifmaVR7U2TRXcZOirXUDCo2\r\nJwQxrT4n25xaKYY9BDrO2Fr0cagw2LyVRHe1SOm+R8O3If61ST+iKT/iuAsR\r\nFAWoTMYRVoZ0wp0NJuT60y77El+qSiCPUiJF210DSpb2vmCqEP8vZDqCVe2d\r\nSxhtmXsxHn5CWD8bVXMYKo/eevrwePnN/BA=\r\n=/7IZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.0", "@jest/test-result": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.0_1658095631413_0.632108066993688", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "440c477f2f404b5967b3b5a63c71785cb5d2e345", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-dbwSjXZ4Wl9qux0O5tQ6uPyimqZFS7za0T7tzCPV++bYZNwXm7lhDGqjBxQZDbx+KpcRyKRe/yWx2o4HKtItmg==", "signatures": [{"sig": "MEYCIQD8TTln8tg+lb1agXNmUPCiDU1moiJzdXc9iZLsaOHv4wIhAPVFKxkLZH6l+xw1tclCIldUywwk/ALA54ZDStFRgcXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdQg/9HDsiFgXwZktvDh6Ko+H5beKnSiv6tz79dpV8ExGmxf2FCxA6\r\nRMB95ifMURNwdHdm3aGhYJHroEEv7eS5ct0gH71Dx48hTUujYFezMWRGh7Yp\r\novIjECwzsc/xxW0BmyrGLznQ1JAvwyqBbF0CJFLiFzofVy2E0vQq2XeqVuc/\r\nlkLdzF3/tOt3eQvmjzPpzo1rjSNeu9ivK/0Lgfhi8/2VN4ViBF2dhsDOgdAS\r\ntnkzj0GPFekWXpYGNVqbMT0a0GftcFmOmOMY2B1ZPL/0BvMTkmkc4rp8Uj25\r\nBqhOgENYszIMH5D6DUH8KJ+p28vYRvwLh28mjV1E9iVIStzrYOTZ2Wds2+fk\r\nFtmwlJgJDQtmaRDcHWFVpYbN22jjr17u8C5K8ovrjJo8AvL64TDw5qyQKhmX\r\nBcZL37QAyustFypS8FQShn8CT6G2v67NnP5NaDhg7+1FpQmhLl4iwVfJcoPq\r\nGfRR9GhSLkkcuOPgJLXIuZzbbgvk9VUoT60lvpVlUvoH3eZ0iQwhiz+pgS1B\r\n29r7gYuN156pz2JPjNxJ1wDzXncNq4xozIN7h3fc8MWRVzUXDJDfK2kB5u5z\r\n30Wb8V2e3OoCddrIS1+/Ci40Ep4jLlpUEZBkDv9NKVYD8gI161eparTeOEmo\r\nPrwRZPScyyX3+tqQXUgp0spCjtgnlUtjrGM=\r\n=MLaq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.1", "@jest/test-result": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.1_1659601413553_0.006000956728522855", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "40c3e0e406f5a3a3d8dc9422a3a6d765ed6784f8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-NAzXZn7jq5jHNof9s867uaD8Br4ZyPJIkolhsDDHa/RaILAPUMAuscMpgBfhRyYHQToFdM15qLzj2txipEKRJQ==", "signatures": [{"sig": "MEUCIFtc1agpasYFWMR5GkcO3gAYGUwADHthnPS6gcnMMWG5AiEA5ILDBeQn28/yDp08nARagl58mYXtNmJw2Vvhxb/sY4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm6w//ZCwmuGMg5UnNsax6LyO3znxLhsTA2z7chRKcNXZ6aQaulr7v\r\ni9j2YQ+QUp/VUMf5+5glIFAAIFCqYKWsW6snNOLRJ+wyuXLl65kLfOWaYWpi\r\nVNSnaKKwNDeaoOOnEISYHaqXxWzlT0n/N+3RgE3wcAZCioPi29crhfcIIv2+\r\ni+qq3xPuBC4ROnK7IqKD7BhtvUiVqKrUkDMZ04WN+V6ztRabMlfxYRI8Czt7\r\nrJ32ZcJfVezQpV2DUeB9vDXwuwIy8LZzKIDADwmKYuVAVmCf3emrb5UMXU+/\r\nhsdWgFfO+41YLyYVm7IQNVtM90LbwJCFXhQUQoLrZC6GAx7cLWg9QxT8CZ0b\r\nuZoeTtw5/tLnlXzh9ReeFZV5M/vfzB919PuPVUT1R5mu1iPAK15ocwSMIbGF\r\nITeCjhXluZIZDK8cnP9kehCe6oEn4IzPB8jJusr9jpLltyD9VgPo121tyNP0\r\nyw512geT94sMUxptj0+9CC+qfh8kqIl0rF5ofLPlZUMwrB13Rkc9Whz1V523\r\n/FwV6CZnIIX2/hDpma27LxSA8P9swsWp1q7BZvyjV1i5Bh4hgulVVhcTX2Sm\r\nxbufEFJ84aPSu0qIqHcJ6qBamLPaowE30vW0Ddh3Pnsqp2QY1pS5qJV9U4wx\r\nvtDsfJlyBgcvK/53WDC1a2xgSLM9shpsn5U=\r\n=sLxy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.3", "@jest/test-result": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.3_1659879701890_0.06310363202759639", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "683e89436eeea2c4548a1bbe7ab8225a1cea9d5f", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-hZBFTpdC3azVjLg9Y9m2SsFCyy0SUfX8aw0TWC3+rJryGPj15s0qvpOVeoDEdLKWZ0fXKpMdFnpJHnRHiWa4lA==", "signatures": [{"sig": "MEYCIQC7Lq06VdHESKQACaMLHZXBV7bXhFq0vf1AnPlRjzOu7AIhAMc88zV9wVVj7xM5SDcRnltfJihicH8gVET3whZM5YqE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QolACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGYQ//dqpT7rlkvO1npRzKDwiUfZQHGm3pNWUa2Xg3fm+7awzyFdHT\r\nvkiQn4GKIz0cmJJp7phJSim2V87Zen676rlZunjTvPk1yuz1NW3JV7x54s2I\r\nlviKtbhxOWseonSUgmzOhRk1Qbw3k2o0pXnAIIsN9RxSXFqwcaMg7QawwiLi\r\n07h5nhq1WAgcB5a7GxOBZUoPYLrdRXpjJ93HL6CZgW6W9s+u342rNPu6Ni15\r\nDn5tT9FiKU2OJdyd8XcWXN3+rHzGWgnKgZFK4Vm+linoOa2uRZ8RYZ8Iuq7J\r\nHTli9BhlHSJJn0wKHtpLiNR+dfIn9vOJXR5Z7651peeAhZroQHQRJtt/eO5C\r\nak7zoEgILQ35qpf3xKBct6+WmsPAFbomqOg0Ygy0whO4gTJ15ZZW9ZKMGgtG\r\n1u58IVz1Zw4ACSOuQRyr2Ia2ocW4HvgOHqEQn9pJE5nMFE1+fYDkSKbfcaHr\r\nUGKF2Sh0twguwZSjN6Z1gcF6yvsq/PFxpcap8KcZEE9g1RJiZTvybci2I+UK\r\n1anW2tFWfkdZcgJPQag+sEG8Ek7VFJBha3A0xc6Jsw62ku8G8F+fC2r0etqq\r\nNYCoM4XsTAfnSAreRNdf/d7icM3zHUhHkGAji7IJYCjBSCHwB7oXIZDqGRf+\r\nLPDFIf3fo/5A18YtshCal6RnNSusEm5HINk=\r\n=mnNB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.4", "@jest/test-result": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.4", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.4_1659963940801_0.3803935691898972", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd064ee6225a66c88936efdb828acdfd024012a8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-RpIZ8OqCtG0ZlJBA0CgWjj5BFQ/5IwgbO9lr5/JrfjHlIOuddxFVdc90j0ZZUL0QoF5YVaVuUZaXQVfLylJxtA==", "signatures": [{"sig": "MEMCIENv+A8PWScDwepLjFP1PhgJP6/75PssA8ACE2DT+s3eAh8B1bek9i0WripzmKj1mS2wfVjnH2aAXwmDhPJ+S6eC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq37A//d+RuiE+rxAx7K+93DT8vxmy5xxaW05xcCJ5L07lgCrkMypQn\r\nqywebi1K8HDlZUfNmlS6lbQk9A5YUoEexW1eHm5IlxBjFJgMopqdrjHWcrSr\r\n4bZS2XSvSAwaB8x90VNdpx0O5R91FMMjfOwOhWf94L0avzZso7mz9QBYNEr1\r\nHZGkexYDXAji+VwGIeeirKldea28RPWJqbLESNQlOVU36hXIu0XtA5xHnXv3\r\nLoHi6ysYv9OIl3/LiNrg3Wg46a/q9ada1SESbLj8YG1VWPaVmHNGMQdGX2Dv\r\n9y1tOjxTRcRnj9i6QcCf25pSYgKQi1ITnUGiL5AhTFtwZwKYukZEKTSLtWqQ\r\nxYO0opKDmdG9xXNRYDzvg77s5yXTRT7VHW4DelWuY5SgoXRLkYIwlCyvyf4h\r\nIvqpcntNa+jeNdjWzW7YtDMj0zHhTz3OrS8jnr53rPBTU/rJj/f6lmOxLgO1\r\n3rA2KqrXFN/RehWHtQM8wR9x625kh1hffPNvS5/qc0K7l9A09l/D/k7EG9lt\r\nSi/80zUPHxz0QGxUaNAJkLFXbDVCj1SkXvDQWnbEkxpS0iyXFcnoC8nL4Bx/\r\ndPd/GSr/U0Xq+hLkEEDxFk98luEfnK/Jghu7MPnF9ffvrLOMqeOK6yWCn7sc\r\nd0copMtiuvMgnm+BWLvp2onFs9PmW4Jpu44=\r\n=R+Cp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.5", "@jest/test-result": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.4", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.5_1660225260096_0.13383160870779154", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/test-sequencer", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af0ece439da0bef0608c63918f9a2b0a94619b69", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-9YUhoEsooQyaQ71rqyNBytl6ERI8lfIvTSeu0nkpM+LHaphEJlsuJZLptLBOlhZgf4/+Z32PQJ9yWePa/94bYA==", "signatures": [{"sig": "MEQCICh0MvWuMA9T4UVhzceitm87VyZoKUDjJTa4FCNq8MzbAiBokZeAU7nzmevV9eumvwm/u/KTXskswAxngJo1PK8Uyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5blACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVpQ//Rx69PH4mtLm5vC34QMDTEQurkr5k6vRK/NoKpyrLE4mpXSAV\r\nYlM1X57IxK6HPCEQimvWQVxRFfyxUuV/yc1b8UaHJ1W+uD1g4ySIKEI4RIsg\r\nss1lHw/RtI6iPZr4Agts+Zvc3g8jkhXTV6U3sy8TvomoIsxyAbySqcm5iztu\r\n5XF5OlJ6Ls6h3Od91hQsbtuX1PbvJN/kqnfzNFxjzH7TdCEBNeBYQ3VV66yk\r\n7IXnfOKwpnoCz7x9pzkJED+5DhkrFGYXfl0vc1B/VbMsIlucD8L3OdJ0tWk5\r\nhCzDVsdMpwk2oglKvi2VaECkgn6EMsShRA+UtK+C4Z38uJ5VodRpHCBndY+l\r\njejmNmniLdS/LORTY477RKRukX6c7pL1QEuA2ml4m4e7EGP6BL8HDBKyByrq\r\nfo+u/v2HAHxVz7dBS/wbAh5AV7LfeHrk/mT0bwWo9eKhxtQ63NsF86GxEKkd\r\njTcg5o+lLiW3dQb4lJRYxNiBPx71UPtbodSnMgCHLDOE9nykRJNKoVrSXlzY\r\nc+QHdeJvyjjytnhcebuT9Z+fcXB1zxGsUDYgZD8ycNXkVQ+sJU5AdpvnNX8U\r\nuKnJkaQGLIjKESmbZXdGIluygIJKJU6iPScXRxuIwEBqlOfmsQpZs8SZ95Uc\r\nSj8WsG8kzvcEh1hmF/q8Hbc5kw5OexSMv/s=\r\n=mfWa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.6", "@jest/test-result": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0-alpha.6", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0-alpha.6_1660917477287_0.5371666001615942", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/test-sequencer", "version": "29.0.0", "license": "MIT", "_id": "@jest/test-sequencer@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "32da68bec6f83f5f2c7936946eac54d80ba96d0e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-uL6yX//SUME1c/ucbY365obdsrPjvSoNBwB80WTe+drYL4jf7A87vA2+w4hYwXJEIGQspv5skg3iB/sJSys7ew==", "signatures": [{"sig": "MEQCIFeBaUVxiBZxxBrrEMdscc05PuZKN8g8CFEP4ok96C/PAiBtxahDLffMnisavIVtCgc/IKmjB/cHkHWTKdHXBUP2Ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2weACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfpA//f2DvD+XnWbXEP4X52UJNtDArRcIW51Ud1K8+8yCirnZTknUY\r\n6/uK0FgxdmEugbXJMeP3REJbnfNpsAjQrIrFLKK1LjOYxpTc1jMCeu9okYDq\r\ntWNBF7FZb+XBr7vTTHJHf0/PSCWgl7KoqwHSE56txwMdIHIQEFYYAFgre6dR\r\nwPCWqzXAQBZ2PtUAliyEbmFcTyPbYQxrCJFQMHU2Cyrl0++GVgs6tSo4jYeM\r\nwDd+rzv1iMQ6Aw4L34iJ4inOnSBQAx5C690oCdgKzlD1dkAVBIvpQm22htVW\r\naG04CEeSoMjMhW1a/HEZhfr/rkjHPRNPlMLAlwzhVGBgkhbP0O+oLJFa1r1P\r\nIp0etVE/MqQXgkHeQkOBdB5pHR/w0z2HRFFUNUdBHhw2DO4PhW0xgrcAGWRo\r\n9qPaXi5pcg/jPMRV0iyLtFM6xoZH2XG675PpjXQ3AZ7PwN76FzhrVIl59yaU\r\n2F9xMFrrvMdAcGrwdIE93vqV4+41bbDeymvMXrYSzwqdMAoEEPVjvYX8CeYj\r\nGbPjijI6SAKurwXgnBNMUK3iULEmzJuYXvltRhDKRwmBSmlTIKe1e3ycOp9x\r\nLmLIQMfBuKZZNwdnjen7QeP60zUQ+/LuWca1u+k7u/ScMBUfG82u5swHg7vY\r\nLqxLg4a1RV1kWwTT1XIMq3wQlw266cyWd3g=\r\n=NxvQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0", "@jest/test-result": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.0_1661430814035_0.3715976583227898", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/test-sequencer", "version": "29.0.1", "license": "MIT", "_id": "@jest/test-sequencer@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7074b5f89ce30941b5b0fb493a19308d441a30b8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.1.tgz", "fileCount": 4, "integrity": "sha512-3GhSBMCRcWXGluP2Dw7CLP6mNke/t+EcftF5YjzhX1BJmqcatMbtZVwjuCfZy0TCME1GevXy3qTyV5PLpwIFKQ==", "signatures": [{"sig": "MEUCIQDYwvre8YDgHvcWzp/KFBB+Syujqk4g3nneknn0hH1SdwIgdcpVB/a+nbvsGGzpRgKtngo+h3LVDGt22hYxeexYsiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7WRAAkeRUWFqICPoj3E4CyAFGGVQniQyxmQKkuMKjESKjQfLvwPCQ\r\nnlv7ZCfJu6oqajRX5sGkbNsX5TQtKKHYrkN3e0cvDBiM5K+WfZVT+eOYUDR0\r\nMXiPbu0zdpZNQHZe8o1vYbzdez4AUnHKy52y6iaRo7EFiTDdfbS2i5vSLlZP\r\neGKfJ3JxbMDQnQDqA8Ym1O+UgGovE4Vac8RZ8jV6+Qh1oa6DtN8CV0O+M4UX\r\nYQnZwCJm4bs6PQM6t/lK9vdxnDClCJBvAJHjQgfQ4lsg55dwouXr8iPB4GxO\r\nCrhWkdTCfueuVK2BmWyRiidVWf5wAyMLBWd8yVMwqHXHr45BAEhQZFv3A1IO\r\nNYPTIQ6S1OUVDOsauBL2EWZIIXMkJt/AvIUDHD05ZfjB+xP+KzjDa5ALMO+u\r\nvMR9c40BmcfkpFMq/O+w+AxSO6/30T9mmCP2r0xk28bmCxMgC63osgVztg18\r\nBlvja46/LdFAXUDGrt2g5u9E6qZfhUu2/THMXSLM4KHRrv4kHvJZpDFxSCNV\r\n7kt7nZFx7AOq7uUTvGVCfN5/UGgoQyutUPWfphPxv/gDaIepMer0Xbkx+umQ\r\n+MdjYeEi0CxZHl9+99FQaaczPtkWcNwitRhTA1EofXhd7DiIHEqD9biqpeN2\r\nTtHQOQEoB6Vu0JwVvFYGbkXncPUZ4kdblFw=\r\n=BwE2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.1", "@jest/test-result": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.1_1661520887373_0.01845950925124229", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/test-sequencer", "version": "29.0.2", "license": "MIT", "_id": "@jest/test-sequencer@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae9b2d2c1694c7aa1a407713100e14dbfa79293e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.2.tgz", "fileCount": 4, "integrity": "sha512-fsyZqHBlXNMv5ZqjQwCuYa2pskXCO0DVxh5aaVCuAtwzHuYEGrhordyEncBLQNuCGQSYgElrEEmS+7wwFnnMKw==", "signatures": [{"sig": "MEQCIFRlqgS8n48Ypw9+WsHF4W/LZ8LkGsDllreuUBk3aBZWAiBSeU+REXzf6U13HLtx1T4tqIrBHZ6XuUig/HzJAQ0stQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwEBAAokynjRmX4XXTv90gwFuPuOkRNaN3j0DCSa3qAzAHXIK+SHZz\r\nePOGvvtWTUSH8A5K7zLIJuNT8h/+cz6gJrqHmcoHklvmlC/Y27ipfilLzpNY\r\n4q6af/ZepCsshssVp4pvJDffEQ+TJbVs7i4sYkqw7tgU5GgCIgnJk/SOW2E3\r\nke5cOELGee20UM2pgwgLss/0m3l93l7RMNLrqJOiUuCA4mtSPT6nstESFt6k\r\nRuG6X9iYAmuliC4Zgt4B8g5LiFsE+UFpZ92GcPpUBp3+Pp728lzT3+9PWP12\r\ncJDaS8zgP5hysfiUaUAY+pqoR4jbW7JXJ3733SBCuqjaENw5AcSJSlNUws+c\r\n6MYqC0YGjRqihEMN8YOsGx6lvstdospRUPtLa+gQmGOr7mOGAEMAgIIoPsHR\r\npvalj1WxQKuG3GYFiNSBO8tzP7j55oZT091a4ifAl4kqdXsnQcf4OflD/udQ\r\nuhmCFDOp9jww7Ip4BSC9V3RognmflACs2M8TuilxtEO7t33X7PztqaeppwGA\r\nlut4O1iKMqdWW29BjSw/PHA49Z1SFCBvq7F94hzrZGzPGEpL9tWbPYhuKqOu\r\ndM0gnKoamsZllCWmgg5JgCeI5TQg0orEF/RYg6lodxDKXLse983kxFaAohy9\r\nU5+95ZCbIyrV4QWhMNIMg3PqvIWJXoP772A=\r\n=qocE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.2", "@jest/test-result": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.2_1662202105263_0.6233313739645052", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/test-sequencer", "version": "29.0.3", "license": "MIT", "_id": "@jest/test-sequencer@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0681061ad21fb8e293b49c4fdf7e631ca79240ba", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.0.3.tgz", "fileCount": 4, "integrity": "sha512-Hf4+xYSWZdxTNnhDykr8JBs0yBN/nxOXyUQWfotBUqqy0LF9vzcFB0jm/EDNZCx587znLWTIgxcokW7WeZMobQ==", "signatures": [{"sig": "MEYCIQDYEYOnC23HtAbFcZgAo874Y0wA2Yx6uun3jXOYXQTDYAIhAJm2vrRXjnEKX3Jg04HN0V+3gv+xP1quKpg13jN2LSZh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQIQ/+J/AkZxVQiTT7u6f0DqCPyYWIbdeXQ5oWafSYLPn4H1RIUosX\r\na1qr44MW71sJg7CmulHqYLSqsLiw2KCQkY/ajSKilks47g53wj8OUyHGS72L\r\nZt+xowtapp9BebBU02Fd2Je+AX5ehXNcFF6gGk177JRFKXDhrkIi5fzv7bFd\r\niJ2uWPIr+M3ifMvV+NtIU+FS7EaDYDD0SevLevnJlwmNMVaq3Epa/gfE5FUC\r\ntApUIEXhXzlbrlt1ZrwHxmcXGJwLbluqqzcX/eEgjcIRO69d/swlE/9XOWdl\r\nf74PdDdIlivbm6i5tQnLwSgFySdYxPOKhjS93PhDJszQyY2MK1c7MNJw7cGp\r\neuw3FC55JrrIQnF3rfcJgLlLiJcTrMynJfzSb+frPw62tqPCDLoySSBtwz/K\r\n5+9mQihuf8RcqGrEeQbJhoYprJuXvpuHyeSqDN0kiEOfdT33EqCP8u4L/48Q\r\nBiX6WF/GShiG4auCAoKU6LMscxA6N1rGbuNt6XlYGnP1CWoPKT5rikMe/4qK\r\n+gWFydDfkgwoNLSEK9FfL+D0sIrm/tfs8lSfjz3Ib/2aUC+5097hSOFwlZZu\r\n9JhKM+cuvH42csBU+1okEQj3q0CBl9TumxHjItFpqkIbpaS+/cXz930c9ptM\r\nF7L0B9FB8oTvZWrGQEvKtVSQaer11IL757g=\r\n=PpxR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.3", "@jest/test-result": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.0.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.0.3_1662820910604_0.07907867808216418", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/test-sequencer", "version": "29.1.0", "license": "MIT", "_id": "@jest/test-sequencer@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d3d34497e860a44f3f80e94958651f58d3ce64f8", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.1.0.tgz", "fileCount": 4, "integrity": "sha512-1diQfwNhBAte+x3TmyfWloxT1C8GcPEPEZ4BZjmELBK2j3cdqi0DofoJUxBDDUBBnakbv8ce0B7CIzprsupPSA==", "signatures": [{"sig": "MEQCIAX1GZzB8PrDNW0t6huqd/BaJwQzEliQJ3sDQq0Nxu/KAiA7QBothTepFRvG1WjTKpqqWsExZYyXayrCWZlW8CQ47Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrklBAAjOoWrySBq4hc9dcnZjTX1gTtFqBb5De2UVRORbiS3rgV7qlw\r\n8NuPgZYHNBcNz4d9IS8CjLkgRP35QucWADBknI8jvK6bJJsTo1lDE2eaM5tZ\r\nx15+NOly/h7QlQBdwtd+614cla5kFaHIjVRmZIMDAyXEqICTfi4qwdQ7Kd99\r\n6YNx7/OkrLF7AlcH8J8SDE4yC0r4CFBICR+jUqrqb7r/RCUsuJFDjSPykJAt\r\ndq0D0GHMZuFVcsYO7ONEl6RmuiCiGV76k4NKMAkWzgq2NulSEBjwF9NjZYKK\r\nf+FQghFfF1fRvI+ZschmX/t1ag/MqPx+yw0e9QZjhrKiBACeqhlCeP4hscDI\r\nf6qy2wWYVxdxn1NkVlMyoqfdLmnnSD198jBD5hQWQxRUmVO+6SxK/jfIOaKZ\r\netUh7IzgL9QXyQXjkwYLlxGzgQpJy1zJfqBiHR8d/JXyNMjvQaD16pTURhHt\r\niQ48F5DmfV9eeEr4N07SPoJYUD35ayJq0eZhxMh1geFuir2PrjnFFttW2FZF\r\n1yxp92tWrt5Ump2UQ53EmZscX7sr+ph4y6/KhmXv13vCrXX1OdjBNJzZMiR6\r\nV+WzbuG/tajKWja3PLbj0Gf399bWDpmXMuQITqbxCf4pl4FVQ4JgTZ9EwKBS\r\nRJ8SHueXCv662a+JwGXVsPPSiHmYlqiLlZs=\r\n=fT6x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.1.0", "@jest/test-result": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.1.0_1664350666694_0.6608771661076753", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/test-sequencer", "version": "29.1.2", "license": "MIT", "_id": "@jest/test-sequencer@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "10bfd89c08bfdba382eb05cc79c1d23a01238a93", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.1.2.tgz", "fileCount": 4, "integrity": "sha512-fU6dsUqqm8sA+cd85BmeF7Gu9DsXVWFdGn9taxM6xN1cKdcP/ivSgXh5QucFRFz1oZxKv3/9DYYbq0ULly3P/Q==", "signatures": [{"sig": "MEUCIQCK75lg9XQ4/uw6VUmPDCYkNDVHibJSGexQywAOpUgtpgIgGtNEmQ9zBaFlJrpsABcaOSBAG/a0cpOtB1FBnqAt96c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0yA//QZTGj0J5VYuvWRV5Ni+1rcwe0fsILpHvHsxzkj0RE3tDCif6\r\n/RrWKA4Rn8D53qHnDOJ4gklWXgabLClmTfr3M+A4UZ9DsyWtjhr8y2JggTLl\r\nIxJxbdW6j9/AMgdoxRG3PkpgJQqABpqvqSSwPsFG8Sh60N/PhQf6fb8an0WL\r\nLgo6vGzcxLj4tDhNA39pvcabHIsOnS5WJT3ev/NZNwt1zqYfnizI26SzszKl\r\nvQrN4xCVnqZF/gT2d8G6bYSFJJuEbjW9Rs3sIAtgLtsmbphU0VNpZsDNyRre\r\nZinPYPFMtFW8NVQxffmXHm40yz7Ckfoyl27Mm91WPc8jZz3aK6/gEvRBDj6j\r\naWOm88eBHHf+FIqPXaCqrWyTYndC7un/lwJBCmLc8EN6W9R0Ain7C3dynQDv\r\nx2D9+yoAkoS93LwBluzzYkrVcTbx9zLUoelyOOuyTnI8sFWMNmTLO0ZRxpLB\r\nj81t9+SQks81SrxR0tNvcO7Id6+0Iwjfk5rq9wVHQN6f4WCXH5oK7cFDd7Lx\r\nP2C7eU75mqfrHj7A+/thQsxDKrB8CGTEbgPUGxfU2XKVYBUifbIQibJm5g1g\r\n+jDqyBvNwiy/7zNqESjHLmjWHAv/CL+S8QhbHOTgDeB60QGFsd2NTlZyt8sL\r\n0mKkzHRJyWQDtkE+hCGjiSl61aATWOtHK0E=\r\n=bWD1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.1.2", "@jest/test-result": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.1.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.1.2_1664522573123_0.6839796759807946", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/test-sequencer", "version": "29.2.0", "license": "MIT", "_id": "@jest/test-sequencer@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "acd875533f7ad01cb22da59ff4047de18e9d64da", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-NCnjZcGnVdva6IDqF7TCuFsXs2F1tohiNF9sasSJNzD7VfN5ic9XgcS/oPDalGiPLxCmGKj4kewqqrKAqBACcQ==", "signatures": [{"sig": "MEQCIDYE+SS69wFIykp/+LrIAQt4L4MHNxs7BmJYlZtJAqGIAiBAXU0gEcMtB9BkwseyLMaUhtFOv0jRgEvGnmSAaCZclA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDzw/8Dt1HJnrUDhKUIK3AN88VBuirDus3w+dBvn0ko+B7OQRHzKH3\r\nublR2spoHx3Z+x8P9G76nvq0i0NMWOnkro1PQB2o1rFM3F5wqCFWmUVmlksV\r\npm/BCYkvHdnjJIMeSzE+etQvISUHJE/lPAcpjGxJ1CQPaA9kbGdYwM1EjoTq\r\nCcDmkzVVVYK6Xc9qSrLm1QlR0FIA0tlSaS2S1d4fUWs6y6c2vdJUDqCY1Nij\r\nEl2QUc8jcGTJhxcPB6dfBIYk/ghs7vlUbCXUeNB1P4Yhx3hQIpMeeYuGjioR\r\nYu7FKiISFFAd83VOoLWLO+YA8Xw3nFv8dLPqT/NSkyvLwQSWMf0Z2Im1HYdw\r\nOVNnpkbNxJ92QW2wwx3+EWWsS4tW9f93DsOVwFlz0g2rRk1dALtYt9B9PeoY\r\nJd8hFuz883e/Ii+FdPYJWqc9KO8fOFFVohvdblCc++eJsoJ2wlxXxh804UgL\r\nMVHEEIg/iGuRAW9T0OnujE/U8UHZbxMD14u/+76aS5mmW/laasOEasuqNbI3\r\n8fRxLL6K5j/zc2noVR+P+U1qcxLsUAVC0XZ/JG0uRcDb/BbxQJwPmYoIWZp6\r\nhVGpcUyH/MRpIvX+BWXqYOd+4/8J1U89DV6mJ34vgByhkHcJTEsn+cdAiiXJ\r\ncdKoEyk12gu9AAqGJBbsEUlzet96aO8GKqQ=\r\n=bb3i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.0", "@jest/test-result": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.2.0_1665738841417_0.33503877398628146", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/test-sequencer", "version": "29.2.1", "license": "MIT", "_id": "@jest/test-sequencer@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cafd2c5f3528c70bd4cc243800459ac366e480cc", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.2.1.tgz", "fileCount": 4, "integrity": "sha512-O/pnk0/xGj3lxPVNwB6HREJ7AYvUdyP2xo/s14/9Dtf091HoOeyIhWLKQE/4HzB8lNQBMo6J5mg0bHz/uCWK7w==", "signatures": [{"sig": "MEUCIQCC8yZp3J6P+cnYfoTrKjoDtIy6Y6LBiKdR5Mu2GOu5pQIgKtqduXCaQGP92QmQuwzCKzxa2WCLpWbxMimKEQdyRhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTXA//YMsFzaTwaeH999JclbcLiMEnElU2yvWH3UXDnQ4Hzt6w598O\r\nR3v7bUtr/tSbythl3rHZPWZ7z8TgXEY1D5esAIXYQvqdd7zYyPZzd9eJk818\r\nPxtDSyrUk9V5Nqn/luFrMYv0YmTHeCxsRtPwN6jE4HgTui9cXf7WBFVTc5+A\r\npaJnThKJRJN4tStCOS4QyjQotRn5gdNN+CzcFLBsgTOKdPxuaVLDYeHwkz6l\r\nIFH+ECIW+ArclVwiZw5oTyrHBiRIYWNCfM5vTW7aBWVZ765ziQ4Hq7+L4T7p\r\nRnMYoMT+XFl+Tv/5HxQ7DmpSAMsYYUUjUpwyRFD8XSViNBXoH7p94aMXjNJ1\r\nEsj5gmMFVbmK93/5XAmL9VG9s1Gp9+svujnTAHvX6Mgh0qASp+VPybp7ALgX\r\nZgU3jL4LbOTf9rEfCrejtiSlWjAfoCciNmuZpwqBu2iDmhB7IFjamoJtbu2V\r\nN6+KRAwFx7iTKQAGHyUNmcigLozyDc6BCaRyGTML5W1EIjFsgLpi4HK8AoHK\r\n22fOhHI95/KmkexMEp4yYguGJvOykhWYP3D8uLaLt5H5kU+hiNHFGSfI1d21\r\nM2fhgtSQ899el5EY1/GR2L1eB/dhfIkCZsH1he5vXQYfO2IxR3Mn6zP0bckp\r\nVA7XbpDGpRST0O+AHu6D0X3jCWINzus7C/w=\r\n=U3FL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.1", "@jest/test-result": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.2.1_1666108817562_0.30083657381711104", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/test-sequencer", "version": "29.2.2", "license": "MIT", "_id": "@jest/test-sequencer@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ac7487b237e517a1f55e7866fb5553f6e0168b9", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.2.2.tgz", "fileCount": 4, "integrity": "sha512-Cuc1znc1pl4v9REgmmLf0jBd3Y65UXJpioGYtMr/JNpQEIGEzkmHhy6W6DLbSsXeUA13TDzymPv0ZGZ9jH3eIw==", "signatures": [{"sig": "MEUCIQC+krn+2heLizRUknEXgJ7s9x2pN9XuCFJzqydOnO9H7QIgdr+7qtgdMAYakI7AENxy7D7IaH4EfcEa1WCbof9edd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokvQ/+NtkSQ+taldQpByp2hjAnBVc3Onb5nk8IWtOnGBsMTYvFtDYG\r\nbSI1EIt0E5yQ0sDRqO/XbCUJCDaWxgJEGDvhiZkAS3K2W2VDmTCCnfADraQW\r\nK0XyUWqxNW08QGc4Q7g30WG35bsf+85hHh3ZkhOKhHh3Egf5ePkuUyjwkobK\r\nSeLNhYQ7KnsO0X9VWS8PCkNWT8tUr1SYMCuiVISPeq9jotBUCAPRhK5KeUJO\r\n+fjibKGd+LCbii2OnQ3uJ7CMqQtFH7lLf7mlKRyQUZMtOaeqaBEew1tU/8EA\r\n5/M4gcxqd+A3i0vF3sMi+iUEglzH4TO0E5NI6lKNJ9rZnj4D/kAXlrPrAfar\r\n59ME63Gkum3qrznyHZM54rMqW0U5Gj8KDaBCBDiKryUulvsBphFsUSZpuOrp\r\nZSwL94W5PcTGLMWnG7SGcv7UQtVgiep3Z81npmVHWW+VFfxuXiY/rLROKbrX\r\npqxeei5UGF8wB7jQNbZl8TMjdsZHH1T5u52NfeFEDAslpaCL7gvk5lpFv0PQ\r\n98gj8tHJtxdWBxxBW22CjQonit2mxaoPNoH+/Hx6scLSrtVqPuX791bZ5OAH\r\nY92AZLfgC9FKvEqsZ0B1smDrvdVnvj4OKaU94JFQeRhGQOWSrEpVOXn8kJPD\r\nsifUJleDvGSSIV01VSG0Ygb2q6BR8CD0HwQ=\r\n=2F3f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.1", "@jest/test-result": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.2.2_1666643046004_0.2719311867218146", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/test-sequencer", "version": "29.3.0", "license": "MIT", "_id": "@jest/test-sequencer@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0c2198fe482c26d763abbcb183992ae769bb7978", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.3.0.tgz", "fileCount": 4, "integrity": "sha512-XQlTP/S6Yf6NKV0Mt4oopFKyDxiEkDMD7hIFcCTeltKQszE0Z+LI5KLukwNW6Qxr1YzaZ/s6PlKJusiCLJNTcw==", "signatures": [{"sig": "MEUCIQCuw9+fz9MZRrXPRQ8DCMawv5biyj/Mz1mSzHla6Z99EgIgBJWTPiIAgUd5AdX85sr4nAvffyAYCy0WkE5onIxKUcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUamACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMDg/+ODUJFKLsK5RRaxGK2Jz5LrZWd7oY/YwlGFZWHT01fbn90KSk\r\n5ssUDtkx7To4WHGAOSfFnnFrgIxOMk4s9it76eJiUgQMZ3kryY7pLCniqu8Z\r\n1aqIkAlJvBcYh4oxkpOAYhtMx/O7dEsHqoNpT9jfXP2dIrdNi8+uypjeXqgm\r\nd59iygzfPB7bkXrR0Bsk/wxSRsOUaRCwQDgHZSS6Prt1CydjnUpin1mk31xz\r\nYjH8iYNPxGomYdkvAL99oo7PvaznqBG5sjqKdqJ7hOCspU9YiYuYKc/6fNni\r\nM460f5AIo5/9dlOpMIFvs4n/HU9sRC5exNPzBo/+pCeKLHT0bido6+3qA5NV\r\nsD8/MP6IGxozWq9r9MrwLAyVCfEWK/z+G30hekJvOmqIycENNhAQLn4Vnd63\r\n1zyK4KfVANPcsBp5c7glLYnFAiW78dClZ8sZTC1WmPHys8HrXLD7yclu89Qm\r\nRFIOXMeqJZJOGDyqaCb4MKhNXw9WyaYCOTvu+ZwHAVuedpQjAle1VTNzIw71\r\nAaoVjdSSXMaK/qtqJiP+dVzscpUwwn92MEf7Uut8CyPULYcR4vOj0GdvMpA5\r\nb4kyd3ewA0HCbwKYucnmOxXSWz8ih4rCB28s0Pj/4kvZZqun7Sv9sZPLrLB2\r\nCktiQih2WhGnq6mW5coXKeiKgVpVh2YDlJU=\r\n=6HFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.3.0", "@jest/test-result": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.3.0_1667843749826_0.17637516500070816", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/test-sequencer", "version": "29.3.1", "license": "MIT", "_id": "@jest/test-sequencer@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa24b3b050f7a59d48f7ef9e0b782ab65123090d", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.3.1.tgz", "fileCount": 4, "integrity": "sha512-IqYvLbieTv20ArgKoAMyhLHNrVHJfzO6ARZAbQRlY4UGWfdDnLlZEF0BvKOMd77uIiIjSZRwq3Jb3Fa3I8+2UA==", "signatures": [{"sig": "MEUCIQDyZV6pb5puG0cJgHkzBM7ZZLJbIeF8gJCR7LLHtTJsxgIgQ3z8vG6W2rtIso52IQEi9kN/K0L0l4MbCyYK/kKRy4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphZhAAlGkUw3OwpfoixV0dMDpBK8flItpYD7Ca2aE9j5uAt34VcTsy\r\ntF7ptYGk+3JsUCvQiKJ2p4IJyNcK+hjf8BMc8cDMrfin7vDmrtWd+G9zVJj9\r\nZQ0K9DCMHrEfcaHF/QxFtgxqPjT2BoTDLdgtGHRshU5UG1xigczbuM2RZeyz\r\nwtbFncngwJ7uCZP9zUBr3IZeKxbv8PLzuRlbOK1NRtQ4jEx12krOfCkTBjE/\r\nN91Nz0nQwNldS7I20mEflAdtFgzbMg/k9M0Ic2Iq6Msqyu10OX7sDMwC2MuK\r\njN5CgpoVd4h8B0MgYTIDjJkH8cbAWY99UOxDIW8YrftERdrfgFnDNMVfm6Ct\r\nZY5fUvqXvIbLbtdD7X2ShR2L8XPRNxrOA6hAhTtYaM96j20wvkJ5JYWnl3hB\r\nSuLkB1h1666L1knZz0HpO/7ilg6DhWaLNZWg+x0D/U0Is2bR0CpyOrUlTIVC\r\nou3vpK8mfVRBEgJQ4eR8KX41zWm90MTy3+Ro4WaVBuxOV7oYbHg36/fBGt6t\r\nDCvplqDWEpES2XYUwuETQQOLBNPmJCeDFE8xclljBpLoLMyGizC/75nubZsX\r\nZ5D9PdUeZBJn3G5NTBiriOBsMR5NY/MA1MpxUS6zpz/KfwAkqwUCqwmCRwIq\r\nLab3xTok3iZksgV9rmtVfsR3PAg1V7e6xd8=\r\n=Gyya\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.3.1", "@jest/test-result": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.3.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.3.1_1667948187011_0.39428158769218835", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/test-sequencer", "version": "29.4.0", "license": "MIT", "_id": "@jest/test-sequencer@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8041176fccae0f7b86055950461d158833a79b76", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.4.0.tgz", "fileCount": 5, "integrity": "sha512-pEwIgdfvEgF2lBOYX3DVn3SrvsAZ9FXCHw7+C6Qz87HnoDGQwbAselhWLhpgbxDjs6RC9QUJpFnrLmM5uwZV+g==", "signatures": [{"sig": "MEYCIQDt6Czwh1CKbhIvVh4LrdpHEVFFoijDmLoPc4mZHH8x8QIhAPS3OgQPJOKPkMq5Rw2mmLwdQ/v5cxJbBsOmO3f07q2T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7lDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdFhAAmGWTY9xA+KsanocJ89VRq6O7+G6MXbaqIdVOgtwEc28FShYT\r\nNbSl8FOdhZ4+dPow7MgOfrQmLoO8nLj5lWS62oyLgCj2SzC0EA8DahONd9R0\r\nwJ1hs7/8pFvzNWua8dcTuhe0lCgdQtBVzE/S4t14ULTU3Zb1KABZrvKwNSi6\r\nrMKHcFVP3qAw6NKN4Ws9bcOu5zdzZvowBU3FRPPwsOjIRMG2j7bw1030ZKDx\r\n3WfqjSVgExAJMjgpYDAOSPUbwPK7/ZelPIHUeg+bVA2RlUplLCU7KgKxH//c\r\nRmDYfFSonj8sz/IZtxf4g6xUg9bxl7dlx4ToRE5UXbXiyYP5MucmfwXmYpqG\r\na3bjHf42rNp20kU2q5ZzMOHP3ncxawnCWVng7TA2vwVnao35Y5yts/ZPgyQ3\r\nuAQXtJcFxCfekFY6C2EbgKA+024Fo68nZWRcNWAHts8BRwddkrULsCXP33uo\r\nYZmiULybxxjJHRjnsHK54S0cVVWDtWJVs0KC+cblGcCIGIYHeCAIZId5Jzkk\r\nxesPCCFR9APF+qY/hFhKsKLlN0cPvi1fG0SuKQM35tBs5+2BuCmxxzFFL+02\r\nDdetfOe034akF+orqsgd2hfLVDxFyOgMYxaiapKWigE7/RgI2BvrbZfds8uB\r\nJZuryme9FqEPd0m+VX+xayvzEssz5L7qBWk=\r\n=okK7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.0", "@jest/test-result": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.4.0_1674557763214_0.9958011984959343", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/test-sequencer", "version": "29.4.1", "license": "MIT", "_id": "@jest/test-sequencer@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7a006ec7058b194a10cf833c88282ef86d578fd", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.4.1.tgz", "fileCount": 5, "integrity": "sha512-v5qLBNSsM0eHzWLXsQ5fiB65xi49A3ILPSFQKPXzGL4Vyux0DPZAIN7NAFJa9b4BiTDP9MBF/Zqc/QA1vuiJ0w==", "signatures": [{"sig": "MEQCIF5pNbcqFXdLqqX0xSkADLjCcAJHwYxJ2MlalhhBkMwMAiAYEkrjisV7enZVsD7UNGZtHkg1ytPXQBJoz6USnJXdsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14335, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgYw/+LsWTvyLw9pyLPpmrTSvFlg3wbNUcc5oObfJ48lxPsKniezKT\r\nkzICw9+Dbxhz9+EdEDYhOnOP1mbRkYsbBvFVScUj2K8ebkK6dkMhqkmWsgHj\r\n/Le1F2yjFb1B9zvpAFdIJwyPziQWypM0DWz+pZwOvB/zuWEMKnk7k16kshca\r\nZFBNful24woPR+SW4iUtzxcou8nmU2Ug499mWak5HlSnxWfHN0A9v/UIcKNH\r\nLKpXqBvOjv3wxUstNu4BZFO9YuYksX6ovcQrVH/47qiJRzOQclSx4NZIGIyN\r\nBWC4MOMojmQCg3WPiOJmqcq6H5hSgbcl/CFSeqPRqohbJdDWzQCR2uVURuJb\r\ntYzX8TEyDyXM9vUBsDFf3HPrRCOuykv65oBWsyCz7HhpvLKO1pRe0IrY1035\r\n8SsTIcfIJzyejfCUQJRRBf22br8upVlzNCr3dAcy5YB7/3fEWnccawBea76L\r\nzyE/uUo71+pUrH6ICP3KlKsoXtX1nyCVJAvPw7LKSpH3XEm2Jw4b2fVLTrGB\r\n9LMQbUkp02Jni/N7JFp7IXoah0zEZfox2i7/G5q06uvc2GJC8+R8QLkSzH06\r\nEd+DyznlDzbOhs0Q4WNZ7pltor76iP6i4nY3kgPBgouusJgqCBpmC+Oo+aR1\r\nXQ0gM632w3NMG8MOuOxj46+pkwfiWYo1Y+Y=\r\n=orEr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.1", "@jest/test-result": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.4.1_1674745724140_0.9676363078590038", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/test-sequencer", "version": "29.4.2", "license": "MIT", "_id": "@jest/test-sequencer@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8b48e5bc4af80b42edacaf2a733d4f295edf28fb", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-9Z2cVsD6CcObIVrWigHp2McRJhvCxL27xHtrZFgNC1RwnoSpDx6fZo8QYjJmziFlW9/hr78/3sxF54S8B6v8rg==", "signatures": [{"sig": "MEUCIAwxwaS1MSC4TQrNfErautNY+eKKoXujxNAZrzAXxasQAiEAmBK1gqa2KMwCuui+WMLu3f6wB6REW7bVRIGCMr0+m6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5pA//aMEyvCmU+da38egcECit5HLLWuNZVAu/34xpu/e1JbhLBt4q\r\npBJWJ8yVpZsBeD6ChlcYEDj5/WcVp8rqVe0/T4pivHFwzoja+4ILjt73ydZx\r\nsNZI3rCQv/63Bcre5oRhfDgZI0+ssHWAMkybs+xW7ly0vtxdaQLwR+oSqhd7\r\n6Hp/kfEOOwCFTVlEPTBdAH/GKM4z8EbovLylD6+ozgT9vlWxVA7Iv6rK03jJ\r\njAoLGpEJ15E9k9q9fLHXZ4vk3jdp+rGW5bmzJwYWih6Ep+PFg6qz+UL7NGwd\r\nkV6O1X0wBb64xgDJx6/1JGiAP/5IP7IbF+ZGbRI7GMR0Jk7hzn6rwgCdF04Q\r\nCJ7vs7A6vEXye7T92AI2KNup/FHh8tiC44r/eo26TNA6TJt+cOOlnRocgm55\r\nX4G2sl5YrSb6ctDtpAfsrSCQ3/LBvaoxFOIvbTUf2K+0Oy06WpK8XMtE77o4\r\nMVnPRneVwwsqbQ3+8iZwdW6qVcHofYa3XLUEtoYAKozvYEGmCMo5Y87229oA\r\nNJEFBzK7i+JRbT7jpigbMPR3P4Z1iP6khyS5vC4XGTy9SMZKwPeW9SeBFPJW\r\nWZoSYh+gWrABaDMABe3Krx3Y9od4kltEWCJuxOz7bwIob/NwoFmeGCm3W5tw\r\nIXSUgNwkSdR6yT5TFpED53UFPFYmwbucCNs=\r\n=ykxu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.2", "@jest/test-result": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.4.2_1675777543240_0.6680993848161709", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/test-sequencer", "version": "29.4.3", "license": "MIT", "_id": "@jest/test-sequencer@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0862e876a22993385a0f3e7ea1cc126f208a2898", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-yi/t2nES4GB4G0mjLc0RInCq/cNr9dNwJxcGg8sslajua5Kb4kmozAc+qPLzplhBgfw1vLItbjyHzUN92UXicw==", "signatures": [{"sig": "MEUCIQC3S5kLJz5EuasjoDEOzkaijCDKdHKD8I87vPwuBM16vQIgOfCEdg+T1WR4F1NqWdYwaY+uiVzhsnHd3soIBNO+T6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MisACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoljA//WyFUOBW024Ma2DNRcyAC4krYR9tmUq848E10oK+CdLPDu3uT\r\nHAUGSYvg8KvQMMfxJeVEU2cuYFYKYtnyH3RR36INbPDz7r306pEme/iDDyI5\r\nL0trYjpU24B1BnFeMwU7tmOhebBXzk3eRQF/oAZWq577qfxlsU5H7gTbkK56\r\nHggZBLGiP+WNTn+c2POvzAp2F6VgZ02bKujMQePTYOhxeg/QV3l3XAxsZm3u\r\nBapC1Nqe2OXdtMNoZW946folZyuZgTi9yHBkatWzoeYtWXn9HDGI6sedkLs6\r\nzMwv18oMNJRyAWfFLNDEYB0U1QXBXlfZsumjGqyt1AjijzEfSa1ZI9yGkxfO\r\nsDAKD5YwvO3aLXrpc6ZpAiW8/IYhNTCUeFKT0Rs07YJ6NpRHkgNWs2wR+3jK\r\neOnqfDsHbfcXFmADKVRqesFeEh8cP269/xa7u9qe+26zUkJw1o9OZVqyIqwj\r\nsXJ2rpNoVl3e/DL6KJBymnEZkOIJjvEbHaOOwsRz1zw4LnSljA0T9O5oVsl1\r\nngB+UwgVLH/mqduRaqcugzR7omsk+aMiIbS2Xa2GCTF1fgO9QelndB+EjSO3\r\nrR8MxOYgGwhzdAiPqBbHKgdBWE8faBeemDrSsOxp5Gw4K7KudG0i2NtePPim\r\nOzEN4c9V8rrlNKKPXppt2t+MJWdRooDx6to=\r\n=h1Yw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.3", "@jest/test-result": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.4.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.4.3_1676462252547_0.4682992278997167", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/test-sequencer", "version": "29.5.0", "license": "MIT", "_id": "@jest/test-sequencer@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34d7d82d3081abd523dbddc038a3ddcb9f6d3cc4", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.5.0.tgz", "fileCount": 4, "integrity": "sha512-yPafQEcKjkSfDXyvtgiV4pevSeyuA6MQr6ZIdVkWJly9vkqjnFfcfhRQqpD5whjoU8EORki752xQmjaqoFjzMQ==", "signatures": [{"sig": "MEUCICApah1Y7Na/k5X3RaRnIp+/+Obzc/pQnWgyQFevUGcyAiEAwkaA2nnmxoOpPvGeKeTVfJrNzMa22iwlq7Bo+PRbdZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeu1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOBA//aPDdjdj3+p9G55mdHbnUZF3jk0SFujIihPgVz/vuxvgtYCAt\r\nRaCy8OpdMdMRG013IDxmP4vb5rahshlDvIf4qb2qoxXMetTFXewbMJqaMWRD\r\n2ycBM73tMnCe1Zn2vGeq7rEKoVDoRzOOeMUHqc84eFe59F0nyLWlHVJClGwt\r\ndvA/3IrY3PRsPGSCyEmvLtxIt1Hcv0AHo1LRVIrwAGEbBNukHwE+MpJVCj3a\r\nIOQYPWGtejC4hiuG1nVJMUuYUnhLw25v7P/rMXOs2XXcNLavmwQ6bvMhe9q8\r\n2SZnsp5YpEA7H+4Em4tuEPRZFJMTEcWzlp8cN2fpYCEMr1hv8IaQk6bVbCWs\r\nnH8TIGkTPd/S9hEXy08fCqOV8OHqWhmOMl3H7p2sp1dm3Vuks4ap4zjbWUss\r\ncp0xG1qJ9kfyfU/u/QnRto8ub/8/wxhs4lXgYjArCfKzddQLZ6tUe7tvkPiZ\r\nwYGL17YtQyCb4kC183pS/LKHrpiNS7cCrMxvCzDFTylZsgN5RTDJ+eINKaaS\r\nQYQS9LsCIztSyfqewJeqPulLVflo0knA4Z8mtAJNSlLY13+94aw9nQ2wBHBq\r\nwUaNpaP9TGplvxzjjQZclcrxkjTM+mhNUNFvd4iddYlN32K76g/PszPx3vz4\r\n8XPtOXgrUM8EhI4SukL/j8qCrd6Xjq8jEKk=\r\n=Nk4k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.5.0", "@jest/test-result": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.5.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.5.0_1678109620825_0.2803656632172169", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/test-sequencer", "version": "29.6.0", "license": "MIT", "_id": "@jest/test-sequencer@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "30a70e2dcc7dcf1e0f1170b97384883ce0a7d6e5", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.6.0.tgz", "fileCount": 4, "integrity": "sha512-HYCS3LKRQotKWj2mnA3AN13PPevYZu8MJKm12lzYojpJNnn6kI/3PWmr1At/e3tUu+FHQDiOyaDVuR4EV3ezBw==", "signatures": [{"sig": "MEUCIAjWeHomOOj03jsC8B8xmV2FePNRpWBHyDCiFRv+GaNyAiEA/agyoHWQPdmwSyQCya+xCD42TMLlsvaBCr4RYfXLaS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.0", "@jest/test-result": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.6.0_1688484354363_0.49924136570522304", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/test-sequencer", "version": "29.6.1", "license": "MIT", "_id": "@jest/test-sequencer@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e3e582ee074dd24ea9687d7d1aaf05ee3a9b068e", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.6.1.tgz", "fileCount": 4, "integrity": "sha512-oBkC36PCDf/wb6dWeQIhaviU0l5u6VCsXa119yqdUosYAt7/FbQU2M2UoziO3igj/HBDEgp57ONQ3fm0v9uyyg==", "signatures": [{"sig": "MEUCIAmXawBQjW+fkJ7yJFtxQcu3DjYBgJ+bb3wjkyggYpsoAiEA0jh1UqcVl3nHhHeG6ladvO6lgUUBWSuJfQell4dVEQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "@jest/test-result": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.6.1_1688653115895_0.6274516902118981", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/test-sequencer", "version": "29.6.2", "license": "MIT", "_id": "@jest/test-sequencer@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "585eff07a68dd75225a7eacf319780cb9f6b9bf4", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.6.2.tgz", "fileCount": 4, "integrity": "sha512-GVYi6PfPwVejO7slw6IDO0qKVum5jtrJ3KoLGbgBWyr2qr4GaxFV6su+ZAjdTX75Sr1DkMFRk09r2ZVa+wtCGw==", "signatures": [{"sig": "MEUCIQDpZPtINW2nIl0kQLcUWA5HBwIkwWIW4qIWttsQyJR2qQIgQVHmCHH16xc1vaS7z06QIX4NpI3aX0EcXz5aDWtodsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.2", "@jest/test-result": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.6.2_1690449699615_0.3086493260685703", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/test-sequencer", "version": "29.6.3", "license": "MIT", "_id": "@jest/test-sequencer@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e59f422bc3786d79fac504c16979a5f1b999a932", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-/SmijaAU2TY9ComFGIYa6Z+fmKqQMnqs2Nmwb0P/Z/tROdZ7M0iruES1EaaU9PBf8o9uED5xzaJ3YPFEIcDgAg==", "signatures": [{"sig": "MEUCIHVXH6+tYa4URbsP/VUbjBQPbGOf56Eh6BJ0y7g0Mue1AiEA/kkVIyvRzRPt7pfkzqjlC8ObJ757PqXurRvlCi/6b0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13494}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.3", "@jest/test-result": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.6.3_1692621585616_0.7262537920480518", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/test-sequencer", "version": "29.6.4", "license": "MIT", "_id": "@jest/test-sequencer@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "86aef66aaa22b181307ed06c26c82802fb836d7b", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.6.4.tgz", "fileCount": 4, "integrity": "sha512-E84M6LbpcRq3fT4ckfKs9ryVanwkaIB0Ws9bw3/yP4seRLg/VaCZ/LgW0MCq5wwk4/iP/qnilD41aj2fsw2RMg==", "signatures": [{"sig": "MEMCIG+g688OKW4lh2ijZWHMLjr1tLMMXO6Eknkvm9MVPCS3Ah9nsVqSZIJmOdDKlRVK2nbdnIc/KBcrDOWj+/d2Nr+a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13494}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.4", "@jest/test-result": "^29.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.6.4", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.6.4_1692875472491_0.27946291514323973", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/test-sequencer", "version": "29.7.0", "license": "MIT", "_id": "@jest/test-sequencer@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "6cef977ce1d39834a3aea887a1726628a6f072ce", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "fileCount": 4, "integrity": "sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==", "signatures": [{"sig": "MEYCIQCamEolpDSmrXINj5z5t52FqfVjFpaCUTwTCPNtTWRHdgIhAJdd0y2jGXoi6e+kcjNmElxsPM7Gt64JRM9ShG3PX9oz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13594}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "@jest/test-result": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "^29.7.0", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_29.7.0_1694501032327_0.20754875826376917", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "9026202a6b8271cbd33c6e8739dcd85cab9c4762", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-uJmk9z32YKQ4Z9ObVaTz/O90RvwWE/dfEJc8Lda/sJ62NMT7GHz0dfuz8QuCpv8i/HoDgO79SgbOfLAfFaeSNg==", "signatures": [{"sig": "MEQCIDn0V08iWhrwTdKkQjNsBl1S8sfipc3PyMGU/AiucuQDAiB8HlvnZQK3URf3PwEhTHDt3uOAVA33aYNsx4kzYLqBqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14021}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.1", "@jest/test-result": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.1", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.1_1698672801768_0.44284892215376526", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "33cf6ecea09c67728c418ffc44d9211c4e4ba6bd", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-J4BmIeJ6RLUT5uhLOHK05KG8zHmFjduc6uiWiskUF5XlqLkqetTfF+Qpp1X2/fyCHXd8UYB+lXQH/kqNvN4vjg==", "signatures": [{"sig": "MEQCIGMGITeJf2Hr3kdDP4h/aedL9qfwwGdoTupe/KOQxHraAiAVMl4wqyTjr/GRgEEN1xqCVbLTH/2wZYzeWAbQTe5h3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13934}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.2", "@jest/test-result": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.2_1700126919593_0.33962188452758113", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "66ffc1ae97db96fd24f23312a16e391285014525", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-yH/S3G0iEcHTgWxK6eM/5cehSepQe+X8KK+a2QMAwj5TWsGNsPPNdGnhCpSmzWB9v+bNvwVC4nSWsh+T9uwMwg==", "signatures": [{"sig": "MEQCIH9IxpKB55DQq+haPpd1N5SPxyFsfViRgWA47F0umSw5AiB/W5tf/LO2AG2m4OlUVQ9L794oNqR/j1jPXpFCEg8ZgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13946}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.3", "@jest/test-result": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.3_1708427365254_0.8046711269235711", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4c23dbad599d4ea88fbfeb98f96e3e8d419caf14", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-S375F8oZd8yS/3obeDtXzEUHry97oqw1RwVEuOngf7ieMqldTY5bIvnG1xo0dNHrIE3xvrhlGxUZRYKiNXH15A==", "signatures": [{"sig": "MEUCIQCuWbfqes6wxTWEE/3Q1AxvjMmagDcjtGiTEDNPZ98EZQIgDnOLvitVjdT4cHjG5fVHU1eZkK1gENyV9PW0IwjAods=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13976}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.4", "@jest/test-result": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.4", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.4_1715550219830_0.24625614985585753", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fd200c5b16e51b9cb13aeed5e2f9e264237429c3", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-h8ZCs9XTPLJCxbc2o0EHCFsogx6j2RFJQsWuEoD7drnGw9cGYoopgZUjf64WaptGxh+WUwuqadfceKDQ//usMg==", "signatures": [{"sig": "MEUCIQCp7ndIvOqUDQGLCmVzttk+ZJS/ioX2f/gXrhSOz+uYLQIgKl7biN53UvVf8YBTGSmhoBFyRWb5PleRYvwqiHBZBhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13976}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.5", "@jest/test-result": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.5", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.5_1717073058611_0.5225666766563", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "e28a827a103bbb7b27558cfe4daa78a443ea8cc4", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-5M89jbSQWkBjGlFrRk2wXjRJVxR+uN553sFN0q2TglH0/a4OMSVxRBcCmnIqqDMDizGAlYTxW6BaXxHGHpvrRQ==", "signatures": [{"sig": "MEQCIH5cDhbfIew//9vUE50oKN1yQKIXy2Mwx1g2VEVUpc8FAiAiHrOtQSJ6KgdGupjmS1JPeBI3pkCux2YfUtAAnMk1Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13998}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.6", "@jest/test-result": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.6", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.6_1723102994105_0.050505659410783776", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/test-sequencer", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a4a2143d47af2f14da522f80d2552049766ab838", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-X4uXuY0nqg3/4SYrzIwScFs2KBGa0yIr/r4LLmakdrP0BzTOHOXZG+i3nqVlLFCjHwJQInuxDx79dI3UsJOt2A==", "signatures": [{"sig": "MEUCIQDUY3T60WZXQqr/LofFnroIsiIjvt2nA8Wd7x9UsajESQIgLywlhUY3MjPBVMYcIvnt4XTiQ/icq2zYLOR2qzmgwEk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13999}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.7", "@jest/test-result": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-alpha.7", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-alpha.7_1738225722859_0.4523572842383936", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "02883fa0dab2e20bad876d605a54a7d8db3b5141", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-5KqJxdzfnh6tV+dZDr6zoGQ+h2vmxBW3gHyWpZ35FbMR+mvqv5fn+V5RhlwDAlk6Oz68tCjvubNeNdOtrDes+A==", "signatures": [{"sig": "MEQCIAeHlPD0KJi+YD3ZDDplyDwYdUV6Aoc0L6qkSyK6GBP1AiA86Fgrt5UOO5oIrQOzUeZLB3xwg/mhDflnYoB0GkBGmw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13824}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.2", "@jest/test-result": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.2", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.2_1748309008062_0.5615986822662384", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7e10ad812bed82bea6d268cc201799def073d878", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-mApbPZHqWNbvaRGHjbgXZHhRAsVDajQ7rnLAAQ03IDUE24nWR+FpsCHqtt+FGYK7FSaLV73+u+Z5uglV+vJqUA==", "signatures": [{"sig": "MEUCIQClxtvyIrQVGPiVLFObUVuG16rUfPs2DTZCTL7KYvsnSwIgIWRPs6OwgShVNnq2Dz2t3JHfMx6hQljd+bMWSX94GzU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13824}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.3", "@jest/test-result": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.3", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.3_1748309277493_0.9397624649396452", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "25ee201b17cc2eb57273c6b7549d710bdca64525", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-6rk3mAqB/EQLC9Bp5MG+Vgb2Ge/tfen5jZKUYEa18N4GPjnPb+QdIFWAw4m3VoDg/j/FVP4keX1b/77gZFydgA==", "signatures": [{"sig": "MEYCIQD3vToIOY2oJvO5m9KAXYCY70RDyFgNFqjACawaaxpMwQIhAMfzSLF3Fs6t4AHgu/J4YyWQOFMRiEW+1LS1CeUfINK6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13824}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.4", "@jest/test-result": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.4", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.4_1748329474004_0.25581671767525216", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "007f9ab934dd12dfe8ddb97c55f3da998aec1fa9", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-yhsJt1lgOS7ueV4mtm9KOtM/FgWTdCLFIgn8XGj8mnikJtLPq1vc+yuxwHnt6ORfzsMX45sCxLK3ld+yS0Ax+w==", "signatures": [{"sig": "MEUCIBx0YjAxHisxkAsWdbCUsUopopE/g8xLBlb8pO9u5I7LAiEA+/zTxKZRvI9bfhosIxfAsI6vQ0znPW4TuWk0Tspa9tw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13824}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.5", "@jest/test-result": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.5", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.5_1748478617749_0.849962692315362", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "4c47242316e683e2d19028643df4e79a176cb5a0", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-CJONBhgwLHHsxrpO6AsmZNon52AnWUj17AQQiE0fLeKSHjiM/kTmfuL1uafdbxJ9OVTgtfnuylTUyL8AGsAIuw==", "signatures": [{"sig": "MEUCIQCbQvrz5x7M/9i66b6A8jVBrLQrhTPUeYuwnsWIliOh2QIgWchMsfqOz5KbOrRsA9EvTWeOsotJKQSZ8NOWdwWxySs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13835}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.6", "@jest/test-result": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.6", "@types/graceful-fs": "^4.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.6_1748994659066_0.6153974628074743", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0c38361cfabf9eac87dc56f5ad0540d22f4d91ef", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-trGGiJnkMEx5Bd6kfRetvfwTwUFNrCMjYmQ/1SEgVmtMf0EvENCYh90qJeocTo7wTGDtxUwpt1jEKxRU3F5igw==", "signatures": [{"sig": "MEUCIQDH8zTmMG0bliYNDE+xC/Q6X/lBMv1MpOBgj2WdvdjlnAIgBnq8GuU/65XpyP/ERIcphIgCwRM9ZvR89XBFhiXIabs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13836}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-beta.7", "@jest/test-result": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.7", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.7_1749008152091_0.9894643755502515", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "d74f9672310f930405135657b3718c64d3a0f343", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-MBeurnvRygKhAYCc1pPdPAhIacfVsJKYdf8r3cv8/ICSL2xId+KuWpKlQdj2T0Y0EiELs+8uuvVqnYQN1noCZQ==", "signatures": [{"sig": "MEUCIQDc1c6FfLBC1p/d5F0WM368KkRzuIYlli5iCGQcVjRjkgIgCNlIc0EyK1Dq17bgVHjjtFyoo9pR7H+LRwDpfykKMpo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13836}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-beta.8", "@jest/test-result": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.8", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.8_1749023599889_0.6853597808057148", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.9": {"name": "@jest/test-sequencer", "version": "30.0.0-beta.9", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-beta.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0716dd48f5c4b30100b17fbadf5650ee3e1001a6", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-beta.9.tgz", "fileCount": 5, "integrity": "sha512-dc6ekNnq3O4gIHWedGZgInP5lrXVkmMgdJlTDMNXXEBezAWYOPwLC7WnXQRsxKLaKqTstUJuqitPqpmsenxKrg==", "signatures": [{"sig": "MEUCIQC9saYroPFVjYfzq0cHFf0maxGgeqCwrLJXbZZOx7PQNgIgSbjFEIUNL07VjAuYO3tikAkP3w+VBm1S4DXBHQDyCII=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13836}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2f52a9ed429fb8797a99868860430d55db6d5503", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-beta.8", "@jest/test-result": "30.0.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-beta.8", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-beta.9_1749109236600_0.11265795870713968", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/test-sequencer", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "6618731c35978d808e4044823548eaf6c01b5b7f", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-1OvBsP5TV053niyt+D+F7s5FAwwhHXLZjHvCC0TaPgtM7BCYi3DRuZhmVvtMRvlsIwjcm4ckIqzckWPfbCR7PA==", "signatures": [{"sig": "MEUCIQC3gLoQuph4wab77YPGnr5lmkt5UN6FZzvzpqrjlYteaAIgWoHADtM3W3SWH3ByN3dFZt9KoYjW0hKPJ+CwoZ0Of6w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13828}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-rc.1", "@jest/test-result": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0-rc.1", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0-rc.1_1749430975834_0.10480387842004468", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/test-sequencer", "version": "30.0.0", "license": "MIT", "_id": "@jest/test-sequencer@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7052c0c6d56580f9096b6c3d02834220df676340", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-Hmvv5Yg6UmghXIcVZIydkT0nAK7M/hlXx9WMHR5cLVwdmc14/qUQt3mC72T6GN0olPC6DhmKE6Cd/pHsgDbuqQ==", "signatures": [{"sig": "MEYCIQD1loktUTuITAcUD7y853ppJA/X5cjBwGs7eOyU41SV2AIhAIzciQ97tktdZJDFxKOj+QWQHwfFwUQb/hAsDhdproAp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13808}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0", "@jest/test-result": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.0", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.0_1749521761549_0.1455354488752545", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/test-sequencer", "version": "30.0.1", "license": "MIT", "_id": "@jest/test-sequencer@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2628b28a1129c6ae06c82065917eda48567ab9b2", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-2D3F5XSPIfGMvdK+T6z8fExQso3sPnkBJsUM5x3YQ1Aaz+4Qrs4X8eqzMyC0i0ENfhcijidzz5yMTM4PvK+mKg==", "signatures": [{"sig": "MEUCIByi0jTD533w0hN35wi1r/6Iy13QaM3lKEXxPrm+vaDbAiEA6WAR80LqE+0NAmKLtVT8Fuo/OvY8HZmArdkFkUV4g+4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13808}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.1", "@jest/test-result": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.1", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.1_1750285899178_0.8326566547403709", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/test-sequencer", "version": "30.0.2", "license": "MIT", "_id": "@jest/test-sequencer@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2693692d285b1c929ed353f7f0b7cbea51c57515", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.2.tgz", "fileCount": 5, "integrity": "sha512-fbyU5HPka0rkalZ3MXVvq0hwZY8dx3Y6SCqR64zRmh+xXlDeFl0IdL4l9e7vp4gxEXTYHbwLFA1D+WW5CucaSw==", "signatures": [{"sig": "MEUCIFPJqWGSgnWS+pCItnTsSe4B0hFqkai+QEJwVenXkxjgAiEA3MfUTsyhmDTnb+P499brLG8RfPVi3lrywI4BYpFxR7E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13808}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-sequencer"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"slash": "^3.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.2", "@jest/test-result": "30.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@jest/test-utils": "30.0.2", "@types/graceful-fs": "^4.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/test-sequencer_30.0.2_1750329988235_0.6918141098313471", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "@jest/test-sequencer", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-test-sequencer"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/test-result": "30.0.4", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.2", "slash": "^3.0.0"}, "devDependencies": {"@jest/test-utils": "30.0.4", "@types/graceful-fs": "^4.1.9"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "_id": "@jest/test-sequencer@30.0.4", "dist": {"integrity": "sha512-bj6ePmqi4uxAE8EHE0Slmk5uBYd9Vd/PcVt06CsBxzH4bbA8nGsI1YbXl/NH+eii4XRtyrRx+Cikub0x8H4vDg==", "shasum": "4ef749c994beca340e274e67a4c90f0154482e5f", "tarball": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.0.4.tgz", "fileCount": 6, "unpackedSize": 16539, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDjsC1iwO+pdlH8t1IrMvc1+IVhmqNp5PI4auf8ZKJFIwIhAOBsC42u1GBYn5wZGAeq4Wn+Ki4/M7QXvp4woHpkdvMU"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/test-sequencer_30.0.4_1751499949681_0.7629431424633306"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-04-03T03:56:11.641Z", "modified": "2025-07-02T23:45:50.183Z", "24.7.0": "2019-04-03T03:56:12.366Z", "24.7.1": "2019-04-04T01:19:28.273Z", "24.8.0": "2019-05-05T02:03:09.832Z", "24.9.0": "2019-08-16T06:17:54.508Z", "25.0.0": "2019-08-22T03:24:42.542Z", "25.1.0": "2020-01-22T01:00:28.538Z", "25.2.0-alpha.86": "2020-03-25T17:17:03.664Z", "25.2.0": "2020-03-25T17:58:55.917Z", "25.2.1-alpha.1": "2020-03-26T07:54:55.023Z", "25.2.1-alpha.2": "2020-03-26T08:11:04.428Z", "25.2.1": "2020-03-26T09:01:50.083Z", "25.2.2": "2020-03-26T15:56:07.071Z", "25.2.3": "2020-03-26T20:25:26.001Z", "25.2.4": "2020-03-29T19:38:57.752Z", "25.2.6": "2020-04-02T10:29:52.938Z", "25.2.7": "2020-04-03T07:51:27.435Z", "25.3.0": "2020-04-08T13:21:52.909Z", "25.4.0": "2020-04-19T21:50:56.152Z", "25.5.0": "2020-04-28T19:45:54.795Z", "25.5.1": "2020-04-29T10:55:18.923Z", "25.5.2": "2020-04-29T21:50:08.642Z", "25.5.3": "2020-04-30T22:13:31.927Z", "25.5.4": "2020-05-02T08:16:47.606Z", "26.0.0-alpha.0": "2020-05-02T12:13:31.754Z", "26.0.0-alpha.1": "2020-05-03T18:48:37.613Z", "26.0.0-alpha.2": "2020-05-04T16:06:00.285Z", "26.0.0": "2020-05-04T17:53:46.243Z", "26.0.1-alpha.0": "2020-05-04T22:16:24.463Z", "26.0.1": "2020-05-05T10:41:32.255Z", "26.1.0": "2020-06-23T15:15:50.336Z", "26.2.0": "2020-07-30T10:12:13.085Z", "26.2.1": "2020-07-30T11:35:49.762Z", "26.2.2": "2020-07-31T10:54:04.149Z", "26.3.0": "2020-08-10T11:32:20.494Z", "26.4.0": "2020-08-12T21:00:48.537Z", "26.4.1": "2020-08-20T08:31:47.785Z", "26.4.2": "2020-08-22T12:10:19.154Z", "26.5.0": "2020-10-05T09:28:41.229Z", "26.5.2": "2020-10-06T10:53:20.644Z", "26.5.3": "2020-10-11T17:49:31.109Z", "26.6.0": "2020-10-19T11:59:12.112Z", "26.6.1": "2020-10-23T09:07:06.834Z", "26.6.2": "2020-11-02T12:52:00.520Z", "26.6.3": "2020-11-04T06:18:01.186Z", "27.0.0-next.0": "2020-12-05T17:25:54.555Z", "27.0.0-next.1": "2020-12-07T12:44:01.729Z", "27.0.0-next.2": "2020-12-07T14:36:05.375Z", "27.0.0-next.3": "2021-02-18T22:10:33.302Z", "27.0.0-next.4": "2021-03-08T13:45:32.243Z", "27.0.0-next.5": "2021-03-15T13:04:02.663Z", "27.0.0-next.6": "2021-03-25T19:40:34.342Z", "27.0.0-next.7": "2021-04-02T13:48:30.313Z", "27.0.0-next.8": "2021-04-12T22:42:56.797Z", "27.0.0-next.9": "2021-05-04T06:25:38.474Z", "27.0.0-next.10": "2021-05-20T14:11:55.762Z", "27.0.0-next.11": "2021-05-20T22:29:17.099Z", "27.0.0": "2021-05-25T08:15:58.739Z", "27.0.1": "2021-05-25T10:07:03.876Z", "27.0.2": "2021-05-29T12:07:55.091Z", "27.0.3": "2021-05-29T17:47:52.305Z", "27.0.4": "2021-06-03T08:29:14.775Z", "27.0.5": "2021-06-22T11:10:58.906Z", "27.0.6": "2021-06-28T17:06:03.749Z", "27.1.0": "2021-08-27T10:00:03.164Z", "27.1.1": "2021-09-08T10:12:34.348Z", "27.2.0": "2021-09-13T08:07:06.922Z", "27.2.1": "2021-09-20T13:28:05.578Z", "27.2.2": "2021-09-25T13:35:15.093Z", "27.2.3": "2021-09-28T10:11:31.790Z", "27.2.4": "2021-09-29T14:05:03.102Z", "27.2.5": "2021-10-08T13:39:33.070Z", "27.3.0": "2021-10-17T18:34:53.394Z", "27.3.1": "2021-10-19T06:57:40.491Z", "27.4.0": "2021-11-29T13:37:49.933Z", "27.4.1": "2021-11-30T08:37:29.698Z", "27.4.2": "2021-11-30T11:54:06.921Z", "27.4.4": "2021-12-10T04:43:23.098Z", "27.4.5": "2021-12-13T19:36:54.707Z", "27.4.6": "2022-01-04T23:04:00.069Z", "27.5.0": "2022-02-05T09:59:40.985Z", "27.5.1": "2022-02-08T10:52:37.161Z", "28.0.0-alpha.0": "2022-02-10T18:17:52.117Z", "28.0.0-alpha.1": "2022-02-15T21:27:18.613Z", "28.0.0-alpha.2": "2022-02-16T18:12:37.005Z", "28.0.0-alpha.3": "2022-02-17T15:42:34.942Z", "28.0.0-alpha.4": "2022-02-22T12:14:04.908Z", "28.0.0-alpha.5": "2022-02-24T20:57:34.555Z", "28.0.0-alpha.6": "2022-03-01T08:32:35.191Z", "28.0.0-alpha.7": "2022-03-06T10:02:53.178Z", "28.0.0-alpha.8": "2022-04-05T15:00:22.137Z", "28.0.0-alpha.9": "2022-04-19T10:59:19.074Z", "28.0.0-alpha.11": "2022-04-20T13:31:00.790Z", "28.0.0": "2022-04-25T12:08:14.305Z", "28.0.1": "2022-04-26T10:02:44.008Z", "28.0.2": "2022-04-27T07:44:08.501Z", "28.1.0": "2022-05-06T10:48:58.664Z", "28.1.1": "2022-06-07T06:09:40.207Z", "28.1.3": "2022-07-13T14:12:33.898Z", "29.0.0-alpha.0": "2022-07-17T22:07:11.560Z", "29.0.0-alpha.1": "2022-08-04T08:23:33.723Z", "29.0.0-alpha.3": "2022-08-07T13:41:42.049Z", "29.0.0-alpha.4": "2022-08-08T13:05:41.002Z", "29.0.0-alpha.5": "2022-08-11T13:41:00.266Z", "29.0.0-alpha.6": "2022-08-19T13:57:57.453Z", "29.0.0": "2022-08-25T12:33:34.313Z", "29.0.1": "2022-08-26T13:34:47.484Z", "29.0.2": "2022-09-03T10:48:25.441Z", "29.0.3": "2022-09-10T14:41:50.724Z", "29.1.0": "2022-09-28T07:37:46.839Z", "29.1.2": "2022-09-30T07:22:53.268Z", "29.2.0": "2022-10-14T09:14:01.601Z", "29.2.1": "2022-10-18T16:00:17.689Z", "29.2.2": "2022-10-24T20:24:06.181Z", "29.3.0": "2022-11-07T17:55:49.999Z", "29.3.1": "2022-11-08T22:56:27.167Z", "29.4.0": "2023-01-24T10:56:03.405Z", "29.4.1": "2023-01-26T15:08:44.321Z", "29.4.2": "2023-02-07T13:45:43.397Z", "29.4.3": "2023-02-15T11:57:32.715Z", "29.5.0": "2023-03-06T13:33:40.995Z", "29.6.0": "2023-07-04T15:25:54.547Z", "29.6.1": "2023-07-06T14:18:36.086Z", "29.6.2": "2023-07-27T09:21:39.849Z", "29.6.3": "2023-08-21T12:39:45.789Z", "29.6.4": "2023-08-24T11:11:12.658Z", "29.7.0": "2023-09-12T06:43:52.567Z", "30.0.0-alpha.1": "2023-10-30T13:33:21.973Z", "30.0.0-alpha.2": "2023-11-16T09:28:39.792Z", "30.0.0-alpha.3": "2024-02-20T11:09:25.473Z", "30.0.0-alpha.4": "2024-05-12T21:43:40.009Z", "30.0.0-alpha.5": "2024-05-30T12:44:18.776Z", "30.0.0-alpha.6": "2024-08-08T07:43:14.246Z", "30.0.0-alpha.7": "2025-01-30T08:28:43.088Z", "30.0.0-beta.2": "2025-05-27T01:23:28.244Z", "30.0.0-beta.3": "2025-05-27T01:27:57.693Z", "30.0.0-beta.4": "2025-05-27T07:04:34.183Z", "30.0.0-beta.5": "2025-05-29T00:30:17.936Z", "30.0.0-beta.6": "2025-06-03T23:50:59.303Z", "30.0.0-beta.7": "2025-06-04T03:35:52.249Z", "30.0.0-beta.8": "2025-06-04T07:53:20.030Z", "30.0.0-beta.9": "2025-06-05T07:40:36.796Z", "30.0.0-rc.1": "2025-06-09T01:02:56.047Z", "30.0.0": "2025-06-10T02:16:01.740Z", "30.0.1": "2025-06-18T22:31:39.341Z", "30.0.2": "2025-06-19T10:46:28.438Z", "30.0.4": "2025-07-02T23:45:49.913Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-test-sequencer"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}