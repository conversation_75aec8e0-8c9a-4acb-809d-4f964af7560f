{"_id": "@babel/helper-globals", "_rev": "2-a2ae1a05327179f2ff015ed3ba7888a5", "name": "@babel/helper-globals", "dist-tags": {"latest": "7.28.0", "next": "8.0.0-beta.1"}, "versions": {"7.28.0": {"name": "@babel/helper-globals", "version": "7.28.0", "keywords": ["babel", "globals"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-globals@7.28.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b9430df2aa4e17bc28665eadeae8aa1d985e6674", "tarball": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "fileCount": 6, "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "signatures": [{"sig": "MEUCIQDG5xYRovkRfdPplTzTbJLVDjMCnY9aleidVOlpcU7vcgIgGBIEoK6f4qIHrRvmht7eF+MX2a8BeTbBFAAaJeFIcGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23079}, "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {"./package.json": "./package.json", "./data/browser-upper.json": "./data/browser-upper.json", "./data/builtin-lower.json": "./data/builtin-lower.json", "./data/builtin-upper.json": "./data/builtin-upper.json"}, "_npmUser": {"name": "nicolo-ribaudo", "actor": {"name": "nicolo-ribaudo", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-globals"}, "description": "A collection of JavaScript globals for Babel internal usage", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"globals": "^16.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-globals_7.28.0_1751445494725_0.058501235644816685", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-globals", "version": "8.0.0-beta.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "A collection of JavaScript globals for Babel internal usage", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-globals"}, "publishConfig": {"access": "public"}, "exports": {"./data/browser-upper.json": "./data/browser-upper.json", "./data/builtin-lower.json": "./data/builtin-lower.json", "./data/builtin-upper.json": "./data/builtin-upper.json", "./package.json": "./package.json"}, "keywords": ["babel", "globals"], "devDependencies": {"globals": "^16.1.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "type": "module", "_id": "@babel/helper-globals@8.0.0-beta.1", "dist": {"shasum": "74ef8c2d120b5751f6283095aab3b91df515b480", "integrity": "sha512-nkn95zjtMKkZnn+0xzECIHCJOV7ZwKcUwKPE3QTVwlUTmiz2jBKLH9BoHDzVJKrT1NoYqdfHAczbPyZqwXg+lA==", "tarball": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 23097, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQChnh2xdpedwjqighB7I7RkGHUN2K3ZuS5guGDyd/hXuAIgXbQU+jMR6WHUWqKm9D61npPfUvKBLHVSMXhpA3OCqZ8="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-globals_8.0.0-beta.1_1751447033683_0.7656886366859723"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-07-02T08:38:14.638Z", "modified": "2025-07-02T09:03:54.107Z", "7.28.0": "2025-07-02T08:38:14.923Z", "8.0.0-beta.1": "2025-07-02T09:03:53.852Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "keywords": ["babel", "globals"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-globals"}, "description": "A collection of JavaScript globals for Babel internal usage", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}