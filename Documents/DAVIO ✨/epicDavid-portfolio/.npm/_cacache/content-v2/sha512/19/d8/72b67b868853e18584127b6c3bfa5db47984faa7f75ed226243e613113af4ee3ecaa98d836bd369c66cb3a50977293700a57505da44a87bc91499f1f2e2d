{"_id": "@babel/plugin-transform-reserved-words", "_rev": "113-a05abfc47e3848348912badb65a00c24", "name": "@babel/plugin-transform-reserved-words", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b995a8a3805a4e4323fa9de11a02d611378a7035", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.4.tgz", "integrity": "sha512-QeYIeNzSqaHdKb81iq3FcYWLrJCtTNquO+Jt8qcKgWiV0fNjIB65bin7xh9RabPcUcHpKO4Opaz4d8lEGd+Upw==", "signatures": [{"sig": "MEQCIGiewc6/LSCBwcsV9hjbV3j4pLuCYki+2pPut8etUzFyAiAcVrE4HluQkr3ktvu5wcRekWuq9944sCu3Zh5Hj2LE3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.4.tgz_1509388489102_0.0731786077376455", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2b22c75192d373c2de2b70f3a3cf8cf8f28286b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.5.tgz", "integrity": "sha512-YQRVh66o9LRajfcfbqLDYuSivTe4IiOxwgsFBJ3VC0Odzbi2sNybmPhSTut0J8Cl1om9vEFizF7tr8ZaBQBXJg==", "signatures": [{"sig": "MEUCIQCcKG/l8sCaH5L94Yr/ebeUHkcwVOdwAzBrlMxCpx0u1wIgYjMLJTJF7gyT+RMOYjK0WXSiCE0qjeIGuWJbyGm0FLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.5.tgz_1509396988270_0.0627346916589886", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "b34a47120672cfdca957f8906d03e118124bf4b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.31.tgz", "integrity": "sha512-hTUJDts/bmjxqShZ1xC6uclZ7/t3RKR5WQ518Go/UuXsCoaow7ZFLwTSxZy6s7D/fcxd2W2ynDaDjoO2Qpf4+Q==", "signatures": [{"sig": "MEUCIQC0b4KFZeCrifQMM2vJmTiAJCmGuuF8x7QmpnGD57k6UAIgdUQem3/fnnLs4x0Do1qLxl9xG6OAFc+A41oqA5m1MHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.31.tgz_1509739411279_0.6756396344862878", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "69bf29832c6523e808cb819b488bb5758f4a8ba0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.32.tgz", "integrity": "sha512-WcWIMQ0ql5CTNRYtQ51UwNPrFPTzFdOvIduyBG0c3TVWdcETnLcv5OiKQrQoFb0ipHcDJCVZ0oV4cQy8PSyDFA==", "signatures": [{"sig": "MEUCIA9cgn32gaLm5FLsxzw+O6io+yoex8pXrXrobB2DDK2AAiEAsmvm6j5SDhNcnd5nl9xdmEgzovOH2i1mCXNZnwamrko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.32.tgz_1510493600963_0.7613652069121599", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "73d7cce07f36bd1540e925b8cb5f6be0d223472b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.33.tgz", "integrity": "sha512-Ga624e5kd7HtC2lRfD/ll96X07mAktI8w+QNzlyOhVc/AEP5BVMC/zE8rHdv96DzkFFewaKzL9DHT7SNUXJHYA==", "signatures": [{"sig": "MEUCIDP/lTY4mriBIHtGMXYzFfBjjAXOBPwGry9E0s1A/+6LAiEA//3TlE11oi8mtwSidT/WugciQl7qD3VmU5FpAfzHeV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.33.tgz_1512138504226_0.5734478270169348", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c895da13fc2558c725a25ab119d002340571421a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.34.tgz", "integrity": "sha512-mM+HfHNNmG4yXxawUmmt9uX2PsdrvD3Nvz1WM46ocxv7wRuc0s1S+oQzSYGjkPIb4q1aKgO/g3OGGEbZs2e+Ng==", "signatures": [{"sig": "MEQCICfH790W1cvuIy/GUK6IwdYO4Im3gxgTJ/fwoEqynrqYAiAxzZGHIF4sZE2hCGR2CT0PrFUZzihL9UAuyBlsdI7Jiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.34.tgz_1512225565699_0.9447913453914225", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c996ec41928b4b0edc5d917ff10097735aa45df9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.35.tgz", "integrity": "sha512-mTIf2+x2zZyF1Jsx6E90b4HXnYGdnOtFAxN0hBZpQf77+x7H32DcJiJJvblfuuB4ryy7uDaa6CXRuv8SPIm9WA==", "signatures": [{"sig": "MEUCIQDQZsIivOdh0hnH6M0OiKm8+fUJoRSQUVwouMIP32iPKQIgMt4nHOISJXCqTYvIr/spyP4fTu9vaxHDaUx2QJ0F6t4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.35.tgz_1513288071666_0.2632373943924904", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "f5c4ecfe71db39dfb21ce8f69b707adaf5c8ad10", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.36.tgz", "integrity": "sha512-3Xafa+LdbRuS5c3d1o8zmVbiZfj/0DDT0b/bJTYZ1Pm6dtGDWEqf31xgYqKgHp7sjw1O3bNXGQpGg+fVbYbqAw==", "signatures": [{"sig": "MEUCIQDhbfGDWTGbBjEl5BfiN6f71/LTDqYtpUNxabXyIQsjGgIgdN0aObsJ2Sod6a0VGWXisoiipGI+huRcY5grOTpPSbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.36.tgz_1514228683450_0.23017223551869392", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "137a32cec834abf0905c470f1cfefeab95fd6133", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.37.tgz", "integrity": "sha512-K+WgG51EnxzuiLkRZFuP3NNdWAIY30xTRRJemc4ROqk0QfZEWS2AaikV0+0pOPqLYz/At0TpDbWb0S4ErnaeBg==", "signatures": [{"sig": "MEYCIQDUJEHH9Cvt2Yi3mukxcczFaBygpsBNjI5NkrizNA6QegIhAP3P2Yzy0rIxmBED2WY14P/l2z4QkFVpR4uk5aAtYuZq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.37.tgz_1515427354697_0.11811558599583805", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "40e936932e45001cbe2ac8e71b863d47dfcc2c64", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.38.tgz", "integrity": "sha512-lyE7gYnyQskmOO5vxcKuy+QIXUQlQ6uq49n1eAJ//U7PCbGBNDaUBQTMRtWhIvYkGwDTMHgBiRTFCBXvW5Oo6Q==", "signatures": [{"sig": "MEUCIQDeQvcaKMDXfyCpfst1DKOmhUrXrUo7Binam6nlDkp9pwIgQJkc12lwGZROLYB+1kGVf8eIqH6yuyg84F6IJuhZ1RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.5.1", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.38.tgz_1516206719806_0.9210402718745172", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "930d59e9ee516a43bf2673f077dc0f6095bf9437", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.39.tgz", "integrity": "sha512-oeknbqsDZSQ9MZ6iwwTfevHHSbL6BQYR0MTK4QxgdxmczoeW0P5GpUCKY5cAASgPMIwos7sNQsa0Ad66oxLxNA==", "signatures": [{"sig": "MEUCIBaPo+dBPEqL3rZ46ntCogfKHCEaW67aL28dxpWTOBqnAiEA7xPuen6UPnmHHrlmCQ5P3OWSHeJ5SkLJZgPF5C5k5Qo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words-7.0.0-beta.39.tgz_1517344056825_0.08586831018328667", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "97bc769145e59e309894d96458029791cc5ec5f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.40.tgz", "fileCount": 2, "integrity": "sha512-JLHdeRsZzdftT+wGdHLv8q85BxrgbVvzQDqldHvMzKF4Ahg/D8nKpvFnAtZ/O1uLaJzWsBK20WgYmkgZBOYgNw==", "signatures": [{"sig": "MEYCIQCOShqqxAhXO1KlcmNQ9dAhT6/AFsR+ibIHnW3/hoJQFwIhAJqjmF054FXKx1onpZOVJhIVjaOyLiDnofiP0P/+q3XH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 923}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.40_1518453701503_0.9131744114270484", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ef1ceb55db884303d3c00066ce4649f50e8ceff5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.41.tgz", "fileCount": 2, "integrity": "sha512-/1dsH02Ba+tJAeEo0LERTW201nhLXdpp1Zs4YX1WpxCEJOS1a0eQ8uffF9w8Ap0bhmzybP22q+Z7b//M/lFK2g==", "signatures": [{"sig": "MEQCIGtOZddGKuQqB/bljxoz3LBYee/fcR6DIyzMBratnRJxAiBjeASNZHqDmWOW2uTrLi5XGjE9L6n2HjwhYyaqtTvCsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1158}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.41_1521044776731_0.7239035234165871", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ce5c46806bc8aa2e9b0e6e35c303fffbde32309f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.42.tgz", "fileCount": 2, "integrity": "sha512-OrDKZljYHIwt4K6o6MAs0NulVnGqBAj0xhLVDBL41OKe8sHwe5Ueyyq/czTiy6CvdyHaeTKBXwIgo1Mkd6Zl/A==", "signatures": [{"sig": "MEQCIGhCefUQBiO8YOqhePbf7oOXMJ09/bDOXjZOWjENcnhtAiBVhH33ZgiaNXvavZPw2Xjd4WT4Bdo/QZDdStkEGYm3Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1158}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.42_1521147049378_0.6214412110488632", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "83aaefaf890e8d1477845f42596501634dc8012c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.43.tgz", "fileCount": 2, "integrity": "sha512-wmb5Wx8JmLuyibCV2s5AVD3GkN0yj8Tw2brvX3yP7BtoYHjtFaiAs9jUnaCpuK1cYgVm28CC2JVNBGtjf2RFBw==", "signatures": [{"sig": "MEUCIHmfVX5CFGXYHHowoIwCg2svdqtmMsIth6lzXsT/zSe7AiEAn66dB2OQbOf9K1F+sqLa19Tsl1Eby0VcC8f41Uim3lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1332}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.43_1522687709412_0.7678682967014012", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e97ae0d2f92ebbc18eccd2df0269715431a1f0b0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.44.tgz", "fileCount": 2, "integrity": "sha512-msaEwuUFgB7XwJu8SQV1SnQz8CZdMHSLGoNTKuVMwpp895WIcnU0USZ/8bpl8zLhSNdbg+hYXy7XPYQrtUKlgw==", "signatures": [{"sig": "MEUCIQCKEEIUAf+30gBa04TygmdggxVEQGUcfU2mQVh//cTPGQIgUV31jmeCo8qDGMLXpfTYjqFTqrPC21YhTobD6bhYviI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1406}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.44_1522707610280_0.08873419214338107", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ee8ace84aeda18eb814de564999b9898cc128dbf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.45.tgz", "fileCount": 2, "integrity": "sha512-d8bz0ZNmahu8gxf5yXNSj0rocrE7CeC13FGjCc31ccrNwa+JyOj65NfvkHfjkFUm0eZI/hy/gw+O/meRfulQ7Q==", "signatures": [{"sig": "MEUCID+f+TUesrB8Jdgv3ZOKKLDpF7C5aQ1hRw7xCHILtEHWAiEAg/K3unObnS4MSrCM6dMShTbm7W9LWteUO8f7pNIG498=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1xCRA9TVsSAnZWagAAHTEP/iSbPY4uWhEuedx3erig\nC71yLesVGQlrWqnnuUJe0YEWNgM0+y5rRgGvLZp26DeKUKsU+GHzlud/ceC7\n7PZSsjm9yEByO+S8vZ7KPwM+jOy421T8VQ2ajDoQNTTT1yXDaCi85f9gFm+o\nWOGRwOEBpUDCTTqstggTY9olb+uraQ69ioq0hp7HSrZra6qvECbC57HmxJJF\n7bG7gjWE0iL21voDbuBgu2s9HfNxJUrWW5AfFYGancYsA0adyXIjV8UXxEAN\noSZ99kY6jv2f2NksSK4PvGDZOVWpUA5yIQVQZF1BQkjZu+r8j9nzz+N8O4T7\nDA2Lr9DJ1kz0hotlZwRWyGGyFN86Pksi/YHjabW7F3KLqAp4GIKtZDCvBmEd\n7yCXFdo4vKMAXJnYjV8wQ+timNuEJ4Gg8QWIRjtObUAZsg0D1hb/HDGEhSub\n/CkEC1aOWOwYPEbY1sFz1DUfhYWYVh3HbHNXPxEI++ExWd7VfZYW4wAGK+OV\nHB9V75389YI9PqyvMDr8nekv/1WpleYMSiMo5UkcOYZhWCNDfgC8yMKdHAgN\n2YiRndGodDOLW4ifbeMLBTryIyXz5ySGWX9HxiQkhZUS5ob3NiI5w0G52rku\nOa2yGQuxFkHqi8NzElQSAXETJL7gC/gHge7+92vAIp1g3EW/faKGroZYMLTm\nhhWG\r\n=yd/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.45_1524448625202_0.2533396440117084", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2fdd1657bb8477347421ec5e528885f7c1693b17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.46.tgz", "fileCount": 2, "integrity": "sha512-ZzjC8WSmvWN05L87INl7hUYhr+HBbZzr26yo0p2RKiGbp8w42CENW8TS5ssjf7ArGuSurQWigffu0KOIFfQMRw==", "signatures": [{"sig": "MEUCIQCAZ2nRaDEJ4BhoFjbM2rjVrwcPHACYg95j/ZzEukXSCAIgAy6xu7zhFcVsEv2/zU2RfWB3NNtm8HN15yHAqnzzFFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WGcCRA9TVsSAnZWagAA26UP/2uyOu3KSgQmM5trG5aY\nddRXTTirUk+Aqe3JR5MBTRQuICTY175/oPUna9p07ea1boP1XHoxN1ccVOvk\nmZ2kzLWt8QLUX8TiiO49bIKm/nXlsL4YBT5YaTTtNZU6pG1N/+5tQCQP2T7Q\nFlHcfqb6hy0bRt/mVa7UlehM/9cdlo2fVZdcCGqOfABFLqjUAZPrmvsbd5zg\nkrAlxuBsDolXlJREiEnjHd5eFtc1u5+4gLhQRm8hSsAnpS8QcLrYswQNUKqq\nzeUR8asUckAY0W/mRLlyw/eO82xt7uW1kEOqPkFC51cyjKjSquOMgttJvoJX\nZbu9X7PTh7e1Mm9/bLcwhb1BJnVzMQ2ckuYzSB3edcWc3s65FyWJykQnl0mg\ncj46rKDBHszz76b3A2kysjtH3D8N/7hlYk+7lfKidCpJjJYqMfe5NpOV+lKX\niY5chaQ+eAkWvgUwAQ/mURUdBmROQ77u6d0OCdS6PzPBDTnN47n1lXU63lab\nzsOJk8X/HIHUY0tta5/qkWHCnBF4Ji6QwwEN5LIdDmS7yNSmp+o1dPDHm0Vn\nS1cwAabAUw10T2KvccopGz8rQGtgvp4c7AXCyiZOfekORCipRaIxCs6RrO3Y\n1zKvQgEgljut4diVe9wtQ34CRnO9Jg3CbbLvdU9gWSo/Y9CbpMB+PPhLS4vU\nE4+8\r\n=Fzrx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.46_1524457884300_0.8989272237994126", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c18c68ff65ef638c62019150384d1e64ea6cbc83", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-LOdpV+Q0yo5F6B53MGlypcZUSuA4RegLVBhiw+3PS2Z7KDZtcStHxyd8V7O6n4m5i9VJIOfkENUKaAOdpLoDRQ==", "signatures": [{"sig": "MEYCIQDFfmtQqz6a6yskjF4A+afiFuEbFiuupotT+FLxIujo2wIhAKjkDcSJ2VRgdm5UeE0xNQ7xLq0tUVYV36yF3+crF3NC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUqCRA9TVsSAnZWagAAhkwP/i+xYtYz4Cgc8EVJdeh3\ng+oFnJ2bOWvVoe35haCV0LrSxrfASm22/r8OWWA6DpfsU3PhbiBzhQsRKS4r\n4ZsstXiu3NxXjsFibud7H2e+Gxv67cOaCsvmrtfsrP7iGm5rwdOxnw9HNRq1\n3QDJ3sfGHzhtjZSylTPJDU5xgGLaXHFWdVGIuivkr9AbwrMXMw/JqhS2cnOr\ngQSgwcTuoHXOnEyV5a/xHI5CANH0V8NDxO0fXdq9fKAzo8q2WwcIM1OjI4OX\nnfqKbAB02v1U6nDXs3vJTZhtOZucHPcv44Z9bYII0HVfUOy8e5Iyfzm5SU3s\nr0X3Y3fS5+uEtTPz0ISomJAeBVpbb3ViYKnJS+7YAT+Wg+43R5NV7xWxIgoW\nok3awx4iX1L7cRPsus3KDlisHas4zUW0pa9hDxwvbuLCKcQ87E9p8Yp0za6M\nDfPMZb6Z1f6WLjFSo1OrrncPnAW/UjstnXZ6OgqqjtnTIISphGMPuYwXy//+\npAK3rJqIHxrordPwNEyL8PGJCRg7as3ty79hOGMwKB/CP6csH+FY9n1t38B6\nxUglOHjFREWmDtQywu2n4kQaj95Z0CQrLc866BClTxPs6mMbmdzDmY8x+m9e\n8dTY6zkV54pE5Wc+Q0rw5Mf9cGICfAYAyvA+okS7Bb+ZXJLSf70UCv9ayiAJ\nz5g8\r\n=zoNC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.47_1526342952715_0.6528207856735371", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "039d5a5df3d117c3bc5841284<PERSON><PERSON>ce11d0ad7b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-lkkEz4otTJEiEfXzkLB4Av/0Fb3w9qnF9aW2NCRuSyG76J1j9cecZs5zKVWlVcbAfP5H6SQkgJLHy4+cBT2KvQ==", "signatures": [{"sig": "MEUCIQCXvauHK/EJ4IfCAHQWowlimA2FEXpUUmVwZOTkItTpfwIgOF+Px4fs404azGsOANe8a4laPYYswwcYqhn6PYozXA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxEICRA9TVsSAnZWagAAoUsP/jqi3n7fGhPvKolS1XQA\n2J4AtUh70EmBJQn2nWNJZpZLD/zzm/XvvwBYYybZPjB6V7wiYAzf9wveYamZ\n37n7xuxL0A+YXEsBOnxeJTUTybiy69heQ7DNgiRJBv81diY/DDR4Pi74FQ6d\nc+5+yAeX1m5KSM9NIOrkYxF8H1kIbqlASwtCjI1U/KcXZhCavRjK3GVSjpqe\ndfOV0ohspeKETtGtaSpyp5TwBNwrgcrC0LwofuPI/GeNbkhmjv5iCBbOnJ/j\npH+YfPxolEruMLS8tQo3LYDYEST4IP87rRn4vClDXQ9N868uk2ijKD2sIQ1c\nzuGEO0clNqdgoB3eCXvOyF3TTfTJUfsW9wf9XIanXeGudY5rtQyfp6xz+bHc\nuOm68OLyJoVrMOYs+nWG6emDOC203UssFg7gud/kU7UtLml5i3rWjFvWCy50\n62GEKHOerBcexLxwmsTBMmPbxsrI9f1Bsj/3XlSsgIkg8byvJ6N4mQJfTx5P\n19irBQchXK9gzoM4Wr2nGlULCcI6xjO2+W+aRVHfNJule+gFVwlU3TsoC40N\nw5tzW9xCtD+119U3Z6Y9l0FNknH448T6TPobjf0mWoZ/hHAvy5AFIUxvyGEc\nsID4pWY1o/MyMKC8lHo8M+adRjBP/RsIZWSqmH/i6jkRdHnIecHapLWFHQUH\nuRQS\r\n=E69Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "5.6.0", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.48_1527189768036_0.8159589256156161", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c2360dead25bde816fce2ebf44034c7dcf9aa392", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-SHP8ZJnrURWVOxqe0ocaNlxbasjsdY8B6FCH77K9jIbB9H0cuZ0wxF9Q/OyfeiN4eiuv1W9rkXIyOqbbFt32aA==", "signatures": [{"sig": "MEUCIBUleHuOFTCMQi5r2JoClsMxZc1yvbBJkoIMqYUaPbAuAiEA6FG3EXuhkhgeMWvIj3sWCgqhY9KKUtVSOXczee+HJTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDOTCRA9TVsSAnZWagAAJwcP/3isKPwpWYzmXmtE8SrY\nlMwGZTR/VrOmWNA2FwSRwJ/JVGhaySznwK2oGAmGAonxUaXXRQLQJ7TmvOfB\nXgEPkyEGM1Ahj9NnL++VwM1V4UbjJSrK8vU8ETQMz60pYZYvob/PefXwxUR4\nuCdo4VcwdwKfy9qSue/Jxd0GOKPgrWGAao8O1itdEF6ARHk9uCUsPT7BWQDc\n+KTCTEyP/SkhMLTov6xrHxPm79Dpnypg38M6EaDN9zWheNn9aFj1CliKl2jC\ne4qpwMkNp03tAQHVdxItBtEALS1IQC66wNextdd9A5d1y5xYWYIDNtL5eaHi\nmqIZuv47kVdf44vYiie/K2RzvFi1px61tMIsmsF+a3DCWS0S5WdEksQGOHOL\nnenjGhEqxCA2f9/e8Nfpd+rxkEllyGUVdbhZGZozeC7qH01l0+eBOpQtfOvS\nZ8U89R8nhulhicDuUelIVTiZOIlklF/12ESTF1Ui6IB2cY/AfT3DHsZbrcQq\nf72fLUyzPbBVg8zh8TTKLpgEhIlR9dfKwe3ErWSCNS+8EdBC2ytwN+Fa4gNn\nucO3iLKugHVzliYPOIxvQgcLT0UJxa/HsYH3TcQN7CWzn2bX3qR5vKrAfLFo\nQNR1ZH4Utr+CNpz+Q8CM/Hg8yXrDxO4yVR2mKbf7GeHvxJ3HHQRilR9EfqF5\nnY8M\r\n=7q8t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "c2360dead25bde816fce2ebf44034c7dcf9aa392", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "3.10.10", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.49_1527264146705_0.20572362807016198", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3d0f3b17038971dfd5470a78f6d2fdfd471ff7ac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-DSvs2p4D7xl3H0l9T9AodKVfnrblGw94cNnBO3nysfytzL6RGV+mCeuaSIPs16ODQDjTEkazRPp0teKeYe62jg==", "signatures": [{"sig": "MEQCIE1odDcQTb0iu6nFSC5BSAuyMzIQiEsA002Mv2lNkOPAAiBOPzSIAqnCcZBI+RNSwfarLjCIq66M6Bx2E0i/eooCxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1751}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.50_1528832841475_0.16443377900085254", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "919c54ee9627751a12ed294728fb0ed0add8a527", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-jj6k+u6iftyW+U8TDVB0uPIuMjPYWNRXW5R1MFhc9m9xUXh17mTZS/XnCTJ2Vjj4g+cdFbkztnX4h5IDqfjYpw==", "signatures": [{"sig": "MEYCIQDJf6opIh6uzN1zWJ6gcf42sEu2Y/tRNISjh7QjhNx08AIhAIDRww7gX/NqZUiZ0l4OxcapaizkCcLMr1doMHFurZHj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1765}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.51_1528838396797_0.14641365175512644", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "941a645ca6007c3473e69f7d619b1d0a5ee0e15b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-Uw8IXsZejRhKJpG+KnAvSReKWoN3wtv/HVo4kz4yfKQFeCQagI1j6l2z2W41gees8LokZjOUg7wxTK5tUQWm0g==", "signatures": [{"sig": "MEUCIEX03C88EeYiT/23jCNJOJZalZjlTnRUdSGezoZ3fYeBAiEAtYTE/89AuJvj3foMIJ2KffO03kvCbdVSY/u/E/aZOMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.52_1530838768215_0.5350386293813649", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "93d21592e8c88ad37e0b58c6e95b29bc26931dc2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-B04K0Q8RT1XvK7F8wT6PMv4MAgSQuxDM6SP9GQJbQNbywqwow4aEKtsd0Svyt6mkABcVh81D4H2UHPWb9qbwtw==", "signatures": [{"sig": "MEUCIDRGabq2Awl9JsXW772rg5W/F3O5UBCrPd4t+Q5ixtUnAiEAqP0X1rgPIgM2u8KN98ZC+7yFl5Bchdy5Rd06Owqol+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.53_1531316418212_0.6047300541955054", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "36ac8fdfc7fa3e824f774a8770b2baf09f126f78", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-7frncBbtddtWaz9B142WpWSXnkAcjmlmLqA4mPOw+hZPXejxXNxDhBL8o9Pvg54qy3n5931PGTgUZ8rl1lSHiw==", "signatures": [{"sig": "MEUCIANVIGoQ0URSzLvT8FKn/88ntQ4iuvmMjutEIGvV0STvAiEAq68QqDnNlqeQVMzVYl5/suxfzrjOpm8n1OlTGqylyxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.54_1531764009292_0.04565477512701843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ccac90e9ce41d9de5cd4a630e9922973a8928175", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-W6pPWrdpRYwnThOD6gZetQRVkyckJZozeUbBo92CPpwkazvgIumWErGPDMUp7JDguGCumG50XkwftyjPAEo10w==", "signatures": [{"sig": "MEYCIQDQelAZrxez2ADwBoNjzZMXWFegCWWrVjAch/NL1soaYwIhAKCdVAmMzH0h71MUIxOe3f7lTeL0mHNAs7oBpm0kCB0z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.55_1532815642315_0.2848725885134167", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "306184cb5a118e87978415e0b44f1127bd423454", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-uIYV4v4juFT0YVsEp1QAGdu+4RDRB6+ZfekvVTBO+9F+6W7uBPrwK0e2F5n9eELqvFeniXUlPEzDinKC8QSe7w==", "signatures": [{"sig": "MEQCIEwgsEIRu+fTVfdszTXvF42RU4qQP6vrdxvvaA7jiyIBAiAINn8F2QSae77rCyhZuwlR+tFcmfoeYwjQ7bAy0jXg0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPv+CRA9TVsSAnZWagAAsBkP+wVnERiZNCVbZaGSHqbT\nUm+T1rNbFleo8RIQWg/I49fgE9pwVFiRWRlDw7UdRbNIQgFNBIXlA/7Dyglp\ndS41aNaAZSKX15ZHH3Uu2MIg8nak8Gr3myasGypfjJXFo5vASZbtP2VGAL4p\nAhmvCR43nXDjNh+ee/0CyTwAfN4nimOV+vh9V6MRIIw/Gspwisq8UlbWv941\nSiMQy+QYpTXzsVcOihGlOFrXGDApR3T74FiaQz0PMtswuVWMsggXOILhYYOv\ncEjVH9kHgBjNbmJndyBIB8UeiGZlw96s5VXjZe36Z9fjfTG8k6wcRzLpQbGb\nO7DodHChScYxOaPPQQbM1uUi0vWhKLSbU60BQCnZsia9hasds0bq8wlBlnpb\npepST4TdLmaM6uNmN9/tkdNIp7dAp4hYKKRY2mtA/ZOCqDZJbeMhDQvi38n0\nTVVK/G62HKh8Mr+BruqgHc1C+2tXBlswfbFy94erdz9Glh2hOpGWPYvlMFoR\nBmQ1kv5+mhfTlByT3QhLGZIQFJNe+mB7p4PMzWwnmYow00qCJmUP1d6WvUPL\nYG1GGYdoA7bFX69dFA9k85BE0p0p5XQBL2RHaJVIpS6iBUWS85xV7rH+emUX\nt7sLQNBLavvLiKWMX754cO5vKdztgvDEjsx1eyvyFrsSX2Pcj6Q5OJziiH14\nqcgM\r\n=EDiP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-beta.56_1533344766181_0.8443232148248128", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7934e626592bc066d811c4ad5b8c8b08f793f9d1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-swM2RAcvrSuhCSubfRbdcndhr0bPk3mcbCEMbMZLozpUuiVQkE7vZyFXH+s/P2PZCqcCL+sJHtxlHBdff4lzbg==", "signatures": [{"sig": "MEYCIQCLu6e4jJB0lIjPt5hHKyWG2HAY83XzFuiJ0sn6VnifQwIhAMh8tfpzVyzGV7qu2fN1i2IskBSAEl89hNErM40fvFtg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSpCRA9TVsSAnZWagAAD9gQAJg2jPSa43mHHW6I5rp9\nf2fplm57ipW7loeh4saZ+0oh0txlCFftLWPAIEdCDe/8apfRVBrxw2nXkvSt\necxg0qrpOpG26B4gQskVYsekGpGV+9cqTtp5hkiaEMO7eFGNHKW/aZr0A8pk\n+5Nl7ueZrduer/KOAd63ankB5CVRUe6TZYuydAVv0yqqsPU+2CBSBVRp66Z3\nCmSMZxTP15MUtriSYMtq678uWZl0pOV/MZ54mCXbsiQJdZ+Q+vtpckNNCkLx\nMt5DaiGGJZHLUbTu+tVqlzNoDB/8v3h4LKdba4aJl+/57bSkV1dOMA8N0TZb\n3uUMZIkEDpv2NZV44OVGZtLQo5upzfaoxTToEB7kRe6y5Of8+anM3RH5YQvm\nUXKRNLD4VFMJF/rgD//GAXim9c5Y/2J7YKOz8N5aXoqp9rMslloBqiKy7Luq\nG9GDBl7T+XzesgdZUE+D8/yYs9+XEJwyJKq5i3AlREFT7dqlwPatrF2PYJWJ\n6wIEhAifEIPWUhbSefendC92Nsl8cx8brdMupcO5Y0rv0FVp13GSpMuxXm+p\nIUtpCpZTJTWKfaTih7LoAk5cZE01f1+uKWIelflGkYFnTN0BmOWZyk+4IH0t\nnvEA91Wtj83WArwjoSjm9nTc5TilG4tmJ9HZ1oO/3i+au1uwBHxdmO6rWDyn\nm2Y8\r\n=nKSW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-rc.0_1533830312602_0.18471057491747112", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2fa0c800d6420a4a8376f0df0419b27ed217a246", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-Yx2kxn8LSToTb5zVqsWB1W8cdhIh9dPMZgd6bs6PoVuNfnV2Cuba/nid78E9c/Ui5Cl/1GWwolVEBeh7VL+a9w==", "signatures": [{"sig": "MEYCIQDZKRGLUgdr/TuEb+ksOg7OxjjNySgc3T24zNpdM9bD8AIhAPzufASrE1JAYGz9uhZEfK6kQ2mnYzIG24rVFftYjkee", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8qCRA9TVsSAnZWagAAER4P+wZ0wo/08FzCtUPS3BJQ\nb8T2Lioy8jCuhxtZ3eDs20Ug6Q2WYZlctIAybWo3IIDnssIq2uvYX5//5b2k\n4ORmxai8E7l+YBd0VpuBq4oyJk+NLXfwo5GfLEmHJdx8jHjIyIvMipQaGQqv\nTydsGKN1gcNI/0E93h0CzI56h7G2iuTs9GJcwSM3Cc6GvaS2diWLMtk3DR7x\n2ugzeoIydvN3lhcgQLslfox3lAK4YZL7r3epfKl5b9xq2tRkhAZzYYGPGhMo\nPZbTPuLS7E+ac2ccsz861+Yw+DRCEKPR6Kmgoz1F/5Wi9bFQQraXvZVgZmbU\nSTEfh+be+bhzCGXIj/RVKai+CRLFNtSS12rJ9+NsK2i1mqOtc3qV893j/vGb\nQgGW/HBFyVkxq0FYXcPY92uxSbdEC45MATv2BkmT6qoU979gA0YBi3uXYALq\nAV8b382Ca8QTUAYKQBLBUMXA1dClUcHXJEEEg52rf8j5txPAKU9+yiDVKiMJ\n15Wzg4AVNfzh4uco3KHSqBX8gE3kMkhqc7NVN18l6UXHO4LN5c0Akg7wgTv/\nVSI0RcxcZwMjoJOlTAMC0JZ3IPQfbZjtls17GO2e94id5Rt7DQnlk77DzbiL\najf9GdmnBsvLx1cq+F331PM87YWdeqjnSgh680YWbXK0s+UI8ZF7W9EkbHWv\nEhUj\r\n=zIBi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-rc.1_1533845290322_0.8716428765366795", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d1df87fffeea107139e3b27dce5a19f2b7888a5b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-B7DKLLafE74TDIH/byVO7yOV0rNOdqsJJBOR4v5Kxrm0nojVEAs6JV0DJDfIEE/kfsmryuYrZ8CCk1kTpUdtTQ==", "signatures": [{"sig": "MEUCID/gqMbqHZpS4Qpo1tEAY397NdLgRkQw1ZlBv2Ks3dssAiEA3E0OXDfy8cwW1D/BnZbDW1xGAmXqe+f+5c5w+lCEsCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGbfCRA9TVsSAnZWagAA288P/RsgHyNkQHtoFuLvU2sF\nsdLBCJoSMFJVmUoA/ORfY1OfUA17QlZjZqI7I1yAAq/1nXmLQXEnqADFUAZb\njydbb3tGibljrf8tKwrwTCL+7204JDSL8g6QWmXln1p3AoPm0OqBXCYOkHMb\nQGWGU2JIDFCc3hIUJV8Ciip6JAOD4mEnnmpWHN5/pMxYR/3EY641B4NRIWEl\nJjTLOtZkvqb6eJcSqT+WOa+5rCkATQ6MzUfv0N3R/Dvef/5AdQpebeVyk6bk\nEPhmKY8Zs2kHUvKKP+vUyWkh6BUVYfRG3tTdcUvi+l58J22hkZlv97xQWkK/\njYR25mxtAtXBNzftJQYChlXYll01sqIzGeXyv2byN9UmQxWWpyL5vW4apsEx\noAX9JBKe5oidDN/MU2i1WqAxs8jB/iDuMkB5EO7CfoZnZtT5aC4M/0a6BSmk\n9rfkE0zal5e9Wb0haQ1mMGlMhNO9MHulfXf1RQnHj6E1LgyHFxywraE/oqv3\no1+RY0OXwtswdbFp40es0K/G7PkTHDP1XNZ0DqbEp8S8bJurAaq7fzd4WFbQ\nU5dMYCJSkuWFwZMpnMgSok98+9q+QheoTmo0YjpwwVm3RDnSmDa1TsscINn6\noiW6QfOwKGihLkA/ljSqlifbLop6Yff2sEE6LY4HOMpu0KImzg7lAqdtSpZe\ncJHW\r\n=+3ti\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-rc.2_1534879454775_0.9681242423436802", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e804ed6c9d581e3168fda59ac9f6e8e1daf595ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-782Qo7B4H1ZvhCYMWzwpI2u1HMejoXf7tPRFRr+nAWscDcBYfwPKm/cOUzE3h873yUcnn04pCCI5RngCWL+Dhw==", "signatures": [{"sig": "MEYCIQCqgt8+PFlLMYt6+mTj4vGIX+kJBmUot80vDObhyYEh5AIhAP9acNUmq7h1rZhQE8H2BhKjlthtL0eDUkmUc4SjIJbK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmLCRA9TVsSAnZWagAAJs4P/0qJ+SOksWUOmdVYYHOX\nE0guLTM/Zq6sQxJXwY6U9YhLhW0WTuX6cMaV0fqE+bYKODVIhVmd0TdTTH67\n6YlrlT82H1rpHQMaom1pi+LxWxwNUodTae2gCzi4WvCMQTZ7+V2bRDfmEInM\nAKeptMUn6zGWG9QvBvKYlU1JiWZNGscmDlNkOT3t0qzT3Tfff7oVzVRS66mR\nszKX+FvOaIgCd1wsv4laS7tsb/ui8dcQ1lit2Xq8M6rPAMFivqh3rebS5w4O\npNIT0smAbfjOElmP07kdBo/sBP1w10KkFTyTgTShVTi++dalHTHPYzZv1Wsu\nWfdRy3MZRSlzjX3CWUBt3dis1t4b/lnQkf1o7uTOKoKD6wzWvUV0Imo9Luxx\nfC4zeZF5kOJd/7ay/talzJ5weA57x0ddvbNYLD2peetCjpNTsQ1xnam36OGC\nRWGDZR3YrjeXkX8ADPF4tjFgMPS3v61TF3c3YAIzwlrmx2mKhnTr76pfEz5H\neboF5P71w+Hpd4Cgp2VwuZTd3T3KpQZjg2fxUQcnAzmq8RBzUdQoxTKkY9SR\nSb7sdXEn07A2w7qIGt2FLRdZ2lpzRnc9BV4ruvHCC9iyhdHD8njHCxRY8Zd6\nxqILBasrkRhYkOBb9VogzRo28lG2L296bzahh4COUnC8R+MXaINPgyiXCcOv\ngnhV\r\n=HLl6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-rc.3_1535134091333_0.713798601987268", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "add65cd322a1d2adcddcc7764f33b7ab8e520ed2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-olvHf33pCzhtvCDEqv3nlVWqs20btMES437erUdyQx6flF7A5D9yr6XWMHOqB24yLrALhJ+iY1d380kBMXWeXg==", "signatures": [{"sig": "MEUCICThHqkPSvU2wJVOZ2X0IHHBQWF6gaMcp1CQiNwH2j6CAiEAycK5vIos6ChP0ClwapPTKQIXOi6mQjeyyIFxRDlPJg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2835, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCprCRA9TVsSAnZWagAAjekP+wYt/nzCt+Lmocwiz6LB\nIiXJo0H/o/Ij2fYtHHhF6dJjR36qz9z7mlvYU751JZ0eBi1FAeoPi05nkL0E\nsCKwQE0IEd4eSvrmxWi9ewPanRSnr1oAuJSImcoT26fZfRm1X3wivjF0jNFz\nhYeeNFLgFJbvEo8oxlQNIP0Q6NXzTuxTTTSzXBgZUPlXM8AGvGImd4xU7aKj\n18QegZ65SHwiGYIB4WQJy2HVBguS/irtG16OH7zJieSdQ2jUnTCsde2pugFC\nYekHgJtpokKQafybV8nsBmoIYcNeRv4LgZTLsA27udBohwT92rL+C47vuL1z\nAnN6zEE3+89Kmacs1tx2M3epdI3LsvwkOj58TDdSPb5TofzqHgoKccC2qThF\nIaKmXsUYylfiswYugtGxOk9fmAf3odXkYErpPtp4+nQ8wgC4lfWXFJICIl5T\nsjASzOOA33+Qrvlp1cfLdwY5y5k57lS9CMt4aZGuVj8Rw9QpyMHuINhGN43u\n1iQEpZAa3QXQ/JAZ4HAWXa1ZQkBXWfDJ8aKFd+2V6z8DcD2v8WFVFO3zkoc4\nMUy8MOF9cV4/pyFq0gUTnJoPVy60aqkAV3eplJqju95TEJ+7Q4ddjxM/4zE3\nKKBgqMEvRW7vaWWv4c1mjsXQNae0BoU2KDChTGk5lYb1LdYGeWnNQeNiw1EF\nseXp\r\n=iNjJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0-rc.4_1535388266771_0.5960187847534812", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-reserved-words", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b55a2930372f8075f9285a764ecf2bfa0ebdd13b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-XqAx1rBH/JYQPMC+BiY2uGIBoV1HjDn0RtgWWQkcEzRGZ4+Ag8Bgli4CmqB62n2z5nQ4+UX38ui32sCcqdvthA==", "signatures": [{"sig": "MEQCIFrVst/HVM4CUlTxFRVWwHCVKMGLOaxISIUz47FoDChXAiAO6rxHkwgepmqwlGpp0Pa7lgUu1HAT1fVM7WunSlnshg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHB6CRA9TVsSAnZWagAARoMP/3Ng85Ec9lcK4vNfrD7f\neRk99fcTzHKROHciVJe4R0wWCkocod5teyjRFDdf8rjq3B/PDT56jMT3UyrH\n4VkXtVJ5FekmNu0S7Yxb8y56ndjtu/FX5vydjbaue5EVXIv18CbWwPHyQNOR\nHIE9msJwLe7XjIolWuVV8fv82MDEi2hJxHJLBH4tlyNcGxUAuM7bmQYMTVvs\nl6NBw3DcoD9SaFXoVnQ/ymtyvW2st6FEcea80DjYxSMLh8af/J8y3YwAN65I\nsi29mbir7p5Pr2/BTQ1NNOwWcDzadE14OnUkVowvQ2RzlV21Fej4InLQR62Q\nTLxwOGPQSPcO1Yp7ZZug0TpwIdrD120SAdgQ283MHyqcvVvLjm+E3ad+aC2V\nubSMGXAWIVVxNpXlM3VbyhFvB6GT+kmiNZjXiKNHxZEy6qFVJVPM14NVkhg6\n8dusBvvCGgMx8/WiHkAR9BdwzDTKr71lp/eTz+1HHCGKFMDv9qhhwgM23K7a\n6Jw4kw9DDkQtBtSjXFSJfxTLNuePW+iKjEB5KuVT7wbXwhv02XuAY+8wN9wU\n61jvv98aGWNZ334z6KBgBC6F/qT7af6LTV0N4/FFhQljSrgVmSq7bS+t4SSD\nrCvn3lwc+jFxE5yUrAFwY6D6usDpBQ/EDG8Oh45JquDE6jK2TjfuKhBn0Q0q\niA8g\r\n=Fjnt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.0.0_1535406201921_0.8385123624377784", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-reserved-words", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "4792af87c998a49367597d07fedf02636d2e1634", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-fz43fqW8E1tAB3DKF19/vxbpib1fuyCwSPE418ge5ZxILnBhWyhtPgz8eh1RCGGJlwvksHkyxMxh0eenFi+kFw==", "signatures": [{"sig": "MEUCIHmhegjh8xLisuxWfFRBYLrnVImRaz1NSdGk4fRHBNY5AiEA56pPutELHF6ulSzloM5+76SXYfZP0NwEepA0+Pezi8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2bCRA9TVsSAnZWagAAJ4AP/i/d4LRfkiH+BLUkBp/Z\naonkI0ZuKikV86s2zwmDWegMu6mbRkIb6ypSK59hceIIH1iqZCfoiQav+D3A\nofy/iO1pyZ8faSra5PUpGGhbtH3dTEsneWYe/W2WsQ51VP+dgdMiycTsF8VG\nwS3hWz1jBb1Aok7wAxdTuHA48qTfd55ct4YCnp/VbuKY6eOsKouw+/Zdp9UN\nMnKoHyeARwbPWcfVAHRRTIUQ1+BWTniJsBG1GWY5HG0rl8QKZbf/82tdjLSj\nI37rRZk1o71o8N+35q5iB2SKyb135RZ9CQV7VWhrOZQJDIQAk+E5vsYqvKeT\niyaXYmHN9kRFT+y9H0ZvImnZx87DdNyiNBmZEvRZT9s0TTtAb4n5mZ8PvHb5\ny3GDAPlG6fAilpT177udDTWA0hPypg8KDzWQt9vGnpCevj9H2m9aKXi1oIal\nEXhoWmsZ5ozxiNqgRngeggfN2SSDgwo6vI/aFZ2C0RUDE2f3cXWxGqw0cW/L\nj90gobC1xingY06/vIgGcEsCKKspEpAAN3iTkkRFYnOHcWAsKpd3fusIIiKy\nEQfyaPSZs7sKP8ss6N/LzhDML9ZPpg6vxrUvx22SPdkjfrbgPpB7YTSVXB3X\nHYyA8I6KNUs/F1Ai+DWiiocMAl63uoHcNeX6/JRZgAC9BVF5lnVO0X1uJack\nsyK2\r\n=Wc+Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "description": "Ensure that no reserved words are used.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.2.0_1543863707475_0.6239575003109099", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-reserved-words", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "6a7cf123ad175bb5c69aec8f6f0770387ed3f1eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-OrPiUB5s5XvkCO1lS7D8ZtHcswIC57j62acAnJZKqGGnHP+TIc/ljQSrgdX/QyOTdEK5COAhuc820Hi1q2UgLQ==", "signatures": [{"sig": "MEUCIQD7N5pgox62FtDedJEcznygrTzmA1Gxa4V1r9vt1APNdQIgF+0O92XCt0Uq8Iq06DHaG/jCxVVRJ+LeECXboYonyDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAGCRA9TVsSAnZWagAARJMP/jJNnNorzlYK1bA97/jQ\n1mhp3Q4qPLM05TBoT6aPhTTL6RSeBTGVm+YCyG3VzRkdiudmaATgiyNTGfk3\nXhOpO9ut9ovIW5lUXnYerurJD/FM7kel6X+SMaUbS4xicrDwmhyzwo0kF6hN\na6tN8cCa5X1nyvJRos48i58BgepTu8U0V9kwAy7/IxjaNeauFvX42zZ35ICh\nltCdR8fQ8ci0qEQfVMSKn+Zu9WvXv1E38cjPtU9yitcOcGtZi6p2vgSXd0RC\nxBYWncBXBpT5yk60rY/OWcCKgZRRy74DX1qHMRHE8mGYgje29jQX4ivshPQs\nAeTRJgLqb8ON3Vzba3ezE11Oi75+Ny919hyfQTlxy2fW3SvbmuvDoY5gYuuG\nRntt5kcn3cAQ/Mrb4LfngS3eNL0xufhESfXJ0qin++p91YrRldcMhZs2wKGi\nVxGekLYquxrSGD5omXl9AEZ0G+kH7fQ4MjTl87Md1iwGSJFLvSuFL3/7X3TA\nxpjlLN62mzja3KIBHFF2M8O1tDKc2ekaNJakNmqv58rXkyqsCicdYwldOHcr\n2YkMDIp+BX3UDnAKOPVhTioweI+IN47a/aFXwWI+vAZMLW7aMyMLADGUhY4L\nLAdd+8bgrESaLf85RHBrHSe0TOAKhb22oMp8yQvv6PBWc3ry4R8yZJfcv6Jp\nvsP4\r\n=8zi+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.7.4_1574465541683_0.4796050818635942", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-reserved-words", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "572f21e11b9271e67cc5695890b8d5e58186f51e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-DnshRyDTXZhmAgO2c1QKZI4CfZjoP2t3fSwRsnbCP9P/FSBpf9I7ovnAELswklw5OeY+/D/JIiaGLoUt2II3LA==", "signatures": [{"sig": "MEUCIEDRM0tdbQ8PA8oRIZSH+g/jhFDVpS+OdDLX4vvEi5hTAiEA7HNG7LD8yVMN0zL2PcBO3QqvRIu4X9967922SWCN/b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2797, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVmCRA9TVsSAnZWagAAmkUP/RubxBEcKjT1dMpCxREo\n1MG6KTe45Em6xxND/XYtQPRGAgOXkIgkwnEdLxYEIqfdA8E/pok6HrREmUoR\nyXkBHb09gn/YwcqFzPU4D3fE31s79FOWhMk8t4z1WqSI/YppGvmpRQYmntHh\nJIJtupOwmNlK0plu7NIRX71s1qK5BunZ/18pQKBkpKF4/zwSUW+H1JlxJd1u\ng/C3Sit+H8BAIr+MzfCDiSIn5aUWibJLe09wrGxVP/kz96n6Z+8MvQosQ3yw\nFJnI8ml4E9nIUIjSlbl0ZyyxIzhrM2SDLe1FV2PffpGrEMFBrMwjuBHSilfw\n9GhZ170mevtcZAVtkbVSZZ+nV9ZQOxQLKjx2uVF9zFv6Ycm/0LADHraG6OH1\nc0BDjukwwfjvoE2RNR1DCLB+0pBM8feCo/H2T3LdWqL8kvt4WDm6OT7eacFp\nmC03Yw9Lp2rWxqEivtnD0PLylzFggaPXfRmIblPmhpQACB3uhkmphb3EEZNL\n75pZtyG5nASRz/pLcdbyVzoJg5r/811EgKmoZsGh5XtD3RwOk/e46ZdQAHou\nvYgyXGqKn2luXG9Ox1i+uZyuZyDvFeR5C1/H4uY1FcUvIvPtUhU4xh+2pFvJ\nOEQnWwBWJHV08SaXitAx83Nt8335VeDcfmXV3zfo7tzb0uswYOkj3iSMJ5lA\nBxq5\r\n=Wqre\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.8.0_1578788197708_0.06542628787250315", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-reserved-words", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9a0635ac4e665d29b162837dd3cc50745dfdf1f5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-mwMxcycN3omKFDjDQUl+8zyMsBfjRFr0Zn/64I41pmjv4NJuqcYlEtezwYtw9TFd9WR1vN5kiM+O0gMZzO6L0A==", "signatures": [{"sig": "MEUCIQD6cTlfo8AhLZsuD6VveBsEuKpEuy8NpsI7O7O7Aks0iQIgEc0NbxpL+jxB2s0jo648D9rrZWwVQgDWAs50sYwVo98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQTCRA9TVsSAnZWagAAAgoP/3yor9P77V7dCLg4k5kB\ndOUHWdDsG56dcvYfmMr6EPV+sj3H78FPXdjprLa7QKNPCyPt1ZQDExUCPxQx\nTaTift3kVd2Er/P3QJ9aO4wXLwt+emt7ix1gGuq+jdgncok1pDM8+rMuq3Bc\nNyLRZUQFXo9HNcQ/ZaasZbEtjYIkCgPRA2f1GhS5XQzGyl+Hpy5zrZLKgU/T\nMbSCED5buhtX0fAFAp6Bj3lUYuiOKfZ+nWzOnFUS6TmeT0D6+FQmXFP4QAy8\nR7AmRKVHuQ4bv3Wle6Y7cFib+EltyxM+IdHkfmupGHTdCbf93tZ9jHWpM/Wg\n4ZVTkAftEbJOgN1NQ6zJXYqrhTuOaBrBmdFXjfF+PgPYfkXsMCdWxxngJs0R\nl72WIncK6hE0TSjX/SujufUphEM4FrJZ+9PHnuhCFPqt8yBRv/tssHh20vj7\nmgTCxl5xIaa8UWP1s2FemMq77u5ypsprUdjvgstWIdrOWYo4RruXKnZFaFCs\n+kRAGMYgxWbBlYePW0RWyOYsOv3Ikg4bQyctDLzroSZ4YZZ0xUvuDx37ZCtW\nkYMUnGuCzfuJfV/9pXuOJX7aP81ns9yY+nYcQmYq3YDUPjC/JxoZ99UBr7DB\nEEEv1KL7I95yH5xvSb6O5H++FQldxq4j942ZlfUKvuEg2tOiXV9fAaHeKlK2\n11WB\r\n=DcYe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-reserved-words", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.8.3_1578951698827_0.9750179543178825", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "0fc1027312b4d1c3276a57890c8ae3bcc0b64a86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-qN1OMoE2nuqSPmpTqEM7OvJ1FkMEV+BjVeZZm9V9mq/x1JLKQ4pcv8riZJMNN3u2AUGl0ouOMjRr2siecvHqUQ==", "signatures": [{"sig": "MEYCIQD6xB6aqWC7IlUoskwvuktXkd6el/BPusodZuuW7YR8rQIhAIuNhEi/jC5F46bI35tnIcDsg6euTWaLOW4kT0rqpKI6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSlCRA9TVsSAnZWagAA+k8P+QEWP04u/KvQ4QI4+Nus\nHyuGIsijR7F/Ljdb9ePjV6GWR3O/Z4cYFcAFrIDcX7UFmWmznE/ZBpp3Zoie\nqGdItcQ/6okwRzp92Wen185u1hkSjdi+RKsnVo0M2VqEAR6xjVnhINGXD/6A\nlFn/d+JtcFxVXNyBZFGsGjpzQszRASVljIPzTbvemT3gJ+ltU6g7AhmzfT8T\nwbiX5mYW7eIwMmfY94AcMHP5kDhuEDrge/PgIDFvsUPMZmABg80HI7JESW+y\n3N2enK+9XfV6wtUBjAj8Uw4ihk2rF3Wx4iisVc1mlZrPnOCHIbsrL5Cjzht+\nr/4x5XmHVEo0qfKK75rG78OvbKXE9Mho/KsTfOA4innNdq5D7Vh2zY0JB7fx\nU/RmXjX6vQ27w2U2AUqW+5Sl/gzXUNpx4x3/dl8bRu0vKI29iUIpyglC14Yk\nbcuqed8030Ilvi2JJ9egz0q1nqNSXpVSZY3NaexfcoKbcNEL0vr5wUO+7L+F\nXc43jGJpifxM6Dp59ivIb0go51cw5waYRza9gAYkcVvLy+RnA8XTb3E6gr/Z\nheP/1Dj+PK8A+zC1qsNFYX15ieGRZjXE/KQhK39cMlLC+uTuU4pG7n2y0NlT\nMYk9PV9u5B+hHmV5S6ThbPXHq6hd+Q/SFPvXpdiWoAmK/OyQ7z1NoO+DcEnV\npsKP\r\n=vDhu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.10.1_1590617252295_0.9460011744995316", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-reserved-words", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8f2682bcdcef9ed327e1b0861585d7013f8a54dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-hGsw1O6Rew1fkFbDImZIEqA8GoidwTAilwCyWqLBM9f+e/u/sQMQu7uX6dyokfOayRuuVfKOW4O7HvaBWM+JlQ==", "signatures": [{"sig": "MEQCIDXhTLZ8pZbngFO4cxoQ7sg1bPRpw6NUoTBAfuV9oZC3AiBWwvQw3ui2ZHGMgccqPwgNv4C/C8REzSeeh32cszDUuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zopCRA9TVsSAnZWagAAL3EP+wcJy6HMWQc2fFndYjYp\njlOsQRH/yZ5Ag1B5Sf2F6hdDdP/N6/LGNGLSrjqlytw/7O92ggYBvea6lmh+\nLRBjNtPcAxdgNgjkGNLS36n0h55wzGqVDaXszqBfWN1wLMsa8PSArzFdY5Ru\ntCIlij0ZXtHCfy9A33eEsvmLMVYC5nRhFTcVve0nD+XdbO9n68q6QUlkPJqI\nP/pBdt6uzjmBNHCIz/gatdcf7XHuXfZ7KG3w44rItrhqVkWm/rKFqh+K8lVy\nNvTl1BZCNOY/jxXT9MZK9UF6RfBeBSMc+EVbEyrUk7xfI9AauMiLCAuVJMhi\nQgtH2Gx3uyIvmvjU7dwBcorMqeEjDGIi6bE2BWue8bT7qiHRJTYZTs3EoUyR\n7MIgAnHDxtMwMxq4nuka85/6cOsytIFMOMBUmfCnegsMyBCSzYogJAdQ66SE\n2vQTh+wldyuBX8D/R2IPp5CHqLfr4AChz8UhSnD/wZkC11gCSfUi66r6fudm\nrWKSGe/+8VoPL0i6TNBh9iw4UDS3DiiipDaPWizgr0rsiMbtstuM2dXWYveQ\nnE6m+mE4ojjYT1VxW29/WwrQYVY9lzGR1eR1BWiRplCDFJO09dwfHQDnv0z3\nBICWJAl6NJ5lZ9427acXXJ+HAeqN9XGlAXjM48b6i8nlyws3MzGJlNRyIB47\nsse9\r\n=bMaH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Ensure that no reserved words are used.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.10.4_1593522729229_0.04498351205163731", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "6fdfc8cc7edcc42b36a7c12188c6787c873adcd8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-pOnUfhyPKvZpVyBHhSBoX8vfA09b7r00Pmm1sH+29ae2hMTKVmSp4Ztsr8KBKjLjx17H0eJqaRC3bR2iThM54A==", "signatures": [{"sig": "MEQCIG8u9D3olQ1gvCngAmIJlT0If1nR12zh3p49izYJjenjAiAZtwjljjip+IfeZIikxsyFFIICH3GgzjFEL4wsZUAlPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/cCRA9TVsSAnZWagAAH/MP/jBGIl3vgHQFUVl4CoRN\n1fopPfjVV8RDI2LbKGwC6FwWbw/hOtC97n7NeU9tQvPguYiTdjHF6csMJ02j\naa/5HV2GJwSK4IW0ZSU3vGvBEZ9AuwZfMPOK1mc26bJTBVY8g3Vdg9wJU3gm\nv3IgNladq1LuLkdXmgZYiFsl1gwliwDbFlfePMhRYZjDOqVtZx059ve1c0l5\n55zbCe+QObLEO9NeI+krrFmKjO1sfI37QkKqxWLGLTaSol8IP6F/NPiFZChF\nGP9TfM7EDK7f/RxgiK6LwTu6RG5mHtjkLh/tmUepEXwKBEWwMUyqg90bI7IM\naFHa8vK2VKxKr3KfJjh0Nl4g/Hc6vl55VXwZ5HmcQkTV9oAHA0fWj/fuPUBZ\ncz7pMk92bHr6o9yy4D+Lb63ila12VTR/PVcAT4KnbUYxzlk0UFOkwBappDi2\nd+3iMhEebi/FafYNT6/MDPw3uc+Ojnq2XadzOfSMP4b9NgRyNO1oMHPewQKl\ndM3V8BGGDWvD3Rzbw8BnaEC1qXt0QpfDAhLdTKLWnzfOBeaH2dB9WwJmHTu6\nJlAg4f87/8vtE4eWgFtfm5PP9H79s1Do34s4DoWjeoK/oDYOn+p1imyuBQ7W\ne0OZjaYTlfChRwASOvoKaA/kH605iq2iCEAdOvi0Lq1AcCaTfpj9sMXPH3JH\nRyN2\r\n=SeWB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.12.1_1602801627724_0.43516439946520546", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-reserved-words", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "7d9988d4f06e0fe697ea1d9803188aa18b472695", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-xhUPzDXxZN1QfiOy/I5tyye+TRz6lA7z6xaT4CLOjPRMVg1ldRf0LHw0TDBpYL4vG78556WuHdyO9oi5UmzZBg==", "signatures": [{"sig": "MEUCIQCpllKITpuj/zwXYba9hu3E53Dfny2sk3YbZ37uo1GfVAIgMYNDNlFTrAr3nFDRUMkbROK6OKiOuNx31qK38RWjl80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgrCRA9TVsSAnZWagAARGAP/R3h79nU9diSkMPHnq7N\nLjMZQr+VngaFczDt9m94AnV3C6KECrMK62IAcnRK5PIbpj6LA+bwLrw0DKIv\nhLw3v/UJvgtXnJtRIobftTMvgWC0XZQ9kFD+3h1nNwvlQrjuvLTMFdl3yF/x\n+V4m3TN1sZaoWc3gLXEIDB5FLXn5+d01VGgwyglQuwx8kzw+XAgUpQu34CDb\nVgoJ1AYwL4S7vJuvjzUGUEEakOdJ9aIIGhc1oK3oBH7FOEOHLfTZUQXgpdiI\nTJ0bdY6+MXhMZTJnE8FjXbY4/NI9uI+Xa3ysZTdqXGzN0IXnweuFDngftZzM\nPHk0o7PFehKrAT3NJmmKp8L9+mkZdJJ6bNnEtqu8fWuBBaVuTLRIVsEfthvx\nQMBUS+QFiEcZeMMq52PZvuyqAPovRuwK2U/8Rxt2HVPK/R2r/zikFjX3cw0n\nPjkPUslRmGpd36rv64BmPKHw+jXQiNr/6iH4N6ZiWsr8MceKyqr99/j1duEZ\nrHCfXWDrzoq2pfVAystKLi5BwxYzQcHrwo+D33fBU6xmM81+enkhx0vqXMZD\nAojRVNtC86lf1HlYYmPw2bAnjMZbvF03Y2XsLf3MC8GJB1q6ARzgPLpu1uh2\nFsmCOyUj5m8O/+iQSl/ApefidfdpwWURKs0GjtoTHs5rtDxO5nDtgOPyqfuQ\nEPx5\r\n=FWDx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.12.13_1612314666946_0.6985390646059622", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-reserved-words", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "c44589b661cfdbef8d4300dcc7469dffa92f8304", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-cv4F2rv1nD4qdexOGsRQXJrOcyb5CrgjUH9PKrrtyhSDBNWGxd0UIitjyJiWagS+EbUGjG++22mGH1Pub8D6Vg==", "signatures": [{"sig": "MEYCIQDnX0g0P4fGyvupRDleNj+c8+IXW50h8ps+Jm2stw0H6QIhAPNSoLbYndCpPIpG+BWKLFptLgFDys/hMeT5uWQzLI1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrNCRA9TVsSAnZWagAAEAoP/2rpkp9d6P5gIYTAQxWx\niZcIkLhOHS+kBA8BwC6F7z7nyxBFXf3926Ne1A23aXUotInw4f5r11lE+yXT\nJa5LRV4gU6c2oT23MiS/HJgweI0TGNdyjMrlFAqtnKQksrDo3iqemkgmBT7z\nBYFmC6NJ/i3fygh3zYlyPkMQ/Cv+zI7e0waesB/TkAXo3b4pUfV31Kc+ES57\nrhpl5TUJ3VH6AQrdsn1g+owWi8tUlcHsOWVwUwsGYe4meYaTGGFHx3/pzzgx\n5Nmk9vQpLXep7aOg2ztymc9/ezQPQdw7Htuw54ONKkK81q6KDcioIxR0qgZo\nNGPnnFNXBH7zMFajsY+2jlr0Xlvv/vwbHLYURp1Nz430RD2tQtJMqF9+7jwm\nAVvNb/Zu9rDHmvWN8lJiKPqufJBfEfVXt3MC+KEH3ZXsgPRN31xO9ZGcNSN6\naF8ZHxoPWzyF3uQ1cAVBZGBO8FmvfjrDOp+/Yfdk1tbAFZeTcxEGZhg059SF\n0XiB0NqfhJPD+2nDf07dMyS9Fdew4Wl4tpObEIW7T1OtmP3Bo4H/wMrK6aBk\nK8xl1QfrKANE7smhfsfwp9NFMTBjoZYnjw4eaQjBkabsSHaKbSU9j3Rb9EA2\nF7VUHYAk9sqVHtfeULdO8u2zB4wYyw5PbFto0IXIWJzqZYWSTa3FjqXa3H81\nx62I\r\n=kc5Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.14.5_1623280333002_0.8063695617280333", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-reserved-words", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "fff4b9dcb19e12619394bda172d14f2d04c0379c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-Dgs8NNCehHSvXdhEhln8u/TtJxfVwGYCgP2OOr5Z3Ar+B+zXicEOKNTyc+eca2cuEOMtjW6m9P9ijOt8QdqWkg==", "signatures": [{"sig": "MEYCIQDO3Y6plqd/h1epsd+wV85RkemrZnw+unrT6GkB9DdTYQIhAPA49Oujd+U8WbN/at8EuEbkTuc5nCFxj6I/3uvyTmCk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2944}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.16.0_1635551253601_0.5553511798272581", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-reserved-words", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "db95e98799675e193dc2b47d3e72a7c0651d0c30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-aIB16u8lNcf7drkhXJRoggOxSTUAuihTSTfAcpynowGJOZiGf+Yvi7RuTwFzVYSYPmWyARsPqUGoZWWWxLiknw==", "signatures": [{"sig": "MEUCIDd4tursKTNy1SgAkKL3BSoVCEUAkE3d6cKxFaRGxp5cAiEAnwOqkWOEF/bpgp/Qt+zDzFGNpwC1AyyGkwyNZl1Cme8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kNCRA9TVsSAnZWagAAjPIP/2EKXzE6v3efs03Log+f\n8MFl19/wy9GLZQLJnYQ/jt7tj11s8w+QyCQVLj+nH95163e8H+0CCZoCU048\n7VXsuI/fdZJlu/uz4NSAlQ1kC6lTPsY9gyMyjDUy/hRlLBj+aYtHJf9JJ+5q\nvw1HSRLi5azApQuEZJjtDlYP4+9RpwOxTyFKm5ZTJimWvNQIIepUC5fw/NiC\nWsr8QxLJ3x5lc2zDVjUL2UeqcCoUioE436z5qRkQu1/jB4yVsmSWh6cB+e9Q\nLYjf7CqdF0Rr2yJcasqz0MO5K7enuJeWqwUk6NBRFq+0fFOn6uW4lK4EF9c/\nHwe8uPydvpXblA794kRD8M99iuLghmHD07nCYoBWkCAwCpSxgyBL2tNoO5s5\n5ICDZlvKxAkZ5oM3whaQiJde1g6pA5yTiw/D0sf3n0RpU+xjuHaqqDQ71uCC\nbytI3cJbT+9FATBlmA+3E5Ai5Jn35OXW9QZ7OfpsH6AhbcI/jEh40C+ECzoP\nmyfswCDL+6PB5gqVq5ZSveViccDTYz4rsOvOcxiYNp3IGaZUIgBIGSVUJmnh\nsaLmYrvVcWywY0NakJAxlGMwQD7fu/1h7xjzlWMJaZGSOq0nyBNIzQk4LNz6\neY8tx7XqA+TT+6f1eQlU9XKcy8HDoiu2HmnTZH9e0b/ILWYYKnDhiVTpZYVS\nHS8g\r\n=cS0D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.16.5_1639434508840_0.343918622310704", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-reserved-words", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "1d798e078f7c5958eec952059c460b220a63f586", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-KQzzDnZ9hWQBjwi5lpY5v9shmm6IVG0U9pB18zvMu2i4H90xpT4gmqwPYsn8rObiadYe2M0gmgsiOIF5A/2rtg==", "signatures": [{"sig": "MEYCIQCrqTwmIeiE3BlEq1afHqTGQzO2L2venbQchfRwP+LnBgIhAJRC7ZGMzMaunXNr/ghpde22FgXOFWFj1FjaM8jz/JOK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0wCRA9TVsSAnZWagAAjv0P/0IozU98phqqWkkb2eQL\nsA6eKQW2HVojlnxgLQOWeGKjznSl7RGSL1qK0cOS/HjXLEZrsB7l6LWj78/O\nS3AHg/yiiYXj4L/tsGyGHpYs7tlIs32Cmj6mWFnP9RSiuhe+k5SReEiHoM+m\nP9sMm/N4okSGsp6obfrwDNgWxTqIJNZLTrDLAzkLNZC8XR7Tp1dUaHuHccN9\n+7LSWSbqaeKdA4WXNWoSzADm0Od8/VE2HtuYKlf3TuoVsaUbpX8fjoD7g9T/\noahPPaOoG8XYMBVOOfj5Qe/HkZTLDdWRJZ/1CFYxUSwsNiRaXy3i5UNLaFPI\nEM9CBWddKB1pSiGId7xbsxCYQeGzsBijQGLTGci6KJtDB4yCiQjah8LsGKPx\nOHi8dY4XJdVHWrFuheILB995vEFOGdnp0uInZzkn239AaD3Cb2Kh6tWHEe5S\nhLxZI6HvT+yBOpKa9liNNl2RsU9eNfd3cqPB0VlMArXbCNGoglx3/9hOnMOg\nGdqZuoa8zyqPuBlfSca8BC14SwKxQuJbZynoGz4N3tJEDjZm5v4dU7EE+4T3\n6vLUoSjsysCGs5S5mWL97ixVrADyivPxBFJE85lCHI8vCd/jGQatQFjsqXv3\nvc0hh/PdEDy5VbWXsHsz/07LrLZUOwvExv86WLRyvm7Fu5tw1A4kt0jbjb2/\nFAMs\r\n=WkyS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.16.7_1640910128706_0.4413666933764713", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-reserved-words", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "7dbd349f3cdffba751e817cf40ca1386732f652f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-1KYqwbJV3Co03NIi14uEHW8P50Md6KqFgt0FfpHdK6oyAHQVTosgPuPSiWud1HX0oYJ1hGRRlk0fP87jFpqXZA==", "signatures": [{"sig": "MEQCIDbK50R4u92inLZctE6SVAXaAKQ8g1GDLCi08k+PlrZyAiASxUiKLJJibSL/AJf09KQyVj7fyDbOdMCnO826qlnjfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG8BAAowEqGlcuazFQ7LkkL5+l7b8EiXmSGVzlz0ElJgUq/cXtYcph\r\nICs4gD/g9vBhwk9KdBjh9HQTdI3f+JEhSoFuV5EPFBMkOoX+TWIFKY1jwJvS\r\nxsm6910m8IcgfZWz8jckY4rQQub8SsL9+S1zhzbrBwaPLbfY57ycf8rqGP5M\r\nem/4KClRrGe+D0gpgzF85CV+2Smk2Hz3dMc2+/MxFxFAuHuOkDDJjrLaem9l\r\nIzrCd/lLe0G5TUcAYnlX0AlDHhHPm8Dq2oZpChv9CUiaBMm8+QrGMtA+N/MX\r\naZTTIkgblbU08KYrebWmKX3Am2svX5wsPw0tBvXJf28y3Zduzq6SCjSEu/Z8\r\nQ5Fg8QmikhZLB0Oq+2P+e2Hc/VuISQ1gdLn5onimzLBlQOoPmK7QVIBMIPdp\r\ntnP1OEbjcJAwV3NA3DwBWLRZ/+Ywx3Ah7tWzQZiZlB3Tln/Ei9laEebeqh0G\r\nc+vBJURUUNwypEPe23upOPdha/BW8Axtk00U76Xd36QaEXbtSzJBKl9nUMqm\r\nfT6RfHVw2jD1jG4FOkrjAynGobOcw1nVaICDEwVLq3CnltF2HJEHUuOFQxtB\r\nxG6XkZ+uise9+xxF6Gbb1Tbwuw6tDl6jKBqXDaWrfevRvHcmrYtS5oXLwfDC\r\nrwq4T68ZoVAA5i+fMLum7qLlfCqRAFTlsZY=\r\n=3bTy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.17.12_1652729581698_0.6700970091939884", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-reserved-words", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "b1abd8ebf8edaa5f7fe6bbb8d2133d23b6a6f76a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-oX/4MyMoypzHjFrT1CdivfKZ+XvIPMFXwwxHp/r0Ddy2Vuomt4HDFGmft1TAY2yiTKiNSsh3kjBAzcM8kSdsjA==", "signatures": [{"sig": "MEUCIQCeDCOCGccOZBi/z754492xc1scdObYOhYZRHrOfozq2AIgLl5lPUSU8T7nu/v3kYlVh1czVTQ1x66ViZc5H1wfp+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmiQ//aiqb212KAD/av1CiHPVdJ/HTAxCv45Quv7a0Esj00DtE/JIT\r\nv7MdqU1t5861fgG7bYQu1Tt+Jjq7y0zw4miSfSt6G5aakV84+CUtfk6u/ZLG\r\n/s4+3ZgexOXTkWVkh03/27SFM0yUgR3LjspfFhLmNiT9tYpn6kapWaq6LVCp\r\nDu2B6l6xWqbani16VO1Rpsqpt/z3A5sCF9tyozUcj04tpog05a8jvRk3sODx\r\nSuKPE3zsZIAUEoSCDi7RltcYzJYRuuiu94rRmKd1wm05PMGcn3EECqxgDTbR\r\nzuhu2EdQSMTA6yZN0VNXosWXh9G4fwH27e4qC9hXJ3HVOoikisgP5sH1Y/o2\r\nQ3DRAHY+Ie1klyaxLW7eC07/TyXLv418ZWkzmb5aAdXAcmkC1euzu7qtSGhB\r\nBMoUzsFyIm8zT28T0IFpzunFYhkFGSceSA/3b9HJbsNphlBqdkrBT/Xq+qG2\r\n60wjvhdFpoRgcOq/KXRE4w63+b/TQYUvtVieH9b3PpioiE0IHNAG2v9LZo7G\r\nEjDkphSy9+3jeoUb6iUyRjKxK/tNQz13+nfXRRk3hD8pi5Ng6Kot8+rKsvLZ\r\nf7v7Ecymh+Yi7Gef9eklpjOeeQmu6zGLXOsTWfkSaL0gSjlAkffMKisCh9ou\r\ntNgDZm4g5sv8R78rvHOX5qqTgxh2kQLyYWM=\r\n=Mcdu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.18.6_1656359403532_0.24626206585797927", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-reserved-words", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "3632b9c74cfac84462d52fa9e649d964c764da85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-/o9RBVqdXjx8S4HlKImBS9VWspvmZfqSCOXwO54GuATpi15NBAeL5RQRx+fjI1RrJU1LL2DurHDG3zzthWqVNg==", "signatures": [{"sig": "MEQCIG//LskqDjghtnAQlu3zPBHBPvlwwQmqlROLVV4ISfC/AiAwicXAruoqVh7WY4epj+AZcqm/x+WFRqHQsPb0aPByQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4319, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqqrg//YrdNfGIMT3Qh5A7fQBszwonsfcoAGFk4Hx4p/Cx5Boouq+2C\r\nEaTeQ9Cnl/buBaeXyv+jHsu5HreXQG5j+hqZWXc5p1vFg7+GOeHOxYPKgq7u\r\nI991Af17yWhahd/BfgnpsgVgqcbsyLbGg3jvAWwm7eO+ih1y87edeRayeukL\r\nrBURP+eoG8pr5R/Qrv/IVBDRtZN/a+FKqKPe6Nfpgc6NuRESWubZDUWdlIYr\r\nh1aKhCGRReeM4ckBF4CtUgvHuCupElkCZzeRdQciD6YiVGkfU/SA68zHVDL0\r\nH366VBoSOwdW3fBtZCoBFn5e2sBKvbgxGB9WxXY5+WW5MV3Es/9d/HUsob01\r\nIAiiyRmLIiYNazfLUQ6Qbdc8c3PhK9sVcP7KS+tVBUO/ZAUkITRWI2Anmr/c\r\nNQNsycN8dVKs8NGFhETZEOv7WSEvVFtYuMLp9zhouDlQcDOJ4xc0Hzq+J7lS\r\njPOnqYQFgo5YFJgmbxLju/9oeRuwnkitwn4i5fd/RCADpowULv3koUGzKz+Q\r\nULUWPmqnbxYBnokSNuRRQdnlxhTN2qiQXlYiESoraUXCzczgbD63gE4rJUo6\r\nCtU7+r8mppsowH5mGNVv6NEmDTU4DYx+ZLEGByNmttIokANG5Iz4Mr/PgQ3u\r\nEJZdkOvEp3tEJNRClY7CZmqwlFYyNPjs92Y=\r\n=OA1i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.21.4-esm_1680617369365_0.20939057809996853", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "5f8c7f306e9a89f783fe314d9e3f9deb6e2d4ff2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-zJ1PakwzXMUF3TIVdq9tUmPyHLj8+gW/4ghw9XJ2S6gV/kLRIk6dXccC5Rs3VuzkjZTl0N838qgY0Wgw5ltZ+Q==", "signatures": [{"sig": "MEQCIH4d+EEMtTNtupKMdw3Ljxn4F4Bgxi13E1BVTpjBh91PAiBJyhvELF6CAYaiIyuE6ouXTaJlxMkUlNvBjAcGpt2yRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5YA/+OPhGGnQBOHvOF1Nef34Ya117gxKMnpiejRuEGm3E5/5vW8tA\r\nrCMXmYILhhYxp1/sXlqkL9rX6S2wJIlkBAuil+SvV7wlOO2aGCE/PUVyhOw4\r\nwk1E+zEzJgPJPs4Ilc03DDtOAQJzy/6zr56cG9Jr1GQ1ZRYu/RSJ8QSojH4F\r\nGYGEp86LqYrU+9lATzQaewccngfERgDkoMtws7ssLnvtQ+4HdXPH8SMXp8ZI\r\nyF66dhn/6PQoYez67TSboLV/mYUQ4lAl6ttX0ivr4YTq8bnh3uqAoTFSmpw0\r\nTrxwa74SNi4qwd+xe1CfiGHqPSsr9UNna9KjkDmbx9560cTsume+qxaBtTeC\r\nGzxlt+zWwKtajNKfRSmgrj8+nUTawtHiL0+pNKtrazSAQi4Zpp7m9BBylTCU\r\n3KFrlGWW6dcgKRZKXsu06Fv2NPosHbsd5uwJUfS0iZngAbAzjetTjivxmURp\r\nWkOnIsoowrroMS9UlLHe9wnX3n66myzoL9sUCUwfxnPbq9gg3MRLC55T6R4X\r\n+e//x2XDllPoUzUwe65rAxv/ihBV7IssFxV5TIYLuQug/2C6nZ4H/Re0abb2\r\nLPXEElV6yv3XTt4IulM8cteYv1H9yknsAOy26PJXC59riwqUxjSFeujM763X\r\nMBcbnJ0uPKBNOKFs5b5Y+d/XtNItwPYKwSE=\r\n=fn8i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.21.4-esm.1_1680618081827_0.3799912063172517", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-reserved-words", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "a5c6e216b52292d02228595d7dc2ad536451d1a6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-wQsq8DoZMVJvzKBVqjfcJSNCfqbDTR1ubl2CQeAMr5BZ7TNBk4rIcNIXEw/Yr40x4oNC1QTwnm3CwDyYQiXYZQ==", "signatures": [{"sig": "MEUCIQDidLDFvS7t3Vm6AsQ/EZD79Ejf5ZqRCuceC2H7fpJ8xQIgTUJ7gkss3nHo+20I5VQMXQNY98cLgUQJ+EgEP6B2J1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDabACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRuw/8CPC86ZiehrZ7jSnyokfWfLZxjlwNucIbQaFK06wKnYxTcpBs\r\nJ8s43D5tsay4GyEFzdjgGOAXeLmbHKeCCWx2eBUWE1JTInPAOZ0sbAUbiMph\r\nxbbSHpWUbPrYjs08K0vUeb/+jSyehsjrb1hCagvW9V0CHObnL7BHl7kb5FuV\r\nA4lcYlEQT34OgQiTydP0mnqtJdlgeVhCCwAndY7gMSUya0M+4hzIQnuE0R3A\r\nmXfW3vGAsFtWDQwmOBIsJJOaxUoxWjIwb2wvC8/wd9a4U+UG+4XMFNEv7erp\r\npNptE8TDgKEptgoZh0ees6yJErgsrWuGgy7LN5397bwYPeTUl0Vs3UXT9dxr\r\n0sQ/zwaRbql94Cgt9YU7xqL1RLb/jsYpcqxkmoVDrogQjF3dTNl08VT04vfq\r\nI7hvFMF4UjUxDure4UzdzNj0fNEQmgSnuNsgQ9j439SudywNwHPm4f7KfuXi\r\nqLVmQMxOcv+ZEAqs2cuQMCtt2dX8vIbtQSwz98sbhEuzcAcG+I9UUyly0f7W\r\nJQjH04Qg5Lt1kXON5vh0/5hu8v2+psp/TLW2p9P7XxCT/8K5L9VFsYcipZhj\r\nkiDB6iKRaf7BOEIkt7pz60vjWBaoNayryK7JTx9ByhC/3SHEPjwADiq1NLWA\r\nPgSFXD2Y+JEViRtNzCHiBH9GKQemfY5W/0g=\r\n=fQe3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.21.4-esm.2_1680619163020_0.485639770313014", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-reserved-words", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "10b18cd9c6da210df026cc451f265661208db252", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-ouMFHJ81O3rKI399Z1Fx9pwVKqytabvDosaRr4RebbFGYXB0RV9NSGHoD2611PAC/IL7O0vEj75UWJm1Krt5vA==", "signatures": [{"sig": "MEUCIQCJ48FkDkYHZ4iUlA9gymSTR8b30UM/oFlF+qoGo+qwAgIgOgBGdETpZeDGD/Q5MZmqvclEf61rvoXe6lReM8Prp1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC3Q//e7iZEtySGtB1sjM6wwTzqd/5FpniXpJFNO/sy2FIpyRQIwI9\r\n+r8JCL40G8d5hoBcttftSrkcIbUIRCtorLuk+F4Kf58QuGfvfV56YHUfVYhz\r\niXus4q6Cvx+wGCPgtb2TkjZd1WYMtRXy48HT6o6ri1cjE8AGFSbX5GTQwAl5\r\nf8mQvDnj4NMTmZXehxTBskCGXQt8uZO956jWaX7KfTdK1KrxnkgQke6YAhCu\r\ntXKomRrnL991EhOpp+T8dks3LHnTe1bldmSEySbnUeJmw1Zl751E8acMPmQq\r\nq911Y2PZ4bKgGFTs6AoEuR0uX8gkbPAdp9C+48aQ32MIKO22U39SW0cb8PCt\r\nlp3251b8vQ/ZT56V8NhsLd9oDbNdYjaasczF/N6AEIXXioTDiEntj97/Dzo9\r\nNGZ0Ed43yKWDv991p8H6WQrCcG9qBVEXdaWscBdxFbEJFQAwM5ehfRgBqUYt\r\nPi3sU9p/aIS8y1pUTWsMaYSFuj/3DZarU9T2VthdKSULDmWA5ztTlF4WySpL\r\nQqdMNYI5+phDXQ/kXAoaU7Vs2UyhxOdOFTizGXQry5/4Gw1SYKI2u/X+rDGU\r\nMmJXLTD4kO8mxnVhB56FOsdlviu/AOBr9uuSRkZgjNr3/aMkc0XrYykvE2H7\r\nOpzvPJAUUDdLpGzw6ukFEAlUQOyXQSgxcwU=\r\n=6yR3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.21.4-esm.3_1680620171486_0.4808312745920995", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-reserved-words", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "25991559c7f69bdcc48dafc61d14b9a15ad22a7c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-eibD2R8d/8Tx7m41Vi8CDRlfIgAFNzt5tvWpxmuvsaqec1FlTilZdC5Ni0rpPr3SkibGwqLs2FSGUpJugrcBrw==", "signatures": [{"sig": "MEUCIHaR8Y8mP+CdDm+5U9HEDHmx80accWPjGFAujkv0Pv3gAiEA/KzivlXXF8LNVjL+ooV3hbnw8dROfpLHEkgnIucHVGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCww/+IZ2WYQ98/GG+ZtDnFnixf3DTg5rlB0J91QQTvObDC1le1yCO\r\nxas2OoyDRpGD19WE5GzVe8c2olO1UINw9326aBWqfmA+VdIYUOBc0L9ZBVDn\r\n0HW+goqQRhS2uBjqhxwTzC2RC+NtLa1hlHRgzTqtiA6tVJ+krV+4dBdioxhE\r\nRdSE9NE9qw2sttojiZK9+e40kkucpGHLCnaa+7/LmtyGpFGnmgQAX8kkkwgM\r\nGtjLaLoIpTIga8LyZIGSgfb+SXusw+6SKIS778uIKm2BAE348xOlZwe25KnS\r\nAJNGmOgg+WVIHxsy6a1PcSeCdl2klClNsNLO481fUb81aiwxFKxQAM5OY3Ax\r\nhQRv7C/aR/mdZEcEerQteqJe30vwxWrPoaELotY5+bj0jzZe0la5kJWglxsQ\r\nroJzTc/W55MzO+WQ1bW20GcqZ+RW9lWyCVs73Wpvzc20HVI5oRYL9Iz1gsVa\r\nsitWwJy6hg7ch7bTxF1Fm654l72hA7jefM8jVnvgwoA4XjCBiNN/PiMNh6i2\r\nUgYXbQAkeQaobRPWBSRhuJYzbH4zBj6vKTKrSu41toq8VoseZS3oi9LFoZI1\r\nwbOW3oJJfsHOLTg5vk4V+F5d6SGNSKQMsJLu81qjvO9SGYptnkzEPVQaOpjO\r\nPKulXOtBhZD+pcaM5aXNwVvL3Ec/PMxgBzA=\r\n=9hBT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.21.4-esm.4_1680621203696_0.97185849525642", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-reserved-words", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "832cd35b81c287c4bcd09ce03e22199641f964fb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-DTtGKFRQUDm8svigJzZHzb/2xatPc6TzNvAIJ5GqOKDsGFYgAskjRulbR/vGsPKq3OPqtexnz327qYpP57RFyA==", "signatures": [{"sig": "MEYCIQD8J4z0SUy0UuxkvEWo81JNZIMjcatD9BwpGyZtMXZdIgIhAIeJHbldaoGgUIPw1HIeJmDxlU0eo1/7LEZYWheb/Y05", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4269}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.22.5_1686248481708_0.1274820290488834", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "cca28d3fd981c0a0353c78550e06ad2683c36033", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-79kiwd6/z0+fYrotsEJh9Q5H+gRsxjYKX03v6ndnfYsQ+p0sgGCs1OtZJdmC4aJgkCu4sQ4XQL8TOfUqDKIXiQ==", "signatures": [{"sig": "MEUCIQCTT0yXmnvsqywmtgF804itI0FTAEAIHHR63HRGV0AhzwIgD1KJqvqPY8ic0aJ31dRGmSuRt4JgxBgS8WXUYdQAJFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.0_1689861597419_0.13758116387706876", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "32975942ac9bb1df6e444786d8305636c588fc3f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-sqzFWly11uY1lSsOXdj9iAlj05m0jdCeNUkyHy74RRw1sIuk34CFMjUcsChTVV/p+tU/O8WWJ5i40qQZlbEHPA==", "signatures": [{"sig": "MEQCICg3RTSQrci8ac18ncGCFeD3aoJutxb/DinAwEWSEp0hAiBwzff7DHgaSd0kBO95dytcNcYpLsON9B6zHbf/Fq+E/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.1_1690221122363_0.8587536077471594", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "698c411e971fc8f9e86869c7974dfa20cb9ea577", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-cQc6N8+p293eHqDRs1wgUsu/i2uTJ1EuuLz5KxtVykqt+oE+3joO3U8gpuI+QvscEuQ/hfuTaD+pkHXx36EKsg==", "signatures": [{"sig": "MEYCIQCjF2FZqMz9tbn09u7DDYhGXxueA40Leu2T3E8DJftUvAIhAJ5c5wl2E0Tr28HhowPxlSHl+9jfqaYtIZYXyO9vjq+j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.2_1691594097439_0.5101560242484724", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "652acf325c122bd07c106c6000f498141430c3fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-EzgeL/9N08hgnwACTmSBd6hKwIeUkioUs+0giMEM4j5Zjd/TiudTkJvlOlcz6E7NwaK6wqKfeaZ1SOges1lBww==", "signatures": [{"sig": "MEUCIHoPXFFQFSCVurkDVePWyCbk0beS8l9Uz1J02GrndNbrAiEA86/HlDskcgMkXEzhLlYPx24sWdPXye3eiOMsamEaIpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.3_1695740215064_0.5202340093707327", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "820c890cff181d79ee01d290ca05c835f0b1ca3d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-HkzSyDojpb5Lv8iSk9tfUhHszyeAQN6/CXqzItQwE8TMSe1W8L2Fg++E2msA+5oghCt+adienSI14fwSkA4S5g==", "signatures": [{"sig": "MEUCIQCEY6SjOAuPdxlxfFGIrnzFH8oCA9f9bNH0gCcnDEpQmwIgc5MomirA9sNkx+VMNix2UIAPGeSS8HXt3yqPKEMfaJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4148}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.4_1697076380578_0.710011896555675", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-reserved-words", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "4130dcee12bd3dd5705c587947eb715da12efac8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-QnNTazY54YqgGxwIexMZva9gqbPa15t/x9VS+0fsEFWplwVpXYZivtgl43Z1vMpc1bdPP2PP8siFeVcnFvA3Cg==", "signatures": [{"sig": "MEQCIEuCA3Qh/vOIJcbpxFEtMSAZLEgeeVYb6TQY2EY47uBaAiARMCqygDRC1jJW9aQCt2+91XKDLFhWVOyv2A0Nar2J7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4349}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.23.3_1699513439825_0.1746289851211016", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "7d5e7cfd46813bf04e2defa85fb9a20d8c443117", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-hcqKOKhMzGuEzeecGEdPYR1zYQBHOP/6SZ05LGMf4kEjks6AHNQ7jW9b9WroHpO/nGUGZIHWfS/BSMdlWUAl+g==", "signatures": [{"sig": "MEUCIACbYAxs5Pc77UFpeB0GnCTFM7r3g/Sq5ZhCzjcTWa4mAiEAsbzQGWrPPqUkOl7y2ENVTGy2uMpZipnu3amqQXijJz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.5_1702307934726_0.19265720191933844", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "22a4c19a5080b9fd0c937279f12f73053207894f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-blNqDmaRpKnqILYTZsO2TVk1RxxXbIcES6boiN00cCSBbcT/7/YqHlJdWVc8+XLtOIkgE0GMj4qQYga6Wn5SZw==", "signatures": [{"sig": "MEQCIHRKw7/vjrC1KHc6CfH9IauW5pv9fXd7pvCsWId5D1yIAiAd/PZiWB6QsJv5tfsO5ZWDkcO72h94UXrPSUdMtL0dQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.6_1706285646764_0.5400876271861945", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "00d6ea30e6c09dc823aa61ee246cb1fdc7e152c8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-whKdLcxsAZ+RXBXSjTkfva+mZW0/AZivuSEMnzJHB61mgOXAvWxxwxRtdflqxK3OkpElpyXBA5UC+Bp7Yo9NCA==", "signatures": [{"sig": "MEUCID4QGzBgiZ6bRnbQ7r5AJe7Lye5UIZLSlhdctBTGYO2TAiEA98kMfkEYh2IfcmaLosFaB0qR4o+X0LM8JIMXVG7/Rsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.7_1709129097226_0.1819930414315558", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "8de729f5ecbaaf5cf83b67de13bad38a21be57c1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-JAclqStUfIwKN15HrsQADFgeZt+wexNQ0uLhuqvqAUFoqPMjEcFCYZBhq0LUdz6dZK/mD+rErhW71fbx8RYElg==", "signatures": [{"sig": "MEUCIQCqhbDmvvT9WFAVU0SnteXqsuLUV4L9vtPOsrvsTfl2vAIgd75dDYTFGnRqt2ll8FLyMdJ5J1+jX4hmv22fsLl+bKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4280}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.24.1_1710841740775_0.7367092219054401", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "356581414de68576abf6a2e5ca5676ace55d4117", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-ickwfOI40BxtltRb0BlJzxceCgyFS019fVJsLwLV/xjWxd47QgdUfek+SYt+XfXqwBm4Z2SCSyVzGSeM0Bj+fQ==", "signatures": [{"sig": "MEUCIQDHnk71b3jkD1ti7ceZV9BSzsg614aHAXfOGzRIRkBcKgIgbgIcXYLt+gzzdRKAYbULodFSaS2j8CPIM81a3BAkBPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4175}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.8_1712236792277_0.006303989481841965", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-reserved-words", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "9eb16cbf339fcea0a46677716c775afb5ef14245", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-DcrgFXRRlK64dGE0ZFBPD5egM2uM8mgfrvTMOSB2yKzOtjpGegVYkzh3s1zZg1bBck3nkXiaOamJUqK3Syk+4A==", "signatures": [{"sig": "MEQCIHYzM/8wFqCKzesxfk4Np5/nfkNqh4C1kIADq8mxQIwWAiAJz1hY7sDbEViQhduOQ74GgT50A0/SDjFPWz/56utBYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70209}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.24.6_1716553474840_0.614610923911032", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "ec385fab2911d3b17cc5e9f0ec6beecb4a2f8a37", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-BRM6daOx4BZWP5jGMt/0YAQQuWO6vQPWdw9o28G6oSUl9gMBVsOTU9egHrzWmV0WjvlFwmsVwDsHY2TZUUZDHw==", "signatures": [{"sig": "MEQCIG497Az3uT3/m2o1uN1sYrCBru9h2rK9nn5puPzGrjTIAiB2m2vI+sOCfe+x6HxX8846i1rcg+pkPZUbV1XgU/ny2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70373}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.9_1717423459693_0.9199440440676527", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "bffb54b10a592489f977c71337de23c0c425f251", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-WXNm6vyGbeZu7wiwdHJVC/bkONRQbRuVFQXCFDIKkdSoybTmR/WwUgAqtuv8upR3VUFDvu1h9SZgxLK9PM9AWw==", "signatures": [{"sig": "MEUCIQDhlEyjKne3X69Ln1BhubbkemOWqLj1JOXt93BwcjysnwIgWIQwSpauEg8A5poqaBvuGW2zWePHL6ISXEFnNszCRaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70380}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.10_1717500009063_0.97724591457566", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-reserved-words", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "80037fe4fbf031fc1125022178ff3938bb3743a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-0DUq0pHcPKbjFZCfTss/pGkYMfy3vFWydkUBd9r0GHpIyfs2eCDENvqadMycRS9wZCXR41wucAfJHJmwA0UmoQ==", "signatures": [{"sig": "MEUCIAYVPlozi49qK8o1NWcNPBJmWUvZq8Ji9hmVHUPa7klRAiEAvZFW+W4lrwgg9fm7+qVKFF4qIzCgWEI2AKw/2PP1Fe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70164}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.24.7_1717593324473_0.4704900379582504", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "0f703f6bb85744153f98286841c0731d2bb9d000", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-0YSqx6aGKqeuI8s6dYwdcCSCqnXWNedpCeg2Pe5XxdLlgn5e8VbJVFecOWZPWVeKIgAKHlRIupQoiXpmTLXJgQ==", "signatures": [{"sig": "MEYCIQDajD7o5giYkWQUNMDrXg3/sk6Hv68NEsN05tJ5ZJAybAIhAOWtRGl2adDe1CUdNAs/Iqs2oZrTKf0NqrkN2KaObhnY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70269}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.11_1717751734999_0.5462564764889737", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "dc4b73337e4a116c2234beda4f2444b1cf3f411b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-lzjCZkeJBRBi4fch00dkUVKQ//PYFk0pfyV0+Z/pVKlut7wesC6ipOmE/LuLba+KfxOUHdwZdcvBOVhMkzvkVg==", "signatures": [{"sig": "MEYCIQCT0kiAzboU8Ey2ziwwXFeMwRFzQ0jMEetP/ZGWTr8WeAIhAKB18YJRFB9l9iqv7OVxJLIcDM8cABvDAzHT5ZEUUjFE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.12_1722015211375_0.9688926654172383", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-reserved-words", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "dc56b25e02afaabef3ce0c5b06b0916e8523e995", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-3OfyfRRqiGeOvIWSagcwUTVk2hXBsr/ww7bLn6TRTuXnexA+Udov2icFOxFX9abaj4l96ooYkcNN1qi2Zvqwng==", "signatures": [{"sig": "MEUCIEuBQThtHOPNkyzdqVhllCE+qEi7kjJzFR+nmkSL66JwAiEAnFsM//wR5nc8UTv+mVfiO1I4M3VDQ+CexzM4UMJYEhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74702}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.25.7_1727882092578_0.6659603182065665", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-reserved-words", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "0398aed2f1f10ba3f78a93db219b27ef417fb9ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-7DL7DKYjn5Su++4RXu8puKZm2XBPHyjWLUidaPEkCUBbE7IPcsrkRHggAOOKydH1dASWdcUBxrkOGNxUv5P3Jg==", "signatures": [{"sig": "MEUCIQCrcYiXWhqR57hitqJ3gT+2vAbMjNsdDHimWXWoitZFJgIgNpvN1tykg7BioheYbmb9lr86Z8C4TlOQTyf5BqjmzQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4243}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.25.9_1729610471413_0.45569788614195916", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "0b358aa5fc2005ca0ccd0aaa5890d1049c7ec958", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-HLPytaeFNd8HYe+wqBRJdqXms6uNB01ZgrDNDsIYkDK2hXdDu69FPRYImTZbGB77YJMw8+d+YDiZWFFwSYZA6g==", "signatures": [{"sig": "MEUCIQDQuKaZ8XSnCq+kYZAX0FyW/JSJzhthWeo8sfLfduK55AIgd0b2F+Qco88oxFVpBYvUpVwwZdV2OjrG+10jtmXfYBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.13_1729864453279_0.28594720820081765", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "8a9cbb58ba9609e331120520272e6934313d27f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-3AtNeaQoYIqJ0beQzC0//ugSn+unVMO564nFQCUeIVsNzpC0j57OPhPme+QNTQNMJ2JzoJeRUXaR4pcCwiGjlg==", "signatures": [{"sig": "MEUCIQDmqlrqLsRa9qaMkY1sBlw7yIHg3HapqnN8r2jreScT6AIgU+oYFblSAt2lHkE2egArGrQCizaiVAF/yoHBzgvoxaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.14_1733504045817_0.4512617036237807", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "ddfabda010824170d35d0352504261b5520b1eb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-1yxYSMcmWLC6ZcOIOPHlv9eEebKCaj6fqdTMVSZx8NGF8JdtU/dJObzm22L+ePlrg3Aw6rNg4BpuVv/qKmqlww==", "signatures": [{"sig": "MEUCIQDAoXVb3ajP5EVqImCz89ffy4PBA6Jg7AvjKvVdWw72NAIgXb/C6HuGaHD0OUdqJraejsZyR5vbeZLHHSnYtJ3oqQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.15_1736529870771_0.06301379041799882", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "5de22925250a706b0ea250d688e1722cb25fdfa2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-XmX3uB1pYSs9hlytBIUEs7Ti3OYGJyUe2AJzOtJWo2plX+yQ9ppCfWKmgKR3yCQKHM1sUJNafB6InC5DMuQFug==", "signatures": [{"sig": "MEYCIQC/cv0vOgHOm1NoN9x5o2x/gjBfNlIc4pdJtfe1WWerDQIhALjgFTpIMqZV96/WG82M3PExE5rc7ZZ6OVmX2tCWXUHw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.16_1739534347000_0.9916372839286172", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "68f8475323b0070682edae19873f9614326d4422", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-RLJh2wNle6PfudIEin0XMy1S8u3Z4YYhTcs+iKgIReXXk0QQD8/cYNx8Nl+6nJzt8RMaPE8OFVkZJbYx30w84w==", "signatures": [{"sig": "MEUCIEJdor23l4OhmTxfWWv0BMC4wGTopFozUxfh8OZ/ktveAiEA491vMIk6QSf7GfkLiszRdDGVVGVL/75UxS+b0uw5PmQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-alpha.17_1741717499010_0.6501225503526102", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-reserved-words", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "40fba4878ccbd1c56605a4479a3a891ac0274bb4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-V2ABPHIJX4kC7HegLkYoDpfg9PVmuWy/i6vUM5eGK22bx4YVFD3M5F0QQnWQoDs6AGsUWTVOopBiMFQgHaSkVw==", "signatures": [{"sig": "MEQCIC3tMx3XpVLSwmFKne4P8dMRuqm595Q20ljO9SGsW+TLAiAFLczk0lRiNEOkw5aSMj9WQZVRuzkuuxe/hSP6PaUO1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4243}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_7.27.1_1746025736319_0.6655097317764798", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-reserved-words@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "dist": {"shasum": "36e00963d7a6fa64d4639d0f5cb389bcbb6c72e5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-qfHmhlrEVYmR8tNQ9OTs/n3b8uQJmD5mJPxxmXSUBN8QyUNLhoeys/zrsj3FyTf07PZ57SVPgzUKg+FI4zX+mw==", "signatures": [{"sig": "MEUCIDOFxU/Nuj4UGc74BWu8V/RX0srR6jTQ+B6B0S6GPbeXAiEAl6buxCP2li4C6iVHacKcie1l5kTUHlrjWYM+fzSh7Jo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4452}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-reserved-words_8.0.0-beta.0_1748620268312_0.3333481259719895", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-reserved-words", "version": "8.0.0-beta.1", "description": "Ensure that no reserved words are used.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-reserved-words"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-reserved-words@8.0.0-beta.1", "dist": {"shasum": "355db5bf837b59ede8e9d34262e04ba62243e053", "integrity": "sha512-kZO+u/gtHPr105WTpRNmSEyisOX8pIMlWKL/jRHzkqq+eozhzkdmQjb93I+l7zZpxmQzOr9YTrNUKviHBH2yoQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4452, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG1ZWI92iIs01MNYJgLGpVjKbfRmkK7S65iv/cM+gyYFAiEApi2ewSWLj531058GcuMciACnimOhYGH6+xS9emtslBA="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-reserved-words_8.0.0-beta.1_1751447060095_0.7956093662543497"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:49.167Z", "modified": "2025-07-02T09:04:20.463Z", "7.0.0-beta.4": "2017-10-30T18:34:49.167Z", "7.0.0-beta.5": "2017-10-30T20:56:29.248Z", "7.0.0-beta.31": "2017-11-03T20:03:31.338Z", "7.0.0-beta.32": "2017-11-12T13:33:21.867Z", "7.0.0-beta.33": "2017-12-01T14:28:25.187Z", "7.0.0-beta.34": "2017-12-02T14:39:26.575Z", "7.0.0-beta.35": "2017-12-14T21:47:51.812Z", "7.0.0-beta.36": "2017-12-25T19:04:44.368Z", "7.0.0-beta.37": "2018-01-08T16:02:34.781Z", "7.0.0-beta.38": "2018-01-17T16:31:59.869Z", "7.0.0-beta.39": "2018-01-30T20:27:36.885Z", "7.0.0-beta.40": "2018-02-12T16:41:41.666Z", "7.0.0-beta.41": "2018-03-14T16:26:16.825Z", "7.0.0-beta.42": "2018-03-15T20:50:49.459Z", "7.0.0-beta.43": "2018-04-02T16:48:29.492Z", "7.0.0-beta.44": "2018-04-02T22:20:10.350Z", "7.0.0-beta.45": "2018-04-23T01:57:05.319Z", "7.0.0-beta.46": "2018-04-23T04:31:24.385Z", "7.0.0-beta.47": "2018-05-15T00:09:12.784Z", "7.0.0-beta.48": "2018-05-24T19:22:48.089Z", "7.0.0-beta.49": "2018-05-25T16:02:26.784Z", "7.0.0-beta.50": "2018-06-12T19:47:21.542Z", "7.0.0-beta.51": "2018-06-12T21:19:56.840Z", "7.0.0-beta.52": "2018-07-06T00:59:28.256Z", "7.0.0-beta.53": "2018-07-11T13:40:18.258Z", "7.0.0-beta.54": "2018-07-16T18:00:09.341Z", "7.0.0-beta.55": "2018-07-28T22:07:22.355Z", "7.0.0-beta.56": "2018-08-04T01:06:06.266Z", "7.0.0-rc.0": "2018-08-09T15:58:32.680Z", "7.0.0-rc.1": "2018-08-09T20:08:10.400Z", "7.0.0-rc.2": "2018-08-21T19:24:14.861Z", "7.0.0-rc.3": "2018-08-24T18:08:11.406Z", "7.0.0-rc.4": "2018-08-27T16:44:26.856Z", "7.0.0": "2018-08-27T21:43:21.988Z", "7.2.0": "2018-12-03T19:01:47.639Z", "7.7.4": "2019-11-22T23:32:21.780Z", "7.8.0": "2020-01-12T00:16:37.820Z", "7.8.3": "2020-01-13T21:41:38.942Z", "7.10.1": "2020-05-27T22:07:32.719Z", "7.10.4": "2020-06-30T13:12:09.327Z", "7.12.1": "2020-10-15T22:40:27.887Z", "7.12.13": "2021-02-03T01:11:07.108Z", "7.14.5": "2021-06-09T23:12:13.179Z", "7.16.0": "2021-10-29T23:47:33.731Z", "7.16.5": "2021-12-13T22:28:29.029Z", "7.16.7": "2021-12-31T00:22:08.893Z", "7.17.12": "2022-05-16T19:33:01.875Z", "7.18.6": "2022-06-27T19:50:03.681Z", "7.21.4-esm": "2023-04-04T14:09:29.499Z", "7.21.4-esm.1": "2023-04-04T14:21:21.953Z", "7.21.4-esm.2": "2023-04-04T14:39:23.123Z", "7.21.4-esm.3": "2023-04-04T14:56:11.635Z", "7.21.4-esm.4": "2023-04-04T15:13:23.824Z", "7.22.5": "2023-06-08T18:21:21.830Z", "8.0.0-alpha.0": "2023-07-20T13:59:57.634Z", "8.0.0-alpha.1": "2023-07-24T17:52:02.535Z", "8.0.0-alpha.2": "2023-08-09T15:14:57.632Z", "8.0.0-alpha.3": "2023-09-26T14:56:55.319Z", "8.0.0-alpha.4": "2023-10-12T02:06:20.736Z", "7.23.3": "2023-11-09T07:03:59.990Z", "8.0.0-alpha.5": "2023-12-11T15:18:54.914Z", "8.0.0-alpha.6": "2024-01-26T16:14:06.912Z", "8.0.0-alpha.7": "2024-02-28T14:04:57.408Z", "7.24.1": "2024-03-19T09:49:00.951Z", "8.0.0-alpha.8": "2024-04-04T13:19:52.486Z", "7.24.6": "2024-05-24T12:24:35.024Z", "8.0.0-alpha.9": "2024-06-03T14:04:19.874Z", "8.0.0-alpha.10": "2024-06-04T11:20:09.243Z", "7.24.7": "2024-06-05T13:15:24.651Z", "8.0.0-alpha.11": "2024-06-07T09:15:35.167Z", "8.0.0-alpha.12": "2024-07-26T17:33:31.707Z", "7.25.7": "2024-10-02T15:14:52.763Z", "7.25.9": "2024-10-22T15:21:11.646Z", "8.0.0-alpha.13": "2024-10-25T13:54:13.427Z", "8.0.0-alpha.14": "2024-12-06T16:54:05.989Z", "8.0.0-alpha.15": "2025-01-10T17:24:30.951Z", "8.0.0-alpha.16": "2025-02-14T11:59:07.180Z", "8.0.0-alpha.17": "2025-03-11T18:24:59.188Z", "7.27.1": "2025-04-30T15:08:56.506Z", "8.0.0-beta.0": "2025-05-30T15:51:08.498Z", "8.0.0-beta.1": "2025-07-02T09:04:20.258Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-reserved-words", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-reserved-words"}, "description": "Ensure that no reserved words are used.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}