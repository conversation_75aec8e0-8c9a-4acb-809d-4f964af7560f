{"_id": "istanbul-lib-instrument", "_rev": "109-7c8cb2f4f6b6dbfe3d4ef149022a46fc", "name": "istanbul-lib-instrument", "description": "Core istanbul API for JS code coverage", "dist-tags": {"latest": "6.0.3"}, "versions": {"1.0.0-alpha.0": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "39ac08fd46567ae9115225acf993550bcf590595", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.0.tgz", "integrity": "sha512-KJ6RgXjK7/gv/qclDCeX9IhnMniRYewzqjjdz5JA1x/FLbWmfrDvtOGaCuZe+0hF5Jf/5VAzHQgK8E2tPwau4Q==", "signatures": [{"sig": "MEYCIQC7PuoHiKr8zMxMwQP8YPmfwXD846XvuqM6H8qxoj1TZwIhAPqtmZUYvAbt5LANp5V5LBsXZ4s/8eE/i7rTVMEQ0j9K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "39ac08fd46567ae9115225acf993550bcf590595", "gitHead": "4f2da45d77e120900d366a12ecaa17d720cabac6", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**/*js' -x 'lib/vargen-browser.js' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"esprima": "^2.5.1", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.3.17", "coveralls": "^2.11.4"}}, "1.0.0-alpha.1": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "ba82089e7fc01f016436ad35d1b3145f9c579a9d", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.1.tgz", "integrity": "sha512-2eBCTsjMNw7dg5HQR/s0JkjtofrNdNpYr2PyyCqtMvM78XUiz6cIiRxUv7XeoaWDz5RldKXhwfPoPJzad3cVrA==", "signatures": [{"sig": "MEUCIQDJVj6wfhHBwDfqDjQCnPomr8uI5ODT0gxhH/LIlUXDfAIgVqG0kTs4d68V/1CJ8MRkC2dSqFgvmrueWgVtVgUqEwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ba82089e7fc01f016436ad35d1b3145f9c579a9d", "gitHead": "ecc3e3222cf5e8b6c14ffd02320d8df811406497", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**/*js' -x 'lib/vargen-browser.js' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "**************:istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Core istanbul API for JS code coverage", "directories": {}, "dependencies": {"esprima": "^2.7", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.0.0-alpha.2": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.2", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "694b99ed51ac8df112b109500f1ec83dce6a2d9b", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.2.tgz", "integrity": "sha512-iDO6j5oI2ilYjVKue3TFbVOokufyXkIQ2dhl1tCvKi3Zdm0MM+IITC8hOlfpSM2WzmImbaBvuq1DlOOrw+CjNQ==", "signatures": [{"sig": "MEQCIAr8DynJT2MR6XnHLKQU84mAqaE/BJ9RkdPYcdrNKQILAiBlklbFMLtKa+WnzfPH8gwpVfDRWJn12cYuGySLYWEecQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "694b99ed51ac8df112b109500f1ec83dce6a2d9b", "gitHead": "bde3f06782b6fa20ee23664784052dcdb53d92e4", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**/*js' -x 'lib/vargen-browser.js' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "**************:istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Core istanbul API for JS code coverage", "directories": {}, "dependencies": {"esprima": "^2.7", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.0.0-alpha.3": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.3", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "dcc2d683c8403e1f0668b7033ce2e073963b3e05", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.3.tgz", "integrity": "sha512-XNExnL2LCgNhqARipA7U7eOr33Ttl7RN3JCv3pFpbGDSYdi/h0ydK4x3K9t9VY4Y6Dziu9ononzt4I7ivco43A==", "signatures": [{"sig": "MEYCIQDWu6uZdkY4StebU/WLkEfh+peVWcwWb8sR/s/2pnIn0gIhALDlfAYGA7H+4T/gU8G53Vq4DxfKpjjB16remGXeIqK8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dcc2d683c8403e1f0668b7033ce2e073963b3e05", "gitHead": "6c082baef893a13a0d2fa10401e8b8bd8b34ad4d", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**/*js' -x 'lib/vargen-browser.js' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"esprima": "^2.7", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.0.0-alpha.4": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.4", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.4", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "d1e0fc951e89f09bf0105efc1038481ec96d3bb0", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.4.tgz", "integrity": "sha512-ytCsNcV4S73uBTxLjrQrFxzlW1kYVhKog7OhryQ7bN3UmjQZ9dVJrygQL2jyUTO9VNO4YPBGOTdxNfTeG+oxXQ==", "signatures": [{"sig": "MEUCIQC5g4a04/P732N6AIaX8+SG954GnSywxlyXMkabAnXtnAIgVD6PH5LAttNZiLZSA4B2JdS0kA5OyA2kjNiHwcz6e40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d1e0fc951e89f09bf0105efc1038481ec96d3bb0", "gitHead": "c8dc1072ab6896eeb0ac48441fc3f17ecb898ee2", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**' -x 'lib/vargen-browser.js' -x 'docs/**' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"esprima": "^2.7", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.0.0-alpha.5": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.5", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.5", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "77b72a059a78b4e389c564f1eb195c6ac2b7f731", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.5.tgz", "integrity": "sha512-IoQ+Wyp1Db50TNjPN30DDq+NkX97lwCqTMSTt8RbA8gqOZd1qKcaWJpaJRKwzF6/kWyovHBGfXiQS2xOlBt2ow==", "signatures": [{"sig": "MEUCID81Kpk2ngkLSV4pJnNleML1Q5NCmWopEa1ZpN2UBjQoAiEA9cOUdPgddnpkccroEyxjeuDCMSWQjhU+Tr/xoyKdsO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "77b72a059a78b4e389c564f1eb195c6ac2b7f731", "gitHead": "97a67cb5305aa1e45063c7f70646f4112ba70c7b", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**' -x 'lib/vargen-browser.js' -x 'docs/**' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"esprima": "^2.7", "escodegen": "^1.7.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.0.0-alpha.6": {"name": "istanbul-lib-instrument", "version": "1.0.0-alpha.6", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.0.0-alpha.6", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "5529ca5534d6a98fe038c3c3de7a97ed130df385", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.0.0-alpha.6.tgz", "integrity": "sha512-mKoVWpds4cwh1pNw7WrQy/mkjev3JQTeLdX7uXOJgyDlJcPRmey9EYz2ThveoKW88AaLjjibdpj0+ZcyjOBxfQ==", "signatures": [{"sig": "MEQCIELvitlhYvYJJvLYRNXfyNaJ46uKqsN/f6KIqB88Wwf/AiBE5C/9RtT0cGI8TeYHuGP35Wj6Qh5PggAuCyNzRdOxhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5529ca5534d6a98fe038c3c3de7a97ed130df385", "gitHead": "8347187479c7410fc7224f808d0e0dd7ed6d6fa9", "scripts": {"fast": "mocha --harmony -r ./support/yaml-loader --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover --include-all-sources --print=both -x karma.conf.js -x 'support/**' -x 'lib/vargen-browser.js' -x 'docs/**' _mocha -- -r ./support/yaml-loader --recursive test/", "pretest": "jshint index.js lib/ test/", "xposttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "aliasify": {"aliases": {"./vargen": "./lib/vargen-browser.js"}}, "karmaDeps": {"karma": "^0.13.10", "aliasify": "^1.7.2", "phantomjs": "^1.9.17", "karma-mocha": "^0.2.0", "karma-coverage": "^0.4.2", "karma-browserify": "^4.2.1", "browserify-istanbul": "^0.2.1", "karma-chrome-launcher": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.5.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"esprima": "^2.7", "escodegen": "^1.8.0", "istanbul-lib-coverage": "^1.0.0-alpha"}, "devDependencies": {"chai": "^3.0.0", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^0.4", "coveralls": "^2.11.4"}}, "1.1.0-alpha.0": {"name": "istanbul-lib-instrument", "version": "1.1.0-alpha.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0-alpha.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "c46ac036266b9f7f2467c11c4e302dba0ef87bf1", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0-alpha.0.tgz", "integrity": "sha512-SjfixdfZrgK03uM7OO3hVRTAnSh1PyZkkxTtrKopQSTNJSN2l2w1IaFhc2PF9KS3/Zs5pXKtX/YADhWq82rB3Q==", "signatures": [{"sig": "MEQCICeNgvQjFdRUIjTmjk1zIDw79R16GI8N1rdvfkw/KRVVAiAcarLdcsh9MnL6tjF1cG+UY/phc1Yy1mdR04OfNC+pMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "c46ac036266b9f7f2467c11c4e302dba0ef87bf1", "gitHead": "43f1d4efc91c67df008ac869a858a31a9114539d", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "node --harmony ./node_modules/istanbul/lib/cli.js cover ./node_modules/.bin/_mocha -- --compilers js:babel-core/register --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 95 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.10.2", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0-alpha.0.tgz_1466819698668_0.7192352113779634", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0-alpha.1": {"name": "istanbul-lib-instrument", "version": "1.1.0-alpha.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0-alpha.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "29d379e5a70a95b588e5a9df9a9704c34fb0c7fd", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0-alpha.1.tgz", "integrity": "sha512-fE3YXReNHH7wJ9XjUSNTknNlTCdhGoHOlP3XptOTPEnPpzB5dwvl01VyqaYTytkGriMzhwR3gwIZoGoU18hiKQ==", "signatures": [{"sig": "MEUCIQCyoUegun0b1diPgqT3uAp+YnpYQeXz9yC3Nu9cC05C5wIgPDFTMg8//se/qToECKfpAfBeCqwi/7IG/kg67sl0fnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "29d379e5a70a95b588e5a9df9a9704c34fb0c7fd", "gitHead": "44b789b2923f85ea1ce8ee50495e77ed3566d34f", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.10.2", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0-alpha.1.tgz_1466823011716_0.44960094429552555", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0-alpha.2": {"name": "istanbul-lib-instrument", "version": "1.1.0-alpha.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0-alpha.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "ce7cdc6d3ae35fd4baf10a25c06b461eecf1a815", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0-alpha.2.tgz", "integrity": "sha512-T+a+s+l682SisTH08F8L+7L7E/SokpehiEoLWbsXskGoAH/Ip6EjlKhf9gh/oAt/I3lllFX4z8HkpztFFQPvoQ==", "signatures": [{"sig": "MEUCIFRbexugpNugcqkbtzgnv7hv4Y5QkJ+1I0iK/W9y7A/PAiEAwsZnvV8MVXY2n8RRe13Ak3D8SvGribK4tgqma7XC1kQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "ce7cdc6d3ae35fd4baf10a25c06b461eecf1a815", "gitHead": "27a0495a3f2d1b627fb0b8955534d483638a68c0", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.10.2", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0-alpha.2.tgz_1467312827034_0.429014551686123", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0-alpha.3": {"name": "istanbul-lib-instrument", "version": "1.1.0-alpha.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0-alpha.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "5f5582f5aabdc7d6dce8d334af1422ebc8fa86a3", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0-alpha.3.tgz", "integrity": "sha512-vKGAg4/oQr02AFNZ/+isssGEbuO6a1EZgIgXBGtK8nLbIRzNvEjlyeXnK+Bwkz/vaaEJ5Eloyw6JQNBNtYr8xg==", "signatures": [{"sig": "MEQCIH85xNvfgjQxjpismsWAax/7PDOgO3/HSBAeIkSF6YHWAiBgfuTR+Lq4jtYTvjZJlPlV0/7P3nUZwdx3g4qFsjuW0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "5f5582f5aabdc7d6dce8d334af1422ebc8fa86a3", "gitHead": "489d95f2e3bc62ad270d18d350c997068f5c6e53", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.10.2", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0-alpha.3.tgz_1468560869083_0.1210481496527791", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0-alpha.4": {"name": "istanbul-lib-instrument", "version": "1.1.0-alpha.4", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0-alpha.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "77d9b113e9f761aa84988339013a7203acc98adc", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0-alpha.4.tgz", "integrity": "sha512-ELSSawLteIr4UZF4y3T5AmJnS10kkL1PHDLFMk3PQg9UKT8sQsg9K71S8mk6eBb0QoTbHhu9Cd+2lTRXkND/ug==", "signatures": [{"sig": "MEUCIQDZzsOOppGTQBr3lfbwhzVLuMOdwRso3ihnGWZTZBbBQQIgXB5wyZUXOPx56NLPBHfUrUespjwRwdzRMA8beMdatss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "77d9b113e9f761aa84988339013a7203acc98adc", "gitHead": "1e181bb8c06ba58c4b5902429895bdfe485fab83", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "standard-version": "^2.4.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0-alpha.4.tgz_1468991356591_0.23247007676400244", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "istanbul-lib-instrument", "version": "1.1.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "97e947402670f83ae8f6d6657c8648d9ee175e35", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.0.tgz", "integrity": "sha512-nODtgsqKecqa6fdRrVbvox/6kG0Vao3ubSk+fwwgejRG1XAvqBRtSisNk61/mOrpQekZbGRkJOwlxwxuXNeN5Q==", "signatures": [{"sig": "MEUCIQCpg+kpm2rR3VxCpf4lpruRUThXQzUWs4NSaJx0IhbEnQIgN7TA1Mhb+z02YDqHFcseOcYV5TI8/9hWnM2iBrdiixM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "97e947402670f83ae8f6d6657c8648d9ee175e35", "gitHead": "add579687c2f617d346a3a7618601ba6d73c83df", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0-alpha.4"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "standard-version": "^2.4.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.0.tgz_1470948175778_0.6004537651315331", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "istanbul-lib-instrument", "version": "1.1.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "5fdc53051d4152939d8824fd8f272d31a552a129", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.1.tgz", "integrity": "sha512-MA3xlVp0bu4Gqz5Qbo9bgx5e3sWqgxEPogR5oiWyGKNd0dAXXh3g7lNOrlKFG5iUC9hQ5UAJK+oeN8MZBsgfeg==", "signatures": [{"sig": "MEYCIQCxeKqfOYSEoroIed8kTSZ6h+t0mQiAd1dn/NjhUiov7gIhAMSXvZjeqz/JU4Co2WOM+T1prcVSRpUBCQUXh5VBKoOd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "5fdc53051d4152939d8824fd8f272d31a552a129", "gitHead": "71134b6e4e56bf381dbe4e2ad64c2da764396869", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "standard-version": "^2.4.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.1.tgz_1472575947790_0.3833767755422741", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "istanbul-lib-instrument", "version": "1.1.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "55f5d0ee4e3b8d0aa0c9f93390b330e07fbb7a22", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.2.tgz", "integrity": "sha512-hHgSU3w2WUbQUa8Dm95UD58LVWPzwRXEVlOaIchGhJOguJwpXIulYgtZmVNVbTWBDA48LCH1Z4DDI+sFJpOyvw==", "signatures": [{"sig": "MEQCIGilkZj8V6RCeANmRmxWa7T+ssGHDyG8sn1VcfSRxUo6AiBegnhMfT9HBM6pOLAmJsPCUbNXsIvjcKolWMKK38U2/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "55f5d0ee4e3b8d0aa0c9f93390b330e07fbb7a22", "gitHead": "0b20bccfac1b1fbfbf2561f2a1e7e9261c4c1499", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "standard-version": "^2.4.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.2.tgz_1473302979953_0.1376356934197247", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.3": {"name": "istanbul-lib-instrument", "version": "1.1.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "66d5353d1f592b9e34d1cf9acda9c3f1ab509696", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.3.tgz", "integrity": "sha512-uCKZtyix/jfana7SG4ecQ2Oi9fV+63RUi/2ohQFRb3sCN3jFeYF89ybfObgwfSKIGWvgwy6MyvaEKuw5ZcELkA==", "signatures": [{"sig": "MEYCIQDSKv4bZ6O+J2QSzZ/mbD6tTEpuhF3a8g9zOIEXMZf2cQIhAL9NKUHCMOl5F8ABfttKhdNxHq0aUWb2Mjcd6/OtQNHS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "66d5353d1f592b9e34d1cf9acda9c3f1ab509696", "gitHead": "37a0087e68729dae46989c3687272d154938fa9e", "scripts": {"fast": "mocha --harmony --compilers js:babel-core/register --recursive test/", "test": "./test.sh", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "posttest": "node ./node_modules/istanbul/lib/cli.js check-coverage --statements 90 --branches 80", "v10-test": "mocha --compilers js:babel-core/register --recursive test/", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^1.0.2", "mocha": "^2.2.5", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "babel-core": "^6.3.21", "documentation": "^4.0.0-beta5", "standard-version": "^2.4.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.3.tgz_1473744377368_0.23582313884980977", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.4": {"name": "istanbul-lib-instrument", "version": "1.1.4", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.1.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "95be56e5c321456ffdfddc925ad4dcfc28edab9c", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.1.4.tgz", "integrity": "sha512-NXa8DC9Eai/W15qwsiNwbIhrc0/PGMEsioglcCB1Kidsn2PWrX4x4qmJYtEtyefB6/RyTMnt/GnCJRDXzVQkjg==", "signatures": [{"sig": "MEUCICpJJMfnBMQNVZOeZ3uEexoerNRiNyhiV7Pi8yqUT0UBAiEAkWp3oKiBR4Z0OJHHtubHcmnu9mzGv0FQxS8OQzDEq1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "95be56e5c321456ffdfddc925ad4dcfc28edab9c", "gitHead": "d3d9e0563b6b0837b4c74ecc08f5c81ad1e6e294", "scripts": {"test": "NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src/*js", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"babylon": "^6.8.1", "babel-types": "^6.10.2", "babel-template": "^6.9.0", "babel-traverse": "^6.9.0", "babel-generator": "^6.11.3", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.1.4.tgz_1476685317444_0.5757211898453534", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.0": {"name": "istanbul-lib-instrument", "version": "1.2.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "73d5d108ab7568c373fdcb7d01c1d42d565bc8c4", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.2.0.tgz", "integrity": "sha512-9vMmXrfeJMU7vMH0jLyWuxhwgSjCa0p7C/tKdiiEpNnbohVpNauTHQ39nxV6OGe3Drttw5d9b/ffAN5oVR4ZbA==", "signatures": [{"sig": "MEQCIEkeoIVNQPX9xxojB6PTvLeSZgttQHO9eNHWtdCgsxDcAiAM21svrzE26Wx9ubOmM5O1zGKX8vV0W2xOuNX/6sL6QQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "73d5d108ab7568c373fdcb7d01c1d42d565bc8c4", "gitHead": "8e5e1324535a4ddb7071bd4a495416d3ddafa04e", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.2.0.tgz_1477372272382_0.24113669083453715", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.0": {"name": "istanbul-lib-instrument", "version": "1.3.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.3.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "19f0a973397454989b98330333063a5b56df0e58", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.3.0.tgz", "integrity": "sha512-i98SQdU2M+6IBJNds7XsOp9O8fTuE+eiMuqJeRjvcxGTMSlKXtKjXPCC0bXljQVNqBC4N3b4r75CNBdc5uW6Fg==", "signatures": [{"sig": "MEQCIAkuq1nfFHCxTkVVs4vdEgV+3j9HkWZKmIBU+/DEiPa3AiBt0llQ/TNx9RWUPd82nrX+FIabSBOJqTk7zdXEEy1N4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "19f0a973397454989b98330333063a5b56df0e58", "gitHead": "52b94d242229c4d5760442853f4aa86f4f49d74b", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.3.0.tgz_1478757368335_0.7665289628785104", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.1": {"name": "istanbul-lib-instrument", "version": "1.3.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.3.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "112c25a4f2f9bc361d13d14bbff992331b974e52", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.3.1.tgz", "integrity": "sha512-2zGMzTT9z5ZzqzqdsctbG0vvXquVOtyVTyvpKrGxuiEXuqQZ0oPRFV+hrGzfQl4XZmvGdZH/lgh4G8OCP1PV7A==", "signatures": [{"sig": "MEQCID6pfXPn+7FXrR37QF1ljdfsSNSD2F+6pqkiHvXphCrSAiBfJBjKK9iRsciYFOkj1Nr0+xWDX3J+QwDrQzgOVuBCAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "112c25a4f2f9bc361d13d14bbff992331b974e52", "gitHead": "590abf6730a22e718c6ea211b7048e715664ae66", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.3.1.tgz_1482813607969_0.135913249803707", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0-candidate.0": {"name": "istanbul-lib-instrument", "version": "1.4.0-candidate.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.4.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "4201eb8603cbd5a406259abe598adef1d7da2dd9", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.4.0-candidate.0.tgz", "integrity": "sha512-AV4UbRNVffRuMdVJ2k3o4JEWtxHT/8gm/Aqe1yaAn9OfpsZA3LEGZ6ehdyYC3FJ8CRKoD2IyFL/dAlebfc9oFg==", "signatures": [{"sig": "MEYCIQDhObyDRZI8uUQGBqsgDHqDsyoirz+GGY5VJrSKKDNSZAIhAI8S6UakwIjgNYgSmU6eDZpEEvDUU2Xy+o726tWPEmPL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "4201eb8603cbd5a406259abe598adef1d7da2dd9", "gitHead": "a7286b09241815eee6dbd40cb147f546c3a971f8", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.4.0-candidate.0.tgz_1483392264880_0.6696562620345503", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "istanbul-lib-instrument", "version": "1.4.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.4.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "33da8df669da74e532ba409e0397db0dc56be093", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.4.0.tgz", "integrity": "sha512-rOlH0aBWjvOQGST+cfV7rje/SiEMNTARogbdXK76QlQTdNP9duBIPPQY96NanUYAguW7DXfqQaw3hCPUyNJ3Mw==", "signatures": [{"sig": "MEQCIFYElXpTk2QvRhSMOs+uQeXET+XfZf+E+6/WZpqqzP1sAiB/jn4tqcAa2SrI7rJH6xvkO9dK7VCmqTiTHnTkZ+lg3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "33da8df669da74e532ba409e0397db0dc56be093", "gitHead": "a7286b09241815eee6dbd40cb147f546c3a971f8", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.4.0.tgz_1483494027335_0.1665284694172442", "host": "packages-18-east.internal.npmjs.com"}}, "1.4.1": {"name": "istanbul-lib-instrument", "version": "1.4.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.4.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "9b98c18e327198d24c0bbbf9091fb988aff2d976", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.4.1.tgz", "integrity": "sha512-tV3cc0TWB23cOKkSDvkpj+XxA6np+HgSzIcliVrRymXnKKnCEsEIhJeqYTLXbsqzbiAm2AwXfqd2EXSHwzdIWA==", "signatures": [{"sig": "MEQCIFf+2q0jQO7PrdJ6rh2mtv22MLGOiHe3ip5XKvSB8vPfAiBwam7UgD9X2mNa5Oe5KiRskh+eglTuG4C1Gfv5znSFaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "9b98c18e327198d24c0bbbf9091fb988aff2d976", "gitHead": "4fe4dca0827c02c411401fececcb7c947cdb3c29", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.4.1.tgz_1483519960661_0.5415776162408292", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.2": {"name": "istanbul-lib-instrument", "version": "1.4.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.4.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "nyc": {"include": ["src/**/*.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "0e2fdfac93c1dabf2e31578637dc78a19089f43e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.4.2.tgz", "integrity": "sha512-fMC2pFmXWpyDD38oxA4YMgvwwFB9+viqzVzMdnkEH0bivS994UTrAOrEsTnQWlCCOxHlD2FSb9pFJ/Kh64C5Sw==", "signatures": [{"sig": "MEUCIQCcOFhGPyD+HoJ4F2PgZFcdMZ9mf2RPEIb1t7z6v3KoAgIgA6ZGuuajPC/KxZy+Izxg53wmXPiT6gUJX6wMNWGEw48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "0e2fdfac93c1dabf2e31578637dc78a19089f43e", "gitHead": "a2e013d72e3216bc460816ba9404be02ec38a2d5", "scripts": {"test": "cross-env NODE_ENV=test nyc --check-coverage --statements 90 --branches 80 mocha --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"nyc": "^8.3.1", "chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.4.2.tgz_1483525376896_0.3232681602239609", "host": "packages-18-east.internal.npmjs.com"}}, "1.5.0": {"name": "istanbul-lib-instrument", "version": "1.5.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.5.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "0f844fd5d729f2bcb0eed5329273c581ca9a8c74", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.5.0.tgz", "integrity": "sha512-cYCmFAsegcwiyrfLWGCKR4nKVt4JxXpIOnb4g1eiYCJB16uueN25V3KsHjbLvDUDmHPv8gkK/AEN/JEiLWOIrg==", "signatures": [{"sig": "MEYCIQCu4miU/Y0mAoJ2Jhqed6fOYiajKnbkc/XxnqT/38kMjAIhAJiKSNxKigCmdnzZzKqNtgGRYFxMG8Q+oT5qePZdukbx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "0f844fd5d729f2bcb0eed5329273c581ca9a8c74", "scripts": {"test": "mocha --require=babel-register --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.9.5", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.5.0.tgz_1489982006539_0.4532756498083472", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.0": {"name": "istanbul-lib-instrument", "version": "1.6.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.6.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "0c781880ae6ec63c9f817eaa1406359e614d754b", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.6.0.tgz", "integrity": "sha512-bsqDfUFfgxDUKBPgvC6BU82WVA51Uc6/5fRxC4IZI4RgEfGPxCaGCobUJnrmUO0kgSaLWfdPqEP085LiUIuqxQ==", "signatures": [{"sig": "MEQCID3Jt+TdV15jGu/BdY98jT8sGTLJisSjilNPjMkfMNjfAiAHSbhOXXNjQKx6+MYLnvVwCAEz6iYovwoQ8jtmSEz6Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "0c781880ae6ec63c9f817eaa1406359e614d754b", "scripts": {"test": "mocha --require=babel-register --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.6.0.tgz_1490076920912_0.14042604132555425", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.1": {"name": "istanbul-lib-instrument", "version": "1.6.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.6.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "6c9c3191ebd5aa856d66dc2f0b2f719c3732de2d", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.6.1.tgz", "integrity": "sha512-K0BJsBZhsg7KHM3vJH3qVxD8wLhnErMzebBZE1AHeuAH4kmb5zVhT9RV3j8GLuINi/EBn3cVIU2KjPAkZ99Z9Q==", "signatures": [{"sig": "MEUCIQC063b+4nT6386DnuQNoNyeqEcFwDJBnOq0DKFrmtiyYAIgBwtM2jjp++1LGEDOsTM8JzpZgDRA+f+SCEiZ/8RS4gg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "6c9c3191ebd5aa856d66dc2f0b2f719c3732de2d", "scripts": {"test": "mocha --require=babel-register --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.6.1.tgz_1490077608207_0.6907793860882521", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.2": {"name": "istanbul-lib-instrument", "version": "1.6.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.6.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "dac644f358f51efd6113536d7070959a0111f73b", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.6.2.tgz", "integrity": "sha512-o/TuEa/chJN+tthhGSfKDR1okr9eulBMBB3kAVitCwJhguoNnwGLeVrOI9cyOyiigzu1VkJk7JSPUrBVw1SFeQ==", "signatures": [{"sig": "MEYCIQCC2++OaVNhpVZxAei6sMZDOS7XPIFCiANop4IOu0HkIgIhAI2T8YC+a0xwacan3vXOFUb2i/t+FVS2mBPdLHUngGRu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "dac644f358f51efd6113536d7070959a0111f73b", "scripts": {"test": "mocha --require=babel-register --recursive test/", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.6.2.tgz_1490198154434_0.3805013168603182", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.0": {"name": "istanbul-lib-instrument", "version": "1.7.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "b8e0dc25709bb44e17336ab47b7bb5c97c23f659", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.0.tgz", "integrity": "sha512-Ogi5JTHUBAlPj+kieBhhNiurPYOA8Cn2JO7K/ZS/IxlaZ0Y5k63+7JSvMkdOOt7etB+lMWx7H+pB+0l8N667XQ==", "signatures": [{"sig": "MEUCIDSpPmlhi/xi4S8SQDW5I8vpJJiY/I3xx/JYVdci0l03AiEA2TSuaFVG/qqlsuB9CBlhuDzMyIcXAKIzL2BsjP1Cu58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "_shasum": "b8e0dc25709bb44e17336ab47b7bb5c97c23f659", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "4.4.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "6.9.5", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.0.2"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.0.tgz_1490593890778_0.44251083582639694", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.1": {"name": "istanbul-lib-instrument", "version": "1.7.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-instrument", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-instrument/issues"}, "dist": {"shasum": "169e31bc62c778851a99439dd99c3cc12184d360", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.1.tgz", "integrity": "sha512-8zE3fAO61bB9STpd4MnbgsX9d2IVl4GyrtmwLqacXylBkRbQ+msfo5o2bFQ1t30mUgkKTAV6yJqoKIeDZCOc2Q==", "signatures": [{"sig": "MEQCICq9mYHisZK56aKd6Q08G/hjTVRTl8vw8Z5U28zjdMoeAiAYcQ5oIL4LbPMjcHK0IT5YeYHtGEGmloZm406VU4wNSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "169e31bc62c778851a99439dd99c3cc12184d360", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-instrument.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.0"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.1.tgz_1493442014505_0.4258935444522649", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.2": {"name": "istanbul-lib-instrument", "version": "1.7.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "6014b03d3470fb77638d5802508c255c06312e56", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.2.tgz", "integrity": "sha512-lPgUY+Pa5dlq2/l0qs1PJZ54QPSfo+s4+UZdkb2d0hbOyrEIAbUJphBLFjEyXBdeCONgGRADFzs3ojfFtmuwFA==", "signatures": [{"sig": "MEUCIQDU+OrBfVcmCsZ52SOubotTWfnez3KUjo+Ib1+D+pyubgIgbQQ/iTj5iVoZLO14ciufzBihf3sN0kQaHrjdzSL6Yv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.13.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.2.tgz_1495919586263_0.20151937776245177", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "istanbul-lib-instrument", "version": "1.7.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "925b239163eabdd68cc4048f52c2fa4f899ecfa7", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.3.tgz", "integrity": "sha512-rQVlbABImYqm3cFwLWIyGBkKdvrfQ5WO+nyQUZNqPJBeqTBbTZQAttSg4i1GVxwqbvQSxbWC3llnLNeeG5lmVA==", "signatures": [{"sig": "MEUCIBH/H0EDaj/M3FH1ymrub5FN5W/XR11fsah7Gt4q95l8AiEA9sbLD40gbsTY2BQspqjqLoQ0PyXYiwyJY6+pzwX5Zbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "925b239163eabdd68cc4048f52c2fa4f899ecfa7", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.17.4", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.3.tgz_1498415120166_0.31577161117456853", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "istanbul-lib-instrument", "version": "1.7.4", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "e9fd920e4767f3d19edc765e2d6b3f5ccbd0eea8", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.4.tgz", "integrity": "sha512-ljfKSDkoexJ6XyHYpGgDGJUNo/GvBQN9lT76FeP6ZAF5M9Zlwux6IHyFVoUcQfT01oLzGl0sy0SSpNhyNY2d7Q==", "signatures": [{"sig": "MEYCIQCi00g76sW8WXgSwY810iXGmKazzXCsMfkMkxOQSy8s9AIhAPzQdG7zRcodgAmD3Cyorgnl59QP00FmAZ+tjJP0v97T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "e9fd920e4767f3d19edc765e2d6b3f5ccbd0eea8", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.17.4", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.4.tgz_1500231510457_0.49160993401892483", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "istanbul-lib-instrument", "version": "1.7.5", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.7.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "adb596f8f0cb8b95e739206351a38a586af21b1e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.7.5.tgz", "integrity": "sha512-oywCqjdNTI+motONFXWfOqdtsEb7v/8Jt2MaG43z7RmBDfgyuBveDBaltDo4vRx6CT8P4xwOjGcKwX3wfO8f1Q==", "signatures": [{"sig": "MEUCIGXXJykgwmq5R0Tev8h3oSUa/kh0bzytke8ZXWuV6D1JAiEAp1TrmfVdMrRQsI5HjkDLrJqQK7jazIPoftWyg9bTxoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "adb596f8f0cb8b95e739206351a38a586af21b1e", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "4.4.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.17.4", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.7.5.tgz_1503511285096_0.8027196468319744", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "istanbul-lib-instrument", "version": "1.8.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.8.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "66f6c9421cc9ec4704f76f2db084ba9078a2b532", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.8.0.tgz", "integrity": "sha512-CWE/BOfNVWTcnDi4c9BxPvYW0lt+x/RvhIs4fk+A8QA2WOl0rbiO6dPZH957cmCkB5bTcE19OhDuJ23qjtIa5g==", "signatures": [{"sig": "MEUCICcgPU117et4ImaR2zTl5Pb2RnpA4FKnx01Whnb09GaqAiEAupkp19Q64Cx4SJKlrFnMbfOq/GB0iOt8wQnaVXSS9JI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["dist"], "_shasum": "66f6c9421cc9ec4704f76f2db084ba9078a2b532", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.8.0.tgz_1504571973593_0.017329889349639416", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "istanbul-lib-instrument", "version": "1.9.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.9.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "e5ffbbec250091ba15b3ae71b517d1200af3ba04", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.9.0.tgz", "integrity": "sha512-Nzka0ssUj8fTEUaywRFJRmNWS5ItVx/YG5STtlc+knGz2tZhYCW5THuXmA+qOsWiPb1i5aRHaEeSvZdQnq8iaQ==", "signatures": [{"sig": "MEYCIQD4/RSzmQVlEAfiPO0iBvzlZWeeO+zElz3Eo2zGDSNN0AIhAPIm18OwBYqBlJyzVIqXVi4BkIxbMNum8V7+NW8oPsqY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.9.0.tgz_1508612377014_0.1760429658461362", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "istanbul-lib-instrument", "version": "1.9.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.9.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "250b30b3531e5d3251299fdd64b0b2c9db6b558e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.9.1.tgz", "integrity": "sha512-RQmXeQ7sphar7k7O1wTNzVczF9igKpaeGQAG9qR2L+BS4DCJNTI9nytRmIVYevwO0bbq+2CXvJmYDuz0gMrywA==", "signatures": [{"sig": "MEYCIQDPpx+kDeBjtOOgg1oerJkm/vvpn2DRx1aH2D1Rmr9JKwIhAPuR3chy6LarVxoD6gvYRpsD3l3HxpNNEeNITNkp4xvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-es2015": "^6.3.13", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument-1.9.1.tgz_1508687628818_0.1513688329141587", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "istanbul-lib-instrument", "version": "1.9.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.9.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "84905bf47f7e0b401d6b840da7bad67086b4aab6", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.9.2.tgz", "fileCount": 10, "integrity": "sha512-nz8t4HQ2206a/3AXi+NHFWEa844DMpPsgbcUteJbt1j8LX1xg56H9rOMnhvcvVvPbW60qAIyrSk44H8ZDqaSSA==", "signatures": [{"sig": "MEQCIAZjdBx7ApDQctXmQMSiezo0zNlquVSeXLsHV1P3v3ETAiA0699n+rMV51jfyUVJ8sNJ55pFKRfrgPlIaYeprW+Z0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55268}, "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_1.9.2_1518500924296_0.2793845537804205", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "istanbul-lib-instrument", "version": "1.10.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.10.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "47f20bfed9b9cbbc45417d3c9aff37bfbacbd281", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.0.tgz", "fileCount": 10, "integrity": "sha512-xzY0udlRIKBgDzt9/uuO2xxDrOxFX6bf7u+ReAQvOjnLvoap2GAxjE2jSve9FclIyXOEZHgmC5LnN4uSAhvNKg==", "signatures": [{"sig": "MEQCIBEgUihAyAxzbOMlxSN0uh0hCE+nsG6gyGTqJx3TGyhiAiBS0QAs5gUpeTFIG9lQksb6Mkg+UCvZj+5eUG9/G3pdbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56666}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_1.10.0_1520188984086_0.3158129216002612", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "istanbul-lib-instrument", "version": "1.10.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.10.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "724b4b6caceba8692d3f1f9d0727e279c401af7b", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.1.tgz", "fileCount": 10, "integrity": "sha512-1dYuzkOCbuR5GRJqySuZdsmsNKPL3PTuyPevQfoCXJePT9C8y1ga75neU+Tuy9+yS3G/dgx8wgOmp2KLpgdoeQ==", "signatures": [{"sig": "MEUCIQDrZkyaOtYhgr3RLEwfE0QWjtDM80zdMfqBR2UPKGIE+AIgb8759VlbQI8OFeUqvSfMwnVERYgZpppVUyqPzXtU5hk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57103}, "main": "dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_1.10.1_1520633660547_0.4766695192459016", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "istanbul-lib-instrument", "version": "2.0.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "444cbea04b1a3890c5095fc5637f488aaa9568de", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-5WutqMh/Vy2KsIsoaQjmFSh85+AQnIvMgRSYLNpUUJLvL+2ees50OBFtDxZRN6Ru9GYpEbRiWd2ZfuUvA3lWHA==", "signatures": [{"sig": "MEUCIH+i2jECVV4dykX4tCfhHoX6xxvYXJ1WyvZxApr+U3YAAiEA+gMnDL+XWgPStMHhiszZESny48p5gjYTGoRW642Y0OM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD0PiCRA9TVsSAnZWagAA81AP/2orNFd3ASqHbCcz785w\niqybkxnOSO70aYd8nJjsszK10tkg0i3t2FAqMyfv1ggTxmyYCIwAMSU2p2H/\nCZnLsRXMeTmcIxjQmPUHxZ2S8LO3Z6AUB9m/Jm436vdr+iO8UkqpuyQT38Bo\nUDvtsO1aH2E3Rw+Rmcoa9bIpdQylSqkxDUV0pfEWbu0fZ57k3SQMUmsFDaCf\nKrvLS92yuuzxYSmJjJkUOWkTxvp/PfpUiKCLDB1MoTvvfRIeD+Ttv/k0gafJ\n9Zq/QyLNZj3JDG7JrKlw6ZUi+y0bORc2GZKE+laoyeAz8ncTIMZMnSnkqvQU\nD5u/ZabhfleB3ZlbQ4tzd7P5OKXyZLz5lnicgynPsWs1c3XeBt8G7hUswx6j\nlTzJKsV8XBLXsFf9d30AdvDmFnk5nb5XTg0XioygU9BbzgJF1aM6ZMxpTMnw\n3/9es921f7doLutIfC5HPVQyFnYeQVcTAchgonP1qc/0DLVDPfWN84lLM+wp\nO8YAM1iTf1y5RTx4SJWTy49UTDTkxxJIhP4q1wp+6rfE9PHoQcSSKqC5hFj+\n05ZIFoI2OBvaTTQzJTn9p60lUapIClX1bX+XQIP/BjhDFhC25TLSDilGaSNl\n/lVAl6r6UF0zOZGU+5h7KwfY4C5yLjeWlI0HjTZFwNHJ+L0xUO/CzDZsbSYn\n5TWo\r\n=z6ex\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"semver": "^5.3.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.48", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^3.0.1", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.0.0_1527727073122_0.29081599677215775", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "istanbul-lib-instrument", "version": "2.0.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "583766bafd99fd6c9af78d457409dd412727c134", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.0.1.tgz", "fileCount": 10, "integrity": "sha512-0qdWKyg/66dcjypwJs1F0M5ErIwmGolv7RIgS/EsM9LIB9xzHs2XdyuQ7+C4QazM+nPlshaFskxmWXiCOBbUTQ==", "signatures": [{"sig": "MEUCIQCdrMFVh32tc4DBFjLMWru1F6xQBzLQM/EsAl5yucQoVwIgZdGAFQTySpbA/gZbLb+jYZTZkXvNAfExJJpPEC+1jTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD0TzCRA9TVsSAnZWagAAe40P/374rd2bICuH6ye7evcV\n7oKib2WV7l+6lhLSo39m8P+O47G5TQjF/dIneFz4/l2Ey65Shy2YvyErlYSi\nJEQqIH3hREjAHZt6zacmLenxW6gkmtkRoiC492FITpmOkzXnEEqgq9wJ0fkm\nL4eWzFghYDM8fFZpt7tHH+TVxtg4WBiZ6D+HEOyoTq34A/cBzsFceRtmdzNz\ngtZ984NJ9gspOfeqBNhSES8p4YbOzag0PHj08jvDvOlyBU5hcUxnIpuLdPcI\nhjJJP0QWM6jmv5dFQugpNUFj+st/+YzqJX55+sTjrPGshjJX/PqEai9XaSiK\nKqw1Yx1gv5ZLDdGLxCXkFM+NyAn/nC5vagqvJ0iHVV5xHmFKqFNrbYzF7CDt\nPUyysIHJDpBoqkJZhwyKnEYKTR40u6NKMn8FM4Bvu0DuHUChKNQESRkBDejn\nT3FbNlEQFR2zs4rXfqL9j6mTkB7HbbnaJYNjDGXVzcG/vQCrBBUdD1hxS4y4\nHFAPUnjzzafxaCIzROTX3ahDC2cfCsjtB0IfX+xnaK2hkjx65tBuy7IWbvM7\noDokSnG7mUQcwJ3SCjbfWayOr1kjHeg+kU9Pe5OoyItuXdtFMbgt4KTdNzgN\nKtwAh27g2JbNN9HlOdqAyNUI8ki5DLQBrUbof9AWpdI5xEqZlbiOdS1ml/93\nQi39\r\n=7pHK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"semver": "^5.3.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.48", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^3.0.1", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.0.1_1527727346703_0.08286689067050901", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "istanbul-lib-instrument", "version": "2.0.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "b045fee45d831bf8432c4e0d1c7998a89d7b3649", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.0.2.tgz", "fileCount": 10, "integrity": "sha512-q8UerJp8KmgyJPJTChYlaxbnmqShIH8BZGYQrMJ/BbgKph1wKO3LI94MXBtJFlINWYrdSplr5tWPrMpQj6njnA==", "signatures": [{"sig": "MEQCIEYBJq/ANOXECDQa4E36nb0hzJLW6urfLbGktiRnPIkCAiAjTESc+gY/NgIlUbfzhxf15ofuNE9W03pj27Nryt/muA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD4j4CRA9TVsSAnZWagAAnRAP/Ri0UivY7mV8phh9Ggdx\nAj1Cv/BJZDT5j6BamY6uw0/f8Nqjij1EoTSXoO9/rLJDyCkoFK/9QxhWC6at\nVK4OJUYbVUuZjwG5psKTP6VHUkss5nnjDMoFysOvte0/q1+pj2NY/TXhvDgO\neT+yUM/fu4rHH8smpAL9+3faaLbCoG+ctYT1QuJ+RZLYilv7+Fak3Pc+yLTJ\nN/vTJ3xo2tCxDZK5L9TrOdXpL7IJZfOreaTNfCcoUbLmKfpeAle4Qz770A0S\ne2NoP0PmjKxKyYDLOpKYmCZVCMYCS1gyFqj0WchUKEGCJMLlpdoNZ4H9RYEO\nd0whlW4hAoa2G/hJdGTlTRz/HZzVvWZAt7IK+rAbwo2+an8/JtecFJFZ3nZr\nMW02SYCq5P+oFamZm0dq/6BbaUNQS2M7Ex2IG6Ep/GzFSeVnKS3q2OW2oun7\nIymgzuVCsqrdEypigcL1VfydB0CftJFHDUFMOvj2rCXPN3+9TN1luvOOcAEA\ne5B9attvVRklrgpOqYj/5L0Ro5mY1RIXZQFTcvucH1qBsfC9jfAifKvhbqXl\nG21JhSsjk+c/uVY9B0P/SqOmFhYWi+/nlpbnYhgnq8pP/cvxr9r1zay31k16\nbl95OnKQeeglVdYX2dr3mLZQE2njDclUn2VWxnxh0QmgQyPcsCQO0CCGHhiE\nd/yQ\r\n=GWQO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"semver": "^5.3.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^3.0.1", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.0.2_1527744759188_0.014275305387359882", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "istanbul-lib-instrument", "version": "2.1.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "76576f844cdbc4952a8b3904dae09d1eb10e6c4d", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.1.0.tgz", "fileCount": 10, "integrity": "sha512-3ly7GAJiPKqgbGKh2s01ysk3jd/egpE1i84PYu3BvPkssqrKMXZY9KRGX0mfZ+cmCfTR1IFVnnn/tDHxTer4nA==", "signatures": [{"sig": "MEUCIENF8muHxfIiTD4XBGaKcuT6SRXb0J7VPPcncZXeRTqaAiEAjmJy2EPXWn6kaDnqDQ3ZuNTI3zG7AnXt8lIQ5gakmKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEIL1CRA9TVsSAnZWagAALZYP/ApsZeMxBCZQtJYIcSDC\npjbmbLR12OBAr6z7W9bdeBGgtoLFZVfuE/RxTHe0ZbOQfEboIX9EgOSIks90\nvTQtNf1qkIWCIt3nIfivPLLmG0eb9a17NfTJbbThIjNpo2MOrYOA35L09Z4T\nuylfD6qgJ6GDTjwoB9I5/ryPmMuvAAS3vUmFVnuHm6pTTCpszIus8ITiuVKh\n5ZxtUN7sQrI8mdJtdXD+xKcTkch4UHLcXRZV3ATXjjwc0GUhPJ5tSesNHfMO\nZXVBzB8qBcRpM2QL0j3th7OWgPGUKVSNl3Df/EGU7rBvwK8tWUCLGhh76a29\ncPs8SJEXqiM9QbRzrvQ/d8LC0MUWXRkHdLwzp4a8jQkwiHrNVTf1KaxILwVV\npVCZv5pfnD91x9BDoZljyKnsdAOxRMp7bx/+4myufBQBM4lEttHz1Q+OrQgG\n/bx9ApN8n4JWXku+Rful6Q6l2pijVit6PmxeGi+uv95BSQ4uu0ua1sTMHDJ4\nj4KryM9si7qB6Irv1Nxrb52IhlN13H4VOovD0b8WbceTfhuifTHxVJt3UA4v\nj+eX0Zcl6VUXxsfjd0MdxVvH3hwL7txDJHiEVJ4j+DrWYdSonIx2Hy2eW/FO\niakUKcpFGBrRBwOPwvz8DXGS9odFh6Nl81I3UhfUsBB3sIax76y2FsAbeYZJ\nNDhu\r\n=K05D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0-next.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"semver": "^5.3.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^3.0.1", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.1.0_1527808756563_0.7778921243389441", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "istanbul-lib-instrument", "version": "2.2.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "08091ae49d0b330be2a839dc25c250324c4bedaa", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.2.0.tgz", "fileCount": 10, "integrity": "sha512-ozQGtlIw+/a/F3n6QwWiuuyRAPp64+g2GVsKYsIez0sgIEzkU5ZpL2uZ5pmAzbEJ82anlRaPlOQZzkRXspgJyg==", "signatures": [{"sig": "MEUCIHpgPcylQ4+G3FwytsDXBLwI3ANrfcPTWkpcB2wSDlh7AiEAoSA906dJjHiCcb/6OWBTumBeRd/lYQZak0T8yDIxpDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFy/UCRA9TVsSAnZWagAAWrgP/13a6b/JWBhJ1VH7RTEi\nnXzUOs2vx7s7YkPYZDInK5G4Bw8GnzmG4Ba91ebD8Uebhm74GDxBkryP83vg\n5gJmksPMb9RszaPcpPGe4p1pCqYW4e/m+29YyXwV/hIGuVUCYeISQsRLlviu\nHjw7SHaoGfZlG9TBffqSJGu4S/JzS3kBbEGa0ttuhnGrJ9cGZoBMxO+iMqfk\n8IzYdwYh+8UfO3kPtQlmqO+wNRKHlfwEEg85YEyOhaDPYdB88WOKMLQcyxmd\nyxp36q/S5/nVFw+3IreKCG68rwd4ZKkaU7AQVcvUduDsQwjknnDNmK3otRxJ\ntjdxIhu0XUNLYxarDUKCTgp9XdEdCfDaGhVqreKMmjdtPzX8maa3Z+EypE41\nTT1dmL6kjbNcRNkCFWtwKgOydndKB8YpHJ0Tn5uGNmgK9lF/7KCpdLAUDeer\nrxvmVKzu4iiEaz5kjkHAPnK8OAW3JMgBEwZ1gRHJgSND/LS4Vijq2oFd7cXN\nMvUHnGRiDiZGr+jj1CZ5eTcI9PHllDdhzil1Fe9ibwdxIpDQket5skdGQlgh\numIJ5LngiMCrRQZwvGUJTaPhz9vFThM3caVbiaL0+oYbVElkuD08+k8Gv+VZ\noByTQ9aIUfLgR4DwxKGD7KbM0NO6Ctuz1E9MNtQglQcCbxgSdDxxHvG+aqEp\naqQ2\r\n=9GJi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"semver": "^5.5.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "babel-cli": "^6.26.0", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "documentation": "^7.1.0", "babel-register": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-plugin-istanbul": "^4.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.2.0_1528246228190_0.773795490436092", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "istanbul-lib-instrument", "version": "2.2.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "4ed9b523da5c0bd33f00568d97dcd440fa58669e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.2.1.tgz", "fileCount": 10, "integrity": "sha512-azWDq6BXKEZV1dGAnqCzBO5S+k3hX6IP63NHKXI9+sPNtaWEymJ6vh0rl65ZLgt8kbn7lmt63kdcOMXomW4B4Q==", "signatures": [{"sig": "MEUCIQCvIfG9n9XBJZci7+EglB5zkw7IeOuuJYajTR1ThETT/wIgZ/m0AOgqSI1hdlOGoJK+Q/DfogfI0qBnZhaJCwhJMMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMdCmCRA9TVsSAnZWagAA0bYP/3RG0/IZQmkVQ6mRCpl3\nVd2juePJN+xSaI/bqW4jAydZdrOUleg6HTDzshjttu8uQzQd7YRQlvsNsOKB\nWBPFnL0sImX/Pv8fhN6InhB2koJePyhoCkZPtCnbtOj0WpyJp5SrxKqAsXCp\nUP2yS1pj/qQB8f9ar+b0pkmozdT7U6rmN9QcDfjwamcBFyiZufnt3qQXoP3Z\nTfr7bLC18M97XUficJedxWC1yFSXFAsdZXjgw8wa0FZrCbqLI6lwtfXZsVfe\nWFfOSBX6vfQUrZardpd7Jkpnz+s+3dnb0uy2j+Iw00GzrGtVbL753/qaX+mQ\ngpHCNU1NFfntfE9f5D0F40/C3jO+lYdZGtVBSkfejpUAso0SzQMnWF0WM0lh\nfP5OP6e1t1bTGD7NFGZmmYjgBFsXBsDW8NJ67DBmWJFiFgGiZEJLdoTNnLhu\nW5Q2OgPAqUA3CPfStcW/7zG8ksm0V5Ewit7QtcayINrHougVRTVsVwhMIS/x\nFwUZwZV/K230MzYxcCwaxRXyQAe03Z/JWkt33NWvXaIZZYgOy/X7egfXaVkB\nP0utlAt5YYIyrYaYVnPNqMpqFKuhRJWRDZCY3PMrondVKe1JtJaw3hpN6zre\nGo5kqf8BuPN2ESnzurVA/SoGrSLPIZGZCw93yekJla3nbAV8T461I83MhEoZ\nyN8H\r\n=6m1N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"semver": "^5.5.0", "@babel/types": "7.0.0-beta.49", "@babel/parser": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/traverse": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49", "istanbul-lib-coverage": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "babel-cli": "^6.26.0", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "documentation": "^7.1.0", "babel-register": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-plugin-istanbul": "^4.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.2.1_1529991334578_0.2316873684344094", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "istanbul-lib-instrument", "version": "2.3.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.3.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "777d3ba7aad2594105e60834efcbe968d11bebbc", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.3.0.tgz", "fileCount": 10, "integrity": "sha512-Ie1LGWJVCFDDJKKH4g1ffpFcZTEXEd6ay5l9fE8539y4qPErJnzo4psnGzDH92tcKvdUDdbxrKySYIbt6zB9hw==", "signatures": [{"sig": "MEQCIAiBgEd413K9js4vVQ0TaH3q5QYYdc5QrzpAzBJQo7k6AiAEl7mT0F6qTMsxDPBNTaKlkn9QHpBiIcSGl6zmYeQtSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMtnUCRA9TVsSAnZWagAAbk8P/j710yPFnZdlk5hMbEg5\nn5ROCxTr1lLa6fpuzfI5Ye+PZywLnY/iqky7OZRy6oYFjdZkfs885iXj5lZ3\nVPA9Owg0pzwQeec33dWg1Uaz64zQZall0YU0KgHIqEEFE2yVJQYFzznAtJn3\nSPxgXT1neLEBrc8yHgnbp7RQHAk0LAIHyf/KaCWSFNzmF2fk82X5WuugE8xQ\nB/YRg4Y1n2p+XB/yblCkNvPx7upgryrg9+N89Ni6xrA66JXT4dxFXWqgz2zC\nwpytVRR6T1rYKxPZbRaeFgWE7nWrDxe1CGH+56c1OLTIwlYiCLpaNmIprGa3\nODPy+usWK6hgkEN4Seaxx+LxAQBfBs6iFdAx7ElHnhub+rbr75yx2OTPBqNU\n3Mz1Nrgtv/P9Fkkthtc1d92IE0v/+usBo8gQUnQBOJ3JWZVmzLHma/5dI4PV\nowpz7KGDjtuxRhqD7ChRY5oe8hipDdJ/jE6+gydtGTtmjAsDViwJy2unFY9k\nPeeFtvbfrT21ZWSMfXw6ePHfoAKgyJ+X/sPwGO39GLpueHaCNMFBKwSM6c9l\nAUjdbuQl1VesuvBeQcb+JCFl0yy3oXnZOl2Sn3hnATchP7jlI2FG5D/HMDsF\n78R/MY3/yl8F7q7brikRwWlkb+0voP2J4bay9wlXWfolKVwtBW93YA9kZjcz\njSec\r\n=40UN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "scripts": {"test": "mocha --require=@babel/register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0-next.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"semver": "^5.5.0", "@babel/types": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51", "istanbul-lib-coverage": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "documentation": "^7.1.0", "@babel/register": "7.0.0-beta.51", "@babel/preset-env": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.3.0_1530059220889_0.3895628117784644", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "istanbul-lib-instrument", "version": "2.3.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.3.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "406406730a395acb2e50f0d98137ace16bd4a970", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.3.1.tgz", "fileCount": 10, "integrity": "sha512-h9Vg3nfbxrF0PK0kZiNiMAyL8zXaLiBP/BXniaKSwVvAi1TaumYV2b0wPdmy1CRX3irYbYD1p4Wjbv4uyECiiQ==", "signatures": [{"sig": "MEQCIBSp/bkfxJTtILeoDn23MFaVCe9tUUQ37SR4ku5CDrJUAiA2CIXaA+9daflj9uD881jo1M1pNO/sFJKtWQ2HNu1fdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQQ3JCRA9TVsSAnZWagAADe0QAIbdaFW7qJlePQUUrqmz\nFCEFdb+9Mp0p+0pC6zQP5TINrHIPngWyTV9Zf11Zgo77qTCclyWscOUXpKDx\nCgVmG6LlY7eg2A/bq/qw+JBKT3rgzw3QbP2fCYTplsJO9eLs6GKjy7V9CEVm\n3ghM+lkyLVw2OFLeE/Gn5B3U1ExzKzfNC8QfGpVnW/V0NwNcDkDcxo3KF9l/\nRLMQkQvZXCyIzwhrqp7Cb7oNpLixi/WxzKHLVug/2dD3kiF+ldYQ6MTtEoiE\nlStclqGHfg8zO9tMWB2wnuCCzxg0x15+KBvHYHmMLOHQdbScEJ3VOhcTqVFa\nDeolGQ8pWzddpDsnB6TG/HXmwzc5um/qW/S66Zxf9J0NM6AwTKyTTg5Y95kL\nwBPRvrAxDdBu7GRdwlBJTcSYMcHjheYuvJSdLZS6dRqkuGzj8HzCGPxGWM1o\nSnoJxy/KMo1O3affRnofAYWXqcR34IoxObLQzX5cTdTI/Ise4j5s5UUHfFc8\nZPXeiN0vgYm4DHQKa/lus+9eoHJTvd5P7Jcw+SNJ6BDz/q/FbIpHb5rtOyAW\nTcc/K3uW/L3Bc5P5+UM7oFe3pZboaUybrZT0Lo/gILdvIqWAwCivqeG7A3F7\nXWLVI05yFIyqknbpQq6EMNi3c4GdAcdfqX3z4BBqfUTR1wGkfswQbPM+AA3l\nUMck\r\n=E5qu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "engines": {"node": ">=6"}, "scripts": {"test": "mocha --require=@babel/register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"semver": "^5.5.0", "@babel/types": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51", "istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "documentation": "^7.1.0", "@babel/register": "7.0.0-beta.51", "@babel/preset-env": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.3.1_1530990025416_0.980996049587803", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "istanbul-lib-instrument", "version": "2.3.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@2.3.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "b287cbae2b5f65f3567b05e2e29b275eaf92d25e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-2.3.2.tgz", "fileCount": 10, "integrity": "sha512-l7TD/VnBsIB2OJvSyxaLW/ab1+92dxZNH9wLH7uHPPioy3JZ8tnx2UXUdKmdkgmP2EFPzg64CToUP6dAS3U32Q==", "signatures": [{"sig": "MEYCIQDK6vqHlbUPmfbTE+jbClwWT1jd/6iWFLF9KlNQ6Hcg8QIhAJ4mF2wJmlJMt7UI4JYC/DFK75uO2FV6opo1OMRK4Dnt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbV6U7CRA9TVsSAnZWagAAKp0QAJZ+SFNaw8346m5+Oz9o\nvvAcxmagq1JU21os/Ov8e7aKeEspIZ6eJtC1CgNLEt7XKyd7ZbMwBgiXBlXE\n3OSZ+AqBS5qks6ZdvORJMkYLWT2c7D4iJhj7rQ2vxjzxgmNgUXXQAgIQowyc\nWH73pb7dA8BgEFo1MDhaOXlQ4ABjj8b40bIHWBLEezqa2IZk/Ioj3PzFpS82\nhCqZVtM5AkSwCS3x9rhCus3x+4o2Hf0fwPbdUm0PchaY0zariXkfEJ4Y4nPH\n6tCRHT+jvOlcVjxr1iSQsIH5/zKNZiUqh4B5rJaKjD8vr8JThUCmVePlPsz2\niKEb8ZtGbZ1QEfJI/S+6gr6WCiIIHHiWgdryxUR8okn83VIAJj03joybiATm\n4BaRnGl8adeQhWtNgGPfn+nVYRDJ872B8zXzr3AhLUEZOprE3wQQgrjB25sO\nFkoHUpjzRYBHuetM45y6KutQNrsZKoauW68PZK+bhDOjaZImS7TQs7eSEIx/\nH6jSLDr9CKWbLLeX3tnoBypyVdpr5N1pxcK0Z3MVrUxzhCTLWBTOxo/EwIh4\neIWJiUt66+oZ7S6vLicNs/dpQzpKElUclsL6a/Iu9dHESWBBYiwcHdGnoB6g\n3tX6bekKk5AxalqyZRq4teQgDUlxKfsSWKeVOmik1qvGi0CnePDGDZCt271M\nbjMB\r\n=V3vw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "files": ["dist"], "engines": {"node": ">=6"}, "scripts": {"test": "mocha --require=@babel/register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"semver": "^5.5.0", "@babel/types": "7.0.0-beta.51", "@babel/parser": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/traverse": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51", "istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "documentation": "^7.1.0", "@babel/register": "7.0.0-beta.51", "@babel/preset-env": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_2.3.2_1532470587856_0.09843676778661226", "host": "s3://npm-registry-packages"}}, "1.10.2": {"name": "istanbul-lib-instrument", "version": "1.10.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@1.10.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "1f55ed10ac3c47f2bdddd5307935126754d0a9ca", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-1.10.2.tgz", "fileCount": 10, "integrity": "sha512-aWHxfxDqvh/ZlxR8BBaEPVSWDPUkGD63VjGQn3jcw8jCp7sHEMKcrj4xfJn/ABzdMEHiQNyvDQhqm5o8+SQg7A==", "signatures": [{"sig": "MEUCICqWObS7JDnEuWEbTGHvOP94Dkgkz7Sc1fUMo5XoLYvlAiEA9Lq5LylPMRXo6BcPVNLx2M+f7sa38HzSa9xPDCBWKHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkFiTCRA9TVsSAnZWagAAV9EP/3zUmTMkc1JoLSHA2CXJ\nIs/Bu5Xubdcz58KwbRQRJFiJMOLGWi7w+nIIY6OY4fuzrWH1WZDdJ0KM8gzP\nOvUrDMAm8U4uSgZz5VpuFtZvwf14T7NG+7An1n5HEDWKxyzoYbs8pWoi0JJv\na8R7q4rVSsF4yx+Sw72t5UsM8jgwY89EhUTpaqBMKRpae7mThMzu5LAghzhE\nRnWWHKro36ShGUffLp/Z+/zEta2Xb+pwhBo5gYKkSxhySq88QTQcjvF9wbA9\nPHol0msWb5uAXwZ6veP5SBkeAzimIauYyuHbDIf6oPwbbp0viqOnWf5F1g27\nH459P2dg6+Affjr4K6stcmKOasteB2q/dTwbeBeGf+GneoArDCnZhhBzOwcO\n3L5Z8r2Yj7nXJVfuGFlIAmFHtQKdRyrZ4s6oeAqq7Kh2I2P0V0tlFy9MC1ug\nm1AF878mByno69EvSvkL+PXp1LrdnhNMitlCXUgtfZqXzC9I4BaXHtGXtTGP\njaG8joCkU+AwPMd+zug9GDiFzZviMUPWisGSOq0CZJlrmc+hNkRAvQivbdrn\nvX9NOJ+E2AbkP4RTZVcWGXtcVEfYs/DxGKOMr5XTysDO1uiPvKqu1Ne5m+Mx\n0lv8obiP51rRkR1ptD8Q51w0iJKwi9bwFa8P1Nf4MApap58PzgX+RIs0iJbh\nBfjl\r\n=bXEN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "scripts": {"test": "mocha --require=babel-register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"semver": "^5.3.0", "babylon": "^6.18.0", "babel-types": "^6.18.0", "babel-template": "^6.16.0", "babel-traverse": "^6.18.0", "babel-generator": "^6.18.0", "istanbul-lib-coverage": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.0.0", "nopt": "^3.0.6", "clone": "^2.0.0", "mocha": "^3.1.2", "jshint": "^2.8.0", "js-yaml": "^3.3.1", "babel-cli": "^6.3.17", "coveralls": "^2.11.4", "cross-env": "^2.0.1", "documentation": "^4.0.0-beta9", "babel-register": "^6.16.3", "babel-preset-env": "^1.6.1", "babel-plugin-istanbul": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_1.10.2_1536186515358_0.5558800439417106", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "istanbul-lib-instrument", "version": "3.0.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "b5f066b2a161f75788be17a9d556f40a0cf2afc9", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-eQY9vN9elYjdgN9Iv6NS/00bptm02EBBk70lRMaVjeA6QYocQgenVrSgC28TJurdnZa80AGO3ASdFN+w/njGiQ==", "signatures": [{"sig": "MEUCIGLeYdgrFD1K0yfLqj+37PvDuEbTErcQvYqV8Thu+hfRAiEA0boOSDzTJEZg9/1Qa3To0MJfulSyYMoE1KzDkwfL8HE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkHqACRA9TVsSAnZWagAA2RQP/iq3SybOPBR/7K1nDTVI\nXNrCSvGjbGgfiHp2+s3KSXpb0Pa1j8XDPFLNipYYvMYepy4jgXUd2ksjAT+f\nrs6e0zAY3rPRPj6sMp/P436uY179fUYmCB5+MhVI5bYqK1HICHPCJwbWVp0a\nJJ6V37bkocoiIp2g8G5skIURC7s2zZnV4AOrGL+UPzBQXU6qq1Rvyi3y81Do\nePA8cZYazqSZnW8IvcrntPe56Tp4tKwN0wh4Idgtr06KAqBJKQ7GkswQUPjh\n+HKfZolwf9G5ZihrBOa2vSG7BBSMOybiB6QMvjA0uhLplKG2H55mEt13t+xQ\n+QGcoj0AT9gJL+fikirA/+C/debSdYpt9pLph44khhdJervj+uas0zrOVMkG\n52+455NzEgzVYgS8twRMrVHSkxJzOYaFGqPJ5O+GQDmcMA24U4HztfQIuoWI\n6yJZFUFhowPqKKK8rFwhU0SWcLmPfZPskiiJM8Ak3wGauenfwXR1Ag520ZZp\nm7hJIF39OmYMqhl8Ym53+ohgot4iUZUXn1G8Iheya3SQ87kE8yWwHFT8Hnpr\nxH3nsXbKyWDkA/ZdbRalfVw3URwO2YstZfx6VclnrKymJ7fff8LwutO+Kv7E\nSHZvzAlxHoEgZw3FwrXF45nlmRMQiKOPXVwQW3HG7IT7L0oIfOD5KK8FDpqq\n+NTW\r\n=y+Mq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"test": "mocha --require=@babel/register", "pretest": "jshint src/ test/", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"semver": "^5.5.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "jshint": "^2.9.5", "js-yaml": "^3.12.0", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "documentation": "^7.1.0", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.0.0_1536195199933_0.3814373160767244", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "istanbul-lib-instrument", "version": "3.0.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "dd631e117dd9891e8bf1de7bb400cb8e491363af", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-/LTPhh1YKXjJlb5uggsiZjJHuViIljcIsB1zqmZegIw2yQ4l8LRksRGebJrZUFVEE28ZtKzmmT50W5tpAucfJg==", "signatures": [{"sig": "MEUCIFfAGh4ecXBsbBOHPScGG4F3AMj2iCJQ6NAHmewdJ2xSAiEA372mFu9mfdWreXK4luF3Fl4BkQt6FCHCiWgwqj+KUvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIXvrCRA9TVsSAnZWagAAG8EP/3Baa4fFX0msQGr8grCD\nGEPnJGQrPZFClD5ljAtzGcQsnMAMCV5zMOwn0ZNcFwsGJU4eNTXj0X7F5nJw\nN+nlnuzNBT9+Omgb5TfEoxIS8vc3pHpVMIbECODOBjosyuEkqizxq6QA7sDK\n+Z21s51z4psUTH+TrMDG9XKT25k4FOhA5A0D+zahtXgPRbuCJJCjWqT3SOQi\n1rwagi0SN6t6xCmdmhN6i0oq2eXk44FGNxTfjeVHK50UWQLN0gQmZCXFm62O\nbR5+G5g60YioZXqBgusaB5lIPO/+IgGaQ+Y8bhunXNw04FVE8qw7uo9wrhos\noJ5zOstAMVN9fVmoGF4OEEB4AhJnpRN12oh2/mEHSiO19h3hEnieWGf3HlQE\nUmSVb7d0OvCY+qvvVS9tThaRPPVwYpTR3Otm08e/Xb5ABYgODzeGncDlEiVt\nxCpyig0fDYeeLgvhBNHwyx/Mv2I76wSxlFn4IlRihys1bukZfMY/gFxRv+bW\nPx/iSneSF36ujGaVfFnb132uYL7M1AQhkIxYL7aR30LFUa3OAH97BMh7gWTL\nMrztIEa1lQROnWGh/0o5q3qNBoqQmQoGwxBxBdNKqqWTRNp6ug+fsPolnZsV\nnwpPKA0HvwuFsriqixEJMDeP8MLjRPgCK+RpF0SiyaIOQeQNLWWq6R5y14DB\nHvxW\r\n=kAq2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "engines": {"node": ">=6"}, "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"semver": "^5.5.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "nopt": "^4.0.1", "clone": "^2.1.1", "debug": "^3.1.0", "mocha": "^5.2.0", "js-yaml": "^3.12.0", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "documentation": "^7.1.0", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.0.1_1545698282654_0.7545008265246229", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "istanbul-lib-instrument", "version": "3.1.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "a2b5484a7d445f1f311e93190813fa56dfb62971", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.1.0.tgz", "fileCount": 10, "integrity": "sha512-ooVllVGT38HIk8MxDj/OIHXSYvH+1tq/Vb38s8ixt9GoJadXska4WkGY+0wkmtYCZNYtaARniH/DixUGGLZ0uA==", "signatures": [{"sig": "MEQCIElUett1EkO8X397sFd/FO8YTyqPgaBPOHhF4MnuDpNTAiAQafjW3+U79x8H69g/U+g93bFr7SoydTFnOrSEcx6Wlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS8iUCRA9TVsSAnZWagAAo6cP/iUS+2mv/+rM/rFVe9Vu\nyTCrvfefY57AkeI7kQyZRxsgOofcIno/+zNtRWX2kzSxRkS1OZPTMfWBzU2p\nfa8TUHgRq8bWLIKoijNfRYqigq1eT6w+YXC0e+flZaJWqa/DjLpSc3MyiPvS\n9b/2gBqfrh0c7g8mU9Wd+qknIIHC1jleMhZgCh4Mf77tDFYxfvJYKZc+k3+z\nlt9g4rdluJTHB75mlyxGXa8LnWaa7PJuHYtL1MhkJNFmVt8NYAdU8MpNB+D1\nzUwtHvJWWt8kdffYbNLv+WQvASrj0/Vl5/2rg41/WkA3yXaof9ged5hfCmx/\n7gWULyWE5iXmNgGrFwzMPBKPbHZ4LB6iPeshzcs4xdGuRSZYig1+a9ZVsc2F\nVVKGtDZvmvAkv22oYUH1moIAqKSzZXwLlp1g+7P0lP31k0vobinEYFikEGyG\nJ1p5iHQ1St6O8KAqvj4hnC6zDrv49MYMvFMS7E/h1bo/PYgqhlMmrW4C1pQo\nnNNojOpVfAm/R8I3F5hBIZRjy01T4YONBFPGcvhaj+S9jOH41owYEcDr2t9q\n/nCeGLgteSd8OtURP21D034fkF+XZmeovL4m6P4Exk8j+/KeYVdtmd9kGI7N\nufRSa0cC1cxccJrrW9txjF36HRed7NzaASGGPHuJsQj0DqZqMbrT1dv0H5mz\n6Kcd\r\n=/WfD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">=6"}, "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.10.5/node@v10.14.2+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"semver": "^5.5.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.1.0_1548470419772_0.16403504238150668", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "istanbul-lib-instrument", "version": "3.1.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "414c0040a29af4817e7011d27c4dd1d860ddf871", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.1.1.tgz", "fileCount": 10, "integrity": "sha512-nK0CAimDTrOcHGwyIr6P+iezATI0DhU8BD6wIrWWooXN+vvsPFVca0jtdX3hjnls3aMuWAvWy794NY47/Ql3gg==", "signatures": [{"sig": "MEQCIE/k7tXre8NvIlcZZLnge/T8+yV7j5wnhFAUhudE2aDHAiBTDB/674UVld364PlC60EKfRF/HEZjUvWo+FCsGvF2Dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchwfxCRA9TVsSAnZWagAA+/0P/jAM+FX1quDJMBeYN3Ns\nrwoJ+hfAzA1UGTLe579UllmwR68yfQIbzmN936GvGMbRbW67kobKqxpXebV3\ne2ms0HNG20liXlzW9/TPBuUH/5eYdLayT3OKWw+vK4tA9ybWO16w1kJQG7P+\neZfnOZo+09lgUayjudh6V9n58UcEmJPWKbsDilUVxP39IAlVWGyzaQPpg34Z\nkPvbaYCFBjl5spxxDjjnn3glLRokaRH6Qsp+yxVsI5oDk28iC8pp9feyim9p\n1PmpYQzrM7RVP+U8f1fEjtEGvtRUgFZhQLudK9+P4HEBhavDVuDPw061Du49\n88dAnTACWI58RshLbUTBNcDl85Izf0qY/fI0FtndZ69quRmaMf6Tkp+Ovizu\nvhssvNdDOr6c4RwEisiQH01U7woE5h30ohzdr6uNCStYrwZ8GsyLxapS4sff\nw2iWa+aj1n/FZZss97IxVJ8KgvYY+ktIX0JdLPQCGilsmdx0NogiW7TXvreK\n+Kl7m/6VuKnJcqCAELy1sxlxPW3q0vGi/357BmcGfgJZVpIXyNR+dD7uCvm8\nqeYw5D4gD4gEnnsy6IU5aLmzworg5I684RxPwdGl0Dh6aw3lamjo89+VNBRz\nCp5fMwUeCc0c5nto6lnLj8kThL7Gi3pGm83t78DtRDhAYFF+SLYEy5vhnDTi\n4Q6+\r\n=1TKI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "engines": {"node": ">=6"}, "gitHead": "c81b051d83217947dfd97d8d06532bd5013e98c3", "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.0/node@v10.15.3+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"semver": "^5.5.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.1.1_1552353264772_0.8859238759918282", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "istanbul-lib-instrument", "version": "3.1.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "5e827c0e93ef2f7a89bbbb83a67d5c4843593f2b", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.1.2.tgz", "fileCount": 10, "integrity": "sha512-5OCdsY81MIHduQqGs5k9JTkOioROzpl3r2PSdkh+1C3j5WxtRWhyhgxMv7wRuEsPsg4K0M9OVWtJ045lyBN73Q==", "signatures": [{"sig": "MEQCIEpevEfUCgS/3tUyIkQNbOTe5WfMQkBh+hH1zcsHK3cHAiALP+wDH/W/mC5yvbQH14Jn37ObqXbjpTNALW3PE9PX4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpPXCCRA9TVsSAnZWagAAW/8P/iG38Yz1H6LrbiQbE58W\nycKWBlgNmp5i7f6aHYQTR9+Zb1fJVcwgHXC08+4gFrQ0W8VL48ZDFDOLF2BL\nxNmokfEn7qhlfmoZ73tvT238V95EL846ATu5NxCGCR1xh1kDFCXg8DAxsCy7\nKFrv3pLIM8DQXa8nBXBkFDtbnAeLKGgyP/8aqTLvtDUdyLSKICw7YZ6+//yA\nwM1RPkVTur1j8MZJ1m/Dd/XhawZF1YgWBKoWEGp7yDG9+hGl65LbnQRaWvBK\nYu1heB6CFHr6MSmssm8zewA5wangdBJY9fUVwSJ4Z/81ZqSTyKkHpeEiGD36\nhPjau33Sy/vpfdt4RTNqXV1DT4nLezmthQWL+mFIhz0QPxXd/IWdu2jEqtG2\nekL+gb6FOTOYi4K3Zli+EHyFQ6eyPsV/fNlXKtyhsNnq873I/aGbILZK2j+2\neZVHo4rDdqNd3YTPz5J+eCRLZwjz/8hIh7/Wjms3GnnKw7/A0K5R6fM2kR1o\n8xG8Vnq5w+RI8ziFRlSYX/Vcwjepsh1I8AQ7e18bEIG2eZ95PjoJicUdTrI5\n5a8oO2H1WKWMm1VXbNVVz4yp4hhY5Idt1tKiPwZkABtSPG9wX8A9NvZ49ZjK\nXGrf5lr5mqVnTi7uLxgI5DXUFusqa4g9s9I2yhwIIeawzuMo5PA6QQSiBpy4\nUtUl\r\n=FXuG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "istanbul-lib-instrument\n-----------------------\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n* The old API that is mostly unchanged (except for incompatibilities noted) and\n  performs the instrumentation using babel as a library.\n\n* A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n  to emit instrumentation for ES6 code directly without any source map\n  processing. This is the preferred path for babel users. The Babel plugin is\n  called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n\n", "engines": {"node": ">=6"}, "gitHead": "e8063c799d0854341cb3daaf91c58acd06bd501c", "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.1/node@v10.15.3+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"semver": "^6.0.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.1.2_1554314689948_0.4255571392247359", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "istanbul-lib-instrument", "version": "3.2.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "c549208da8a793f6622257a2da83e0ea96ae6a93", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.2.0.tgz", "fileCount": 10, "integrity": "sha512-06IM3xShbNW4NgZv5AP4QH0oHqf1/ivFo8eFys0ZjPXHGldHJQWb3riYOKXqmOqfxXBfxu4B+g/iuhOPZH0RJg==", "signatures": [{"sig": "MEYCIQDa/w+xCXeH+02hmwqp0qhCAV5a7EGO1QvAzbJWKNJUqwIhAPNzu5FjQcs/ARFZ3f++S3rS6xR7EimfkoMkdxs1hncc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrSjtCRA9TVsSAnZWagAAic4QAIp2AuejSaa8fAAm+k6k\n7gmaJVb2XhkqPFgMVQdDWUcB0yBSuJVL2DLZJK3hO3tJ+hVY5pP3kbut6Qey\nj5CIalWOSui+DmOxGe9V3gP1J3fwRh7VbBTlXhymkTVP25+eULrAurZq384O\natAkUpJ29qqeSBvsrqE62n5jz0Qbj6ZIyOqXwoz1oaHS4PNktMbIhEKGaT+P\no/rLLxJT4n1Buu6OLpHxWm28baG2nkPk9bw2jnrj+WxKTqxOGaSuAUcY4Aq7\n0/vgxWijvHaT8Kef7JK5A9PZEjD0U/pPTxsVxnmHSKiexnPsxE2HSTtYPrYK\n7Yxf1Lw/uBPzhgJikHb4jQpAX/CGc/49NZ3HG4r9sZu3UkCQoih/JY9FjxWw\nDMRXAJMgRGk8yvSZRcmCdpHj/i7jph+mnIsFxGwyh/AxFSs7aMX8hwbU7XZ0\n/Um6nEFQUaAY8sqPtdLpx13FoO/+oROJZNshsXL8qnoeRtcjND8p6jgTJT4x\nmlfApO+KSP132Pytjp+Yz3HgMIQQIRVLhelzFkjZqUBWNlS3dP7L/RriEpk+\ncvCR6KXvwR+4WYuygkmz/czJdqRlvlF/sMJmsdDu+ZIpTj07e82UMvbDJ+JO\nVyXvulTc9PDF+Gx3tD0pjybE7OuZ4bzKeGcmlCB5Bc6++I1kk5UhJ2X9f1qO\nKFya\r\n=v6g9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">=6"}, "gitHead": "9f8aebf1f08159df20358d77fe98c809d2027c5f", "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"semver": "^6.0.0", "@babel/types": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/generator": "^7.0.0", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.2.0_1554852076406_0.06460034189115604", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "istanbul-lib-instrument", "version": "3.3.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@3.3.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "a5f63d91f0bbc0c3e479ef4c5de027335ec6d630", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-3.3.0.tgz", "fileCount": 10, "integrity": "sha512-5nnIN4vo5xQZHdXno/YDXJ0G+I3dAm4XgzfSVTPLQpj/zAV2dV6Juy0yaf10/zrJOJeHoN3fraFe+XRq2bFVZA==", "signatures": [{"sig": "MEUCIEHLgV9Z56m+julBahGyNv2l7QKwkAkwkBF3T13FTamxAiEAiT86LZUwGSUf/TamBXH3A987e7DHFh9LViIE7d+4JI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwMrFCRA9TVsSAnZWagAAj48QAJe99ohqXQPQdaukjkAc\nb3tyOK9OjGzFcLyE4MOmvLoxUAJ2PDjTYueS44Fs+vjAQI4FR/5BsI9Q7rxh\nqcsjJaH4mwr2LLaz3ZMtK4nyUeZ3TFpoDjaP2K48gnBmn14OcXNJzDYgoc7i\nsIwr20067qVVze+VcWEM/hnTo1tIq3Jjz0NGa/PjJrWPAl1+NFGwkkGD8y3q\n1fQ6THzieBJ2rqlDij5ByrD/h+7l6C5s+dhAgVNrbNKqwMAnuIsdBcKksr05\ncqq5USoL+COFF+yZo5MBjeqJ/6GX2QpLpwDF0rUCBnFKfPWxwvH6MwSs1B5w\nUffl+0OqkwR1rDHhhgUGiULOs31Y/8P2fMjfg7ntm2313sVq3NE5jTNmZ81V\nQ6CDty4xia/AW91xDVICWz8B8i77gFgbdRfj3evd0eFuyGcGRE9GvxkmeLok\n+Fiwb18O+Maf9DnN0mNrsFOxTTvotj8crxoJLsmpsuqE0opnXc3pYTE/P5la\n8niDlpbRAn0r4wyH78nVuITQjUWqCvLasDfuC75dUsL+b/TV5ES64J8wn5Dd\niNmri993vO38lCbo0qqxafRcUeGtg16fIgfXuJ6Ngvm1pnOimBsFTXVxnB9Y\nwOstR3IplOGoJqg5uLdwpIfhk8NTKjmWX1icZILZRUxGnMo1wll75LXtidwm\nUvX4\r\n=+dWo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "scripts": {"test": "mocha --require=@babel/register", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"semver": "^6.0.0", "@babel/types": "^7.4.0", "@babel/parser": "^7.4.3", "@babel/template": "^7.4.0", "@babel/traverse": "^7.4.3", "@babel/generator": "^7.4.0", "istanbul-lib-coverage": "^2.0.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_3.3.0_1556138692552_0.4455647677102821", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.0": {"name": "istanbul-lib-instrument", "version": "4.0.0-alpha.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.0-alpha.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "75c426d3c221451cb352dcc5b9e5971bbdf03cb4", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-yStz3NJNoEDUtIec6pNUGxgFw3BT89U/enrcZmdKy1xvp625x4/338bvxLbZc8jtvOYoZRcH1G6JVC5nYWV+5w==", "signatures": [{"sig": "MEUCIQCysl1sYfgg7n1cnI1vae7GLFpqkfYbZ+0ej/fv+bnPbQIgAwbn1I5Ab9p9uUEbwb881pEJnLyDRzGg912dgi+IEEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCifKCRA9TVsSAnZWagAAVlMP/1Pqj7fpLWWhbf/GrDuv\n0bE27Rjwm3SeJaxkgZJilDHNKhpnymTU/Nh/H2KC5oIU1mGnq/zZehQMUEAE\nKwxNPyKM6CLPYH2bv9+IUIGy+SUW6+KueX0cYDZCseluLP4Zw/uGKpzs4bAS\nMQ2X5P4jQgXVtd2H9C4/NtNwnqWaEcUeI1QSzXR5+X8bYyy03Mh07up6tuqI\n7bWzBUzgIzltmFNBYjqHFr8Svj0KyUPFkWnHyJW65NiZlLfNgRquTVzWjgdp\nnLwmcv3yqe2VrkVAeSEX02ak7TDBKkj1avj7ojrhEhQp+c5nKUhw4pHNJAXk\nIbGwL5xMdYxa/NejAJtS3nYsaMBDtR+Lj45ED7QZjyl/0rfl72PlDt0f0EM8\nnYbSxA5Q63itr4ZXfuYEuQabKu/2kOMr2M4P83ccyvrq+XE2uvpNA0l8Zv7O\nET5JILDjP1605wuIvOmrtOH7TC4IlwbHYVmDXRV6VL75BKjdTJv4Yf/j+Cps\n7tu/jtC/LMCctW+bdqYZisCvBz2DAanvENlgVUbd7AOly6KfCISydiCEFa8l\nZcTohHjXOCpH/Q1uRFKZZivPlTxrwSPzsgXuKo0ccbu3/cecMs9JM1ZHGWHl\nUqH6j9oZreZJTuzJsoooPTVaErdFSfbwqEDCBUY3uI5PpiZ+p4eJ5s9zsI1s\nOCou\r\n=+rSJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "2e885073a9398806c9b8763dd39418398182ca34", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"semver": "^6.1.1", "@babel/types": "^7.4.4", "@babel/parser": "^7.4.5", "@babel/template": "^7.4.4", "@babel/traverse": "^7.4.5", "@babel/generator": "^7.4.4", "istanbul-lib-coverage": "^3.0.0-alpha.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.1.4", "js-yaml": "^3.13.1", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "documentation": "^11.0.1", "@babel/register": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.0-alpha.0_1560946633463_0.03502828726730356", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.1": {"name": "istanbul-lib-instrument", "version": "4.0.0-alpha.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.0-alpha.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "a6dd9ad3b788c618d25dc61084310744fe15f875", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-w/SgxDNsdwsD1jL1vefUzKv3drmOKWg9AFzsXdoSIS61s4zXy/O5yCWeZRePnL/a5J2/wps9nDS1g0lya1NSeQ==", "signatures": [{"sig": "MEUCIDGGAAjJs5/w1yFQVjJBHymysaQp6IeOEIWYWBQA7UoVAiEAknHN4rLvIGcBArDd1YKAmrbzOmX/GLXK6WB7JZ775VI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmT4DCRA9TVsSAnZWagAAkfoP/jFVe8MlwURNVQQXjo5p\naI7OEptH2ahZwIACy6GVrdOMERC0xxkGT41rGwcMTTt8aMFxq+xBPgi9REam\n040dVZi0fQALv5RHd96kyx83qsViv+AG/qVm4NErtMdJ3olt5lq/3c5g4mKc\nkeoYltFT5+ZttH/Dsq8Sgsxa8zjMDdzHvk9pEnPBAzfXT5cfiE7N+FWT7CBp\n7KcALYXeuT8i3QolbIKA+/o0YIIf5Llp5e1+pZtlhh21HaavL07lhGyJroly\n4ShgmYyIpzsZGIqRzRm6w+Fg/ImA7W9BcHdhgLDTRsBfKacCc6NShpH0pUM1\nfJAxkzh4uAZATaHRM40AQND8Xofnr6VC5sl7WbyALivkiRGLUa0B9F953n/R\nUIKEFAMtM1qNIngFlsQGNcoPUoL6YgMUn8oooFn3ckDBNCqB9Ge6+Ut5HSwa\n0pGMAQ1cBn/lD9NqKZcLA3fNxFvxU7PEvIg8gPosyp9KVmCZPo0qYnaWacT1\nB+uhH4gC8H5ROF6F+7ZSwY5ObmDpjqLzPLZFm3OUU6WUX0MO3V4ioEqFZ6h5\nDVmsr7yYauUshlWTOldVBmZfjXVamlAHVziIh5Q1Rf1SeEqkTz69/qppNZn9\nYeO+RsLdN16eeVcSwMJST3ZPg8mC7Zg/D5dhxJTpn5tXGfvGoactnCaZIaTr\nysAN\r\n=nfKV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "4d5e777a9bc4847d178ad31f379307124cdd1e4f", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.16.4/node@v12.11.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"semver": "^6.3.0", "@babel/types": "^7.6.1", "@babel/parser": "^7.6.2", "@babel/template": "^7.6.0", "@babel/traverse": "^7.6.2", "@babel/generator": "^7.6.2", "@istanbuljs/schema": "^0.1.0", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.1", "js-yaml": "^3.13.1", "@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "documentation": "^12.1.2", "@babel/register": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.0-alpha.1_1570323970695_0.856004481428791", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.2": {"name": "istanbul-lib-instrument", "version": "4.0.0-alpha.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.0-alpha.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "d329c9f385656a00d4e93ff0faa865c01de865ea", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-0zSGZ1Xxb2EQOoCW+i/fzaLUissLndYr9njE2V7ZrYHJTsKL/BkDW1byFLUM/VqTkoosERqX3e7/AbtdH16awQ==", "signatures": [{"sig": "MEYCIQC8U3P8cvnrBLT4G6uOtkwmQQ2JZPkR5a5zYXitHyTaqAIhAK1DVjjpPSAmsJ0zzGXCZ/6cOiptS0aQbdQKj3duldQm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvJIeCRA9TVsSAnZWagAA060P/iN0ms+3HWQB5MRa2AoJ\n7WGuys1VFuMN7F7W/wg1lsgUk4zegDrAJaj80DUH5djARGuJXuzn5EXzJVYj\n6IbfIzk5u+4bWwkHnk+qbogKSI0wxRv/VP2b8eSmxOaUR66nFPpp5amz7B/L\nMS99KyklkzCN/mRdT+skZ9nOqpQJm3T7ISq6RrScOnv+jEnem3aqcG0im/kF\nXsEj4VLLi5Yt8bPKRJkz0dUUEODoKFnx3fEptouIYyYLH+J8MvjABnfILT6T\n3wGU2uYGz10HChBoeok9JOOlF4iI3E0U4Zl0aNdLj7fLuPdhFJPXbFh81t4a\nuNK1UyjZgfoH/CUOaiGWUq4ECimXomPBG7udER6PxnsLk0YTEPuQREBUNRVx\nKoAUd/Y0ShMkixtwIgCE0iYHv03lyg5+RtRI/A2wPUl3w2tAqP3hFl6UuOXn\nA1Y3ZpJRHk/Eu8tLObRLLah6PxoZofyIyEIStuluzycIigSdDfR1qlMnfVGo\nilRceJzdWrerAtb3yQkQQ8TeE/zIbKLgVFf+UHSKB4IxEftCLh7wY9Cs9VZZ\n5APZzbo7LJ1kb8iJPYYoBbCWz67UrsDDApJLu7U2h9bYcKni+53/z2X6+J/1\nxDzLYiRCTCfLBVMOyFOL1pDBxNjeXEpu3S2McR88ieuSQWL7Kp/ARSz6CnYW\nJIKM\r\n=cbRk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "7c1367b5786fd6378aa0f3920386672a15edac71", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.18.3/node@v12.11.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.6.2", "@babel/parser": "^7.6.2", "@babel/template": "^7.6.0", "@babel/traverse": "^7.6.2", "@istanbuljs/schema": "^0.1.0", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.1", "js-yaml": "^3.13.1", "@babel/cli": "^7.6.2", "documentation": "^12.1.2", "@babel/register": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.0-alpha.2_1572639262035_0.3734201896676057", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.3": {"name": "istanbul-lib-instrument", "version": "4.0.0-alpha.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.0-alpha.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "9dbacf1034501a02ed90a1ab290ee4a9027a7891", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-zKkVmrXUT9wxeqsnPF4BiELXEkD0ImqM8IK9dcKpwnNQ1RhNpbCc512QnjhhA28SV0CL0ySptHookUt8dz7DDA==", "signatures": [{"sig": "MEUCIQD4wcRQnHpQrHTRRp21UIwyy6yL7O37HiETQjoE5Cqk9AIgO1vvScPHCOzDVQEKj8OSINz4hr1KBWslw3Vez2FBzYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69ZWCRA9TVsSAnZWagAAJ5EP/RvW0nwE06Cf8z6+nFxc\nHStr3465qqkvGLQDg5ObMs3OkS2A92X/zNSX/Iu4GzUfWO1kEKkTSAUY3Z33\nXqpqwOA/wwoPfNfJEkjtn3FEPDCvCP+DhginGX6A2Q67r3PUTioAHxSZQXL2\nMgLyyVseX27zjuV4OTpw5TNjUghQCcXRf/0jOfWtoEH/yNUQZGxT4P11fv15\nkcmv2v+xgYSjCGRgOGa1UQzTZlzeA4wAV6Ww2c76Q1MjC2W56zRSG1/+5+4f\nu7fObmYwkvR4a7SZFcBDTKnCgRdrraOUQbKiBUlgDBtDQX3A7HeX0w8qEbX7\nrbeoJq93oMhegVla1HA+Q7vllbLKKIlSap9WCB8yehqyNNd7aBb7V121dOdF\nM87ehYnAylHsFtQCYOU1TOYo/bwKCaT46br28X5b1tsLYz/96ERlNjCFKGM4\nZ5Pa2X7JmG1O8Ket2xLFlJgLiHpwwZFdf8FXtUvzYnSZSvb3lS64fxvulyFY\nbEut8Kl55+aZ9zAXyxOUS7wF5vmpfF00wso9lAXooQ6OSgtFcIR0VHHFIbAt\nP8dpbkTQOjc1Ot0hseMMMQVul2BhQQBZZIFq1/oQYX/u8Z00GzR9FE5pRHWc\nvdsr8YGF2sjbAZQWbHoNhbYykTetLYqraZltRzSDtjGY5uyxSRHfOj2aDMMA\nWfMZ\r\n=3/Rp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "9546946f0e4bc80714a5b318c59e459781f05550", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.7.5", "@babel/parser": "^7.7.5", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0-alpha.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.2", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.0-alpha.3_1575736917692_0.7479365658125476", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "istanbul-lib-instrument", "version": "4.0.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "53321a7970f076262fd3292c8f9b2e4ac544aae1", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-Nm4wVHdo7ZXSG30KjZ2Wl5SU/Bw7bDx1PdaiIFzEStdjs0H12mOTncn1GVYuqQSaZxpg87VGBRsVRPGD2cD1AQ==", "signatures": [{"sig": "MEUCIQChMb5S+dLCNri+4BUoFbTo8ASIuy+cqY6O7o3R9fpv0AIgWXi+ZLAos+8ew3189yzhMJA0mPOkQ3CuRThhS8UFMQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TUOCRA9TVsSAnZWagAAqEoP/0WRHH3UXMnPbfL1y2gC\nl4mLzbnHimjpuEqrfvs0Feo4jXdBAdC7ICLYfkBdHQwpYUqJdExaOPZsYgLn\nsMDsHfU+3+9YQFQQbTQzu83nP6U3qUd+opQssQ/lnbvcfBqTFZoDYB2vuYz3\nW6eVZq3cz8vn14eqNu5lh6ei7xGbisbjcXPYkcaJQd1jVb8y6Js50k4vhcnQ\nWODrhL0EkWW8WCGz6vl6MOf64oDJ9H/Npo4DyFcvv6xUqmp3/ieWMiq7KJLY\nUpz/txkDCk0JKWRNxIMFAqFfsqMBbGXb08ZWxt38ldAEy22JLJR5G/SH+6UN\n5oGOb0YqNket5TQyiCPTuZJ+MKgC+9liLcUvkpCbctBTM//uVYfUWTPWqWYW\nn7XERWCwzeHc92qZmu7QvTgHDqGQ1asBTM5HIGSgwdw9WrF4Q6nGwhBncbwS\nOY+R9/Gs4YT8tAiIs/OYpCLLs524gypM5mqfl2wmF1Ex44Gyk2Z6cX8wjKB6\nmB0OQdc42WEEGFTUgAmAr47BehB+mmEQGoHe+ohXauEoRSRwJrL7miT8zoNc\nQGi7Qx+DIZz/TeNMF4FKHcHfOQEHgzZhOE3JqKWNi7wprplDxr/4M3j2KG5e\nVHjTaKFiG4FbQB/etiDEubTFPiVBmQU+BOsslT1gVW/Pt7DKCD1nAhoA1OwQ\nj0u2\r\n=Lspo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "5319df684b508ff6fb19fe8b9a6147a3c5924e4b", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.7.5", "@babel/parser": "^7.7.5", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.2", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.0_1576875278559_0.8157640261467647", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "istanbul-lib-instrument", "version": "4.0.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "61f13ac2c96cfefb076fe7131156cc05907874e6", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.1.tgz", "fileCount": 10, "integrity": "sha512-imIchxnodll7pvQBYOqUu88EufLCU56LMeFPZZM/fJZ1irYcYdqroaV+ACK1Ila8ls09iEYArp+nqyC6lW1Vfg==", "signatures": [{"sig": "MEQCIADTe+bA3SId3h1bybRCOyqPN2UudWMX63SJhevsLxQ/AiBhubesWfVcNjiAVBfxcnA6VgBUhsAZXlPfFCy6jSp3xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeN8dwCRA9TVsSAnZWagAAwYsP/i9DeyLaTfUU9GT8eWn2\n4/QMosG1OMd02hOJAPVoKArEW87NQSRg0nDXjZ8R6U0WupcD3DuX1jsAOHzd\neOc3swiJhzbG0BivVIVaxn2/uORjeeDagIoZhdy4OwbAM3TdwnKF3FBgIZdt\nOBCITZKyXXhKSI+YQowygaA/F08wKY0iY0FzUZp4k/O/dyZp7JheLDOT+AkN\nriG9YoeDc15Mz/TtGi1QDob7d024N/MVPkw2TMhwl8x+F6vZxYJWDrYgjHBs\nQgJHDOlru8S6nEaaDc0TDJ4lJwz1NlGRFOowzUEqiDmfG49wz6Upxx5Fea5N\nUCZKRAe39lbPMx2R7uTPvmkVvBAMws6EIDP5HsNb1/fSRl4MuUIqNp2vbCoL\nVDwrcnDnAubm62KHRTiJE2aq0NeZ95OM5zSUfa14jw4OcBtz4c64vquum3FN\n+NJO2gQuvXs7W3pteTyuIwlUtj/a4a3v5ES2axI1VyKD7YH1VKA5pxdHP+LG\nVFJ1nOwlRP7Kmsz1Pk+mJHoieYeQPz/pV59J5EB9nBeoN0ZdgnXQSeuiLr3H\noVtDuE47q/hMSgu/8n6rkE4V+7QtB6+Inq1r/YG9PYiRdeAhO44ZzOkMjmv1\nDA5vWA4dBRf+wkwj0dXLbv6UPy+fOIaK4bMhrQE2UgFxUwSlYj17bgV2hZQG\nY8N1\r\n=PN7h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">=8"}, "gitHead": "4eb43e4325471549d2aa880b5ed2ada475265fcf", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.20.2/node@v13.7.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.7.5", "@babel/parser": "^7.7.5", "@babel/template": "^7.7.4", "@babel/traverse": "^7.7.4", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.2", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.1_1580713840274_0.020548289937544872", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "istanbul-lib-instrument", "version": "4.0.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "23c37bea3bf549f71ff8b4e815c09a94347c032e", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.2.tgz", "fileCount": 10, "integrity": "sha512-1ClUsKzA/Vzcx7DNwd1x73NV26sxlGKTLmC0f+UT8ms+5xrabYA2mIp0VyxKtr1cdh/e+aH4CI3iQnYvJFH7rA==", "signatures": [{"sig": "MEYCIQDgh5b36LAVJZtsPy4iC3o31F1bmqqkJCMMltsKa4Pp8wIhANAMWqbgLHaTzk6Aq1TBgNabCBU7YB4h/8k4022HGePD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJessKoCRA9TVsSAnZWagAAWXkP/1cIPff4F5tzMvuvUTBs\nj2i25g4SlPSsIBNRBE4smA6oESiV5gD8GRb6F7UjkffVZlTs55jP6xHLGAss\nteuzGC3ENKEsoBmZLc7KXhdpcdycEuJqDk7MI400Mb11I3JlJVwl4BnFbtW9\nwdNCXb31E7bYdlW/D6o3L8goj1SDReHDMJDESuECz/gsd2g9bD97QXixmxUj\nVYp3xJAVTYP8zv94gWN7t5xCGeday9RoEvRN2XFMzSVl4fQRIUawocNl6h30\n71GGhC6Pbb7B2ABAmQRg2UghKBKeKQEsVQrugT7RalVcpNe2fI1OxUZ+7ZJu\n8LZ4c1qUMyaJIqAZHQNKGJKa13lJDAfv89LAhlf2S9M3GlrhFCAayW10Z4BL\nDL3EfWaZFmN2QGK0XoqB58MZAZhp7Pe6IPiX/d63xMv6D7aNLcF7f/HDoDNg\nhOUXNRr9SfvBuOGewvH3y9c+ttE/xXiZxfjQ3xS/LmTvreiuBelUxCc4KlNc\nXmMraVJG6mVDPtlvNqUw02dm/jzIm/mcul8IXfN9Z9b4fU1pcIsF7DMFABeW\n3+OCKHGc5oCbC62dfmBuM3dKZFLc6loiGvExJFDEZOHKy9nB2frveI6MyHjF\nEAZ5REXGNfNcYm/LFOigXpxFORImgl6pZ9itDag6f4mGRa58qIPQywprjy4w\nUVnu\r\n=4hER\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "c4f276e26455dc96705a49e7651e86a2345724ec", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.20.2/node@v14.1.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.1.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.7.5", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.2", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.2_1588773544338_0.8728870396602832", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "istanbul-lib-instrument", "version": "4.0.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@4.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "873c6fff897450118222774696a3f28902d77c1d", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-4.0.3.tgz", "fileCount": 10, "integrity": "sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ==", "signatures": [{"sig": "MEUCIGrtNPjRleLY1td/Z6wIotWy/VEmNaqqRnlZcyxoz6X3AiEAnAuHCekEjhSHBBo6Y2iNlqfnhBJDVg3WnDCCig5q2u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetuXyCRA9TVsSAnZWagAAhbgP/j03Umk/CLrCNmJOd1K2\nG5ZKN5U2br//hwAChQ2HHo0UFKFu6drF0XJsl5FRMPmWgXGzcx6pA4MFnOVh\nGvOkUswYPJjmxnh/3vCFfi+6lYTp1/aKB36kNBjn2IXzNQ7XDQQK+uoaj9sJ\nrMmBCrXMEtHjfj5pWCG+BZSSaHAaUJrf8ruNpJ1vLb8pCSeuq/UW+CWeuieV\nl+hLzNa7wVAGw1Dcus/Eu1chRXjErU2hXDCm3kU8ZvFWZedINiXh/mblt4rL\nXbZZNiCzvqeNCHq78MoLolBqd8dmMV0+EkAV3meueZZnr61cHpUD88gqhWzQ\nLYoDLBRh+WqD5Pu9FuAdNqvx01UCdajVzkx7x2e1IqR6gyPN8qLSgrrRbWNQ\nz2vA4anXd1wf6trJC5jsojbzF0t6tFwhYlkNiTB15D4ezNZEYZpCY+VwNw+r\n/7yJgp4T8CXv50NXSWKjn05w9JdTXkPEsd/HatmGImCkOmaYAKF94w6wCCsS\ncb838NBe8pf5iDju+AdXLM6EEqcTo45nRkLhXwnil4YHOD38fDiNq/ZgRyQU\n3bJMfemzeam4qs509YExSdyvnMWgx48+6YGX8OP8in1+bN9ZoxorjQupC62r\nuWblRL7mjEbreZ+DhchlhrlQfBtvTOjX72HpSsGITFRclTKMTTB9v4XBA91r\nt+Iv\r\n=xYok\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n", "engines": {"node": ">=8"}, "gitHead": "2c6f0e24680d050503d404de0ebff53467fefbff", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json --require=@babel/register --include=src mocha", "release": "babel src --out-dir dist && documentation build -f md -o api.md src", "prepublish": "npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "lerna/3.20.2/node@v14.2.0+x64 (linux)", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.7.5", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.2", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_4.0.3_1589044721441_0.9132715762029722", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "istanbul-lib-instrument", "version": "5.0.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.0.2", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "f67bdc68a5f7f7fc66307cb5c15d8a05328916a6", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.0.2.tgz", "fileCount": 10, "integrity": "sha512-z28XoipfqunDrEAsc2ToAlzlQhHg54JYFYPzZiH9QxwV3nBuWgy09xqeXc/IdKCGgOehL7VIUdHt/qn9QvEOoA==", "signatures": [{"sig": "MEUCIEBTc74ns/uoXWej3scHzaGB1QJSKkFNjb+ahXs4Skc/AiEAtohtj9i3JIpZoKLksoP6jI1UN+D44xsZPnSEaSCrq64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPrlWCRA9TVsSAnZWagAA0FEP/RzQsqf3Y5m/twI2VjbI\nzyX3mjN2kD0wWiR4I8r11F8AlR7qGKVNvLe46aMnXYjPJJyMQWvC3szPdbyP\nq1DTxV65ipAKz4EnS6aqu3Knnu1lLj9ZJp1Lp8bJs7FphkdLU/BcrP5Dq/a8\nHJt25KaByvvQ48/sufiqF8kC2mBB3/RxTeZ0+i43xmC1Zd9vtCaFpc8pb8Bf\n1bXliePGxbXB8pW2JhZYhexRDI51XtRG0sGSpxzBboAa4ZewFCb1dqoUi3u0\nOREaX9MSnV+PX+CgCWlf8RpHNyuADVOO8IVCAknVqIttRcOzBgOho5ihru/t\nzQjzj1NW6EaH6LrEUKbZLWxdU1zbW2Ry5DhXmaYgKfQzX0PTiUydOuxmsNCc\n83pfvkOxYtMKpJLe1DdTVyO6mEbg4XpPddtI/r7ovLDBndI62612iGiwEMaz\nSt26myw111CKabH8eURCYXyuTTHoGMRSbRltu3RIC5kDW9sfGi4JAECepkHv\nB4lPbz6tloL4r/juMK+su5UWkYkxMCby+zVSRcOy/uUhYn3gCup1QaYXy8ow\n5FHqKMam9VYeu8iAZM33StB7vNkQw6S7hRg/MR9Oafe343g9wPiAJ+rSTF6E\n/g8cze9nZKhGTfb4XZOnt+purO0mA6bv/6FsqxLQfp4Rz8ujRlj1pVX8O0cn\n9VP5\r\n=BT+p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.15", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.0.2_1631500630794_0.25474368922654445", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "istanbul-lib-instrument", "version": "5.0.3", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.0.3", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "48d7f2782519b6b28b49d1ba096ae1bdb7a8934a", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.0.3.tgz", "fileCount": 10, "integrity": "sha512-4Gur5Xc2qzqFCZ4fUJk3jwJiVYP5HeuA4UCj7IC8KdqoIgV7FjHW/rn1nghCYp2yyEEpEmh2tKstQqDJUVweng==", "signatures": [{"sig": "MEUCIQCd+ux1yJ94oF8KWSIX0CcVePhLbO4no8LUCQ9iWCw3WQIgBi05LWIr6NT6+U3NgTgLFMc4VSYObbEbw2H5yM0Ss1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61476}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.15", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.0.3_1633533318049_0.8941517999747777", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "istanbul-lib-instrument", "version": "5.0.4", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.0.4", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "e976f2aa66ebc6737f236d3ab05b76e36f885c80", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.0.4.tgz", "fileCount": 10, "integrity": "sha512-W6jJF9rLGEISGoCyXRqa/JCGQGmmxPO10TMu7izaUTynxvBvTjqzAIIGCK9USBmIbQAaSWD6XJPrM9Pv5INknw==", "signatures": [{"sig": "MEUCIBP31SXCFfwa6xBJNEACkywYOeKZ5oq1S8RZavMCspcmAiEAxPxSiHMIWZ+hlvw5CLjJwjF1aAIKcSMWkhY+KwhY2Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61938}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.15", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.0.4_1634416738949_0.5694703737868945", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "istanbul-lib-instrument", "version": "5.1.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.1.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "7b49198b657b27a730b8e9cb601f1e1bff24c59a", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.1.0.tgz", "fileCount": 10, "integrity": "sha512-czwUz525rkOFDJxfKK6mYfIs9zBKILyrZQxjz3ABhjQXhbhFsSbo1HW/BFcsDnfJYJWA6thRR5/TUY2qs5W99Q==", "signatures": [{"sig": "MEUCIQDGFXFXzuy0dYb6OUY9ddAdBMNA8mXwUJ1y2TVYKf9pVgIgNwj6aCmpusQz9tnypp5oYtwSI1dklCaQ9bK7x93iclY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2wJCCRA9TVsSAnZWagAAXAkQAJxoHf04rjKoDbWbMItO\njzD9KWaazwISxQXAcBX9b9BhDLnXODykdPSjK4USaXA+q3oyjLmuLeKQWy4x\nUNRJsFzgT6xm4lNE9lJrB90ad8H7HnPHhPvNqDQ+LY+BD218ZzVO1NGHe0At\nm4kYK6f5ra+ITG8sdjM+LlGKVZzVcrJxOa/fj6ESc7EdetPpCwDpWEhm57YC\nTWx39snm4W+JiRfialxZq/lp7nMIcaFD/uPGnjzWZ4oaWxCPP6RlqKjd45sn\naL45yBxXbpUbeCaboY4HcyUBNfpSFdaPlTI3YsmPHJMtFTF1RMF0PVCgeDMf\n+cj4oSgUKPe9LN1nJmc/L4rw8X0Q4WHRhBKAZWmVb4G+VjWf2uu+lFCeQ1LS\n4FmwlYXmSFJQ9mJrRURfqxnsJISJrWD1nSuM/uGW5gpoIQT94F9Lfg//Qegd\nu+Y5JEViJdNVa/94//7QMQVsM0O3rRFwStBq+YnE1Xf/ZksvVqoamkFi5Xad\n/CTMTE/cv2Ryrx+7iXXsbcwsuCdeHzEFYeki569NddyfJBv4GDvOUdUCVvsn\n1jeEZqcpoLN/07ajM4dLDSp9w2g2JRaDADQlB8h31ROF6cUflLDaiKfvIs39\nlfR5Un7FSaMUVwaaNtSNO1ldfJdKpUvz6itYcvXFyItqaf6s1evfI4XQrCYy\nH3oY\r\n=1Sgo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.15", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.1.0_1635347768669_0.30796401535211304", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "istanbul-lib-instrument", "version": "5.2.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.2.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "31d18bdd127f825dd02ea7bfdfd906f8ab840e9f", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.0.tgz", "fileCount": 10, "integrity": "sha512-6Lthe1hqXHBNsqvgDzGO6l03XNeu3CrG4RqQ1KM9+l5+jNGpEJfIELx1NS3SEHmJQA8np/u+E4EPRKRiu6m19A==", "signatures": [{"sig": "MEUCIGnwEe0m/ZSx3y0W+zJTqYI0tvai/WTrJ5hk5rWXTD6TAiEA82vn2PZ3Ln7tPDf20fiSIN5n/LonpSNJi8tYFksVVJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYEMDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqudRAAiLtb/Ory/fdDAgppGuszIkCN9MwLgGYDNMtVQZ3TTuv8n3LE\r\nJFjk0kn9iDRTNYcfG3uikd3f03xslr27x3mvXLwKYp3WGp9nfg6lbxJImgJ5\r\n2ydTY4pg+iC6NMdmUZu3IynH787vH2O4+dTQapgWxer7a7OPJwvJlV2J6Lub\r\nrRcfDK1vZBSZQU/CbaumV+S5w4ou1QSCFqkCnNRUEEekTgfuk1nQXVa2s2KM\r\nN7TazEUW7wJG7/8TLDXIWLpBw3hYmL6g9b8oRNgqUvSlqndQAIeKS6Oi091N\r\nPqd2QO0J9T6JSZHYy6wZDrPh2t0COMEEBGB/3sFQxsKs5ix8gOeXWlb3p99F\r\nTTDu2qxXpl00WjTp9hiPRvzXF8QP0zSvdwKZewkgzAfsgIRMAs1Yti+saiL3\r\nvlMhok/KYrPTe0JL2frmH+mcPUmhpsb5yOW2FHBHTM6PjyzoOuZqyqcK+Ou2\r\ndpbrdF65+qfi+MnDmqSncqTp2MBiBMbYoKilJjO6PnKSdxqWDadmtdVeul/0\r\nc6eEhAv76pUvY5ppegYGBztHeTG2+Os+/cre8ScDX6ZnFJzyZPa9G4VUOVTv\r\nzn/Lo9/DdfuzHwjcyT+3IM4nIriI3/bhCn2O2ncP/8ZxvFYmXKGXUMMOmXAJ\r\nGLsiKyFZBxU5Y5qOFQsS41uC+3ZeWuuJVgc=\r\n=k0zk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.16", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.2.0_1650475778976_0.43029792694513347", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "istanbul-lib-instrument", "version": "5.2.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@5.2.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "d10c8885c2125574e1c231cacadf955675e1ce3d", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "fileCount": 10, "integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==", "signatures": [{"sig": "MEYCIQCCzeopWRWvNpLSAujfjVbf/+FEmi35wR7K5oiowX5siwIhAIbxqA8IyEl5EyssnhV55cCQo1LgpRx6lIAml6+RjVG7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPgS6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9yA/8D8M+JUIKar1PROFhWUDTwugTaddKu7NhKDb7nVz9Srxx1+QL\r\nsvHGgXjPesG66YfwgbVWcZmkgj4V+I/XbDh8MaUreoyNiwsrHxajw5YTwWIG\r\non+0hIcg5+KStlF/QNXVCWErbHDjX63yx2hM+SxWnLWHVVQWaWSuTVqhQ7Ut\r\nGDDf5lleLe/Mf5HT/+xgD25p85ED6t8K9Kv1qxlUql46XE5L+rXsWUtojxRW\r\nLuz3p9ZUQAOtGpdhZKSLZ7D3DDkK9H8ZtTMUdWP4pkD/zyi22W0cWEgeggIQ\r\nrMTxb1ruVJC9B33cdnbqPWC5r5BHgsjB7nhPVY9nhGXpYyaqDqXWJ3wNyVe7\r\nJjthzIG1ppHQGH7NQyi5l3wVgNHSgzjzSzdTQ4IPqv9QxGa3kb8MS7sHKam7\r\nujslM/WOvPdAFw/0neX9mf1VBS34NIaMg3qDG1g9VzdOZLePgiwjKaQNGYeb\r\ntb+IULV5dqXaUhGIdTmO0gbbtxqZc/mmhYDzlyFaHAZgqOQpg7HSPoVvpU32\r\ntuaVXWsvkH2FESU5vjiolXHTmRvSv0g8T1Mwh8ZglZVBf3Xi5jqJ8RWyrpkI\r\nB0aPc8eZ1lL7+NCC3rg80CBxC8h/KzHWin3UDBK3SJP5JBr8yYx8d4bEIWB2\r\nkIkZkmVft+bvyzVOJ22gfMZ5TG+ovBrse0s=\r\n=6PhO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.17", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"semver": "^6.3.0", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_5.2.1_1665008825926_0.2024152244778621", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "istanbul-lib-instrument", "version": "6.0.0", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@6.0.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "7a8af094cbfff1d5bb280f62ce043695ae8dd5b8", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.0.tgz", "fileCount": 10, "integrity": "sha512-x58orMzEVfzPUKqlbLd1hXCnySCxKdDKa6Rjg97CwuLLRI4g3FHTdnExu1OqffVFay6zeMW+T6/DowFLndWnIw==", "signatures": [{"sig": "MEYCIQCbA0qVebxinK1VfmVbWoItqEyUa65z352qsy66F03UeQIhALcSBPo6jXG1WEfrqTLiazdJRfBv0a/0x6TPGfZCs9Pa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70634}, "main": "src/index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.18", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"semver": "^7.5.4", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_6.0.0_1690295513017_0.5110530074002604", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "istanbul-lib-instrument", "version": "6.0.1", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@6.0.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "71e87707e8041428732518c6fb5211761753fbdf", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.1.tgz", "fileCount": 10, "integrity": "sha512-EAMEJBsYuyyztxMxW3g7ugGPkrZsV57v0Hmv3mm1uQsmB+QnZuepg731CRaIgeUVSdmsTngOkSnauNF8p7FIhA==", "signatures": [{"sig": "MEQCIQDEhEKP8k0Y6UnNvAWRARhCG8i2qrVf0EupuV3Wfy2pewIfDx3OWFbfU6iAJv8KPGilWbtOAAyzerCkdekSn/A9pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71198}, "main": "src/index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.18", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"semver": "^7.5.4", "@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.7.5", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_6.0.1_1696405132596_0.8477029740469566", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "istanbul-lib-instrument", "version": "6.0.2", "keywords": ["coverage", "istanbul", "js", "instrumentation"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-instrument@6.0.2", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "91655936cf7380e4e473383081e38478b69993b1", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.2.tgz", "fileCount": 10, "integrity": "sha512-1WUsZ9R1lA0HtBSohTkm39WTPlNKSJ5iFk7UwqXkBLoHQT+hfqPsfsTDVuZdKGaBwn7din9bS7SsnoAr943hvw==", "signatures": [{"sig": "MEUCIEjg0K3BZP+72WL57ZqFtSj01ImsiIc9aRUTBB1G2vbpAiEAg9tw84wPG14lOlh324VEQzIvOeSJ3pW1uN8rUWAfhT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71505}, "main": "src/index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-instrument"}, "_npmVersion": "6.14.18", "description": "Core istanbul API for JS code coverage", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"semver": "^7.5.4", "@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "chai": "^4.2.0", "nopt": "^4.0.1", "clone": "^2.1.2", "debug": "^4.1.1", "mocha": "^6.2.3", "js-yaml": "^3.13.1", "@babel/cli": "^7.23.9", "documentation": "^12.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-instrument_6.0.2_1708375823614_0.8432833690287314", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "istanbul-lib-instrument", "version": "6.0.3", "description": "Core istanbul API for JS code coverage", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "src/index.js", "scripts": {"test": "nyc mocha"}, "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "devDependencies": {"@babel/cli": "^7.23.9", "chai": "^4.2.0", "clone": "^2.1.2", "debug": "^4.1.1", "documentation": "^12.1.4", "js-yaml": "^3.13.1", "mocha": "^6.2.3", "nopt": "^4.0.1", "nyc": "^15.1.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-instrument"}, "keywords": ["coverage", "istanbul", "js", "instrumentation"], "engines": {"node": ">=10"}, "_id": "istanbul-lib-instrument@6.0.3", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==", "shasum": "fa15401df6c15874bcb2105f773325d78c666765", "tarball": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "fileCount": 10, "unpackedSize": 71994, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC58HMtX9xyI1Ton0NNDnkBjRw8xGUeQcrO9FOxtgWdVAIhAIc5uqx4CQ4EEd1XV+3SxZ8Rh87izoN5UlcpHq2C1h0W"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-instrument_6.0.3_1719779158306_0.603559123890486"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-11-21T22:37:22.940Z", "modified": "2024-06-30T20:25:58.672Z", "1.0.0-alpha.0": "2015-11-21T22:37:22.940Z", "1.0.0-alpha.1": "2015-11-24T19:08:59.404Z", "1.0.0-alpha.2": "2015-11-24T19:15:45.145Z", "1.0.0-alpha.3": "2015-11-27T00:10:49.217Z", "1.0.0-alpha.4": "2015-11-28T08:36:42.873Z", "1.0.0-alpha.5": "2015-11-30T04:03:31.937Z", "1.0.0-alpha.6": "2016-01-25T00:26:32.454Z", "1.1.0-alpha.0": "2016-06-25T01:54:59.200Z", "1.1.0-alpha.1": "2016-06-25T02:50:15.580Z", "1.1.0-alpha.2": "2016-06-30T18:53:48.798Z", "1.1.0-alpha.3": "2016-07-15T05:34:29.668Z", "1.1.0-alpha.4": "2016-07-20T05:09:17.808Z", "1.1.0": "2016-08-11T20:42:56.017Z", "1.1.1": "2016-08-30T16:52:29.569Z", "1.1.2": "2016-09-08T02:49:41.144Z", "1.1.3": "2016-09-13T05:26:20.362Z", "1.1.4": "2016-10-17T06:21:59.088Z", "1.2.0": "2016-10-25T05:11:15.524Z", "1.3.0": "2016-11-10T05:56:08.838Z", "1.3.1": "2016-12-27T04:40:10.316Z", "1.4.0-candidate.0": "2017-01-02T21:24:26.942Z", "1.4.0": "2017-01-04T01:40:29.570Z", "1.4.1": "2017-01-04T08:52:40.908Z", "1.4.2": "2017-01-04T10:22:57.460Z", "1.5.0": "2017-03-20T03:53:26.799Z", "1.6.0": "2017-03-21T06:15:21.137Z", "1.6.1": "2017-03-21T06:26:50.320Z", "1.6.2": "2017-03-22T15:55:56.454Z", "1.7.0": "2017-03-27T05:51:31.457Z", "1.7.1": "2017-04-29T05:00:15.051Z", "1.7.2": "2017-05-27T21:13:06.345Z", "1.7.3": "2017-06-25T18:25:21.091Z", "1.7.4": "2017-07-16T18:58:30.595Z", "1.7.5": "2017-08-23T18:01:25.197Z", "1.8.0": "2017-09-05T00:39:33.724Z", "1.9.0": "2017-10-21T18:59:37.123Z", "1.9.1": "2017-10-22T15:53:48.892Z", "1.9.2": "2018-02-13T05:48:44.334Z", "1.10.0": "2018-03-04T18:43:04.134Z", "1.10.1": "2018-03-09T22:14:21.348Z", "2.0.0": "2018-05-31T00:37:53.201Z", "2.0.1": "2018-05-31T00:42:26.856Z", "2.0.2": "2018-05-31T05:32:39.404Z", "2.1.0": "2018-05-31T23:19:16.650Z", "2.2.0": "2018-06-06T00:50:28.308Z", "2.2.1": "2018-06-26T05:35:34.629Z", "2.3.0": "2018-06-27T00:27:00.961Z", "2.3.1": "2018-07-07T19:00:25.482Z", "2.3.2": "2018-07-24T22:16:27.919Z", "1.10.2": "2018-09-05T22:28:35.474Z", "3.0.0": "2018-09-06T00:53:20.055Z", "3.0.1": "2018-12-25T00:38:02.878Z", "3.1.0": "2019-01-26T02:40:19.972Z", "3.1.1": "2019-03-12T01:14:25.009Z", "3.1.2": "2019-04-03T18:04:50.127Z", "3.2.0": "2019-04-09T23:21:16.612Z", "3.3.0": "2019-04-24T20:44:52.673Z", "4.0.0-alpha.0": "2019-06-19T12:17:13.572Z", "4.0.0-alpha.1": "2019-10-06T01:06:10.859Z", "4.0.0-alpha.2": "2019-11-01T20:14:22.178Z", "4.0.0-alpha.3": "2019-12-07T16:41:57.854Z", "4.0.0": "2019-12-20T20:54:38.706Z", "4.0.1": "2020-02-03T07:10:40.367Z", "4.0.2": "2020-05-06T13:59:04.447Z", "4.0.3": "2020-05-09T17:18:41.548Z", "5.0.2": "2021-09-13T02:37:10.979Z", "5.0.3": "2021-10-06T15:15:18.204Z", "5.0.4": "2021-10-16T20:38:59.160Z", "5.1.0": "2021-10-27T15:16:08.945Z", "5.2.0": "2022-04-20T17:29:39.203Z", "5.2.1": "2022-10-05T22:27:06.192Z", "6.0.0": "2023-07-25T14:31:53.207Z", "6.0.1": "2023-10-04T07:38:52.829Z", "6.0.2": "2024-02-19T20:50:23.795Z", "6.0.3": "2024-06-30T20:25:58.499Z"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-instrument"}, "keywords": ["coverage", "istanbul", "js", "instrumentation"], "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "readme": "## istanbul-lib-instrument\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-instrument.svg?branch=main)](https://travis-ci.org/istanbuljs/istanbul-lib-instrument)\n\nIstanbul instrumenter library.\n\nVersion 1.1.x now implements instrumentation using `Babel`. The implementation is inspired\nby prior art by @dtinth as demonstrated in the `__coverage__` babel plugin.\n\nIt provides 2 \"modes\" of instrumentation.\n\n-   The old API that is mostly unchanged (except for incompatibilities noted) and\n    performs the instrumentation using babel as a library.\n\n-   A `programVisitor` function for the Babel AST that can be used by a Babel plugin\n    to emit instrumentation for ES6 code directly without any source map\n    processing. This is the preferred path for babel users. The Babel plugin is\n    called `babel-plugin-istanbul`.\n\nIncompatibilities and changes to instrumentation behavior can be found in\n[v0-changes.md](v0-changes.md).\n\n", "readmeFilename": "README.md", "users": {"gotwarlost": true}}