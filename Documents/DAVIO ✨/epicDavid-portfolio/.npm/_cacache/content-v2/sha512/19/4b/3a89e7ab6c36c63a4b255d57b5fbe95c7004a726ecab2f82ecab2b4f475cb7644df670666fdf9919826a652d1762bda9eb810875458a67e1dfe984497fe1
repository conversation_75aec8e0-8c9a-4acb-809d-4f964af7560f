{"_id": "optionator", "_rev": "44-998e05a1eeaccc592cb3e3bd67c1c621", "name": "optionator", "description": "option parsing and help generation", "dist-tags": {"latest": "0.9.4"}, "versions": {"0.1.0": {"name": "optionator", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing", "homepage": "", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.0", "levn": "~0.2.0", "levenshtein-damerau": "~0.1.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.1.0", "dist": {"shasum": "df5d359cb19ff2f7ffe7af647fcf252a4787b4b5", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.1.0.tgz", "integrity": "sha512-lGEsImxpQLd/whfEqKcTugB8x1oSlfjAKOPLGEeXX4hWOXKKsVkxGblMZQ168s/oBJA+sjhPaSYHxLDXwPrFpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1J4KUYF7XLNpoBDMyVHnAEvRxAx4s2QtwGIEPpkUkJAiA1cwdDkj/C11XI9W73AVLnVqLHx787iSZ2gJ63QdSKYQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "optionator", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing", "homepage": "", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.0", "levn": "~0.2.1", "levenshtein-damerau": "~0.1.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.1.1", "dist": {"shasum": "e8bb0bf77d0077f22a2c355f19282fd15f628973", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.1.1.tgz", "integrity": "sha512-P7tiwLS36XWP40ZutxmPo3CcLAlhD0recf+V2OUa5ORx8eh/BESxXAXpLEpGUyNwGzj8SLFxw3cxx2RiPC25AA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE0ewdSIxz1OI8fSKWCmwPZTs0ZzbwJXvEE+VhtHwV/0AiBGfBA+fsG+jddmjezupR5DQzVmDI3vSh4wXgsXOJDBdQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "optionator", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.0", "levn": "~0.2.1", "levenshtein-damerau": "~0.1.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.2.0", "dist": {"shasum": "47fa948575b3a26b79185262729362c4db03850e", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.2.0.tgz", "integrity": "sha512-nXP11Z/Tzk4045RwDIaGGFZ29UEOshD5969U6cMdPYJJk6skhsUCFI0xde9TBBLtQ57a2SDkTzLkKYeaiC+buQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGvroB1tc/f3AX2MQhSW4jN1XCU3ijvBBhRRSbtOVxdgIgC8S01UhM0MBlfQQQ4pqCHzDfHn1jsTvBovD9YyHjtNw="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "optionator", "version": "0.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.0", "levn": "~0.2.1", "levenshtein-damerau": "~0.1.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.2.1", "dist": {"shasum": "15a4db3fb92143060614f1d1b999800a459356d5", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.2.1.tgz", "integrity": "sha512-jTtbkK3SrOhOIuMUapqFLAz0fUpCMLO4nn0VWL0WPboHEkmjlRhGLJX9gIPgdAvrbcb2NWbgR40rQSvxIuJ7+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/iKNcVsW+1cTtthdO4V12kVrFsJ+0W/IJ+Du4SfB7MAiAnFW/VIXjYDadEmirCQ/rqVyKm5LEQpfVuqpC+XOFyYA=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "optionator", "version": "0.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.0.3", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.0", "levn": "~0.2.3", "levenshtein-damerau": "~0.1.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.2.2", "dist": {"shasum": "d74ff4624ff0f14a473e3b16f323372c445243bb", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.2.2.tgz", "integrity": "sha512-tK8ED2+grrDGFdxOPYGkM0bKLpF4mfx2pResz9xbB5swbRtpajZqKYlfo3MrLcaE0LrX2w8bwADL41ufUNllJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICjIu+XZl1f/GgCNMpEuduB5E5cZudGy0WBEp/W4Z6f5AiEAvmTe/AWJO44cDisOroM+rfx08y0N/y5uVgyYTZJMmpU="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "optionator", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.0", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.1", "levn": "~0.2.4", "fast-levenshtein": "~1.0.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.3.0", "dist": {"shasum": "9715a8b5f5e7586cff06c8249e039cd7364d3f54", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.3.0.tgz", "integrity": "sha512-qM6AKy0HNNRczFIFciGVSkh6H5yu8kC2hdgqElG8pM6IvQwFYVBd3aUrqjsgZtauuGZr2u/Nf+wLzlZgeCqpSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVasut2xjMtqTjoICejAIWxUb/WRNHZlkcQGJvMKw8sAiBL8xj/l3XJVrJ3AVqw3skZ6nDSm10jTx3+huJY3cWp9w=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "optionator", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.0", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.1", "levn": "~0.2.5", "fast-levenshtein": "~1.0.0"}, "devDependencies": {"LiveScript": "~1.2.0", "mocha": "~1.8.2", "istanbul": "~0.1.43"}, "_id": "optionator@0.4.0", "dist": {"shasum": "e79c7926ff7d550f92c714dfc3da21d7877ebea6", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.4.0.tgz", "integrity": "sha512-4pYF8ZAL/bbPvlGHaIhaAacAdJcoJkVIVAyKYjyHz0NMkvAH2aA7xSLsYMR6n0sPzKJWqmKEBkICgnO2nwinoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICqUOKe7BfghofmjkYHHnIsT34scEkMuRfDzaNjzeUHRAiB+akeWyE+7AWXtzTbSU5boU9UKD2EL8Z66u09e3Go+hA=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"name": "optionator", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.1", "deep-is": "~0.1.2", "wordwrap": "~0.0.2", "type-check": "~0.3.1", "levn": "~0.2.5", "fast-levenshtein": "~1.0.0"}, "devDependencies": {"LiveScript": "~1.3.1", "mocha": "~2.0.1", "istanbul": "~0.1.43"}, "gitHead": "52241eef663601bef5120c3a770d60533d2b3097", "_id": "optionator@0.5.0", "_shasum": "b75a8995a2d417df25b6e4e3862f50aa88651368", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "b75a8995a2d417df25b6e4e3862f50aa88651368", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.5.0.tgz", "integrity": "sha512-jUr7aBk/kCInAEsl+qxuw4ORpe458atDKXNLhyvPUD4NfnsJsbAViX1b9nb/0rS62lO8cIFd1VoiaXLQ+MybOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdBE38IhgBVmyhK4nVvuSxGEYDFvXS+pUSck8gto46JgIgRYa7JsdyPmOJ1KneHyhgcfbWWrOnXLO5ohWrFxZ+pBg="}]}, "directories": {}}, "0.6.0": {"name": "optionator", "version": "0.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/gkz/optionator/master/LICENSE"}], "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.1", "deep-is": "~0.1.3", "wordwrap": "~0.0.2", "type-check": "~0.3.1", "levn": "~0.2.5", "fast-levenshtein": "~1.0.6"}, "devDependencies": {"LiveScript": "~1.3.1", "mocha": "~2.0.1", "istanbul": "~0.1.43"}, "gitHead": "c5636b758667c550c2386c4ae9dde0a52e962298", "_id": "optionator@0.6.0", "_shasum": "b63ecbbf0e315fad4bc9827b45dc7ba45284fcb6", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "0.11.15", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "b63ecbbf0e315fad4bc9827b45dc7ba45284fcb6", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.6.0.tgz", "integrity": "sha512-mQXimyhX3iv8+/dPkdwDnNCtXmcGJIrVTw1vIxBGegF3VShG45GtDbZXK46EnN+i0i7HjjwWSgr7XzMxbIzDSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICfycEnEo9vBxBbPz8mDM6+ct8vTOGZnRcJ4v5TO0tgjAiEAnts3UvXSVMQiFKp8wLRQQfKadHHPEL/NtWQFgxiC3KM="}]}, "directories": {}}, "0.7.0": {"name": "optionator", "version": "0.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "wordwrap": "~1.0.0", "type-check": "~0.3.1", "levn": "~0.2.5", "fast-levenshtein": "~1.0.6"}, "devDependencies": {"livescript": "~1.4.0", "mocha": "~2.2.5", "istanbul": "~0.3.14"}, "gitHead": "bd1f9f19d5b3d87c9ac75f4092b426f3327d710a", "_id": "optionator@0.7.0", "_shasum": "cfae9fe94c34f9c2da29e124c5d96780b462f0ef", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "cfae9fe94c34f9c2da29e124c5d96780b462f0ef", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.7.0.tgz", "integrity": "sha512-G/7H1GvrNfTxmsQACLYStWN3WYD5ZcZ4s6TGbHOXEYjA2915fpwPGUHh5gqLIAbd52tUdXB4cJcZ6VEYCHccMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCH1u5GB3PHUFfSFTSXn+bgDMKlgDUxSLsZ5o2TXXwBmkCIDTUGuzYBRXQDOc5xUAjIR0ZWI9TvrdsJliPau9MApkz"}]}, "directories": {}}, "0.7.1": {"name": "optionator", "version": "0.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "wordwrap": "~1.0.0", "type-check": "~0.3.1", "levn": "~0.2.5", "fast-levenshtein": "~1.0.6"}, "devDependencies": {"livescript": "~1.4.0", "mocha": "~2.2.5", "istanbul": "~0.3.14"}, "gitHead": "3bd4094bab482eb8eac85acb5315aa5a668f553b", "_id": "optionator@0.7.1", "_shasum": "7da6e252a847c3d30e41ef19b733d702c36ee5d8", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "7da6e252a847c3d30e41ef19b733d702c36ee5d8", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.7.1.tgz", "integrity": "sha512-rr+yKCqbNbZTcgT6QcM0ZgsS5m1Lz0x/+Kc/LIfn2I0h9Kp6rgvA0nqB8ScWVBlfs/ZmKtEyN/HESGhgDzBCPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICbWsX1ef9WxDlWTyD6JmAQ4boM59/EVoYTjyEp75LHhAiBGwwn02ThYxMuna0GOh8nHDMC/MlfK7yjwK6ZG8vGyIA=="}]}, "directories": {}}, "0.8.0": {"name": "optionator", "version": "0.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "wordwrap": "~1.0.0", "type-check": "~0.3.2", "levn": "~0.3.0", "fast-levenshtein": "~1.0.7"}, "devDependencies": {"livescript": "~1.4.0", "mocha": "~2.3.4", "istanbul": "~0.4.1"}, "gitHead": "5937692c5f3ce3605bf6e21ec06fdc4dbd2432f5", "_id": "optionator@0.8.0", "_shasum": "4d8406ae01ca657535d0125c45930feb2b56b606", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "4d8406ae01ca657535d0125c45930feb2b56b606", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.8.0.tgz", "integrity": "sha512-3aapPu1MurSp7fqhGWiD7NGJHU37Hxn3SnsF88Gf0KxdstrTmgvU65wlLVcHvaaE4c/HX3G9nBFFrIyq3MxAgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIClDKZ9ejGK6ub0ObblIQbmHiaH5TX6j3RokHmqdScilAiBsooR5Iz3TE0dJo9dXPPJXYO0oXdvIrAMI7TcZ1H2xpA=="}]}, "directories": {}}, "0.8.1": {"name": "optionator", "version": "0.8.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "wordwrap": "~1.0.0", "type-check": "~0.3.2", "levn": "~0.3.0", "fast-levenshtein": "^1.1.0"}, "devDependencies": {"livescript": "~1.4.0", "mocha": "~2.3.4", "istanbul": "~0.4.1"}, "gitHead": "88e905d2546df814bc20ff88af93eec8c47c216c", "_id": "optionator@0.8.1", "_shasum": "e31b4932cdd5fb862a8b0d10bc63d3ee1ec7d78b", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "e31b4932cdd5fb862a8b0d10bc63d3ee1ec7d78b", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.8.1.tgz", "integrity": "sha512-X4shcS+JSgX1IpWxOFOr38swI9OFAUVa7jmF4A21vjVc0y3z2f9RLyHtTvmf/Yp1pH42AFWVFogkB+Pi7Ezejg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1pM8RHInADrN9xuu5lAJhmVkqp+cLsnJZyi5qQK0xSwIhAKwEhLDfTXt5jFKSvagliSkDGzW3DQTtgI4tO7HKtfOb"}]}, "directories": {}}, "0.8.2": {"name": "optionator", "version": "0.8.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "wordwrap": "~1.0.0", "type-check": "~0.3.2", "levn": "~0.3.0", "fast-levenshtein": "~2.0.4"}, "devDependencies": {"livescript": "~1.5.0", "mocha": "~3.0.2", "istanbul": "~0.4.1"}, "gitHead": "191de235d5afa47ebb655fc0efbc2b616263d81b", "_id": "optionator@0.8.2", "_shasum": "364c5e409d3f4d6301d6c0b4c05bba50180aeb64", "_from": ".", "_npmVersion": "3.9.0", "_nodeVersion": "6.6.0", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "dist": {"shasum": "364c5e409d3f4d6301d6c0b4c05bba50180aeb64", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.8.2.tgz", "integrity": "sha512-oCOQ8AIC2ciLy/sE2ehafRBleBgDLvzGhBRRev87sP7ovnbvQfqpc3XFI0DhHey2OfVoNV91W+GPC6B3540/5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICbsSYgm3EU8sx7ydsnGbKbUhUs7qjk0wiYY0YYnNifNAiAyXahEnXXL4WQCA0Ge10kOip6HhXnVRAB/7AAf4z1wxA=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/optionator-0.8.2.tgz_1474487142656_0.7901301246602088"}, "directories": {}}, "0.8.3": {"name": "optionator", "version": "0.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "word-wrap": "~1.2.3", "type-check": "~0.3.2", "levn": "~0.3.0", "fast-levenshtein": "~2.0.6"}, "devDependencies": {"livescript": "~1.6.0", "mocha": "~6.2.2", "istanbul": "~0.4.5"}, "gitHead": "80318611b86ad28a38671f811d6c80bf564cdbda", "_id": "optionator@0.8.3", "_shasum": "84fa1d036fe9d3c7e21d99884b601167ec8fb495", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.16.0", "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "dist": {"shasum": "84fa1d036fe9d3c7e21d99884b601167ec8fb495", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz", "integrity": "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==", "fileCount": 7, "unpackedSize": 50062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw0FcCRA9TVsSAnZWagAAqVcQAIBT/cAObUtQwRhVcW+4\nA8HpBdKmnvqVf5333H9VBxSGTt2lDnA8zRMVb1efJGytxuMNlFEVquP8dMBH\nwA0QxFaiATvDcrJUtz/+GNST1SZliIrXwUeeMx7mKXjzcqxT5TQ9vFzU0hKF\nRfgZOL0seMFStF0aEnpGmvJo0dezTXXqpePCKaVY9vC5u5+66115i1HIQ8lF\npgaNW9EO2F5egmmqeF9eqduezx4o+W+0uRxTjlUE9g1yxERuNtmaOAolgVhq\nRofxcIFsdv/3O+citgIEs618a9ufpPbOM1DHm4MCQ85nKpScdo5z/dibUMUW\njzZJfkQlTFiRxlxo/nHkbIABHRDPatWbg+l79zOsVNDB7hxI2Q7FMfNpN5kI\nhW5BouzVzXRqO6XFJuAsMUOMWGrEJxA61UXtVFVR/4q9OYlmilk8Id/hLV9c\npM5mzpjdcMZ0cmN0hchYdH9f6lUfxzyQOKCIHBLGIPVWvlVBxERvJBIiNcEA\na9KoYNfdYyB1Y4DPWfBupNvtrwrtSd7lCYTUvcMdnK9kGxrGHlVqTibOAia8\nWJ3d0+ecZSr9SbMf0vD5yWCoUAUPBu0AH48pQT56bOb/jcbAClk3u+FzLEFD\nuAWSO/Q0yz14d1qG+JGWGQVbCoPGY6bPcl0pL84g7O8A3solTb879H0DJxsL\nRmwz\r\n=triO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtlRcGCbm2qtlin3cfXyE713DyDnm5RufQXDsnoEMw2AiEAtZNdhmYmt7yPDGx0didgmubWlCWzu/OFOts2OCteaoQ="}]}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.8.3_1573077339837_0.7452709036564638"}, "_hasShrinkwrap": false}, "0.9.0": {"name": "optionator", "version": "0.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "deep-is": "^0.1.3", "word-wrap": "^1.2.3", "type-check": "^0.4.0", "levn": "^0.4.0", "fast-levenshtein": "^2.0.6"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "b6fef87a008a0119fa385600e7bfe16c2e578999", "_id": "optionator@0.9.0", "_nodeVersion": "6.16.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-tXCWTjzOfHhZu5XUA3F9aB4ja+S8CxlH44YaQP9Y8pz5gEvQvFZ4UgPfbljhgrHwo/WOP0vP12urLNXId2o/pg==", "shasum": "2ca7ea00a6b05844cb2692dcfa4c854aa4027a6d", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.9.0.tgz", "fileCount": 7, "unpackedSize": 50156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeh+naCRA9TVsSAnZWagAAhJ8P/A+2W9LUOksS8syk3HjM\nRjnmdOTILWkiRAgoiDiETmsIIj9Ak184zaN58Gs2h3bP+KhEyZzskoV4jW2s\ns9RF4LeulkTvV8SQZGQkC4VEy082D5WkoZ9dGxSuaUv2umtVliGjvzgeuWPP\n4PJZRdg5ee6GxgM7kd+qWANkDqcE0K8HXSQ8kMOkrBTxbvIiK3kjGmBqWZWI\nBWOSLZZqIlHuUbQVjQqiEzAyb1zXmQVPl9zjsVR6OaJZ71LjB4lvGTZu34IQ\n/YNYXdrjW/j+652xN9tV3QvJKdPKFfcME6Bgn9rlQXYeYBpSK5Pfpz/zVl2E\np4Kso7jRpj7CZCUOUNyOAxzGuABkjgVq8Z3Yxin7p8YVq2PDlrtCCS8wjRnF\nNfH229NxdSKqMNAZTh174hsaN5zs/RQ5eqMNssqGXAdy/vtLhzHMPt2pkqe9\n2SUjITlHjpbXg/PWOR6l0P82A5MPZ+UpciuBPg87dWSreQFY2DggVp+GlkAa\norBpPJ4EtIxETX8hZU1SBusLdIlOqrrUbQR4AnderNMpT7/4T43GhouXXcWD\nqufTM+ds7KP9aLMKZLDT+FRyZbrqw2XHsF0rHFY4MlyhlOjg3IJt466mfAAQ\nROmmoxXeNHd+y9hkYgHHL0ownfh9hMP28Xld2e5N5kDo4Cl9Kvvzugrx3m8V\nKg+b\r\n=vP+1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7g/GcK5f3Q+dpmOIncEVxa8gur3GkkTxJyHbGIWvqAAiEAtGvSYTeGZVEUsuW/mF2XQE0qbKsiYrjtl49xOnLTfRQ="}]}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.9.0_1585965529574_0.9048793486070725"}, "_hasShrinkwrap": false}, "0.9.1": {"name": "optionator", "version": "0.9.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "deep-is": "^0.1.3", "word-wrap": "^1.2.3", "type-check": "^0.4.0", "levn": "^0.4.1", "fast-levenshtein": "^2.0.6"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "1fd04d6749892326150ac3dcb564b0511fbc010e", "_id": "optionator@0.9.1", "_nodeVersion": "6.16.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==", "shasum": "4f236a6373dae0566a6d43e1326674f50c291499", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.9.1.tgz", "fileCount": 7, "unpackedSize": 50156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeh+wrCRA9TVsSAnZWagAAaXMP/jGOgAKFrBw32nDGc5nX\nuU0UHonyITEjUG9FvplMVCN9WxeIAGeu9BAP5WeBvli9dOKND5r0+dGp4i81\nCm3Pe/7MThXWof/A/BDLM31UnQXyg7HKRridMfAUAsVn6il8gnSbCHZ36jXf\nEGeaIMk8CCpYrxZaj0U1yjoTHOaJDlqlq/LspG4ajdaL6rXk+kuLkQ2UzeXs\nD+Oq75njpUWiJliSAXbrFFfRl8KPIX9TLjAH0VmRwC7ol3oimLkPpHXaNuXz\nVDCB07M/U6rzeuZjLXvonphTMvtR/vZ+tEhwRtU2p66gZgaUnEDmbtpPK3hY\nAP+oBYFMWecSnOdFtPsqaK1m/cChfIA0nrOTn4fWXcY2EMKPVVCF3Y0tkpyK\nTSIASy4u4KZXT3ZVBsoY6J6FzXjKqwT/omeytU3FJfHdjj1PvLZoGse212pX\nDGd3lpsiHs+WEtGAy/h9pKVz7/avLHtvetUOqLltMbJVdLp9iwVJDNZ4GdC3\nhdh7aHMy2MIsXR0yXkSPufB1zEgxXqXvW0Wk9yqaKSJ7U4BmZrZbbOm/c95I\nZgMnVKV7EpvRbypLODWyj5o84wAaaO48ccMOa+9z/TmKWVTDWN59w7P5yLxe\nX92znpgeC61reUuY8jD3i9s1dyhT99sucpGLkQvVphWYZQN4alB7dWWoZblW\nn1Ew\r\n=14su\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXtUv4PpZE2hpJf+6A3rZGMl1jR1rpSJ0xHXa9nc1cwgIhALY3ep8qeVAHE2izE3TonCmOzthv8ZoKMfg+tWw6Zlg1"}]}, "maintainers": [{"name": "gkz", "email": "<EMAIL>"}], "_npmUser": {"name": "gkz", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.9.1_1585966122262_0.6662911299582241"}, "_hasShrinkwrap": false}, "0.9.2": {"name": "optionator", "version": "0.9.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "deep-is": "^0.1.3", "@aashutoshrathi/word-wrap": "^1.2.3", "type-check": "^0.4.0", "levn": "^0.4.1", "fast-levenshtein": "^2.0.6"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "dd770bcc13b9255accff40eb7cfe7076b7df7f4b", "_id": "optionator@0.9.2", "_nodeVersion": "16.14.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-W/Q5m5jpuspnghjVKWcs+5sm9Xl9BIdDmNLWaQbwneNQEZFosaiVPC9vtwN0Dbxx4x7TLY7bxjvttW26AmcKrQ==", "shasum": "4952f975da08f7a2b47b034bb635e7c99db20475", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.9.2.tgz", "fileCount": 7, "unpackedSize": 50172, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCi0Tg0wKc6o4s0sCTusCmcojIGVlFL79k3RaT6xmiTTwIgOhpqoKPI7t/mzGB5Fh/1U8P1dSX1PPqjuLNb20kcZbk="}]}, "_npmUser": {"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.9.2_1687922589228_0.9015422405659701"}, "_hasShrinkwrap": false}, "0.9.3": {"name": "optionator", "version": "0.9.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "deep-is": "^0.1.3", "@aashutoshrathi/word-wrap": "^1.2.3", "type-check": "^0.4.0", "levn": "^0.4.1", "fast-levenshtein": "^2.0.6"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "gitHead": "04e773185572b1c37fe469354e101f2ea3cd9ce5", "_id": "optionator@0.9.3", "_nodeVersion": "16.14.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==", "shasum": "007397d44ed1872fdc6ed31360190f81814e2c64", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz", "fileCount": 7, "unpackedSize": 50188, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBSefrKDZRL4TYr0uQtWGAAw/lpvLK5VuH/OS1dPB7OgIhAOW+CplB7Hb7yxw+pSrtzoxMdoPUy5mEA8BzTVKIMR4i"}]}, "_npmUser": {"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.9.3_1687927710067_0.3470724196345274"}, "_hasShrinkwrap": false}, "0.9.4": {"name": "optionator", "version": "0.9.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "main": "./lib/", "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "^1.2.1", "deep-is": "^0.1.3", "word-wrap": "^1.2.5", "type-check": "^0.4.0", "levn": "^0.4.1", "fast-levenshtein": "^2.0.6"}, "devDependencies": {"livescript": "^1.6.0", "mocha": "^10.4.0"}, "gitHead": "4babe25432de186943d78f8f69875d1255802f72", "_id": "optionator@0.9.4", "_nodeVersion": "20.11.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==", "shasum": "7ea1c1a5d91d764fb282139c88fe11e182a3a734", "tarball": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "fileCount": 7, "unpackedSize": 50157, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGjKVJZy6Yyk2m5GN2ORCAT2UHJW6E/yTxO2i1zHvId5AiAdinB/6X0Hm+WjAuoQqnt4dBr/2FyxsMphqASV+O/Ydw=="}]}, "_npmUser": {"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/optionator_0.9.4_1714169871250_0.6304539150609294"}, "_hasShrinkwrap": false}}, "readme": "# Optionator\n<a name=\"optionator\" />\n\nOptionator is a JavaScript/Node.js option parsing and help generation library used by [eslint](http://eslint.org), [<PERSON>rasp](http://graspjs.com), [LiveScript](http://livescript.net), [esmangle](https://github.com/estools/esmangle), [escodegen](https://github.com/estools/escodegen), and [many more](https://www.npmjs.com/browse/depended/optionator).\n\nFor an online demo, check out the [Grasp online demo](http://www.graspjs.com/#demo).\n\n[About](#about) &middot; [Usage](#usage) &middot; [Settings Format](#settings-format) &middot; [Argument Format](#argument-format)\n\n## Why?\nThe  problem with other option parsers, such as `yargs` or `minimist`, is they just accept all input, valid or not.\nWith Optionator, if you mistype an option, it will give you an error (with a suggestion for what you meant).\nIf you give the wrong type of argument for an option, it will give you an error rather than supplying the wrong input to your application.\n\n    $ cmd --halp\n    Invalid option '--halp' - perhaps you meant '--help'?\n\n    $ cmd --count str\n    Invalid value for option 'count' - expected type Int, received value: str.\n\nOther helpful features include reformatting the help text based on the size of the console, so that it fits even if the console is narrow, and accepting not just an array (eg. process.argv), but a string or object as well, making things like testing much easier.\n\n## About\nOptionator uses [type-check](https://github.com/gkz/type-check) and [levn](https://github.com/gkz/levn) behind the scenes to cast and verify input according the specified types.\n\nMIT license. Version 0.9.4\n\n    npm install optionator\n\nFor updates on Optionator, [follow me on twitter](https://twitter.com/gkzahariev).\n\nOptionator is a Node.js module, but can be used in the browser as well if packed with webpack/browserify.\n\n## Usage\n`require('optionator');` returns a function. It has one property, `VERSION`, the current version of the library as a string. This function is called with an object specifying your options and other information, see the [settings format section](#settings-format). This in turn returns an object with three properties, `parse`, `parseArgv`, `generateHelp`, and `generateHelpForOption`, which are all functions.\n\n```js\nvar optionator = require('optionator')({\n    prepend: 'Usage: cmd [options]',\n    append: 'Version 1.0.0',\n    options: [{\n        option: 'help',\n        alias: 'h',\n        type: 'Boolean',\n        description: 'displays help'\n    }, {\n        option: 'count',\n        alias: 'c',\n        type: 'Int',\n        description: 'number of things',\n        example: 'cmd --count 2'\n    }]\n});\n\nvar options = optionator.parseArgv(process.argv);\nif (options.help) {\n    console.log(optionator.generateHelp());\n}\n...\n```\n\n### parse(input, parseOptions)\n`parse` processes the `input` according to your settings, and returns an object with the results.\n\n##### arguments\n* input - `[String] | Object | String` - the input you wish to parse\n* parseOptions - `{slice: Int}` - all options optional\n    - `slice` specifies how much to slice away from the beginning if the input is an array or string - by default `0` for string, `2` for array (works with `process.argv`)\n\n##### returns\n`Object` - the parsed options, each key is a camelCase version of the option name (specified in dash-case), and each value is the processed value for that option. Positional values are in an array under the `_` key.\n\n##### example\n```js\nparse(['node', 't.js', '--count', '2', 'positional']); // {count: 2, _: ['positional']}\nparse('--count 2 positional');                         // {count: 2, _: ['positional']}\nparse({count: 2, _:['positional']});                   // {count: 2, _: ['positional']}\n```\n\n### parseArgv(input)\n`parseArgv` works exactly like `parse`, but only for array input and it slices off the first two elements.\n\n##### arguments\n* input - `[String]` - the input you wish to parse\n\n##### returns\nSee \"returns\" section in \"parse\"\n\n##### example\n```js\nparseArgv(process.argv);\n```\n\n### generateHelp(helpOptions)\n`generateHelp` produces help text based on your settings.\n\n##### arguments\n* helpOptions - `{showHidden: Boolean, interpolate: Object}` - all options optional\n    - `showHidden` specifies whether to show options with `hidden: true` specified, by default it is `false`\n    - `interpolate` specify data to be interpolated in `prepend` and `append` text, `{{key}}` is the format - eg. `generateHelp({interpolate:{version: '0.4.2'}})`, will change this `append` text: `Version {{version}}` to `Version 0.4.2`\n\n##### returns\n`String` - the generated help text\n\n##### example\n```js\ngenerateHelp(); /*\n\"Usage: cmd [options] positional\n\n  -h, --help       displays help\n  -c, --count Int  number of things\n\nVersion  1.0.0\n\"*/\n```\n\n### generateHelpForOption(optionName)\n`generateHelpForOption` produces expanded help text for the specified with `optionName` option. If an `example` was specified for the option, it will be displayed,  and if a `longDescription` was specified, it will display that instead of the `description`.\n\n##### arguments\n* optionName - `String` - the name of the option to display\n\n##### returns\n`String` - the generated help text for the option\n\n##### example\n```js\ngenerateHelpForOption('count'); /*\n\"-c, --count Int\ndescription: number of things\nexample: cmd --count 2\n\"*/\n```\n\n## Settings Format\nWhen your `require('optionator')`, you get a function that takes in a settings object. This object has the type:\n\n    {\n      prepend: String,\n      append: String,\n      options: [{heading: String} | {\n        option: String,\n        alias: [String] | String,\n        type: String,\n        enum: [String],\n        default: String,\n        restPositional: Boolean,\n        required: Boolean,\n        overrideRequired: Boolean,\n        dependsOn: [String] | String,\n        concatRepeatedArrays: Boolean | (Boolean, Object),\n        mergeRepeatedObjects: Boolean,\n        description: String,\n        longDescription: String,\n        example: [String] | String\n      }],\n      helpStyle: {\n        aliasSeparator: String,\n        typeSeparator: String,\n        descriptionSeparator: String,\n        initialIndent: Int,\n        secondaryIndent: Int,\n        maxPadFactor: Number\n      },\n      mutuallyExclusive: [[String | [String]]],\n      concatRepeatedArrays: Boolean | (Boolean, Object), // deprecated, set in defaults object\n      mergeRepeatedObjects: Boolean, // deprecated, set in defaults object\n      positionalAnywhere: Boolean,\n      typeAliases: Object,\n      defaults: Object\n    }\n\nAll of the properties are optional (the `Maybe` has been excluded for brevities sake), except for having either `heading: String` or `option: String` in each object in the `options` array.\n\n### Top Level Properties\n* `prepend` is an optional string to be placed before the options in the help text\n* `append` is an optional string to be placed after the options in the help text\n* `options` is a required array specifying your options and headings, the options and headings will be displayed in the order specified\n* `helpStyle` is an optional object which enables you to change the default appearance of some aspects of the help text\n* `mutuallyExclusive` is an optional array of arrays of either strings or arrays of strings. The top level array is a list of rules, each rule is a list of elements - each element can be either a string (the name of an option), or a list of strings (a group of option names) - there will be an error if more than one element is present\n* `concatRepeatedArrays` see description under the \"Option Properties\" heading - use at the top level is deprecated, if you want to set this for all options, use the `defaults` property\n* `mergeRepeatedObjects` see description under the \"Option Properties\" heading - use at the top level is deprecated, if you want to set this for all options, use the `defaults` property\n* `positionalAnywhere` is an optional boolean (defaults to `true`) - when `true` it allows positional arguments anywhere, when `false`, all arguments after the first positional one are taken to be positional as well, even if they look like a flag. For example, with `positionalAnywhere: false`, the arguments `--flag --boom 12 --crack` would have two positional arguments: `12` and `--crack`\n* `typeAliases` is an optional object, it allows you to set aliases for types, eg. `{Path: 'String'}` would allow you to use the type `Path` as an alias for the type `String`\n* `defaults` is an optional object following the option properties format, which specifies default values for all options. A default will be overridden if manually set. For example, you can do `default: { type: \"String\" }` to set the default type of all options to `String`, and then override that default in an individual option by setting the `type` property\n\n#### Heading Properties\n* `heading` a required string, the name of the heading\n\n#### Option Properties\n* `option` the required name of the option - use dash-case, without the leading dashes\n* `alias` is an optional string or array of strings which specify any aliases for the option\n* `type` is a required string in the [type check](https://github.com/gkz/type-check) [format](https://github.com/gkz/type-check#type-format), this will be used to cast the inputted value and validate it\n* `enum` is an optional array of strings, each string will be parsed by [levn](https://github.com/gkz/levn) - the argument value must be one of the resulting values - each potential value must validate against the specified `type`\n* `default` is a optional string, which will be parsed by [levn](https://github.com/gkz/levn) and used as the default value if none is set - the value must validate against the specified `type`\n* `restPositional` is an optional boolean - if set to `true`, everything after the option will be taken to be a positional argument, even if it looks like a named argument\n* `required` is an optional boolean - if set to `true`, the option parsing will fail if the option is not defined\n* `overrideRequired` is a optional boolean - if set to `true` and the option is used, and there is another option which is required but not set, it will override the need for the required option and there will be no error - this is useful if you have required options and want to use `--help` or `--version` flags\n* `concatRepeatedArrays` is an optional boolean or tuple with boolean and options object (defaults to `false`) - when set to `true` and an option contains an array value and is repeated, the subsequent values for the flag will be appended rather than overwriting the original value - eg. option `g` of type `[String]`: `-g a -g b -g c,d` will result in `['a','b','c','d']`\n\n You can supply an options object by giving the following value: `[true, options]`. The one currently supported option is `oneValuePerFlag`, this only allows one array value per flag. This is useful if your potential values contain a comma.\n* `mergeRepeatedObjects` is an optional boolean (defaults to `false`) - when set to `true` and an option contains an object value and is repeated, the subsequent values for the flag will be merged rather than overwriting the original value - eg. option `g` of type `Object`: `-g a:1 -g b:2 -g c:3,d:4` will result in `{a: 1, b: 2, c: 3, d: 4}`\n* `dependsOn` is an optional string or array of strings - if simply a string (the name of another option), it will make sure that that other option is set, if an array of strings, depending on whether `'and'` or `'or'` is first, it will either check whether all (`['and', 'option-a', 'option-b']`), or at least one (`['or', 'option-a', 'option-b']`) other options are set\n* `description` is an optional string, which will be displayed next to the option in the help text\n* `longDescription` is an optional string, it will be displayed instead of the `description` when `generateHelpForOption` is used\n* `example` is an optional string or array of strings with example(s) for the option - these will be displayed when `generateHelpForOption` is used\n\n#### Help Style Properties\n* `aliasSeparator` is an optional string, separates multiple names from each other - default: ' ,'\n* `typeSeparator` is an optional string, separates the type from the names - default: ' '\n* `descriptionSeparator` is an optional string , separates the description from the padded name and type - default: '  '\n* `initialIndent` is an optional int - the amount of indent for options - default: 2\n* `secondaryIndent` is an optional int - the amount of indent if wrapped fully (in addition to the initial indent) - default: 4\n* `maxPadFactor` is an optional number - affects the default level of padding for the names/type, it is multiplied by the average of the length of the names/type - default: 1.5\n\n## Argument Format\nAt the highest level there are two types of arguments: named, and positional.\n\nName arguments of any length are prefixed with `--` (eg. `--go`), and those of one character may be prefixed with either `--` or `-` (eg. `-g`).\n\nThere are two types of named arguments: boolean flags (eg. `--problemo`, `-p`) which take no value and result in a `true` if they are present, the falsey `undefined` if they are not present, or `false` if present and explicitly prefixed with `no` (eg. `--no-problemo`). Named arguments with values (eg. `--tseries 800`, `-t 800`) are the other type. If the option has a type `Boolean` it will automatically be made into a boolean flag. Any other type results in a named argument that takes a value.\n\nFor more information about how to properly set types to get the value you want, take a look at the [type check](https://github.com/gkz/type-check) and [levn](https://github.com/gkz/levn) pages.\n\nYou can group single character arguments that use a single `-`, however all except the last must be boolean flags (which take no value). The last may be a boolean flag, or an argument which takes a value - eg. `-ba 2` is equivalent to `-b -a 2`.\n\nPositional arguments are all those values which do not fall under the above - they can be anywhere, not just at the end. For example, in `cmd -b one -a 2 two` where `b` is a boolean flag, and `a` has the type `Number`, there are two positional arguments, `one` and `two`.\n\nEverything after an `--` is positional, even if it looks like a named argument.\n\nYou may optionally use `=` to separate option names from values, for example: `--count=2`.\n\nIf you specify the option `NUM`, then any argument using a single `-` followed by a number will be valid and will set the value of `NUM`. Eg. `-2` will be parsed into `NUM: 2`.\n\nIf duplicate named arguments are present, the last one will be taken.\n\n## Technical About\n`optionator` is written in [LiveScript](http://livescript.net/) - a language that compiles to JavaScript. It uses [levn](https://github.com/gkz/levn) to cast arguments to their specified type, and uses [type-check](https://github.com/gkz/type-check) to validate values. It also uses the [prelude.ls](http://preludels.com/) library.\n", "maintainers": [{"name": "gkz", "email": "g<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2024-04-26T22:17:51.588Z", "created": "2013-11-02T01:40:11.286Z", "0.1.0": "2013-11-02T01:40:13.114Z", "0.1.1": "2013-12-17T23:23:56.525Z", "0.2.0": "2014-03-14T05:24:09.984Z", "0.2.1": "2014-03-14T06:25:34.517Z", "0.2.2": "2014-03-21T00:08:50.647Z", "0.3.0": "2014-04-08T06:51:04.492Z", "0.4.0": "2014-04-29T07:52:44.914Z", "0.5.0": "2014-12-18T03:27:20.071Z", "0.6.0": "2015-04-13T06:06:24.093Z", "0.7.0": "2015-12-09T08:53:22.246Z", "0.7.1": "2015-12-09T10:02:15.631Z", "0.8.0": "2015-12-29T06:11:35.577Z", "0.8.1": "2016-01-01T02:45:06.038Z", "0.8.2": "2016-09-21T19:45:44.538Z", "0.8.3": "2019-11-06T21:55:39.994Z", "0.9.0": "2020-04-04T01:58:49.755Z", "0.9.1": "2020-04-04T02:08:42.952Z", "0.9.2": "2023-06-28T03:23:09.467Z", "0.9.3": "2023-06-28T04:48:30.285Z", "0.9.4": "2024-04-26T22:17:51.408Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "bugs": {"url": "https://github.com/gkz/optionator/issues"}, "readmeFilename": "README.md", "users": {"greggman": true, "mondalaci": true, "quzhi78": true, "noyobo": true, "rawphp": true, "nec": true, "dozierjack": true, "flumpus-dev": true}, "license": "MIT"}