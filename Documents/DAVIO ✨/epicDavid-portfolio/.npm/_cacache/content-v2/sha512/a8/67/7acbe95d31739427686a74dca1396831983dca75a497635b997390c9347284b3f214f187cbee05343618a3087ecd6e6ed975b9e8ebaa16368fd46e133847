{"_id": "lru-cache", "_rev": "317-c4a87a67b1f960c42cc2256aa497b017", "name": "lru-cache", "dist-tags": {"legacy": "4.1.5", "v7.7-backport": "7.7.4", "v7.6-backport": "7.6.1", "v7.5-backport": "7.5.2", "v7.4-backport": "7.4.5", "v7.3-backport": "7.3.3", "v7.2-backport": "7.2.3", "v7.1-backport": "7.1.3", "v7.0-backport": "7.0.4", "legacy-v10": "10.4.3", "latest": "11.1.0"}, "versions": {"1.0.2": {"name": "lru-cache", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "lru-cache@1.0.2", "dist": {"shasum": "04deae53134b6583567c849d868a2d10d5991bfd", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.2.tgz", "integrity": "sha512-xrRAw9qb4GaHCm0QyJbldrYBbSYgL34hk2FFXgFOsrO0R7lnSVjQXVfDKF4RmlpkHU87JG58JZDRAjteN9gEvA==", "signatures": [{"sig": "MEYCIQD4h++jdoYHafR5Ju510yIpwFjPlcDTUd3tCEj2xnqxzAIhAJGD0iWgJzYPZvl3Zd++9KVtMrfLnaQkjrODhu6wXc7J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache", "engines": {"node": "*"}, "scripts": {"test": "node lib/lru-cache.js"}, "_npmVersion": "0.2.7-3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.1": {"name": "lru-cache", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "lru-cache@1.0.1", "dist": {"shasum": "fbfcd2d6e2d8f4519be9826bca3cb70900ffcd4b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.1.tgz", "integrity": "sha512-z0Jr4NF2G+dPi1P98wARYOq0b0CTI2izu3gX2ZkndnKim4ZE4e0qIiI+6k48KF4FueBXakPqF0R3y9xJEFE/VA==", "signatures": [{"sig": "MEUCIQC2D0SqSc/RzFRCr3a7c3w3VeQ6QuEDXgh22iCmEaMlzAIgYdlYMbZCkYpkNXoknF4I1EzkA3VuEkpdogDaP8z1z8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache", "engines": {"node": "*"}, "scripts": {"test": "node lib/lru-cache.js"}, "_npmVersion": "0.2.7-2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.3": {"name": "lru-cache", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "lru-cache@1.0.3", "dist": {"shasum": "ef2ba05194250bd4781dbe57b6064d7320e58b73", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.3.tgz", "integrity": "sha512-kAySFADtNDZ41WmCGqFBlQ90ZztEfQ+k1UDFXAHxjrN0QBPmeQYpDL0/3s/BJwaOEqXtue9OLBl0o3GHDvRJXA==", "signatures": [{"sig": "MEUCIQCkk2dCQ3PQyWe3Twh/YPcept9je4nP1uDPHaMvjJxefAIgGq3Sk4Rt+3PJ5zR6tUUgOHcV1fBoXLueTpOT7Q6pfXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache", "engines": {"node": "*"}, "scripts": {"test": "node lib/lru-cache.js"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.5.2-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/lru-cache/1.0.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.4": {"name": "lru-cache", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "lru-cache@1.0.4", "dist": {"shasum": "dc2af9b3022fb7e17630ed7bdf6a1839b7b70291", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.4.tgz", "integrity": "sha512-wWgerry4u8LGwwavm+dw+1WiqFvC4DiifUf05ASmOGQz0OJh3UYIPwzVD35YyjXQtKTYpnPGFAgBAZL3+fQJvQ==", "signatures": [{"sig": "MEUCIQD4ciymscQtBjqEk554DFJd+hJhlKRbyM9MMA8/SDjCMgIgHX3u9pimeTVZuv3ZxO6E1E9UiNmoJEyscoInP4VJa8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache", "engines": {"node": "*"}, "scripts": {"test": "node lib/lru-cache.js"}, "licenses": [{"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/lru-cache/1.0.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.5": {"name": "lru-cache", "version": "1.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@1.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "62815a3bcb609c1c086e78e4c6a1c4c025267551", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.5.tgz", "integrity": "sha512-78LsxOtsCtZe6QOYdrBnSlI8j0r7bal9Les5ZQH0njXtAuKLQpwd2UOTe0+r0CzKsDeH/ujYXJNCswYj6Mq9Tg==", "signatures": [{"sig": "MEYCIQDYz9dg7SJNiaoWnoWUJUSNu7J4nV19yqBrAQwfLSQTiwIhAKjG/+U3OT/PCRZPyKXPZ8i2kzorgNoI+rVi0Ds5/kPG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.6.6-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.1"}, "_engineSupported": true}, "1.0.6": {"name": "lru-cache", "version": "1.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@1.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "aa50f97047422ac72543bda177a9c9d018d98452", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.0.6.tgz", "integrity": "sha512-mM3c2io8llIGu/6WuMhLl5Qu9Flt5io8Epuqk+iIbKwyUwDQI6FdcCDxjAhhxYqgi0U17G89chu/Va1gbKhJbw==", "signatures": [{"sig": "MEUCIEA9zTsAcceBCmT6vtCTkhpfkxaUX7+gOZm1o//6tmKoAiEAjw0b1LxntWDMraV0GTqxRsanMS+z763zRggD67EudSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.7.7-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0"}, "_engineSupported": true, "optionalDependencies": {}}, "1.1.0": {"name": "lru-cache", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8d4a68dc0ab1cd5a2f39352478c495e9dd33cb61", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.1.0.tgz", "integrity": "sha512-CPLaY1EghHiGwL1adHzxAjIXaMWR2lk0g4bfvVsmPXl6M28n2uQdY65F69O0FJw5iQ6sfuTohqelGAjZa/coNQ==", "signatures": [{"sig": "MEUCIQDLBVk3cL2uca7Uwj8exs+TC0VexKJ3RLSVDXhhM/RTtgIgAnWEoZFtQVSzafQtL9Q4FsiiOzcwWGL2tZB6GGBOS2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "v0.7.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "1.1.1": {"name": "lru-cache", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@1.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d6f24f75c28c9ec1239ca206952689696ec11e62", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-1.1.1.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>lk5sZedTAMJHe1KrK833YyYVUfiZgsmVQ1YplQwa3xQUrE/wtmhZ0Mc3GDvmpgtiSw1/Z05NAsOx/IpYeQ==", "signatures": [{"sig": "MEUCIQDJj+AI6kKhMrq6bX7FyGFO2Ukp7QmPOvMK86XOB3IgggIgDP7CCr+QzJn4zwORX7LZGUuwF9zSpY2JWWUnJZHQKLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.0.0": {"name": "lru-cache", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0fc80ed1e8276dcce18a865bce8a56ba30b81ecf", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.0.0.tgz", "integrity": "sha512-LGKBUSgkwjNCPpZZa2CU3bVhqSYNMmBktH12A/ITj3wvi+DmBbZCL+ovIwEnoaC04J179aU308+bmQqpKHTslg==", "signatures": [{"sig": "MEQCIC54oA9GBSbrbsI73R2eHcQ7zJalCe6lx0eAGTdf/ehNAiB0a3aNYc0fE2yAhKaDYY//UmBiK68R1MRWpHMeF8G73w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.0.1": {"name": "lru-cache", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6feae28419f7fc358a063a5b188d52d15538006a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.0.1.tgz", "integrity": "sha512-CeQC0bWCsrWvvnYEX+gpMlubqaF00VmVsl/lL2n+m0RQLtZ/2Hd8zypbKjF70UPY7B+1F9bjdyjteM1h8VeJsg==", "signatures": [{"sig": "MEUCIQDovWuhx3BOHs/+Gy8vhZUrMR+FF6/xKRe1uMYaGwpo6gIgfjnc0VED1oIIe/NHuSArq+lgOjctnaTdQ+U90YAYCZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.48", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.0.2": {"name": "lru-cache", "version": "2.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c7e26bb69eabb8f6ee8242b3a569f5af7ee2fd3b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.0.2.tgz", "integrity": "sha512-NUy98YbQ5PyHRA+erk4IUiIiFfxgMpQWoaO+WZZU7enoEHqSOoasRRvHjlIXjwW6MUQ1B3qEsU7+1yP4DnbdPw==", "signatures": [{"sig": "MEUCICV05Q4t4PCh0m07tSOwg1cugbbArGm1O8MiHZYWIYilAiEAlO9x3TJ/z0vpazHXFnujVVdhG8i3bgkVX0oAxBv/3zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.0.3": {"name": "lru-cache", "version": "2.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dc18834f4a2e2b45faab6170b69b74741ef3871a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.0.3.tgz", "integrity": "sha512-k4cXk0ZciKMXzC3kJ1eq3WpQGTT0LidL6L/ocJLEgSibwZquCTrj2SWpD8r3ZuCsPo/nrA94NHBhn8Db7CX9Fw==", "signatures": [{"sig": "MEQCIBH8L+hlTE/rlkkWCmBOkgl+3u9btOeN/y5eHRs9BGBeAiBlfVyIQN3keXdG/mFc/LgqU39LHSPxxV0Ftc7ffMENUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.0.4": {"name": "lru-cache", "version": "2.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b8b61ae09848385ec6768760e39c123e7e39568a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.0.4.tgz", "integrity": "sha512-p6+W5xtxxT2y2bKbZuGSz3Rr2mkq+Mq4kXt7FRntJNTeu0BkaNN9AwGvygEz3G90d08JwfgLK9Ho6jbh0SwPQg==", "signatures": [{"sig": "MEQCIEL9Y+LooAaCbhBssVElMvPfPWSaS37j1Or08pMpHr88AiAVX9NBtsYTYShwiX7O1E2g5pPeoTsN6r5y44lD+r6iRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.1.0": {"name": "lru-cache", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ea1baa0fc9146c586aee06bd2fc547ab480e2e3c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.1.0.tgz", "integrity": "sha512-hOpKYIlkW9UbrqQoyaAMJQamdDQqPLL2lA0y+7Oz3bFb69nWJEjrtZ414dqeUsBNaDgbQlHU+yUA91Bf7eZiuw==", "signatures": [{"sig": "MEQCIHdWT3fp1A3s5kR+ctNHw6DOpAlXz3r2C3BxUrUHIo7/AiB+0v98hFF3GLQkI2PbXsxwIOPGjlzM3N+DJKJ/QRwj5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.63", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.2.0": {"name": "lru-cache", "version": "2.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ec2bba603f4c5bb3e7a1bf62ce1c1dbc1d474e08", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.2.0.tgz", "integrity": "sha512-nnQiy1lsNj5xmeoe48piKcv2xWdL6KXxJeN3aobdSH939OMTK/qXRkuVSVAM59nS2KMPBeuqx5GD+e8JbZwPdQ==", "signatures": [{"sig": "MEUCICBwvASSJ3FVqG84xZ1pcDCAxCjxZveUKgbILHADeVVeAiEAqNpfho5vFAZkj2mwGBRKpFOM+uJNVtDFSHc0qVK/pxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.2.1": {"name": "lru-cache", "version": "2.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dcc1de19e79242874a0e883d09bb1ce5c2bb58f4", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.2.1.tgz", "integrity": "sha512-sfqhHkcOe7AbbzwvLSHnpHs/VzISX1qy10leIBYZ6cD5MqHIaIm4qIJeQQiq4DmfY/aYmfMOl4iD1R+xTrREGA==", "signatures": [{"sig": "MEYCIQCaFzIvoKb4I0C4fwH5wUCtGzkKqUPRHPDDj+0czwwb9gIhAMmkhMn6BVlmSSEK9w8ISqsUsElSf8OTrMSgf1VIQHLo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.2.2": {"name": "lru-cache", "version": "2.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "62b95a10cc7f8d85f3737506fe82cdcf3fa04d4b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.2.2.tgz", "integrity": "sha512-L0bzqz8cxAiIBO0Fxnp/LJSGvq9uaIBVyj3TSbHYQx2iswlaammlWVBSIaxqGTOKZjaNu8h6VgyrlOHYyl53iw==", "signatures": [{"sig": "MEUCIQCmaamflzCtiIGoBGVP8iK4t4EAv1DZTp8FnUlMjY9yKgIgVB2M+O2px+3qrf5SK1lLOBVA2NlcuO7LTP/VU5kgiNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.2.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": ""}}, "2.2.4": {"name": "lru-cache", "version": "2.2.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.2.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6c658619becf14031d0d0b594b16042ce4dc063d", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.2.4.tgz", "integrity": "sha512-Q5pAgXs+WEAfoEdw2qKQhNFFhMoFMTYqRVKKUMnzuiR7oKFHS7fWo848cPcTKw+4j/IdN17NyzdhVKgabFV0EA==", "signatures": [{"sig": "MEYCIQDjb98+9F53NuXk0XGrQ6wslAIrO4ewfBFG+HE2AStQYwIhAKWv9IEDPa7Yu2dW6aUs0xNBfP0lOgc9v9fSIWDVmx+X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.2.15", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": "", "weak": ""}}, "2.3.0": {"name": "lru-cache", "version": "2.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1cee12d5a9f28ed1ee37e9c332b8888e6b85412a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.3.0.tgz", "integrity": "sha512-XyBCYL0kTZLNIFj48mAUe1Q0PTLsOlH4ck3YhHM+Z2Aai8aELn6bqc+Ieh4gpaN3diduq5A06WaNz2Qq+8RuMA==", "signatures": [{"sig": "MEUCIQDzYKhqrQY5FvudDAFzZFkkREaczzne/pvqCiR1aEHFEwIgPARlLs3p+qlb0EGq3Xl5GnKW1sjxE+Jzu0rdTITH+wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.2.15", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": "", "weak": ""}}, "2.3.1": {"name": "lru-cache", "version": "2.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "b3adf6b3d856e954e2c390e6cef22081245a53d6", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.3.1.tgz", "integrity": "sha512-EjtmtXFUu+wXm6PW3T6RT1ekQUxobC7B5TDCU0CS0212wzpwKiXs6vLun+JI+OoWmmliWdYqnrpjrlK7W3ELdQ==", "signatures": [{"sig": "MEUCIAMiFENBV55+BqRn+/0SxZC0y9lQogg5W7VTJ712cCl7AiEAqDTLIVU9WPellZLj54XYJveT0SN8Z6GhVokUaqkkz0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": "", "weak": ""}}, "2.5.0": {"name": "lru-cache", "version": "2.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "d82388ae9c960becbea0c73bb9eb79b6c6ce9aeb", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.5.0.tgz", "integrity": "sha512-dVmQmXPBlTgFw77hm60ud//l2bCuDKkqC2on1EBoM7s9Urm9IQDrnujwZ93NFnAq0dVZ0HBXTS7PwEG+YE7+EQ==", "signatures": [{"sig": "MEQCIAhGLHLU59I847Wmq5J67+L9v4Wq45IrNhf1uyGbqtBvAiAkAJAzdycYP4v21v2IZ/sNd0+Xd+Tgjfve5DuHbDYwTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "devDependencies": {"tap": "", "weak": ""}}, "2.5.1": {"name": "lru-cache", "version": "2.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.5.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "60b81048343cd901d529c97a7284810b4aa2ca03", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.5.1.tgz", "integrity": "sha512-FIGZXhZfWjFRTSYBtulC929NjRAi+0m0wcUvIFLB+RtEEccMMV4cqGaHGwREqmus/WA/qB60W8tR4NaUz/ldAw==", "signatures": [{"sig": "MEYCIQDHnm+1PXiDgv/SvAOLzLyVPehT3m228cgN5WNja/mFsgIhAK/MlKmvSQEOINcPDanfGkzb3ts558d0M7xqZg2+k8zb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "60b81048343cd901d529c97a7284810b4aa2ca03", "gitHead": "355fb2d7acd89f08d400345ce2ca8cd27b672095", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "", "weak": ""}}, "2.5.2": {"name": "lru-cache", "version": "2.5.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.5.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "1fddad938aae1263ce138680be1b3f591c0ab41c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.5.2.tgz", "integrity": "sha512-wyqfj+623mgqv+bpjTdivSoC/LtY9oOrmKz2Cke0NZcgYW9Kce/qWjd9e5PDYf8wuiKfVeo8VnyOSSyeRiUsLw==", "signatures": [{"sig": "MEUCIQDxWC75EwtFcmqZAupus4uQbuLe9EuOvitashKCNS1/KAIgE0rkvc6y30fWpciclRXBoW5NgCkPRseqQhLVOeyhRyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "1fddad938aae1263ce138680be1b3f591c0ab41c", "gitHead": "ec01cc48ac06ee07b2b56a219d5aa931f899b21b", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.0": {"name": "lru-cache", "version": "2.6.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.6.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "a26389f2e49a5586f42f3f00a430d4e8798b287f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.0.tgz", "integrity": "sha512-h0VpjAsJSq0QW3FSLM0sX4B25UIt6H9rY1Mir/1tfhdVsCX7ynWWO6PO4TDwooR5cpODkPTPy45De+UQQqBY3g==", "signatures": [{"sig": "MEQCIA6Eu+RDiTjy+vrG7pDs4r16d2pbbO9mYYKw9AE5XgjoAiBVeNCb47m71Hi/MP0yZjjTtoyW5O3LNMpJL/sXm0Rbfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "a26389f2e49a5586f42f3f00a430d4e8798b287f", "gitHead": "1763ce34f26d9d011191e7f1b3e39345d9c0851d", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.8.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.1": {"name": "lru-cache", "version": "2.6.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.6.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "9933eff15453fae1d27096365143c724e85c6cbd", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.1.tgz", "integrity": "sha512-Iax0mM/BEBb16fyjFfC/Iqn2Ef39u4nlSjN6bLw7X9VzsYnjvBKiOP6JxmQtoFSTOdkAIoVtgZ4tSykAAXRMzg==", "signatures": [{"sig": "MEUCIGkJE49grf5/bN39t8xLLOHH6J7rlESVpep0fhTlWfr5AiEAxIEQkV3lMtkLfS059xYrKl1vjeYIMU8VMjdQgigC6Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "9933eff15453fae1d27096365143c724e85c6cbd", "gitHead": "ff3dfd40e437fa619f09610f45d1ac523bbf27c9", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.8.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.2": {"name": "lru-cache", "version": "2.6.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.6.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "77741638c6dc972e503dbe41dcb6bfdfba499a38", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.2.tgz", "integrity": "sha512-bpRrwcmF2FELy0olDACeUheUM6F4vHLWHVXpBhEXoJrG5lPQ4Yr8qYDGKH2A8NVtBb6eKQ4+pU8lBZVv9Bq1lQ==", "signatures": [{"sig": "MEQCIF+UPMVWHMm4EowKplOIDvxK29KLxTjBIxgRbAlc/h8WAiAOR4R3/4deVYJQhyoSugo44bnmEzvAyQH3zlyxIrvD/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "77741638c6dc972e503dbe41dcb6bfdfba499a38", "gitHead": "278d05fcc714636eeedb3959bca80c20c19a61df", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.8.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.3": {"name": "lru-cache", "version": "2.6.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "http://github.com/isaacs/node-lru-cache/raw/master/LICENSE", "type": "MIT"}, "_id": "lru-cache@2.6.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "51ccd0b4fc0c843587d7a5709ce4d3b7629bedc5", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.3.tgz", "integrity": "sha512-qkisDmHMe8gxKujmC1BdaqgkoFlioLDCUwaFBA3lX8Ilhr3YzsasbGYaiADMjxQnj+aiZUKgGKe/BN3skMwXWw==", "signatures": [{"sig": "MEUCIQDBpi+Q0hkCehAy2e320hZmr+1AhTLXpv1Y4szMw7YfUgIgTF8eBhEOm3GoYy2Bq/Q/Y4UzSXxlRwNPHg2ZAsTEITY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "51ccd0b4fc0c843587d7a5709ce4d3b7629bedc5", "gitHead": "0654ce0b1f2d676a0cfc1f3001a097af9e7b0dfb", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.4": {"name": "lru-cache", "version": "2.6.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.6.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "2675190ccd1b0701ec2f652a4d0d3d400d76c0dd", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.4.tgz", "integrity": "sha512-HTGRj0QugHZO4kkaPcnILasgemYHYMTbg1Isy63x8brLmy2IFLyMeiHaRHYJShPFjtguSX5VV30b7bSDrurNNQ==", "signatures": [{"sig": "MEQCIGDvNk1zdwqJ/nmUpMVVQNol8mAwRQjZKJI2/cZgjN4uAiB8hxxbNojz9ph65/9cL+eo0aLVab6MGso32Cg+JViEpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "2675190ccd1b0701ec2f652a4d0d3d400d76c0dd", "gitHead": "aea58fc0a12714c6e1422963e7ebea66460ec39e", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"tap": "^0.7.1", "weak": ""}}, "2.6.5": {"name": "lru-cache", "version": "2.6.5", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.6.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "e56d6354148ede8d7707b58d143220fd08df0fd5", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.6.5.tgz", "integrity": "sha512-a07BiTXhWFUBH0aXOQyW94p13FTDfbxotxWoPmuaUuNAqBQ3kXzgk7XanGiAkx5j9x1MBOM3Yjzf5Selm69D6A==", "signatures": [{"sig": "MEUCIQCG92vBkUxJfivt6VwRuP4Am8WcdVCJnmBV9GaEeuTp5AIga+m3eRV8Nu375UPJiZJZa1ACiyIkNIRwz9pTk0dGA6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "e56d6354148ede8d7707b58d143220fd08df0fd5", "gitHead": "7062a0c891bfb80a294be9217e4de0f882e75776", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.0.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "2.7.0": {"name": "lru-cache", "version": "2.7.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.7.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "aaa376a4cd970f9cebf5ec1909566ec034f07ee6", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.0.tgz", "integrity": "sha512-qh9Iy109GLbTZhGxk+cAUy7qkxwSd+BZerHSWoiyCAyOLr5VX3fSCKAVVeT/1pGGYtshkK0rNtrqmdGuwWu+CA==", "signatures": [{"sig": "MEUCIQCBhVM9QZPb4QpvAg4U9f/0Gt7sA6M1IM1Ne72y55dbkwIgdRrpesIx/Ei/FUza+hZHlqo7l1jTHv1PFsKriJZgnFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "aaa376a4cd970f9cebf5ec1909566ec034f07ee6", "gitHead": "fc6ee93093f4e463e5946736d4c48adc013724d1", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "2.7.1": {"name": "lru-cache", "version": "2.7.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.7.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "b665391c30582a2df9c2fbb31ed50193f93b604a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.1.tgz", "integrity": "sha512-5vteep/DXRNAWd51+M2xNmZdkxFf37GIetCtndVdHfUqgr9CcmtkTKOJvMl6JTTX39xjqcbqCVNod9/yZohCdQ==", "signatures": [{"sig": "MEYCIQC/92ZX7x3OKMBAAC4TsPhtrCxQ4NsFozFfoU9dWpQqlQIhANLH4ajNqRQmviLe34j15AsoXbSj8tvgxPKfSSjUP91v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "b665391c30582a2df9c2fbb31ed50193f93b604a", "gitHead": "7414f616267078264b5459a2f27533711487c018", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "2.7.2": {"name": "lru-cache", "version": "2.7.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.7.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "73810d9a2da104d07519fdbaa3947895432c6b99", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.2.tgz", "integrity": "sha512-kyYOJPbezwHfX82vzYiogdM6rGsgMTTrNEvNdVNmdh9r30peY6b0+34V3piZrC7+KDYXTzdKImHp82sOdbTjUQ==", "signatures": [{"sig": "MEYCIQCUN3MIW6k0KaASoS2xyiAyDw9/yPIh3FPK4c8NhOBhowIhAIxivBVzaVBuoqdi6HcKoeI9dQsklGsGeb8k6fGMIK8p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "73810d9a2da104d07519fdbaa3947895432c6b99", "gitHead": "c70ccfdadc7063ea19e82db5a178469510cabad5", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "2.7.3": {"name": "lru-cache", "version": "2.7.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@2.7.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "6d4524e8b955f95d4f5b58851ce21dd72fb4e952", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "integrity": "sha512-W<PERSON>bWJ60c3AgAz8a2iYErDrcT2C7OmKnsWhIcHOjkUHFjkXncJhtLxNSqUmxRxRunpb5I8Vprd7aNSd2NtksJQ==", "signatures": [{"sig": "MEUCIQDBtc1Ngt5qhJ/4DvWU1KP2KP/7FDiokFrDH4Zw6QBuDAIgYy2zEjlf/Wv5XUb9DTc+tuhqNRpbJTUEgMujVzRolQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "6d4524e8b955f95d4f5b58851ce21dd72fb4e952", "gitHead": "292048199f6d28b77fbe584279a1898e25e4c714", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "3.0.0": {"name": "lru-cache", "version": "3.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "65704ca44b10718bc401ba7e0c1cfb5b69422d5c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-3.0.0.tgz", "integrity": "sha512-cx7+qk1tNz/Fd8ljPkosK36Il+3SAlofa/Rxn8X5u0mfZo+Yvt8YJD+vpaaxhXmQm3tE+jYTJ5AG02efOF59Qw==", "signatures": [{"sig": "MEQCID+FxCHLJZTmHfVKzRfp5IU/MgZmD8X3yjrV/NCXYw+lAiAKh5Bx8f6sfqxJ61RXMxmxEfMYIJUR0qZ+c4ZKeIu++w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "65704ca44b10718bc401ba7e0c1cfb5b69422d5c", "gitHead": "487ab824ec8515add9f4dc78ec12b77ea0b51d0d", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "3.1.0": {"name": "lru-cache", "version": "3.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@3.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "695666a271c1364350e8256fee8657fb90a6eeb0", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-3.1.0.tgz", "integrity": "sha512-ctXgysQ+BDobe8dANTYs5GlRfcY+WtFuaXPs5erVchOv4ue5i/s2I+3fyUFKoaebxn9GadcxwqrzjyYrp4Izsw==", "signatures": [{"sig": "MEUCIC2vJ1ddYfqe0Jed063FJHJ2cK3PesLmLrsCHX7DCT9HAiEAv4/V9IRhZV3zxr9bC9K+FBFdOO3So+GP6hx4LBMOrdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "695666a271c1364350e8256fee8657fb90a6eeb0", "gitHead": "f729777fc0af1e5c61d0c724fc8c0a56bfcf6603", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "3.1.1": {"name": "lru-cache", "version": "3.1.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@3.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "62e11a908886c69713d102ba7b8969c8315348f6", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-3.1.1.tgz", "integrity": "sha512-foKHyugDDIuZpVyueQ9t5O5R/cc+0DUM1dOhn0TIjafpYJMj/jmm8bJEYLm5gsmzOf7oUQUCaLUM5Rqz12kTrQ==", "signatures": [{"sig": "MEQCIH8qwS3qaYHQlg7cmeOLoHPA/8ka0Prz8lnPTqxOq41MAiBbBoMAjWjo9xej7vwPa4DZKzZNyg9Eby5Jkw0ixOy0gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "62e11a908886c69713d102ba7b8969c8315348f6", "gitHead": "bdd31947533d1d91b17618f1a30346bc3eb9840f", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "3.1.2": {"name": "lru-cache", "version": "3.1.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@3.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "2c108220c9a73d4f516e6f3147c2f8f5a8eb0296", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-3.1.2.tgz", "integrity": "sha512-Lz/U9328AZ9LzQowUMxeqtC/KRVluT1Eja39HY9ENmOf+JxOb0V0Ft/AEs3Ns8L+Lg21ZlnjuJoHXrnIuVqgqg==", "signatures": [{"sig": "MEUCIQDp8yCUHd52L1WWZhauFe/Z0FvdqniqCgFlwbQTC6faswIgY6WaR/P96vCbmuU7fwQJ7EvAgNgIiCYkoZACPevW4Gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "2c108220c9a73d4f516e6f3147c2f8f5a8eb0296", "gitHead": "fa470b7fd44e386defb6be5fc3ae61906a68cc6f", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "3.2.0": {"name": "lru-cache", "version": "3.2.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@3.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "71789b3b7f5399bec8565dda38aa30d2a097efee", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-3.2.0.tgz", "integrity": "sha512-91gyOKTc2k66UG6kHiH4h3S2eltcPwE1STVfMYC/NG+nZwf8IIuiamfmpGZjpbbxzSyEJaLC0tNSmhjlQUTJow==", "signatures": [{"sig": "MEUCIQCTMSP+Ualrq9erVXg5eeqO71baGSDnl3vKZM2M8jZbHwIgVJt8HHJvHgWdPqWqmuVNgZ4BCHY7ItKgIkhjzqGT1r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "71789b3b7f5399bec8565dda38aa30d2a097efee", "gitHead": "50d2d39a6649f1165054618962a467caad861142", "scripts": {"test": "tap test --gc"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^1.2.0", "weak": ""}}, "4.0.0": {"name": "lru-cache", "version": "4.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "b5cbf01556c16966febe54ceec0fb4dc90df6c28", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.0.0.tgz", "integrity": "sha512-WKhDkjlLwzE8jAQdQlsxLUQTPXLCKX/4cJk6s5AlRtJkDBk0IKH5O51bVDH61K9N4bhbbyvLM6EiOuE8ovApPA==", "signatures": [{"sig": "MEQCIHy1nmo8pjvRL8NqLSjXFapgwWY7ZCe9TyJxcWrDU8jSAiAO8K0kUZzI9vqT5/jq9MkrBDJYzcM7uNmtmtbgg8Anzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "_shasum": "b5cbf01556c16966febe54ceec0fb4dc90df6c28", "gitHead": "da374d4776aaef443765b43cb3617e09c170a5d5", "scripts": {"test": "tap test --cov", "posttest": "standard test/*.js lib/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"yallist": "^2.0.0", "pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^2.3.3", "standard": "^5.4.1"}}, "4.0.1": {"name": "lru-cache", "version": "4.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "1343955edaf2e37d9b9e7ee7241e27c4b9fb72be", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.0.1.tgz", "integrity": "sha512-MX0ZnRoVTWXBiNe9dysqKXjvhmQgHsOirh/2rerIVJ8sbQeMxc5OPj0HDpVV3bYjdE6GTHrPf8BEHJqWHFkjHA==", "signatures": [{"sig": "MEUCIQCaq18b4co110VRt/RIa1T8tuhNNH/y0JMCrNxuk4EapgIgLPKoHASI9wwAp9fGzDhS6kLS6vE41iJCpzOLJDwAacM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "files": ["lib/lru-cache.js"], "_shasum": "1343955edaf2e37d9b9e7ee7241e27c4b9fb72be", "gitHead": "6cd8c8a43cf56c585bdb696faae94f9836cb9e28", "scripts": {"test": "tap test --branches=100 --functions=100 --lines=100 --statements=100", "posttest": "standard test/*.js lib/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"yallist": "^2.0.0", "pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^5.1.1", "standard": "^5.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache-4.0.1.tgz_1458667372415_0.8005518841091543", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "lru-cache", "version": "4.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "1d17679c069cda5d040991a09dbc2c0db377e55e", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.0.2.tgz", "integrity": "sha512-uQw9OqphAGiZhkuPlpFGmdTU2tEuhxTourM/19qGJrxBPHAr/f8BT1a0i/lOclESnGatdJG/UCkP9kZB/Lh1iw==", "signatures": [{"sig": "MEYCIQCmSO2l/oFVSA4sBxmSAlrAImYODkWRDAhgwGKRmMxhFgIhALnVcH6OWWi7UCKJqazDrkoxsbXRxpXfwf4qBgrnDdbB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/lru-cache.js", "_from": ".", "files": ["lib/lru-cache.js"], "_shasum": "1d17679c069cda5d040991a09dbc2c0db377e55e", "gitHead": "f25bdae0b4bb0166a75fa01d664a3e3cece1ce98", "scripts": {"test": "tap test --100", "posttest": "standard test/*.js lib/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"yallist": "^2.0.0", "pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^8.0.1", "standard": "^5.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache-4.0.2.tgz_1480273800672_0.31606275402009487", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "lru-cache", "version": "4.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "59be49a683b8d986a939f1ca60fdb6989f4b2046", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.0.tgz", "integrity": "sha512-aHGs865JXz6bkB4AHL+3AhyvTFKL3iZamKVWjIUKnXOXyasJvqPK8WAjOnAQKQZVpeXDVz19u1DD0r/12bWAdQ==", "signatures": [{"sig": "MEQCIEKWaq6AySPJBJ4oaveMzGH4sEWPQkdJZ49CQ80GE8gFAiAivRpvLhcEBgthx/3YsAL9j7/rHFZ8AVpgYQauC3g8rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "1a77f87d74b46715b80acc3f6b44c12d030e9902", "scripts": {"test": "tap test/*.js --100 -J", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"yallist": "^2.0.0", "pseudomap": "^1.0.1"}, "devDependencies": {"tap": "^10.3.3", "standard": "^5.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache-4.1.0.tgz_1496771655220_0.868791145272553", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "lru-cache", "version": "4.1.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "622e32e82488b49279114a4f9ecf45e7cd6bba55", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.1.tgz", "integrity": "sha512-q4spe4KTfsAS1SUHLO0wz8Qiyf1+vMIAgpRYioFYDMNqKfHQbg+AVDH3i4fvpl71/P1L0dBl+fQi+P37UYf0ew==", "signatures": [{"sig": "MEUCIBDJphxrBxrupe2xxWX8uMBA2gD9BGg61RXALU68NTw6AiEAmzcYeuSHw4o1VhrYxdEsm1uCS79VY2ibv/jodTnedgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "e992f26547a575299fc8d232580e53229393ea7a", "scripts": {"test": "tap test/*.js --100 -J", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"yallist": "^2.1.2", "pseudomap": "^1.0.2"}, "devDependencies": {"tap": "^10.3.3", "standard": "^5.4.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache-4.1.1.tgz_1497150046014_0.012352559482678771", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "lru-cache", "version": "4.1.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "45234b2e6e2f2b33da125624c4664929a0224c3f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.2.tgz", "fileCount": 4, "integrity": "sha512-wgeVXhrDwAWnIF/yZARsFnMBtdFXOg1b8RIrhilp+0iDYN4mdQcNZElDZ0e4B64BhaxeQ5zN7PMyvu7we1kPeQ==", "signatures": [{"sig": "MEYCIQCYsTsw+LGV7h72h5Xo6xuqb7rMOUSoFCEPKXEy2KoCDAIhANTbSnR8ICB36Gx6vKn3M60Ry5ciE55m/W7BsCRk4ljv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17609}, "main": "index.js", "files": ["index.js"], "gitHead": "2a95eda2a22b281f3253304231b2bab4432e2f8c", "scripts": {"test": "tap test/*.js --100 -J", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "5.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"yallist": "^2.1.2", "pseudomap": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^10.3.3", "standard": "^5.4.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_4.1.2_1520531913886_0.19100220467390994", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "lru-cache", "version": "4.1.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "a1175cf3496dfc8436c156c334b4955992bce69c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.3.tgz", "fileCount": 4, "integrity": "sha512-fFEhvcgzuIoJVUF8fYr5KR0YqxD238zgObTps31YdADwPPAp82a4M8TrckkWyx7ekNlf9aBcVn81cFwwXngrJA==", "signatures": [{"sig": "MEQCICU4mO+CH2vmK8GwZMDB9zne03hHTkkjn4VPgMq7qDMNAiBjLhff3HX4RU1wHpo/KWSpUXyigx3E8MG07aGIgCyfQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8N54CRA9TVsSAnZWagAASnIP/0DTXmV6eHfBolsGsEoI\nXAMUXMcWCyZvr6DOPJQCr4yk8dLPM2XSLpbf3OHA0iL5pKmEcsnfLwpLnVkr\n881WoMUsvve2wQg0vFopy1nq7HiJWm9fIwpWiQ7+ZzNPSKJZ4l2HyJoqhgf4\nwtf+MnFS9puiTnqZnJDS4pnw3scnnKzj0xeGBzV9K45ZH9i20dISxd/WK/Og\nJg6J1uqnnCmLcRSgDhwbQ+mtwXFd/aG0TnB7Cj21OcAScf01Z6NiVxVbDzLu\nnzOoQfh7EiyFxw9Mn7gJpONgIprLIliAdpek1QNuM+jx1etx4EOCAYn2S0qx\nRjaqqAOqosD7jGiCByWzl0iyCvGYYKs1796SmK9ivnVRpae7UDKLSpS9aBqw\nYIAnQyAK+BHBP7jvKvplvNxEvwGFxZyXQ7JMI/0dGQHeKp4wUStIMfqykRST\ncQuU9f6qhheDGEQshUFR4pC96o5kKsHAXnkRJQUYYaLMn1NUO8PtC7kbs6Oj\ntdTCKM6lo/JYthEfe3rBbPkLCgXoD25HGpbH6o4PxfMesPUSDmssYJ34ISLB\n2wLkQ5ciw3/OcrDLOHPAgykTpIjK+rt9VlWIXCKFVz1IDqmC+DhWh7oO9Nc0\n6xrI4e3njcViDR9ptV2dsrUileJFcLStOh92PQUYrVqoedXtGQt0tt2ptmu0\nkR0v\r\n=llkV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "gitHead": "9c895c3045b4decf49bbbd7d5171f0be2ff86039", "scripts": {"test": "tap test/*.js --100 -J", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"yallist": "^2.1.2", "pseudomap": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^11.1.4", "standard": "^5.4.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_4.1.3_1525735031368_0.027008230747383788", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "lru-cache", "version": "4.1.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "51cc46e8e6d9530771c857e24ccc720ecdbcc031", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.4.tgz", "fileCount": 4, "integrity": "sha512-EPstzZ23znHUVLKj+lcXO1KvZkrlw+ZirdwvOmnAnA/1PB4ggyXJ77LRkCqkff+ShQ+cqoxCxLQOh4cKITO5iA==", "signatures": [{"sig": "MEQCIDyeN4zo1oY8qcl2cwkl9G8lnB+NqpMyiaAJhWoxvZtIAiBBIIlh9OnbY5v/MwsbQNkYXsBYINvbNxcMr008XB+D1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9KNRCRA9TVsSAnZWagAAe50QAIbgbQAfTApPu0BHiF3k\n+X37PcgXsnnzihuIcKoxkXLuZ0UQGw9Aj4XPXSrYJo2cQlLBePZ4AyfKxHD9\ngfJDGLUlHT+CybKdk1Af6YCQjWHrnFk56og0N9P12+ens+HU53mv+WiCb4nz\nP5Sm5E7GbWJk8cCDM6FM6aVHnPAoUwbxL6EkQNXCz2tJT4y9QGYt8TKIvbRX\nlYDDlPj+mBqkXPUsko/SCLOtdQPdxvXZ2t8AKfe1CR5vjbzRE6E6eBuVWfE9\nZpPRay+S1P7r+irxGuF7Gp/eqRhtt2FBnrKCiyaiUIAnicbZCABbhGOtQOQH\ne6mAf5opVKL7kYa2DmZtxCE/4sTGY3yaC6ER2Q1/uyZSQJbQkTLJ1DxzQnG7\n+kG3PvTVcqPMC62A2jqaCntM3stf35+fSbGO6TNvpP+43b+HsilcbSRMcus1\nLU0xZNP/npNxp1MpIzccPJ2PxztsaoQI0/iNBFTN+LLSN8ITfRXk/XvUsuSI\nEvpxSJ6QO1RnoZ20VBodTeDAZM+vc6deFIODeF3d0CYx9jjcixqXHUQaaAcL\nSySCXibQCTItxpycwxYkoNgPHie80sP+adSO9tM0tujOpYi+c6ICI0bkrZwu\nXMM+U7amqPd/D4/4oZgowSEJUOdP0gkll+270XBsTQd623t00TuM1rQsDJPn\nthdz\r\n=J1WG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "099689df17f0d5de2e15a92e83262052772649d6", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "lintfix": "standard --fix test/*.js index.js", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "11.2.0", "dependencies": {"yallist": "^3.0.2", "pseudomap": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.0", "standard": "^12.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_4.1.4_1542759249101_0.8740159848771953", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "lru-cache", "version": "5.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@5.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "de3d12fb64e4225b8d3eeda3c738f7d6ed007473", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-InxIcrhmeOXzY3n557oYAKV9HNTClbNAnjqizOci/fJiTrMa45iFd1OavQCIEyiHZNxM11fly2c39EH5st7ABw==", "signatures": [{"sig": "MEQCIGWbCYea3AKdQhssbjkfARenxvSBpY/lTuLVVVXtEDmfAiA/HA6+lTNsbHK/GIExppLFkWHt3HN//1ODkSTG5Z42tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9KrvCRA9TVsSAnZWagAAvz8P/AzH1/6DH2eP8ukWz0z9\nCVpr05UaSnaEsCTLl93D4G0U8UOfLl/ATNhGEVqGIr62w+vilNxuhzJ9GaKw\nTVG7sphGKDlmFcL1BK4bAy2AaGB4LHKFIpSLRHMN4k4RYTW7+5OWDdfRGwCU\nRxhNj9cTWcZMqclapxkq5gTfVKfWoZMc//h2n1Ny1mFlh5I1R3TmE6ptrIY2\nyO2vvKAfi5qyjAATzImVRe1AEoBxR1pLbYsh6mnui2sgTzsLnH/9yHliG2NR\n09ntFiYBb+/rTLH10+5G933KrJWwGv16u1sn1JDw50LBa1YdkXk26uHP4mfy\nbMOrF05VuL9cZpezkBl/EmSttlx92ZQqYtaQ7POM43y/s+U0Q9QHaZUbz0j6\nJT765pKqM/eBS52snNhouaxeLYYOcEV0LC2j8FWM3R6VKzcEEHED+Y/duaBN\nxBWgnZcP2VsSkwnyDRKU29dOCp4C+ogY2DE/bYVB3r8dMdoz7OB/SPJCN/Qc\nOo0K7czaOpAYFfKOCh/GnQEsgC8VBszTzRuG52ldy2unrORDa1GH7h/jvTQ1\nkg8LrkERA9YI47fz2nLjb7qz+5uXJH2RtpxHJak/lCnq5X9WS0lhJ9UxAc4I\nIHKVPAR1ScxGBXaPT1gfoKgBfduItSVY4nFNDulB8ItNEyJkMJnRI3bFCsJF\nKFrm\r\n=mplu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "989d730321ebb338ff7aa434d0249d2f0d97d709", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"yallist": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.0", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_5.0.0_1542761198629_0.7454856979770508", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "lru-cache", "version": "5.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@5.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "44999c73102eb665b221313ac80ddde9bac287cf", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.0.1.tgz", "fileCount": 4, "integrity": "sha512-g7SHQ09RoBCtU0OxKr5XeyoeYuRXD97yTO1YOuvPeSzpbKtKVh5hqYUJGNvTGsxLEKx375o4irDnMZw8F8+kow==", "signatures": [{"sig": "MEUCIGQXswT/al9fj92uUDWvhKhaSPsLnBtfDzeJs2/nSn0TAiEA21IR4/ulHktVxgXIKNDPuiaLmvyUQlsr9QLPlVpNuZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9K84CRA9TVsSAnZWagAAUH8P/1rSge049REo5fxrNaVh\nVZn94c+5/LG6p/4faGUt6U15EClVHN2WmVo56sxJC4QZQBTCvhUHJ91RM3To\nYjQUVs6IfLUE41ERHEOX9AmMvS+7PIvSmSUl1Y7/sVpgDZ2IiKxS/Ra8jRWn\nADiwwLM1klg4VKT4uvnrAQV/TAaSSU1Evwop1P3NZHcSxaong7qa1urHhuSc\noD0jXaEz7TYXzJlWQjFN+j6oMLo2lzmBE0lpezHJH6p1TiiGjpLMomWpHbdw\nO2PFzviLDdRW4ZbtfqyLOszFLqp/YpcT/5bzI8dFkVefjam22oiIDgrVrPQ3\nW33PUGdJhNkZwyj5D8dXpRqA+j0DtZezlkk4X6XCpRc8MuS0I10SqIdQ9ApW\nRn7gnDvUV/JHkb4DCwVIIA6GzzWfpB1u14dcGmFJLlNhrwv3HN5wonvRVgH3\nMx4/qeVPyVSUdyM+yyV6KSxM3woNqiy2nTccveRabwWhDnE1jyjJ7kUqRUrH\nx9FDNDXrY+3ciOKdCxM41/zKb5fFM/fsuNcnqZWSTELVSYMjJLERotqbxuGM\nqZ484/bbNA5OyRi7uG6bXrAoRAZahG9Utot6uJukWjDOjJwtOuUArzUdz40z\nlcr91EL2Y88zw4M4EXYjjLJOn8mrF63wPzNoBIXJbP7nxJ2iLdIqepaU6zUy\nxHve\r\n=84q6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "54a9dc48cbc1a3a838e80d6f400bc5efb2bfc666", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"yallist": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.0", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_5.0.1_1542762295972_0.4767621080205895", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "lru-cache", "version": "5.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@5.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "3f6dc53a2123619399699210608cf144467469cf", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.0.tgz", "fileCount": 4, "integrity": "sha512-a+QQK7jJaNExd68avUHSF8nnY6xDJRPlYKn+npF1Xv/QLI3Hs59vJpDtIhtZipvEwgcqvefDbADsgVfKOacmDw==", "signatures": [{"sig": "MEUCIQC5FtWCWeuZSRffvPAJb+RjOyNK57Xx5EJRP22u4Ls/zwIgEqeIvdYFsht13dju70OdF1r1NO/iE42OqBQtFiW6HLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9LOCCRA9TVsSAnZWagAAVVcP/iR8blR92B/b0h+/61jn\nYaaF4yIvm3HyxMNPpgXVtBJn1T65I0juR4EGHMcdgtDjAd7IojC94LdjXOJO\nZz3Sx3EbY2XKc5mF7gCR4xmCOUaCl08vl0TKclsqOYB0+brOvdwRs+Bd+A/E\njxHQyYJwqsldnDU7hPn/zwWRkvkOQ7x1seKqDHs/T7GCau01+Ywm/7rQbpT+\nzvO7pl+3zAagZv2nMh6lcy5fHJsWo3zip0Xr16GPJEEp2ahYjSAuiPAJQkX8\nhMpFCDHou69j1Jw7epnZedaLhvvhNNA9ZI356S53AwgJN4pd5Qq096I7TDVx\n3PTBx93fHmtcuQEtLjMAa5R6CeBH8gJcd46HWKkr3tK94TAJ3GxmXiJNtLLx\nsNcQFICHK4YWehC++8Qhu6Kz+828Kgg5gKNOrOmF71P3e0faeN2Yez/7XahS\n5Gv3blxnEp3g+K8n1ymCVVq6GA0YNAesFHi/TQ7yW9dTdf8kWpZrIJupMoKS\nTAykiRfBZO8EHw8VwLTzTreCjiFPfUh5kPTYJBN6CXZbltAdLgQg6iNgcRxt\nPCzS3bwPzWrb5j9fQlM0O8wFgpSzDmBB3OHI3eFPWKkXj+eKQZCcwYNGs5Al\nfn3g9XiAeQQDNZBN5BuJxRAaE9CKPWAfbibV4ywUpyxAn1hYhQXVIbyH49Hz\ncTYA\r\n=s5Fu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3c2af5a71cd561b6d76693f801af41a5047a90d2", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"yallist": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.0", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_5.1.0_1542763393628_0.982190355130331", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "lru-cache", "version": "5.1.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@5.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "1da27e6710271947695daf6848e847f01d84b920", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "fileCount": 4, "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "signatures": [{"sig": "MEUCIBILagLs1MuDQ/Y7qp8O9PXXdLhCeOk8J5ujwiwVsYLkAiEA5cMZ+LEnDglkeaNzSKY8MvrgqE/zh7RfVclS6y/+0dE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9LiOCRA9TVsSAnZWagAANvIP/iNd0BQp0dzx3440jk/D\nQK4yzdNKtv688NqB4JXeoMaA7945hmh96AvrDqeVYd74kp28R8KZ+yln3TCA\nlOqzraf2kwT2giu63CMfACQMBH8u5BjRThEuHdY+S3exJsnkRisoDhyS/7DJ\n0I4gVdRy8LMfwO+UiTkSysmOTb9Kz4BbNvCCUyyB9824oXuqECKibJoaEeTn\nn6O0JuFfQUygt9di18iozjq3CmO31pq3Dht90sTb0pLmChCMgg4m3dcg6g2H\n8CCO7/bHg76TtrC8eb1lkJlb2im0PjZN3OYJ0vY0aKQW6P79V18Rp0UGgK3x\noO7hZ2xiPlgug40kBGziPeYsPwpwjiMMDhnExkkSQarxf6NtuJ/Sj0+tTWza\nOXreTmMwt9MmWvbhVDD81svZ2YgZb5VfQzsRyE2WRVIJZwxhaQ4x8dlJSq+p\n3JL4K9qkcPPPWV+JJNDdcSTQVuw0deiUOsqSRMZD8nXvvN98/ugTf9rCvaev\nF+YyxIi+5hYqNYO37tG6WXTU5ADFqb1lf/7agpIAzF0pyuHkUEilk707+f/a\n0vZ+sCp9o7hHRpelJjHl6Cy6DrxE92SyHHHZ8rZmwupPkAS13lyWIRuqqBel\nJKtTxc9SxgxgwWJnLz1uW99gjzLMcXA/wN28oamaKtFpuwlt608NY9CES/Hq\n30nI\r\n=Zq/B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a71be6cd547d28b24d28957a15b10cd3eb34555a", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"yallist": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.0", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_5.1.1_1542764685228_0.235338811270295", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "lru-cache", "version": "4.1.5", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@4.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "fileCount": 4, "integrity": "sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==", "signatures": [{"sig": "MEQCIEl9zRq0rk/wyZSxwWMPt3xZgWMlhZnjdrO/Z3i6ImV4AiAcOsgAIOH4Ts+Sdje34lTHKIyLc3JZBLdF8Uf4cxVSHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcACavCRA9TVsSAnZWagAAApYQAIClk2BJDU+mzS2L6g/6\nWU9peKlytceanWLW6zivfwQc7E0gNOb0NXY8XKFOxAibBoYStcKvxTqPLR53\nCOowz/CMKFHnyoZd+rPvN8Tt6So58+sk6k03W3M81vb3IGB1Sjx5scSfjHS6\nIzxR/BSRJ2HIPSfvs397Uxm82YmZEVLaDE0C4bPzXq7M8FWRKW8GV16InLoH\nWoIh/XDs0Q2dSerWlI96HvoE6UybkY/kfkpL3957AAUap3vTj4N0bDl9DKt2\n0lcbm/Ba//zYLjbXu4zkCDNKgPr7lWioLRSH0JI2ykoqlDsuz8GEqye4dvc0\n/SkIpj+DR0k1qnwoLFQeKqCa+bIZO8+y8zqKqauoitoInhd73hZR57QgaThF\nc0BWs19VYfKzG1/OVzgHrFxJwP9fqiQb0r1oJ3jz4HI/4z0T4sY5fvKGGRRa\nip4wOeLr3ASRBGNGkH4q0PKbciZtZ08vt82+vdknDEoGc/ld+HC+NVmUlALF\njveTwVK5jB+1iOv/r7QW8Y2bhA3b0hyxTL8aAozG1TlbHhikZ+Ueq4wG12mn\n/hJuq+YsF7eQIK6Ifn8+V4iwo2wGzCvFKYFcd9f2qHQVYdGIUeAXsDL1JNeX\nW8lx9eGsbFfX+xzLfqw692GhCOKi9QTPA8Qzeim8af/wnuBMUOqJNxysLaOT\nb5/m\r\n=3dPc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "57658f575607a44bb2fa38dd0e43d74835e42dae", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J", "lintfix": "standard --fix test/*.js index.js", "posttest": "standard test/*.js index.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish --tag=legacy", "coveragerport": "tap --coverage-report=html"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"yallist": "^2.1.2", "pseudomap": "^1.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^12.1.0", "standard": "^12.0.1", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_4.1.5_1543513774289_0.3522451077999784", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "lru-cache", "version": "6.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@6.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dist": {"shasum": "6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "signatures": [{"sig": "MEUCIQDH1h04jqNTVbunN2inimDGmZDFw6TYP2ncxfDMuXattQIgJo4b+w5Z8V0GYbPSr/i4M67tO8TctMKFGy77xlJ4/Tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCQ7bCRA9TVsSAnZWagAA+F4P/jdlPS14NkOtYnSPFZMj\nDC3wsaM3hYlUtdiVJKDOTjhKdcgZ6V2FgXDq716+5rbaq5t9Von4FeweMgp8\nnloQVB1CRctlLPIZRoV4Mje9K5rA4Utb0lYqQk52SIWBN+xJDyyefZneKPnQ\nZSGaXipOLLYPF59rBaCvkxI/Sx7NKELQeXZhGeZWgUPNPejFC+qseoFfugtP\nVk2AKuejsSsdzLbdxADE53k720y/D/biUK0cmQcV7yTAqo/XaxdmRNwr34Fd\nNJ2dbm87BEjC2hGs4WS6OhoPFdIN088c6vrilKerKgrDxHCzHJi0E1oZ7IeX\nByhOLk03SScXYdmibzm0O9iWrTuBEJm6toEToJhNziv/HFTgDlhm3OajDHEz\nrb1cV38QwBcqE0O8X5uLQ0nZYCMczliwWKwMhkcsXmEA7wsiObM4Y3CVnpGl\nDurnfCmoAG0x+8NVAKeijXPgl0T/7TgtiByyfuH6kukF+URg0VYBbqFFz7AP\nbOxA7RZIDNaLeYjn86iGYyNtXQPwPtl6Q3qy7i4YlncJsQb0pDqgp8Qn2ib3\nYnGOXf+FxVYwQwGv2OCCWWCT6aT2bQk5SS1qaEOrMHNKTN5PPrXa9oXQb7I0\nnNTAy9YBjlEwfW7fHvX6xa9IH3wDT/P8MYmYHEwbpH6uB2y2u/zWCuROQMzo\nuAr8\r\n=B5wU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a71e08008f2d25442426d0d26e8133dccc2ca9a5", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"yallist": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_6.0.0_1594429147159_0.2608141604867613", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "lru-cache", "version": "7.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "50e43c1dbcf17eae78611d8abc0869847030740c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-7YrVYWtAmT6LubLqn/EdQrATm/DmBL10s7cDasBTvCgtAtU29UcRtj6MFi7ihmFOlAwOazQLIQq8pHQylKRhOg==", "signatures": [{"sig": "MEQCICOjsOReoA8zPWc0u57TUJ+fQ4ly/I6xP/bqJYkWqhiZAiACcGclqRerYCKVSDGU7M4W83Yi6GRlJJ/CkHrldCzBvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAb1hCRA9TVsSAnZWagAAd/YP/27zAvQ9Xp1/yYbOJ/8f\ndr36JxVozkq7PPCr2ylVR8UveQbrcPERNFuzIL46YUtFvSxW7HNAbqgttFur\nhU3/gZutYa1/5lcTxXo5I94P785D+AEkLnZU8NNPG+TOy7v/6fXF7IlHY/Xe\n3PeO7MtXTY0KPQBus6C1ShUj1klglLUuZZwrOvYGgLHl491Vyrw7DQIXuVu0\nHBhJGohlLMazz/NM/HUgxXec4ztNSU/fMaDeBu3IyH+gXhe0uVbk5suII4AW\ncGpuc2pPLYDQPVX51m6FPh6mNT4mBBzBNuufQAoSMj9UpWpEol8lCZQRtc8Q\neyIqfu1kBhEjdYnJctMMYFlA+NoeIRWa2Orkvq4oWcZbJgPPJsbVookiUSGb\n66tGBox2bTZ7AxnRVrv81aHi0ST+t+SuWNbMHpBJHuafppEhiR+KSvvXk6jN\nn8iiOeR45fqhTgqkNaYhTtRPOqjxCEhsdPhq0pLjyjEdh5KS/jKa1a5BDAUB\n68lnZ+L2YD/gvgZ/BivyurahVuipMkTiqctTdD1XNCT68rkLuy/vaFpIXKOc\nQOtTDdOQEudXzNJR8aYBJan9Nz2kih0iQMGrUkTMi3GPPIvK2XKOD3N93zLy\n3bPIYsT3jcy3PcKg0RPOTHAD31KEuXqKP6/FKNpkM7ep8P4tkjiK6CD1CRVb\n6h9J\r\n=Dc85\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "a9b57257c36a6746eb9c7d216df01f430701ecd9", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.0.0_1644281185634_0.755406333862241", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "lru-cache", "version": "7.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "aa4d4a97a176e1cff29504ce125ede5630078aae", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-A6xsa0F6rWat7YXjt0gOEIK6uEjP0DBet4si4IY1+vkYPmcfyfTqdjU8GLRWcoQU3x3YlQ8m3kUlLfYvlg4WRA==", "signatures": [{"sig": "MEUCIQC9usAbto9mHzf4XbfXE3D4aop0bAZa6s1yhu4Y7VA6owIgC2lKmlPiVhALImHqFPjBsAzkkhX/ZJcGkFAeynlB/Vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAb+RCRA9TVsSAnZWagAArm4P/0mKFLkRYTeaoc3EhLPx\nhGQLzUNh02K/X/WY+qSa6rdB6qfeJwi+rFymrBSgHpMWEjyUHIAenRBFaQ71\neNA0lWDGwlk94MW2HHWB1aWPIwm4MY1+KEhtt5IN4yjDhjM2pCrrv1s8DWQk\nOfx7wzXZ1qvJVG3R40AMUDwM0YsGHNlcAGS+pPyipUYgbXdizR35bVjxe6Lt\na5qzt0YJB9p5LFWaNvI5WEYHHwGgnqsCfRkbV/eFwC+FWSiri9jhAgoXXwUm\nLk75nwCG2moQB0HKlQpXVs74rv6dN0RnVkUSGi4Y0NjehNzsqT5EYX6eRG/d\nc5j4hlDFphqThWFY2Ds5M6Ob93aqoVBkvQmZHTkCVhA9clNsexD5fB7RGoe5\nHB+xKFCDecTjBFUHHUZEyNYRvsDUuNxzsJujT3HaGOwfW35KYtR3nsFoCd5J\n7MT7XsQtXb9gAh/k7xbnfnXCvvj+qYpegVdpGNhSjbp0Tj2+0oeZyU2ifud3\nE96DIpWxLdnY0LGZrsqSShRFM5a7bFuslzl+4ikcQP/iJNlO0fa3Jc2FMp7+\niyqYCjO/axX/Uf0iU3J2JxxwhBrf2B647IrQ4hztP5Os/W2IEF0HOQ4mVTf7\nzahUvZulLpDS7t6yQSkkFnvjsGsjk8Zjw1mlgF88bARy4vFlRSlKaNhme194\n+Z30\r\n=doDL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "51cda550d529c460b392a3c915f9d1124a5f878d", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.0.1_1644281745791_0.5842864070845559", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "lru-cache", "version": "7.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "980f5b1395ea563db9fa075c033f109e28711023", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.1.0.tgz", "fileCount": 4, "integrity": "sha512-nurMcfDB0KMoovula129I+tgpJZOy6goffYyl6L5Tnagg1HJqgUFGtymaUsmPqlbnmeBOohqngEiLZ39VMBwbQ==", "signatures": [{"sig": "MEUCIEonysLHmKaswWyOvezIFQk2OyYHn+1E2C1tLY/FDwa0AiEA7UqCwouulThEULXAwS3q4TrgvsvqXIRA0wP6hhjKWNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiArEkCRA9TVsSAnZWagAAbzgP/0SRhW1jLLu5s4UxjnqL\nxnnXjlzFmBADGhHjUnsKRSpuRwtuFCuoIYVl05d0N5PXm4URMdtp0S220XeX\n/QHRDxf0KoXKiRYKjIjJvIOK9RqsncgPjrR4+H5AOv0jlpq4J4Y2FXvAqqbN\nff6Y5zKZ/oSV1E/P5+97yEVWjZ2BNWDwmNfdIM7TBPjT2ceRXUdfvZm0Xspn\n++Zd6trnVfyZhb9f+ISh8rhI/bdKlh3ygUQ6L7lNlk4n0HVsIoYZyc3vxQH2\nBScAp7nQVM/hc8FN1KM+YOlkLAgdG30dxiWErnWO6pLLtqmzI+s6Niu6SIwc\nyuv64y7+JubcNbGXUhWn1xweQZrJAYHc87ERizPMvq+5LN4E+h/1sIWyYicG\nEDW3/gr0+d/e8hOJfMN02cDm/VnUUnZoalrltYmT5YVtt7PGNFoYiwfLBrQz\npJ6GhCezHLDhm7GRiHwCUeJ7XDcusbXoGoT5bs+NL31M8gaB0AhHIuzV/miX\nKBbvI/GYgr7fSD/7Tp1kFm/XrsHCtgumgF1aLoGQyTYdRyh3gAMIFuFh126Y\nDLqIwjPFXwc1Jncsdy0lbCCA2bfdNZfeCBCdMpWt637HrvcuIC9cMWOZc4Nd\nXE/Lyu78zqPxboLtA84GTToe6m0f6bTv9Hu+cODP5ndL9Vo2p9p8imNJHbve\nkke3\r\n=NENQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "0ee3152612b96d6f5b4b64a127b93a8676492c8b", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.1.0_1644343588416_0.46202560296111983", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "lru-cache", "version": "7.2.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "daff6b477c30eb1bcdcc7dc361fdc1f913c57691", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.2.0.tgz", "fileCount": 4, "integrity": "sha512-Lurb8qd5p16b50M9YsEBZGbgE3ZlatPkMLITGu/8HKRloMhgly88m5s7kyByu0bNn+e5C3LyyuScTJXk4nn+Tw==", "signatures": [{"sig": "MEUCIBLrzjVngpT1IfRmfb5WCkyxSt8I+LqD2ohux6gGtrlyAiEA5QOVpEKFGYdoVt5hiyxddH+5eqSjUJk0Q88ApwStE3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAsYACRA9TVsSAnZWagAAvrEP/jdo03obrmX5XvChLfbg\n1PtlGk++PgoaMtGDQJDypLrx7wd6I71tB+rOXjFg00AXEgX5P6CO253D9V2U\nvQ/gSDaJqfKZAZrkNxdE9IKh8tmXohiAhDjQ8IK0AAjl2Y4Vu0dFhAaePNTp\nDmgBnxNI7TwwMGeKGEw68cyHTwwhRWsSVMDulvbB1Uk8hMX+bUr7VNpciGCF\n6S9HdMbKp4KcXlVAg6kKmTkhH5IUJNCrUIh94hPuyzX1GboB+BTPic12lkfT\n9qqSjmJTshtzDo9YKDrHMTvL99YjlPYunGbYGHNe03x3IiQ6LgIK15oKpgud\nNpuHDYtY4eNW5iaF3t1on5CRr7TPsVEMOJIghF8D1XhjXodJVIJY09J/XKd7\nRDiyihDphjazyfk5ocki7fLqpfionZXbrQ61QC/WPJTIrc5wQ/UBM0ruFXcx\nekrjCEvKgRc3/f1OgPVCmaH8S99MOMOZqPDuZmWs4zR8Ue4v6IezeETWBs02\n6ib/5i5hVVIHV9G292FFiJn1meKU8BRBJBfHoOimLHkrGov8zpy6ITWjPt0t\nlqz76OTHp2cZxna2TiAo4pdwT27XMkV/gm2sxS2wvgAM5w0n54Siyn4uqUFQ\nJTbVpZczezdbNUPoVzJTrJz4HVy2SxZPPgCEqOnIjmfgdkkPvwXfXZ/gkoRS\np4Nh\r\n=w7cJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "385bf7361acce2a0f0045f61b02cd114b4bc338f", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.2.0_1644348928403_0.749574143670314", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "lru-cache", "version": "7.3.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "475ed4a39610204d8d23b243e902ed074dd8052d", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.3.0.tgz", "fileCount": 4, "integrity": "sha512-itGqsGM9i3Jt0ZbEkYRKQ3K/Q61vOLNuUkmK0jyjt1VH76gfcvjHmPC6e9uvjSrKOP2aN3T9L1vN0nxQSkrAjA==", "signatures": [{"sig": "MEUCIGu/cPQzLT135hO4fmvhZT6qDgs1pfOMA/KapSI5ap8eAiEAibpRzr8uF9lQMv036aJDIlEJux/fB8/jUerj5EyHths=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAwrzCRA9TVsSAnZWagAAXvUQAIIJ2bxYwbR0F8EPA1nH\nW88p821GR35RXSL/+x58Jg8O/vAy/9YAwnSNepHzPc2DrRqb2nmvBDzx3fkd\nU33QIfEzdKQ6N0wWlqjVdEw/EpgY0rJWK0f1uQfW3/AcNJuadHFKPs5MsSOa\nnP3xswPTPyrH2oU7TnafnomZ/zxtMTSzdBludsQOZKBX6WfUV4VA97anxoOT\nZ0Gh5KOxKJ1kVrOQ2fjkLtU0D4/iZ5GBPX4GkhZGKG/O82EbT+gLXyPPDsmj\nvnyIwyDpeiQ2CiO1qyaImxO+gZyWc+CAkdVXratBw7AGG/anRCeBulEgNIzt\nk6xRmya1TaqbAfabkqOxajOUQZZeAxDxAdQ6/dprWnf9zmtf8PpB3vsZ+D+C\nSY+eoHvNyfOCpLEEk37iPHmNwBxqXG0oQfzSNnbdEOa2wScgwc5wE1pQ7HXn\nXIe2kGPLN84wFz/B1nm+tezWU8HKftc7uWFVLCY/0vcg4Vcyw6JAS+jWLtPd\n+DzJ3OIurp4ZbuFZTYAVQirrvlmw5BIMSUQ2hkvkGMzgSM6WBgWVOrVb8HGz\nohhV1JePO77oiT+yqtzClbr1f9U13eHBmsiiH15MEuC/036ZUAFD1nrIW3nT\novreaeC63ShSEdvrGmBbdhNOffBwPkXFD+QjZOG9tkxY7jrkV9Mr5xvU9+PC\nUvpW\r\n=oibK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "ea96cd5ccb59e2b96ddf717840d5f046f76261e3", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.3.0_1644366579810_0.9272432594774989", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "lru-cache", "version": "7.3.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "7702e80694ec2bf19865567a469f2b081fcf53f5", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.3.1.tgz", "fileCount": 4, "integrity": "sha512-nX1x4qUrKqwbIAhv4s9et4FIUVzNOpeY07bsjGUy8gwJrXH/wScImSQqXErmo/b2jZY2r0mohbLA9zVj7u1cNw==", "signatures": [{"sig": "MEYCIQDJLAoyWUBLm4TnGV3wCM8GHjyt8wd5T1iiGAIpMtYNDgIhAOVqkiweUtNHFHnCMgaNmJTKFn3aglajHMGOSTxaJcLw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA9v0CRA9TVsSAnZWagAAMoQP/05sgB2Zv6OhB6fREBaw\n6i4J6oEypzY8MPQ1aRkfu57ee0PMdh+xihXJvgxvLda0dESyfmlZCLyNQKee\n5LPeC9s8RfGZaWnSYpcPtJQX12FUP/tHFLfMp80nPMr4gEf1nUecp8hkZ1VB\nMMbpXsSI+vQgPOowFGiN8/y6bQEv8zhU56DNmDkAjfUSEDJWvpE+d8q3VxyA\nNtU/QIrwCpmw7ZqKfBTbVimH1ZBavaDgPBIvh55xm27/Au3VyU4BruAmZo0i\n0H+5W7e7k+15HkUQkG5GifWdxRldUqJMbGs7ULTzyA8tPdfGdVMsmoaro0pW\nXlnvC6kZksCMFdhIhiESYS/6Zu/mecHZHincNdC0nKsB2pnu8Dyf0PvgI4Zp\n0UxRhkcQaM+JROT8DTksGuva1mKfV98SGuucfVl+ZBVvFme07ERKPrZV9upo\nKbQU8+5ybemNyMq4oFC8/bjJipVHwhmjH9IAhzZSQkHndXe6d5rvOJQBIjZZ\nJ/Be43fQhM7wvgG90ZfYweqkeZd6vGC8aJiEbyZnpmnMbqAIjvw2u2nlIgqW\ngq1FrXJCj2/Q/bB/hC9vzYSf4u6jDDcJSJAUMMrNrmwnTurOqAzdevXRmo68\n36u1/vfZLheatNtIJ7rXD5lZKgPfUJb2TnxWR+NZ1Iqcx8swpjsrII7ctxcy\nR/ud\r\n=ErU1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "13cd6baf5bbe8b427813db80d5f3791998e499af", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.3.1_1644420084416_0.7876810834242707", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "lru-cache", "version": "7.2.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "e8655de0cf28ed7ae7dda5710cad12a3fab90c3f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.2.1.tgz", "fileCount": 4, "integrity": "sha512-2snGOz/Uzmbw0KRcJ67raVUsQkTmWsx2UcagmtM57Ci7q8bX45ILe7G+iwE6VjqyPMzz3b9J4jEjojBnZQIIdg==", "signatures": [{"sig": "MEUCIHCa1/BPR+5HfTZMuqa+F1ZDYGtrrbAFNFIz34EepdihAiEAgFv2b3eMI9b9R4fNEKWaF6xWjf/oCxCKWTm5FgVPIJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA9yFCRA9TVsSAnZWagAAuB8P/0G5BERZgrNM4b1F+UPX\nHnKSbmhxoNCZJoyaDGYVpw0+b0dD0jsevtZT40uZPhXpgCoHAnDJ8UHfsz9r\npro8jiQQdQh+Ow20sExPNp5W33tQqfJEowDlNFX9rqzEf/zSIUwJ1h4SROAv\niXG8OAWnIWUaUDNKKRS7kqrRackoEUFhIQMsrfHpIm2hZYF5A9LawPqmdksn\n3Vfx/03eXfJrscUaywZnz/ugLvPlJJ2xRPV9GKicA3P+70Kzsbe+XtIY+qaW\nhCN6s2obZ+T4s7kEdjkUmPEZitsgLs1phldUoDNxlAWjG6clYO430ISapo9A\n0Z3yDgNkgXjFruj2uiX71Bx3tejg4IvlZUH8AXCSmfUry5PB0hSVK59oHOPb\npIG0BAyaGXPj84qrXVVyi3rIbhgH/jSfycwBz0/s6wQVU2qmWX0bEheCvLdn\nkzWi9Hysg+TR0avi4Qh1yYImZcSmlRuyYWo3TZPgK8xN2cuG9esQtcRJnBLF\nrZ3SLaj7/2EgKYngGZI5odJzCGvGHboD44LEn+GM/ePCCybkCw4LOOk1uyu9\n2Qk468VrfOJwyPZMulasouSiuy4urtO+e60zYg9W3/syQw0eXi+Vo3+966ui\nZGToDnvdnPXRLBzOi6atzZMGrW2L5Tzoo1FaleLLzOCjKU2xxX3mRX9ObGbX\nsBtV\r\n=nEG9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "f01a2da54238513d4e33cb5ff2bbe97f26176ec6", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "publishConfig": {"tag": "v7.2-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.2.1_1644420228836_0.5934694907030751", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "lru-cache", "version": "7.1.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "b636127cc13931e6eaa2d8994382c7f73bab29ed", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.1.1.tgz", "fileCount": 4, "integrity": "sha512-qsTWhEwB7kSjf0BckLLUS/OVVk4lLKBFZZRUSVQw09AqFHq+zONgLH2jEW8rNJgTORH1d4sljDuUtjSgpbLO9A==", "signatures": [{"sig": "MEQCIEsqSnqM6smxkXB5JZVDbS3qa4EzmsEVtuV525La8tMZAiAZ+qbeJZ6gIKCbqEbI9rFi7gCb7Kywnn20duXre9lKqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA9zmCRA9TVsSAnZWagAAjg8P/Azqm+Jma4Sj1PsMRVjC\nHj3A99ADMEkFVChkqgalQaCMm1ciDeidNhw+f5DaEUCUSba/pIkCh/zg4Lhe\nlxnzULgHGHhhULTgtv2Awwg9wpTtI8Op63YjO7ky+ocTcSwayBYaNf5k19SC\ntWPy/cJQjBXgRvDyjklLZnDnmeQYRL7W7FCfGHVDPHPKkfdivBNXMw5vtH2m\nNhk/2bKvmfZmCBQbAsMiDUW5VEjJjuQzMPZjJQbS2fU87aRdrXGQRg5D3GoJ\ntcDkh4DutVGnCtLwzH7l0laX3QKRzKJUD0SD/Dr3eT0pUwR3XoY8fesbYJE3\nlWGAGYpS/s5i9FGeC7STl1cExQn6D7Ugk8EuVqAdIr1ipqFKOmcPiLP1qv37\nHxkSmjMfMrDog0WSxKMBm8iicC7lApCthR2eSoA0EATrgVhc0qE7T51MkH3s\nWa2G83mxZFldkWugiQQlLTryMvE3cZX/4O98eh5Hn+xN3yrUYMxW89FQYf38\n29S4GDhc63SesOB5AOGznSuu8OAdJ9NVVVPhuzlmL8Uw6ZMFzXS27/YDuTId\nkFv2FSZD5zgmHHjp10dBx9vSAPA6cFWz/jFA8Q1t7kDpdZMWUy1CsSOWBzX/\nVfwwHrxGhOza+pFNiQHf32Tq4wRlgeNGEWaEH9fi5uUmScv1vgpqn7+ItlYM\nyv1H\r\n=uvMe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "121148a85bc0da3d1270aa8cf8c12556ff11fc86", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "publishConfig": {"tag": "v7.1-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.1.1_1644420326615_0.8904005679247122", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "lru-cache", "version": "7.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "f5bd5d54960c7247ef6622f7af65578bccd9d974", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-JyQNYHSkvbRX0FT5QrL6KxWghAZMz57P9xdkWmDHvi0PG9IC341q0fLUK6Ax0DV85+AsU7LMt3XFLImggzj98g==", "signatures": [{"sig": "MEUCIBee0btKIJzBBASMKZj8fhYhg8KDeMhJuJIfjYCXeuecAiEAnBGJHyc0eZuRrWCHGEMNhkntvYMb1Fgn9DGbPYkSPOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA90zCRA9TVsSAnZWagAAoe4P/1n8/godLEjNiISQCUvO\nL12+MF7qifx4nIIid5REjYgNBMwi60JHWP9UYcC7HQm57zlzf8Kv+Zn3QM3Y\nDfM3NFV8t70rBDRY2lxAqBFebvZXPkdp47k3OUKPfpoBx+8ldNZJ4hqkRm3R\nxGHo5SzJvR+o+34gI2KiVg1Y+ybw3UbM6G2VReY9tJNgCnx+/lTr1MeJZqw5\n0HU5OvkgcK60BH1vUdOXLvXxLSSgmBwCIR4HAJ/pYF/6SpdFLtaTGckTwojV\nYiLW2Mn/YX3gjUY4ooOoUTogHTggUKbaAQBsfjcJQKpvIJEljdz+fNvYlb/6\n5FA1NslPWMVj7iU+MAd9635oj5SkAjKEY5+nJis9z3blmQ3r6b4+gZlTIHFE\nj52N3SWPKY1sS2tGnRtFBVWkU19nfLWQ/9RLReDgptvYtYFU+P4cGeMR6fQh\nnEn3l55UvXHczQAjtv3L+/6deXgKaqkYd9fZ/fjsRJB48L8Gryf7m1t5qyLk\nwqTLWkxhW9CHKHScnIPru0cHfgAYBo03NPQyon6iiFg0nbSTUkDYYbjdjT3/\nElCYgdBfy5brJc39HwpKcK4XkMbqSx+zXOjUmpdQ60Jm+FKf5HUUgPsq9kAi\n3Vqb1HpmQAN+3P8G3is94C4qfagfYmnykqaD/r7pDIV2SDyeS8h1lh0bS+ps\nJP+p\r\n=lFsk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "02c726f446fad5014890564a18863bda025a604d", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.4.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.4.0", "publishConfig": {"tag": "v7.0-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.0.2_1644420403014_0.9419591400604976", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "lru-cache", "version": "7.4.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "2830a779b483e9723e20f26fa5278463c50599d8", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.0.tgz", "fileCount": 6, "integrity": "sha512-YOfuyWa/Ee+PXbDm40j9WXyJrzQUynVbgn4Km643UYcWNcrSfRkKL0WaiUcxcIbkXcVTgNpDqSnPXntWXT75cw==", "signatures": [{"sig": "MEQCIDyi1C12pqig2D67vuNeQOTKA9UZL7xWiOzwn0S7jp55AiBjJl/y1HXjmu5H14UiXcNgeF81AegXsZme8xcR76mjdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFDPgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxKg/+PsFA/q3kBp6YELYYFvmW6TiS5Sk4GegthimlVCIL1di34og4\r\nQvEqGWZ4TaIe/wDB2z7S7DYUn2Wnln0WePvcpbrM+r/a4x8IxlmJnYmfM/jU\r\nE37dfmhPrKNNRtaML2qjBiOgVANG7D1keDf1lV0QYFsiAsyYHra8OGClAc++\r\nrohZexeaKTrxbK1rBI3F6G4XBJjjUJAojwZMFeq0uCtGnxLTqJqH5hxTf8BX\r\nsHOjycqFmmq3LcYba1ksy+RsFnSkXKeo+jhu8DZqycZ1ZaZL5TDN4vllxqAm\r\nqLd1UklI2ZNyy8y4zFCyRFy79J5xjhufZvGUFMM0/DT43Kd6kGaHXY3I+EVX\r\nVCCE/g/2s7QdzhnTgCFFJ/m41nYgBeSnXQZVmAjMCXhDVrmInGCmylQaf2ip\r\nF+jUcD5o/StVNOimyaeK/h37tRE1qZ5ykvW52P6ZSgWmfQ0fx9X+RfGz+zmF\r\nyU21h2XsdWzDtq4ae8IRqiHQcADAdJyhSuHDkSV6dFWrVcCbN+DVSUkBmF4c\r\nu6czl6Hdx+GbtsPrurxFMX6sUgAxvwRzKV+a5UaJhCkwolGaktjmotukju/8\r\nz/hOA/2T28MPjH2DChyoTgzH2kAynJeTxgMPG0l3GzwjmZB3QDLye47FROGQ\r\nm+bRVEMeOT+rfyQxZIkpEfX5//1n6QWW42U=\r\n=qMBa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "d511442f93820ed65d419bd7a2542a93d1faedaf", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.4.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.5.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.0_1645491168225_0.9488434959764496", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "lru-cache", "version": "7.4.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "afe07e885ef0cd5bf99f62f4fa7545d48746d779", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.1.tgz", "fileCount": 6, "integrity": "sha512-NCD7/WRlFmADccuHjsRUYqdluYBr//n/O0fesCb/n52FoGcgKh8o4Dpm7YIbZwVcDs8rPBQbCZLmWWsp6m+xGQ==", "signatures": [{"sig": "MEUCIB3FBRc28gKaPnzxhz1dJ9/fOwOPi1CSBAm+vDdR5mmHAiEAiPSN4JhHXVOU7jc5zEGMhGDYEINlX5S4iiJJVinBYt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIuojACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBZRAAhyUAPJaxYoNKmqdn+CwAO9IzEO6Wz/0qHWbLH5axvUlnavJ2\r\nAUugfpiGtHZYuIpEzKlRk67E3VdMMQqAP9iabJcn7VpbSJ3STZ8W02QZaeO2\r\n5Yq7D6pjULLkMgBLFNMQefsxh/DCchVuWS/cAlMN3TNqjbFMXxHgW9wqRDAJ\r\napoKT5y4VNTSrGj2o28RDbnihsgQq/c0lovOS4OGw1XPE4tRr/NygRvnGr98\r\n6oN9SbYdjcf17v3HCz69m9wx5ZcyIQxP3E0BwJVt3S2BjPps1Qz3Ha3+hIn0\r\nYF+cP9uPUJt3KPg9ckdsL6kFIEtuKN0sFeU4ytf54JLt7yOKH9puJOfMdLiB\r\nmfsLobDC0410vT/+4tzYDCNzY1XbLSooYgT4VqvY/nDXrKhAqw0VU4aG6MVq\r\nM6xAR/oGw55mNXNQEBsWdmogpYmgnoKLqEcEUzkbm7xlURhh6vLAHKwKDu1p\r\nvhtWBj9s8yDcf+Iq3nIA5hc+FaZu+ELG/HzS5069kaa2lrKpmoqCKrAN3ahd\r\nEVFpopBRPAL5FmILerQIXpOj+ZtK7kbVkT9DhZdY6WokYJ9aDztujDeNfF01\r\nc4X75le73wKeYNCAcfuDcnBEtlQI+AsGmLxKPZY4EMOgJqqUR6OHArHzpycS\r\nl1RpDoJxl7oacw2452XcGSQWcQgMqzFZ9AU=\r\n=hTfC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "2be1d2436218360dbf4c673088323b88840bcd24", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.1_1646455331417_0.017315440199106913", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "lru-cache", "version": "7.4.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "92f7b5afe82759f51b216a96fae3bc1828df9712", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.2.tgz", "fileCount": 6, "integrity": "sha512-Xs3+hFPDSKQmL05Gs6NhvAADol1u9TmLoNoE03ZjszX6a5iYIO3rPUM4jIjoBUJeTaWEBMozjjmV70gvdRfIdw==", "signatures": [{"sig": "MEUCIQDixr+/umAXLj6trvoLPcTGrJwAu0++OXmFzBKimLGQ9wIgX+opM6jw4OB4FtFccw0Ezy2nK+Jeny/u9C/7B3CEXIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKNKuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotlxAAj0GlPZtQJGDWkmef3hC90LJJa71eU0uxGgROkEP+L7QWW5Fr\r\nfH+wN2isE0v3H3S35yp2CwWIyfqJs/9XX6TalDTbGsox5tyLGCwccJG0g8J2\r\nsXpJbPbxwLEVC5GQXtTcbvYy9zDVS2hLANihfzyZtg7l+bLG8Psb8oxb4oaW\r\n4+JkW1eiMPWUn7zuB6vULrno2wADA2WjTpGcKpdgB6Zui99rivRsU+NAb/q1\r\nGKtavaMMCUbYP4ACZ4UAZFrgFYi5EYsQyPXnaIesMxD+u48XAXNwbi5Lgd6V\r\nFyT7BJBbNRDNJq8sWjTqCjRPzgvSJQUw/gLCHRyugKDBTnArqHW6te826PIE\r\nW7I7RnuexbTxjBYQB1yJx+rBWaJKaZgG8tvX6zvyDIdMeq1JD1/TjKfDwE6z\r\nzEZglQw48gzKLgpcfU0x9byu8sRYrTmv/ylBdTTHrNr2NykzQKToebHxqtV0\r\nWj/hKDWtvdz220SeNXI0tKxNTtMlxi3AH0U5J1WC+BIhFJAkWq6lPbOGoUp2\r\nLZVwWDg/AgDyNKGN6am7ibM6GfFbSAaAvtOWEIpbjsjzbzxswW7SAiojYBoC\r\nCyEdFVQ7VDt4HPVApvRgpmZOeVkSVrpBSPVHfKxPNqpmo1ywqk2kTGBMA5+l\r\ndrSGkUkGugmhuXVUssTNauVDCto6lLwUzhE=\r\n=jP8A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "04765f85a1e480c14b9928f2a40c4713ff8d611b", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.2_1646842542209_0.48780490214664574", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "lru-cache", "version": "7.4.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "b28a09754515ca5c6efcf1a5c995c2a52c40ac20", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.3.tgz", "fileCount": 6, "integrity": "sha512-kuOtCBL+fxym0asLazBsj4mEmt4kwiLV6KTGYdAO9jjXVkx5svj3uMd3j/y88YONegeFAOETN8u6XPb8cw/mIw==", "signatures": [{"sig": "MEUCIQDojhZmiSm7ygupU/R/uull8RV5oOWMQKuERVJZF6vMhgIgNx+B1rVpp3tO/ZOAGGEplIMpvz9Gv1FFpYX01ST3f88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlBSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe0g//eXE4RDxXiX811SDTkN1Q24IH2z21byhiQUvWUXFJyAe/hKya\r\nRB1zDZ2k0hboePrV4YjnoQr/ZyC4LAAdg+E7V5lg7PgnOt+FtKiMKaaoDkGm\r\n9MPGu2xD+Kxua3PbA3WoJLOp+7wqFu7JDhaVeu4FmzK0xO32QlidysHVMRvr\r\ne/ncoZ9iEbX8sN3rS6oIvHOONNmVM04vdKv99k5q24eA2BW6bxD52atfU9ip\r\nmISZ7kCf6dl4sXsPFqOXarCnFO7CjKRXH0SzH6bVKQVOWicyvzkPWv06WcSz\r\nZcwXpBO6Om8bzwcEKgpCaasLfHJHksd7JtdkScaQ4My/x5InnXVL+GcOSXWT\r\nv992Fjk5C7P0X55GvU/Rf+5ZOBwd9e+1vj8lMX4ONgN1rh2T74+Bqt39NwAT\r\nAMh4lPvgUo3gWbpgAjEpKkyhtzosYJVTlSrAwqPi0Glu1Jz1hMN42ACPGucE\r\nKaRPUDFujVAVSWtY4OurasnKE3TG5Y0pmehlYVrEpq/gIpsPmgDAkZ5Erqtl\r\nFo4rsXf1ezJma056cQ/qoW9Igt/3hEjpyRPpXaR81ARK1M7So7CbZQUdTmKd\r\nfTBFGEaUqaYEoIUINiJqHMb6Nyz6MJ7E/0PX1lQoxqXIVGgdzy+lteELI1+O\r\nSCZTzp6ReNLGKLAaFuOvwwsssIp+Tbnd4WE=\r\n=1i7n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "00449b331aac094d7a2aedab78b45ac354f17245", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.3_1646940242150_0.5160291687367282", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "lru-cache", "version": "7.4.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "a3dabc394ec07e2285af52fd24d0d74b3ac71c29", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.4.tgz", "fileCount": 6, "integrity": "sha512-2XbUJmlpIbmc9JvNNmtLzHlF31srxoDxuiQiwBHic7RZyHyltbTdzoO6maRqpdEhOOG5GD80EXvzAU0wR15ccg==", "signatures": [{"sig": "MEUCIQD8DqSCj9iaxTZIKGC68GTncJz4BE3iia55jvhQkrmx0wIgdTWm/oGYZilpv62UqHjF1xASQJajDvg0vl8ag/npaDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlDQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMOA//QI37Ul7S3x/UcCuo6cbKvdpEiRpdQA9hD9zYak0Iv8GhdNMV\r\noj7oo8K4pAfkoODrLJzDPJoNAq975YetOYxcjiMP3rIt74AUYsj9jDa7iAQI\r\nj31ozpcCd3PvNaTz0S29x0USsamydM7c6E8PfrEg/5qiqRp/Omlf6aVvG7ws\r\n4Z/ZWh/TL8BKJq6oafjPzA7WbOP60JwLk5cpsH6zwFk34mniNSCsyOh8of/q\r\nrEtS187ieMPK88ETYIlKJ7de2lm80OeQgH9RdMZE5R+RdInf5ZJnr7bx2cza\r\n5KrDJgmS9pAP95F75X+jfIlQNaIB/AgLJXvDQLuyJ/DBQ10Bfjk0ZedrUkQI\r\n/R3XAXEqihucQuOHXHHps+Qjck5P9PAQgTYFRGmsnNhjg8xdcn+UsSU1pMMD\r\nevPpklmq2jnZv4a8NRGXDCjGcgAUrkujbHHlh9u+fRtzS7ztphPSnnafEukc\r\nli/HIXtt6zuPnFDOh6aB5EYwKwQPzsXcmCNuxgDDuOFgBFYEmmHoa9PU7aM3\r\n0WRBD+tpypfj46ByAl8Spv9m9U8/3Du2sInI+ZhrLAxtbhA9NVjt1rytOWP1\r\nECFkupliIJOQ9R9RUgMQPmJ4HDtU0mAZIoBDHfPmfHAFwa4ftIurYKPBhRNn\r\n0N4r3YEDoaxH6f3c0GwyKtcdcoi8eBEHMW4=\r\n=q/h3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "aaf23eda3027fcc1bee8739b320a8cdaf404c14b", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.4_1646940367866_0.8649527812271489", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "lru-cache", "version": "7.3.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.3.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "2c02862058d222d98caea16a091acbf926e36e5f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.3.2.tgz", "fileCount": 4, "integrity": "sha512-W3jeormhox/OUUGWDTXj2+1ks9YAZNkzyXi/4v9P2nzQGlD+WL/dX7yUE6/ZZp64A7sr8zQAkP0AfPxJ6bDJ0w==", "signatures": [{"sig": "MEUCIFU96Qef4Hxn6Tiw4udcIC15W5RGfBuORMec9TCq0DTsAiEA00lrqEfHEd0T7PYAlg5XMsnUXUE7dLURgL4MfopyQoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlJaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+ixAAh9y4z3cvEYxmwlnQ4AnxBCSwJgMGQONUPRV+W9+9kC5ee+Hm\r\nyL+zaiSO2pbr0ErhLtQMezNcNzA5ZXnb77uqCZxX7eXdbwARFk9KOVs6aaxO\r\n85X7ttBa9RR5xRKVRl5VibumiPfXT3SqK9bQb3FiJp8ht1N9rKP8gMxX2dgu\r\njktou0kL7RjG7xHbq4KyN/r/lOBdDLLCO8874X+I3dJWtZKMA+TjBTYl4ix7\r\n/E1+qTdBl3r3wie68xjraVN6YlU29k2loWrwoIH0Hj15DS/ujPU+HwANyk+2\r\na0SlUNWnH2s3Dftx1Ewveaj7+jaspVsGmiw6BNJmdnf6cQ7NMbbCgmDVJAem\r\nVdgyNz0cKze0ilz8q/tZ5G7RSP8YdiZEMNgL9AxkgwKlVscRkHM5JU312vNu\r\na/I9yf5H2H1fAzYCWclih7QbSJ3RY4BBDKm6zoU9bQSH2tswsYeXpN0iuzJ2\r\ngJM78GyRJau0Ty9zZRGuP7k72dvVMZG+qRGJUFgpgqIdz+zjSCl2sTOGzqm0\r\nrmRVasL0pVuHmoDO3wbwWP6OiQ7ySCpGJqgZkDyKyafDWhGgX+pZhaAR1rWG\r\nuNNVOmWO4E//rq4GQHdi2XTrRtM+OdXAUj2+gZ1uzcsV0W3Qvh/Dz+an1YDK\r\nmlGedXTpVPlNBvWqz1eBOw5h0UTLlA1jxKg=\r\n=TPgp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "a20078b4a6b13cfa88f64e7697aac2cd1e80bc23", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.3-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.3.2_1646940762400_0.03571872113995811", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "lru-cache", "version": "7.2.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "f9692a86d9316588110b45de0f9bea1a868f34a8", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.2.2.tgz", "fileCount": 4, "integrity": "sha512-zPNrJgZUeynYdtzoMZEMuwZjoSnyp5+kVkwo0X4UHAO7qCgn5v0bkXTYyzI7k3nFXDKVkhlXs8Smt6aMPFbCxg==", "signatures": [{"sig": "MEQCIB3IBy6uOYr751Gxev01SFjj79iw7fhQMVirGQsg7Kp9AiBw+46HDm7grtn5vpo7iEO7u5x2i2fU7NHu27Uj12xK/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEsQ//QGrGHUSGuS6irR4UzcDgc28etvPOg2a/aujgPBuAUtnawHFu\r\nGOwSZLRw7em88EWoKetXvejxuWQ6gcAGXCIFHlPNmXOgAyxiXRH9B9M9pfuz\r\nULNVz6IRuDRDkUMm3zWbqdPS8uyN29N+ebxCs3UiwhjMB9zFs8+sRn2X4Crg\r\n6D60XRz0atIfuh9SGPXrDWkPKULOXyMsKVoxtYNWyZXdGzRj2MyGWCxCPoIu\r\nEO9GVxyRUUxvDIAPtJ+C9WNexq+/My7QyvgoPDvak9ZGWWf3yEAreDYcWce3\r\n2oLRsFdJoJH9eUcmUgLX7ulqSWDwh0d9Zw3NWfXul0qXAEwNaR2Z2Wp2oNfT\r\n9NvOBuM+aLlWow2kni7KGsDN2G85qbHbkvVoDAPd5BXKTxZLS05O62UEvOQl\r\nP8ICRok7bSY4NOYYXZyfMT0QfGg66LngfD03SJb4FrbYteyk9FhQQTgTGLcT\r\nRfomJIP642ugk6CvLOqahy/We409nVUoFlCU1QIw/OQKXVdTZ7s6HtiUMgmU\r\nSsNCk687RypQZqwCrwNNDolJizhXS1N2SsuI9JBJ8OgN6jHA5Cln9b0PU4O6\r\n9XHlXHQc/kAs0LpZnGcE7mupsKkX/m4qCPF61l2FWz4ADoQuzYfmy7gsbdIY\r\nzDZyowZC2thskQSQWbYrhpCY3AqIePsVCXk=\r\n=fgA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "5594005ab0011fe9ebbebb943626a600c659c52c", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.2-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.2.2_1646940804670_0.20291375813945156", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "lru-cache", "version": "7.1.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "ab90020ba299d9d140cf97570958ec67eb3f2797", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.1.2.tgz", "fileCount": 4, "integrity": "sha512-u4Lv1LM66bMGCi8WQUURP7ORjC0JNlw0jrJZr/0prh4SOcTD68sZSECgJ2xtjM/PO0/Y5NUGAP43EeCaOByCAA==", "signatures": [{"sig": "MEQCIHdFdPYNa0ihT7hvbvABQbiMZjd2o1NAb7d/ylmO7+t2AiBY601rZ6gYrBZWbPzusNGmYR3cIE2vDFUAZEGkKDkiMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33937, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlKqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmogmw/9EMq7Gziii7rv2sLlAZwcijllKU+SPwkUlIKGwnb8kZTILEjp\r\nwE8jI6FWqCWGa0g4Mr7KEsEOtHbLGZ7QIfjs0iDHzvNsuuIXplfL4JduHTv6\r\n/pETfmyNySuGPiUJyEN1kcxiuTSNXtO0SZ8RUJthH+EgpInpMzqMrV5zTtOA\r\nHlfBKuc8tCvOyZnRxyFWhCO5KaJ2RayJNp4UuaDa8MiJ2RMxOVrnG12HA89o\r\n4us8iW4M/M0HYWiR9Wlap/PKByYIz4XRxItgg6+xZyzSTY8T+OFxLNdK1VCA\r\nC8P/b0bhPLm3EP8WAJdU+8o8ZjadyRcwr9z8AoZmIA9UpNLu30uZ6ANV4hVX\r\n3P9wAONTML6QwLHLyDRo0ZHGlRSDnrSIiXfs4nPeBCsuw9dkfwpha6rkn8oS\r\nZNDELTxWgCCCGC0GXBBLOXTaGjxh1vBKo53m/7MsqC+uxNHgGPRjPKvp/Uao\r\ni1U/w6/DgmdyTfXNbJvtntuqD5PGRMQYp7Djxdn1cFgA1+eqiWZCV8Ugxjz+\r\nGrwfafV9L3w25rRMVq31FMUAqUqa3UBmHRowCdcllyP7EImk+HRQaqPCpqoC\r\nZifTsmqO9X4M8k7S0v8LIo0dhErn18lOdk7hiFbfUCR3cbT0CDYx1u9bJODW\r\nSVlNFtV+grXlQxquAdw7a+30xX49HWdIBgw=\r\n=jaIx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "440baeca180e03581e50c37ae873fa862d73a197", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.1-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.1.2_1646940841899_0.6806455451382682", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "lru-cache", "version": "7.0.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "7f419682ad66841da4e87b4e3486fe80836f34a2", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.0.3.tgz", "fileCount": 4, "integrity": "sha512-Fq6gvppbqhKZd/qpePv56fQNmVj9ZwU8+qEo/8k5LtJuJVvRW1ZdWnFN9LePFJnoJ9gUSjhT+CsxNp3wwQOdEw==", "signatures": [{"sig": "MEQCIA/0Gy+XzxRtiWoZdfiyY9k9LhGcLxQb3+/ak9EVN3h+AiBTxVU9BxmmNzQysX+z2kj+p1fiB/bpoA6sieREhJt6KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKlLVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWvxAAn3VSYnB0rU/zRkx7YU+Gd8FGIzenGqdIcfBZ/GD6NpOgoZ8H\r\nWZ4yfy4rojdt0RSaWj2sR7k11et2G0E1Xu+GxTY4WO/iv3U1ALHg7//YUs+M\r\n+MicGKHPhJpgyIfj5K7HsBUSnXkH8yHzwEdXikCTZD/d/0hTfD8vA8nIqrJI\r\npgaGWLlI7Zgo03ygjuC+ue9ZN361dgjuvNzutsypVjtL011N0qb2coOwXns8\r\nkUzdpkZ91Uj3CJCOcfCG7B+MvRF+1vOwKrVtKTnrhPA3TYeFKsl+BI5+ID1d\r\n/9Rtk6nzH94sOBFvTKjwZWQq49LnUjJErU8m+pejFJrC8kC2Bme4ozmz0Y3F\r\nQNqWtIo1OOgBgyRTwmL3OCxv2aj/jRawYYiO4ZbBRAPL5hd6Pp2zqNrDsCvn\r\ngrYdSrv9Ok5/5vejmYimR+7czMO4hYEt9cI1AUJcsZ9n9/8otRUSRQxlcHxf\r\n9cby6OAQ2YmV9nmrVoQyDrTDbTk5OmM43esdqJmx6uXHl8YkCv9Jc5IKtkFL\r\nPwTG9t0MjhLDiSNcRhkazw951xBUyGwlGdoWYdUcGcjE3U9smsYmF9DjuPJl\r\n6URS2fnEFkmn3Q+StBj92SH6eiUcOCmpX0rxXAnhuIBN0MN3Zf1PVd52LcYo\r\n1IJ8F8YdudXbRvEy82bbDkiNN2Jg6iaf1oM=\r\n=0wRN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "0d515a2c47dbbbf8f5663d1488c20ac2b4221783", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.0-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.0.3_1646940885672_0.29784891373906497", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "lru-cache", "version": "7.5.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "5e14318d64e6f180a5cf3b9b955b2e89376c0efe", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-8neUvdgNtubJ+VNNqrqOLjUoIlN+NRPFtv1Vne2rQ4uPCxhp0W4TlqntzTLnVVezGXcSSdXTrKCTYooEQA7X6g==", "signatures": [{"sig": "MEUCIB5geOdAQwU3w3FoOdrrdXp81JnBjo5/PN1MGs/CT9+iAiEAsXejHl8OojWGQpozHUy+/cX71YxnCNmqPxMut4M/jN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLq8zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhsg/9EEMNAZj08N2rPGh6cgjguBM9K+re0kAEK+1QLODj8EP9O8ra\r\ns9sKmXOSSzvPTySYLoR9Q83Yp/SJjK+cwWf3HNyb2TXsoll9La9d8ME0WRmt\r\nU0i0/1PnWdB9lsAS/wZB0WPmPES75a+nxQEXqBYpCATs7/WDBuFgCwDmi6zk\r\nmm7IGvGV+mjdfWbj/I1rUwjnLWokXNO66cOVeW/SPO8kE9ks8XosXCreA0uU\r\nDkscuC1t75hz/TOsKFZyNXOxV4/QuNBcV3IlcFEV3ER1Uc0ewA3SHQSG292m\r\na3cRQYNk5D7cUK/BTYMYXpyMjh2mt2Rly3StIjGLTsNb1nZ9vEwvGX0u83Jq\r\nmWlt3pmBSgLNHsKdcGcsQbP8MWeUQlwsKGgS5/y0yiuSTNKM3Jx17jn/wx/Z\r\nFeS80USBt12Ug8WCgpODppYQw93YsMqZyVw4NsT1YVsX5PkLX7Wdn9ouxg+U\r\nRHtUnF7sOImVC798iCxpC9KKB6mW3bHl/Hu3PeZJO1u5V6PDz8axVXcUuYtY\r\n51govwxBk07VbG460lhocEoYFUWRK/DIOApWCgqqcDfT+mkXe5h1eYwne8c4\r\ntXdmZh9+NqzrYbd6Ba5EP1R9tidO/nJH1z5XRZKoAAQ532oJkNW+2xIf0rbM\r\nicnOJt6JRtNp1w+qdlOS3l2puNBPALPaisw=\r\n=RUfu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "6406220fae5ca7bea5c083b121f53a526edf8348", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.5.0_1647226674916_0.5975480216807405", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "lru-cache", "version": "7.5.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.5.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "660a134c2c3c015aa453b03df55d2a9f0c216a0f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.5.1.tgz", "fileCount": 4, "integrity": "sha512-q1TS8IqKvcg3aScamKCHpepSrHF537Ww7nHahBOxhDu9D2YoBXAsj/********************************==", "signatures": [{"sig": "MEUCIQDlXxwXJ6eEI0sNmi9zxHMy7dxOBEazeYd2AnVGdWWOSQIgNNyUm+2e5eS8OgC7oY4SiH4ZiaoPllwYHWdmAjpfVA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL35gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXog/+M8z8izDW4APbU9wr0KDMR8y1apoWOKtoCfg72ODUVkkzpg8E\r\nlxlsyQb+T5d05AaVOUWehAHfT3kRr5SBMMCMNF0ym+QvUddphEUpyQoPZhOZ\r\njznArAi5sX4JMmXslXFED4ZsVbZTVjlj2KezMCgaKDRJn8njTvDxhe098ZKe\r\njw9EweaRziteEc7fncQZOnW+XxKvBcNGUc7Ra8nhtMt6AVEpHdDjgSQBQtUo\r\n0c4QfJ6hgkDBcqHd5+hB8xlWn6hORDXxsJpg3fnnup6xccqRlVqqWOk4vrAZ\r\nSC6N5906uOHCRDuckzWcJmUTTJBaT6vEm3c2i0eljqnSYKmDwHl3KaSD1a/b\r\ntOwItHLyNiYQesVIZNT2B5vz49UUfTHO38gV9w8AnrFZsFPWwG1vfQsJ8UiD\r\nWnUc6dLyLA7De7CNDVylkuAF5sSdTixt6X8VQsSrHx2Aj4EYUCIbfSo5B4n5\r\nhqtFaVV+GkoNmVaXlD4+Nr7VcKXdRjxAm1YJ4Dgi3D17SHbzRmhUDp5BN0Sd\r\nMWNlvTUpV0vUQKj5a/fnYV0tr718kBQNQFwD6Ki9wFj+3yhUMvCb/LVMT0js\r\n5MemsbmkxFsZkSDHup1aIhtLo+EeRrJ1gsylZo/ErQdLcJEZmYIP74V1YlAw\r\nkiFdoBTa4XfLyGXiiDHLne8CMXRy7LBUFak=\r\n=aBR4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e608eb8341df51e128e1a2526682901e38539b07", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.5.1_1647279712764_0.7234295714520351", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "lru-cache", "version": "7.6.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.6.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "33c9a9815ce6e4c0e2d5d151f6a28400770e7ce0", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.6.0.tgz", "fileCount": 4, "integrity": "sha512-zjOf6cyMI7rcN+5MtLsT4GnDjc6D9XHi8kYcsfXTqWC+yLdSiU3/jtEPX9wZE77+XLtnmdIWu3+291hkizfH+Q==", "signatures": [{"sig": "MEUCIBPdgm5mEeOiNZ2d3yql2l6cwS4GAEVJ6Ifdg9rTlRZGAiEAt1GOQoW3JuMcbFtuUno141aSiNnqik6ysY7a3iwlQqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMrIxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3Pg/5AO5g61kLtzMlwOgEdM3STM3GYap6rbh0rDHyVnLtOqRcX0vS\r\nlmbfrmGOiVtikUvbPX5yzalqDttMKYcWIDaFUtAJnk9nFOkOB1vL+e/Mi<PERSON>sy\r\nJVj9kFbWn4DWwmvnEgZU4bwb3HiFDuyNJ2EmArnhFNImyi3L9CJBvnsh/fRl\r\nqsd4XwST5v5SoH4pUXcJZtTRUqhhauzZmi8ZkHROSz4sMNPc0sKf6vjxqi3/\r\nbIVwPlEh7MiCVHZF4qfZ/6tRepHrphXW89F8L+odsp6KsQmkqIbrhYAAJURd\r\nPdbpTne9ZG0A4FVHU4skLCtJamRCgqZghLAUa8Rd1YYTUMWCf8HNOKlbHde/\r\nucpQxPZGHibCAZqP/ngw8BKm1ABvbADaKZb/IFIOOBTmA2YQ95kpvrxYPKTB\r\nEYgr98AynMMZCZSNsMi7z9oikb1570KGZwT6vG2Q5NdHwC12L1RoeLEtR39P\r\nuRVINjMNcTYFYrBEUVJ7pWx6w/IXaK1oyuN/7VPhYaY8+0ro8L/8s02CR1aN\r\nuJ4qcD3aV6p5kBEhhmpJwoTupkCBODL5WPnnRJUkY6jxJl9bLhHH/dgQBZC2\r\nc834C1ezZiA6fIJww4YVrfZfbgJ1h6YrW/1SAZSJbmZ+xEAgdfrfXI8g5bmG\r\nKrm+nNxI7kyFC1bcpd58H5877mwnBikxeWo=\r\n=sHbw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "243114d7d4a72b81b40b8d546e457396a321cf10", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.3", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.6.0_1647489585674_0.2590322162594001", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "lru-cache", "version": "7.7.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.7.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "8ce6eeb1b553660a99edb3ff832cecceaeeece17", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-3W9Irr9YR2ZHJbfRr/hj8VtzqH7DugwWyHONyDByP6PLS/YJV7GTX5dDYS+qFe/LkVfnCjtk6vkVsxxKGol6jQ==", "signatures": [{"sig": "MEUCIQDMPxXu2KF6PQU9f2QqsrA0jAZ54nZeRX1DwniMxOwXiAIgFVCnDu0BneDGtUa33BkCl74p/aYynZlpMVnOqRhWxVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM8ioACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBqQ/+JvAkFERuTs1HsvKyU7JcMC1iGzxU8SNc5Q7T6lhyljTeCM4S\r\ntauA5mx8j2aEoCK73JcK5kZarbmlQM3QXiVn9wZCpPce/JpJ2cD6SL4J/1S5\r\nH0oohyTwXD/BRWG04Osz11r5KOnmtPpJGoZvU9zwhcpr1C3pUbWZRLXCXvh2\r\n7mgGevufOYMDZ5XQWgmOjYFDPpnxhqbtIhpHFjMYpI6Lz8Pwv0sDvYs5Jw9c\r\nptOnkB75fCPLZWdco+fNKlHRqxsxTYFR9a7hwCAKiTztlmOQ0X2UL6/HTnDx\r\n8d4xAMLEd427tyW5asEGSHGCB5bqhtahhyRVbDXIFdeS+tAuhkj2Qk0aqEXF\r\njdXsEvCLMsnVOaLLeCWeay6HxpRaX6Ng2ESz4kMNOE+xCzTFQtt7yazbWBMF\r\nm3HthPAVr+hNML9gERFK0g/KgSncxSOXFgfNYa1wxqyhHObhbMO45QOYBPjS\r\nKcZhRgoxSILx+C4sfLnSj2OL9flktxcQc/WGMIwNj4J2MVhO0jnGRRFV13ic\r\nSwrGBQpOLqte45M3dpxMrSDKJYnjNuRra0xzwh8qiNk4tVRsFOiznOKaB3nC\r\ncertf+hT8/lLspHQzL2D9h8OV35umergVYi6XklkTnQcTI51LD4L4EIDYB4A\r\n+YNKgJMWWfygPuMBbJWqXl97WP3CYLBroSk=\r\n=Rbfm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "74edff9a114115b1c93364f73e6e49ec4d8f2d9b", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.3", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.7.0_1647560872353_0.846644611126973", "host": "s3://npm-registry-packages"}}, "7.7.1": {"name": "lru-cache", "version": "7.7.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.7.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "03d2846b1ad2dcc7931a9340b8711d9798fcb0c6", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.7.1.tgz", "fileCount": 4, "integrity": "sha512-cRffBiTW8s73eH4aTXqBcTLU0xQnwGV3/imttRHGWCrbergmnK4D6JXQd8qin5z43HnDwRI+o7mVW0LEB+tpAw==", "signatures": [{"sig": "MEUCIQD7dcM+wp95issnZpHowi3I93PtADbRIA9kB1jyFTo/8gIgKzuPtlF2XzJ7B0Ww4QeoMrxgj98X7cs76gUc6+NK1ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM/W4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Iw/+OgqB4tbw+vI45E3QBv9H8KyL69oVYkrkRGm11GVdtxWqQyrL\r\nln3NJk0MyUfr8RWMyHfZWLTJHbx1MDwbmKCvQ+YpOmRKsPnDjgF4r0A95r26\r\n6Hl44XoD+JLG1+3o5BbGBTZ4fuXnIpgzC1s7btXGKhkgMPDD4iVNLDVN/5xM\r\nPcxFD8dq0LZHRmoLIZ0kyRPbSm97Nc1nbVGx/7FQDVCrswiYrquflVIXsLkz\r\n1HXLrdgS8QHlh48wrD7OqEEb3GQSjVGhlFosZgSiRigxhIenR+SWTtHdUiBe\r\nCxokmRmS3nc3dAimDMinKMllpehcweRrIQi1D5WvWC45C+e9ip327ITh2ZGs\r\n5pvObzawKWjj5C1Qq/kqZhRbBLPGcLv17gTQzgeZpeQZhHz9SkQ1S+4Gy1az\r\nHQpdCAbQRa9y1jIosJPLzG7Zn98fD2Tp9HFM8ZpuEPNuQNWvmL2+sV6RarT8\r\nzWJpVDjPRCzTHcVh79gwgTd37ZLl6gH76F1LrK4F5hQ2BJsl8iFcbCEbXJOO\r\nPCz3YIIYKwch3SWbaKeeU1Mh3ZMeC9hk4ce8A+lPTG2Fb7GiHxDV53TJdVmp\r\nScz2mD7RbjY0ET1h8s0YQgb3VvBa2WMOqQubaFo8lcmVcW9UH3DZZiv8jDtZ\r\nFLxpxrrPJJNTqtZqc9wGeDSrflOPQkn4/9s=\r\n=OpdD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "b0e4020b7b2c619ba48a61758afa2c17fb8b7cd1", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.3", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.7.1_1647572408611_0.19350637451252428", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "lru-cache", "version": "7.7.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.7.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "9a31f924b96e0e238c1f981bf3fcc2563aafb5f0", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.7.2.tgz", "fileCount": 4, "integrity": "sha512-WkdIOIF7HkfVHXxKLjhH6lyAxSFoSO5NZpZS9cH8Oe5rAI2ZDrVmIweDAZUHqIhl0zasQUprVVR8uv2yggYYvw==", "signatures": [{"sig": "MEYCIQCtJtinjS0QhPunZHf0JDkSqvwvWtuGN7Vt8U5VkW9EkAIhANpJasD21WSIAtgVkbgk7bp8H37g5pD2w2tS+b/l5Zhe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQ34mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLrw//VuOKH43cjufVfKKt4oTwp+rFfcX+J2jLR2oRE27tqXjIOVwh\r\nH4BpgT3hCYw5n9J06aYhsjSWw3VUKR8zM0eZ6oiICsHCVg/bxFmlhLxMGPe0\r\nfy+RvbAspKmRYCUY8wL2LV7j08azN8HLINGG16YMoLWcdWvm03AAZVO6S3qB\r\nK/9FVgxlrFBRx0t1NhsLY0aQSOy3OQWrH2RqNOYKTkGCeF2o2eG2fVbLlVDc\r\nVAsuqk1PN6JwRLoOuhK20LzFrKgQxwKLE4osfTn4aZ5uLv887DStrZE/6ivE\r\nzMlvQzRQHterVYMmHU6BpjD3CXCNKzM5m3RaUdQXZ/MpTG1/kdxeRmcPOXDA\r\n7RLOjzDbDEINVKohrYWZ+6NqlVPtAolUGbNHyD21DmBfb/QQNncXiRfZb22t\r\nOA9qyPDvBJJzMxlKDWcdMVKy70UJltd/kY5PXpkt2A8UWu6RkLtso2visN8O\r\nlKyiYXcAbZ3EmhK2ddHycf7p2VRnUcXsWSTZk/1R0i8MxciUO7refj/tcKL5\r\nbN4l2HqPiWS2H6ffd5C69yPDSn/jjBOW69YzrtYD+2DquHhvu5AQwdnnKeCW\r\n55+1Ivj3RKTr5Ds00622UsTARgdsTxfCh/PWFseEy/z/6GlDMLmUcc/YWE3u\r\nuhky+jsrZWD99BcJXXDdviQcc0HZAgXMS3I=\r\n=/mtB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "49b95f27ca3af929fc5fa7cbae36bbc1710663e9", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.7.2_1648590374005_0.46460989436219746", "host": "s3://npm-registry-packages"}}, "7.7.3": {"name": "lru-cache", "version": "7.7.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.7.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "98cd19eef89ce6a4a3c4502c17c833888677c252", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.7.3.tgz", "fileCount": 4, "integrity": "sha512-WY9wjJNQt9+PZilnLbuFKM+SwDull9+6IAguOrarOMoOHTcJ9GnXSO11+Gw6c7xtDkBkthR57OZMtZKYr+1CEw==", "signatures": [{"sig": "MEUCIA9pErJMChD9ZiswMtRQCiD6QG1TpT2l/E74RMnkdp9zAiEAvwenukeUDHbWz1yKuqlrd/0FnYJAxljAkWCsyvuPkjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRHTfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYHA//WFAOU39I0x6kno+sIyq0RV/U3SM2P8Ta2jZmsLrlxFo9q1cX\r\nBE8EIfZKjqY9eGqXVz6ozR5eZ5sL6pX8pXb4+/INlC9y47HUpw3xOeZT3lSB\r\nxuDNxJDnECBhzyZjRf4A2/MMGai9VpL2pCbhZKaITKgHhwBRajPZvy2MBtPn\r\nZqIR7lzoEAV3YuoTzgarQUT6UgXiXeQaRCmZ967D4ThehR8o5gZx2zPNDwq+\r\n9AtWMrTglyo2D8yFCKKwIkNG/NyuYNwBx/E8FlfpdaUvpy518TqQogWb4Hcy\r\n5E/4LBwv/1SiIwKqYnF/7mXmkMNT9nLbZmnt0t2AtuVS6QnX+0ZEVWaTNenT\r\nul3ZMuSW9CffZmBtNxaS542Sl5BytMHqGOiee2CuiusFzJDjIis7g/p1KGth\r\n3A7xXRWHIrSeGEuPAweZNBSH4QOCH2f6jOSPu794Dmypj0RwekQepEzc2dxd\r\n17tL4bXeHKcI9CJx3wKEzLUur/Rm4e2jJDhSXDlGgB5ApzmxmZ1r6OucSTKZ\r\nX3NCl/0rrxS+BuPxp45RQaEQYt8T0vjfMmlk8dTUPX2ldJRBko7eLPAmCLlz\r\neUhxfKbxLvZjG1wuITh+xEsapGw5YCu93M4kS24ur8+bcbPprAZiDPR+KOrA\r\nSOI7b26hKJ5kVhmAm1RT+e2OSiuUkUS09hQ=\r\n=6AHc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "96a05fe9433c42c66d8c885fac8789cfa250b743", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.7.3_1648653534991_0.14582414592360093", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "lru-cache", "version": "7.8.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.8.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"coverage-map": "map.js"}, "dist": {"shasum": "649aaeb294a56297b5cbc5d70f198dcc5ebe5747", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-AmXqneQZL3KZMIgBpaPTeI6pfwh+xQ2vutMsyqOu1TBdEXFZgpG/80wuJ531w2ZN7TI0/oc8CPxzh/DKQudZqg==", "signatures": [{"sig": "MEUCIQCpwDIQMtrxToPSE7saBqejZX34MvFPHQOre7Gu/hiHiAIgCly/+IhX35a5RVRfsgB03xMw5+xTSDHrDDfdZt5CeqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTz8LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop4xAAlQQMANM8yb5JlwbYhh3JzBKyrAs3vlEQa3nTaq7i3KicSD0v\r\nt70DQ5cAq+fWgnY4DnfB7bJW8wr9C8SETDRjIkjj9U27+Fuw+8MMQGmnGX6e\r\naU8ZWrqfL/L6X6mYo9e900jcd3HeArWm5lvyxxIrM0FpxxeUwWYHkP21o84Z\r\n9YNjKy4Mv6Tz3MCL/8kN58cL40QgAMNv4tah0yK3Cr5aBKF27xB+0quz+QzC\r\n/lGsvdBWYlVG7R2AiAcvz4KsEzSowPaw3Huh3skCW6jL0n6wDwiTEmuj+62Y\r\nS<PERSON>QqJosENlRUTAXgtNTJW8vnKxPLGhfDstTK+KCNfR8EhwTOYqUDQCM\r\n/3uP1l+qXEwAsD8zJc4aWUY4yIbg5ofF1+s9bGDUsx3ssGhkx/66mEjLa9nX\r\nhTyDku3eicuBsoFxuUYGNPltqeNn9PPtgJBpXQt/hWcvy6SWSPE/vsJrOan4\r\nOcVNoVA6xfMivvKYXWMtfO529YS3E9tLmRxQmBO4jGVuDTaYvgReEZMTqdfz\r\nHtg0ukq69t2zUXbQpQc5DS0UCo2vmQzADOVBe2tnyP/j1qEC3dm+OY4NDG7+\r\n0nu/1PNYLaa4Qdn26e3dpBFRtb3vdpGDh6sGRU/U3pbY0xycRvLiEWFG8Ili\r\nQaVeVuc5wbUvce4A3oogINsVQcM/5D36SIo=\r\n=YR0h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "3cca7d2bda98dae88a09d53b4aaafd4e589ef91a", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to latest patch version to fix memory leak https://github.com/isaacs/node-lru-cache/issues/227", "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.8.0_1649360651088_0.45077281608338127", "host": "s3://npm-registry-packages"}}, "7.8.1": {"name": "lru-cache", "version": "7.8.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.8.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "68ee3f4807a57d2ba185b7fd90827d5c21ce82bb", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.8.1.tgz", "fileCount": 4, "integrity": "sha512-E1v547OCgJvbvevfjgK9sNKIVXO96NnsTsFPBlg4ZxjhsJSODoH9lk8Bm0OxvHNm6Vm5Yqkl/1fErDxhYL8Skg==", "signatures": [{"sig": "MEYCIQDlCkz4/8R60vw5NgVZerHBTfwrdsOfr2FLMdX5wytvuwIhAJrygsw/vhP9dl9vZDbQ2jFpADk9v8qL0bmWrXN/rahj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUdycACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUCQ//cYSX58lpI3bx0Reej/67bMRA4D94Gbx5C4sx0CbXh/arQrz8\r\nOnT7LI2DgplpEJi7U86SLYdy7n87IoZkTEVdDIeGk41XZUpPXCUf85kW3sim\r\nFGN/fNPykfQyIS2nddNG2N72BsD2daZYmYAme8Qvf1EvBicxpm1OgwXfj0Ir\r\n6HCEpV7bAkJJhTeILRrDv/aWmjwB+XU8/GLd0QEtRSD9iCGk5y8NtaYcpwAW\r\ntGspllk045BdejxXLPF6XXtRoDn2ZsYhqBUiclcI1KNlyc6EPbWJdX0PepTS\r\nIJ1yKOyWzqU2m+47Q6OtWrXqUWdSLMrsD3nY5SUmBxy8qVHvc5k7xz+0nTku\r\nPDiDonVAxkSXRhtM1QHjRXFhxRV0trPVEvbLQxZfquq3VOQ5KXxvO4U/Zth2\r\ng2C6vyZVm0FJZCwbTm2Dn4mZN8rnZv/q7MZ3eG1Q2dQyS3YMYOt2DYmBzsXb\r\nqZdMIdHgTKE+NF3Zam9gi5gqnH4LWQIMaAAO1gzWdo1frZv8V8P+4hRuJGqM\r\nMGbsZTStvNdHJxCgenaLZECeNsOuedpolSbAqHS4NilWrjeZ+G3O3hNU5Rxn\r\n843wlxz3WA8ySayVE4Y3mA8NAYndMBHuz2qJolu96LcPq8M4QOEh4CyYBaJD\r\nrHGPQOwpL8PEfF0tHKJzf+7eYFEVlFKJpdE=\r\n=zQoH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "1e7e0f1bdcc82e73233fdf0535d104bd5a0386c5", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "heapdump": "^0.3.15", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.8.1_1649532060382_0.7559077276959458", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "lru-cache", "version": "7.7.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.7.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "7fabe5409884d3d2bd88292e431e49494d84ca13", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-mQK1gl/pguIRj8m1VizoMknnzDn8bjxxFPJNtXTQdQWIlGzNIQWEqvSuEoe/fYfgusWQYFXR/muGmGnaYzLfPA==", "signatures": [{"sig": "MEYCIQDecKDxuAr1gpXF2O/WbhJrYgCZyHLp/19/6zBlGzreVQIhAKkFf0W+IHC2qXcCMQLBKvSXSzCYKLaqeg44xJDDufOV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUd1WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqr2g//R4qvmXMwJ4HqmcMrzIFuyLPBvylOWV4xsZsXc3TlPzF9YhtV\r\nmnOqG84cygYlGczs1IV9kWSGcaUBu4Y1rGYuuJDpmENLHZRLOk1pSayP6GWb\r\n2+Pg17jLEUvdpQ4S8dz+rgwwn+nFCDKsbucoMYYG/LrhXlvcmp7BjLXTMaNB\r\nxR+3gzMO84cV1QHoGCuWSOsdWHlPN8N/joG0NWNScqSKLiJbuiZZUdAcAUfm\r\nnyfEQPUWe7W7HGU7rN/oZr1/+bJ79T9LopYhyPBuhTbd+oyreLrNlX4XLClK\r\n3+ruwuOD1zyIYKySpIRt9gspYNH8a3j1eZkR3X6DN4qnnuhB8G3RV6uzOS0F\r\nk2vygBTMFSwOiHM+F3P8lBV6WZ5n0xHCIaBMM9F8zXyPLvSniTS/HbTHbdj6\r\nkXwN7Mp+hUQUVXAg7sCun1vlgnLzWH8G65puyUMUklHmzwBFYiPEvhGxup5N\r\n94mE5AAne/QigHaaaryKpgh1j3qMMJn/UVAYvfA4jQTblIcXqpMkovjvrA5l\r\nyOHnezE4123EtB4YitA1jNH06KMeyx28aXG7YiAqSLCuvVGmWr2zWKrP5yl4\r\nibM4PkhONO85h326lJcvECFboD8HlSi4x82VYfVpreneQ9UH/xmkuSlLFqmV\r\nQDupqQTR5khHHRVQ8l917RuLanqWvNXDYC8=\r\n=FoIV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "711c7be43a93a5c67574343fcef977019454d2b0", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.7-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^15.1.6", "heapdump": "^0.3.15", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.7.4_1649532246628_0.032840497097149646", "host": "s3://npm-registry-packages"}}, "7.6.1": {"name": "lru-cache", "version": "7.6.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.6.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "14a52901b083ea4f8ea6b7ea9eedf8f31d0d32e3", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.6.1.tgz", "fileCount": 4, "integrity": "sha512-ggu423hHChjuFdz9BYpHGSgiYBFV8zJD23WSB1QoLGqX4PGRYc4zg+MblXgPWHToYcUi4TpOxujb1baqgJMynQ==", "signatures": [{"sig": "MEQCIEWr0Y1GMgKCNIQRzDeF5s0a7S+HR7khGfaxRYxNOKmJAiAMk+Ie5srzNwV6jLYi0UC5XuYVuVlwkAyiVq3AtEUeZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUd4SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhIxAAkcs4BzVkxefp1+B31RQe1scW40a8wyOupjSEwuG0B2Yw+PL0\r\ndE3ab4QjU359QxyHftT2y9zBWf0lPVDkP0D/VIcSHM00KyNecxBz6wCUk3Ju\r\nADelZfIymVpgyTbXndzt/T7AmK2aI8Z3OlG3K4Az36JPdJLUMFHAl5H9eIl1\r\ni59uePFM/i1cETbcr0vPc301WdwiPyWed+LDav3z6+R8Rfp4RGcdJuggdMzj\r\nvrwVRhcdz3zsVotZrE8MR1gxFnzP8+lUmn734vjJA6LrAnGIuOwe1diEGvn4\r\niiek68raRPZhvsri1Lpd5LDqP17fBtNk/8cs4YOCYUjZ5fYjW/fV6WtyEzzH\r\nEHxgPn60QAFalw/4R2gqpZI3V1CGHcYBL/O8pHe9HoptWTad2zVShpog3I9f\r\nrXpl6/bfmrKLRuvMGEtmxpUYUAMLo5qEC9CynB2M8FC6HRVsnfB9f5eyNUia\r\ncQ5UJ7sNjkSm0M1oORM6Yw7warT6BBEwwyCXGVyS4bImVD0yo7S+Xbbm58Fl\r\n1bu0gqiQ1npo8KIGIDjp+xj65B2wQGeqm7xdTMelcWoY/oym2WGmXzhVNezk\r\nj7UHbyokXMZi0oxn1Tq8cYhHNAz2GZBlil5R3QwqVsq1RmTjLwvwm+lgKuBN\r\nJAkd8Hzc17RxGuVmlqCmKxhWzx/c6SAudiw=\r\n=O5Wb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "8822133dbdee2deb1d92b96e9bea14c447a4ba8f", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.6-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.6.1_1649532433838_0.13480944572485698", "host": "s3://npm-registry-packages"}}, "7.5.2": {"name": "lru-cache", "version": "7.5.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.5.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "f7da8d0a1906bacb397e0747796d53b08441d877", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.5.2.tgz", "fileCount": 4, "integrity": "sha512-Vd4QEKEWXQeV20F2GffIwDYYDcplgpcozPDzv59jD8+AUgmVYCATaFrZu4efkCeYfMeJsOz6ZnkdWPssI4jRyQ==", "signatures": [{"sig": "MEQCIBnlbNyVMFE95e99TnWRu+SVjFDKNkpArNaMkm9Q1OZvAiA4o/z1061FIY0O4NzI3VJQyQUeXHW84xLi1LSeuxxf+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUd+tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFQhAAlI23dVUimew3/NixwYBAgkfIEqFnqu2CIuvC/+nGMziTzfhy\r\n7pvOoZAB+yibZ01O8kHpe4LSBXUJizY2Kkfb/da8xtni0jGzrIZ3X9O1PPFm\r\n7rpt/lIO3XY8s1bWyvhmQvW5K3qZjkGBCXxfO4PXm/X2+ELrj1ZpNGktpWw5\r\nm2Q9yaDfgvv1tYBEUXRbe/cEj43buF3dUcYZAQsg8CIh6lvq1d1wQuE91TEP\r\nbseJU2zeggUgh70cdfy+IdWL91dUTruCAd5PUgrzOIT2YrOWdg4JWSGwqvZ2\r\nW6VBzZQiK5xsFRAkG7Br5amr3ETXqcmrnowpOaw0yFOg4GK0cCmk+W1cjgkR\r\nJqSULeoS3bw15ELDyRIwvj9KQ2/dLaAr1LWLR98RMTFjCN8sGHHmNyX4Kz/x\r\nCoFS4rj0lb7GhumRaze+8aQEgbVBydQQ23EMaMV+OkSXBnZ4VQp8PZNsHCJS\r\nx07x1bOn4kWzq2R1Cmym5CUYT5yryH6XVTWF3yy0llNej4CAdfV/s45199bf\r\nDjmSX0qbs8TlqZoqurYyFt6V3caoTn2MrO3Vn5T6CXnNyaNBXKR6fUftDqh+\r\nbCnUGInoR7L/Q0MK6sjf09Xx5BgNnkGi9znO4DUf+UovNCnXIq0etoO6/0d2\r\nv260kUfKmKYnfb8vcNnuuTBWz+DTg6cecLI=\r\n=IPKL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "5c146c6720c3b73cd69b3e3ffe700d70a6818f1f", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.5-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.5.2_1649532845651_0.6747761236685403", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "lru-cache", "version": "7.4.5", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.4.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "818618db4de37bca83292f46362429124d6f0d45", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.4.5.tgz", "fileCount": 5, "integrity": "sha512-tT5jwefAV3F9AxyRPO/HrupMB4iLwae2a3sPMzttYKQBxc2/qaOdKUJxGZ3q2pihiHasDfpNkuNHGk92cb4RyA==", "signatures": [{"sig": "MEQCIH+gIG94US0mL/Ypi5busNzn2N+GHn42RDTFWyQfjWRTAiAWX2CEPWGzVler8uZKHCgzuBzoyaVUESCDe1iagRY8zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUeAbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSDQ//YioQDw2U0G5lUiyxYBj/tjY7pn2vs/eZX0DLboDHzLml6tmg\r\nH1oMxY7n6ECNKwvQikYRkI4692ZO+MSL0T3VvU7338xVBdU+n+A89De7Th4k\r\nUOwDS9j0ewGVKS0u9SotA7vC5FRdomnyTNRmCnMDZYAlncu23Vn4BMComf6g\r\n0G/kT3X+Kx4iAC5CQ2AJN9YITwU2Ox2bSW4GCFGuFqM6iSW+0sW4GpM8Vllw\r\nh6clsowh2TQTuz4YnzjpLALnAqBYgSlOuquc/sgZretpdsSHH8xkQMo3iTmm\r\nWMr5Q3etFQagNPmKGmhZcKozsQPtQEzfhQIBTJ44UG6GY5CzGiZbGUHqrrM8\r\nl1huYFv7KO8i7HB/vVFDDSxsW4RpZM4B2KPxDH0+zy4eLlrHrkmPpNL5WLFh\r\n+bAVKHCaW8P7MjxlygebpT9ZCDFfz1peJHU/IwBBw7RYF6ffdsSnf4HxA5I5\r\nvn6LLvyP+7FLAwq7VQlZucQZSJET7QzH0ojdhu+kSloSG4SHthAamFHGtn0x\r\nVKJ+phhy7FC5LmOuPlVqSM1lkezNiW0JvCgIfEbkejeEHR6bsXnn+De2QZmv\r\n2UOTlkej3giOLVg+IzGU/nt8krD+2ECArNEmN4Gw57SnmndPU80AXKgxkPj4\r\nwEJL6nRmKrzQslqE2mRdZdWu3gZBqFwXHWY=\r\n=aVo7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "./bundle/main.js", "engines": {"node": ">=12"}, "exports": {".": "./index.js", "./browser": "./bundle/main.js"}, "gitHead": "a801dc9da5b3efdc00369db735e3666978fb6a1f", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "prepare": "webpack-cli -o bundle ./index.js --node-env production", "presize": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./bundle/main.js"}], "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.4-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4", "size-limit": "^7.0.8", "webpack-cli": "^4.9.2", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.4.5_1649532955073_0.007987751944029231", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "lru-cache", "version": "7.3.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.3.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "a78f086b73a6eb4b61cda8e3e1b86387b4b81d33", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.3.3.tgz", "fileCount": 4, "integrity": "sha512-wQNrRksJ6nnJTNGKgCiOhfIm8l+H6/Ar227nSwRlSAMBKFQdhdnd03DXRZyLLbaNruqPP5h3QsVboG30/MG9mA==", "signatures": [{"sig": "MEUCIGWypo+Zh8uCvPegvA9/WtcXEzK//ulOFwNpmAgcGMb3AiEAnLHnACHZh+r4zudUUubD2I9bpR0WC6TDi4iJ6ojaGqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUeCSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotuQ/8CFogSKtqlacr2AS6dlJSTjqWRG5MWk2Xs8YkxIn2LqKdamDP\r\naXFlxghjADewICdefoW6W025aqdqQzN2UTTnF9mmbGJAcN+6pl+19yR71epN\r\nEAD2ZJDUvdt7zk+3aQH7vhH+T49zmMLkD+gbODiJhZAV+CVTjkf5PAYXhqFc\r\nje/X6ZufaaLRPDkrNchFgOph8ALMa9OJdDffC5jOl1sIH/oPRsPpIdcSx6Lu\r\nGvYUs1BjPdW6Shjl3d4YaIfv/McVa37uGz1PwyyVhIbNaITARHGSbwQBtsEi\r\nEe5ujIstUdrzAb77QAfcEy0P13NleNVm/BP9wZ5YFL0FgwWzDjkwIluD8wMn\r\nV3xNRMhuZ+bjUql2Q+fDBL6U4Vs1eaikAJvUbBWmtH+TXSAb9HG+7vBnPn+o\r\nheNiUBwbqmNTuzF0opz+vel0QaxXBOe/36j19Cg5uRs9AGmTQt3gvtVO+8AL\r\nIhm2bD4I6vYlIyrjPcFju8X/j80SLfoCZ8P761z4qI8cmzL1APkzlFqjpHM7\r\nXykvg6elckgvQfS+eEKIZndymBpYkGh93srs25mnHOKWCbRpv3AqUTynmfxm\r\ne0//rZJzSNChDo4q6VtOqjUA9y7VqLdm7tO6duAyqnAwIDQH08HkHwAaLW/p\r\n9DHgU7Bjkn37EkYF1b08FHB2LoJg0HsZ3R8=\r\n=e10Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "9ea6b31e6ea8ee76df808a26b64f133409ffc36b", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.3-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.3.3_1649533074055_0.05583952193172492", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "lru-cache", "version": "7.2.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "fbd88fb36e3c2abe413c5258eae7f4587c44fabf", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.2.3.tgz", "fileCount": 4, "integrity": "sha512-aS92ErFQmSZtXltSlLANepZOpcveIDHIpStGPzwWHJYFS956qGp2BpN5Mc5r1ZFyJCLQ9CsVIQTGRZpUrRo3NA==", "signatures": [{"sig": "MEYCIQCbf69H2BE/NDJS6HfOA9/nraZrCcUpNDYdEKwtHEwt5wIhAMuzuFMg63RQZjMrVJGHxVy1F3oWCbRp7sk37zr6mSuP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUeEBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZCg//QOEbRZNhAQI2noxwDtYuUzgT9ifDpNJfZ4royGvD9vcOi46o\r\nLTziG9hoFO22S5KBlmOKqJ1pTRrsMMHeyCZd5WlavjRJI7PHk6WKu8ptZl4R\r\n05iCf33XzVdOYyjsP0mSvr++vF9cthqJL0V0ZCJ42YFbs05q6r+fGp/6wRsC\r\nYqNSUrsqU+7UmH46bLTyuoLUebRvP5XzJrjZAnzLeSCKVs3uM+KliPwKgLg5\r\nuOlTmhzOFjolgpKUCPMP+ere28JLa6Z69jUlcBOqtYVcYVOqL0lK0IME0Hm0\r\nfLwbmfWVySVUA0DJM28W13T9cvPyMjDeE+jOKuh3sf77cRYKHS87FcaqcUeR\r\nx53Q9EpTmwpxIPhGJtDNQjhYIO3JfTCgU1xNrShiXxvSsISd2bZKm9cfd/yw\r\npSg0r1DciRk1yjVUGAQcu7QZRD8380Blrl+uOYtppf2bV+OwV+ky3mqZw65x\r\nI+ZD9dQFy2NiURILM9tl+sbvF4CPjLFq/nCWCuQLAEtMOMQ7ujzcQ6OPZ38j\r\nT/RFaW7tLHJeTNZd13Dmgr+lHCrZGEoWspdaP8rRJD3zRO5uGlTSU9cFPEUu\r\njfBdRmxV8CpsuYjCRgdjTFYFfJxQ5jQMppHH6Vafbg3xQC8fTSd5T+EyAGnX\r\nWqqRGh2OIi247GqJQhiZeNK2zFjl1Zlz8UU=\r\n=pjFI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "83732579b980df95d5215886e701fcb1cc3087df", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.2-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.2.3_1649533185138_0.0879470212388509", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "lru-cache", "version": "7.1.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "8aab7e6d92732a0218b9df5b7218dd32ef2b414d", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.1.3.tgz", "fileCount": 4, "integrity": "sha512-cJyvx0rDiKcK4VXaLUoR1aaSbWJ+ONdYcyIjw0cOjaj4Z6oAcxYejWpkYRu6DqTRbYvBBijnLMiffdUJDYLF1A==", "signatures": [{"sig": "MEYCIQChAD+0BQTCXwqtcsRu5kKpLtGs5AQtrfFGe2m3lvKTswIhAL9I/2SrXGPNErV9gWJnuoM/j2gnXp+/JAX5KcTjWBXo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUeFyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqULRAAmY8adZuDNmPsWMLsWAwvxPxWODRkaf8HjrK347yC+gUYAikQ\r\nz9RUY0crHae4EC2G4s8OMDCqtw2uj3t7XPGhNJkrS3SwnzT3JDBD9J/LBsQa\r\niMMdGjZ7LD38dJaA/o6KkOrZGyVhl+5A3ho3X3CxjULt87CGmyMkDYqE9Hmq\r\nKvWNQIlhyDkJDT7hh7ZF3cFcs5pA0ttLevLwOmBOQVtxYahjz0E2fhdyOHfl\r\nU4HNBRbgYqY7Z1wJXslqLRsqau5CiGuc4P+qY0zW7UH8c623P3/HBdHwNweQ\r\nG5s72cAPbHKqlPmpCJ1pAHcsB2AYU2G2pvBgfqWSGsn1Hk8qzLjeHfpl4+Ul\r\n1Km4uqhNw0d+Sj7PFPHTHxUk+olyF0olF7dtKJWCuhx7Ie8l8N5VlvlNZkH7\r\nHzyfjrS388WGA+yWR+SazMXhbzgE6/YKrRHv/0jIio6ABPu9R+91lza7dXu+\r\nkQM/ueTj7dAPk25Og/ZLV2bzZpQpRgZOMbQmNMBmQmYn8OmkV19gBc9oQpuk\r\n1RXuJbSTL0JVwLR5+lX9AWrQs2hj8XuujrY3pZCtlbUSarBGLKSvwL47DB5r\r\n6g/DLHoHEu/tOVLtZjj3MtYjRP1bkseK7Lvqr5hl2TTGLnUeSBqN+rVPEnAV\r\nJabbMssvoZNKIP/FsHwNA+ishlj0ooeRC40=\r\n=3iPk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "90980da4996ec7b91048bd13b0a801047892e5ab", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.1-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.1.3_1649533298102_0.8880244259816878", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "lru-cache", "version": "7.0.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "505f3e1dc5b2c0189bff238a1b98dfecfc4aa8dd", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.0.4.tgz", "fileCount": 4, "integrity": "sha512-2kmlhulWWdRsG+ELcVC94Gs0MRurw6Y5Gh1EHMzbYnYkXeqNHtGg9PTjlcmnT+gxcihwlYwjJ+/gYQl37hMV5Q==", "signatures": [{"sig": "MEUCIQD/2ULncTBQFof5SPU3nFYJ1OLxdRA04quMqLHutFVi8QIga9p4O/FgcyUyx4VwgTXnNoJAaXOWjxmBNi5+oPxXEG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUeIyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmptqw/9FQjS+b45ECps5qMEnhfH9nloqBg8ewiIXHIrVws4MKDzXgbC\r\njg4YTGNYubE+Ei5boHAb7XlSqXENBo9K/qmSJSjMCdnuUK7ArMCDtpgvjeJC\r\n+msfA0ohBeh2BqLhxsVgmEXhfVkmQCZRL2uaUgLPpqEDXHiEXlYxZUyTclXw\r\nmSfH1HOwqQhFbG1pIhIzeJKRVtcdXsUw2GkyHCo+ZvLRU5GZWClSNj7D99W0\r\nQw7ixABMRiNHEnkxJat07B1Gvdz4f1f9o+6Eo/80Xww1mhVCJnTcdZysIRsE\r\nSD3Vmf9czeSfXKH7QhzNmmITonP0pBxiYsqusWWAPT2Fi7yEZrcF6W4vhpnC\r\noOH8bMc/S5E7+iQ4ckWDiFry4qdSE/C9+v7W04wlC31blR/3CiwYWIO6D5Xa\r\nUgg5+QcGxYXbpqOmm+ux0zY9IN2X9C8F5RrjZ1KB3xDxO4RyruE6WU13trg/\r\nMwdV/N9ReRINZBksawbs7bIUk3oRNQK4FNXra5NFAeTKQmJYjFIApUi+D4cv\r\nDfc7owH6/5akpaXp12503c5rvG60SbYyHExHIxCD2ErGQVGh0opMB1N8Dbbh\r\n9RK3JB4Fb3fvbIp2k/IzQ5LM0vXxap5FrmEeRoG+LD4NKrZVZGE11jpFzqQN\r\n2SLHNK68yBCTpovynRzj4jXlfO48C0FUAy8=\r\n=w6Gz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e92da0a6af50a6f181eeca48cffaf266e2dd10f0", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "publishConfig": {"tag": "v7.0-backport"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^16.0.1", "heapdump": "^0.3.15", "benchmark": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.0.4_1649533489992_0.1583613736748093", "host": "s3://npm-registry-packages"}}, "7.8.2": {"name": "lru-cache", "version": "7.8.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.8.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "db4d3bbcc05b2e7a2ae063f57fdb42d8d45f1773", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.8.2.tgz", "fileCount": 4, "integrity": "sha512-tVtvt+EqoUgjtIPD3rXSJCSf5izSRJShgnzUeK59T+wxZ9LrFEP3GxhX/Mhf8Rl7kk4ngd4vZaV+5sEibhvQ+A==", "signatures": [{"sig": "MEUCIQDpkucdX8s39ImIT0l2fNCnArXNuYpB0D6H+jckX5yhAgIgZWesZO1ojlJfa6ZbJnVXntsy3akyoO1K5sptlEZnRqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibKKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoO2g//WVLZBn4cx2FiQuVKwpqKSNji9e0kbKejhRTdehWke+tSw49O\r\nNnOiDRXAFy1Unu64KaE0Ge8FdBuP+PTyjy/JoNObk5r2T7e5wxmeb83fXFem\r\nk9GQMTaknHopIe9CT7b7nMXjyDo8BtU9cvFpJZ/8AVXzYyB6IQAI0blPwLlc\r\nB7Q27fltM+mutjTHLBrwsnYf2DTsln9Q/Ofq/61W1jIKZOUzzdMekA/zKK0A\r\nRLV6JfR4/oEd76qP4GjdmMWxvVVfgheGp7yUMMtKInV1nm9a79rhyp6yz7Rk\r\n5liRzjxLW3oN2wPEGwrq4RIccqnxIV4UrDAkMbwbaQrqRJKt8cjJLQ4E3bjD\r\nx+eVNBooEJpPVDDr8xDAHUrhjTrRen1Ja9DBF6eNAywdk1MLI2wHWGH3FVKO\r\nAKvGX/cZDwnvxT4aXVhHfQRwKZ/ycWvOjmblKPnae4UEgdQgHCjRZ4/MKF8/\r\nwpgOTLZND92R0Lddx9QyPzgboB+n3Lmt+4jHPiCLn3XD76HNwSAv5Wq/ADJ6\r\nzGIE/9jubjq3aOh/m2So3hMt68Dr1uJhBqZt6FJaUSn1FJZdhKRLwaphGdOt\r\nrRHHlfsjHoqUBTwjSYThfRSAXElt8NG7naA6z3RnJ3Rf59rnOnms85MHNnwu\r\nk5BXOYVzrd7qnxjzlcE3IP7MAYTGs3ZJkEY=\r\n=EpOw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e71778502d324b79c3c304ce70e96be3b4cfdc49", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.8.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.8.2_1651286660471_0.8601136598219326", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "lru-cache", "version": "7.9.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.9.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "29c2a989b6c10f32ceccc66ff44059e1490af3e1", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-lkcNMUKqdJk96TuIXUidxaPuEg5sJo/+ZyVE2BDFnuZGzwXem7d8582eG8vbu4todLfT14snP6iHriCHXXi5Rw==", "signatures": [{"sig": "MEUCIHxvKuZRSs7BQKhmE/Bz9Lq/feFw1BaNn3gvF5vbOrBuAiEA09RjMQLW3ulQ59qPF9yRdb7gD6Lf4KBQZcQ+A4tu9nA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibMAIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorVxAAmfdiJ/Ajx5bUOt2qLmT6WbmIQEIKjUbDv8T7rwR5Y1mKQQSa\r\nBi3mUDcSUtIvHjv+dZVSOk4E/zjOdDAns7ciAzu9mddKl0bRGVLJYCUqzex3\r\nzmz3NQUB52BBHKVKUiTXnTRpZB9NTEf/yfEMFykI+tvAE7ZsfetwsIDT0rdt\r\nwM/63vKOPK+zNAOXEF3yggznc6pZdhmINOc2QjyYiVfGRrTEy5v3msAsmUrv\r\nA1ewG700p8Xgnau5irrFxTlqKP8RMBrNoV7wZo/EdwseJiJnL6IpZlMYJj4V\r\nGz+pjqCx17LV1tNPLmEwY3YeBEqgPQCt8Z8z7DBcIFPWCDFvQWANTImyB7yk\r\nBs0D3nIqJP8w88LHoetYhsMAyryGZiOxcEd8yXEhYMIgEhTjVSwD65Xh/ExW\r\noXqM16sCRyZ+zQYc3PCwmpPme4H31JPLbwbU1ITc7tk+pHpFlSXu7F11W2YH\r\n8p1kRfv4uDAmkkbEBksSrYQMaM2RRe3IGTmEHIC9TPMbolU+8IRpvie/fJIA\r\nro4PEuuUh0+XMrtHx/Mgc+N/NjJevpdpJbuRRFl+b9U1qsg6oNzZ2OhhCiaq\r\nOD0n3IJkW9HD/+XUOgm41y9x3cnbCdO5KWA7LMDocN66DxrmLkcxtdPkyob2\r\nDmGyzLUPIaINx1ddqpoEhHaXJC8o32W+tBI=\r\n=T1Z+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "892db2226254df892af461a272222cc4d61454b3", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.8.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.9.0_1651294215860_0.1613350285048809", "host": "s3://npm-registry-packages"}}, "7.9.1": {"name": "lru-cache", "version": "7.9.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.9.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"node-arg": ["--expose-gc"], "coverage-map": "map.js"}, "dist": {"shasum": "f1e19ff47b4815aa98ef16d7c30024c1e3947da4", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.9.1.tgz", "fileCount": 4, "integrity": "sha512-ZPHK6KZ75hO+eWpXJD7dH0V4lY17SDyRvRdZteRpFt4onQoAV5v8VyZMBjpEwOG5ZZT39IczmIv5nzqLGA5CTA==", "signatures": [{"sig": "MEUCIBON8gN6cAR2FchlEAQVb6Z2v3MPZIRl1HuLVVGbxiIJAiEAlkPcj5KBAti8K7f2aoA9obOWWI4WH4N5kn+ev+ZfXEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA45ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog8A/8CqYw1ARiPl2bXJHKtCVYTNeNCfjgoPFaMhGSeY4HznCjigUv\r\nqLGZL7Ra+P+hZqgJgQ+PbD9jooBFt2xu2qRcGItQRuaifZ3hyDiqY+XNUCnH\r\nZkskhmmeb+WgCDscpKvyn6Q+ZrkTW7QxFMVPWCiqqp+jLpyrrrlvaKztwUa3\r\nu7Qwr1vVIfdrIBxTd5MFA9kHVES3AQS3aad+J2rr7tHqRLc0RuSqG8gw0tIB\r\nm85P/D2bZo0O4pWq/P2x2VkmF6/GuFyjErGH8G6/8ntuPnyCie+FLVjt5XJF\r\noAUNR4e6AzNQEXJsn3Ca8HkzIQOJoB0vwhwYsXuA0c4rCbNdJmGoShJsTXsO\r\nB3O9nwhKC1z9ehPyCq4lEcTxnqTEdj4//ARIPzgg+b176TdsxJN4Zb8NvrH3\r\nhtLBUuJpdRmy7eyEyYD0e/AxW2GKR8g8HL4RlndmHpaTmXlrEhz9Bn7/Zek5\r\nU+TQR7qFAWTGnI7LFM0xfzWi79K52cJSTMlpNZLRBVT+h64lbm2kFibIYGgF\r\nOtZ5krIVtocYfWe1lLbBS4MunZwZogmcx36qYs5Z0dAEvfEwdW/G7x0MIM3q\r\nZEbeFeY2bKKnaYesUcatXiAPbAYECPwKTFgjr8FNE/jWKshcVlaOpKdeFAMU\r\ncsdC+x25y8Eapw0aXYLa4eRchjhWlE01MxE=\r\n=18r5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "32e7912b07c5fe68c8ed8a32d7795112eaf4ac50", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.8.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "benchmark": "^2.1.4", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.9.1_1652297273152_0.3353799908560604", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "lru-cache", "version": "7.10.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.10.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "db87bef52eb5036bd66f7f9c32cd1d614b5652e8", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.10.0.tgz", "fileCount": 5, "integrity": "sha512-mk5BXponDPbfvGlRuKBlh8YefbGXg61gUFunI/z78Cl+XXUgEs6PSvyoKVjfGLwT79Rk1V5w6M3w52p8eBm61Q==", "signatures": [{"sig": "MEUCIQDGEjz5RYbZASjgF/4VdPImrXtQ8ljBIujg2C+2AYGYAgIgJ0ZLcCXZTF3cy4X0OvW7NLmDAjUsc/oMN+09YdMkmU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA8yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRpQ/+Ipj9EH9qUbmCLz1shGpID4A9kksySX1mf9yYV+Wfd8Zy/KCP\r\nT2XVp8f3fndmyEJQvxUcZisSAfX8Qg71yScoNyaZBTRiOQO81GkiyqpF9TKX\r\nwNFqRUF2Mm+ExO+RIpaHfdfIFOt/nKAQaarMy9r5rF6Cva/xnuOOEDWBdV1N\r\nZGI008iGS3PsXV5+j0S+G93GgfGCKWXJeedmwIPB6O2bUB+E/OVGvsuZmQm6\r\nv6IetP9HiA4OrFE2qj117UK3lvf0701/yZZYO+uR0jFpRO4pZ1a1rK5Q7hB9\r\nd04j87X9ZBdwh2PXCIHei33rcwHi6PosUjsAxRPpvpelmWEdG+xF5ZAErt80\r\nOd1ttMva5JNgi2czbeoeFeU9J0e0ZOuS6aO2oUe5YRnU8jOzEA4ekujXEJtg\r\nYrpjlB1/+yDwOML2yPGKgEVLOyD8bhCsWUSBsRoI2rgVEJITOr9n78e/AQPo\r\nuxVVt0B4u/XF1SL5MZ8jRqadiCWfwwQ+O0Ue4qLMkumlGGiLM8muOqJH00xB\r\nHutcnA9sHodQl6bUPCIwYdKBF+w2qrv85A6SQn6PK6f22SDISF2omqO1psSr\r\nETsRrUZHj0OUSCT9smL+zsGrPOJTLBWnm84meQR+tu4c9X6FOc66bVmEbeXd\r\nXaJ5BVAdNiJn0oJ0rmyENCu8JS35DmMVkmw=\r\n=U9Y/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "c551cbf795fe5d7a19ad6786f71ca9f36f08f4cd", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.8.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.10.0_1652297522418_0.6961966610383523", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "lru-cache", "version": "7.10.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.10.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "db577f42a94c168f676b638d15da8fb073448cab", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.10.1.tgz", "fileCount": 5, "integrity": "sha512-BQuhQxPuRl79J5zSXRP+uNzPOyZw2oFI9JLRQ80XswSvg21KMKNtQza9eF42rfI/3Z40RvzBdXgziEkudzjo8A==", "signatures": [{"sig": "MEYCIQCnw1Bn2yqDe/3ntgfkJ/NUkwVpF5I+cMlwHTt6g2bFZQIhAIpz5jwt+63CMN+I8MN5BR5XNHlxDffgXVGaWzSbPnvh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifD4EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqp3BAAoNLwWp+PV51DdzcTU3cA9GDjoJqDuHWBeakFb8q/Tk3juWjw\r\nDEc/Wttr/ZE6G4N2jPDx+93cGmP/N+jRwQEZpNprK46jSkgk0hvYmS6fwhva\r\nU0m1eghwccj0LuE2jUVdRbcsTS6EWPYQb8OcNhL8i+/H6TfL9RKLnhFgJUP3\r\nMqFERiCtLubmPoPbmEsoiPdYHUR09TDSPNKW7qRm2a+3xnQoqSZjMaYDMlkL\r\nkyuqWaSC5+eOpeJQXQ/vrtFtW7nEpvrg8Bqb6E7UZf5CYtOPLxD3zRbGjJCU\r\n8VNa+lWoTbBvPAW1nxMkjewuwDdmsQLm0a2ej/g3qd9C/3CCfjucNlhv328Z\r\nL5wHy6vLyllgT+trZDqwLEMyfsZU5IbZkkiOklfDTOdXooYfv05Y1ysMgwYz\r\npxiUcGmEblE471xze1haT9q4WJmc4faZ38QJprbmkrqwZ9rDBhf9zzv4jtBX\r\neXpe28iB8YbirTZDat1AfC40CTkqMO8zO0vBdbmMB9ZG1669CobT3U1dtV6H\r\nSWYFfztPsf8h5pWIFdRM9wiZk7e3lMBZev7GIh4vssl5wDPBnNgcg5U8p7bm\r\n0sV3YWsxUV5zVe1gDanfx5UW4VKviTp0IN0b/En0RrBiOHRzZAT73ERxVrIL\r\nNooQl9nTfRu0N3CTToMFMYpCJZIu3JUbLrM=\r\n=hgnT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "b486515795a4f245f65d7425363486f33f2ed7bf", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.8.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.10.1_1652309508275_0.07397421288859918", "host": "s3://npm-registry-packages"}}, "7.10.2": {"name": "lru-cache", "version": "7.10.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.10.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "aab494e0768ce94f199ef553ffe0a362f2a58bb9", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.10.2.tgz", "fileCount": 5, "integrity": "sha512-9zDbhgmXAUvUMPV81A705K3tVzcPiZL3Bf5/5JC/FjYJlLZ5AJCeqIRFHJqyBppiLosqF+uKB7p8/RDXylqBIw==", "signatures": [{"sig": "MEYCIQDBPZN9J+N+uLTdTwvHF1WoUx80P574/ecXPO3R3/zeZwIhAI+1U9kRMWTcTE4hQSQcNj8twXkFK3Fgoeh6YqeL84Bs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitOmYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvJA//RvZxEUfyehmIJ//m55cIwuVRmzI5c7dNvDY9OBBAM+hijp4q\r\nlxGTc/AtHHq1el103rsOSrTQ520ZySx4xYA81g0h6O3unM2vqZ0ImAq/fua5\r\nKpObvDXMM51l6o7a+C35+P/HA/UDHakmgdDiUjkceMqXUdCpj2eEY0yBHdhb\r\nAUdVBHig7nQO48YaS+diqs6wir9LO1z0/lDiVNHBiAMdS76Zum8JtCopydrF\r\ncTD9YUslk4tK5GJdlNms6XZ5imrVZ8M5yRDhOxXeHPPYXRFZH/mJh6FS+BjT\r\nLbZr0rkfz01KQlKSAhx3uFGiKQC134BuPqccnu+TwF0E8+ULfZZrxFy0nxh+\r\nUzek33AQB+NkP5Vce4ShQmDW/4Rba5lUVGkdGAJ/81BH88VVkfiMGigdYc+5\r\nPXMdTGhkV/aIwlurIkrt09YwYl5uIa3VAr1KjJGmFE9v83SCt5RwroflvrkP\r\np0r2EjBYmSa5ngec0gd0oCVcJHx0Ei1r9WGuNCgCifBvfXfpsE6QI1rVq/Nx\r\nHhmCQYr3ME0QhFw9gkMD33bXV6EI1eI+W+a0zHg6cl/xU8WOVvfjGP5IIhkP\r\ntVPftyd9z+wxiXIDiRiJoiMhy9kgOJjwkkHlBfa5qJiiGL1fEUjcxk6mCLjx\r\nahtteswHwCx0dwuwzFr4Z80h8DCe8zrCZv8=\r\n=1WlN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "ea673da69acb3b5c2c105f2d174dca2f7480fea0", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.12.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.10.2_1656023448274_0.3368364984252705", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "lru-cache", "version": "7.10.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.10.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "8c0c42c48cb145a1d568fc288377e8d75c528bbe", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.10.3.tgz", "fileCount": 5, "integrity": "sha512-y51R1ks7W1/LXKf7gPUKFB08aJakxfHKNp/B9d4jdMtryARTFc6rtU5LCdIS7v4L0ZAJnGzAAXfFI1deF6pDTA==", "signatures": [{"sig": "MEYCIQCgKu+VT9a9uigDaAGiDaM68Qs//6c5WEr9O7ix2BOGfgIhAIt020mJQUCVwp0uJQjHFgOGN+pmvTNZe99+IaMdoqvx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivKW1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqkqg/9E8QxFFa4Z25Z3C91IZ02EvxgpEvBkfNgqaeMJWk3/jDyWnr6\r\nxE4bo9O5WevCw8BLlqT5QU54hRlLFrmermvSEaybutete91SstKSr8bDkHlZ\r\nTfibxWXHa7xwHCjT+73b9AR2nluXS5bM/yKNEwa2Zb4C1vZ9nLKhl8Gbp5cp\r\nJusguE3u3aAD9MDNrNujSCx/V4QxyuGQWUubN/OcQWme5Ii5yzIDSR29ETVW\r\n4rKlWtngyg9meBmT3QpzQ0G0xof43fwNLVEc2Wl1UM5X9fD9xNSHU2KCOOX1\r\nLS3YoQATkuznzdcTHkjhGcL5xH38S4UNpjBg4S4hdYZBz+e4wa/viN+Z/daL\r\nLwOZ/7XsA5J2gi2WRYG7GmspYwQCr9kQWFeXvoje8aAFiqNrDq5RRSSUvGx/\r\nxZJJyIB4XoqR8a1i1Y9zm7v6gsvERCt6f3QABgUtuXkUTT7S3UT+IYmxY15m\r\nh1y+tYV9ufL3W5Oho0TPb2u4hpuJ9Z8CDz0xjyK8v8jgTmDI/6/xUzFuh8+Y\r\nfJYgUQr4KEJUxRIoTirr9EO5hKg+6eab3SpHZck3WMpHLfLgEJc9d6KspF/J\r\n8YtqhY2QIc+dcNS4PS2DY7V03HCMy5MZ2ZURL8j1kVYZIJ28UD4AJa5jsXd8\r\nwLcErG0LiXIGCM+GBfc/OE7ivBAJ6FFHAnw=\r\n=SSBo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "b9918def87157ec802c5ebb2d8c53f5625133481", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.12.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.10.3_1656530357449_0.6084094548930938", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "lru-cache", "version": "7.11.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.11.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "e8e18a08af9c2af3fabdfe0cc43d24aed94a5f3a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.11.0.tgz", "fileCount": 5, "integrity": "sha512-cMXDHMxwo6Rv5Zdv4ReNNSpRkCTCRRV0JGGTaF3WN3emk0Th35YNWr+U645hjvh+RxjwifVYoJ6368fKHtVBKw==", "signatures": [{"sig": "MEQCIGij/ITmloyohVSOvLTLOckOjUojRBYhgpbgxIWClo/MAiBipmsncmUmtUQ8zaM/z2aPq1L00Q5aKHLk03TH5lT7kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivM0PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwjQ/+ITtbpTkHYwZ7FA3etH1Q9d1ekOAgM3zhVDA/V6FP5JtanF73\r\nU/ZHXZsKaQAW03+wArxSeWDsjHOozcvrtTFHsI4AYQibwPBoqcxm+FbTJtWY\r\nfNfRfvpvdoks8UO07aC7gzL0lv2m69SmEsLWIHomIh/3KPte7U8DcinvTNJq\r\ntLgg6h3srDUpdwAA6t1N4n3HJWhy6HNFKlaDkB2vZ8KCGsRiQYZpS8A4CDj1\r\nuPV73rRjMn5zd6rmklU7WXTV944W8XCf3YUz/VpA5Z1mgJE5KckZa5NGA/pf\r\nNdWRGLoOraQRBdXwfs/SjM53H8OZenxbkYmoN+iAT2s45DkS1CppdYS7/9XZ\r\nwKjVk9uuozVoe0I3sdy2ySgEp9atAptk19sFxpFW1rxqJP5CguKLh8iseysP\r\nwyW0uTEmb+bPTAJYbFlTJcpx5tqPAHwPGJnzagyF6hiRJQp+4q5p2hZ2VtZA\r\njH4YSM9IXMMLyijo6EmpRIdT6eXRdjB07mLUNuhO1OAL+0TvVTjW/Hy4lKpw\r\ngUbqfdhRGkB+WfLbXBaYUOmR8EUjUTHBIQIbvwiiLEE/4mX5EgPHufMmz2TY\r\nVJLfJ4ts//9bk1Jhuk/GudvoeaVE1I0DKxGtqpIokanHRH1WnfvwircIcWz7\r\nPs6PQYM5l0MLo4nKs12zc1wYodOam72GZl0=\r\n=SCAX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "118a078cc0ea3a17f7b2ff4caf04e6aa3a33b136", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.12.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.11.0_1656540431667_0.247888844518644", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "lru-cache", "version": "7.12.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.12.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "be2649a992c8a9116efda5c487538dcf715f3476", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.12.0.tgz", "fileCount": 5, "integrity": "sha512-OIP3DwzRZDfLg9B9VP/huWBlpvbkmbfiBy8xmsXp4RPmE4A3MhwNozc5ZJ3fWnSg8fDcdlE/neRTPG2ycEKliw==", "signatures": [{"sig": "MEUCIQCft02IZqc3sP2q5bIAcgId65fxt776fuyso++2W08CXgIgSY7JouGHLidysst9UhSDWEPzoTu43KahxHYE3zIlTAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivNVfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXlw//a3ljBz/5pwA/O2VLRJWDoebU3w5E14YJfEla5TzpSYWuC7a6\r\nfP/Xtuedg+gW0gPuTDXx5AUg6odyp8AAVqIlHEm6NkI6c1F/0cmc9kez/7FA\r\n2qZHx0ruLuBS2zKfAxh2mOcBykrf7jsy6N1wsqrqSuhDmv66Ed4RB06vZXfd\r\nMB4ChhfBtAmfCCsu5NwtFH8Wn79llT/qCmZqQxsxwL2Tzl3cVV441lTzEJu+\r\nkaGkGGOrfefaBa/JUu3AqDVldcszDmioFJ/WlvvrfzA4zlz3owaQX5keAgJn\r\n4MykehtFB8uSWRd1ltzNqcfitwf7MPyEHHoUTcoRed6eC6qWNSCNpbX93Z2z\r\nga+FbiEp0nks80qNHsF+DMDEFMQMVQb/HrRhcirtgW/ZtdDIsJgPuEicNJtO\r\nMinAglsyUBN2RSPXcFnvl+MYFbp6f1PTgMk2i4jyjXxyrEj6j8dWAwEmRBGK\r\nsOyafMjxhdLPT7BkvctKecKa+yk+zZ78VEY+RyLcki3keWz+aDjMrXQxNs/l\r\niJK+24m5J8N9Z6ZzfMG+kUqXsfpUhDq7fJFxK2pxYq1ktDzMy9DXKGtqncmi\r\n9f/UoEvUuj0B0MsMVc8JfU0YLad+Lxy3mOGoFVrQtRt9bBoEO5E+EhCaYkpV\r\nmdYA27WE3oQDr8TkZLbHVLI3fiSpzIvPzSQ=\r\n=Z4QL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "7ef678e4548992a8640ba5dae35f72590f9eba7e", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.12.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.12.0_1656542559256_0.3493810587319208", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "lru-cache", "version": "7.12.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.12.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "c8df7cabbbf9aed9c3a910c39da74f984a5eb53b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.12.1.tgz", "fileCount": 5, "integrity": "sha512-qYiZKGl80IiaXkBzj2dZ2JqdzgFKh2/MAwjAAE6KXG3wLIE2dYVdD712fAL++3dSsQGBm1QDJTegFu9p+fDa3g==", "signatures": [{"sig": "MEQCIECXfamNOSMfYcpQbns9dxkARHNyALMFpQJtMi1aQPkqAiBAoyE3h3jYC41ZP3oYOWX5prZ2RTkvLjcP/JBZmzbwAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizf0dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofMA/9EIva7ia63bwq5k5k7USm1X1+bnGAdGA985d3JTTAhvQy8Fg/\r\nm+JToCzXCZlqGayGV7HaxgZwNwMxZvAZ7xUAM0kTXLT4WCFFglrWobwOWYOj\r\nXsyq4Qqa9IvJDh7kW54mObvzLs9Bp420vvSD47PFzxwo3ArC8p34GUBIvwkU\r\nVK42kUZyUXasTADx4r2vlLoyiLe4piMAh4lrX+/OQmQUM0dDOfMmgac5lHy9\r\n3YcJxR9lOARsF71lYf3//d1z9+5h4PLs2Ntq3u3BV49EEW9idPgbn0/a6EEd\r\npmz8E8YkyrDkWNNl3/cwt6sqeHcjUl822uRBGFRxyfGrz8lOxP546x8J1/ig\r\nNXowIpAqoHvGCJRRFiMcafC9TFCR6K5nfC/KKwCiSf8hsHZfZvcDI1oSZO3Z\r\nmtAnY6BJTNNgm+GX1iK3RVmi8GRwGStgjwXMUl3i928eljNG0s1vSvJNPDoH\r\noCINsAa94Vqh+OCR6QpAujOSFT84uRk36KRaQGltr0falsP17t1rCnQ21apz\r\nca2AEil3qGq5Q19t86GQj12ZNGsheUK2KcAYIWADyUsoh4FJ4o1WTuww0+A3\r\nO/Jx7SJxGeKBXroDS8e2AXnG3URRiEOAp5I7GmmlcIX9X0UHESmjjOYgPQ+h\r\n7ai6xnzeXuFHqBGbshb0VAWXolLACIXdk0Q=\r\n=OcXP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "9e48f892bdb8c0f8e640094ebce6dc59833b435d", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.13.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.12.1_1657666845109_0.6798962179611703", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "lru-cache", "version": "7.13.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.13.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "c8178692969fb680cad948db4aad54066590a65a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.13.0.tgz", "fileCount": 5, "integrity": "sha512-SNFKDOORR41fkWP3DXiIUvXvfzDRPg3bxD1+29iRyP2ZW+Njp2o6zhx9YkEpq1tbP0AEDNW2VBUedzDIxmNhdg==", "signatures": [{"sig": "MEUCIQDyM6hmG3zzrEaOq96Eb8CHtYw+BRspd4Ev4bcUtBru9gIgJBMM/tyOSiGA8eTcLyZYvU1LcKhuL39SU4aWqZrK2S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizgDcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ7g//azdKJ+feAa5BnbDp9vFk501wxdVPp++T/NYB6apXzybC84kg\r\nVz77UuUKYzeE8mNguLPH63NQVy3gdCLdUf0xcc49YVOhWhA1k5Bia5KZnydO\r\n+USZ5iTGHl3dnQasTzeSRa8vJ0gXmiQUKg0LH1gQs/WcQd3d5cOFPpGE4LtR\r\n13dPieqACIRgXrv+HQYvkFl9RZ9oVKNRaQ7ux/0ciMHQUWF1PNvWAJSmPEmm\r\nVzHA580+BCZuFYd4EDGH7zyq+XqbZMlYCeVcodTe2p9hrqOIQA9h+n2oszA2\r\nJdDQlVkwzslR3EjtrtzQ9Ysq/HAXIZLNko/6BSUM+KQWNeoZTpi48E3NQ6SL\r\n3By1YwazkFtAVWRsMvioGHbuWCn8+Vi3GWLD6pea6GevCYwm9v6uUNqCOkFB\r\n41jtyvJFAUs1/dEjUiVrR0uSD3jpfOAlcFG0NdTHYhzd/6Hm7up+CXIolMiT\r\nf7Dx1kSlkRX067CSnw4Nq0Bds0xGasA4hq3XNiPx4PdAIpHpKRDB5wwCDI3a\r\nZtCbe+vUKJATccjvcex7dm7L7L1RZsjiXI56NtHbraceSt0HcQSD1UPfkNVs\r\nyHQU3uG6ljC19oPUElkJ6+qDftTSwrailM3K50OrEQEJWc3pqPZqf2zr+5Lf\r\nuIzvaiTnagfjtyQKN6L6uSXlbmXUbB1zvco=\r\n=FMKG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "c028709c722214603a423860f855d4ca9def6141", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.13.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.13.0_1657667804110_0.06700403111055775", "host": "s3://npm-registry-packages"}}, "7.13.1": {"name": "lru-cache", "version": "7.13.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.13.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "267a81fbd0881327c46a81c5922606a2cfe336c4", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.13.1.tgz", "fileCount": 5, "integrity": "sha512-CHqbAq7NFlW3RSnoWXLJBxCWaZVBrfa9UEHId2M3AW8iEBurbqduNexEUCGc3SHc6iCYXNJCDi903LajSVAEPQ==", "signatures": [{"sig": "MEYCIQDzy+mqbCu4mEiDuNU7VJlN896gwkbuVJdKTODIrvCR/gIhAOZEy1gxegAMLGyb6vIQbY9b6rqFsiSWES/XoLx9+iNs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0Kb/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpI8w/+PHd95rcCDH8kygCmeu60LY4eJcxhTaQYIQJ6Zapzua4d+4MR\r\nSH9+13xS27bUvtbHdrs0QC1ct6PL5hOSMGDUXIVVv1MJaMSIsZWgnnAnMQJT\r\nDiPtWne1nnd4/BX+eihQsgIcOJXniDMOcZyQ8wSwv9Dk4Iv22S6qxB3rnez1\r\n4OANZI1o4PhFcwi5k8lpaFfvsZEWdapy9xgOA7eSZ/E+JNIXjwCBH9m191va\r\nha/hsXiyQTsC+t+q3U3EYCMyyoVVE2/MbyJUJydtR3+gZbO0aJSWJM0Le9ar\r\nJfJ18BU/pzWMM9DbtgHhgOqBTjJNGfVIPKIdqSY+X/XRX/GBMSX8N5/nFZ9i\r\nhj3fJ/6JLD4M5Nho5eMAsfLH7T0Y8+dVIKHQsRnHoY9pUT5WFg5Mui4hYdgp\r\nEDgAsMk2sHvkuX8MJ7NoyDu3itW45fF6KQkPXVVOSsfXiPNgxvbsXbyCJzgC\r\n3GC/TLzuowAPvapYZcPOKtJh4HLxB5a7sMqTFnVUo3OzW7Q+QfecPO/KqFoD\r\n1LE7crvRZ1BZQ4yh9mw0MdS/3Fhdqapge6+7aUwuqJWURKY6yYlFj0QqJ17p\r\njDSLHSUzMPjtdsUg8RUwECTam0tdtkZpFCv4O51kQj+nEzd3DAYfCv4h0kr1\r\nRPIVdS+N1Mh+Uz1Teywhe97ZBJsL7OuEGsA=\r\n=WK3v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "92b35ae6e8c2e6abca87850591d8eba67cd3e26d", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.13.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.4", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.13.1_1657841407172_0.9174413335644569", "host": "s3://npm-registry-packages"}}, "7.13.2": {"name": "lru-cache", "version": "7.13.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.13.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "bb5d3f1deea3f3a7a35c1c44345566a612e09cd0", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.13.2.tgz", "fileCount": 5, "integrity": "sha512-VJL3nIpA79TodY/ctmZEfhASgqekbT574/c4j3jn4bKXbSCnTTCH/KltZyvL2GlV+tGSMtsWyem8DCX7qKTMBA==", "signatures": [{"sig": "MEQCIBi8r087FRe1/Iqo19JXIKSIsdojhDQSQj7gaw51R3paAiAeslq/6vKEb5f1917PI+XE6EhPofQvAkXNKA8L85w1sA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6WWMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB4hAAhJIkCTplMmVwzn5RRmk/Xlm9zzZ389VKYIWFoeHtZRo8qlUc\r\nRfRZTlz6lewQ/TIkXWK72s8JgbSJ+vRP55J+VIOrq6CNUzMxL9IXw6aE0EkI\r\nELME7iULupG++S0voU8+GQVgPrAJPGyNPlAlda+dmBvGDtgLvF1e6vs1EC17\r\nyRYc/fKD9Oh8uX+8hWf4xovAUJBF3BJCKY5+086mfvCt3sjV2VQ5eGYBTsjw\r\nmqboOaWku7TEJCb/XteYQfSSI+aDYIhnKwzrLXNyoxspql2Z17bX8eTJ9UFk\r\ndSDI/kdjTmGkQs6AZr2W3shY1I+nr628pcqbnqthjNnKUUnLjPc6DeO30byc\r\np6l+WvbKAzZVQoRSUozdniGvwPEyPD5Ap9gBVwm/M2/v80QVTMvy+WpXIgVa\r\nMSlNuKsVxZz3LzssLUQe5D0sT73q/2Kf7IPI6tEp7mO8/L/BSw5ydQ24Xg55\r\nH58X5nWqg3gQu6WPAIRt8Db9E14pGdLSAJYIDJcMsIv4/IRpAJHAJXzdJFoo\r\n1jftEIEQ4Oieor8YVCW7CiMwoLyzPnaJhyvMalWp57Ac3WY8DQPvBiCKCeRl\r\n/nMlcujdwXwTxWkFiTdXjE29VfFphHZ5CONwMOZGKPcYim7MxpTC9t4DUTT9\r\nPSpcp1mGYNT9Bomm+zi15Vu7ef4G83uJgdY=\r\n=sFxY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "661c8b78a47e4aa87604bf8a486070182f0cbf8f", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.13.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.13.2_1659463052668_0.21576298050212817", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "lru-cache", "version": "7.14.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.14.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "21be64954a4680e303a09e9468f880b98a0b3c7f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.14.0.tgz", "fileCount": 5, "integrity": "sha512-EIRtP1GrSJny0dqb50QXRUNBxHJhcpxHC++M5tD7RYbvLLn5KVWKsbyswSSqDuU15UFi3bgTQIY8nhDMeF6aDQ==", "signatures": [{"sig": "MEYCIQCAZ4TPr6eLutJaKBpQ578924qNdb/4s2xN3GQPZSww1AIhALlHBouERZISVzbRKqp+T7kCXfJYn9hjsDdYYKS9Imns", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/Ba5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeUA/9H5LuFKw3IdssxxzMjU7K3aEirDbWpxYCLW14lr4yEOkcStHS\r\nDQXCM5cXurK4Q72TQRRkpSEaAzFIaB62+IpmwtUY50kGFZjfJ2rrwAqU7VVD\r\n+EK9aY3/VDEo8J8edyABQEs8HsFULWcrV5H3MtOTWJ8Zv3umUFzLnsdCNauq\r\nFJOoe7JFh3c5xF7Vk6jJ+V2vRN+Sg+XScfnpsB+4zX2uQOyH35rK/T1nd7Rl\r\nK4ILc8fo5Vy0CGsMUe30AderiJxaYg/kC5VaN6abPRVOTXfvVlOdBLTEqTbv\r\nkQaiRf70VAUIqUKGaE2Xdkf0mwsWwvG9j/NbVkQagCDki6mfkHLee+lzvAuh\r\nxEmZ8Cr8Xj5J81rw5haKW8iTccJmr8WLkVB2fL9Fn6ED4zvKvMSLxyutb1zl\r\nrA0BLboKKPAVe4rn9wFyPVHxF3Hfr/mWx6Gz3JEnq0xZws0UtDEqyMVc28ie\r\nSEObHYpYanFEu01cviRFdrJWUIu99kvNlcOLnXScT9h1B9BsLO+/NNdZsJcK\r\nug3BHdyhoGhm5tfmzgd4Ylm3JgtLnSY7RfzmWDQzJf2tKdB994KK1vKRhNDV\r\nMCvE3uyJd3B+RVs56miKr91+3r/2fFXqW/R006B9LEW+gDU4x3NVaB5Uqh+k\r\n8z7TiJ+1u6XF5hfMenbW8QMpp5pJY6WiUlM=\r\n=mpl5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "97d1e2d780a4fda2b1ced1c40a7d9847a967e495", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.13.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.14.0_1660688057566_0.9884912674901185", "host": "s3://npm-registry-packages"}}, "7.14.1": {"name": "lru-cache", "version": "7.14.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.14.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "8da8d2f5f59827edb388e63e459ac23d6d408fea", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.14.1.tgz", "fileCount": 5, "integrity": "sha512-ysxwsnTKdAx96aTRdhDOCQfDgbHnt8SK0KY8SEjO0wHinhWOFTESbjVCMPbU1uGXg/ch4lifqx0wfjOawU2+WA==", "signatures": [{"sig": "MEYCIQCX7zLXpN2akqG+c/Qf2oDQbhdXE7j+Wfpr+2k2iGunSAIhALCiqgWOBTW9rDK8M91jFbIQCil/a8pMQcJwl102pg9c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYqMeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFJA/+OfOR++MdLQ552GZK/aF6UZ92BcPO9v5oXCaMWf2MHm45JvXQ\r\npoj0O6Vn3E5X9ht2bGnPhLlsZe2C3msW0ugz2RTsD3JYAQxrDfUmklqdPk+7\r\nxCIXGKzNReFRQQ+5Fv14Y6gKIAWJNEU9gQvx/dPF9414Fco+baEmZ/LmVfeE\r\n6hdLB/rPpLDWQOB56boVS8UAKsx+o+jd144sVOwdtNAqVrvPbd929UIwxZyZ\r\n1puVvIa2kDBTjTVUcB54PoPUKbh6XRSdzJAHwDNAFeMTFY9Mci/GT+XZP3zI\r\n1e+jUirtSn74DUCblK46QI8j1MLlCzBNLnCXD77zp03LVVGlCWJNHExWOmG+\r\nLmNhl2HQZyCXNVmH2F/R7RRYZKpMqxzV8Lzmcr/rQcrrRGn51cpyxl0ekApx\r\nkcVgTrvZO7zaBVRk1hF3pXeDOKI7Xqsvw66LWrXs4wmKm99fIGm03S0naUUS\r\n34/Ho/IAO9kKxCzW7aYqg+HLyC5y/+WzFdQ5ELZRlV/BsowBllRPrTyNMhoA\r\nVYcduBC7JNHjmi4pwWWfOeun2sHKTRrA5nq4hh1ulgbV71IUeNxd5bnbcGev\r\nVSr2aJVSOAPzr7TCwv1oa6fzzcLuxRCokrxtZ9lVBcXwPFrxRGGyfyBYni5a\r\nF9vebBVAWP20ixCSHz9mkR8zpMxwPlCyDSQ=\r\n=+SIf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">=12"}, "gitHead": "a63ce28eacff77dfd30f4f6d62adcd361d8228ab", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "", "format": "prettier --write .", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "8.19.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.11.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.0.1", "tslib": "^2.4.0", "ts-node": "^10.7.0", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.14.1_1667408670424_0.40452886365827423", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "lru-cache", "version": "7.15.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.15.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "4437550407da5ec8c4fe0946a137fe2f7f07a171", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.15.0.tgz", "fileCount": 6, "integrity": "sha512-LKpNuyKR1lRsqN5DatvMOkW2nmUAwI22HoQK604nhs+WiRWSIC0MFUKq2XYUKv1fCVPK9Cro4d4Il3DxM80/fQ==", "signatures": [{"sig": "MEUCIEbVgmvbHRUbSBAKvGrHbk/toRckgzhoDMBt30uhXMtOAiEAuBT1JW+JHpueyz9VZxWVIau+LTzGX/QJe/UQrH8wVGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7XnjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnxA//bNz+ZEPTNxshlKT1pLYB3OF2UaCAXGDqx7eGgLE2BoqtAt54\r\nHkXSj3clFwsfqGdNt1J5l7SA/lWJaPp5L8JU78ZGNSHjQxqT4Sbz7aMnQZqc\r\nsoP3b3sCUBQ6ldvAtEP8ZuYXdWaM0PqedEZZgIQ7zEPH6Vv9LK9caQzrsLuy\r\nb10NiULplkY2W4DNPFRzrC/Xr5p+r3EHZmfWWhESKUwEfs/Q86f/zFVwBQ1B\r\nNIDJ1ABoqbTymnl4AuS7jZ+wqtfpDAdQvz5NwX2fQsSL1zK5UwL7Fme6ZsFh\r\nWQENi9geiZ1Ov7MbcaEAp3SsCPZ/eSh9hZCWD//3Jo06HQAoR/S+prq1bVKl\r\na0bHMYLXbvkZNFg5RQX/Vs313Dij1vV2wBOTBCmc+Qj5YhBBKWQ7S569673U\r\nmJn3CECpd4qYzRoZWZOrboN5wqC4bbJ9MIO/9Hof+DvvF2kIfpktX8otOpIL\r\nd3j20ZqMPtEgmVABGbu9lMdAtdjrNngY78L7Jg6/v62gy/QYCmWDZa1qv0tv\r\nqsl7Q/5jcbnR7aFD7XXbs0Xf5MF69783U/fwtqTkEUpltZ93/tYBtBMh85ak\r\neNzAxyIvdsTaG024GtTNP3IgBiAUCqP2c0QpQR8wYb3nynAApnF6D0v+7Oa7\r\n/13MBTjhUxLpM0E8jVWEys2G4Oyi/NICQ4M=\r\n=mI69\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "d1936a4067da0bc7fbb12247945c66fc472fd094", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "format": "prettier --write .", "presnap": "node ./scripts/transpile-to-esm.mjs", "pretest": "node ./scripts/transpile-to-esm.mjs", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.4.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.15.0_1676507619397_0.266953800791945", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "lru-cache", "version": "7.16.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.16.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "b1b946cff368d3f3c569cc3d6a5ba8f90435160f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.16.0.tgz", "fileCount": 6, "integrity": "sha512-VJBdeMa9Bz27NNlx+DI/YXGQtXdjUU+9gdfN1rYfra7vtTjhodl5tVNmR42bo+ORHuDqDT+lGAUAb+lzvY42Bw==", "signatures": [{"sig": "MEUCIQDjulWF8aEgFUORazNR+/A25vJbSIpJmlMQA0HvnDcI6gIgbdGzCNlTOEENJzTl8KoVNXerpv0cHeFwin3eWApzREA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7oioACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8dg//QZfMQBRdj0S2msdImyvud9qkdf9UyDP4AOatpy4jKbU3S0nN\r\nARzaHRxb4md5WPK+qDQqvZYLw9Q7UC2LmGP+tGVrdyBRZvTHO5SPMX5Maeme\r\nNpY5Lqy42e0liVX7uaxIFnFBmfLi28wQGPplYgjF34egzo7o6YcwY/NIZ87s\r\nJKc9xOzriAld+W+4akaWaVy/pNAKzRhzDVJ0XH7pLMYW2aOcvbFLw3n2A78O\r\nHhjgGWtiIMM0/l+ADLy9vwr0vsIi27c+kC2JYdDD/1YvykxQNE0Zk4WFfRoF\r\nZhhPZkUhPt43sgG6j1bMpxdww7lonuvYs7HjjQJCH5PvEqBNv5WdVpi4iUcn\r\nqYsZg/HtF2JxTrKzBXihYtCgIm42JA9kkydMnGtDLv3LKSzmAmPmc+JM2rPF\r\nfxvqNZpnk5+aQWjZP+FeJ29hXxkenF4eCvlSRs57DMTzqL+ADA15Fr3zqt+a\r\ntwkxnL+VHg3NuIOt2grI6PYh78B/ijEHSHAXfNjmzVr//fY8TDOzRvX0tn5R\r\ntUaMY0rjDuxTdYujPcMDaGrdoY6gJZPEBrHvzStVO0AVbLTXWM9AWToGAS5S\r\nUO5QnOoRRXe6P4JxvEUHzi4XuOyjJeADlhl4rpfP/thQxBIzIhA1BH/UWPVC\r\nYRhRuMZnkRw0vQKfGTPSu1B9L7CUzH3rEbA=\r\n=BtFG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "576eb281e59c4cd0ed1dd73ed2fdcfdc1855d362", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "format": "prettier --write .", "presnap": "node ./scripts/transpile-to-esm.mjs", "pretest": "node ./scripts/transpile-to-esm.mjs", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.4.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.16.0_1676576936436_0.17996278230067775", "host": "s3://npm-registry-packages"}}, "7.16.1": {"name": "lru-cache", "version": "7.16.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.16.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "7acea16fecd9ed11430e78443c2bb81a06d3dea9", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.16.1.tgz", "fileCount": 6, "integrity": "sha512-9kkuMZHnLH/8qXARvYSjNvq8S1GYFFzynQTAfKeaJ0sIrR3PUPuu37Z+EiIANiZBvpfTf2B5y8ecDLSMWlLv+w==", "signatures": [{"sig": "MEUCIAruQRUzivZr6evVXKetDyhi1Q0WOWdkVoUBQOwpRBpKAiEAoGDs52Or4kqe2GkqKyg4wsSH084ip75SmHYxzxF6/Cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7/mQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRmw/9HIwCIIa8aXOQUFwfiRniOhPsgb1scDudr57HHSMM5Df6BLsg\r\n6yZz0Sjtpae6EZlDVtuxBf3pVUBf+oB6Gaez0bTDNAcfAgq3/zno1clLFc/U\r\nLqpNr7NG/CE1M006pYLf4QYuqeb0yFD40kvF1kKS94hIsDTb1nwqdE2c8ONH\r\nGoZsEQVoZn81GZmuyVsKefby8L2GFMQAFw2GOkLm8TQ6oj79LRux4kknPvIG\r\n1+tBIM8ZNQM5bq1LIchDXs7qKBlXFCuQjPsWKWcIX2HwR5fcDNeG5Wpdfxkp\r\n0Od7ndoyjwYT/Q80LT9lPtZ5D8suZtKQ21TbJM7vg9KSy1WmRgBUA/QQwiyz\r\nPz5Dix82D71kIzCq3R7UW54bnyBqVHqGm2PUDgsuP0q3GJXEd0BkRb/JqjeO\r\nRkof4lh0GSQUoZ3elOrbQxZ5qmfnxvvoTFTPF2NIGKZNSJF6L5C5TKBp9Jyk\r\nyCiK2z8Pfjls83uVUgLc3GteSjAUw3paUiOGJK0zrPtlEumBHUHp+Z2ZRtgT\r\nYFsqjEj5AH1cgwLFl/mlmQKIpnifWb3EWf2bhue1+UDGdIhQEzdVrqitq2Pe\r\njfIE4RPswaQjXt6pG8EfJEeteCAKJchNLBoAuWuORJM9tQi9JwGtGdW5dvHO\r\nobkknI/xreIj57paWfq6uXTAC4ns4yhSP28=\r\n=vgY7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "e0e0413e5bf86a859cb572a8c78ddcba16eeedd6", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.mjs", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.4.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.16.1_1676671376040_0.6378636530611248", "host": "s3://npm-registry-packages"}}, "7.16.2": {"name": "lru-cache", "version": "7.16.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.16.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "df096ce4374d0cf9d34f29a35832c80534af54b6", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.16.2.tgz", "fileCount": 6, "integrity": "sha512-t6D6OM05Y3f+61zNnXh/+0D69kAgJKVEWLuWL1r38CIHRPTWpNjwpR7S+nmiQlG5GmUB1BDiiMjU1Ihs4YBLlg==", "signatures": [{"sig": "MEUCIAYNGOJ5RjRnPeroZ7Ay8b0RzvL1Q8eEg9i6q5b1kyRnAiEAmOo1FORLESDFBvZV42llVlk9z2WdcKjfBHvOZDbT+zA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9SNcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojMw//bty2O8H/w8uKELO9gG/fKvc8/cXe4qNBpdFdF/AHr2v18dC6\r\nkUY2O4l3rIXMFEYtVk0TNtYgbf0hXodVgpyn3tC5qJoYaQ40hFkhgqp9rLI5\r\nXiDT3MsAWFm8IKEluBumlIBgVBpbV3T8KHEW9WVGxgLz2Ci5XSkIFKuAMJ/i\r\nOy7jo6DwAnbEO9ORiBAmPVPSPbAGUNud/m09yrls0sFUII8OAF8HFuS+BxoC\r\nHCdGBsKuPCJUd77VAWUtphIFhpQzFYkqGgNarkSVTneSfnm6/M1dGU4pZzbe\r\npVGBGQMI3H8vkUq68xsj7PPQTC+LN4zSu/sCLK4jag7qWrKlTHrfCxQrRf0M\r\nXBLdQaK1Q9AQy7vyadtQLjmDUlGfhJF1Sb/bEmv1cX242KXYUwUXKgK0WcKR\r\nWxHZHxmyvbYTLXvtNsa8ih9k+5JhEPh8IZFpeXcNwSl9grrN1jRzn1o6J1wA\r\nA1KbDyG9ZWNh6VfoSJtUfh1KC2QK2kj5jLyOS3JEPajU8B/o6mUXLXzn+hbD\r\nQIEEqT4U5cgzmSNiErfB/8iNd8f7W0Eqo9hBtZqkytdcuf/x9aoylen0CQwc\r\nZpRGvXULCkPfEeXCsWcEwZ7Xt6x619ce5slSPAm7+FTb1ILaBYzZUAgAvdeE\r\nxQxMKzbUqikgD8SL5tEGdabPUp5mVQApkoc=\r\n=80tD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "f764a81b44d1a3be28b7773e99dd93ea9ede4fc4", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.mjs", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.4.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.16.2_1677009756642_0.17254313299895707", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "lru-cache", "version": "7.17.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.17.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "00c7ba5919e5ea7c69ff94ddabbf32cb09ab805c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.17.0.tgz", "fileCount": 6, "integrity": "sha512-zSxlVVwOabhVyTi6E8gYv2cr6bXK+8ifYz5/uyJb9feXX6NACVDwY4p5Ut3WC3Ivo/QhpARHU3iujx2xGAYHbQ==", "signatures": [{"sig": "MEYCIQClbTS+E4Qh8QHgX8dJkJDu8fzfyjzq7kNzb/YCebPjwQIhAPi2rfBqiF91avz49Wg1uixAB2ck0JCt4QnsQYkqWmu9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9WeMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDLw//Vdkm2jjQuwjkWuzriBBD62WxhbODdBlVKgej4x8rbLC6v+N7\r\nLOR9diC80m0X7S3dtS4Gl2YscJL3qPIv3KQBARo/ffXtPnSdq67VMj+2aNJG\r\nDfqAEXjxd6rqAMwVwieMdg9sH7AwFzpXFudk/squhnXG9+meB98SgCW9bDqn\r\nBjzVLyoAXWQ7eRFzAAiZGN4mmTBGsqSnwFRy0pKvTMi9OV4GkOQZd1BJfe7E\r\n5fjDzBNJjlLF0pL3owJOiTisoFsPSOlbS5V6xxn1I2u32ukUMYVdoJOM+J6c\r\nvIPj2Yo4Qn8ftKdZKioGkX22z+o0R7QHRLHockANZjT/QBIKMhtoLPxu28nM\r\nho1ZoSFvyAuxgE6tysP9FjHWmXTjc12TLA4UyGi4X3iEO68wgFoohKonfnin\r\nuIb3aYuANJWZtpaknuGp1br/uN5MCRLYbz/u+Bzu7L0QVMYmOnmVzc57au7R\r\n5kGvLfOXUoGmINB1h0N7rfVgUVQu8s22z4Pf3LCL1iyBvSaWvVlTNnd5et1A\r\nhRigbGZ56ISkoYp63qjmVrnKK39WZBeKBsKdyRBshJdqH9KyF/CY9s2PVB5c\r\ng7sDfEKrI5HrCXAbnEryyok9rRurujXdG5DsYsBlL0VFvocacUswS4osKsYz\r\nwAi/r6Wgsr2uHLfdZ5CRVwP+qUdDtjJXJYk=\r\n=AySq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "a568b5d466146dc913e34bd65245282dd648a778", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.mjs", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.4.2", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.17.0_1677027212292_0.7379392665739883", "host": "s3://npm-registry-packages"}}, "7.17.1": {"name": "lru-cache", "version": "7.17.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.17.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "20d4a9b01f1fd2d699ff8fd45db3c5cb8b8d8cec", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.17.1.tgz", "fileCount": 6, "integrity": "sha512-nbaJQddiyHrJ325YyIslLpPoAZDg1JZyHrd4TGM0AWufhq9XEbjv+c4B5YdUQnT3ylOd69Rer+LcjyE4bgdYHw==", "signatures": [{"sig": "MEUCIGSUGLhQIi/GXOqnTdU1fwKR+mUTm+Ratm2sL6z8hMG6AiEA5iHwpHndfPVqxWyCJLNmmrjOKBS6QlBc/+D0M5WzISQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/wJKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqsA//XKHibA9hCKLNw7SSpaoKkX4EYeym/eOylHvw8nqVDEEcpY5i\r\nlj2tWwdb0bd+SDOTdtoUr9G8T4YgdeO+L75DkQtE86dAiZVC/5HiQT+p9whh\r\nxFJODiZM3Z4iJ+Mh7iWWtFsYD8BR4rdRWs9DVgsz43yGEeNcZlO0p4LCcHSu\r\nrHm7m1Lbtt+cr7BNF97gU4zLLAuPUOheLhkOSzNTtBXTWVKZY07G+baVjF/7\r\not+ssnCQGG/60+AaD/yAV5yGC24bZiSxizJd3mQmoDSzOM/HHLOUidgdY0PL\r\nFYzNFcmHQrVmmj13PvNNw1+qTwGmjwa4A62P0M0ugYSU6H0b76I6usa6cbzf\r\n014m/6nb1fSlq9vtJFZYBIlQe/qtno93EYPnQQSIYPe1kyBF6p9fHPBjBwaa\r\nvWpuXRYvb+57EYwLD7xfs4dbO8iqqK+1594lkXpdFpC4tku3lFm5NLr8tkpU\r\nNR/9pqSEi6CjE2unhWoivOCEfbtcEZWe5GorBt/oYfW33I6xJtAp08Qwf++7\r\nf8Mqn81D3/luchk+wT4Kva1/s44fqTL8OHNBzuM6JrqkuuUNMvr69OkdynEE\r\n+IQeKupMqf03QRDt/Pm3dHNruuweq+r6MO/sBDFC7/4ZVv61nbuqIOMZC8vm\r\nfC2u8Dn+f3iQIQG1F6jb4UY99LjH8KMY+NY=\r\n=Sr7m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "d5ecd08db8555e1f13d67a5bcec4ac90e2a11ee0", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.js", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.17.1_1677656650139_0.11422907407478378", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "lru-cache", "version": "7.18.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.18.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "c420f068ace992e8c52377418aec2cb08f86d362", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.0.tgz", "fileCount": 6, "integrity": "sha512-zB4mTJNwserMxerHY0evAuBe/5wnyeznwZ6h19vTV3B9lbqm2c7pWlExjjXgBx8E2J64JF4siGBqKksl2cHTgg==", "signatures": [{"sig": "MEYCIQCQLVtrgiJVwlov0O5LbEFHOvX8s77gN46Itgt/SRRrTAIhAJfYNoyhkxuN3dCBDkF3MTZzb6l+ESIZFLuvCZ97wIux", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/wJyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpiog//drDE4+WHZ9XASqV08/L1pCUICtURwWKjwbCsAhNNjCujPxw+\r\nQkQL2sutc2f6LCULjMn6KIUPoupquO0mzxLMeHGTeQ8CYn/EB8CFgtsPbbWI\r\nWCkC1w8HbqQXLNNNCL4dZjVAszSPcakJgI7znTMIdil01IEgDdHEEqxnIqIG\r\nBNQhGJjoX/bYi0QVnxYoGETFFQ3ITZ2xptEIKRm9gJE7Y6SjScpf4Xmq7lxK\r\nplfwWjXb67rWOyA2JHhBKW8c4iZMMhIGDG49dCZ37yUb38HZMwtGwf2j+ODG\r\n//WNcQaF3bVVS3Ua+/nlcFw+wmesEjPLdPCPbEMnwVD9Nu6XMRYork/dtHCQ\r\no/CpCK9YKroOViEKD01v/P4CG+ibrk7+BZF9IWValFD0loNymSGLZd27gXVX\r\nBnEAxzvBR7uAfmhm00AxsVqr9zG5GaVyt8ZiKCjW4HNLR+vDE8hZP8qCuayZ\r\nDGfGhOC06E0gWf/jehunJdVAUaGZhGQbZRZz+qd0jhwKyHdMMgkAIiBWMz8J\r\nU+LaYpEkopMOi1BAOoKEcSDrRKsYymC+uHpCRhtMTQzzGXU91Dtj7HhUJPLE\r\nyPeK7KfbHxiuVFSgeWQoZzbq9XvLp1GkuKvsquTXYxpEOkKRVUbg4l/YPwlr\r\n+Vx0a9ax4Mzz/KH/wXs/nwBKqYTxRGTjM3c=\r\n=pB87\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "670414d6ac05eed3b5b05f28143104dcbca634a2", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.js", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.18.0_1677656690686_0.4721490585484849", "host": "s3://npm-registry-packages"}}, "7.17.2": {"name": "lru-cache", "version": "7.17.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.17.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "f3facd44b39e77d0cf4dfe4c26f6ad11e34a2da0", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.17.2.tgz", "fileCount": 6, "integrity": "sha512-H0yMKR8Pq7lPtf0RKJbQeNxCJdZpTZFVTtGoFPW2qKxaGgeGKFxOH0Tjl0EIydUk3aku77XwM14aX0F1bzWEDQ==", "signatures": [{"sig": "MEUCIHsxPO992uniEbPA+AL2ikI/tr8ktHiA9TmGlNFjECLcAiEArD9m8Puz2NCQxBEkWfMvS1GoacJgqu8/mP/S/kgsM/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/wMVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0aBAAouIXyDYHjXlZphuUVQlf8Qme0UlYWgX5EkfPZDumgD2oiuVp\r\n92s/6CvK8X50SuncIi9wP2n87pZ8Y2HM1zij9bbcdV+7jij27t9fYuTH+O0H\r\nwz5ClfVf3UnNWVB/reD2iJGMWI00e+wQYoY/V8KRJJ7xTWLB+OtsPFZ4YVC8\r\nT1uQMgoWyS+nSDgnMfMpS2AATWpWDCuSzFtILfs6c/LjX9/Od9E+6WdOyb2J\r\njO1rDAMybib4d3IsJv2J6kAShpXwglcolnfKkq9CmRDPE5DXCSuks1tzIID1\r\nl3dN433hQNy9g2hzCxIu9XZOVBydpWdSplJ9vnQZhMj0UFr6h1PWlzgjyFQO\r\nvm235sd23zjVQapHJj6LYhrwj9y9cTTIs9em3l6eJSAbA3cHhJIm71C2KkBh\r\n2cJiA9EcbqOiPuXH0oN1r2r+IEL6LWc4TaW4S9h2zw0+FpZBSS0GrZCkS52n\r\nlhcXl9+lfFd32neamUMoGshiwXHyctB7djOFxVEH2DEseC5oxuBk7ck9Hm7K\r\nB1nyY9R4KHnMtg7DitNQlLrQqQWQUvMvSZJb19qzPnEb2IvZ6ZB2JFutb0Vv\r\nXjxunBQb1PWwNenUKuSiDrlQwBXrIASKgvt/AUyeyeeOLQxSR+P7O7SR8bNw\r\nJsHHKYO4GNlp+bjXE+y/YmQE+zrIMotdF4Q=\r\n=TBC0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "1e07c5e70efe8a69cf4f63b9aeb0813fc1615f6a", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.mjs", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.17.2_1677656853202_0.41193412318724865", "host": "s3://npm-registry-packages"}}, "7.18.1": {"name": "lru-cache", "version": "7.18.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.18.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "4716408dec51d5d0104732647f584d1f6738b109", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.1.tgz", "fileCount": 6, "integrity": "sha512-8/HcIENyQnfUTCDizRu9rrDyG6XG/21M4X7/YEGZeD76ZJilFPAUVb/2zysFf7VVO1LEjCDFyHp8pMMvozIrvg==", "signatures": [{"sig": "MEYCIQD5Xv7WdsZz2hKVfUIfh98tXuCV4gOo0tqy1OfKRxF9sQIhAPQ2bGlD/jBzQa/XPysv3wJBUZayD/Zs86wDGteULVaH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/xphACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrf2g//dnQ1emj5a5naRFodABIL5z1iocxuCcNzS3sNL5xUNZ5qo8li\r\nJiliaG3oni/IL8NCUxi95RlUp/M4kJzAEUD4ofb/j27eCwSX+0gvlpEnqFTD\r\nQXMNuFCiG4FFvy3XYM64K/1WUUN/3g5WjCsSC7nvMWOt5SHymSar/BBMZCVZ\r\naJ6QXo4w9uxgSH9V6Jrz9lfN2FYUa5DJengThfsLrnrU29mX4bquFUrwru18\r\nriJrkzDz4QvEmCg5rElyL1oqqwg3WWzpiV+B/qxdp/uf9atbpHfzC8guSjtj\r\nUlMJsBg3Did8E5Cwtj0tMmuUHt9Rh8lHo0O1p0y2wh2/JgZuBo/exeTFeJof\r\n0pv8cKw89YjoOAGZexxKC4w103JnUkfnBDirHKuO678G9Ydcm9MH0UwkkcJx\r\ncWCe3E+TTboSnemxZf40MKVy5HKkDteH7eTvS88zWX7WIdnzIAa5aeHIwjVI\r\n6EPGYGbEcnNnx7nHeX/2fU/mH5CVngf+MqbSCM5tUkrbV6pTzm9AR3bBWmNh\r\nqJp9Ov0AW9opMN4ouXmU96G9+lgR0qButT4ryv/6N4X7gkZAInAfWuTe9SY2\r\nNaQwbhgKBssB6DSdldzX/haOhSYVyPAdhyLJCmZM5dagmV/llBQBa6McTkVn\r\n2d6pIjFggZg70NY+fYXfykaMYaP/nqiiS9A=\r\n=K2Xx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "078625cfb3f63b00a4ded536647290dec68c4892", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.js", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.18.1_1677662817480_0.9250039029847505", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "lru-cache", "version": "7.18.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.18.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "5ba8dba5778e7771af65803172ce75b49c1504c2", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.2.tgz", "fileCount": 6, "integrity": "sha512-KytVYmZ3reaw/f3d7GCISvWWjTYxszNdvD5rDvm/zECga3eSWzryRY7iauJsjo6aaw03lHYTSNTk7lW83Bv+zQ==", "signatures": [{"sig": "MEQCIGkoT+Sr1b/lUJN/9Z4BMpOCAew0d2IoobKQw/PziPqRAiB8UQCicawTHLGuvMHcWX9L5Udzr3CvaaWYkrnvh/8MDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBAyHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosjg//ZKvdh/Q/bcbqLzjetKfRB3F90MKMIJKYnWWruqx/tXhA0w+2\r\nBmuPX6RduFcTbWPa5w8pwH859jccuTQsMlXoKUbTN7CTmuQdzMaktGGCkFbd\r\nIb2SuK4r5Tf8k9sElb3mThHw4dmX/IxnxFdvedrQqtcTvqR3GQYqjqXUu1zS\r\nyqDZWsbWQaV3cFSGWMQZmIfOrbQUVLTFDgvigM7iytU0p7xlE0qgL8t3Z6tR\r\nPPkZdp63qLRmKgIZiIciXUGwb0X/DIaEkzn3WOkDz1aBumOhWjv1viTUlN6f\r\nbA7EKxLnoW4+HrXLAl5lXmnc+4Vr1PIpYixCYDmrObzdxwLGlM70lcQyZqRc\r\nZQT5XOEP1V6uHeXzwparEl8GsMLTRI78+RFI0hrqPjEKiZHC4D7tO6q8GcPO\r\npVrepX9cQlngUYRBewFB+PsRRk+4D1CLht2uVa2jYPIUO/rM31sONKmabUeB\r\nM0mu41eR5TTZhtKaOuI9oYZCyH4dG6Th9MMDUZxeVaDvX1DvoHtiDQVZmE4w\r\nclC2g8DVQLQTsUqgGDdYnCKpAr5Dx0TCSYdVqwK6ziTNORTy9RCFTm08tkKG\r\nsBJmH9+/9WSUkx1XjTNb80hsdRp4rwQDtdR3ljIA0/O2lbKDktVLfh4EovCd\r\nFWPuo3Uve+jQIYWQtGoUPKhUNiAFmGiHQeE=\r\n=v0uJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "c4f47055760ff2b19402247cbe43d1622cecb418", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.js", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.18.2_1677986951570_0.01958078575880129", "host": "s3://npm-registry-packages"}}, "7.18.3": {"name": "lru-cache", "version": "7.18.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@7.18.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"]}, "dist": {"shasum": "f793896e0fd0e954a59dfdd82f0773808df6aa89", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "fileCount": 6, "integrity": "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==", "signatures": [{"sig": "MEQCIB/7m8ND3ca2Hda3voxxEozsYtcxsHqWkQmhFJXpSejuAiBtU/wiGxqLafuq/lYPEbE/p5Jub+6EnJFLZCA5Jn13/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBNmqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/Fw/+IdwQaz58WRnxKy1ZGWIiASvGH4zBPZFWJR3soqyiNu3N81iM\r\nm9KZMhZjEwxBz/+fVrkhv06DDVE30nNqa1ef0sgEW0bHlE4cLH5ECLFnkS9V\r\npvoTui7sFWK759pWW8H7mf6YBqFBHVqdGpHlOZWfrEth4i9AI9QEL13NSZ79\r\nyuvPlETpPpwCRO65wMrTlmiOCUhCPmjHfeEE6vIXXAw2k95p/UfL4/3HMlwR\r\nK14HyLatfPMnNcPbQ20zlNo9uM1TzJKgJymugSBj+ODkepZ5h7vcionpgnuI\r\ni2N7Q0yqbep5NGqMQV0A4SXGj2xDL+Vl4aa1cZAf+c1eD/h0PfPuxgMIEiB+\r\nOwRmMSCVe3+8iBSsmBpT3lEw4wtJqCw8PP0FlkNjqfYpcP2h4jA7Mo3cSoq9\r\ntwqm19xj8RQMJgsyffba38Fcpk6bsGzgcsEYlDsPL6qaQ+QnwT2FIOtEr/63\r\nSeB7foBB6liSksvvAAzT7luHMoQLxS1Rx4cIMWyD6YdjviBhoGECzBox2fPJ\r\nOc7n+Mts88VqnKZrvq9sQkUxItr1U1q94bhAZlAqwXiXM6qoHpK3vUa86v1s\r\nEun8zeVWr7ABobqn3Kfam1kqgaNPwzW4uFndtiguxT6R6lU6IvWALigUYo77\r\nMiz1Uktd+MMf05redYatcxfGaV+IBkZnrnc=\r\n=SX1E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "types": "./index.d.ts", "module": "./index.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "gitHead": "7a6f529e2e7c1bc3c81f3ee996267ef2006de492", "scripts": {"size": "size-limit", "snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "node ./scripts/transpile-to-esm.js", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc ./index.d.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_7.18.3_1678039466586_0.4227452101966651", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "lru-cache", "version": "8.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "152bf98daafa6a46a5905694fccd6f5f7f6095fe", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.0.tgz", "fileCount": 19, "integrity": "sha512-pMu1vSJIwJPS/YuMJAJFjvKA2OC7rvgKqJHr90JmZ1kv/hO+MuzqHRSWqyn730vlOwc1Bx/c8+3izTGzmKyXNQ==", "signatures": [{"sig": "MEUCIQDYUlge6+4up9XIgA7tmrb962TK8h7kOAiX3QWHZrlxjwIgcc/VbWvB8hsJCVI5SvuX+et24P+JNjz7UeoA5UzYkoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDSZtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9gA//a41YDQNxTrlDF1CQknMxbqFOfGXBfhZdBJupyXJjkfbpq2+j\r\nRToTEbPYrXH3ug8mUOPt8T8D7v0sqvgQBo3K0D+O3o+7evmH34r4fMUGkeSM\r\nTchIHqOPcKKhWl/YvZbR2g/yL1Rz6NglHlTk1cuAYQnJHDl53/82Kr6SAvO0\r\n04bcQtAtE39HFlN89PV+CpkX29K4b1jFMcRE5fAOCsk8TphcKq/KztEU3BvM\r\np4sEl3ugjh4zG3L/pzKjEhDurYFoQBUMkZMakiIMKnS+I7s9ImCNs62eRdsc\r\n911vjbKmxJcx+mqIUR64jQXXL+Zhaup0cVkkAvXmGQzhIFXZJdR4ZJUmUb4W\r\nxQHj+IUU5aSCsGgmGh+AbeiUC4OHy6RGZGy0xgmjDd8usKQUTBwC2OjUR47Q\r\nbXu/nZcUPvdjeFXtGzA7Cbd5sCAZHvPAC+4i/RkBe6j3v9kwkrpNUxY9mbTd\r\nuQBzn0OlkxKorZ5RkvkgcMK0LVPuaRnkD9XmTP8WxQSWucK2vIWRwKg+4l1L\r\nuVbYQtSt/PAQT3IcuB7ooS4zVK/7+1dmXKEbNjk7GvCSoAPLmvfN4NnYxi+y\r\n1cPrzT6VEK8CjqE6lLUVxOLjNa410h6QhIcIsbqHDLG7ZGFIJb7LI6qYzU7G\r\nvbeARbJc+mx1INj8Q2ro81FkqZthfeW42aA=\r\n=Dewd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.min.js"}}}, "gitHead": "42fc1a3ac57604a1d26ead49ce87f4b3518505e1", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.0_1678583405046_0.7925986236072033", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "lru-cache", "version": "8.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "a73771c61e574a59002ca84cc0e3c18360e01c52", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.1.tgz", "fileCount": 19, "integrity": "sha512-XNwsTET0L3ybFDTjSDBH6RJHgYz/vSB44bMBxtyThRlENVeUGW7GIIrhkxClxcq+ErQsv3pco6gxj+lOUxxRQg==", "signatures": [{"sig": "MEQCIBbSHERiW5AsoocNKHXDsG5oUeIgpL0XDL3z6WtRkgCYAiAx9tv5dNX9GAjIM3Y6Jsc+TBLuU9Y/5koirUye5RO9Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEgwJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFrw/+OIkJoFPxY96kb9bX6IojZC/iK9M1YjqFeaaz/Cod5Qq8RzOB\r\nrwnohVXMXi71CaxfKo5/FmmidNu6ADVFUzJGvGwCrfa2yBAqzNokg5lzaHW8\r\nf7pGFYobp9QyxFwoMeoyZTH5PNUxeP2w07f3VvujH34RMTapuPZOH+Hxzzi9\r\n2W/cBUo6VLM5frI3UFgOIeXFCuWnmkm2ILsX5RN46B4IvrQXb7C01AA+AiLt\r\nvQBuHphcYjGUn0pfaoGc1Oe7lJ+0IHIYtr3KcvKtnad0nxFK9VNuuLpyH6lu\r\nKeKjLYTLx8JtsGhGWvfm3qC6De4tT5MQNOceMnD1eAHF1zk+r9zMa1kCBJo4\r\nL2FwFMQXDiA04rQ97Y9gH8P05CPOuetvZ/i025X7EB8kJDc6mOwIOmzpckLQ\r\nDsjn6Qrv99DLqjJGbYPewkoy236vfyFBsbGaeChd5LWan8QXXqzJTyTdkG/W\r\n7ox8qhF84eMH2mkJ7kl32D/9+lAgsyA2ziwVZ6bSMmdl6KlK8LQNd3iralu7\r\n1bDMg56v36URdYwlyURseT6YO/WZhmk0O85A5S4Z6mE1PsgbYVLK1Xuhg3uu\r\nXwbUSa2eoI/lHf8ggyOYlmH44m+bbHf0XOInmfjHp3B29ysKBnnVFsV4mDGc\r\nbFEhZLyn3cw/g+vFg34b45IW9Pas73FZBRA=\r\n=fPTa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.min.js"}}}, "gitHead": "ef7f1e0ffb2b3050187c33747f0e8aa3b607a7b5", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.1_1678904328958_0.0807202645594034", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "lru-cache", "version": "8.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "ac0cbcd4ead5eb13d19d4ffdbeb03d8f6c4553a4", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.2.tgz", "fileCount": 19, "integrity": "sha512-t6fS3kjO56SnjwYINO+G4urbp+NsaEuNPVAWwI3b3ZiY63djFh9If/p2XfX1bjdop4fCGhZRdNhLWUaO26q6cA==", "signatures": [{"sig": "MEYCIQDBPNK/h/zyp/1ZlHk8L4rvm9QbggMLxp7myDMVCxMvJQIhAPzpCRZh4JHBcPr6Qb2T96ff9Si3ApZYP5kIl8en5FBe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEg6NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC+BAAoCTn89UBlxLEAd0qjesAT/sVkUoDufOf3vs0YUfD/9Z4iIFQ\r\nkgNfsEKKv51r5dHKcF0Evj2LXYwgVRK4QmxLLA4G1LFdEJ1lbSPMbD3elToA\r\nuD/InslluXzY92C8l3i07KRQVAzdbi5bKgF41QTLsD2COSnWkHc2Rq1ymLGd\r\ny2ubQN1ZfzZNAt3x8enTvHsDaqJW//CFRF2mX8ZVsdPqlgclX76Z/f0hRkqW\r\nYHHp62l6FTdwG+fduzEnWcmxtDC6QJqU1FMzf/DDTWqMg4OCZJtmGt66MI/x\r\nGW7K3KwHoIQqJvaZGMnRyE5nNnkviArfvslnM6YK9diuqIL9DWWTrNq7iO5p\r\nBqB2GnNq43jS1OmRNAR/aFkGRvQc+8nILsRTOCEgZdaqDel6xQAWb7wWe0Bk\r\nQlM0A2kPdhFBECX2ML2SXTLAvqsqIgsorffgRxZ6elxXv2gQa8bOkDBCP6p3\r\n7bq/BTDXusNkQLpG1weG7vjWg/uX8RuICf+Ee4qYBzuiTdbK8Qj69nJaVja9\r\nZphBbGDU+k0PLMvH5m2+iQrnVZMow8Wixmh3oYX5yUJ1my8fJKsSGsIaQ3hW\r\nNtTm5ZRSnQ6e8XYadZtkdc6XngFqtSIbgxLPACWo0bwTM1IdjxqOy0yG2sRP\r\npsRBYYVzZ52wW2CPErPY6zO0p0RS4cjvMn0=\r\n=H+Xq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "bba1ccf313f91e09e37ca9dd4faee28565ac0404", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.2_1678904973633_0.9387161465355252", "host": "s3://npm-registry-packages"}}, "8.0.3": {"name": "lru-cache", "version": "8.0.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "42a2c84ec91426d165b0887783f6a0fed367402f", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.3.tgz", "fileCount": 21, "integrity": "sha512-hDYyWLYUrrTuQQlZWe6U56i+t6UU7LnimwV9Tvvw0HadkTnD7VbErepoJVeNEfC4exBIcGwO8M7TQyF6HO5tQA==", "signatures": [{"sig": "MEUCIEKg9BNjkrXhoTz7ZF37P1rkCycCxfVSTZ74SNWtHUkEAiEAw9mKxmdiGvF2+0UlfxVlq3lTU3oyEhMpzLVzeNTiAnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 635730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEhHnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfGw/8C+xYQQ3tx0WDIlarlOiWVUfomhnbA9SwLFXhCJDpx8EMaFfs\r\nb87Y3mS3va+OWC/L9M480h72kP0EyKl1CXo0XnkolQNyJI9nrIQw/fT2z9US\r\nIY2MPotWw331RINYp54VD/MEJcZP0GFbEPCESrEvj9F2vyCXYHGgQIV5FVfk\r\n1DglpJ7zM62TfbUEoQvWkbAluoEZLvAHu0NCvF/aQFaB3hPNf9r8GXUnkMrO\r\nT0r3BhXl1cogXs4KGtPrz9m2lLrxrYuZhI8XlRlMRLR4yvO8lpSCZtT+e/Ln\r\nrOsPI1urquSLkbJdl3pzykbL0F5wg6Exdyd8pzk3qO0Yi+xCatHZP4LhfdBZ\r\nnQ63ti6AUjl6TceHkXYGaGjJh5DAMZTDmMU0bS+t/XXdzLBPDk9szBPRGgBi\r\ngiGpgKGmlYlfLmeSF92JJehIUuvmViH9ijkv0di4BtKTYi9P1+EjFXYZMEZf\r\nZn4xV8ZneauS8nrsOEyYwwRzFi9TVkKilme2K6JrJToidRzn3UqWgfHlK0ze\r\nRbV7TNVlI+aRPGwVOrdhil49tLn76GuhcnA5wZzCvKyx1KWCxSD2U+dFR4rM\r\n4LsQv+CW0aOIr62bNcxWbKRRAjDLT2INmdY9fQCNctfI4OcTX4JBW04B/0CT\r\nwuGRcooCkX8+tfYqJ5GjeVFZIspzudyM2MM=\r\n=YSfu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "48e65174d7885604947fe8c47623edca3094c6f4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.3_1678905831242_0.9824179500875971", "host": "s3://npm-registry-packages"}}, "8.0.4": {"name": "lru-cache", "version": "8.0.4", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "49fbbc46c0b4cedc36258885247f93dba341e7ec", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.4.tgz", "fileCount": 21, "integrity": "sha512-E9FF6+Oc/uFLqZCuZwRKUzgFt5Raih6LfxknOSAVTjNkrCZkBf7DQCwJxZQgd9l4eHjIJDGR+E+1QKD1RhThPw==", "signatures": [{"sig": "MEYCIQC7lTEs3gEz1jpd0ta6QQQPe+JDsFHA8PQpUNkX3T4oYwIhAJtJ8WX3PuO5xy2b4e/FC5+dv5z2jMZDX62otO+4gQUm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 637668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFPJAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/IA/+JWptjEmGQY8VkAMKaugz9k44qWKa53F2A56eemoo7KWsX1wG\r\nw18HjxF3oPwNf2/XQBXIMrRysjo5/ov5lHwKEcgATQT+hIeuv+pPLlc9slHu\r\n05bHBVOQsp+HCFHR1o/cU6SFUknNSjj6YIH0IvbYDbWMFcSrV7mZEay4WHAx\r\nmUcI9Q5aMwncpKSEDq4A/S+azSbIB1FqGPzX4Z99E1/D3s0gKZB48pFSBhsy\r\nH78QsgtmZshCldYQuHCxCd+aXZuHuPhsCYJboYRhwoo4tAdfLrlqjPj0M1TY\r\nQXEpBt9aLrAWrAjpd2BKXOmpZEc/8VV2xwgY1KJDsIyj0xOu4li2H7CMJfS8\r\nrVhRy751czxG8B2ZEJEsNMXSU0yVvY1lHNwxbtP0rNf3nMqYW0CQIhu3Fulu\r\nnwzZ+FP/J0mHgGTjTmuZio8JwNbiRpLjoXC33ghjvSaETcB1KFdds1OeNX0A\r\n3Ekw71GiEMJpNJZ5Lz2oOJezhgjehEHIaCBKQlgwSu918g8vdAUZCh3CkZ81\r\nYQDF9UmoQfFMCEVdQqTxZVTJUsN7bwbnH0CiSM354jW3YOQETCqXXelMa+Pn\r\nF1SCMKdDV54xJhRZtl2QJftoSXeK6zIVxSPakwcZ5zxuB7eeFJjKX/SJLG7a\r\n+aer6lMn2Sh07gF7Ji+hc2NkDJYssJPPg/A=\r\n=TooU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "77e977f3dfe41edaa483bad7d2ae8b4d1f7e86f1", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.4_1679094335997_0.5941552746946388", "host": "s3://npm-registry-packages"}}, "8.0.5": {"name": "lru-cache", "version": "8.0.5", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@8.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "983fe337f3e176667f8e567cfcce7cb064ea214e", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-8.0.5.tgz", "fileCount": 21, "integrity": "sha512-MhWWlVnuab1RG5/zMRRcVGXZLCXrZTgfwMikgzCegsPnG62yDQo5JnqKkrK4jO5iKqDAZGItAqN5CtKBCBWRUA==", "signatures": [{"sig": "MEUCIEuy8+ifHZrgUUwlm+zfv0pXCCKWm0sEEk6s4tl6K0S+AiEAzvV6sFTI1OKs2SbZN/Q4GNvgipBS/tdZupI57XYRPlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 637806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLQ/oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkQQ//Tx7XTInPiPXfd0VziBAz0uVwoKRQc/RX9a/tEHGCtBIkj7V3\r\nSvntAYDuBd0H7gCkkM05C0BJowC0qOQLeOUHrKkfPzo4I84632J0l5c3nqXS\r\nfFJ3QMMz3Rt98x/etEv/Y6A7N1g3l8wGrS419Tk7Lc+Y4r8vd433QkR8whA8\r\nV5NYnD5tueUPCz4LgV3/rV6ffrwFi7UR92todc9Jh4enQ5YBY1jRqvw1UERN\r\nyoIgSg/+ghtOdTheQmIWk4EfA90YbedVzVeZSchEd6SkChDMXkBLVwQQ9L/r\r\nYRurJ0XcNUeI1sCoe+eV7YiaxwBgGMP4ScEZgh/9qRjK9CJuv5KDs18VSNko\r\nSbZRgTPF05oZWdbSi0+bJ9274o/2j2aLS5fOsgvzUWGSN/SVg5/jKfbcbC66\r\ny17S0tynCtg0ZT2/+byyongFj8/PawqDAh+ZPoxZup1rj/8UvlDAAByLHBy4\r\nhxq09Dm4HCb5zpk6KdMRUUKVIyTTpS034Y/Xb+D1IJ3LsUZdzqT5n96Bc0hx\r\nkm9xx4Kprhqv+Zrll4OeZ6awUeJpieaDULROI/Ob/hAQR7AESyBkEtcc0rR5\r\n/kXl2ACWsfxN/EInyZ78SkVI4+g4k4GR5fmbdr9h5sTtoecYC8K6vqrAzX26\r\n3K05KGjYRyswDKXsIlAspdR2822sv/V3CoM=\r\n=XytK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index-cjs.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "a00597128ec7158f8703f7e5bdd5825d6ee05169", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_8.0.5_1680674792647_0.7714487382500526", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "lru-cache", "version": "9.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "daece36a9fc332e93f8e75f3fcfd17900253567c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.0.0.tgz", "fileCount": 17, "integrity": "sha512-9AEKXzvOZc4BMacFnYiTOlDH/197LNnQIK9wZ6iMB5NXPzuv4bWR/Msv7iUMplkiMQ1qQL+KSv/JF1mZAB5Lrg==", "signatures": [{"sig": "MEQCIBkQfbBveVxGXb6Vi6PswsyZnStiEgfIode1FT6r8j+tAiAIc6tzGjOt/ERszZdxNdc63RS/WNwexMdjKoZqDPfYKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 635812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzD4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmBQ/+KQtkLW+9ApJR4NfCBIMtwBzQ3Ec5UcjR7QRZiZ9VzrjwxtVL\r\n4lrc4BWtugIdtx2gUhKdgAjfWEQNoJUQRyFakMoug6F+wK19J/QcbJE0PXrm\r\nsLzv7CeHv5qI0wG3kltr8FSXLpwZAGHv3dtc619jVEnv+dtyhqNSmbXKkNQj\r\nQwjOxFKmRQ01gSZE3C8G2tplwoRknfCtYkMXzewjdWdxJQ3J7+Hc6Gv63Cut\r\nvHj7LBh4oORknImHUzqnzQy4xiGfPYyKpmY3ksC/fy5ENXRNOIQuHupyP+NX\r\niqOfpaIdQI1Y57F3hKLMF7xFBkeKU4xgbG1XdLlUup2hBs1A7tFRtecFFaeO\r\ncUkB3CIX42j9eexXLO2OSTbfJogGgjcS2xL/y77ua0K/66DlTq+O2VhCfV8f\r\nugEHqNyj4tvBUXzmQ8TNROrN9I52uGBVRMJbrrdr2SCYIKaG4fbO88RAPE5j\r\ncSQKhUNYVC1Li4YHMFND4ortVVAgx8GrcHrXPp71peDgd11j+XB2QHjhn3xG\r\nToT+6INHQVPnjqJrR+jJMuSwHZe/lIIqyaue81+AK6vpU6/MPACFtMSIS0c1\r\nUrvzvaH/2uTO9YhnOa6uNi/gM278Fe6ffaRCX//MNRcbGbzQCDhVJQE1/C43\r\nQbIXxvanhkZ/exFwE5nSmU3FZhEUdhPOp4M=\r\n=vQh5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "1dbb15bc33c1b30ac6dd5f01ca31605e9076dc36", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.3", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.0.0_1681076472715_0.31206244894537405", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "lru-cache", "version": "9.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "ac061ed291f8b9adaca2b085534bb1d3b61bef83", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.0.1.tgz", "fileCount": 17, "integrity": "sha512-C8QsKIN1UIXeOs3iWmiZ1lQY+EnKDojWd37fXy1aSbJvH4iSma1uy2OWuoB3m4SYRli5+CUjDv3Dij5DVoetmg==", "signatures": [{"sig": "MEUCIQC+2SND3X0jTuA25fONz5KJLUWvLPbtUEg3dNKA+IhengIgUwewM5XbZYwTV23UIFsEzmJGhGhDZT2lhwsp5UD8FLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 646814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNECDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTyQ/8DfGKA+0iD3DgZVYHeBAcX2T80To5JFj/sKm8tvYN++sZSHjZ\r\nR3HHJlnE5DKqxMUd0NRQLgsy4vxlWRgtOOFqkBrRp+alh7pz6rBTLHCe+X3t\r\nL/wfWf7RXCE+Qk5TDNXAj+xbSoHeO60hyzLyN9J4SVUycNddnXoRN8OKq+m4\r\nkPXTdh15Sdatj5eq/giZU0uzcT0/HqEzh7KD1AXScfGNR3ZLl1uhXi2saq0p\r\n4+0Nf6hx4UzF8OCWN/8xDGCUhm/kNihHq+bKr7/7/59uoCiwe1+1XQRLQ6Hh\r\nTNV4JYlacuyGO29CmYSg/svTTkj8SfIKNshPm37a+l7zoink84RpYqAzMlAz\r\nAJi3+KmdaObAtoHzjotZoWG5kdrRdHnG0VHpw+9WmhdvLj9BM1gEf2jg5HFj\r\nvu8ul2JBkWXQQSwFXkQp8benaZhiGE3ZtAvqs0Sj3ADHXJAbtpnXGKQKEZM3\r\ndDDlxJInZSHAveaEMArM8apNUdWIOPPTC9v7am41X1NKS/0ChSoNBr4PfnGu\r\n2OKNJIawBX8rG2x/J8lX9pILzvu2eLstdmI2EZ3iV7pDlLkvCuIUB2TxH+JG\r\nEYW0vKDFdh9DanmlslaiMVcqcC81PwDAhwWktAQh1gMy3tmxAaP52H5v3+t/\r\nRBTYSBhiFTX4mcYDW2QOR4Syejr0rpGPeVw=\r\n=6tD+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "c08f0e0dc270e7573160e28c49135c28dd088745", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.0.1_1681145986817_0.8452365544418965", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "lru-cache", "version": "9.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "97f13c6b20532fba9bae821c39cd7d471f65119d", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.0.2.tgz", "fileCount": 17, "integrity": "sha512-7zYMKApzQ9qQE13xQUzbXVY3p2C5lh+9V+bs8M9fRf1TF59id+8jkljRWtIPfBfNP4yQAol5cqh/e8clxatdXw==", "signatures": [{"sig": "MEQCIFA8Po2HB65euI3JExqGQnJmhG8XxXkaeCD/9ekwzE0dAiAZJztHQN1SioBtbgVOFFA5RXAo0dkd7qcYwbBseQlMsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOEqoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrP9g//WtCsUQRsK16SZ4OZHBEG+2FRWanlGRh19LDymyjRyJIRsthi\r\nsV+FCTKtzZJL1j5kNSWldmaA3udcPUHKE1F5DYchmvyF4SAMKzWI+0lTWzOx\r\nvWryFn6yupecOnDIEqv2rTA/9o8ZEgDlGKkP0KIHeBBCHeBVSAS9EXLJnkcB\r\nbLLZypVMhwdxlycYzNY+h/04hERVsfmxrDHhZv1jQGvTe205PG4r5PCYAWbQ\r\nXyQbfyatupxFHOGdJHV9R7q1E+G+xrsCwoZ7oEq9RXpdist/+tS3y0WT3jd5\r\nsBIFotcANDocVSi0fZkNyQ18pz7jmVLsra/Pbzm4+dEaGHdTSuMgoe7K/kLv\r\n0Ch6Uop6ty8Rq9wMngGa4V2xKZ0RXB1/uLfgf3N38knBOrl82z80NzTonBSv\r\npWKhCtOfhgJ0l7GTAg4iXmITEYB0NetNyQVcKy4eOaYkWfpyjgXuzzeTZnTl\r\nGoVPeWyX0bpjporQtN23ZCY8IZ3vro0vKR+v7TiOLNMz1PJVrDK6yqxQ9D1e\r\nwLy6obJlDDFr88AXEcHiksXkIXTah7rEmCj/0r0KkWlS9we069nCrTBjQx+K\r\nSgfhoxL/4bLR/ACbIgOnr06rtf/aqDcwWsmVh6YNMy2Zh3LeP1q6/MqCntAp\r\no4SqqsW/jaL4MU8oc1jizTd17jRlE3TsyeU=\r\n=4VF8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "63e0015791118fd86c848d38f0ca534d97c611d6", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.0.2_1681410728055_0.6677195179148792", "host": "s3://npm-registry-packages"}}, "9.0.3": {"name": "lru-cache", "version": "9.0.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "8a04f282df5320227bb7215c55df2660d3e4e25b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.0.3.tgz", "fileCount": 17, "integrity": "sha512-cyjNRew29d4kbgnz1sjDqxg7qg8NW4s+HQzCGjeon7DV5T2yDije16W9HaUFV1dhVEMh+SjrOcK0TomBmf3Egg==", "signatures": [{"sig": "MEUCIQDfBMMVxiaanSGIPgN6kLMhwQPJ7jIZk5TfgMW+JwimmgIgUev43Y+y9I9najTxDjaTgaMiywHeCabKboI3ifTIaNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 651820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOb9MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6qg//dBfQwtKSqDY0jYacjAj+N3WZrAiysh25ufnqoKK1KJGGTxjP\r\nNqDYzXcHg5j7o8SHSJQfzDL0C/f8biGcl0T6nRiNmPp1LvNrsQgYG5JL6sdm\r\nWq9wgX9xyy+XSM0cwclK23J5sAplGM7oqUJLT1u0f4wj0waN8ayph9oA7QdG\r\n6jxqTpXH4BPmrVU4knTKyj5+TozdlHKVCghbDAvdYfeia4Nn+0q2ZAprCkYV\r\n1psbsk6sOl9MgJBKyB0AEcYFoQU7xitJpk72XgqK3fIiY0fp7pi0b2f4NF3r\r\nHXvFptGfcrburOm1X0KPTS5eKYCdzKuAHwCRunQECuFX92WA5QmX70JLMQsF\r\n/WRomffcZgygCRPcheKgkJe3IyTrwafhYlgsHeNfO/5C4Uz+Aee7PvZWFsXm\r\n3nNcAop+84P4dXl0f2EiEw+cUHM+6ti7PLmi1WldkpcS0xeHpjEWUhg2+/xp\r\n1H5HBeoLngltA6uIF/F7rGIjZUe1K8ihOOdZdz/wjlGEZTbj/MWGfNDocNu0\r\nCpnP/xwC9+r8Ik1YoMTemLJxKqPi/UfB4MrY71l6RHYgwk5MMTSXiGccvFYv\r\ngq8bszRzZXUDbWnf+pzqUkldhued/6IIwkmDbb/52h7QSboO8DKgvumzWhhW\r\nJvQlKX/AtToQ5wCcTDG5ARBUfl/+acXWV0M=\r\n=zHDQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "88bb31c82d418488a18f1663a2a6383853b632a1", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.0.3_1681506124200_0.6269964071629419", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "lru-cache", "version": "9.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "19efafa9d08d1c08eb8efd78876075f0b8b1b07b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.1.0.tgz", "fileCount": 17, "integrity": "sha512-qFXQEwchrZcMVen2uIDceR8Tii6kCJak5rzDStfEM0qA3YLMswaxIEZO0DhIbJ3aqaJiDjt+3crlplOb0tDtKQ==", "signatures": [{"sig": "MEUCIDf5IHA3AgzdQlaxI1Gu0/KByrUZPFC4slKL85viXhYTAiEAqrMsnwlwVBmwoUzt7+mjlCtDyAyXryxpXH2L7suA/dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPjeGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcKA/9GZ4BJktp82FxBDSYSa2R//m4c9Xir2A5OODJ+ChJLpnsRcQp\r\n49DZyGJ/9eRFpSg3HLML8Ib7qLpQZ08eKE+cmr9rYrSkHZMx4/HFxE6cvwOQ\r\nrXAin56o/YWpjq68B/tmAYFCWYTgVhTVjYRLv1Yzhx5FcLq/AGjMs6opnw8w\r\nk+1Oh2JQ7qc7cVmLRYvAqEREuWuOnqZAbTk/GiMv9YHaWoRSv8QraYAaM0Ux\r\nRmSDLiy1pMPlaGa86QjMlYb2Zn0bNoo2dXtA9FgEhvatYHaWBDycTTUIuQkz\r\nY+ZVv8dRth1UfArvz5dWQK8ewCR47S6QiJT7ll+8fGnN4XZWfAakelkW1j5i\r\nbpRHKDbSFiCdH5/67YzJNuBayZ3p6Qlh3+vOHcM3tOECxx15frBkxj94qt05\r\nFuHgClqRjId7sd4HjFAKFyhNNUTjuP5UhbWvmVQa1dihRmrVJgM+7DKNxlQg\r\nFOo/+i1cEPgVgQXOSpZP17pIqOCHQsr6OTY5pSKT6s8WPwcm+2WkLAU8NWv0\r\nimRLlVT+NtEE2+jXgFiOP/zUzuNvU0emgL5DsD4I6w7+bXo17fg2Lm7HZIjl\r\nnO8feeHlAaY+3vN/WsB4il98dt7hV/AXHnzxsdWlUm2LdZrB28IqxoT7BDgV\r\nB87SDhnOqlWA0ounxq9JO6+w6CnxrdFaMbw=\r\n=Y6vP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "4134a83cc65f4acd90010c880e9ad8c401588003", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.1.0_1681799045888_0.9256027548982357", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "lru-cache", "version": "9.1.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "--no-warnings", "--loader", "ts-node/esm"]}, "dist": {"shasum": "c58a93de58630b688de39ad04ef02ef26f1902f1", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.1.1.tgz", "fileCount": 17, "integrity": "sha512-65/Jky17UwSb0BuB9V+MyDpsOtXKmYwzhyl+cOa9XUiI4uV2Ouy/2voFP3+al0BjZbJgMBD8FojMpAf+Z+qn4A==", "signatures": [{"sig": "MEQCIFEH9p5DczCjNuY4dRLOvUgLOM/tyByaqnJ9K+ebSIFhAiAINPw4b4qiSwmL/lZ7o+/K9jYEVGduCYzXSUv8U6XzCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 653411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRIO0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrB5w//YTeXwwxQWij2nYWhwiCbTP55lCklRDgefp66/I73EAo2APuq\r\nz54kuTarGXZMlznRO8M99yDNBHaL/ipp7c75GkHB23G3fNZYnO5vyNr9hcXM\r\n6BCjb7akKtV3IzWGhG2FJIAO5aVf3giVDYgqKcJZUIt31swryDdixK1EO+gB\r\nCnLK3FRTuAmFs31RnU+YOcaLl4i+IontVVlLIyap9bVxOW5fbQJjpChQ0uo+\r\ntz4BOQoscaz6HTxWlHWv0ATtAgfcJHpl5rFGuVvRY9gVGvGMRI0UYOpEcAS8\r\no70xpEDYCcsMUqSUeWfuBxct/Q6C/ZzDNpEnOoFC2/37bH7U7C5cDanRX9Rb\r\nQDFQD2reQuQJWpoWoQSghXbzhM5oeWdxvXbckXXR1glQTtYzFL7fGfuRzPeR\r\nmip4igofilSh/XO3FsB79jCa5qPfSh3Q8yjdRUspulNwdSvTcUq3xl9UbKwF\r\nzWa0UuDCQsc6zI6fRfXscasa2OQ+LrG1IGCABhWk0VRp8babTNeNsOGMtmTY\r\nIASNbVUAp/QMfF5KxjlKN1H+fOKieRMGHynpDbePscGCXWRsDxEGhpslHHMM\r\nw/7SDFoBWuKuXoE8+pVk7G8b+scrhOUPLdL14D+DYxS/NpGno4u+gePJ3PZF\r\nF8zXt7jmSQCxpAbV0ciua1vlVDqJEDH2tBU=\r\n=KNLe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "7d51bb3561c785290c3c700210bb617c757f62b5", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.4", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.7.0", "typedoc": "^0.23.24", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^4.6.4", "@types/node": "^17.0.31", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.1.1_1682211764554_0.4473129870573098", "host": "s3://npm-registry-packages"}}, "9.1.2": {"name": "lru-cache", "version": "9.1.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@9.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "-r", "ts-node/register"]}, "dist": {"shasum": "255fdbc14b75589d6d0e73644ca167a8db506835", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-9.1.2.tgz", "fileCount": 17, "integrity": "sha512-ERJq3FOzJTxBbFjZ7iDs+NiK4VI9Wz+RdrrAB8dio1oV+YvdPzUEE4QNiT2VD51DkIbCYRUUzCRkssXCHqSnKQ==", "signatures": [{"sig": "MEQCID+lK4ijOUyde3vyzf1DVaaFbDSifrfQMuQz+KTnRQvoAiBqfQJgRZEoCvsCnQ3N7rtJl0GUQtHRYM/YyzZ/dMwfnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660095}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "01075cb564a95ee1ac60652ecf96cda302b8d5bc", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.6.7", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.9.1", "typedoc": "^0.24.6", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^5.0.4", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_9.1.2_1685637546812_0.8542414815336028", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "lru-cache", "version": "10.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "-r", "ts-node/register"]}, "dist": {"shasum": "b9e2a6a72a129d81ab317202d93c7691df727e61", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.0.0.tgz", "fileCount": 17, "integrity": "sha512-svTf/fzsKHffP42sujkO/Rjs37BCIsQVRCeNYIm9WN8rgT7ffoUnRtZCqU+6BqcSBdv8gwJeTz8knJpgACeQMw==", "signatures": [{"sig": "MEUCICMvXNkfigEtyM69woEtLyFcVpzHyUzmy5dAW+J/Gtq9AiEA/ENYNnIHJ49ZxxyvVXZGXMcYCHW5XejB+i+jClSi4h4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660558}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "c5ca27729fc4b0a0d8bd73904782fabe114e17a0", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.9.1", "typedoc": "^0.24.6", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^5.0.4", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.0.0_1686851124404_0.6815600786657885", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "lru-cache", "version": "10.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--expose-gc", "-r", "ts-node/register"]}, "dist": {"shasum": "0a3be479df549cca0e5d693ac402ff19537a6b7a", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.0.1.tgz", "fileCount": 17, "integrity": "sha512-IJ4uwUTi2qCccrioU6g9g/5rvvVl13bsdczUUcqbciD9iLr095yj8DQKdObriEvuNSx325N1rV1O0sJFszx75g==", "signatures": [{"sig": "MEYCIQDYe+RHxDMJiHGj5+EAdtI4lFhSDP4KDFiU19ARrV+DsQIhAMzY6X/E8pd5pSU1Fm17/NG1fAemTLVHWIrFz+i+898L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 664048}, "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.min.js"}}}, "gitHead": "870a66deb10fb1a8ecd242ea960465fd3232bac9", "scripts": {"snap": "c8 tap", "test": "c8 tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprepare": "rm -rf dist", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "size-limit": [{"path": "./dist/mjs/index.js"}], "_npmVersion": "9.8.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.3.4", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "ts-node": "^10.9.1", "typedoc": "^0.24.6", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^1.0.6", "size-limit": "^7.0.8", "typescript": "^5.0.4", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.0.1_1691703120033_0.25329435071596396", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "lru-cache", "version": "10.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "34504678cc3266b09b8dfd6fab4e1515258271b7", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.0.2.tgz", "fileCount": 13, "integrity": "sha512-Yj9mA8fPiVgOUpByoTZO5pNrcl5Yk37FcSHsUINpAsaBIEZIuqcCclDZJCVxqQShDsmYX8QG63svJiTbOATZwg==", "signatures": [{"sig": "MEUCIQDXr3urAOqT98NguNlnEAt2LMZjdi6TOCSA4kUAxMuxrQIgV57c1T3rLm8p7lN5QA5MqfiDvavl0A2b0eQ5fNwTQO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448717}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "744ba6d230c51410c51606c6decde861cf8d33bc", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.8.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.0.2_1699629529458_0.6416395921481668", "host": "s3://npm-registry-packages"}}, "10.0.3": {"name": "lru-cache", "version": "10.0.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "b40014d7d2d16d94130b87297a04a1f24874ae7c", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.0.3.tgz", "fileCount": 13, "integrity": "sha512-B7gr+F6MkqB3uzINHXNctGieGsRTMwIBgxkp0yq/5BwcuDzD4A8wQpHQW6vDAm1uKSLQghmRdD9sKqf2vJ1cEg==", "signatures": [{"sig": "MEYCIQDNXrI3b3VXGot9ZK37E7ECl9I5LbShhPMyhEOWNwgBYAIhAN1kAT2KZPX/yXUnnW36H8in4F4B2qdACjYe2jyxiKxA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448705}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "e8feab5bad4bec65043a7d82ff3fbd3297bbeb57", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.0.3_1700367598788_0.5397764706398125", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "lru-cache", "version": "10.1.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "2098d41c2dc56500e6c88584aa656c84de7d0484", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.1.0.tgz", "fileCount": 13, "integrity": "sha512-/1clY/ui8CzjKFyjdvwPWJUYKiFVXG2I2cY0ssG7h4+hwk+XOIX7ZSG9Q7TW8TW3Kp3BUSqgFWBLgL4PJ+Blag==", "signatures": [{"sig": "MEYCIQCT+ADpfE9lLzdgbbK0jOzRj6IrWnTZkie3zWS4c37ESgIhAPI8G53CV5+z6gmDoQ6JABESoFKeQCmBMe6xK6pNfLm0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 456410}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "58e6aa8a861c43780d6760ae12d983c08dac41c9", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.1.0_1700678888563_0.835097983864266", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "lru-cache", "version": "10.2.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "0bd445ca57363465900f4d1f9bd8db343a4d95c3", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.0.tgz", "fileCount": 13, "integrity": "sha512-2bIM8x+VAf6JT4bKAljS1qUWgMsqZRPGJS6FSahIMPVvctcNhyVp7AJu7quxOW9jwkryBReKZY5tY5JYv2n/7Q==", "signatures": [{"sig": "MEYCIQCe/rxK844idxw/6u4bYswHacr/zUwkFddtogiq68tynwIhAPuivjD7UIaKxMcAexrXpS3jFqn+67cKPC6LhExoJtAo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 457894}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "c7dd17f530943e96a26a110e51963abb7c17a960", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.2.0_1706217116503_0.5780037708803691", "host": "s3://npm-registry-packages"}}, "10.2.1": {"name": "lru-cache", "version": "10.2.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "e8d901141f22937968e45a6533d52824070151e4", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.1.tgz", "fileCount": 13, "integrity": "sha512-tS24spDe/zXhWbNPErCHs/AGOzbKGHT+ybSBqmdLm8WZ1xXLWvH8Qn71QPAlqVhd0qUTWjy+Kl9JmISgDdEjsA==", "signatures": [{"sig": "MEYCIQCc6z6qZo7piD6Soy2tjUiJaGpvZAbW+9c4KXRyO/BHwAIhAMGDGfLERlV3Vj07Bumv6z8Zu4W72S6yFX2Au+5E+3Vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 457894}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "9fed5bee978690a09e67c6f02e7be57b9e7d5cc0", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.2.1_1714060141058_0.5039324334176987", "host": "s3://npm-registry-packages"}}, "10.2.2": {"name": "lru-cache", "version": "10.2.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "48206bc114c1252940c41b25b41af5b545aca878", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.2.tgz", "fileCount": 17, "integrity": "sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ==", "signatures": [{"sig": "MEUCIQDCOKdVYZDOWLh8ED6nh26KEkI9MidI4WnRCkwWgX/lKgIgd0af8I+SONpDVFSncky5FNSmFrArIqVs6rI8TckNPiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 680860}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "150764920a2798df362811ced8dc20041e35b7ee", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.2.2_1714342767574_0.4100468428394972", "host": "s3://npm-registry-packages"}}, "10.3.0": {"name": "lru-cache", "version": "10.3.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.3.0", "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "4a4aaf10c84658ab70f79a85a9a3f1e1fb11196b", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.3.0.tgz", "fileCount": 17, "integrity": "sha512-CQl19J/g+Hbjbv4Y3mFNNXFEL/5t/KCg8POCuUqd4rMKjGG+j1ybER83hxV58zL+dFI1PTkt3GNFSHRt+d8qEQ==", "signatures": [{"sig": "MEUCIQCAdCgXQrSzVEbnHI4EHzHqMwio9IaO/mrl2XeothoNHgIgaJy//yiJUXMGfvUaGzf/k3KanW/eDzWoO9FhlSa/ksE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804323}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 || >=16.14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "d71af85ef2c0604bf93357315097b52d6f17240c", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.7", "tshy": "^1.8.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "clock-mock": "^2.0.2", "typescript": "^5.2.2", "@types/node": "^20.2.5", "@tapjs/clock": "^1.1.16", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.3.0_1719540061785_0.3947996455465521", "host": "s3://npm-registry-packages"}}, "10.3.1": {"name": "lru-cache", "version": "10.3.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "a37050586f84ccfdb570148a253bf1632a29ef44", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.3.1.tgz", "fileCount": 17, "integrity": "sha512-9/8QXrtbGeMB6LxwQd4x1tIMnsmUxMvIH/qWGsccz6bt9Uln3S+sgAaqfQNhbGA8ufzs2fHuP/yqapGgP9Hh2g==", "signatures": [{"sig": "MEYCIQC0cK3rYQIseIq97ppuY7XvnRv7acy6Pn6P2+nD/KyBSgIhAK2eks0IZ2bBMQLeNmYRzD+bKzw+HyfIv3kbOpokearA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804363}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "3edad217ce3a4b6535baf03957882d8afff82532", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^1.17.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.3.1_1720239279168_0.39567121574392305", "host": "s3://npm-registry-packages"}}, "10.4.0": {"name": "lru-cache", "version": "10.4.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "cb29b4b2dd55b22e4a729cdb096093d7f85df02d", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.0.tgz", "fileCount": 17, "integrity": "sha512-bfJaPTuEiTYBu+ulDaeQ0F+uLmlfFkMgXj4cbwfuMSjgObGMzb55FMMbDvbRU0fAHZ4sLGkz2mKwcMg8Dvm8Ww==", "signatures": [{"sig": "MEYCIQCBRBOEnnEIVjPyx2oXGLchHJDvQqOsKMyug6mnRBsOkAIhAK5RBC2KVtqFY3f6nlWG5fMVA6RSh0w9Uftj59PFxfhG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804290}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "52c9cb00034799257a4e08d9b7f037e409e00dbb", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.4.0_1720397106380_0.5267471832841866", "host": "s3://npm-registry-packages"}}, "10.4.1": {"name": "lru-cache", "version": "10.4.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "da9a9cb51aec89fda9b485f5a12b2fdb8f6dbe88", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.1.tgz", "fileCount": 17, "integrity": "sha512-8h/JsUc/2+Dm9RPJnBAmObGnUqTMmsIKThxixMLOkrebSihRhTV0wLD/8BSk6OU6Pbj8hiDTbsI3fLjBJSlhDg==", "signatures": [{"sig": "MEYCIQDkwiQ89aPHlodZcENNaXDb9s7djMsMBGUnP8WZo+3f5AIhAOzrtCRKJTa/sXN6+cNzzPPDs/Spgn21ZAPm7QIZiRHk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804340}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "14 >= 14.21 || 16 >= 16.20 || 18 >=18.20 || 20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "e01135c4270941ac54d00a6b96eefdca31f3a6f6", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.4.1_1720475592813_0.10475492289736654", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "lru-cache", "version": "11.0.0", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@11.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "15d93a196f189034d7166caf9fe55e7384c98a21", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-11.0.0.tgz", "fileCount": 17, "integrity": "sha512-Qv32eSV1RSCfhY3fpPE2GNZ8jgM9X7rdAfemLWqTUxwiyIC4jJ6Sy0fZ8H+oLWevO6i4/bizg7c8d8i6bxrzbA==", "signatures": [{"sig": "MEYCIQDGn9fFQIL8qN3Dot1OMtld9asGdAOLBqwSqfa6KxeRnwIhAPbWWndVdzfEUT5/izJiZp5GghEYMu7WdHZWml1Lo5Ed", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804296}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "e1981d1719accbcb2c3604303bb4b7a8a6cc267a", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_11.0.0_1720475647564_0.6687738198218627", "host": "s3://npm-registry-packages"}}, "10.4.2": {"name": "lru-cache", "version": "10.4.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.4.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "78c38f194b747174cff90e60afabcae40c3619f2", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.2.tgz", "fileCount": 17, "integrity": "sha512-voV4dDrdVZVNz84n39LFKDaRzfwhdzJ7akpyXfTMxCgRUp07U3lcJUXRlhTKP17rgt09sUzLi5iCitpEAr+6ug==", "signatures": [{"sig": "MEUCIADy0NCscla+DXWVKDwEzeD/Ze8syRENYb1nz6sbXVbfAiEA2j3ko6JaYqt+l6ZGqgeDN+xLpuV/krx8t0AVfxSU0EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804364}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "14 || 16 || 18 || 20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "f7e9f2ad98dda229cb0b99f44bd1d32be86d74bc", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "publishConfig": {"tag": "legacy-v10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.4.2_1720493688195_0.9895544946653254", "host": "s3://npm-registry-packages"}}, "10.4.3": {"name": "lru-cache", "version": "10.4.3", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@10.4.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "410fc8a17b70e598013df257c2446b7f3383f119", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "fileCount": 17, "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "signatures": [{"sig": "MEUCIQDE9j3XqgM58+QJC4D3liZT4PFtynHR8pJJQEU7KGW1kAIgWdnTxB1ZZ9Omsiz583CKbQddaJjNP9Tdg9ZkvJ/5Ttg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 804301}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "228d71964978ad71a7dcea664c494468c780b956", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "publishConfig": {"tag": "legacy-v10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.0", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.25.3", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "typescript": "^5.2.2", "@types/node": "^20.2.5", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_10.4.3_1720545651941_0.955027377139289", "host": "s3://npm-registry-packages"}}, "11.0.1": {"name": "lru-cache", "version": "11.0.1", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@11.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "3a732fbfedb82c5ba7bca6564ad3f42afcb6e147", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-11.0.1.tgz", "fileCount": 17, "integrity": "sha512-CgeuL5uom6j/ZVrg7G/+1IXqRY8JXX4Hghfy5YE0EhoYQWvndP1kufu58cmZLNIDKnRhZrXfdS9urVWx98AipQ==", "signatures": [{"sig": "MEYCIQCBAXzukOQcYJLVMrIdRR1FFh7eEbKJmNU0UeC1f0AWUQIhAInNuD6vQ8KGpMVFPaCdqR1q83W64kTLMJcYBVt4FP15", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 807826}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "cbdd1d0eff9d1a4d89dfe756678ca92a27d1551c", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^21.0.1", "tshy": "^3.0.2", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.26.6", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "@types/node": "^22.5.4", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_11.0.1_1725592639089_0.4055454916510839", "host": "s3://npm-registry-packages"}}, "11.0.2": {"name": "lru-cache", "version": "11.0.2", "keywords": ["mru", "lru", "cache"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "lru-cache@11.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "tap": {"plugin": ["@tapjs/clock"], "node-arg": ["--expose-gc"]}, "dist": {"shasum": "fbd8e7cf8211f5e7e5d91905c415a3f55755ca39", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-11.0.2.tgz", "fileCount": 17, "integrity": "sha512-123qHRfJBmo2jXDbo/a5YOQrJoHF/GNQTLzQ5+IdK5pWpceK17yRc6ozlWd25FxvGKQbIUs91fDFkXmDHTKcyA==", "signatures": [{"sig": "MEYCIQCoOEDA5ZwfskJK7UkLiszBmmv0GxggpYShS7aIcDimCgIhANeyvSCuvuRqhNfUU06gvXEsC4Jb3TAMPYwMftv88Cna", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 807860}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "gitHead": "65c9971e3fef123ff0f17d67add53b2d99b461b0", "scripts": {"snap": "tap", "test": "tap", "build": "npm run prepare", "format": "prettier --write .", "prepare": "tshy && bash fixup.sh", "presnap": "npm run prepare", "pretest": "npm run prepare", "profile": "make -C benchmark profile", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "preversion": "npm test", "postversion": "npm publish", "prebenchmark": "npm run prepare", "prepublishOnly": "git push origin --follow-tags", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/node-lru-cache.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A cache object that deletes the least-recently-used items.", "directories": {}, "sideEffects": false, "_nodeVersion": "22.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^21.0.1", "tshy": "^3.0.2", "tslib": "^2.4.0", "marked": "^4.2.12", "mkdirp": "^2.1.5", "esbuild": "^0.17.11", "typedoc": "^0.26.6", "prettier": "^2.6.2", "benchmark": "^2.1.4", "@types/tap": "^15.0.6", "@types/node": "^22.5.4", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/lru-cache_11.0.2_1730397080054_0.9058538021200011", "host": "s3://npm-registry-packages"}}, "11.1.0": {"name": "lru-cache", "description": "A cache object that deletes the least-recently-used items.", "version": "11.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["mru", "lru", "cache"], "sideEffects": false, "scripts": {"build": "npm run prepare", "prepare": "tshy && bash fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write .", "typedoc": "typedoc --tsconfig ./.tshy/esm.json ./src/*.ts", "benchmark-results-typedoc": "bash scripts/benchmark-results-typedoc.sh", "prebenchmark": "npm run prepare", "benchmark": "make -C benchmark", "preprofile": "npm run prepare", "profile": "make -C benchmark profile"}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "tshy": {"exports": {".": "./src/index.ts", "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "devDependencies": {"@types/node": "^22.5.4", "benchmark": "^2.1.4", "esbuild": "^0.25.1", "marked": "^4.2.12", "mkdirp": "^3.0.1", "prettier": "^3.5.3", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.1"}, "license": "ISC", "engines": {"node": "20 || >=22"}, "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"node-arg": ["--expose-gc"], "plugin": ["@tapjs/clock"]}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./min": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.min.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.min.js"}}}, "type": "module", "module": "./dist/esm/index.js", "_id": "lru-cache@11.1.0", "gitHead": "44e56735e5da8582a63b2f0a41af8b5f9d4a069b", "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "homepage": "https://github.com/isaacs/node-lru-cache#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-QIXZUBJUx+2zHUdQujWejBkcD9+cs94tLn0+YL8UrCh+D5sCXZ4c7LaEH48pNwRY3MLDgqUFyhlCyjJPf1WP0A==", "shasum": "afafb060607108132dbc1cf8ae661afb69486117", "tarball": "https://registry.npmjs.org/lru-cache/-/lru-cache-11.1.0.tgz", "fileCount": 17, "unpackedSize": 819995, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIB4CQotciE7WpG65dGN6OMNdJrcFsxCuVBm4fbz2W/lZAiA70bK6zhbDvptwW0rG2vu0os33rZig5jpfq9OzVZMKLA=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lru-cache_11.1.0_1742829258301_0.1662545006796785"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-07-16T09:09:00.041Z", "modified": "2025-03-24T15:14:18.754Z", "1.0.2": "2011-07-16T09:09:00.041Z", "1.0.1": "2011-07-16T09:09:00.041Z", "1.0.3": "2011-07-16T09:09:00.041Z", "1.0.4": "2011-07-29T19:12:01.745Z", "1.0.5": "2011-12-09T01:12:43.326Z", "1.0.6": "2012-03-27T20:58:02.558Z", "1.1.0": "2012-04-10T23:40:12.876Z", "1.1.1": "2012-08-01T09:36:45.601Z", "2.0.0": "2012-08-09T22:21:20.057Z", "2.0.1": "2012-08-14T01:07:53.273Z", "2.0.2": "2012-08-27T16:37:06.609Z", "2.0.3": "2012-09-13T05:19:49.029Z", "2.0.4": "2012-09-17T15:57:27.674Z", "2.1.0": "2012-10-19T12:23:46.276Z", "2.2.0": "2012-11-29T18:39:13.480Z", "2.2.1": "2012-11-29T19:35:00.176Z", "2.2.2": "2013-01-15T16:07:29.189Z", "2.2.4": "2013-03-26T00:15:52.058Z", "2.3.0": "2013-03-26T00:28:31.542Z", "2.3.1": "2013-08-19T21:27:14.453Z", "2.5.0": "2013-11-21T17:38:28.040Z", "2.5.1": "2015-04-10T16:23:17.960Z", "2.5.2": "2015-04-10T16:26:47.162Z", "2.6.0": "2015-04-15T17:59:01.480Z", "2.6.1": "2015-04-15T18:09:11.352Z", "2.6.2": "2015-04-24T22:13:44.022Z", "2.6.3": "2015-05-15T01:21:44.570Z", "2.6.4": "2015-05-19T01:38:58.214Z", "2.6.5": "2015-06-30T18:20:57.642Z", "2.7.0": "2015-09-11T18:23:12.257Z", "2.7.1": "2015-11-23T23:56:40.083Z", "2.7.2": "2015-11-24T19:23:22.999Z", "2.7.3": "2015-11-25T18:12:14.005Z", "3.0.0": "2015-11-27T22:55:18.812Z", "3.1.0": "2015-11-27T23:09:26.205Z", "3.1.1": "2015-11-28T01:03:32.062Z", "3.1.2": "2015-11-28T02:50:32.082Z", "3.2.0": "2015-11-28T21:54:13.864Z", "4.0.0": "2015-12-21T04:46:32.089Z", "4.0.1": "2016-03-22T17:22:52.848Z", "4.0.2": "2016-11-27T19:10:00.911Z", "4.1.0": "2017-06-06T17:54:15.370Z", "4.1.1": "2017-06-11T03:00:46.159Z", "4.1.2": "2018-03-08T17:58:33.933Z", "4.1.3": "2018-05-07T23:17:11.482Z", "4.1.4": "2018-11-21T00:14:09.190Z", "5.0.0": "2018-11-21T00:46:38.801Z", "5.0.1": "2018-11-21T01:04:56.118Z", "5.1.0": "2018-11-21T01:23:13.744Z", "5.1.1": "2018-11-21T01:44:45.407Z", "4.1.5": "2018-11-29T17:49:34.387Z", "6.0.0": "2020-07-11T00:59:07.352Z", "7.0.0": "2022-02-08T00:46:25.781Z", "7.0.1": "2022-02-08T00:55:45.942Z", "7.1.0": "2022-02-08T18:06:28.749Z", "7.2.0": "2022-02-08T19:35:28.576Z", "7.3.0": "2022-02-09T00:29:39.933Z", "7.3.1": "2022-02-09T15:21:24.580Z", "7.2.1": "2022-02-09T15:23:49.075Z", "7.1.1": "2022-02-09T15:25:26.816Z", "7.0.2": "2022-02-09T15:26:43.140Z", "7.4.0": "2022-02-22T00:52:48.599Z", "7.4.1": "2022-03-05T04:42:11.553Z", "7.4.2": "2022-03-09T16:15:42.374Z", "7.4.3": "2022-03-10T19:24:02.285Z", "7.4.4": "2022-03-10T19:26:08.014Z", "7.3.2": "2022-03-10T19:32:42.532Z", "7.2.2": "2022-03-10T19:33:24.808Z", "7.1.2": "2022-03-10T19:34:02.046Z", "7.0.3": "2022-03-10T19:34:45.898Z", "7.5.0": "2022-03-14T02:57:55.093Z", "7.5.1": "2022-03-14T17:41:52.929Z", "7.6.0": "2022-03-17T03:59:45.837Z", "7.7.0": "2022-03-17T23:47:52.537Z", "7.7.1": "2022-03-18T03:00:08.858Z", "7.7.2": "2022-03-29T21:46:14.178Z", "7.7.3": "2022-03-30T15:18:55.150Z", "7.8.0": "2022-04-07T19:44:11.404Z", "7.8.1": "2022-04-09T19:21:00.559Z", "7.7.4": "2022-04-09T19:24:06.845Z", "7.6.1": "2022-04-09T19:27:14.020Z", "7.5.2": "2022-04-09T19:34:05.904Z", "7.4.5": "2022-04-09T19:35:55.240Z", "7.3.3": "2022-04-09T19:37:54.210Z", "7.2.3": "2022-04-09T19:39:45.312Z", "7.1.3": "2022-04-09T19:41:38.279Z", "7.0.4": "2022-04-09T19:44:50.120Z", "7.8.2": "2022-04-30T02:44:20.634Z", "7.9.0": "2022-04-30T04:50:16.143Z", "7.9.1": "2022-05-11T19:27:53.310Z", "7.10.0": "2022-05-11T19:32:02.584Z", "7.10.1": "2022-05-11T22:51:48.451Z", "7.10.2": "2022-06-23T22:30:48.426Z", "7.10.3": "2022-06-29T19:19:17.650Z", "7.11.0": "2022-06-29T22:07:11.851Z", "7.12.0": "2022-06-29T22:42:39.511Z", "7.12.1": "2022-07-12T23:00:45.304Z", "7.13.0": "2022-07-12T23:16:44.308Z", "7.13.1": "2022-07-14T23:30:07.377Z", "7.13.2": "2022-08-02T17:57:32.835Z", "7.14.0": "2022-08-16T22:14:17.758Z", "7.14.1": "2022-11-02T17:04:30.635Z", "7.15.0": "2023-02-16T00:33:39.543Z", "7.16.0": "2023-02-16T19:48:56.658Z", "7.16.1": "2023-02-17T22:02:56.193Z", "7.16.2": "2023-02-21T20:02:36.827Z", "7.17.0": "2023-02-22T00:53:32.474Z", "7.17.1": "2023-03-01T07:44:10.359Z", "7.18.0": "2023-03-01T07:44:50.868Z", "7.17.2": "2023-03-01T07:47:33.373Z", "7.18.1": "2023-03-01T09:26:57.672Z", "7.18.2": "2023-03-05T03:29:11.766Z", "7.18.3": "2023-03-05T18:04:26.750Z", "8.0.0": "2023-03-12T01:10:05.236Z", "8.0.1": "2023-03-15T18:18:49.143Z", "8.0.2": "2023-03-15T18:29:33.858Z", "8.0.3": "2023-03-15T18:43:51.539Z", "8.0.4": "2023-03-17T23:05:36.272Z", "8.0.5": "2023-04-05T06:06:32.862Z", "9.0.0": "2023-04-09T21:41:12.855Z", "9.0.1": "2023-04-10T16:59:46.983Z", "9.0.2": "2023-04-13T18:32:08.427Z", "9.0.3": "2023-04-14T21:02:04.467Z", "9.1.0": "2023-04-18T06:24:06.133Z", "9.1.1": "2023-04-23T01:02:44.856Z", "9.1.2": "2023-06-01T16:39:07.022Z", "10.0.0": "2023-06-15T17:45:24.798Z", "10.0.1": "2023-08-10T21:32:00.348Z", "10.0.2": "2023-11-10T15:18:50.180Z", "10.0.3": "2023-11-19T04:19:59.082Z", "10.1.0": "2023-11-22T18:48:08.803Z", "10.2.0": "2024-01-25T21:11:56.776Z", "10.2.1": "2024-04-25T15:49:01.225Z", "10.2.2": "2024-04-28T22:19:27.837Z", "10.3.0": "2024-06-28T02:01:02.016Z", "10.3.1": "2024-07-06T04:14:39.428Z", "10.4.0": "2024-07-08T00:05:06.577Z", "10.4.1": "2024-07-08T21:53:13.080Z", "11.0.0": "2024-07-08T21:54:07.840Z", "10.4.2": "2024-07-09T02:54:48.371Z", "10.4.3": "2024-07-09T17:20:52.211Z", "11.0.1": "2024-09-06T03:17:19.396Z", "11.0.2": "2024-10-31T17:51:20.390Z", "11.1.0": "2025-03-24T15:14:18.553Z"}, "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://github.com/isaacs/node-lru-cache#readme", "keywords": ["mru", "lru", "cache"], "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "description": "A cache object that deletes the least-recently-used items.", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# lru-cache\n\nA cache object that deletes the least-recently-used items.\n\nSpecify a max number of the most recently used items that you\nwant to keep, and this cache will keep that many of the most\nrecently accessed items.\n\nThis is not primarily a TTL cache, and does not make strong TTL\nguarantees. There is no preemptive pruning of expired items by\ndefault, but you _may_ set a TTL on the cache or on a single\n`set`. If you do so, it will treat expired items as missing, and\ndelete them when fetched. If you are more interested in TTL\ncaching than LRU caching, check out\n[@isaacs/ttlcache](http://npm.im/@isaacs/ttlcache).\n\nAs of version 7, this is one of the most performant LRU\nimplementations available in JavaScript, and supports a wide\ndiversity of use cases. However, note that using some of the\nfeatures will necessarily impact performance, by causing the\ncache to have to do more work. See the \"Performance\" section\nbelow.\n\n## Installation\n\n```bash\nnpm install lru-cache --save\n```\n\n## Usage\n\n```js\n// hybrid module, either works\nimport { LRUCache } from 'lru-cache'\n// or:\nconst { LRUCache } = require('lru-cache')\n// or in minified form for web browsers:\nimport { LRUCache } from 'http://unpkg.com/lru-cache@9/dist/mjs/index.min.mjs'\n\n// At least one of 'max', 'ttl', or 'maxSize' is required, to prevent\n// unsafe unbounded storage.\n//\n// In most cases, it's best to specify a max for performance, so all\n// the required memory allocation is done up-front.\n//\n// All the other options are optional, see the sections below for\n// documentation on what each one does.  Most of them can be\n// overridden for specific items in get()/set()\nconst options = {\n  max: 500,\n\n  // for use with tracking overall storage size\n  maxSize: 5000,\n  sizeCalculation: (value, key) => {\n    return 1\n  },\n\n  // for use when you need to clean up something when objects\n  // are evicted from the cache\n  dispose: (value, key, reason) => {\n    freeFromMemoryOrWhatever(value)\n  },\n\n  // for use when you need to know that an item is being inserted\n  // note that this does NOT allow you to prevent the insertion,\n  // it just allows you to know about it.\n  onInsert: (value, key, reason) => {\n    logInsertionOrWhatever(key, value)\n  },\n\n  // how long to live in ms\n  ttl: 1000 * 60 * 5,\n\n  // return stale items before removing from cache?\n  allowStale: false,\n\n  updateAgeOnGet: false,\n  updateAgeOnHas: false,\n\n  // async method to use for cache.fetch(), for\n  // stale-while-revalidate type of behavior\n  fetchMethod: async (\n    key,\n    staleValue,\n    { options, signal, context }\n  ) => {},\n}\n\nconst cache = new LRUCache(options)\n\ncache.set('key', 'value')\ncache.get('key') // \"value\"\n\n// non-string keys ARE fully supported\n// but note that it must be THE SAME object, not\n// just a JSON-equivalent object.\nvar someObject = { a: 1 }\ncache.set(someObject, 'a value')\n// Object keys are not toString()-ed\ncache.set('[object Object]', 'a different value')\nassert.equal(cache.get(someObject), 'a value')\n// A similar object with same keys/values won't work,\n// because it's a different object identity\nassert.equal(cache.get({ a: 1 }), undefined)\n\ncache.clear() // empty the cache\n```\n\nIf you put more stuff in the cache, then less recently used items\nwill fall out. That's what an LRU cache is.\n\nFor full description of the API and all options, please see [the\nLRUCache typedocs](https://isaacs.github.io/node-lru-cache/)\n\n## Storage Bounds Safety\n\nThis implementation aims to be as flexible as possible, within\nthe limits of safe memory consumption and optimal performance.\n\nAt initial object creation, storage is allocated for `max` items.\nIf `max` is set to zero, then some performance is lost, and item\ncount is unbounded. Either `maxSize` or `ttl` _must_ be set if\n`max` is not specified.\n\nIf `maxSize` is set, then this creates a safe limit on the\nmaximum storage consumed, but without the performance benefits of\npre-allocation. When `maxSize` is set, every item _must_ provide\na size, either via the `sizeCalculation` method provided to the\nconstructor, or via a `size` or `sizeCalculation` option provided\nto `cache.set()`. The size of every item _must_ be a positive\ninteger.\n\nIf neither `max` nor `maxSize` are set, then `ttl` tracking must\nbe enabled. Note that, even when tracking item `ttl`, items are\n_not_ preemptively deleted when they become stale, unless\n`ttlAutopurge` is enabled. Instead, they are only purged the\nnext time the key is requested. Thus, if `ttlAutopurge`, `max`,\nand `maxSize` are all not set, then the cache will potentially\ngrow unbounded.\n\nIn this case, a warning is printed to standard error. Future\nversions may require the use of `ttlAutopurge` if `max` and\n`maxSize` are not specified.\n\nIf you truly wish to use a cache that is bound _only_ by TTL\nexpiration, consider using a `Map` object, and calling\n`setTimeout` to delete entries when they expire. It will perform\nmuch better than an LRU cache.\n\nHere is an implementation you may use, under the same\n[license](./LICENSE) as this package:\n\n```js\n// a storage-unbounded ttl cache that is not an lru-cache\nconst cache = {\n  data: new Map(),\n  timers: new Map(),\n  set: (k, v, ttl) => {\n    if (cache.timers.has(k)) {\n      clearTimeout(cache.timers.get(k))\n    }\n    cache.timers.set(\n      k,\n      setTimeout(() => cache.delete(k), ttl)\n    )\n    cache.data.set(k, v)\n  },\n  get: k => cache.data.get(k),\n  has: k => cache.data.has(k),\n  delete: k => {\n    if (cache.timers.has(k)) {\n      clearTimeout(cache.timers.get(k))\n    }\n    cache.timers.delete(k)\n    return cache.data.delete(k)\n  },\n  clear: () => {\n    cache.data.clear()\n    for (const v of cache.timers.values()) {\n      clearTimeout(v)\n    }\n    cache.timers.clear()\n  },\n}\n```\n\nIf that isn't to your liking, check out\n[@isaacs/ttlcache](http://npm.im/@isaacs/ttlcache).\n\n## Storing Undefined Values\n\nThis cache never stores undefined values, as `undefined` is used\ninternally in a few places to indicate that a key is not in the\ncache.\n\nYou may call `cache.set(key, undefined)`, but this is just\nan alias for `cache.delete(key)`. Note that this has the effect\nthat `cache.has(key)` will return _false_ after setting it to\nundefined.\n\n```js\ncache.set(myKey, undefined)\ncache.has(myKey) // false!\n```\n\nIf you need to track `undefined` values, and still note that the\nkey is in the cache, an easy workaround is to use a sigil object\nof your own.\n\n```js\nimport { LRUCache } from 'lru-cache'\nconst undefinedValue = Symbol('undefined')\nconst cache = new LRUCache(...)\nconst mySet = (key, value) =>\n  cache.set(key, value === undefined ? undefinedValue : value)\nconst myGet = (key, value) => {\n  const v = cache.get(key)\n  return v === undefinedValue ? undefined : v\n}\n```\n\n## Performance\n\nAs of January 2022, version 7 of this library is one of the most\nperformant LRU cache implementations in JavaScript.\n\nBenchmarks can be extremely difficult to get right. In\nparticular, the performance of set/get/delete operations on\nobjects will vary _wildly_ depending on the type of key used. V8\nis highly optimized for objects with keys that are short strings,\nespecially integer numeric strings. Thus any benchmark which\ntests _solely_ using numbers as keys will tend to find that an\nobject-based approach performs the best.\n\nNote that coercing _anything_ to strings to use as object keys is\nunsafe, unless you can be 100% certain that no other type of\nvalue will be used. For example:\n\n```js\nconst myCache = {}\nconst set = (k, v) => (myCache[k] = v)\nconst get = k => myCache[k]\n\nset({}, 'please hang onto this for me')\nset('[object Object]', 'oopsie')\n```\n\nAlso beware of \"Just So\" stories regarding performance. Garbage\ncollection of large (especially: deep) object graphs can be\nincredibly costly, with several \"tipping points\" where it\nincreases exponentially. As a result, putting that off until\nlater can make it much worse, and less predictable. If a library\nperforms well, but only in a scenario where the object graph is\nkept shallow, then that won't help you if you are using large\nobjects as keys.\n\nIn general, when attempting to use a library to improve\nperformance (such as a cache like this one), it's best to choose\nan option that will perform well in the sorts of scenarios where\nyou'll actually use it.\n\nThis library is optimized for repeated gets and minimizing\neviction time, since that is the expected need of a LRU. Set\noperations are somewhat slower on average than a few other\noptions, in part because of that optimization. It is assumed\nthat you'll be caching some costly operation, ideally as rarely\nas possible, so optimizing set over get would be unwise.\n\nIf performance matters to you:\n\n1. If it's at all possible to use small integer values as keys,\n   and you can guarantee that no other types of values will be\n   used as keys, then do that, and use a cache such as\n   [lru-fast](https://npmjs.com/package/lru-fast), or\n   [mnemonist's\n   LRUCache](https://yomguithereal.github.io/mnemonist/lru-cache)\n   which uses an Object as its data store.\n\n2. Failing that, if at all possible, use short non-numeric\n   strings (ie, less than 256 characters) as your keys, and use\n   [mnemonist's\n   LRUCache](https://yomguithereal.github.io/mnemonist/lru-cache).\n\n3. If the types of your keys will be anything else, especially\n   long strings, strings that look like floats, objects, or some\n   mix of types, or if you aren't sure, then this library will\n   work well for you.\n\n   If you do not need the features that this library provides\n   (like asynchronous fetching, a variety of TTL staleness\n   options, and so on), then [mnemonist's\n   LRUMap](https://yomguithereal.github.io/mnemonist/lru-map) is\n   a very good option, and just slightly faster than this module\n   (since it does considerably less).\n\n4. Do not use a `dispose` function, size tracking, or especially\n   ttl behavior, unless absolutely needed. These features are\n   convenient, and necessary in some use cases, and every attempt\n   has been made to make the performance impact minimal, but it\n   isn't nothing.\n\n## Breaking Changes in Version 7\n\nThis library changed to a different algorithm and internal data\nstructure in version 7, yielding significantly better\nperformance, albeit with some subtle changes as a result.\n\nIf you were relying on the internals of LRUCache in version 6 or\nbefore, it probably will not work in version 7 and above.\n\n## Breaking Changes in Version 8\n\n- The `fetchContext` option was renamed to `context`, and may no\n  longer be set on the cache instance itself.\n- Rewritten in TypeScript, so pretty much all the types moved\n  around a lot.\n- The AbortController/AbortSignal polyfill was removed. For this\n  reason, **Node version 16.14.0 or higher is now required**.\n- Internal properties were moved to actual private class\n  properties.\n- Keys and values must not be `null` or `undefined`.\n- Minified export available at `'lru-cache/min'`, for both CJS\n  and MJS builds.\n\n## Breaking Changes in Version 9\n\n- Named export only, no default export.\n- AbortController polyfill returned, albeit with a warning when\n  used.\n\n## Breaking Changes in Version 10\n\n- `cache.fetch()` return type is now `Promise<V | undefined>`\n  instead of `Promise<V | void>`. This is an irrelevant change\n  practically speaking, but can require changes for TypeScript\n  users.\n\nFor more info, see the [change log](CHANGELOG.md).\n", "readmeFilename": "README.md", "users": {"285858315": true, "detj": true, "dwqs": true, "hden": true, "neo1": true, "nuer": true, "shan": true, "vasz": true, "ccyll": true, "hanhq": true, "kktam": true, "lcdss": true, "lfeng": true, "makay": true, "romac": true, "soloi": true, "sopov": true, "syzer": true, "yvesm": true, "adamlu": true, "buzuli": true, "cr8tiv": true, "cybo42": true, "d-band": true, "daizch": true, "egantz": true, "genexp": true, "gindis": true, "isayme": true, "joanmi": true, "koslun": true, "minghe": true, "patmcc": true, "sirany": true, "tayden": true, "tedyhy": true, "v3rron": true, "x-cold": true, "yeming": true, "barenko": true, "biao166": true, "chaoliu": true, "chriszs": true, "elcobvg": true, "endless": true, "hckhanh": true, "jacques": true, "jerrywu": true, "jovinbm": true, "lianall": true, "newswim": true, "piron_t": true, "shavyg2": true, "tsxuehu": true, "wookieb": true, "ashsidhu": true, "bapinney": true, "bchociej": true, "coalesce": true, "dgarlitt": true, "draganhr": true, "dskrepps": true, "fargie_s": true, "heineiuo": true, "hoitmort": true, "jianping": true, "kenlimmj": true, "leonzhao": true, "luislobo": true, "qt911025": true, "ssljivic": true, "voxpelli": true, "wangfeia": true, "zuojiang": true, "belirafon": true, "fgribreau": true, "gavinning": true, "mojaray2k": true, "roccomuso": true, "sasquatch": true, "zihuxinyu": true, "acjohnso25": true, "brunocalou": true, "cestrensem": true, "derekclair": true, "micahjonas": true, "neefrankie": true, "nickleefly": true, "princetoad": true, "shuoshubao": true, "sisidovski": true, "thinkholic": true, "tiancheng9": true, "zaphod1984": true, "ahmed-dinar": true, "appsparkler": true, "mccoyjordan": true, "michaelnisi": true, "sessionbean": true, "shangsinian": true, "soenkekluth": true, "vparaskevas": true, "wangnan0610": true, "xinwangwang": true, "ahmedelgabri": true, "ashleybrener": true, "comigo-npmjs": true, "hugojosefson": true, "mpinteractiv": true, "shaomingquan": true, "tommytroylin": true, "zhangyaochun": true, "adamantium169": true, "crazyjingling": true, "diegorbaquero": true, "yinyongcom666": true, "andrew.medvedev": true, "brianloveswords": true, "joaquin.briceno": true, "subinvarghesein": true, "joris-van-der-wel": true, "nikolayantonovsoserov": true}}