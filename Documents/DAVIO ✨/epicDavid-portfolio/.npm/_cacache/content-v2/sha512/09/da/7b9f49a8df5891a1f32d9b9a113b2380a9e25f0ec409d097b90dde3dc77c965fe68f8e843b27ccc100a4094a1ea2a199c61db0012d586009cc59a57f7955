{"_id": "@rollup/rollup-linux-x64-musl", "_rev": "153-3104707a5d8197492117fbe29c28546d", "name": "@rollup/rollup-linux-x64-musl", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "180459c2e823093a5e9c8b88864f06641bc6280c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-LXaUYRs00INKQQ4JlFSTPFL+7r66bvNhL3w6an/BhNdZWlO8/kI9Uxea/UaVOFGt40/zijp5dfk0muWTn0Vg9w==", "signatures": [{"sig": "MEUCIG2lvwliQtZKkw1m4w+TXPkwcGPoi2Un4wZ7ExASNGrdAiEA2ErruB4xhj8F+06RvPCULz0TIVLqVT3dmt6njQuzQw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2489308}, "libc": ["musl"], "main": "rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-1_1690865355724_0.14502496161504652", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7db20d2b30ae10bf9bee79214efe64cd72be7ea9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-t2Mi/CHSFpP1EzoZ5G0RQkucmvwbQzI75WA8BdpIuV2uWOLWJMhjBnjLN+dCzcrhQDHp0zC4Mj/C1DpB+EXEFw==", "signatures": [{"sig": "MEYCIQCpFeb41ltTLNsCHrSPVG1oTWMleaaFIaeSKGCiQ4qv7gIhAJtqtIiy/KcR/QxMuq2MpVxcubJRhtfSC4XjHYxdeLKT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2489308}, "libc": ["musl"], "main": "rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-2_1690888612190_0.8878518775584794", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5b497e9b6d0096a00c709f6a9b5634c5d82a36d8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-OPeRu1htAxMDABixG2vs51bwOgkvRMQj01VMZgiO3slGk8vef19B15K0uYD8fFhXKMZpytTUZpa/9JrQnRH8uQ==", "signatures": [{"sig": "MEYCIQCK671snK2aHlBVgoTd4HlkfRltv89OIm7HsapHRzK7ggIhAPpfNkICeF4MuarSZsxvFqwtszn2QzQJu7CyhliUQZAv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2501596}, "libc": ["musl"], "main": "rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-3_1691137036659_0.08919855875844873", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8e5b66c73b48986b6d783f6108f1b3f883d4491c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-3O0U7NeRIHYmIjphFMIjDA/+Viaf1rkDxdeGZUAiMziPORlZRgbKnjmt//XUrTB8fHRFdSSCdFFtqjuferTrWA==", "signatures": [{"sig": "MEUCIQC2ksFljh2fR9sUdzatT1D4WAURmBzf4vSC3U23NjMRoAIgJfikZ4ee8ou5JCSM1TBn2tMz+WBYzfusfQCe0ErikBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2501596}, "libc": ["musl"], "main": "rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-4_1691149024274_0.35465867680389174", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7e162ce5708a8c2875605b5bd8da3b56ac093c57", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-uufPzQ2YSE7XGfG3xTVR7tE9DA0otKfoI7iqEDvdqySbuf+fOwr92X6UajHognaRXZ8/u0fYPqPTaDcztsTQcQ==", "signatures": [{"sig": "MEYCIQDB5y5Uj0h9hnTR4dF5e/GFW+PvYL9V1RuGQFgj2r7V2gIhAORxj9OBU7tYgajEk4IyHCyK/kU1yw6XQYew8nLlKEM4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2890831}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-5_1692514629666_0.7237644897465956", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d5535e038956d07500a4f17c71f0ac3e81dc3cee", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-/lQXgJlvrPJ77Ev6R8IUc2OY6UDYetsZzVwdD0WIbAkJnPyeGTgwkR6gIzLEWg/aMtkvWkxwl4em93rhps6cuA==", "signatures": [{"sig": "MEUCIB+Bfw5UeN9w64VrPzYEEaf5qdrdEwTcFKMkpvso3DLjAiEAivMVyhFJ8/YrYWf0oBbbgPhMCKwX9EKFkF6uNOeABew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2890831}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-6_1692517917083_0.5756815184936415", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4ec827c412f789e77e0f7ef868b75d5c3c43de84", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-4yWRCz6sEAXhiMjh8+Wa5FSlePhuvcmm0yTf3rEMV+GrMSsrfMuPkm4q8brIWCn6Sc8OwcrTjrV4QORtNAmZiw==", "signatures": [{"sig": "MEUCIQDVAh4TH1D+huxiQjzDj33sQtEV8XR9yrQl/29gNdq5pgIgJ9hAbPov00txyuGY2G+kS0332OmQZvjH3C0BWAjjlKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8345753}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-7_1692527643975_0.7957495572792683", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7d39292f4747193fd80f73fd658f4ad47d2128e5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-waYg0v3qEkWoiOlQ1ze/D3gXu+ew2w0VpB6pJDseQUrsafWnWsJ6HF12chY328LVmhQpMenKE9skrVAZSTSmaA==", "signatures": [{"sig": "MEUCIQD+F7vStCM2vtIHUz0YjHPBzChCCd2GqmuJjGr71dVHhQIgS6ZSK0FEI3ENVYbvkC3tgwzDSrjJL9a9osv6fXjqz8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8345753}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-8_1692530576144_0.37107176086977556", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "875d884fd72b3a4ff84fbbd004ebc2eb55c55ccf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-qKFry94A1W01WCfPYYpUV03Y/wC9m116KmiRWOom0sEmccSxSqznq/YveEXes6DBGNgiv9oE+Lj8crCjVhWQVg==", "signatures": [{"sig": "MEUCIBPFLHGikvfas66pJvsQ88ZsOwZVmWI7iVjj+QJkTETrAiEAgh5bRexEOwrwsHsrsYRWAd3achaFu62ZBfT+14PARbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8345753}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-9_1692541781337_0.3483463037475012", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "54e89c7ef7c9439ecc0295dc6df2ebdf3d807057", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-tmxFiKXCFITv8JxxPNXLD3bh7ysVs6+UMRMkt+UIkdr0zsPb5iWRa9VdrZNXJ60qKqx7Bc/B0sJknkOMpmyI0A==", "signatures": [{"sig": "MEQCIDcFE06NEHKJs3Sis0F+deUdW1aiO6qC5WZXGjl11VUVAiBmxItnGEVv6NqA1vUThbWbvHNiYq4SPK0UIsrdb0AP+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8330101}, "libc": ["musl"], "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-x64-musl.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-10_1692631839623_0.518254267696912", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "532a460e498fe4ee20f41b5b9e895ac43027a5ce", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-uLNsFgC5E5qpQFem4CiWpJIJOQt5+zBxt6kQ1rpfvkTOwF6G0AEGXrQCUpj+PSlZw0jopn/atXRyHxlq+n4cUw==", "signatures": [{"sig": "MEQCID9pNSb7OptDGJOzKgBbDfk5gei5LQfyIopZ48Wh5SVYAiBu4p9qHSfsMjEhAp/SB3cPMA28oWaFAIQ669MDfZLYTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2878432}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-11_1692785779727_0.9618058155364193", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a1ecbf393fe4cf2f45f034ed198bf83a323bc9a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-jUbA8aVs/fA4bNumpIduY7Aom8pE3lVCHnWY2dZFqveZ+nGcAbjPkzT399xW4SLU4rXwAjPNNJcyRBw9qkI1Mw==", "signatures": [{"sig": "MEYCIQDdvoz3ORdCmXW9f+3vHY4S9JY2Q/KtdWCh6gDUesSw+gIhAKJnE6whr9RGBYqBUM9pyromIhnXPDX5BR3arQ0mlGgF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2878432}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-12_1692801675876_0.07832505123962208", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b13a94b945398189d132d2f15d537fb614693dcf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-8WOBUNEvAtdytl1y84J3UfLtj4pGGUr1T44ViqbfjljSVLGY0lqpa8GSLp5cKbf8RxNp4UkNqr/RKzY+i1VR5w==", "signatures": [{"sig": "MEUCID+AZdc3GNRRwAOO1mOumEN1mviaQdTbZjFmeS+uIIaFAiEA67GbfVzptbCuNovoIVw4/TgruocDSNmvj46Y4zHZ4Rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2890720}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-13_1692892135161_0.021810425856588767", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "92b981cf0a0c1dda0c07cb450a768266f64611d4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-zf+ASz9eej6Hf8btBaJ4fsPA5kfD9qCJsLokrSUV8okYb7TUSfVyQvZzjv29arvRc5sXqXqacEzKqoNQYC30ng==", "signatures": [{"sig": "MEQCIBIXqEEuB1qKH+jpVwMmmgX6i/WU/5t3XmD90dHH0EObAiBZkfLkX1mnjpFTWkG38znskilrEPiX/dmDaK2Ge3cByw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-14_1694781286018_0.18102590229756288", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "afe8f2dd6e4044080b7f88243e07411c4a2a6488", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-C6kDsACMyRTVVjyavjjrHpFJreUWb752o6UYSwM3KkJaaWmsCYjCq1ycQ9m04F67iXJxno0tCaexahXYML/BtQ==", "signatures": [{"sig": "MEUCIGPqgA53QhsHdzD3YzVXeb8ektxa8KhIqlqrFY3LXZcsAiEAobnUeTzuk/KMBJyJ4O5HjgpT9t2cUahFCGTMgMAMLjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-15_1694783233398_0.6544904355716694", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e05ed273a9479f86253d481d095eac0fc4de3394", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-wwek4wuQK+bvzGXuD6MRM69tF0pAeu7d5cP/hl6M1V3ctESGSR3GffiuH2yjiTrmegXZye99jS9yCLqfibNLRg==", "signatures": [{"sig": "MEUCIQDeurepeB3fWiFGPwqTfWqZwriyEKuxbObrGjM6mF3uzQIgATGmo+kXo0AxEZzRz/oN2FzDHSsC4i6Ukx8+skZaThA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-16_1694787459104_0.54464481784204", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d8c50d0c1459b3229934eba29c2b7dd0a6e14ad3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-u+sGaJsYfmmIVGzDXtsce1wx9V/j98I2SxUqpK9M8mTQ8UXdAVo+2fyxISG6KsY1+6xfegsz8SoVZxOJ04HzwA==", "signatures": [{"sig": "MEQCIE9SkTlWFL/KQXHCFOz9NyXfcKo5SDKt6PT6YTVHNdr3AiB3jkLz7w06ZVWpg1G2Lpg2T890KdgHtSs3FEuGZRdWCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-17_1694789968463_0.829064145373448", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cda2d7fe4979378715c9f3b04b442c1dbcaeee58", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-lNsoKXVDTPzG8F4nFTDLPf+0Trh+bOAFeLZBU6SztX65l6D4anOzj8YNY5kEpyQ8kJCLpZbSG1jgPGM19XWL1g==", "signatures": [{"sig": "MEQCIEMDKDW+w8oPwlqkFZlevlzEHwNXsqlruY1UuB5QOjCHAiAiP0TSoKrdHYQ+pp8dnMvhVKH31l3ck24DUVwOYEWL2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-18_1694794269061_0.05843230597278537", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "effa4dfe19b691622f9b952302508f922ce2ad3e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-T1IdnCbV0P/sHR/qk6YMXPYZ6qroOPS/Cah8OMJfHgZntvkxAnB7JHm0H7rCnohDFuW0NJTGupUZyrWhlIvagg==", "signatures": [{"sig": "MEUCIHjZWd/uIozruH0M6n6KWVkX8ou7I8H/p2Qwfli7Yaz+AiEA82BJP6oqznXyXJUULQzTjiZHD+eYt/WKXlTVXEb2Jzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2874336}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-19_1694803879287_0.09215113027035637", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9cb5c887d17eea15572d3de818a7352eb84c7fb6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-D8SFkt3ipxWL/xgn2exE5rMRmby0byxyuMXzq1i6XlRQ9z8d/SUtToJAPhzIAA24GyLDOjDcDVdWuAruLzQ31w==", "signatures": [{"sig": "MEUCIBkiG22pj5JlzOtNVK9zHnWauKhnxlbqfNjfTJDTreEOAiEAjuheLXymkoZjdh8ToeXNVzZSZT3Lrflfhl80joz/RRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870102}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-20_1695535859024_0.6486138954417668", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d43c52adc79333723addb9ca6b8eb571c24528ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-XsNpr97FQc0tf9a+4OA6Z7YsW/l0Uo8fRS0splU/TF1bF6SPxQr1tajMdPRb/Whsh+79AF7rVyxKdzU7iXkqEQ==", "signatures": [{"sig": "MEYCIQDJD+WIYZF3GYG7pogXtCo0m8atOuhKlfU/iI6MlzdbZwIhAI2YYtfR7VT2OJn0a+o1a47w3os90W9s0MAmnlAinJx6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870102}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-21_1695576165520_0.4008453898011868", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a2d1fe311f082adb8425244e38133a33092a66aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-BrMyDeI+rxGrcj1gAe5zTiF22hsQpXEt0bvKp2zTsQDHe56mn1GLBXvJaT/401zfE94M9ditDdt337rx540gFw==", "signatures": [{"sig": "MEUCIDVuyUoBEQpoSc0wiVD7SW0kTjKIHK+xC5rLeNORWSXFAiEA+MaFzh7zKFtjCslq2zXN9XUDlX3SjCVtAXtt8XHWe1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870102}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-22_1695745078698_0.01900946654245006", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7823a68878415cd7c22f2ccc816716920e1b2d35", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-LKpQT5+PsXrOUyeuVVvPC1PC0lhSNeBYPF6Q9FgjYosxE+cdFSisU+rCxkRJwYX8fXmcK8/vuMkqPpUP6xCuog==", "signatures": [{"sig": "MEQCIFOLAx+lj3e7sFSpxbIDqtCUi8san8yComzzD1CloGJ5AiA4Z00D4QYfwD7qhvl2t5tR24xgGTZF3ZI7w9/7I5ZxXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870102}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-23_1695759289499_0.576452608583534", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4e860c5aa9da3918caa18d553aad3c73e9022b13", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-0y+oXnCCT5+U5V58bY7dy65yDrWWfopFJwtC2EbFeA9SHrVjG36/TQo535ML3zdFwO+fma8r5FP1os0psbQBXQ==", "signatures": [{"sig": "MEUCIQCUxc1mhmVUnvInnfTfRNUuhevD7jGoOiqAi+Xzn2LVBwIgPv4neoUn5QQvVqiBgVI0fkWdVNJt9k7gKH1c3TGRNJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2898774}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-24_1696309990359_0.3943374855408852", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2243fdf2d826ea58d44174ccd85bc5afbb6f5182", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-f/gelNCe4Y8MKg0uAHp+RlTqqB7MoU13rfoSrODqYU6nXsruhiFPPSJFMO1QIOmNODbSWow1p5YRaqsq+HMWvA==", "signatures": [{"sig": "MEYCIQCbtQ2bmwaYkj4O1grefnnR/HtPJ+2ckMQr8J7u9VoFYgIhAOSWsDeoEx+r6iv9MVk9kKgGLGuCecbSUMLikV+HzRZS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2886486}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0-25_1696515219484_0.15951301777491267", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "fca20e521a477b0e4eabdd2d649ef640762f667b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-Xs2tOshU5MD7nK5WnaSBUwiFdBlMtyKdXOOnBno4IRbDIyrjLtx9lnSIO47FNP0LtpGfyOcsK/lE/ZsLlnXyIg==", "signatures": [{"sig": "MEUCIASB/qLKIxKEPdHllYHmq7kFc6TdnjOTSLN0ZLNca4ugAiEAq/QFc5mRDxDIaE85G7G0XhdEaZYodquFLnhIAkdRlVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2886483}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.0_1696518917502_0.874752548035288", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "beef57eb5ae8249b5874ec9655c9b87afcabed02", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON><PERSON>duwEBd4x3JprMc1CBZUeagX5EwwCMUx2mN7BNkoWbHrN3e0RDss1T7X9m2gmXsEl5fdPxTTgUo9t3h2V7IQ==", "signatures": [{"sig": "MEUCIQDei+grBZMlVJfXOSw9Um9SOloTTZJo+5WvC39RhsGrDwIgBYtDGyg5MoVkJkf6ofQ1PSmYwq3/TZdtaFDZqgQCA9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870099}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.1_1696595827140_0.6098202787295774", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "88246776c2b45b55e5d56a070eac592d10280d59", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-jJRU9TyUD/iMqjf8aLAp7XiN3pIj5v6Qcu+cdzBfVTKDD0Fvua4oUoK8eVJ9ZuKBEQKt3WdlcwJXFkpmMLk6kg==", "signatures": [{"sig": "MEQCIHbKw357rlVKv5Z/acQVbvk8cQ7V6GjbEVveyRknhY1cAiBvwjf4oQN0l2UojzqGuF2ce78dxOzMPi9Zfd4xpcPD3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2870099}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.0.2_1696601948283_0.10507542880056464", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "241c84e3adf085173b2d7084b1e7c7a345197981", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON><PERSON>iucXLZSHJnRKfQ6xgz0SjL0TlXr76kCwpr2ULQRxbvvfEm4Z0HM+DEgE1R6OthSqPxnxNqT2IACkQ6yvGSw==", "signatures": [{"sig": "MEQCIBNnJ2JBELReRT2IxrpL4uOE/xy0p80CtcIi3yxdvEimAiBTUy90wnDnirBv3dd/0+n8ffygWmXGWYa0+pfzpqSEUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2929435}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.0_1697262757099_0.6318778969373953", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4274161500949caeebc61d911c5462b1000ad8ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-fkOj14x8fAaBZRQ2VvEF665oQXcgGKsrAXpfo0Bl/zrKNda56WQfsozPzH7+qaJXMTNt3ylc1Z18SpqEvi5w7g==", "signatures": [{"sig": "MEUCICMej9OfGu2MOTXV63bj0TYgL5G8cg04DmkcFrmbVv81AiEA8NrmepBjBZxUWkE57D5UpX3yROFXd1iJqQBy3AlpUIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3040427}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.1_1697351531518_0.6041090323018845", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ca007b4c41e9910a159ad199b0058d0f05519c25", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-GNATGqh1xMSIwFt3A7co3zyw8hMlUs8E+cBe9Hank0L32hpDYavoPqu7Uat74es+eDqrGn0szfm08ZjYf/ApCQ==", "signatures": [{"sig": "MEQCIANkYepKYhuxl3g6PYFhKt0FRMWN9+OZjYbeE6q0pU9GAiAYsf0nqNIchaxa1VUjZJVlWY+6mMZ80rvoBwAlQzCxmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3040427}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.3_1697392129870_0.839648229912682", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6f356e16b275287f61c61ce8b9e1718fc5b24d4c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-/t6C6niEQTqmQTVTD9TDwUzxG91Mlk69/v0qodIPUnjjB3wR4UA3klg+orR2SU3Ux2Cgf2pWPL9utK80/1ek8g==", "signatures": [{"sig": "MEYCIQCSGU8BlByhzcZCkWNaU7QZaUyU0Yn/Vpt9tffTZ8n/aAIhAIG/GcTFeKKsk8YEhlJrkDyBRG4kHpzm2ubPGuT8FSU8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024043}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.4_1697430872602_0.7317193662997785", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "fcd449c2838c61c2c320ae8de717961dc5c98d1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-36p+nMcSxjAEzfU47+by102HolUtf/EfgBAidocTKAofJMTqG5QD50qzaFLk4QO+z7Qvg4qd0wr99jGAwnKOig==", "signatures": [{"sig": "MEQCIByaokVKJYDYz868pcd5kVbB5ubO74ueVY7ZAg6jCXPDAiAL/XxYNBGs6MILvHmnNU6G+wbsHkKzxY1tjXeBLwiLkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024043}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.5_1698485036075_0.38145546116422424", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8da7d3cb43eecb6ad8406dbcc4028d72a73ed700", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-VZNQTK+8+HiypfMV7mv8htJwIQ69dCzgyn3blQUlvMGxQcUwc5bu489X+Y7dNhKP6x4LopF4+CCiiexhi7ruNQ==", "signatures": [{"sig": "MEUCIFg+8GdC+8DO6rcefMpMBmr8Ey2nVVrb0d+57PP7qomUAiEAkop4fOlvBXaDe8+NebnmxQJvv8W80jvUfmqJY9VsAPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024043}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.1.6_1698731138941_0.3963369036480646", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e5d4f4529142ba7873071d4661d37eb50e211a45", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-atih7IF/reUZe4LBLC5Izd44hth2tfDIG8LaPp4/cQXdHh9jabcZEvIeRPrpDq0i/Uu487Qu5gl5KwyAnWajnw==", "signatures": [{"sig": "MEUCIQD9GTi3FR1YPeVL4VivskkvgecDFcIuf/8wMfE4Rk3ANQIgEkgk+Nn4OexsVYwPBDSFq+M2ojjrXO+n/2QuwBJRnPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3044523}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.2.0_1698739866216_0.18927856510078067", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "89479dce5e5bf6850fbca92fa7f1637ddd70c9ef", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-tJs7v2MnV2F8w6X1UpPHl/43OfxjUy9SuJ2ZPoxn79v9vYteChVYO/ueLHCpRMmyTUIVML3N9z4azl9ENH8Xxg==", "signatures": [{"sig": "MEUCIQD5CakF9Ssel4QxNAyFHTTZgxHK4/uLYSW5qt2FZ625/QIgFXBdzoNtsfPpXbIaGdEA2larbNpm4RlEITA/xFBNYn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3036331}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.3.0_1699042411001_0.5893830199124359", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ef79a281c93716741e9721fa393328f823565c10", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-5i71ndo6vZ/EaYpWV8h0TypEc5lCmPru6hST35XiTzV9XUtvbLDfbD2T3nSU5MeQMZVgQHCHXelsH3KCGTA8WA==", "signatures": [{"sig": "MEYCIQChO6DNHQ1ef4uxvOfmTW6qRCjtHTFlizxpmDmcufaEKgIhAP+bn8SYJR8iiuRmZ3kO/MeeohsEb8TClNGe7kgFLwsv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3046859}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.3.1_1699689495685_0.8344255336614108", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a0d66ded3b3a9581702cfd20440fd986d56d5ec5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-3g6jaXxXVFaDnFoMn2+E3ludGcXFfEr6lDn+S1lh9Qe0JcL9sPt1wGh0g2cKIlb6OakNOFopZqJ5Yub9F7gQlA==", "signatures": [{"sig": "MEQCIG+9QXWHW6JjZdPMUgKOeL6WL+vjKChWS9P+KQLLVGt6AiBdBfaz0nH4ibFpD8qq/A1tblmfF047taw52kwhODacaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641355}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.4.0_1699775413110_0.2665026874038905", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3e5da42626672e2d620ed12746158b0cf6143b23", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-tB+RZuDi3zxFx7vDrjTNGVLu2KNyzYv+UY8jz7e4TMEoAj7iEt8Qk6xVu6mo3pgjnsHj6jnq3uuRsHp97DLwOA==", "signatures": [{"sig": "MEUCIQDAFvZ3aVG6zVrtIQTKMIz1exQ3dRLR5IhydyjUM6aAAAIgJdTTLhtKfONp1t2W+sv+IUSk//l9bLfO+N/Z9OVKmis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641355}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.4.1_1699939571899_0.4800986901930502", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a17f5decabf05b74aad684de56cf43a72a289a0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-eBPYl2sLpH/o8qbSz6vPwWlDyThnQjJfcDOGFbNjmjb44XKC1F5dQfakOsADRVrXCNzM6ZsSIPDG5dc6HHLNFg==", "signatures": [{"sig": "MEUCIQDP27JZuSeGlPoV5Kc+mj6Y35WSZskKVmDNbm7RckF+hAIgcdv0s8ZT03tZZzflIJxPR/+9C+pNRF5rgG6bCxAf/Ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645451}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.5.0_1700286751734_0.030963821999995478", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9cc8c0ea1c0e0d3b18888d5b2fd51ef6c9b42481", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-ZRETMFA0uVukUC9u31Ed1nx++29073goCxZtmZARwk5aF/ltuENaeTtRVsSQzFlzdd4J6L3qUm+EW8cbGt0CKQ==", "signatures": [{"sig": "MEUCIQCni965mw37BkdsCzZ6vjmblIZBjQNErPP5a98Vr+oEIgIgKPWjSM//Wx7s09lLbwT4cd7nKDFVYL2AGauPSMSyBHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645451}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.5.1_1700597610090_0.3423029762426768", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d5dcc18af0388209e11586d3c0c9626ba7577b04", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-/f0Q6Sc+Vw54Ws6N8fxaEe4R7at3b8pFyv+O/F2VaQ4hODUJcRUcCBJh6zuqtgQQt7w845VTkGLFgWZkP3tUoQ==", "signatures": [{"sig": "MEUCIHlqB3xW/8j5XgJO4gdl3z71IXGTcPRGCCAWDObc9Uu0AiEAyTcbrC0Jn9s7+LR5ulAU6AC93hzmfGgnQrRsdsew8yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649547}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.5.2_1700807410912_0.16198886338318919", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "a4c7f5e0c363b2c34f6a7566b1c9da00bf0b96d0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-Bbm+fyn3S6u51urfj3YnqBXg5vI2jQPncRRELaucmhBVyZkbWClQ1fEsRmdnCPpQOQfkpg9gZArvtMVkOMsh1w==", "signatures": [{"sig": "MEQCIFhfimEZcc5BZuWCjz+CsQBInK8Vqh0WPFfQgWnPKeu0AiB718g5RVqlOolYuepwof7e8VU6PWgHU/Fl8iJZB4sssw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649547}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.6.0_1701005973298_0.9062612210719201", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "357a34fdbf410af88ce48bd802bea6462bb9a8bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-RkJVNVRM+piYy87HrKmhbexCHg3A6Z6MU0W9GHnJwBQNBeyhCJG9KDce4SAMdicQnpURggSvtbGo9xAWOfSvIQ==", "signatures": [{"sig": "MEUCIQCJi0duoWERjY7odh0k1fZPvVzvnixB+ApeT/rU7G6JRQIgIWdFCr5FHYY7FOKn65vmbPyPDFVSWDZ83vdGK+uIAAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649547}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.6.1_1701321812153_0.6603142003148179", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "554315d4f252f9f324da587fbf5048aaaf1da12e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-R2oBf2p/Arc1m+tWmiWbpHBjEcJnHVnv6bsypu4tcKdrYTpDfl1UT9qTyfkIL1iiii5D4WHxUHCg5X0pzqmxFg==", "signatures": [{"sig": "MEYCIQDJ5YCaokoTKt7nX9/70t+0n9FBSwLLw8bGaz3mYHPxmwIhAPPRVKUSq5IauTio+OHamyAZQ50e9oBySmPgrgN9FyuI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624971}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.7.0_1702022304702_0.972581858460773", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "df8d0966b02d1bdc6447b5eb58fa18666da1f3e8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-mdxnlW2QUzXwY+95TuxZ+CurrhgrPAMveDWI97EQlA9bfhR8tw3Pt7SUlc/eSlCNxlWktpmT//EAA8UfCHOyXg==", "signatures": [{"sig": "MEQCIB7244aoscHCoaqMyHZh7RWyaMQjq++kdbWqetT0q88+AiBM1LMJCt+YNKGTy44MWTLaElsZ0N66ZOf/JBBTmxkqEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624971}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.8.0_1702275919695_0.9245475344355321", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2d2dbdf5fbf2c19d1f3d31b8a7850b57f5799037", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-VFAC1RDRSbU3iOF98X42KaVicAfKf0m0OvIu8dbnqhTe26Kh6Ym9JrDulz7Hbk7/9zGc41JkV02g+p3BivOdAg==", "signatures": [{"sig": "MEUCIHRQl+YdbMfO9sEWFD8Fy8MQNcBFbA8XiC/vN9QKcnMcAiEAyZAb9GMcNs20mpoOG/qgSymct3ujBg9dYxkEXZl2ZD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2624971}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.0_1702459479294_0.18011293795346073", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "eb7494ebc5199cbd2e5c38c2b8acbe2603f35e03", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-t4QSR7gN+OEZLG0MiCgPqMWZGwmeHhsM4AkegJ0Kiy6TnJ9vZ8dEIwHw1LcZKhbHxTY32hp9eVCMdR3/I8MGRw==", "signatures": [{"sig": "MEUCIA5vmjsqsxOWctXqU1gYgTkmkExRdGTiBl9I/wRit24bAiEAnjpqEgO+FHR9q+PR9HyuE1UgJwarpu56xh2tuedDGOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641355}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.1_1702794392020_0.6358032526946149", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c15b26b86827f75977bf59ebd41ce5d788713936", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-m0hYELHGXdYx64D6IDDg/1vOJEaiV8f1G/iO+tejvRCJNSwK4jJ15e38JQy5Q6dGkn1M/9KcyEOwqmlZ2kqaZg==", "signatures": [{"sig": "MEUCICWl/J5okdSYGUOwMGaBIYX2uXp8jQauNJ03kvpIDJ3PAiEAl+MIQxGN077jz5mo6IWveDehOtaMc8x+kGH+aNnyc3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637259}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.2_1703917426770_0.942943380953551", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ed47ca1976f01d49457494f9e3fd62585d0e903c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-0OAkQ4HBp+JO2ip2Lgt/ShlrveOMzyhwt2D0KvqH28jFPqfZco28KSq76zymZwmU+F6GRojdxtQMJiNSXKNzeA==", "signatures": [{"sig": "MEQCIGxO70zd/PwymUYqirwgVcLkWrSWoCoX8qFI7ElvEsfAAiBXH2DwCLSTsvUW+gZygG6CBP+4ZpEMIrLVFAPBeVz2bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637339}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.3_1704435667419_0.03616388341355292", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "25b3bede85d86438ce28cc642842d10d867d40e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-RoaYxjdHQ5TPjaPrLsfKqR3pakMr3JGqZ+jZM0zP2IkDtsGa4CqYaWSfQmZVgFUCgLrTnzX+cnHS3nfl+kB6ZQ==", "signatures": [{"sig": "MEYCIQCg2sF2kc6PWLXqKG+Qd7hcl2/taB/CzJpmRmhzh2X6XAIhAMGciABPZY4kbkfRLWetDh++jhA2PLcLBlkcKHXGCxk9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637339}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.4_1704523161136_0.9840509844010574", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "fe0b20f9749a60eb1df43d20effa96c756ddcbd4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-ezyFUOwldYpj7AbkwyW9AJ203peub81CaAIVvckdkyH8EvhEIoKzaMFJj0G4qYJ5sw3BpqhFrsCc30t54HV8vg==", "signatures": [{"sig": "MEQCIHdBYxoyRNCgQAjI28tsegGvrQ6HBKyTOQeDasYsq/YDAiA1X2Tv94ycW9+1KPxQjPJ1NThWVxA4W5P3Tfq05AqpzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2637339}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.5_1705040193715_0.10807725524195799", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bafa759ab43e8eab9edf242a8259ffb4f2a57a5d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-ch7M+9Tr5R4FK40FHQk8VnML0Szi2KRujUgHXd/HjuH9ifH72GUmw6lStZBo3c3GB82vHa0ZoUfjfcM7JiiMrQ==", "signatures": [{"sig": "MEQCIElKCvHAip0dSg85P3dUVsQUcc/kNvg6k5zUCDNkn1xDAiBkdrzLyFrzrbTYrZshEy4xPo+3Tkgq9BhBLpKhLVL5Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2629147}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.9.6_1705816359201_0.048303497238768145", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e4ac9b27041c83d7faab6205f62763103eb317ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-q/meftEe3QlwQiGYxD9rWwB21DoKQ9Q8wA40of/of6yGHhZuGfZO0c3WYkN9dNlopHlNT3mf5BPsUSxoPuVQaw==", "signatures": [{"sig": "MEYCIQCeV0dMHLhIBCea/IRY6PUPaSSPDRKmmhcJeSuPU/17WAIhAKC/YehDU1UW/KWLzeFQ8d75/F3oGkTm4won4FBpBHfx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727452}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.10.0_1707544743453_0.7753181503958306", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "132cf0787c966b3b22cf4475706fc4cb08ec880c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-vIAQUmXeMLmaDN78HSE4Kh6xqof2e3TJUKr+LPqXWU4NYNON0MDN9h2+t4KHrPAQNmU3w1GxBQ/n01PaWFwa5w==", "signatures": [{"sig": "MEUCIQClN2cOzlc2vcKsSUgQ12T+Aj67W6va6zJX5Ozy8PiS4QIgGYP0F1V6coMZL6kwCseegsbjgiZ8AmuQ4znwEqcU6M0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727452}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.11.0_1707977412680_0.8012862702436672", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "50e8167e28b33c977c1f813def2b2074d1435e05", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-LfFdRhNnW0zdMvdCb5FNuWlls2WbbSridJvxOvYWgSBOYZtgBfW9UGNJG//rwMqTX1xQE9BAodvMH9tAusKDUw==", "signatures": [{"sig": "MEYCIQD71ategBGFmO0GVQUFCVFsmuEipF1RaitLtMRQxqcYiwIhAOnKhzP6ghYmTeToumFG7dvdejhcCcf/ZYZNw3t41vWt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727452}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.12.0_1708090390570_0.21122244740699747", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3f000b5a92a32b844e385e1166979c87882930a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-7/XVZqgBby2qp/cO0TQ8uJK+9xnSdJ9ct6gSDdEr4MfABrjTyrW6Bau7HQ73a2a5tPB7hno49A0y1jhWGDN9OQ==", "signatures": [{"sig": "MEQCICokB47s4RmhTFMi66ocA0LQ1u5PoW2/lgcoAkRd0KE4AiA7ghyUH7EiUgHj0w8IauapfrZIFC7QgCedRRYIG541wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2702876}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.12.1_1709705044788_0.514509761154244", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d2f34b1b157f3e7f13925bca3288192a66755a89", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-9RyNqoFNdF0vu/qqX63fKotBh43fJQeYC98hCaf89DYQpv+xu0D8QFSOS0biA7cGuqJFOc1bJ+m2rhhsKcw1hw==", "signatures": [{"sig": "MEUCIQDeRbyu7KTrcXUUjX6SiU4uwk4e6BMcMkJcpVGhPJgxVwIgLZ/NDFfSZ8Y7ACLWo96w6WreI0EcDkx/fEsq4Da9H+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2735644}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.13.0_1710221351157_0.49406980549163726", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8cb70ef3d75a20086dda14a2a6c872bc372e76f8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-NyNbypsFurjrZCc67luLniRqaiDv2osGOEKMLqCUSkt/gs1e5oOce5Q6kXlJxDybOXjNDC8MiBHYF6BpnfS5uQ==", "signatures": [{"sig": "MEQCIHenbpeKduEn+k801L1Ukt5OfT+9MypRbJR5otAMorH7AiBwuCRe0jiZcm4YHq6Fq5uOQl5UQ+TiOE208OxasH9uHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2702878}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.13.1-1_1711265994427_0.9565281652684676", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "07e0351cc18eeef026f903189d8312833cb6bd1f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-VTk/MveyPdMFkYJJPCkYBw07KcTkGU2hLEyqYMsU4NjiOfzoaDTW9PWGRsNwiOA3qI0k/JQPjkl/4FCK1smskQ==", "signatures": [{"sig": "MEUCIQD+5qtA/mANSInKGu5cQAHUZfhbIxpDzuJkMxAa9x5gTAIgWVwkCx4iEGgzgoUrXfWsqAdU9nIrFj7LdaIsWbxOv1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2702876}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.13.1_1711535292587_0.42797690800533417", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e8dd0f3c2046acbda2934490b36552e856a3bc6a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-M/JYAWickafUijWPai4ehrjzVPKRCyDb1SLuO+ZyPfoXgeCEAlgPkNXewFZx0zcnoIe3ay4UjXIMdXQXOZXWqA==", "signatures": [{"sig": "MEUCIQCLJMr8nSpQVFPMYR8eerCe/Mdw5mLqzVMtU/QCqTUZQwIgcv14d6pmue2n9YF/RU/+bIWTUbwut3dhC/P27uHBPRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2702876}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.13.2_1711635257333_0.7024313424148261", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ca74f22e125efbe94c1148d989ef93329b464443", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-i0QwbHYfnOMYsBEyjxcwGu5SMIi9sImDVjDg087hpzXqhBSosxkE7gyIYFHgfFl4mr7RrXksIBZ4DoLoP4FhJg==", "signatures": [{"sig": "MEUCIQDFK49C7cAwkt+G0TtCeAmYw+TI/HwVdI3An+Lqct7IowIgBB27E4FNuevqL/XP6WxryDE9am92Yl3GnghbGIW9vYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711068}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.14.0_1712121818557_0.5322359578889853", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "38f0a37ca5015eb07dff86a1b6f94279c179f4ed", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-JNEG/Ti55413SsreTguSx0LOVKX902OfXIKVg+TCXO6Gjans/k9O6ww9q3oLGjNDaTLxM+IHFMeXy/0RXL5R/g==", "signatures": [{"sig": "MEQCIGXL2hUSBysevZTHf89TiiFm4I1fg+xZ5YkIk1s3HdOtAiBSU1dMhTPWSyzW/CbkyIsy1SrdsvMl0vtZodHYweC5tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2698780}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.14.1_1712475366123_0.6057500944180796", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "85dcd3f549c2fdbcf1cb1f1b5f501933ed590880", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-8zfsQRQGH23O6qazZSFY5jP5gt4cFvRuKTpuBsC1ZnSWxV8ZKQpPqOZIUtdfMOugCcBvFGRa1pDC/tkf19EgBw==", "signatures": [{"sig": "MEQCIFrJ+e/fekOa6HCJkjxsmDRpdkkvGT+WnAUJIAHk2Yn0AiAMQxnNZHL86+dbsd3Gi1N6mtKBMMqSM7fD7KB+ezBT9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711068}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.14.2_1712903047552_0.052855844801883656", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5cc0522f4942f2df625e9bfb6fb02c6580ffbce6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-s+xf1I46trOY10OqAtZ5Rm6lzHre/UiLA1J2uOhCFXWkbZrJRkYBPO6FhvGfHmdtQ3Bx793MNa7LvoWFAm93bg==", "signatures": [{"sig": "MEQCIEkGfNfJ+tSEgWejhz70tQrJlKRoziTWLF3aaLHXT6s0AiBxBPAJtNNcItw1rWuMEJJvnnuwY1/TC4E2/Zd+o4A9yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2698780}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.14.3_1713165539930_0.8203685994306884", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "145745e339e282c7afc36142bd5a3f9495c7c681", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-4qKKGTDIv2bQZ+afhPWqPL+94+dLtk4lw1iwbcylKlLNqQ/Yyjof2CFYBxf6npiDzPV+zf4EWRiHb26/4Vsm9w==", "signatures": [{"sig": "MEQCICLl4l9D06+ZsTTmZCTdrFzlu0FBSSTcRn0PHHkfwcogAiB+7N25ULircRxNz8GUeRiC+rDjY72VVZjpK9791tAm0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.15.0_1713591467326_0.9409318791594228", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "524046b68de9ccc938245628bb75ef6f07d515c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-YKCs7ghJZ5po6/qgfONiXyFKOKcTK4Kerzk/Kc89QK0JT94Qg4NurL+3Y3rZh5am2tu1OlvHPpBHQNBE8cFgJQ==", "signatures": [{"sig": "MEUCIQCC3xOEQWK7HvbiC7LvFA9pH2pihKXp9JCCfHJLvFERWQIgQrrcCMM0iRTvw30nzMGNa3WO9+rUGquQbMYm7z7fKvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.16.0_1713674584809_0.7032153769068417", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "20235632e2be4689d663aadaceaaf90df03b1a33", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-5ICeMxqg66FrOA2AbnBQ2TJVxfvZsKLxmof0ibvPLaYtbsJqnTUtJOofgWb46Gjd4uZcA4rdsp4JCxegzQPqCg==", "signatures": [{"sig": "MEUCIHAIk/AFU3XKsg+PO9RYPPagP0g5GWWvAp1yHZnMxPJVAiEA1J4nEiWot4WIoXR5ZPx5ZBlDTtqOaBwA2GyeegauQr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.16.1_1713724237498_0.5764422213698741", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d4fd52a28d5ce4aaed436311d89a9a1eaff87c2d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-/2VWEBG6mKbS2itm7hzPwhIPaxfZh/KLWrYg20pCRLHhNFtF+epLgcBtwy3m07bl/k86Q3PFRAf2cX+VbZbwzQ==", "signatures": [{"sig": "MEUCID3wQHDkivBBlKd/lXJzWnRDnmd88TEnf/fCPf9BilSrAiEA0JLGE7gYVgYtLhRSLSsgaroj6O2idI/IYkSVh7m06no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.16.2_1713799194942_0.7470452916570234", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "51f5db1d3ff1a41f809f4877073a0318f0d733b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-guO/4N1884ig2AzTKPc6qA7OTnFMUEg/X2wiesywRO1eRD7FzHiaiTQQOLFmnUXWj2pgQXIT1g5g3e2RpezXcQ==", "signatures": [{"sig": "MEUCIQCq5KyW1PUWT7SO40JRc/9wgL9y5qC8mMmUJMjSvumiCAIgW7+slaWOTBF7J3jjahz3LNuhCL4A7KfJaby2JewdwAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.16.3_1713849183177_0.1603795619435413", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "03a5831a9c0d05877b94653b5ddd3020d3c6fb06", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-ch86i7KkJKkLybDP2AtySFTRi5fM3KXp0PnHocHuJMdZwu7BuyIKi35BE9guMlmTpwwBTB3ljHj9IQXnTCD0vA==", "signatures": [{"sig": "MEQCICxC6chJHUw5zh58xz5YdyFfsXpayqPmjfl/asDRzR5uAiAp9wRY3oUkWu19vTRY038h1hFfXPNgqX08rAYs9nLkzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2784796}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.16.4_1713878135401_0.38545680976083707", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6a96d08c563cb9b90ce806ebcb218518872ec408", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-OUhkSdpM5ofVlVU2k4CwVubYwiwu1a4jYWPpubzN7Vzao73GoPBowHcCfaRSFRz1SszJ3HIsk3dZYk4kzbqjgw==", "signatures": [{"sig": "MEUCIQDmEN8TJNNQTRGFgRnVQr7FUIdJM8AJxtMN/NxtBtfkggIgP9fCS258khIH/dG22XpUTclXSN1IWJwxvb99zw9ySfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2780700}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.17.0_1714217420696_0.9571082543416343", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9d8f4c016f587bab6a1c21fbb966fdb4d076bbb9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-KoB4fyKXTR+wYENkIG3fFF+5G6N4GFvzYx8Jax8BR4vmddtuqSb5oQmYu2Uu067vT/Fod7gxeQYKupm8gAcMSQ==", "signatures": [{"sig": "MEQCIDOt0quobiJ9Ak4wn1xJpW7QIq0rseGOiu8q9/WVwTFMAiABHcKP5/SzFpsMBbv56VQpjPNQtYPlVr7dfNdgdkw7fQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2780700}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.17.1_1714366701567_0.9050945437362143", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9254019cc4baac35800991315d133cc9fd1bf385", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-h1+yTWeYbRdAyJ/jMiVw0l6fOOm/0D1vNLui9iPuqgRGnXA0u21gAqOyB5iHjlM9MMfNOm9RHCQ7zLIzT0x11Q==", "signatures": [{"sig": "MEUCIApzVyBRSeOJpWGWAzE6sd7lpqssB3rPqmBSHCwSnTNZAiEAoq1Qx5DZsY1f0/MWZQenb9sJC5bC1j3mc8Ksk8BhhSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2780700}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.17.2_1714453283041_0.810011366864327", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f1186afc601ac4f4fc25fac4ca15ecbee3a1874d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-LKaqQL9osY/ir2geuLVvRRs+utWUNilzdE90TpyoX0eNqPzWjRm14oMEE+YLve4k/NAqCdPkGYDaDF5Sw+xBfg==", "signatures": [{"sig": "MEUCIQD9FNbP1UX36LviPHcQatSkmmDNFk66I0Aikz2KrPmbcgIgJWMtSkCKC8FZPBoUZUFKfyW7EuxBVj/yJWJku2rz4Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2788892}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.18.0_1716354256282_0.7124137612580133", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "08f12e1965d6f27d6898ff932592121cca6abc4b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-pDLkYITdYrH/9Cv/Vlj8HppDuLMDUBmgsM0+N+xLtFd18aXgM9Nyqupb/Uw+HeidhfYg2lD6CXvz6CjoVOaKjQ==", "signatures": [{"sig": "MEYCIQDB8Wv7wxtrcrprafZyQ+sOUlkPHpx6Krdf1A+u+JP7+AIhAJDMlVx7eANVl6hM4mr5C/LegWbAtyrRTfzs5xsJkGC1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2682420}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.18.1_1720452343965_0.40846937080086443", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "65da807ac66c505ad14b76f1e5976006cb67dd5f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-HxfbvfCKJe/RMYJJn0a12eiOI9OOtAUF4G6ozrFUK95BNyoJaSiBjIOHjZskTUffUrB84IPKkFG9H9nEvJGW6A==", "signatures": [{"sig": "MEQCIGdKqnZtrOSCgDUeS4OmYfEg2r80EdRIJvso2H9jJXzMAiAJZs27ze5wdS2Ho5WD8a5T5ZtXlDoOntORUxPTfS8EJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2629172}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.19.0_1721454405061_0.054403760998248396", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f973f9552744764b221128f7c3629222216ace69", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-V7cBw/cKXMfEVhpSvVZhC+iGifD6U1zJ4tbibjjN+Xi3blSXaj/rJynAkCFFQfoG6VZrAiP7uGVzL440Q6Me2Q==", "signatures": [{"sig": "MEUCIQCO+HjoLlIPtBPBO0bYIKZtjlwTE5u76C8bzTFQ3zCqXQIgKn7m2MUHUgqrKLpVQyT3GqaFw1Dt65gv/pcfIM1MYMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2629172}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.19.1_1722056069621_0.6053187393098383", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "64f0c704107e6b45b26dd8c2e1ff64246e4a1251", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-5V3mPpWkB066XZZBgSd1lwozBk7tmOkKtquyCJ6T4LN3mzKENXyBwWNQn8d0Ci81hvlBw5RoFgleVpL6aScLYg==", "signatures": [{"sig": "MEYCIQC7UqQ7ZMka1gKrIXYeUkQ6cnAAnyrEdjfp9ZE79w3yeQIhANUxjbtGtgIBPjcYAUPmHQ+CfpUo6Th24x3Efm0AF9wq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2555444}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.19.2_1722501203846_0.15267564386949184", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1507465d9056e0502a590d4c1a00b4d7b1fda370", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-hM3nhW40kBNYUkZb/r9k2FKK+/MnKglX7UYd4ZUy5DJs8/sMsIbqWK2piZtVGE3kcXVNj3B2IrUYROJMMCikNg==", "signatures": [{"sig": "MEQCIHhyPdYLTHqYIPyEfDfLHIgHSiB0fESJZcrJdAmZ5dKQAiAf7hYeW5Z/GV/Cjg5SRwIB8nve97kK+p0owJ8GvaSWEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2547252}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.20.0_1722660561438_0.7155520928444745", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "10ebb13bd4469cbad1a5d9b073bd27ec8a886200", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-1vvmgDdUSebVGXWX2lIcgRebqfQSff0hMEkLJyakQ9JQUbLDkEaMsPTLOmyccyC6IJ/l3FZuJbmrBw/u0A0uCQ==", "signatures": [{"sig": "MEYCIQCRSDguRJNE6uT89P9WUM6qJ+rfQ4B3y4Fbn/+L0WZQ5AIhAIt8/FuqUzRnQDvkCyh81BcMp9F0L2UbZ1dO7cwXN+cU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481716}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.21.0_1723960561543_0.1827052915883376", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d613147f7ac15fafe2a0b6249e8484e161ca2847", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-CbFv/WMQsSdl+bpX6rVbzR4kAjSSBuDgCqb1l4J68UYsQNalz5wOqLGYj4ZI0thGpyX5kc+LLZ9CL+kpqDovZA==", "signatures": [{"sig": "MEYCIQCHDdUz2dpcLAQzejqQbeif1F05/TUSExH84gWHZqz4twIhAKPGFXsO9BZi9h+XehGqrlourZbfvGXWMf7uYUQQBKed", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477620}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.21.1_1724687684845_0.21472159984613226", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7b7deddce240400eb87f2406a445061b4fed99a8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-7twFizNXudESmC9oneLGIUmoHiiLppz/Xs5uJQ4ShvE6234K0VB1/aJYU3f/4g7PhssLGKBVCC37uRkkOi8wjg==", "signatures": [{"sig": "MEQCIE+d5YtbedghbwPODcUZ8YDuUTlT2lPKm53BVC4+B+63AiBR55OtQa/hbi41sT9o5dMWFZe6RbXL6ikEYYhBgB0DZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469428}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.21.2_1725001495724_0.8993046265572227", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2c2412982e6c2a00a2ecac6d548ebb02f0aa6ca4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-S0Yq+xA1VEH66uiMNhijsWAafffydd2X5b77eLHfRmfLsRSpbiAWiRHV6DEpz6aOToPsgid7TI9rGd6zB1rhbg==", "signatures": [{"sig": "MEUCIEoTfzlr8LzEfciKNmCo6HLhTHxIw769IAPvwAejNa7bAiEAyUDtBqwwGJMoMlXfrgEyxlZa7QMUWLh+s5gfbeWGngQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469404}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.21.3_1726124781594_0.175325566152978", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5438b2dc38fe467444cf769146098be083022d0f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-anr1Y11uPOQrpuU8XOikY5lH4Qu94oS6j0xrulHk3NkLDq19MlX8Ng/pVipjxBJ9a2l3+F39REZYyWQFkZ4/fw==", "signatures": [{"sig": "MEYCIQDWO/D2Z61S1wz/FUrFNLtoLZqkBidsxasJCcFygmWxbAIhAPMJ/lgdh3UAia1o4oq3GPuOcYVGBB+nienp4pV/Zx4S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477596}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.0_1726721761998_0.1707334207933775", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2cee572374b9351f4ed17c543c009e295b158825", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-euksHNkKlXS9RKKHSBBPtloSEUGPg1eRVGfOkXSSIj5W9LdkMfOefsTlVf2g8kuayZW/98nIJ83Fnou9OaZNXA==", "signatures": [{"sig": "MEUCIEutM0l+dHEMaKCHz36p9ULCxxUOjq3gepEakza/T0C0AiEAuIdl0sjQ5/U+i7Bs4TB7eVZgj9oC0nNopjGVO/mqB30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477596}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.1_1726820542802_0.35386403161356217", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c280202d5b54d04f1e2b810359fe73c4973e8b72", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-sSWBVZgzwtsuG9Dxi9kjYOUu/wKW+jrbzj4Cclabqnfkot8Z3VEHcIgyenA3lLn/Fu11uDviWjhctulkhEO60g==", "signatures": [{"sig": "MEUCIQDs2Ya34SR6H7WAkpHS3MCn3uCp/8zJ7j2glbXMfXdkFQIgN/bJZRaK8NQPBzBkh1OqVFFLLM+ORVOPf95E0+YQgxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477596}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.2_1726824855775_0.04536863910285227", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bc59d1630c6baf615cc8cdcb956e13c800d2bbe2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-x12JKeTkpnbgsC1xEzFPAz5K9JtuSBqbDjmmFvXzAm4GTOzZJmXERu1Y3llDjEaZTsPXv6Wq2azAxGK2DINPcw==", "signatures": [{"sig": "MEUCIQDJqfpJ7GRe6Fetuk5K0FRKm8IRUsRKg5VZoxm74A1oXQIgBgSY8NOB9yY93vvCjyiJBPLPpVcPQfP5eRzopafuuQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477598}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.3-0_1726843710031_0.34800653544177496", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d9fe8ee9f066626c6f12a1d1006d2bb0ea3c176d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-fUL9CVfu2DLycemee7QoYHpABaLUJh5QM3naBUGlZoysaSFVvAbFju0wBHcY73IGdUb2MI2Yh8sSjIPAiUs+Kg==", "signatures": [{"sig": "MEQCIHCgC7dWz+eHK58YYBiDEi2fTyLKP7tJkqbLoVxImllsAiAhz7NU0EKYPhm+6NhjtDIGtiAaiNoKW3yhe6Fk9r20vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477596}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.3_1726895021501_0.9928833220836397", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f473f88219feb07b0b98b53a7923be716d1d182f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-UV6FZMUgePDZrFjrNGIWzDo/vABebuXBhJEqrHxrGiU6HikPy0Z3LfdtciIttEUQfuDdCn8fqh7wiFJjCNwO+g==", "signatures": [{"sig": "MEUCIQDgM5S/ew2ePT2qVDvFgJXToA66KsEuWXo/lGofL9Y2iQIgUkZT9ZQqVixhjFU4Xi4ZEW0Sa4w+pzD4D+jcaMjN8ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477596}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.4_1726899111004_0.4013275772785456", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "88395a81a3ab7ee3dc8dc31a73ff62ed3185f34d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-uBa2e28ohzNNwjr6Uxm4XyaA1M/8aTgfF2T7UIlElLaeXkgpmIJ2EitVNQxjO9xLLLy60YqAgKn/AqSpCUkE9g==", "signatures": [{"sig": "MEYCIQC8HA56daBYBnOfcCV7AA5Gscb5Dn8tKLD91oFxAt+3xQIhAN13DcpezAbkuSMcqRCjjzzeZmaODilFVJKsqesIfTO9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.22.5_1727437729460_0.00555707524645177", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cca8bf6f96467494c4cb8bba996752d3c7b20714", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-4ZoDZy5ShLbbe1KPSafbFh1vbl0asTVfkABC7eWqIs01+66ncM82YJxV2VtV3YVJTqq2P8HMx3DCoRSWB/N3rw==", "signatures": [{"sig": "MEUCIQCOourmUe3RPfUmGSPuyu53H9fKSbYZ0fFjt65lRPDWJgIgXZ7EqCpfFAe+jZodMtIzHZQe7AwO7vghO2J2vFVy3QQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2448924}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.23.0_1727766648699_0.7573782211930669", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d9fe32971883cd1bd858336bd33a1c3ca6146127", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-w1i+L7kAXZNdYl+vFvzSZy8Y1arS7vMgIy8wusXJzRrPyof5LAb02KGr1PD2EkRcl73kHulIID0M501lN+vobQ==", "signatures": [{"sig": "MEUCIA7awLZSYK39Rdb/sO3r489lelbxcmoP3Wuc/He3rwbYAiEA4z/j7VphG91TAvu/JCKNFDZ/CMjY5wV1bB9dMz6Nqhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457116}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.24.0_1727861873251_0.8805435833445836", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e3c0a875f780c3b7487e5aefe8db06a1930f1fc3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-UrwXowd3gyT+/ijoeSzMyHHGUaV3WhiJL77eTZE8/Pq+9K6auacIJ264biAUhHJ3FjAHsXNhzEmxGnj4tpDz2g==", "signatures": [{"sig": "MEUCIQDXBLLLN+1t2fJKxROt4+z4mR8RL0WPKD32L3DysM58qgIgV5wX2xNffNTa1kYfj0LfvvulgcpMn2NzFT+ybGCvOcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461212}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.24.1_1730011421132_0.9721425466199727", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "59800e26c538517ee05f4645315d9e1aded93200", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-XAo7cJec80NWx9LlZFEJQxqKOMz/lX3geWs2iNT5CHIERLFfd90f3RYLLjiCBm1IMaQ4VOX/lTC9lWfzzQm14Q==", "signatures": [{"sig": "MEQCIEnZxljEGecvLiiqTzwxaz6+1z/NUIrCoTLJyzKhvHrBAiAnng+mdoZB96OdhZRRidBGtYDqnZtVeySYeZ9XF7hc0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461212}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.24.2_1730043647929_0.5046408373746509", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "59cb793af37a29fe56bb3f02228cfe8d4bde8ba3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-VnqL1nWBqesTHEFxQhb447FiYiNRoYUNBXJBk+1NjQ8+Kwd6zFDlqY5wnDJcJsGQQt4ZZ9Lh64SS0ZXPOkSWiA==", "signatures": [{"sig": "MEQCIAFd/fIi44GsUeP1soyOQYDzRz/IEuOJbHuzgHxX+/HSAiASzHfbQPm5Ws14gbTj0HhjZrh/uu+dIwWNnG6ZCJNKYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461214}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.25.0-0_1730182549940_0.19431935490173635", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8ae9bf78986d1b16ccbc89ab6f2dfa96807d3178", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-rMTzawBPimBQkG9NKpNHvquIUTQPzrnPxPbCY1Xt+mFkW7pshvyIS5kYgcf74goxXOQk0CP3EoOC1zcEezKXhw==", "signatures": [{"sig": "MEUCIQDovmIHg8yBzNPljxYFb5jP2K84lvNHf43Kk9JZsImJdQIgYN7LGX92Dd3RBdyASpodjjw2tmYR/LkXMNSYWtUihVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461212}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.24.3_1730211288505_0.49752033102874105", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0af12dd2578c29af4037f0c834b4321429dd5b01", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-VJYl4xSl/wqG2D5xTYncVWW+26ICV4wubwN9Gs5NrqhJtayikwCXzPL8GDsLnaLU3WwhQ8W02IinYSFJfyo34Q==", "signatures": [{"sig": "MEQCIAxgvGceR3ei3m9RX0Un8mZ+2KzFSFxiKFlAelTpzNKDAiBZnzh+duJF9NH4mE8uT2Hz3SnyfNFCD1Fhca5VKFy1hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.24.4_1730710066389_0.0338723398084293", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "192f78bad8429711d63a31dc0a7d3312e2df850e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-zx71aY2oQxGxAT1JShfhNG79PnjYhMC6voAjzpu/xmMjDnKNf6Nl/xv7YaB/9SIa9jDYf8RBPWEnjcdlhlv1rQ==", "signatures": [{"sig": "MEQCIDbTPWC2sywHJKD3wEB7w2r3o1mUMTCU6mFfQqH1yeR0AiBSXTnUiDTVWjyCu0F4G+4b+pduxNCl98yfFbq7X7sBKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485788}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.25.0_1731141481165_0.36118789138304686", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "371315e032497f7a46f64b4ebcd207313b7f6669", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-+HJD2lFS86qkeF8kNu0kALtifMpPCZU80HvwztIKnYwym3KnA1os6nsX4BGSTLtS2QVAGG1P3guRgsYyMA0Yhg==", "signatures": [{"sig": "MEUCIDdg0rGyBDVHlmdAt1Zd0w0nBgaZg5ZlG2V4z/ETC97SAiEA8yz2AUAlbLn6ZI8TQrVI14RqWKpFnap4UbeuKTR5AmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485788}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.26.0_1731480337297_0.38221395095681787", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "28bccfe2a9fe27cf4ca23ccfcac767da616f137c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-7u2BdTW3PTdb4xIhzd0x48KZNMNzxULaR9QyqxYoRPuM2utpFEm6Dcph2b2pISAEIGOWD8MCbT7QxFcCcVsFKQ==", "signatures": [{"sig": "MEQCID6Esm7xNRf3Ik5gisnhNm2yRGMmKRW4UH+JgH29/z84AiBnlKU51rzR34UAVlKIiEK7jwswTA6ymZJnE2EZXNt4LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485790}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.0-0_1731481431511_0.4415212326974418", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e3bdb72b700d39922520ba3feec72020d2eeb9a0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-hPDZgSJgWfjdlmIYWBCEk/gsFsiWI4k7/mIPNzqhKA6cMqRUZaTddoj7Cci2FnXPDLejqSSJ7SpvWiVHCk8fFg==", "signatures": [{"sig": "MEUCIBpIXHQGGBZ6SbszgkJBPlJV1hZcW6YbYLAmZeoaS+ujAiEA8NRtK3Tgs3QJVEsJHMelLjiLlygh9uIEH++vxTg8uuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485790}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.0-1_1731566028532_0.004721204231130205", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "28612348c42e405aa6c384e7e292f20ea3af489e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-dS1+eCbbao54XB+wLW6uuwRkChq4L0UfKhd3wvt6s+EN1rTIi24ee5Lk3HfRGq9J2jsRm12/AGKLA0kd82Sp/g==", "signatures": [{"sig": "MEYCIQDp+X8lRobkVzsluOtcDG0vSijPyN2Nwamq9PKFpNcycgIhALkRZkcNZJ2xcDgArbI9+Qi12swDKPfD0yWPabkLDYtm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.0_1731667273149_0.004907348100466891", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "735672b4c8068fe32471ea367706ed6134410e02", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-9Smd4QG+SLi5Nn6nB/FWAhWO+5zBEtmGmWZKPAtw2+dvkm/9582n6ahpu9TNSJBr4kBHEBE1tkc8OxS9Cr/OgQ==", "signatures": [{"sig": "MEUCIQD5IiN+KGsilfuTcGqj01SnNvaWHxgjcqvYYTQX1s8TUAIgB9YnYeKaaxojIKbbrm7zW3UZD+5G5pWtTtXRToY8hbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498078}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.1-0_1731677328698_0.6469693536095205", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "dd5b42d37429ac9ca5c282ce97c7d699cbddfb0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-kbzGqsqqRQUuqVU6AkcJ43+tPRs48Xy1SulAl0zKQHWWL4NQ3F+wVn4FQawZWLX4QTb+fMrHXNOASUJI4VPV8w==", "signatures": [{"sig": "MEUCIQDMLrM0yJZpghJy3yikXz1yAe3jEVtTiNRiTCjPSSd69AIgUfPjPMACsoJwO05wCbPyoJrJY64Bh/KBmv0z1kBp+cI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498078}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.1-1_1731685123147_0.07481403434492262", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bc055d2601993e21eafb6e541426e1aef2c8130f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-mU8t4pSlUkvdRrhP8Gl8cU46W61avh+wUWTLQd/EBm/Ny19slYYXVvB9lHpAtLT9AVaXxMvTSc9m6H1qmUDDlw==", "signatures": [{"sig": "MEUCIFPztAB5VFjJXuyZ3riGWE2I5ukFtTqZRu11B752E8MkAiEAxEO4bQz9witk4vQorsxZAcf9pgGqzTgHbfwA5jJaFgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.1_1731686901831_0.007621601870160477", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b93cf65c2beb3d6a7139247ba6a948014502dcfb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-dOlWEMg2gI91Qx5I/HYqOD6iqlJspxLcS4Zlg3vjk1srE67z5T2Uz91yg/qA8sY0XcwQrFzWWiZhMNERylLrpQ==", "signatures": [{"sig": "MEQCIE0YOTyyF3kNArcOJ97EL8/0j1NhFK7wDO/TpRWsc1sqAiADKFpTGFDj9EdgHuUOwafKav4xftLjvTDwxtW880R3tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.2_1731691245490_0.08452478420864273", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6547bc0069f2d788e6cf0f33363b951181f4cca5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-nBXOfJds8OzUT1qUreT/en3eyOXd2EH5b0wr2bVB5999qHdGKkzGzIyKYaKj02lXk6wpN71ltLIaQpu58YFBoQ==", "signatures": [{"sig": "MEYCIQDtsnDB/lhnE/hjFxuo7RWMGOVaXMA5UioVnEhvsv0n6QIhAIgzOA1enQTign4Q06bYHNGOcf5LRm1gtki7c/9pGdCz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.3_1731948015830_0.9777439949189284", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "927764f1da1f2dd50943716dec93796d10cb6e99", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-5AeeAF1PB9TUzD+3cROzFTnAJAcVUGLuR8ng0E0WXGkYhp6RD6L+6szYVX+64Rs0r72019KHZS1ka1q+zU/wUw==", "signatures": [{"sig": "MEUCIEiDCgv27GQ69/7TqBKV2Lhd+47+gEif9ecZodwauIsxAiEA+irBLxwbsLjgrRGqB5YqJLKcbkMpCdUKUuyKtNtohlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2506268}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.27.4_1732345257099_0.6863869102232727", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3435d484d05f5c4d1ffd54541b4facce2887103a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-eKpJr4vBDOi4goT75MvW+0dXcNUqisK4jvibY9vDdlgLx+yekxSm55StsHbxUsRxSTt3JEQvlr3cGDkzcSP8bw==", "signatures": [{"sig": "MEYCIQCEE50Q6pOVV306mGPSEcdtezO+lwdfiIZVP1++VcFxwQIhAOGRWD6wOm/ugvg/qjJ8ct23kmsr3uaEBHzZHVRLnMFQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2498076}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.28.0_1732972585658_0.539057765719239", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6cc7c84cd4563737f8593e66f33b57d8e228805b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-xQTDVzSGiMlSshpJCtudbWyRfLaNiVPXt1WgdWTwWz9n0U12cI2ZVtWe/Jgwyv/6wjL7b66uu61Vg0POWVfz4g==", "signatures": [{"sig": "MEQCIAIeVTXoNRphlnndGgbo8LnplRjNL5JuMTkizTqX6TTnAiB6CE8/4P/cDvSOIlkTCA9BES0WPOSc+N/CnPgTbTxXxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2489884}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.28.1_1733485537653_0.051921581379522186", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bd5799cabe37d9191ec4cf51c84b72d75356cc46", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-prwj0aPeKZnqdB8Nd46tpnpkivzcMcpDvz+V0aCveNwPp4IaNXjlnlo8FTxgJzX2onfj+6I3nE9AqTAn/ZsK8g==", "signatures": [{"sig": "MEYCIQCLvCH2xW07JhzN3ru6Eo3qNFIJl+ZP6n1cysg5rA7FNAIhAPZEZRZ9yPL3iocVDlf8nNhrOiM6PhGyFO+pnVnKs8vy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485790}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.0-0_1734331235375_0.45859669398820735", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ef357f4144281aea45a1371690a3ec1acc322861", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-41puuDxYxpmzmlU9vIqOSGZLTSbF/SKBhRjORqFffBn2wdjJoyVfXnl9WldlsM4ODkz+mrpHnEaobLX1rf0sig==", "signatures": [{"sig": "MEYCIQCoxRn5vq/jmM2fVOPJxAUZneE7Y711gqGJJmksmxoKuQIhAPXqKzn7A2so6tGxbuw8ZggraAj7hAL8J9Vur9ANJbcb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485790}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.0-1_1734590289600_0.582565763377854", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8889f4e5c0e3897ef9014411c71f261159f842e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-4gE3GARcM43t6vbb2b4GXoIwVZ0yoNpCmVrmcXKqoN7i6+IvmTSg4p+5C0utopP6FBewxRT9dxTP8IIlOh/sMg==", "signatures": [{"sig": "MEUCIBtiN0urpJIakp2iICpqDmzQyHm+8r9nawhSlypI9LWQAiEA4UB7KFg+MNt8lM18FCGN+1EVJefSDkygc2WaibxgSeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485790}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.0-2_1734677804709_0.5568031316231556", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4399c9230817e44638cf16dbe0f1d9ac7b02b9a0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-YEABzSaRS7+v14yw6MVBZoMqLoUyTX1/sJoGeC0euvgMrzvw0i+jHo4keDZgYeOblfwdseVAf6ylxWSvcBAKTA==", "signatures": [{"sig": "MEQCIDifYSvhh5KYREEKxOH9Oz1yFbcBzGxkh85Ksdg8lHNlAiBp9Dfp120GX2//DuCacw9jPX8haZ+tKS0g3ka/uNri4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.0_1734719886041_0.8266536780143139", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ab7be89192f72beb9ea6e2386186fefde4f69d82", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-xufkSNppNOdVRCEC4WKvlR1FBDyqCSCpQeMMgv9ZyXqqtKBfkw1yfGMTUTs9Qsl6WQbJnsGboWCp7pJGkeMhKA==", "signatures": [{"sig": "MEQCIFo7Lroa+e50fSytS8NXAcoapkBFIiPpprEq1u6zIUC5AiBUZwYAw3weXhxBhmd57Oubey0xSPz79YkeWTxRUB3n8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.1_1734765404557_0.9359266794757712", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d3527aca3a917f07d7200ec3ffb29de442cf26c9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-aNWxvbgaorbvz2j5+P8f/VUNvgQApsFa83KM5MrjghTKr7701qOaGsxpjuvzdxKl5epxsF7h6Edo8UZArwmXGA==", "signatures": [{"sig": "MEYCIQDZvnnD2NSINZ2+bBUr2nCUWBziBU7XXoNs7xvLQkwfVgIhALK1vj+DPUus1QJRxgdyr2PWoohqI8MaNhC16zqYfU8Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481694}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.30.0-0_1734765476555_0.210826027228749", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cc5209e905b56cab8bed1d50ce1a5d59a5666d45", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-2svNC3aA55HosjmrwDwChZIOAeE36Hj9RW/Ei2MAYCJ5pGjeofStCs1wDStHcneUIV/mPwk5+eO9nJwyeoHGqg==", "signatures": [{"sig": "MEUCIQDOcwtNohkW78ckD4xv7/3nCbIgp2Fz/LB0Ws21TwnjjAIgM6PEZ8TdkTYsaUgx5kg+2ST22pMDWbrrfY1nYchYE8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481694}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.30.0-1_1735541579148_0.6942098297823875", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "24ff1a64dddd75c489bd8714bcb5a659769d3e4a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-jycl1wL4AgM2aBFJFlpll/kGvAjhK8GSbEmFT5v3KC3rP/b5xZ1KQmv0vQQ8Bzb2ieFQ0kZFPRMbre/l3Bu9JA==", "signatures": [{"sig": "MEUCIFe7B8txF30SqSOe/v79j5wD8YsWuwm6S0WQSaJ4tVR8AiEAz5rMz55J9afeW05qpryhQqcW3gtotGF+pTASfVGQ4js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.29.2_1736078905937_0.27778804999435724", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cf8ae018ea6ff65eb36722a28beb93a20a6047f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-3wzKzduS7jzxqcOvy/ocU/gMR3/QrHEFLge5CD7Si9fyHuoXcidyYZ6jyx8OPYmCcGm3uKTUl+9jUSAY74Ln5A==", "signatures": [{"sig": "MEUCIQDaTAh/ZjDYM6hN2yT0Q/enHTsMVgYMDyRo2CLfKWPt/wIgXogTInvoJqjDTVdZobI0NAEeMkkmajKS3kioamlX/zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.30.0_1736145438262_0.9395858755966682", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "036a4c860662519f1f9453807547fd2a11d5bb01", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-V9U8Ey2UqmQsBT+xTOeMzPzwDzyXmnAoO4edZhL7INkwQcaW1Ckv3WJX3qrrp/VHaDkEWIBWhRwP47r8cdrOow==", "signatures": [{"sig": "MEUCIQCRVHr+BQIzID7MJhLbCrd7v6zUMGnOtdXvBWyRDkNwkQIgZKEZHTkFIDDjXhR6Rg6Of3SLsllZ6oRsnhUgk2/Ue/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481692}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.30.1_1736246193936_0.7855643374069092", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f830b2e36140dfc39225c78de783c5d45b42fc6f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-xTL1xgHJU46QglkLkI2VMhCmxXFZkJVs+tkasStd3zMtXakeH4cgoyj3+TGyg3w2HKAqTkxJ8o62QYQO07Uuxg==", "signatures": [{"sig": "MEQCIDvWJPRpLjezUyi3JOXLi7YiHIpgTnQ0poirXM+WgcWHAiAVNPuXqnvCtSDTF9zxu3l960IQxHsmsqMOrkIu7yxXww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481694}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.31.0-0_1736834302756_0.9793700940288983", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "615273ac52d1a201f4de191cbd3389016a9d7d80", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-ypB/HMtcSGhKUQNiFwqgdclWNRrAYDH8iMYH4etw/ZlGwiTVxBz2tDrGRrPlfZu6QjXwtd+C3Zib5pFqID97ZA==", "signatures": [{"sig": "MEUCIQCZySYk+qcyO7u+vGLQilYvvOrFmmIXnP7ZwHXuQznqdQIgPN08YAIpxuwDgdvLr7kVEbEzjcfntNaVgyqg3M+Isrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2506268}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.31.0_1737291449634_0.29069372093776824", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0d2f74bd9cfe0553f20f056760a95b293e849ab2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-qcb9qYDlkxz9DxJo7SDhWxTWV1gFuwznjbTiov289pASxlfGbaOD54mgbs9+z94VwrXtKTu+2RqwlSTbiOqxGg==", "signatures": [{"sig": "MEUCIE2sY4jBkPgM5k1l2fRTueG97nOIyOvmbe8WxEubx3QEAiEAsmBVFH5kmT2H3exHwT6iB9nUB5s3l0APCy8aFjRCDYE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2506268}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.32.0_1737707295098_0.5250176892659006", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "186bb4254aa1ed864e5d8270b2e965962337ac5d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-URA4lcratxPW+EAj2+0Jbe4FsvIxQDrhzlYhYsqV6NljGb5uqxc5xt/YbooP6EdASbnkVMHP4TwvHRqrm4+HYg==", "signatures": [{"sig": "MEUCIQDrmquviTLuReYzzsLF02GM+iQsn4Uv49mS1uQ5ARBpggIgYbsGVxeorLr2819FYEQfzTZKXMqVuGqSPKlsMGvcsBQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2506270}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.33.0-0_1738053049245_0.6273727551442396", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "73d2f44070c23e031262b601927fdb4eec253bc1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-BLoiyHDOWoS3uccNSADMza6V6vCNiphi94tQlVIL5de+r6r/CCQuNnerf+1g2mnk2b6edp5dk0nhdZ7aEjOBsA==", "signatures": [{"sig": "MEUCIQDfGjLQ5bZ1YnzbpYahD/gt45BAMHE/MvD1XPTncxaCkgIgFlqNTLHd3kaINqE/MMtQcvmd/sKvNgpPincLAT0Dj2Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2506268}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.32.1_1738053235875_0.9231136257745949", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8e410902af30d8edba29f98f669811dda97ee8e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-1W1Rl8zUm2qIwGDUaH51aH64CfL9sag5BB3U92Dq7HrpRQb5pvv+tUeN1dMYr9KHX1t52fy5HuPEDVBrlywSvQ==", "signatures": [{"sig": "MEUCIBo6/rL2LDvko9F81XHEEhySgimsi3M2W6hfxGJj34e0AiEAvtDGUyt174b6dwDxd0tWPZDjqdgiaYZIwFWnqjfU2KQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.33.0_1738393962457_0.17264412295322407", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "516ece775a9e9770d6bd6bdecc7926a0355491c4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-jo0UolK70O28BifvEsFD/8r25shFezl0aUk2t0VJzREWHkq19e+pcLu4kX5HiVXNz5qqkD+aAq04Ct8rkxgbyQ==", "signatures": [{"sig": "MEYCIQDtsaflJz9u3gyLngkjdWynsejRCLhYh9lEZmzvy3LYOgIhAKgIvzWTgK5ZwbMv4zQfXsP5JFIeVw1eWHoluvElBLsi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.0_1738399262445_0.13481259022486602", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7439d15ffe62ac0468ef2d539d4c39b4686e1276", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-jqtKrO715hDlvUcEsPn55tZt2TEiBvBtCMkUuU0R6fO/WPT7lO9AONjPbd8II7/asSiNVQHCMn4OLGigSuxVQA==", "signatures": [{"sig": "MEUCIEMXziRLInhbH8eOFsaGomxVgyUGIjMENQnn5dCIaIA2AiEA2+zJzCGuZDidomTLaA5MRsuAviO/BhzzHv2mzeN3ygA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.1_1738565933115_0.12231945751171613", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "11545b2f17c8fe70809dafd6c128b547b44aa000", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-aDPHyM/D2SpXfSNCVWCxyHmOqN9qb7SWkY1+vaXqMNMXslZYnwh9V/UCudl6psyG0v6Ukj7pXanIpfZwCOEMUg==", "signatures": [{"sig": "MEYCIQCtK96WxASdX4kIq24Mk3GwmciGuh4vcG8Gyz+U9Rs6HgIhALOHzgCSXNVWkIF9RH/saws7wrNL/PlmHWY8p8rr91GL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.2_1738656646264_0.45377610579386807", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ac3de953f8e31b08f1528e17f0524af15b2df38c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-usztyYLu2i+mYzzOjqHZTaRXbUOqw3P6laNUh1zcqxbPH1P2Tz/QdJJCQSnGxCtsRQeuU2bCyraGMtMumC46rw==", "signatures": [{"sig": "MEQCIElHiWfm3I4Z60J5tHB0vZlWV0UD+y1/ojIwlDW7XYA/AiByVJaWTtuA3O0mcZ6cqRA/SPlxuK9Yr4dwGRVeqeMoPw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.3_1738747368033_0.9269975692486023", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ba3d117d0c36502b448936dba001579df9dc549a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-/iFIbhzeyZZy49ozAWJ1ZR2KW6ZdYUbQXLT4O5n1cRZRoTpwExnHLjlurDXXPKEGxiAg0ujaR9JDYKljpr2fDg==", "signatures": [{"sig": "MEQCIDD0T8zkBWEikKthzSIsCkn9V6POy3cAUnFJnT1vOMbzAiBaO6IwlmK2Y6ND/+wuAgbqzP1De8W5S9OGQCxqBYkCMg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.4_1738791112622_0.6021138491608489", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "50246753c1f1cdaea03db264e0eff0f0ac5c4ad4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-iUcH3FBtBN2/Ce0rI84suRhD0+bB5BVEffqOwsGaX5py5TuYLOQa7S7oVBP0NKtB5rub3i9IvZtMXiD96l5v0A==", "signatures": [{"sig": "MEYCIQD45PqXTl7yyYFZnUeY9APXol+7oStC07HkNkXGqEW5WAIhAKdLm5BXNIZ6jQCFr43vO9hyvQeJ/Wc568BiyihYD9Q0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2496012}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.5_1738918425432_0.5004640542565864", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "61c0a146bdd1b5e0dcda33690dd909b321d8f20f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-zmmpOQh8vXc2QITsnCiODCDGXFC8LMi64+/oPpPx5qz3pqv0s6x46ps4xoycfUiVZps5PFn1gksZzo4RGTKT+A==", "signatures": [{"sig": "MEYCIQCRysjlvpaBZPTVB48c54YhbZruRCL61eCQaqxRjI83MwIhAIYJr4vrnxE27S1mUB8iawEY+qJe4YntOI3oRd+m8KbT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2487804}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.6_1738945969719_0.7685357829332218", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c7981ad5cfb8c3cd5d643d33ca54e4d2802b9201", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-7wJPXRWTTPtTFDFezA8sle/1sdgxDjuMoRXEKtx97ViRxGGkVQYovem+Q8Pr/2HxiHp74SSRG+o6R0Yq0shPwQ==", "signatures": [{"sig": "MEYCIQDfplZGV3WWSz/aHpATmDJoAXJ1kncjS2SOnhsRXDn7kQIhAIda3XkpoSzAFFX0GzP03ZTo1eVUG4QO28GvzvaTMf43", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2479612}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.7_1739526880170_0.39705506393125156", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "00b6c29b298197a384e3c659910b47943003a678", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-SCXcP0ZpGFIe7Ge+McxY5zKxiEI5ra+GT3QRxL0pMMtxPfpyLAKleZODi1zdRHkz5/BhueUrYtYVgubqe9JBNQ==", "signatures": [{"sig": "MEUCIQCKjXmmDNirZADXBTTusGZLqEuimzVkJFPMyGQM6rIEiAIgVo1ZxutdwSGyAXemtNlTkFCs1O4mcTG/ea1tTTn6TF4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2479612}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.8_1739773625884_0.09542431237146842", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "29a6867278ca0420b891574cfab98ecad70c59d1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-cYRpV4650z2I3/s6+5/LONkjIz8MBeqrk+vPXV10ORBnshpn8S32bPqQ2Utv39jCiDcO2eJTuSlPXpnvmaIgRA==", "signatures": [{"sig": "MEUCICC2YdrhRxWX+3h1qIpW7htW8k4XJQkLu8lKmnBy3o5RAiEAhM1pXrllzNdsMagfgwH0N0gV1DXn/vo6D8t7xCuLPwg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2549244}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.34.9_1740814400832_0.8189554392705096", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "dfcff2c1aed518b3d23ccffb49afb349d74fb608", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-QysqXzYiDvQWfUiTm8XmJNO2zm9yC9P/2Gkrwg2dH9cxotQzunBHYr6jk4SujCTqnfGxduOmQcI7c2ryuW8XVg==", "signatures": [{"sig": "MEUCIQC9wglEhZNMAzpHYTR2dQ1aOcd71xQfpUvjEg+jZOuAFgIgTt4RXLLySB354eYsYaiK3ym2M3ExzZUoE1PQ/NN0mbg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2553340}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.35.0_1741415132593_0.28433369926322305", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9eb0075deaabf5d88a9dc8b61bd7bd122ac64ef9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-sycrYZPrv2ag4OCvaN5js+f01eoZ2U+RmT5as8vhxiFz+kxwlHrsxOwKPSA8WyS+Wc6Epid9QeI/IkQ9NkgYyQ==", "signatures": [{"sig": "MEYCIQC5AM85tznQ17X6Te3TXyBV5TPnQiq6jJ9UW73/nznJSgIhAInRsgeJmge2PlQhvXFXaNwMl90WanUScSgwi5KWAGna", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2561532}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.36.0_1742200589931_0.6573394646086503", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c134a22d30642345de8b799c816345674bf68019", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-E2lPrLKE8sQbY/2bEkVTGDEk4/49UYRVWgj90MY8yPjpnGBQ+Xi1Qnr7b7UIWw1NOggdFQFOLZ8+5CzCiz143w==", "signatures": [{"sig": "MEQCIGSXngSdB8sIikWfnFD21w4yo+tDsvuZHbp02ENHw8VIAiAGvWXpIEwGVJt9JrjMxGqq0U8BBCmzcyi7GVVjoE6AkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2557436}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.37.0_1742741867017_0.7811219474842055", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "520c2a8547672ec6c56a6833f6d38e9380d63dc7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-q5Zv+goWvQUGCaL7fU8NuTw8aydIL/C9abAVGCzRReuj5h30TPx4LumBtAidrVOtXnlB+RZkBtExMsfqkMfb8g==", "signatures": [{"sig": "MEUCID6Zdr4K7mayAfc7YNU2WhAKXwYeQv31CnXO+RZQRTigAiEA6Vny2kzU1ZZQgLZUxwbBfdfgO2a5pYmPeOtQLNvFnTY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2557436}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.38.0_1743229794451_0.6090553691998031", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "81caac2a31b8754186f3acc142953a178fcd6fba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-ThFdkrFDP55AIsIZDKSBWEt/JcWlCzydbZHinZ0F/r1h83qbGeenCt/G/wG2O0reuENDD2tawfAj2s8VK7Bugg==", "signatures": [{"sig": "MEUCIDHi/kT+KH5/dHIT6Aa3sT4RZTpFl1G7iD+uiJB8JFR9AiEAi1HQGD5rvmHHlOM/58amnhQLn+8ianGBWikBdWlAanY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2557436}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.39.0_1743569418208_0.3764453737147375", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8e703e2c2ad19ba7b2cb3d8c3a4ad11d4ee3a282", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-HZvjpiUmSNx5zFgwtQAV1GaGazT2RWvqeDi0hV+AtC8unqqDSsaFjPxfsO6qPtKRRg25SisACWnJ37Yio8ttaw==", "signatures": [{"sig": "MEUCIBHIwHO1LuTV+2PdVvGXB/J8tE6iC521/s0uq+ru7mHRAiEA1Aop/Ck5Ju15K0fvmBibdFZzakDsn+MmvLddyxrjGKI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2571052}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.40.0_1744447220134_0.5486508038805058", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c76fd593323c60ea219439a00da6c6d33ffd0ea6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-2BRORitq5rQ4Da9blVovzNCMaUlyKrzMSvkVR0D4qPuOy/+pMCrh1d7o01RATwVy+6Fa1WBw+da7QPeLWU/1mQ==", "signatures": [{"sig": "MEQCIC2WQ0sE3HOYRW1R8ECDoWnK2tT5li2oHZX4Cybqp1WkAiBk1H3PyQTrBLRbqxkGXKE7ZFJjWLKzGzOQhDTVsbcU7g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2603836}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.40.1_1745814969185_0.5601431702614272", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "fdf5b09fd121eb8d977ebb0fda142c7c0167b8de", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-tD46wKHd+KJvsmije4bUskNuvWKFcTOIM9tZ/RrmIvcXnbi0YK/cKS9FzFtAm7Oxi2EhV5N2OpfFB348vSQRXA==", "signatures": [{"sig": "MEYCIQDzA0FNhwadMpKf3zAP5X9DjsKzVkL3Ibrwf77CfKnrCwIhAIgVBZ3k19CJFpkxZzMfuI0gs6UmAQyj7wj0IlokiNvy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2603836}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.40.2_1746516460183_0.07967312980977925", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "8d3452de42aa72fc5fc3e5ad1eb0b68030742a25", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-m/P7LycHZTvSQeXhFmgmdqEiTqSV80zn6xHaQ1JSqwCtD1YGtwEK515Qmy9DcB2HK4dOUVypQxvhVSy06cJPEg==", "signatures": [{"sig": "MEYCIQDhNL2sZ6pEgHYfKdXLawG+VXvPs3/VT0nLVYbyyrw03gIhAOu5VgHIYTskC/aeagulTX9fBIA16XIlb6tyY3XgIW1V", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2603836}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.41.0_1747546452145_0.26601525865161935", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e67c7531df6dff0b4c241101d4096617fbca87c3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-y5CbN44M+pUCdGDlZFzGGBSKCA4A/J2ZH4edTYSSxFg7ce1Xt3GtydbVKWLlzL+INfFIZAEg1ZV6hh9+QQf9YQ==", "signatures": [{"sig": "MEQCHzXTf8WyWfHRd/6VNEPTofEAvVtWVSyMt7/oudva2/kCIQDP8clrjGmkSpnuyvoheWoivv5F8URScsN8L80qdB7m0A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2657308}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.41.1_1748067325584_0.9740318395075551", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4c02ac0bd1403cd0e806479deeefff2386cc4f16", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-2S74RTJ1lJeiX+HSCxlLD/6Z6ndu9/+Huf8kYrI2XTCztFosOWsjPt+aD4cxBG1JTWjoiRYtutc8HABruppFQQ==", "signatures": [{"sig": "MEUCIQCFuVtntYwDdDpWk0aSZJKLYki1Tndz6pHsChMvqK79OwIgex6aDbcmVkcH3CExUfm5u1+AHNvJ2i+jsoVBSI6L4Eg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2665500}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.41.2_1749210078900_0.5324426704617093", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "9a0f8691dede53d1720ebb2aeef72e483cf69220", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-g86PF8YZ9GRqkdi0VoGlcDUb4rYtQKyTD1IVtxxN4Hpe7YqLBShA7oHMKU6oKTCi3uxwW4VkIGnOaH/El8de3w==", "signatures": [{"sig": "MEUCIGMFX45KGvsMyIJ0m9n6MXYS2tAg1Zkqry4zq76LsKgEAiEAsn1ra/Mk9wvZZ2dWGEoTF0nEgaHfUEdUFreXqvviQdU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2665500}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.42.0_1749221339668_0.8849368756576377", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "51cbc8b1eb46ebc0e284725418b6fbf48686e4e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==", "signatures": [{"sig": "MEYCIQCEnZYj/YFYW1llB+04E1GDyzTcf3V34R42vbzhethvEwIhALRx93421cn0uouG/7ZKmfJbIxBKmDlv9UIXXxmSiCfy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2665500}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.43.0_1749619410742_0.8932532068701495", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "0a062d6fee35ec4fbb607b2a9d933a9372ccf63a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-PQUobbhLTQT5yz/SPg116VJBgz+XOtXt8D1ck+sfJJhuEsMj2jSej5yTdp8CvWBSceu+WW+ibVL6dm0ptG5fcA==", "signatures": [{"sig": "MEUCIQDsBFzHSlJ2aKC57J33vnz5fQAls6Z4LRw6xbOOyjXLbAIgB+DC8vEvaRf1hc5BjqeESUi8z1av5toB1wD675xeRPc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2665500}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.44.0_1750314226227_0.020682848701270462", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-x64-musl@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "3ecbf8e21b4157e57bb15dc6837b6db851f9a336", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-iAS4p+J1az6Usn0f8xhgL4PaU878KEtutP4hqw52I4IO6AGoyOkHCxcc4bqufv1tQLdDWFx8lR9YlwxKuv3/3g==", "signatures": [{"sig": "MEUCIFa/7CrFS0m8O8rmQSW6RoeolEek4BUJkDpUhwYvAuGKAiEAqJ4+nWVTznyDni0DbRCHlfC2PcNg3PhIHM4MVUHrW6Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2665500}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-x64-musl_4.44.1_1750912503989_0.3873756336976333", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-x64-musl", "version": "4.44.2", "os": ["linux"], "cpu": ["x64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["musl"], "main": "./rollup.linux-x64-musl.node", "_id": "@rollup/rollup-linux-x64-musl@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3D3OB1vSSBXmkGEZR27uiMRNiwN08/RVAcBKwhUYPaiZ8bcvdeEwWPvbnXvvXHY+A/7xluzcN+kaiOFNiOZwWg==", "shasum": "9fb1becedcdc9e227d4748576eb8ba2fad8d2e29", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2669596, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDSDQBXSJx2kGFyGOhQnhHGsPofw29snaGDuQXGKjyuSgIgW9A9P7cSIQMfXwDEHHYY0KHX9/M6rp55xkP/3IUvMJk="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-x64-musl_4.44.2_1751633815820_0.9103933509189932"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-08-01T04:49:15.645Z", "modified": "2025-07-04T12:56:56.297Z", "4.0.0-1": "2023-08-01T04:49:16.037Z", "4.0.0-2": "2023-08-01T11:16:52.447Z", "4.0.0-3": "2023-08-04T08:17:16.885Z", "4.0.0-4": "2023-08-04T11:37:04.567Z", "4.0.0-5": "2023-08-20T06:57:09.929Z", "4.0.0-6": "2023-08-20T07:51:57.348Z", "4.0.0-7": "2023-08-20T10:34:04.306Z", "4.0.0-8": "2023-08-20T11:22:56.412Z", "4.0.0-9": "2023-08-20T14:29:41.652Z", "4.0.0-10": "2023-08-21T15:30:40.001Z", "4.0.0-11": "2023-08-23T10:16:20.115Z", "4.0.0-12": "2023-08-23T14:41:16.167Z", "4.0.0-13": "2023-08-24T15:48:55.379Z", "4.0.0-14": "2023-09-15T12:34:46.257Z", "4.0.0-15": "2023-09-15T13:07:13.717Z", "4.0.0-16": "2023-09-15T14:17:39.376Z", "4.0.0-17": "2023-09-15T14:59:28.695Z", "4.0.0-18": "2023-09-15T16:11:09.232Z", "4.0.0-19": "2023-09-15T18:51:19.585Z", "4.0.0-20": "2023-09-24T06:10:59.246Z", "4.0.0-21": "2023-09-24T17:22:45.848Z", "4.0.0-22": "2023-09-26T16:17:58.991Z", "4.0.0-23": "2023-09-26T20:14:49.836Z", "4.0.0-24": "2023-10-03T05:13:10.764Z", "4.0.0-25": "2023-10-05T14:13:39.743Z", "4.0.0": "2023-10-05T15:15:17.766Z", "4.0.1": "2023-10-06T12:37:07.390Z", "4.0.2": "2023-10-06T14:19:08.522Z", "4.1.0": "2023-10-14T05:52:37.276Z", "4.1.1": "2023-10-15T06:32:11.806Z", "4.1.3": "2023-10-15T17:48:50.091Z", "4.1.4": "2023-10-16T04:34:32.883Z", "4.1.5": "2023-10-28T09:23:56.354Z", "4.1.6": "2023-10-31T05:45:39.292Z", "4.2.0": "2023-10-31T08:11:06.783Z", "4.3.0": "2023-11-03T20:13:31.395Z", "4.3.1": "2023-11-11T07:58:15.951Z", "4.4.0": "2023-11-12T07:50:13.345Z", "4.4.1": "2023-11-14T05:26:12.148Z", "4.5.0": "2023-11-18T05:52:32.029Z", "4.5.1": "2023-11-21T20:13:30.263Z", "4.5.2": "2023-11-24T06:30:11.214Z", "4.6.0": "2023-11-26T13:39:33.572Z", "4.6.1": "2023-11-30T05:23:32.465Z", "4.7.0": "2023-12-08T07:58:24.951Z", "4.8.0": "2023-12-11T06:25:19.965Z", "4.9.0": "2023-12-13T09:24:39.541Z", "4.9.1": "2023-12-17T06:26:32.268Z", "4.9.2": "2023-12-30T06:23:46.991Z", "4.9.3": "2024-01-05T06:21:07.638Z", "4.9.4": "2024-01-06T06:39:21.340Z", "4.9.5": "2024-01-12T06:16:33.954Z", "4.9.6": "2024-01-21T05:52:39.400Z", "4.10.0": "2024-02-10T05:59:03.688Z", "4.11.0": "2024-02-15T06:10:12.922Z", "4.12.0": "2024-02-16T13:33:10.823Z", "4.12.1": "2024-03-06T06:04:04.969Z", "4.13.0": "2024-03-12T05:29:11.406Z", "4.13.1-1": "2024-03-24T07:39:54.617Z", "4.13.1": "2024-03-27T10:28:12.790Z", "4.13.2": "2024-03-28T14:14:17.479Z", "4.14.0": "2024-04-03T05:23:38.811Z", "4.14.1": "2024-04-07T07:36:06.345Z", "4.14.2": "2024-04-12T06:24:07.774Z", "4.14.3": "2024-04-15T07:19:00.123Z", "4.15.0": "2024-04-20T05:37:47.526Z", "4.16.0": "2024-04-21T04:43:05.040Z", "4.16.1": "2024-04-21T18:30:37.699Z", "4.16.2": "2024-04-22T15:19:55.162Z", "4.16.3": "2024-04-23T05:13:03.410Z", "4.16.4": "2024-04-23T13:15:35.666Z", "4.17.0": "2024-04-27T11:30:20.919Z", "4.17.1": "2024-04-29T04:58:21.774Z", "4.17.2": "2024-04-30T05:01:23.265Z", "4.18.0": "2024-05-22T05:04:16.498Z", "4.18.1": "2024-07-08T15:25:44.171Z", "4.19.0": "2024-07-20T05:46:45.254Z", "4.19.1": "2024-07-27T04:54:29.854Z", "4.19.2": "2024-08-01T08:33:24.053Z", "4.20.0": "2024-08-03T04:49:21.669Z", "4.21.0": "2024-08-18T05:56:01.763Z", "4.21.1": "2024-08-26T15:54:45.034Z", "4.21.2": "2024-08-30T07:04:55.938Z", "4.21.3": "2024-09-12T07:06:21.812Z", "4.22.0": "2024-09-19T04:56:02.229Z", "4.22.1": "2024-09-20T08:22:23.081Z", "4.22.2": "2024-09-20T09:34:16.020Z", "4.22.3-0": "2024-09-20T14:48:30.326Z", "4.22.3": "2024-09-21T05:03:41.779Z", "4.22.4": "2024-09-21T06:11:51.275Z", "4.22.5": "2024-09-27T11:48:49.721Z", "4.23.0": "2024-10-01T07:10:48.934Z", "4.24.0": "2024-10-02T09:37:53.517Z", "4.24.1": "2024-10-27T06:43:41.371Z", "4.24.2": "2024-10-27T15:40:48.128Z", "4.25.0-0": "2024-10-29T06:15:50.186Z", "4.24.3": "2024-10-29T14:14:48.770Z", "4.24.4": "2024-11-04T08:47:46.557Z", "4.25.0": "2024-11-09T08:38:01.431Z", "4.26.0": "2024-11-13T06:45:37.643Z", "4.27.0-0": "2024-11-13T07:03:51.696Z", "4.27.0-1": "2024-11-14T06:33:48.829Z", "4.27.0": "2024-11-15T10:41:13.515Z", "4.27.1-0": "2024-11-15T13:28:49.019Z", "4.27.1-1": "2024-11-15T15:38:43.472Z", "4.27.1": "2024-11-15T16:08:22.144Z", "4.27.2": "2024-11-15T17:20:45.798Z", "4.27.3": "2024-11-18T16:40:16.060Z", "4.27.4": "2024-11-23T07:00:57.347Z", "4.28.0": "2024-11-30T13:16:25.890Z", "4.28.1": "2024-12-06T11:45:37.853Z", "4.29.0-0": "2024-12-16T06:40:35.596Z", "4.29.0-1": "2024-12-19T06:38:09.854Z", "4.29.0-2": "2024-12-20T06:56:44.969Z", "4.29.0": "2024-12-20T18:38:06.284Z", "4.29.1": "2024-12-21T07:16:44.887Z", "4.30.0-0": "2024-12-21T07:17:56.786Z", "4.30.0-1": "2024-12-30T06:52:59.368Z", "4.29.2": "2025-01-05T12:08:26.237Z", "4.30.0": "2025-01-06T06:37:18.466Z", "4.30.1": "2025-01-07T10:36:34.143Z", "4.31.0-0": "2025-01-14T05:58:22.972Z", "4.31.0": "2025-01-19T12:57:29.846Z", "4.32.0": "2025-01-24T08:28:15.296Z", "4.33.0-0": "2025-01-28T08:30:49.577Z", "4.32.1": "2025-01-28T08:33:56.046Z", "4.33.0": "2025-02-01T07:12:42.686Z", "4.34.0": "2025-02-01T08:41:02.698Z", "4.34.1": "2025-02-03T06:58:53.312Z", "4.34.2": "2025-02-04T08:10:46.504Z", "4.34.3": "2025-02-05T09:22:48.293Z", "4.34.4": "2025-02-05T21:31:52.866Z", "4.34.5": "2025-02-07T08:53:45.733Z", "4.34.6": "2025-02-07T16:32:49.985Z", "4.34.7": "2025-02-14T09:54:40.413Z", "4.34.8": "2025-02-17T06:27:06.100Z", "4.34.9": "2025-03-01T07:33:21.123Z", "4.35.0": "2025-03-08T06:25:32.857Z", "4.36.0": "2025-03-17T08:36:30.191Z", "4.37.0": "2025-03-23T14:57:47.225Z", "4.38.0": "2025-03-29T06:29:54.712Z", "4.39.0": "2025-04-02T04:50:18.512Z", "4.40.0": "2025-04-12T08:40:20.333Z", "4.40.1": "2025-04-28T04:36:09.393Z", "4.40.2": "2025-05-06T07:27:40.436Z", "4.41.0": "2025-05-18T05:34:12.391Z", "4.41.1": "2025-05-24T06:15:25.869Z", "4.41.2": "2025-06-06T11:41:19.138Z", "4.42.0": "2025-06-06T14:48:59.925Z", "4.43.0": "2025-06-11T05:23:30.992Z", "4.44.0": "2025-06-19T06:23:46.459Z", "4.44.1": "2025-06-26T04:35:04.212Z", "4.44.2": "2025-07-04T12:56:56.054Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-x64-musl`\n\nThis is the **x86_64-unknown-linux-musl** binary for `rollup`\n", "readmeFilename": "README.md"}