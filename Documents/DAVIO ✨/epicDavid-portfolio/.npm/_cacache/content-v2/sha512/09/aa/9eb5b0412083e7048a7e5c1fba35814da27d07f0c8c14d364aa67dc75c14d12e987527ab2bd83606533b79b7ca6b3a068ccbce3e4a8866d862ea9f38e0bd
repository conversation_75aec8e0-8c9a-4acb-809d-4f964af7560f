{"_id": "@rolldown/pluginutils", "_rev": "57-0821a1f959c0d6e89b267318114ba56a", "name": "@rolldown/pluginutils", "dist-tags": {"nightly": "1.0.0-beta.13-commit.024b632", "canary": "1.0.0-beta.14-commit.12b8061", "latest": "1.0.0-beta.24"}, "versions": {"1.0.0-beta.8-commit.56abf23": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.56abf23", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.56abf23", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "6198fd7cb1edf40b022206e6ddebf4304503aac5", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.56abf23.tgz", "fileCount": 2, "integrity": "sha512-Zoa3/5c/qDR8R2QLZooPO/7OA4OFe135yiJveWX04AK5F8Jj0aOihhczsw+TyxYbOEeD3/Vlf311ExBgMlBSqA==", "signatures": [{"sig": "MEYCIQCdVn0pq45F/MmoVYVQ8F/auT8oVlahVzk6jpOLXZC44AIhAO+FDMIt5E2d5GdbPFhIIhPx243kveicQDlP8UmEYFbG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.56abf23", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 2146}, "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.56abf23.tgz", "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test": "vitest --typecheck", "build": "node build.mjs", "build:all": "npm run build:esm && npm run build:cjs", "build:cjs": "tsc -p ./tsconfig.cjs.json", "build:esm": "tsc -p ./tsconfig.esm.json"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9e778863c464a2d71b3e21ba3c1a9646/rolldown-pluginutils-1.0.0-beta.8-commit.56abf23.tgz", "_integrity": "sha512-Zoa3/5c/qDR8R2QLZooPO/7OA4OFe135yiJveWX04AK5F8Jj0aOihhczsw+TyxYbOEeD3/Vlf311ExBgMlBSqA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.56abf23_1746801247791_0.8608497648365963", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.f97bbba": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.f97bbba", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.f97bbba", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "8ed892588be336bb2478e7a1d3e4eb81f49a3510", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.f97bbba.tgz", "fileCount": 15, "integrity": "sha512-uiNWiZt3xzzl6iuk3hhpz8QTUqmh1bAbtZ+Yuj7x/v0bn52dTYsNKwftfxvcFR+THmRQbn2CsBky/wmOw3JsgQ==", "signatures": [{"sig": "MEQCIARDjiW0104pYsDs9E2dhE77LIk+s/lxI21a7IgGX9akAiA0K9DJIGylHp5xBLF2m/E1Cos0yN4V/Jyom9odqHTeug==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.f97bbba", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21215}, "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.f97bbba.tgz", "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test": "vitest --typecheck", "build": "node build.mjs", "build:all": "npm run build:esm && npm run build:cjs", "build:cjs": "tsc -p ./tsconfig.cjs.json", "build:esm": "tsc -p ./tsconfig.esm.json"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/b558f819ab47d8a08f9d1e87b8f08b23/rolldown-pluginutils-1.0.0-beta.8-commit.f97bbba.tgz", "_integrity": "sha512-uiNWiZt3xzzl6iuk3hhpz8QTUqmh1bAbtZ+Yuj7x/v0bn52dTYsNKwftfxvcFR+THmRQbn2CsBky/wmOw3JsgQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.f97bbba_1746803366801_0.9865814488178628", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.e5c11c6": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.e5c11c6", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.e5c11c6", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "3af80fcfad5f183c9c2097b91ae9ed9a73e31e23", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.e5c11c6.tgz", "fileCount": 15, "integrity": "sha512-vv1Lzt0h1f/nSsJrbftGIN6H4ik/F5xjl8iUphARPUvvZQXIJlGNc+Ympex0aEYmLCJgm5XlyTA/9HUGdb/m/A==", "signatures": [{"sig": "MEYCIQCsLBywfyoBPpqd5RWFmxdbOnqQ4Frhf81q2i85aKRr2gIhAOOBA2/Uxm555friDLDC94E9aaoxQDkRmqFjbWR9mYT8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.e5c11c6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 21215}, "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.e5c11c6.tgz", "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test": "vitest --typecheck", "build": "node build.mjs", "build:all": "npm run build:esm && npm run build:cjs", "build:cjs": "tsc -p ./tsconfig.cjs.json", "build:esm": "tsc -p ./tsconfig.esm.json"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/347b0d5ef73f556e6c9b274ac5547306/rolldown-pluginutils-1.0.0-beta.8-commit.e5c11c6.tgz", "_integrity": "sha512-vv1Lzt0h1f/nSsJrbftGIN6H4ik/F5xjl8iUphARPUvvZQXIJlGNc+Ympex0aEYmLCJgm5XlyTA/9HUGdb/m/A==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.e5c11c6_1746836203032_0.26462648624725826", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.05b3e10": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.05b3e10", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.05b3e10", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "6dfc67f77f65f903070588352a368e7be164756d", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.05b3e10.tgz", "fileCount": 6, "integrity": "sha512-mvF7FytqnWD36DyMl9DuKcAT5Z9gyfm8l8rl3CiSYFPTnrTqXwSz1E8C7bPEJBgjUIv6EykNLBNCiq575BhQig==", "signatures": [{"sig": "MEQCIBAdZ29UgQiQsHOdzIrWmW0LRCTLr1+rYaaj2tHD4Jh6AiAYQdnSn7xwVfbNDeLp+EXjLRqVbvI6wcxTLc5lXYNzIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.05b3e10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19257}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.05b3e10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6def6f9512818e1ca1d06794e36ada75/rolldown-pluginutils-1.0.0-beta.8-commit.05b3e10.tgz", "_integrity": "sha512-mvF7FytqnWD36DyMl9DuKcAT5Z9gyfm8l8rl3CiSYFPTnrTqXwSz1E8C7bPEJBgjUIv6EykNLBNCiq575BhQig==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.05b3e10_1746887396379_0.392486836191396", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.2a5c6a6": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.2a5c6a6", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.2a5c6a6", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "f1b83ca51b3f4132e0bce5bf853c34bf0df58fe5", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.2a5c6a6.tgz", "fileCount": 6, "integrity": "sha512-00vGY8ox2diQ6ZOw9lllyV3e7KrtaczhOz2hsHkOoVIBWyMFGcJD6CpFN10nTLkaaSSqerQjmjxeVwTJH2yhYQ==", "signatures": [{"sig": "MEYCIQDE6xzjMVrsYcC+aBkc1e419IDAPZp/P/JEp3oEB64weQIhALEsPE6oSFujWVk3hrJDgKa2TTi0P4V1TZJe/YiV1048", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.2a5c6a6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19257}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.2a5c6a6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0ece95602286d1361be0dd116a167b2a/rolldown-pluginutils-1.0.0-beta.8-commit.2a5c6a6.tgz", "_integrity": "sha512-00vGY8ox2diQ6ZOw9lllyV3e7KrtaczhOz2hsHkOoVIBWyMFGcJD6CpFN10nTLkaaSSqerQjmjxeVwTJH2yhYQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.2a5c6a6_1746981663347_0.7028449342694352", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.8951737": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.8951737", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.8951737", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "a8fdeccc42d0e5e8481914b015d60ba271380292", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.8951737.tgz", "fileCount": 6, "integrity": "sha512-dx9SoAb0lLSZp3Jhy5jRCdJg5OJXv7S7bdF+qpLjPMoRPfvFcwIRi9QPdtprqjdkOR72+peteBYTdlx1LWmQSA==", "signatures": [{"sig": "MEYCIQDZc/7/OuetQTeKe5/R7exhrGuGx5144PkMDIfGOUCImgIhAMxk9tIWXp6BKWvM5sq+ha6BTxlusTJ78A3aDQrxu4OG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.8951737", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19257}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.8951737.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/45a8a1e8c0d01e94aea5a8dd77e51844/rolldown-pluginutils-1.0.0-beta.8-commit.8951737.tgz", "_integrity": "sha512-dx9SoAb0lLSZp3Jhy5jRCdJg5OJXv7S7bdF+qpLjPMoRPfvFcwIRi9QPdtprqjdkOR72+peteBYTdlx1LWmQSA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.8951737_1747009147471_0.6515862616704942", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.66f4623": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.66f4623", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.66f4623", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "c416a31be5adf2e711410446eca8b6bacb3f85af", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.66f4623.tgz", "fileCount": 6, "integrity": "sha512-lgeOZGyO4/UEm6f6hjkd5pociV/9z0O9nOwvBkfxN+Mi0SHZlOKNVOak/KTJU/bD0QK+/RjC6xar9D36jKntKw==", "signatures": [{"sig": "MEUCIADfvWygcF98RchzFxviBZBgNOr1120vvRYyzfJEaZMRAiEAiJob2la21Xu3JaHqeoc1wC8maTiEMRuTAIy7g1Q7fMU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.66f4623", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19257}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.66f4623.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ae7cb1928297f20def7b2d799a218e27/rolldown-pluginutils-1.0.0-beta.8-commit.66f4623.tgz", "_integrity": "sha512-lgeOZGyO4/UEm6f6hjkd5pociV/9z0O9nOwvBkfxN+Mi0SHZlOKNVOak/KTJU/bD0QK+/RjC6xar9D36jKntKw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.66f4623_1747037930050_0.5926223576704481", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.d95f99e": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.d95f99e", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.d95f99e", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "5af9e4eb66ff690dd8a167a4ee0e39ca766e3867", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.d95f99e.tgz", "fileCount": 6, "integrity": "sha512-m0VRAx0VjzbiV55GPB3kRbxonm9pkiTzn5HVu/xWfCqGfnFY2G9bjJCnwwZ+pNE0Lg/dppOYHfnPIZcOLgF4tg==", "signatures": [{"sig": "MEYCIQDnR7wd+/DY+IlEyjTAY4g5p4c6Ds3RNThlSwm19l54oQIhAKvb3ai099gePE0DytUod/cqNno+2O2/AilrR67U46X0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.d95f99e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19257}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.d95f99e.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/79275ed71fd73a7c5ce931cd311ea8c0/rolldown-pluginutils-1.0.0-beta.8-commit.d95f99e.tgz", "_integrity": "sha512-m0VRAx0VjzbiV55GPB3kRbxonm9pkiTzn5HVu/xWfCqGfnFY2G9bjJCnwwZ+pNE0Lg/dppOYHfnPIZcOLgF4tg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.d95f99e_1747095548526_0.5481786411671445", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.985af6d": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.985af6d", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.985af6d", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "7b730c0b9644662db0b7228341e51e5f329d019b", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.985af6d.tgz", "fileCount": 6, "integrity": "sha512-LGseQOuEJa3BcQ5Nj3cRRCAEYxCkSrNAG23LOHFKH+PuNfxqJx4nQRjrUWlzLpEGAPYNYRPuKTdaw8YZuWzzLQ==", "signatures": [{"sig": "MEUCIGuePLtrVCOFzFkBaf2j9/ObxfcqyMbrZdIB1AniHY/jAiEAuqEMPK8MzYfWIg9/VAEBiEUivi7QQuhW7If0hOb7C8k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.985af6d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24548}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.985af6d.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/0af7e34446f6211aefd631b647afaba0/rolldown-pluginutils-1.0.0-beta.8-commit.985af6d.tgz", "_integrity": "sha512-LGseQOuEJa3BcQ5Nj3cRRCAEYxCkSrNAG23LOHFKH+PuNfxqJx4nQRjrUWlzLpEGAPYNYRPuKTdaw8YZuWzzLQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.985af6d_1747181809416_0.847278131878431", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.bf53a10": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.bf53a10", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.bf53a10", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "93ba4a8356612c8d2583855cd8c76e0b150aa642", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.bf53a10.tgz", "fileCount": 6, "integrity": "sha512-jPWgCdBQhg8jyFedTSeMYIvpU91p1O79b3CEgfRsdq78yPkQ3te8k4xRIGT6jGgJOe53u+jLZa/N2Jqzd6ZyZA==", "signatures": [{"sig": "MEUCIFvrET0bJmIcmObG4kLU+PWOf2d9vU5gBf4FtH5k8MEpAiEAj03LeOJskXM8YPFYfq614VacvFVOtOCCZtWq9pHcCS0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.bf53a10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23858}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.bf53a10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/060efcae3457470aea37400907bdf660/rolldown-pluginutils-1.0.0-beta.8-commit.bf53a10.tgz", "_integrity": "sha512-jPWgCdBQhg8jyFedTSeMYIvpU91p1O79b3CEgfRsdq78yPkQ3te8k4xRIGT6jGgJOe53u+jLZa/N2Jqzd6ZyZA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.bf53a10_1747268248269_0.9551448298764995", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.709eb63": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.709eb63", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.709eb63", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "70340e8c39b09c0cd93ebb5bdc4d141103a3d1c6", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.709eb63.tgz", "fileCount": 6, "integrity": "sha512-b6/sEOJXsAGYWTtYRqHU4bVaIccQg565oPwHBvwF0KQN8U/SdFUNT1vytfZEpZKjdDPP67yGkHJg9DfqgWYDqg==", "signatures": [{"sig": "MEQCIDug4UTloaJeBAty9mzbe7SIeGBcEWzfPEYfLXkJWWvkAiB/O5SlUNqKENwXsyp8QGvtDaV646QCkgAzDVr507RUDA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.709eb63", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23858}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.709eb63.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/81556f74995bdab4b8a343a270ecb0e8/rolldown-pluginutils-1.0.0-beta.8-commit.709eb63.tgz", "_integrity": "sha512-b6/sEOJXsAGYWTtYRqHU4bVaIccQg565oPwHBvwF0KQN8U/SdFUNT1vytfZEpZKjdDPP67yGkHJg9DfqgWYDqg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.709eb63_1747354740812_0.8314663296340199", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.53a64a8": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.53a64a8", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.53a64a8", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "614c9ee03831e351ae58c51783805f9334442579", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.53a64a8.tgz", "fileCount": 6, "integrity": "sha512-eZDcmSexolGK//VsvWjZniJoQGASWXnj5CpWEKXYc2Jr+zFnfXUycdpjrr6KyxDGqLzoBWmNGF4OZVACqqh7uw==", "signatures": [{"sig": "MEUCIDjwa0npQ8PsH9/Ay9z9qOF6AQBfbcOE6R8AyDd0/dqRAiEA3dzeCxlc9E8fewwrQ1sfuPh+q49ZU7EMX9VWSwqTRw0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.53a64a8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23858}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.53a64a8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/815df1241747bd878813719dfaf48b6a/rolldown-pluginutils-1.0.0-beta.8-commit.53a64a8.tgz", "_integrity": "sha512-eZDcmSexolGK//VsvWjZniJoQGASWXnj5CpWEKXYc2Jr+zFnfXUycdpjrr6KyxDGqLzoBWmNGF4OZVACqqh7uw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.53a64a8_1747441054319_0.29366465010806464", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.a29b7fb": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.a29b7fb", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.a29b7fb", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "93d8a70530d0d33b65b32a458fc71ac6e95c5b8b", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.a29b7fb.tgz", "fileCount": 6, "integrity": "sha512-s5nJ3TK5IjznUXCY5njlgYUgWjIdaQYYdZX5zz8EML5+RX0YVS++tN4RNfhtPQrbrzpA7ANjUTH+aJeqgr0mbQ==", "signatures": [{"sig": "MEYCIQCXMC03lRiTfEZsCzv8I9TcjhKbYHkmhs9N+uqukWH7VQIhAOBBR2oP81Hax0Yab3cDvtkd5GWb23otXCxvHfBhjrz3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.a29b7fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23858}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.a29b7fb.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/2069d8ae6234c717e2e5e1abcb85c4ee/rolldown-pluginutils-1.0.0-beta.8-commit.a29b7fb.tgz", "_integrity": "sha512-s5nJ3TK5IjznUXCY5njlgYUgWjIdaQYYdZX5zz8EML5+RX0YVS++tN4RNfhtPQrbrzpA7ANjUTH+aJeqgr0mbQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.a29b7fb_1747504971855_0.7615483775274374", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.8-commit.360c072": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.8-commit.360c072", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.8-commit.360c072", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "b2c38ff94a3a52291437462dfc604a76d67dc6e5", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.8-commit.360c072.tgz", "fileCount": 6, "integrity": "sha512-OI2A/nrQsAJKNdEbBqGUG2uWyFFdH4qmUfOKzrO8AZaRQqz0rvUVBL4r0gZRQenfMWxFslQoHgjT4Y94hYTd5A==", "signatures": [{"sig": "MEYCIQCcDRLoXHVYFG6Cb7n+wnX7lvc/WTA9gyR8btGXrXbriwIhAIDKQfVUT3T9FYUGE9D+gEtk9EBJcydXazVVrKSo/0Y2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.8-commit.360c072", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23858}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.8-commit.360c072.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/be5f881e17b76df26669e28f1b070a98/rolldown-pluginutils-1.0.0-beta.8-commit.360c072.tgz", "_integrity": "sha512-OI2A/nrQsAJKNdEbBqGUG2uWyFFdH4qmUfOKzrO8AZaRQqz0rvUVBL4r0gZRQenfMWxFslQoHgjT4Y94hYTd5A==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.1", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.8-commit.360c072_1747613846840_0.8595675798772313", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "68ef9fff5a9791a642cea0dc4380ce6cb487a84a", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9.tgz", "fileCount": 6, "integrity": "sha512-e9MeMtVWo186sgvFFJOPGy7/d2j2mZhLJIdVW0C/xDluuOvymEATqz6zKsP0ZmXGzQtqlyjz5sC1sYQUoJG98w==", "signatures": [{"sig": "MEQCIEZ7q1TFHDI/fmdxVftJHYYhGEaLuV5HxR+eNCPNAgUQAiBXGFU6JwC33o89ayBoWbh48WsCH8ktFiGvHoGEaybQZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23860}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/eb97424c487888b9b121a6b6456645d0/rolldown-pluginutils-1.0.0-beta.9.tgz", "_integrity": "sha512-e9MeMtVWo186sgvFFJOPGy7/d2j2mZhLJIdVW0C/xDluuOvymEATqz6zKsP0ZmXGzQtqlyjz5sC1sYQUoJG98w==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9_1747632940321_0.5654503736578242", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.43425a0": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.43425a0", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.43425a0", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "d528ad4988b7a5d89b226ded96f9fc11e08444f0", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.43425a0.tgz", "fileCount": 6, "integrity": "sha512-JcB8Cm4t/kFaWeiv0gd24sahB1PrkRF3f5AvAMRQQxY7z16FKvFB6BV3Z8YsgJdNUmEtUd64urlq89C7ls8WsA==", "signatures": [{"sig": "MEUCIBzweC2uzxlsF23cI8Gh2JpKBZSsTrx+6sXpy7f/I0h+AiEA/IYr+GxV5nCA9fra16nEwSdqDOrrKWJ/gS3zkTjaDTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.43425a0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.43425a0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/e340d8e36e24b85fcde0dc606bafd35b/rolldown-pluginutils-1.0.0-beta.9-commit.43425a0.tgz", "_integrity": "sha512-JcB8Cm4t/kFaWeiv0gd24sahB1PrkRF3f5AvAMRQQxY7z16FKvFB6BV3Z8YsgJdNUmEtUd64urlq89C7ls8WsA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.43425a0_1747700296290_0.9914739403985435", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.76c39c6": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.76c39c6", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.76c39c6", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "34e7bbc64fec7a1d2080b50cda271a673cb41e68", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.76c39c6.tgz", "fileCount": 6, "integrity": "sha512-opXD8EMfs/i8duUBqb/F0Ah+gxZ0GejsC8ACrQpB9uvIM7oR0NS8hpRcmMefHmpdkbYWETKY/VcAXl299akuOA==", "signatures": [{"sig": "MEYCIQDQm6yXGmOSTgylFV78qN7icgcbOw3SZ2Mzceqcn4KADQIhAN6AZTb/7KIi8zr9mOe8+WMsGaTguyNK+4sIbz+ARRim", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.76c39c6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.76c39c6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/edf009aae9b46f7d3ffa04bb9c16d822/rolldown-pluginutils-1.0.0-beta.9-commit.76c39c6.tgz", "_integrity": "sha512-opXD8EMfs/i8duUBqb/F0Ah+gxZ0GejsC8ACrQpB9uvIM7oR0NS8hpRcmMefHmpdkbYWETKY/VcAXl299akuOA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.76c39c6_1747730644312_0.7174822293931666", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.ce72026": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.ce72026", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.ce72026", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "a481fe603d695231b00c0258e392e0a98e10bc51", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.ce72026.tgz", "fileCount": 6, "integrity": "sha512-CAnIFIXLPBj+cX1reCSNovwjFgMgjucBmMJhHYMninOFTyYHTyWKxHTGjvkijue4J3wvcMHr4RWmg8045Nf75Q==", "signatures": [{"sig": "MEQCICcm1qLsN+af+aIDbPk1ovuAgArT36l/hUmiBSNrW5mYAiBPpAayiBj9tPcco4jOupAmRqAIbQBOqY5Pfa+Ot0DOtg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.ce72026", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.ce72026.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a1d30bc1dbccbef52f2baaa927c2155f/rolldown-pluginutils-1.0.0-beta.9-commit.ce72026.tgz", "_integrity": "sha512-CAnIFIXLPBj+cX1reCSNovwjFgMgjucBmMJhHYMninOFTyYHTyWKxHTGjvkijue4J3wvcMHr4RWmg8045Nf75Q==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.ce72026_1747786642949_0.6380588345712936", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.51df2b7": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.51df2b7", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.51df2b7", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "e7d12895c2fdb0db8ec8639a677b8ab0b8711bb6", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.51df2b7.tgz", "fileCount": 6, "integrity": "sha512-A+2WuD4O5309iooTWXMS0vMfb44bKJUOCwJj2oBYvOpaNT3VHFmgLdx++NTlKh/3hJ8Xa+Zy8c34z0EBQSU+mw==", "signatures": [{"sig": "MEUCIQDwSYF/cZqtEhkT1+EViVNs0YflUOnK1oL+VAvC2Bg/pwIgBxL54Um+VNgewnFRGDVEG1GcuAU1vxlSMpG3kjZ7g8U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.51df2b7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.51df2b7.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d45460fd1d704b4bd5f2fa63df8bb9c6/rolldown-pluginutils-1.0.0-beta.9-commit.51df2b7.tgz", "_integrity": "sha512-A+2WuD4O5309iooTWXMS0vMfb44bKJUOCwJj2oBYvOpaNT3VHFmgLdx++NTlKh/3hJ8Xa+Zy8c34z0EBQSU+mw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.51df2b7_1747873048884_0.7317201462515786", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.aca15b3": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.aca15b3", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.aca15b3", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "3d5ff998c217f5be1c0b427e5ad03d22de045850", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.aca15b3.tgz", "fileCount": 6, "integrity": "sha512-wHiTuHWYcuTVrn/F9OO6XXVuI0/jyC/w/PWjZhBBzcCgUNNY33ghgXMqwm4ZXIkFxQmqAO0NUFKa+BDxb9SIZg==", "signatures": [{"sig": "MEUCIQCv47+jx5hPAHDO2P5Ryo89etoCKyiC1MOW4GBTKf7e1QIgbdwxcyFkZ2ztValoir8ctzhobKvYFK4VcEIOq+6w3vk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.aca15b3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.aca15b3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/05d7dc62d400c8aabd01a96c83f61c5f/rolldown-pluginutils-1.0.0-beta.9-commit.aca15b3.tgz", "_integrity": "sha512-wHiTuHWYcuTVrn/F9OO6XXVuI0/jyC/w/PWjZhBBzcCgUNNY33ghgXMqwm4ZXIkFxQmqAO0NUFKa+BDxb9SIZg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.aca15b3_1747959428309_0.6706235333505641", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.d91dfb5": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.d91dfb5", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.d91dfb5", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "8ba25bccc4b61ab1aa42b8feb0b1abbe1313cd68", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.d91dfb5.tgz", "fileCount": 6, "integrity": "sha512-8sExkWRK+zVybw3+2/kBkYBFeLnEUWz1fT7BLHplpzmtqkOfTbAQ9gkt4pzwGIIZmg4Qn5US5ACjUBenrhezwQ==", "signatures": [{"sig": "MEUCIQDIy0D1PpkfCIKVsxEe4w7EAEzvZjneLdIvFXXtvk08+wIgLiCCmv8AcwGw/XNUXaGh0maFM168jXFayIQOBe1iGOc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.d91dfb5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.d91dfb5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a960ae6492a9990c86a877efd01a4c35/rolldown-pluginutils-1.0.0-beta.9-commit.d91dfb5.tgz", "_integrity": "sha512-8sExkWRK+zVybw3+2/kBkYBFeLnEUWz1fT7BLHplpzmtqkOfTbAQ9gkt4pzwGIIZmg4Qn5US5ACjUBenrhezwQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.d91dfb5_1748045784445_0.900140333329652", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.8371a90": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.8371a90", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.8371a90", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "997439020705f359929bb7110d06cbc54d07c08b", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.8371a90.tgz", "fileCount": 6, "integrity": "sha512-SDmCbisAMZsIjtWyBUUO0/uHO0TTUc0Mt86CCFX7rl14EtP2VokpWYYH8DI42BySgmlJjckcghSCIkAJvh8ufw==", "signatures": [{"sig": "MEUCIQDA3Cb+s4VVi8xPsgZTQR1MumJSG7h2BQnUds+hghOjZwIgbdNcxiRR66FfjRwK/VzMpcaon1x70m7eTp0bRxOYKIE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.8371a90", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.8371a90.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ed24ea6f1dfd4cfb767f60290c1a8794/rolldown-pluginutils-1.0.0-beta.9-commit.8371a90.tgz", "_integrity": "sha512-SDmCbisAMZsIjtWyBUUO0/uHO0TTUc0Mt86CCFX7rl14EtP2VokpWYYH8DI42BySgmlJjckcghSCIkAJvh8ufw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.8371a90_1748132251590_0.978294288566707", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.273d50e": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.273d50e", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.273d50e", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "edc0ceca1b6ef8518caa1d188189e65a6c9a02d2", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.273d50e.tgz", "fileCount": 6, "integrity": "sha512-QOH6dLsibrXA7KWzZcybmRdotggY+CsOeSzBi8IbEdsIn67lVOtdBsQAF7gIXsokrp6CQwOc00KfzPrLF6fW3w==", "signatures": [{"sig": "MEQCIEaXcc6c7DtkMh4P5TZkN5tj2h35ru18Lrw9rhRmlhCnAiB+D2wsmZ+QmF2uS5oaIYbICch4PCNm7z1hI9b7jILXgQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.273d50e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.273d50e.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/6a6f2cc2ab3f973a7372049549d33d38/rolldown-pluginutils-1.0.0-beta.9-commit.273d50e.tgz", "_integrity": "sha512-QOH6dLsibrXA7KWzZcybmRdotggY+CsOeSzBi8IbEdsIn67lVOtdBsQAF7gIXsokrp6CQwOc00KfzPrLF6fW3w==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.273d50e_1748218649042_0.0939619591391827", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.b174110": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.b174110", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.b174110", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "b68f99224c8723bec6866cee22f95e2b83eef6ab", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.b174110.tgz", "fileCount": 6, "integrity": "sha512-l54AGmj0y8ytD0iB0XDR9FfJlTa6551eB25MV1Jnmk6dSY3XhLUJ4dqCjHyhhcteki8ofvusYjydVHkxQKQnbQ==", "signatures": [{"sig": "MEYCIQDXHeIu5DFGqxTLScSkQEroYb2zS9h2x7EltRAu5snxDAIhANQtiFvy1+rTOoufn4GTGnlSGZ8DXwbTTQ9heohtbBfl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.b174110", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.b174110.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7aae3617d36328da7a018b313ed4c02b/rolldown-pluginutils-1.0.0-beta.9-commit.b174110.tgz", "_integrity": "sha512-l54AGmj0y8ytD0iB0XDR9FfJlTa6551eB25MV1Jnmk6dSY3XhLUJ4dqCjHyhhcteki8ofvusYjydVHkxQKQnbQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.b174110_1748347469869_0.4105085925137688", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.ca4e9dd": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.ca4e9dd", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.ca4e9dd", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "1937902e1f9cb41129461bb8879f05b5163cd0cc", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.ca4e9dd.tgz", "fileCount": 6, "integrity": "sha512-PJGChwTDnz3+06qBUBYUCE0fkbQWEEbGQUs0nO2PrjI1Xvink120JzSq0ZiiWSRT3dBrde3UrZVlZuTg0KAOnw==", "signatures": [{"sig": "MEYCIQCPcsVlCrVKcRL15q7poqkQbydCpRBuHcsY5ZmkUTDcYwIhAORlr2L05548B1ddb/fKE6k7YgA0HWAGfqo753yryuUW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.ca4e9dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.ca4e9dd.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/d8c36d1bec6957d2d4ab87451f9e07f0/rolldown-pluginutils-1.0.0-beta.9-commit.ca4e9dd.tgz", "_integrity": "sha512-PJGChwTDnz3+06qBUBYUCE0fkbQWEEbGQUs0nO2PrjI1Xvink120JzSq0ZiiWSRT3dBrde3UrZVlZuTg0KAOnw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.ca4e9dd_1748363203291_0.6124844293420477", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.9-commit.0ec9e7d": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.9-commit.0ec9e7d", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.9-commit.0ec9e7d", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "5289e6db64475e250f84de7ea0bfafba4f821d80", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9-commit.0ec9e7d.tgz", "fileCount": 6, "integrity": "sha512-hMrblfoHK3yTyv/Losh6ZLhAgFfYY99vbsDO5eqWZGPYL1U8DNR9CKIIReUsJ8h1Al8q8S6K1lJXyPmFlDxxgw==", "signatures": [{"sig": "MEUCIQCpXNzKauIzETuTJ3yjiqXhF0HaadouSzFg/CuZ1KvPZAIgSHE13hWhePDxISgmjutfE4Fhiw6WdZrhBn0+APsY46g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.9-commit.0ec9e7d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23875}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.9-commit.0ec9e7d.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/8ceaac41619c7ee49c2003b0886b75ae/rolldown-pluginutils-1.0.0-beta.9-commit.0ec9e7d.tgz", "_integrity": "sha512-hMrblfoHK3yTyv/Losh6ZLhAgFfYY99vbsDO5eqWZGPYL1U8DNR9CKIIReUsJ8h1Al8q8S6K1lJXyPmFlDxxgw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.9-commit.0ec9e7d_1748391585656_0.6197295493074002", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "5ee8737232ee926bbe51e62e5d8d09fdf2b67a4d", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10.tgz", "fileCount": 6, "integrity": "sha512-FeISF1RUTod5Kvt3yUXByrAPk5EfDWo/1BPv1ARBZ07weqx888SziPuWS6HUJU0YroGyQURjdIrkjWJP2zBFDQ==", "signatures": [{"sig": "MEYCIQDyhUW8M2AdCgKoei/moqT1h1AOGCz+kojeD7Qur3qnBgIhAN9uW86Bn0CpisYOINA8SWUqzcjWNYhkGooItV+tHLPW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23861}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/118eaae384c9f56b6344e4c9cb646b08/rolldown-pluginutils-1.0.0-beta.10.tgz", "_integrity": "sha512-FeISF1RUTod5Kvt3yUXByrAPk5EfDWo/1BPv1ARBZ07weqx888SziPuWS6HUJU0YroGyQURjdIrkjWJP2zBFDQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10_1748439765010_0.6887434261478873", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.ac4e5db": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.ac4e5db", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.ac4e5db", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "15339d0d4c83281db4fbc61303bb442c685cfb6e", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.ac4e5db.tgz", "fileCount": 6, "integrity": "sha512-NQOrTZGpcq9uHF7BEP4rFH73JuUrvFhKjs8vLaE9pCFCEl5GVrhdaLXNtQp0zIK1AjZIvS9cdlqTvU7XRrzqqw==", "signatures": [{"sig": "MEYCIQC3ck6nS0c7RFYAg9q4aQUJfWDmIoO/v4RkxTwikd6MbAIhAMjHppb2juIH5boyX24lnZXA/mNdKVK7KnFzP5etspKi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.ac4e5db", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 23876}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.ac4e5db.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/cf1f288116f8a5369c7dca23da8acbb4/rolldown-pluginutils-1.0.0-beta.10-commit.ac4e5db.tgz", "_integrity": "sha512-NQOrTZGpcq9uHF7BEP4rFH73JuUrvFhKjs8vLaE9pCFCEl5GVrhdaLXNtQp0zIK1AjZIvS9cdlqTvU7XRrzqqw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.11.11", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.ac4e5db_1748477978484_0.16514132364200274", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.87188ed": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.87188ed", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.87188ed", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "875715cca17005c1f3e60417cec7d7016b12550d", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.87188ed.tgz", "fileCount": 6, "integrity": "sha512-IjVRLSxjO7EzlW4S6O8AoWbCkEi1lOpE30G8Xw5ZK/zl39K/KjzsDPc1AwhftepueQnQHJMgZZG9ITEmxcF5/A==", "signatures": [{"sig": "MEQCIBN36dO3BMglADS0vg/7vmLKPbKmWq7ChbL0kRNXfQ7EAiB7rGG0lLk5llx9yWfnZ922y5fnY7aguTQSvMuQ2h15Qw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.87188ed", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.87188ed.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/03ed652f17d2d967f89ba52820229d3b/rolldown-pluginutils-1.0.0-beta.10-commit.87188ed.tgz", "_integrity": "sha512-IjVRLSxjO7EzlW4S6O8AoWbCkEi1lOpE30G8Xw5ZK/zl39K/KjzsDPc1AwhftepueQnQHJMgZZG9ITEmxcF5/A==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.4", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.87188ed_1748564403858_0.4487115315609469", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.81375fe": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.81375fe", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.81375fe", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "4fc5f2d02c58ff6cdd098327679a90ff473cc0c9", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.81375fe.tgz", "fileCount": 6, "integrity": "sha512-1EM0Wq3yQWGV991iCbV2qJKx0ShmdBuCIkZiE5ZDEOVCvmoP0TwTPn7zWfz2TBFv/UuxAksk8KyUpoi77B8xbg==", "signatures": [{"sig": "MEQCIA2WV6hq1ko2eBytJyy/wA8On0EIJwtTo7awColyJsKDAiAf8eqlVffY/X6MXlbFK4vHwxxZPLlUd4pwCri6fo9iag==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.81375fe", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.81375fe.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/46b288dceccf9db4f409bbb785def57f/rolldown-pluginutils-1.0.0-beta.10-commit.81375fe.tgz", "_integrity": "sha512-1EM0Wq3yQWGV991iCbV2qJKx0ShmdBuCIkZiE5ZDEOVCvmoP0TwTPn7zWfz2TBFv/UuxAksk8KyUpoi77B8xbg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.5", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.81375fe_1748650766536_0.8273842858227174", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.2c4c2a8": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.2c4c2a8", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.2c4c2a8", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "e822366ee3d938faac9a447ad372ad981e347b40", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.2c4c2a8.tgz", "fileCount": 6, "integrity": "sha512-07gXfcVwxs0tAw0+PI4ziRoEpebDli+Hr5pG7rq3Sc/Ny3fKki0aiQfSH4AnUAUOshTe5l3fVFvvCWVck1tLTQ==", "signatures": [{"sig": "MEYCIQDJ5GiIh6ERdcFTSMggQkJbngCGu3IWpBu7t4Bp/7twTQIhAPcg/8brkT1COb1IzpP8prGyT4S5ms7b8uVc2Ydl0Gss", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.2c4c2a8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.2c4c2a8.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c669a6646767f3d0f55bef357429a6c4/rolldown-pluginutils-1.0.0-beta.10-commit.2c4c2a8.tgz", "_integrity": "sha512-07gXfcVwxs0tAw0+PI4ziRoEpebDli+Hr5pG7rq3Sc/Ny3fKki0aiQfSH4AnUAUOshTe5l3fVFvvCWVck1tLTQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.5", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.2c4c2a8_1748770129375_0.5190549797890309", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.885ee53": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.885ee53", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.885ee53", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "b181bb974826bab65d55a57736d11177c35595c1", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.885ee53.tgz", "fileCount": 6, "integrity": "sha512-1/DpP+/pHksDTWB4yxtLMWQjCshVzLxRVci8VLbG/6tgzqgAHeK56g9a2rHAonH240cO1cZ0JZSkfcJb9XnrwA==", "signatures": [{"sig": "MEQCIHDGgpyAm7qfceHVNkwv1MNwHRtXHyIX8secq2iG0lMlAiBiUZvxWsR0C6HdvbTPTAb/LjE+itJ1ZUk4KIjcKGDhHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.885ee53", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.885ee53.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/46a926c58cfb666ed342b9435a962ac5/rolldown-pluginutils-1.0.0-beta.10-commit.885ee53.tgz", "_integrity": "sha512-1/DpP+/pHksDTWB4yxtLMWQjCshVzLxRVci8VLbG/6tgzqgAHeK56g9a2rHAonH240cO1cZ0JZSkfcJb9XnrwA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.885ee53_1748797542931_0.0940761344672374", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.bf212da": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.bf212da", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.bf212da", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "420cf00fd5b8a8c3ab1b0533b0b33b1cd20bb24d", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.bf212da.tgz", "fileCount": 6, "integrity": "sha512-ydTqRsJGNaUsKBv/O7NWdKETk40KHB9nh1DJBEEkbek4gKwhrL29eR5t0Xh/bKw+w3Q2rVEdq3ToPx5qmHXtjA==", "signatures": [{"sig": "MEUCIQDNvxZ/FGCElEI/uiuAhwovxbX1roMMfQQ1nBOQowvbHQIgWm2sKE/Q9iNvYoEd8U9bKyyKI6JpKkrzWoenp/aocjw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.bf212da", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.bf212da.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/ca6fc91086d9e859519e405c17e726dd/rolldown-pluginutils-1.0.0-beta.10-commit.bf212da.tgz", "_integrity": "sha512-ydTqRsJGNaUsKBv/O7NWdKETk40KHB9nh1DJBEEkbek4gKwhrL29eR5t0Xh/bKw+w3Q2rVEdq3ToPx5qmHXtjA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.bf212da_1748823603256_0.0447718472664036", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.174c548": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.174c548", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.174c548", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "2036c7790293aafc4f0649ca89f3717f5995a2a3", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.174c548.tgz", "fileCount": 6, "integrity": "sha512-KPEn79vaz3R6/jQGWsGVpOYnZT1Ro+fDRC63JEtbskHGzg650ZQk/+ou9v6g2GZmIEy42zTSsCACCzd2v4xFmw==", "signatures": [{"sig": "MEUCIDUb4MAeSuAr0mUssva5vA5delgRAwJLkNHrBfGs13WnAiEAhKU9xaFVQM2zYel0fkw1pHF09JHjyEczTCzj7lCTcXI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.174c548", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.174c548.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/db35a581cc576b452610915f659cec1d/rolldown-pluginutils-1.0.0-beta.10-commit.174c548.tgz", "_integrity": "sha512-KPEn79vaz3R6/jQGWsGVpOYnZT1Ro+fDRC63JEtbskHGzg650ZQk/+ou9v6g2GZmIEy42zTSsCACCzd2v4xFmw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.174c548_1748846708223_0.5373203949247687", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.10-commit.229c919": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.10-commit.229c919", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.10-commit.229c919", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "d6a627e622be61cd12793669172bf091c58d281c", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.10-commit.229c919.tgz", "fileCount": 6, "integrity": "sha512-8qxbHZtj7DddhpCoaPNqwfxFxfN+rzA5W25veCHeEnub2zJOwB4gS6duilQOhp/EM9wMjhjhlA0Q6tJnp2HAnQ==", "signatures": [{"sig": "MEYCIQDK9B3hBYfWTWmnykOKvDvfYSayi77H+kH5kAsy5ww+9gIhAOy/RTkIFPBwCCxqqFhBkNkD1HbOtB6OJ4m4DAPOPreL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.10-commit.229c919", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.10-commit.229c919.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/a486f70640bd6257cc02160d16e23b44/rolldown-pluginutils-1.0.0-beta.10-commit.229c919.tgz", "_integrity": "sha512-8qxbHZtj7DddhpCoaPNqwfxFxfN+rzA5W25veCHeEnub2zJOwB4gS6duilQOhp/EM9wMjhjhlA0Q6tJnp2HAnQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.10-commit.229c919_1748910005008_0.8086176029861607", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.11": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.11", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.11", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "1e3e8044dd053c3dfa4bbbb3861f6e180ee78343", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11.tgz", "fileCount": 6, "integrity": "sha512-L/gAA/hyCSuzTF1ftlzUSI/IKr2POHsv1Dd78GfqkR83KMNuswWD61JxGV2L7nRwBBBSDr6R1gCkdTmoN7W4ag==", "signatures": [{"sig": "MEUCICdIjoyxmnMS75psvQHhOA2FTk0q6LeGc2Lk8C6p+lJFAiEA5PIzzIyx2Px8PLWMibTeAgIrMYybFzX7c8Y8QINGnBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.11.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/1dbbab0545dde558234175580d6301b6/rolldown-pluginutils-1.0.0-beta.11.tgz", "_integrity": "sha512-L/gAA/hyCSuzTF1ftlzUSI/IKr2POHsv1Dd78GfqkR83KMNuswWD61JxGV2L7nRwBBBSDr6R1gCkdTmoN7W4ag==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.11_1749017099432_0.7881668895597893", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.11-commit.83d4d62": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.11-commit.83d4d62", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.11-commit.83d4d62", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "a24a45a960a548ca9a562d1f1949fb140fceb25e", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11-commit.83d4d62.tgz", "fileCount": 6, "integrity": "sha512-1sT61QX+bwpzomBhzHDrHPVHWfSe39n5TAQgMzbMnB5aVhxhveHsh/LqLBm406sreHL2qLhYaluSTQyyAHpODg==", "signatures": [{"sig": "MEYCIQDRohe02F1qEQ0+szG4kpsBgNP+vI5cUkWhEfu9OMsssQIhAJZIlb4vEBhl/QeZSSa0z4TJ3sRh/2yuGShJmhbqbt3O", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.11-commit.83d4d62", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.11-commit.83d4d62.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/c5d91513fc061ec510cc2b4cc24b4c23/rolldown-pluginutils-1.0.0-beta.11-commit.83d4d62.tgz", "_integrity": "sha512-1sT61QX+bwpzomBhzHDrHPVHWfSe39n5TAQgMzbMnB5aVhxhveHsh/LqLBm406sreHL2qLhYaluSTQyyAHpODg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.11-commit.83d4d62_1749018998450_0.9773486183380677", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.11-commit.9abc457": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.11-commit.9abc457", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.11-commit.9abc457", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "d0997433521831b0422e7f103a2c9c717b4284d0", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11-commit.9abc457.tgz", "fileCount": 6, "integrity": "sha512-Jzq9CXeeWJJi1AHSsx9r1RGukrx2cBPfqyZQiUYcvu6mfDfk9w5MdTLu2xnMX1qbmmERZdz4lbHtyVs9GFIAOQ==", "signatures": [{"sig": "MEQCIA2Hy3RFWXBfxJVBM+aMDDtrvkSFwxZTuJGERhfnZCJDAiANKLaIXnHDbDJfaEdUmqv+FmdHIoHiVXqYCM7nhlTeDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.11-commit.9abc457", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.11-commit.9abc457.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/01790d65cd684d8254c707686f7513d5/rolldown-pluginutils-1.0.0-beta.11-commit.9abc457.tgz", "_integrity": "sha512-Jzq9CXeeWJJi1AHSsx9r1RGukrx2cBPfqyZQiUYcvu6mfDfk9w5MdTLu2xnMX1qbmmERZdz4lbHtyVs9GFIAOQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.11-commit.9abc457_1749070275272_0.6023475953415411", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.11-commit.f051675": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.11-commit.f051675", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.11-commit.f051675", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "a98c9bb9828ce7c887683fbccdf7ae386bd775b3", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11-commit.f051675.tgz", "fileCount": 6, "integrity": "sha512-TAqMYehvpauLKz7v4TZOTUQNjxa5bUQWw2+51/+Zk3ItclBxgoSWhnZ31sXjdoX6le6OXdK2vZfV3KoyW/O/GA==", "signatures": [{"sig": "MEQCIDAp09KLs2RmWLeNH8ZmXOOcgI9ea/Rv2QZWC4Rvh1KZAiBzGLNcyyKq/CIwp8a28Gl4V/5XPblSMjvOlnFTBUNSKQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.11-commit.f051675", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.11-commit.f051675.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7195583453c1364fbc6455eb0c3e7433/rolldown-pluginutils-1.0.0-beta.11-commit.f051675.tgz", "_integrity": "sha512-TAqMYehvpauLKz7v4TZOTUQNjxa5bUQWw2+51/+Zk3ItclBxgoSWhnZ31sXjdoX6le6OXdK2vZfV3KoyW/O/GA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.6", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.11-commit.f051675_1749075156639_0.5253823659027785", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.11-commit.0a985f3": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.11-commit.0a985f3", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.11-commit.0a985f3", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "6cb1aa3699a29e3becc9315c4afd9f778d1f38ab", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11-commit.0a985f3.tgz", "fileCount": 6, "integrity": "sha512-BzOULtKKG5aXllquK5TQKwonut+cN7KtWSt9UUAwlipWyNPKViJs+vFVTBwdvgSsHrWefNVjHkC9rO1eeYKkDA==", "signatures": [{"sig": "MEUCICexGfhIaxK1ruDnyTWo4IrJy+gyyImLocXWhceDrN8EAiEA9YeLhjDMRQP6XAPiYNUG9spPhJfYHDs/H9YdeMZK/R0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.11-commit.0a985f3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.11-commit.0a985f3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/db8db359851e5a531c4a3ec60195bb95/rolldown-pluginutils-1.0.0-beta.11-commit.0a985f3.tgz", "_integrity": "sha512-BzOULtKKG5aXllquK5TQKwonut+cN7KtWSt9UUAwlipWyNPKViJs+vFVTBwdvgSsHrWefNVjHkC9rO1eeYKkDA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.11-commit.0a985f3_1749161670438_0.4682623164156341", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.12": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.12", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.12", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "371370bf58907836a3d7c4bd1bd16ff9f8d4105b", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.12.tgz", "fileCount": 6, "integrity": "sha512-VFBZWbTqklrtkHvm4LCEH1G7+ybldM8nvOFpXwOUNRtK1vzosxm6r7F/hbr2ISmMrUbBJO59S5JJKe5CsKeOMQ==", "signatures": [{"sig": "MEUCIQCBfgUbukUXidd9yn1ruWZxk9GJc0XNdzTe1vWooUVMLQIgOFSi8pa4B0F6m9F6pTjNFrcaVn83/Hp0Xtiyj7OINPA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.12.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9bacd1f632be2f69935cc53eee6f1828/rolldown-pluginutils-1.0.0-beta.12.tgz", "_integrity": "sha512-VFBZWbTqklrtkHvm4LCEH1G7+ybldM8nvOFpXwOUNRtK1vzosxm6r7F/hbr2ISmMrUbBJO59S5JJKe5CsKeOMQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.12_1749221428373_0.5346692293026394", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.12-commit.ee57cb6": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.12-commit.ee57cb6", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.12-commit.ee57cb6", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "da41b584c82297cfde7a2a2f9e395e6453d5952f", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.12-commit.ee57cb6.tgz", "fileCount": 6, "integrity": "sha512-lUhYry6/suPbtCSIMRvpY7LtJBul/lQKfVRXaILAL2nE8xvyVysGDLmJCVDD5pgpIHS3r5HmYzc/xBLHYzUsnA==", "signatures": [{"sig": "MEUCIQDl8zi6NOo9CinuMbzvRhBvK8P7q0BuxyWaZ9w1iG6iVQIgDbioWjym1YxCeZ57aaDXEOzF8I46PwrQQqC7JLvSmvY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.12-commit.ee57cb6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.12-commit.ee57cb6.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/4907960245321e410b820218fb912222/rolldown-pluginutils-1.0.0-beta.12-commit.ee57cb6.tgz", "_integrity": "sha512-lUhYry6/suPbtCSIMRvpY7LtJBul/lQKfVRXaILAL2nE8xvyVysGDLmJCVDD5pgpIHS3r5HmYzc/xBLHYzUsnA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.12-commit.ee57cb6_1749255651548_0.433541896058258", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.13": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.13", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.13", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "7e86b13181ee467e2007f60c0fd767534e7576cc", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.13.tgz", "fileCount": 6, "integrity": "sha512-/9TBv7Ir9ojO1mDlTy35X0GSGqvP+aRa44i2fciAK/EEJeimvJyL6eN2Ug2RwXEGFVumgZh231PeykYjo2WBtw==", "signatures": [{"sig": "MEQCIFcCixawSy7Uq29nhJG5Z43HXbMnBM2tFh0NFPSCnlMPAiAFpFUd7CbESAdgXxA/uuk54Sz5CzJ9/3kQuQyUueSHYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.13.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/75c29c61af57d10f5d527c72506e486b/rolldown-pluginutils-1.0.0-beta.13.tgz", "_integrity": "sha512-/9TBv7Ir9ojO1mDlTy35X0GSGqvP+aRa44i2fciAK/EEJeimvJyL6eN2Ug2RwXEGFVumgZh231PeykYjo2WBtw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.13_1749311464367_0.3258516172411412", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.13-commit.6bb8d8d": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.13-commit.6bb8d8d", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.13-commit.6bb8d8d", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "f9d20cb7b2f0b9ad19bcdbdc2390204c24f8f23f", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.13-commit.6bb8d8d.tgz", "fileCount": 6, "integrity": "sha512-ESXz6c+FJDu49YAIX7b1ISwPOTLFsTNhdwE9zaNnv9UNWsHMyYDX3qqjqVR/8zRMQqlulOWgSp5I0U3R+vHBwA==", "signatures": [{"sig": "MEUCIFFEurEp1KgNHv8Whv9aQ/8I2yhVWzs+IELJeayx1hG1AiEAisF2z2bqNvi5M3nPIT/jRV4JAJvlaqNSRpw7LgqMpLs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.13-commit.6bb8d8d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.13-commit.6bb8d8d.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/7101c4fe6d633cd83f2f5e0cd581992c/rolldown-pluginutils-1.0.0-beta.13-commit.6bb8d8d.tgz", "_integrity": "sha512-ESXz6c+FJDu49YAIX7b1ISwPOTLFsTNhdwE9zaNnv9UNWsHMyYDX3qqjqVR/8zRMQqlulOWgSp5I0U3R+vHBwA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.13-commit.6bb8d8d_1749342034616_0.6490898389972823", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.13-commit.024b632": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.13-commit.024b632", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.13-commit.024b632", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "3f39f237fb35fc2f3c2065e75ac681a70b77d569", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.13-commit.024b632.tgz", "fileCount": 6, "integrity": "sha512-9/h9ID36/orsoJx8kd2E/wxQ+bif87Blg/7LAu3t9wqfXPPezu02MYR96NOH9G/Aiwr8YgdaKfDE97IZcg/MTw==", "signatures": [{"sig": "MEYCIQD35a+U2FPIPbNxW43jD76yGIJEaPTH3T2VJNNIrmoZ4gIhAIZPgBsFRFHJO9Wk4LZai/EbxN2GWX2+EXZyndLTe95w", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.13-commit.024b632", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.13-commit.024b632.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/9d7553763ea6b3487647811dd4f7bb7c/rolldown-pluginutils-1.0.0-beta.13-commit.024b632.tgz", "_integrity": "sha512-9/h9ID36/orsoJx8kd2E/wxQ+bif87Blg/7LAu3t9wqfXPPezu02MYR96NOH9G/Aiwr8YgdaKfDE97IZcg/MTw==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.13-commit.024b632_1749428435348_0.5867063204482197", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.14": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.14", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.14", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "b8eaee22fff44943f19f195de85f34be506c25fd", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.14.tgz", "fileCount": 6, "integrity": "sha512-Loy5RyDGXVDBWMIE0vKqb8L4wlVGaxO2gy8I0HsI+C2UHQ5t58cr+ZTX5KWR1WWzbHJLNrq8s9Rlrkh+q7qsFg==", "signatures": [{"sig": "MEUCIB555lMC2wxRIONjtxpORF7OZTsztdiyfnk4TZdLVBUMAiEAjRKWw89a/8yK6UZxp5EixQFv8sTP0kr51x+QldYjUSI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.14.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/29c37a70d1c200d73f3cdff172acf4a7/rolldown-pluginutils-1.0.0-beta.14.tgz", "_integrity": "sha512-Loy5RyDGXVDBWMIE0vKqb8L4wlVGaxO2gy8I0HsI+C2UHQ5t58cr+ZTX5KWR1WWzbHJLNrq8s9Rlrkh+q7qsFg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.14_1749544995561_0.8435491526117023", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.14-commit.12b8061": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.14-commit.12b8061", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.14-commit.12b8061", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "544e5775c841b320100b91c30b0443533ae3f35c", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.14-commit.12b8061.tgz", "fileCount": 6, "integrity": "sha512-a20DfVsjzRAInZbhoMI0fqvi9XVrAU9CZyZsbn3k0BJAGz3MoJKUl/z5Q3TcO3svlLB3lTN2K82cLWH+O0JK3g==", "signatures": [{"sig": "MEYCIQCN1xHaXvWPx4eYQfZfD+lUbCb4Uma2NZ+8WjIcLAbdYQIhAJp2zKXf6l/mRf53eTyZcGt1L1UHYBFt/phVpR0De6I6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.14-commit.12b8061", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24521}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.14-commit.12b8061.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/53d1655250f94423f1d344b4bebef430/rolldown-pluginutils-1.0.0-beta.14-commit.12b8061.tgz", "_integrity": "sha512-a20DfVsjzRAInZbhoMI0fqvi9XVrAU9CZyZsbn3k0BJAGz3MoJKUl/z5Q3TcO3svlLB3lTN2K82cLWH+O0JK3g==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.14-commit.12b8061_1749643948159_0.30712648413604726", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.15": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.15", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.15", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "fd82a9bb7d5349fd31964985c595b260d5709d74", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.15.tgz", "fileCount": 6, "integrity": "sha512-lvFtIbidq5EqyAAeiVk41ZNjGRgUoGRBIuqpe1VRJ7R8Av7TLAgGWAwGlHNhO7MFkl7MNRX350CsTtIWIYkNIQ==", "signatures": [{"sig": "MEQCIAb1OWQN/T7/YSBiU230anMfd6/k31bQU6fyJNNNQJa+AiANOXAAWQjLJFOiDG9RHkvWJ+CyEFmbIAWSUQ801gUjyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 24506}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.15.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/527a233950aa2de40dc36750a6b6a7f6/rolldown-pluginutils-1.0.0-beta.15.tgz", "_integrity": "sha512-lvFtIbidq5EqyAAeiVk41ZNjGRgUoGRBIuqpe1VRJ7R8Av7TLAgGWAwGlHNhO7MFkl7MNRX350CsTtIWIYkNIQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.15_1749645399112_0.1929244205263878", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.16": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.16", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.16", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "596b55c685e25c825e18c54a6d4624a7a2865fea", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.16.tgz", "fileCount": 6, "integrity": "sha512-w3f87JpF7lgIlK03I0R3XidspFgB4MsixE5o/VjBMJI+Ki4XW/Ffrykmj2AUCbVxhRD7Pi9W0Qu2XapJhB2mSA==", "signatures": [{"sig": "MEUCIAyV7fVKKELA7CpO7vHOfLpman/09JKzfp70qIAlP5u2AiEA1Glw5GOJIXcLnbtZEKMN3MvsuCIi3lzlZ+9kM4b85ZM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.16.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>"}, "_resolved": "/tmp/def97f2a3db7f001102e791dfe453de8/rolldown-pluginutils-1.0.0-beta.16.tgz", "_integrity": "sha512-w3f87JpF7lgIlK03I0R3XidspFgB4MsixE5o/VjBMJI+Ki4XW/Ffrykmj2AUCbVxhRD7Pi9W0Qu2XapJhB2mSA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.7", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.16_1750001918501_0.8460214240243926", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.17": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.17", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.17", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "397007156d178fa26ea7e09f1cd8b1db42df1c8b", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.17.tgz", "fileCount": 6, "integrity": "sha512-i6p5fc1+lAmR3OHmNlv7/3PIY3EtuUu4kVARjkid38p7cmyIyqr0QFnA+k3xoB06wQUpBA91H1HFlRreZ2v5oA==", "signatures": [{"sig": "MEUCIDFKFxiV0E7lE20wbYPi5b/AzPKXyjuEA1YaDpnNzF8lAiEAp6ExIrHqZZoSRmtmgLzCab5PNTbaa9K+swHi9YsGpS8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.17.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/8cb511d80e2f236c061bcd789aa7b85d/rolldown-pluginutils-1.0.0-beta.17.tgz", "_integrity": "sha512-i6p5fc1+lAmR3OHmNlv7/3PIY3EtuUu4kVARjkid38p7cmyIyqr0QFnA+k3xoB06wQUpBA91H1HFlRreZ2v5oA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.8", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.17_1750167526128_0.2883213016179602", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.18": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.18", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.18", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "2875d7493c8b19f7b67ffb85689657545bb0be96", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.18.tgz", "fileCount": 6, "integrity": "sha512-sHG++r1AOeQrzp0Lm3w9TBuaMHty3rU4yCZ4Vd/s428dvv3eTIhuRqHPHJCBlVpZjOJ5b4ZcBPTyRCsDKFt2+w==", "signatures": [{"sig": "MEQCIE7ChkuH2/ntmsoy5EYE9M7k5fVSZyulR06GfRDU7o6QAiAp0T4wiQNo9UTWSnL9nMKvRWZGCkADpFN4lhWzbXh6NQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.18.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/02f399b8c440ce01b30609e2678ea680/rolldown-pluginutils-1.0.0-beta.18.tgz", "_integrity": "sha512-sHG++r1AOeQrzp0Lm3w9TBuaMHty3rU4yCZ4Vd/s428dvv3eTIhuRqHPHJCBlVpZjOJ5b4ZcBPTyRCsDKFt2+w==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.8", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.18_1750306843556_0.29294912804113626", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.19": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.19", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.19", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "fc3b95145a8e7a3bf92754269d8e4f40eea8a244", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz", "fileCount": 6, "integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==", "signatures": [{"sig": "MEUCIQCoYS9tzogqn9C7ahVZGdVyt7yTBNMbzvuxrnjsY0xX1wIgCswa14S03T5DzcoQUcIJ7fS3MjyiWxf02cUqrcjJdhY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.19", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.19.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/aa841d08ea6884199afa4a2ba8ea9f3c/rolldown-pluginutils-1.0.0-beta.19.tgz", "_integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.8", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.19_1750582852114_0.45357063127737307", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.20": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.20", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.20", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "aa9bf56011c3bc7d4f714933bb1ee67c5d2c1227", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.20.tgz", "fileCount": 6, "integrity": "sha512-PCLTsxf5Gvek1g7KqO+DYnhpqpSzHeSNGso+mmMnv9jm0dFBky1SZCzz0ZTGHstWwpTsmTlIRdlcB949f6PGGQ==", "signatures": [{"sig": "MEQCIGxmyTgbxX01s0zxUjSOcl4tcyIvN74cvr1e5FJnN8vhAiBjmz6cxNFzlgablesrFacEjCDAagUw6Dmv2MczS2kgIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.20", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.20.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/c772a206a727b07218fe4e88304150f6/rolldown-pluginutils-1.0.0-beta.20.tgz", "_integrity": "sha512-PCLTsxf5Gvek1g7KqO+DYnhpqpSzHeSNGso+mmMnv9jm0dFBky1SZCzz0ZTGHstWwpTsmTlIRdlcB949f6PGGQ==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.9", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.20_1750861690588_0.8223049249416736", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.21": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.21", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.21", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "b40b4b3b954f980cce8bbd75af62d025a9cc7e5a", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.21.tgz", "fileCount": 6, "integrity": "sha512-OTjWr7XYqRZaSzi6dTe0fP25EEsYEQ2H04xIedXG3D0Hrs+Bpe3V5L48R6y+R5ohTygp1ijC09mbrd7vlslpzA==", "signatures": [{"sig": "MEQCIADu/NE3xRooRf9hWromt4jK56P509/VCzB/pKOvGmmNAiBLfg9DFs/57mP+sv7AvqI0u/ttPvk8FBvNhf/Q0sWgJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.21.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/7774f7d18d1bfedf3783138f6b5b9bdd/rolldown-pluginutils-1.0.0-beta.21.tgz", "_integrity": "sha512-OTjWr7XYqRZaSzi6dTe0fP25EEsYEQ2H04xIedXG3D0Hrs+Bpe3V5L48R6y+R5ohTygp1ijC09mbrd7vlslpzA==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.9", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.21_1751006696802_0.7959099992921044", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.22": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.22", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.22", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "68de80a83c6570c3b907f3c52e9c0c9989bdd885", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.22.tgz", "fileCount": 6, "integrity": "sha512-/i+XBSHy+t8NacDNSucTckzPfzEa+zQVnZPxRp/Nr2q4xhGsZ01tN7AMRJVxmDkUKDzib0rteOUIn2x0mvk4eg==", "signatures": [{"sig": "MEUCIHRr6eGZ65PqTlfNWaL2aZ7etyR/VTjOCXRxEJHJTwgoAiEA2I+YTCmk61pGRJ22JQmjsHv0hFQRa7z8Ve8rv/8fO/s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.22", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.22.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/33bde0cc4f7cad892746a4b3ba107de7/rolldown-pluginutils-1.0.0-beta.22.tgz", "_integrity": "sha512-/i+XBSHy+t8NacDNSucTckzPfzEa+zQVnZPxRp/Nr2q4xhGsZ01tN7AMRJVxmDkUKDzib0rteOUIn2x0mvk4eg==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.9", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.22_1751261899772_0.3084909089488821", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.23": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.23", "license": "MIT", "_id": "@rolldown/pluginutils@1.0.0-beta.23", "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "homepage": "https://github.com/rolldown/rolldown#readme", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "dist": {"shasum": "f896db6e79da076e1cb3b2eb13d0009824e3cb38", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.23.tgz", "fileCount": 6, "integrity": "sha512-lLCP4LUecUGBLq8EfkbY2esGYyvZj5ee+WZG12+mVnQH48b46SVbwp+0vJkD+6Pnsc+u9SWarBV9sQ5mVwmb5g==", "signatures": [{"sig": "MEQCID0ETlICSgnelOt0VUOqcTyzPcs9GzK5PBS6r00M7JQNAiAGdAlMsK8oHf9P+lJ4SBxq1nbc8VOIUHBi9oZndIUiiA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.23", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 27432}, "main": "./dist/index.cjs", "type": "module", "_from": "file:rolldown-pluginutils-1.0.0-beta.23.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "vitest --typecheck", "build": "tsdown"}, "_npmUser": {"name": "rolldownbot", "actor": {"name": "rolldownbot", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/tmp/5324a40fdc7153b3c972cf9ad1452c32/rolldown-pluginutils-1.0.0-beta.23.tgz", "_integrity": "sha512-lLCP4LUecUGBLq8EfkbY2esGYyvZj5ee+WZG12+mVnQH48b46SVbwp+0vJkD+6Pnsc+u9SWarBV9sQ5mVwmb5g==", "repository": {"url": "git+https://github.com/rolldown/rolldown.git", "type": "git", "directory": "packages/pluginutils"}, "_npmVersion": "10.9.2", "directories": {}, "_nodeVersion": "22.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsdown": "0.12.9", "vitest": "^3.0.1", "picomatch": "^4.0.2", "@types/picomatch": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pluginutils_1.0.0-beta.23_1751358104966_0.566552257003377", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.0-beta.24": {"name": "@rolldown/pluginutils", "version": "1.0.0-beta.24", "license": "MIT", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/pluginutils"}, "publishConfig": {"access": "public"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "devDependencies": {"@types/picomatch": "^4.0.0", "picomatch": "^4.0.2", "tsdown": "0.12.9", "vitest": "^3.0.1"}, "scripts": {"build": "tsdown", "test": "vitest --typecheck"}, "_id": "@rolldown/pluginutils@1.0.0-beta.24", "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "homepage": "https://github.com/rolldown/rolldown#readme", "_integrity": "sha512-NMiim/enJlffMP16IanVj1ajFNEg8SaMEYyxyYfJoEyt5EiFT3HUH/T2GRdeStNWp+/kg5U8DiJqnQBgLQ8uCw==", "_resolved": "/tmp/94eea4d0190fcd852947c9cd8b84cb54/rolldown-pluginutils-1.0.0-beta.24.tgz", "_from": "file:rolldown-pluginutils-1.0.0-beta.24.tgz", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-NMiim/enJlffMP16IanVj1ajFNEg8SaMEYyxyYfJoEyt5EiFT3HUH/T2GRdeStNWp+/kg5U8DiJqnQBgLQ8uCw==", "shasum": "fa0b0fcd9c91c09d6eae9e497513cc553f9b500d", "tarball": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.24.tgz", "fileCount": 6, "unpackedSize": 27432, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@rolldown%2fpluginutils@1.0.0-beta.24", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDYxKb36cYjG4yPXOmMtVrZrqBCcH3T03OMnnC9BtxMLAIgeF7Vbno2oWTxu1ocG1VhHPd7RTxlJq8gxBRP+TMHr3A="}]}, "_npmUser": {"name": "rolldownbot", "email": "<EMAIL>", "actor": {"name": "rolldownbot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pluginutils_1.0.0-beta.24_1751705585578_0.5180782278060776"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-05-09T14:34:07.682Z", "modified": "2025-07-05T08:53:06.294Z", "1.0.0-beta.8-commit.56abf23": "2025-05-09T14:34:07.981Z", "1.0.0-beta.8-commit.f97bbba": "2025-05-09T15:09:26.987Z", "1.0.0-beta.8-commit.e5c11c6": "2025-05-10T00:16:43.273Z", "1.0.0-beta.8-commit.05b3e10": "2025-05-10T14:29:56.552Z", "1.0.0-beta.8-commit.2a5c6a6": "2025-05-11T16:41:03.531Z", "1.0.0-beta.8-commit.8951737": "2025-05-12T00:19:07.636Z", "1.0.0-beta.8-commit.66f4623": "2025-05-12T08:18:50.229Z", "1.0.0-beta.8-commit.d95f99e": "2025-05-13T00:19:08.708Z", "1.0.0-beta.8-commit.985af6d": "2025-05-14T00:16:49.601Z", "1.0.0-beta.8-commit.bf53a10": "2025-05-15T00:17:28.437Z", "1.0.0-beta.8-commit.709eb63": "2025-05-16T00:19:00.973Z", "1.0.0-beta.8-commit.53a64a8": "2025-05-17T00:17:34.511Z", "1.0.0-beta.8-commit.a29b7fb": "2025-05-17T18:02:52.051Z", "1.0.0-beta.8-commit.360c072": "2025-05-19T00:17:27.007Z", "1.0.0-beta.9": "2025-05-19T05:35:40.507Z", "1.0.0-beta.9-commit.43425a0": "2025-05-20T00:18:16.459Z", "1.0.0-beta.9-commit.76c39c6": "2025-05-20T08:44:04.487Z", "1.0.0-beta.9-commit.ce72026": "2025-05-21T00:17:23.161Z", "1.0.0-beta.9-commit.51df2b7": "2025-05-22T00:17:29.072Z", "1.0.0-beta.9-commit.aca15b3": "2025-05-23T00:17:08.478Z", "1.0.0-beta.9-commit.d91dfb5": "2025-05-24T00:16:25.135Z", "1.0.0-beta.9-commit.8371a90": "2025-05-25T00:17:31.783Z", "1.0.0-beta.9-commit.273d50e": "2025-05-26T00:17:29.238Z", "1.0.0-beta.9-commit.b174110": "2025-05-27T12:04:30.033Z", "1.0.0-beta.9-commit.ca4e9dd": "2025-05-27T16:26:43.490Z", "1.0.0-beta.9-commit.0ec9e7d": "2025-05-28T00:19:45.834Z", "1.0.0-beta.10": "2025-05-28T13:42:45.188Z", "1.0.0-beta.10-commit.ac4e5db": "2025-05-29T00:19:38.692Z", "1.0.0-beta.10-commit.87188ed": "2025-05-30T00:20:04.064Z", "1.0.0-beta.10-commit.81375fe": "2025-05-31T00:19:26.717Z", "1.0.0-beta.10-commit.2c4c2a8": "2025-06-01T09:28:49.561Z", "1.0.0-beta.10-commit.885ee53": "2025-06-01T17:05:43.125Z", "1.0.0-beta.10-commit.bf212da": "2025-06-02T00:20:03.461Z", "1.0.0-beta.10-commit.174c548": "2025-06-02T06:45:08.395Z", "1.0.0-beta.10-commit.229c919": "2025-06-03T00:20:05.162Z", "1.0.0-beta.11": "2025-06-04T06:04:59.684Z", "1.0.0-beta.11-commit.83d4d62": "2025-06-04T06:36:38.635Z", "1.0.0-beta.11-commit.9abc457": "2025-06-04T20:51:15.463Z", "1.0.0-beta.11-commit.f051675": "2025-06-04T22:12:36.819Z", "1.0.0-beta.11-commit.0a985f3": "2025-06-05T22:14:30.615Z", "1.0.0-beta.12": "2025-06-06T14:50:28.548Z", "1.0.0-beta.12-commit.ee57cb6": "2025-06-07T00:20:51.726Z", "1.0.0-beta.13": "2025-06-07T15:51:04.555Z", "1.0.0-beta.13-commit.6bb8d8d": "2025-06-08T00:20:34.845Z", "1.0.0-beta.13-commit.024b632": "2025-06-09T00:20:35.525Z", "1.0.0-beta.14": "2025-06-10T08:43:15.719Z", "1.0.0-beta.14-commit.12b8061": "2025-06-11T12:12:28.344Z", "1.0.0-beta.15": "2025-06-11T12:36:39.333Z", "1.0.0-beta.16": "2025-06-15T15:38:38.682Z", "1.0.0-beta.17": "2025-06-17T13:38:46.303Z", "1.0.0-beta.18": "2025-06-19T04:20:43.757Z", "1.0.0-beta.19": "2025-06-22T09:00:52.278Z", "1.0.0-beta.20": "2025-06-25T14:28:10.770Z", "1.0.0-beta.21": "2025-06-27T06:44:57.004Z", "1.0.0-beta.22": "2025-06-30T05:38:19.924Z", "1.0.0-beta.23": "2025-07-01T08:21:45.131Z", "1.0.0-beta.24": "2025-07-05T08:53:05.805Z"}, "bugs": {"url": "https://github.com/rolldown/rolldown/issues"}, "license": "MIT", "homepage": "https://github.com/rolldown/rolldown#readme", "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/pluginutils"}, "maintainers": [{"name": "broooo<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "rolldownbot", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}