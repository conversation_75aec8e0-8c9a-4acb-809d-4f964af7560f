{"_id": "magic-string", "_rev": "163-ea275bc72ed060ca116d5ac4198dba82", "name": "magic-string", "dist-tags": {"latest": "0.30.17"}, "versions": {"0.1.0": {"name": "magic-string", "version": "0.1.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "dist": {"shasum": "ca0013859e534323a9dcbf07bfcb0fd92bb13f22", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.0.tgz", "integrity": "sha512-YiPF8PtEBP2UW4BQ4ppK4YuYwei/L7Y1Ql2CY7RYNJ69ThHrh/tI1WecD3QP0P4ifU6n5h9pWD/smsjXcFskzA==", "signatures": [{"sig": "MEUCIQC53eD1YcJXeHzCfGyuiXNFE/2M24WjSbCVkr/ZGA8U/wIgLhqtQCSz+D5mUchMiSBJSSFcrHCXM8FzydmvcIX8WEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ca0013859e534323a9dcbf07bfcb0fd92bb13f22", "gitHead": "87081b5265d5c39ac49bee887dc30499bd4bdc05", "scripts": {}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}}, "0.1.1": {"name": "magic-string", "version": "0.1.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "dist": {"shasum": "958bfb152a10c5da049772de93cf9ef69588357a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.1.tgz", "integrity": "sha512-Gv5kyMtRdty6UPBWFvm9o8QFEjBH4t4s73VwYCrMZLaAfxU+B0NbK2Tq1rmXL5caAfj1D4GY3YTW/SzTXcKNkA==", "signatures": [{"sig": "MEUCIQCeBH+5EYOczHwgFH01LAwO8xdewHYm17PkYD/ycJzB4QIgbhw1+jsVRJbkq3R+5com7ancmlBX4pk1sbstd+SsBmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "958bfb152a10c5da049772de93cf9ef69588357a", "gitHead": "e2a54d64a54f5c8b5cadf4ddf968f4e6fd5a5cde", "scripts": {}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}}, "0.1.3": {"name": "magic-string", "version": "0.1.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "dist": {"shasum": "9ea47e359623faec201d53aba19b19a553ba2936", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.3.tgz", "integrity": "sha512-d<PERSON><PERSON>ZcJSkxOGbxwwe/NDIHsp1SsvyPovR06iu/LDcX5PHZGk9UZm8FS90phnVXbgL4XL54AL/XSM3cD9BnM2fg==", "signatures": [{"sig": "MEYCIQCu9C+UoslwaS02x2Hx03k7X9eX0/8fSH/lTeAXUonuVQIhAKwTxbvxnGuqMTYL661fPVn8ieoMJH64Zx9o6BjS+05u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9ea47e359623faec201d53aba19b19a553ba2936", "gitHead": "586769f73d97e9ab4e8ed28852c7de39eed10b88", "scripts": {}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}}, "0.1.4": {"name": "magic-string", "version": "0.1.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.4", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "dist": {"shasum": "6c9d3cb0a432946352fde843ef6676466e66580a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.4.tgz", "integrity": "sha512-oqSmSgG2EohQ2duL2/9fgEqzxq7tz8v6dWh9cESLlRgDY4yPvXErPgwuGFQVYGdFsEi8fVTAEuIZqHa0rUDKrw==", "signatures": [{"sig": "MEQCIEI51F0cvP97P0usjipsVBdRFYiNIUllRdJIhKjKGy1WAiBJMep6TIfbjhCsdHgNah+MCFe7CCKysCSVTpLCXFsi0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6c9d3cb0a432946352fde843ef6676466e66580a", "gitHead": "4a81aa6ffa731f1e54ae0530002847bc8a55457c", "scripts": {}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "_npmVersion": "1.4.28", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"source-map": "^0.1.40"}}, "0.1.5": {"name": "magic-string", "version": "0.1.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.5", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "72ae92918368033fd42c15e60940a8ad1fe557d1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.5.tgz", "integrity": "sha512-qhkz6oDBkXfzfk+OHIwP+qkPA6AP8DcC/QpcLBxNAm7JUV2hROmbtzO5NaIrlVr++HQgqvErBffgyMll04Kouw==", "signatures": [{"sig": "MEYCIQDNmOHuYOWto30M90pHXuOCabMx49yPZqBVvMEBUuO7BgIhALOEmUZ9QfFB6rD1Ib0IRODifGb7t0JnQ6bHd19J45XJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "72ae92918368033fd42c15e60940a8ad1fe557d1", "gitHead": "7e4d3b3737ef980b62df3b549a7e00ed89d60167", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40"}}, "0.1.6": {"name": "magic-string", "version": "0.1.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.6", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "925239a7635058832d40c270bef1026cdbd7be1a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.6.tgz", "integrity": "sha512-+X/9kY/trs+rkv2sLDT5QpCHGqTRNELFttWksbV+0MoT6cKUJsF6Alm3fNtN7oG0dDoT+/yLp5YRddzfID/f5w==", "signatures": [{"sig": "MEYCIQCZh68Zu+Q6PRA95lcZKXPaWhEINzGMi7qqmL7sp5Xi3AIhAJ9Sc6IA5TUKmjKrdiBdSm40K4xnEUdGqWMzo9i10wAF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "925239a7635058832d40c270bef1026cdbd7be1a", "gitHead": "e64c287559e336c145be40aeb4e4f00635034068", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40"}}, "0.1.7": {"name": "magic-string", "version": "0.1.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.7", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5cebf3aa48ac7743bfb4ca43de07c3ae08822a3a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.7.tgz", "integrity": "sha512-t42SARfRF/hjAhl12H35j4bDxzoIOvndETzsBL71JqyALBn860b229pLQth/tQuJL/wNiPWPimx7HBC5Zqf/WA==", "signatures": [{"sig": "MEUCIAYwwBFawOCIt1LtK6cNV7dpRBCHHS2R4dAcP/nELL0+AiEAn4S3x/lTUKPnuOkRHLzeJOdg6Xr1Q3BjJo82XIRanDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "5cebf3aa48ac7743bfb4ca43de07c3ae08822a3a", "gitHead": "a684ef34df78c55d62f7f90bb2441890d928c219", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.1.8": {"name": "magic-string", "version": "0.1.8", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.8", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "172e7cea3722e38bf8690cd778234bcb21147d13", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.8.tgz", "integrity": "sha512-i/ovvM5ICN1iwHrxOEdu28qCS10Uu8UL+kHvriaKQioLgAbgerTr+pw/CPDSEctpO1f72anBpOJPKk+Ea4gvcQ==", "signatures": [{"sig": "MEUCIQDrhDMfonQFhmy3l4RC3eaXUliqlbOy9AxXeJ3X84gNgQIgK7RE5idhxQvXJsqM1XEJb3+N3sqIya+rgUExNOGQp/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "172e7cea3722e38bf8690cd778234bcb21147d13", "gitHead": "2de3dac6b0c311e8354faeca66299af50bba8b84", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.1.9": {"name": "magic-string", "version": "0.1.9", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.9", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b2a3f74097eb59c672230df495eef017e0bafa06", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.9.tgz", "integrity": "sha512-2rPiK41eA/kJY6fkQnkQNeWGGN/we3+LfxLMtkEY+cVE40pZ52AYuuaQdq79zod0jffdT4Vtzvr6JCrEKodwYQ==", "signatures": [{"sig": "MEUCICrAZOlMrB1EVdPsoe2214qJYv9xQVm0BqTC4PpX9nQKAiEA3iXKJXp1xzOQq/SESZSr7Au8scI1thasSnl1OBgE6AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "b2a3f74097eb59c672230df495eef017e0bafa06", "gitHead": "d93e4b4d815a925a93fc99450b632aafc3976fa9", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.1.10": {"name": "magic-string", "version": "0.1.10", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.1.10", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "222833335ffdf36e5a08f2049b6ddb9ae5e43cc9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.1.10.tgz", "integrity": "sha512-MjO3g6qUtFEh7jBp+MAek4sDUfx+FF6tKAhD2UIOC/jvWcVVIn+j1VYmB6fmXZyNogKBVaY5NkFT6GBFDAWf3Q==", "signatures": [{"sig": "MEUCIQDCJbOWbmAzPyoZ7NOGo8jysy2QNIhhQEp+pbBxF+WlPgIgZeBHncZcWIuqMOwu9J3/Mhq1qSJTA2m0KIiSTYe81zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "222833335ffdf36e5a08f2049b6ddb9ae5e43cc9", "gitHead": "23dcd79b13f8e243eeabf6cddcd2e59ca3ef76fa", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.2.0": {"name": "magic-string", "version": "0.2.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b4300ff015b933fd7011933903d66cc36aeddd0e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.0.tgz", "integrity": "sha512-GTrjGJVoq6vdbvqIfKbGq3v69tJ/ZT5qHTzfVurjE+QwAsoeo79vwLaPovkHlgfQd7KXs4Keg/rl5wJQLCwvpw==", "signatures": [{"sig": "MEYCIQC9DyaZPOVbZyEGzPpk04lhNq/xyDbEkxVp706p2DtmZQIhAIF+eNtZOa19KgruQHs9tyH+a3pvWPgEFSGQP3Y5UJZ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "b4300ff015b933fd7011933903d66cc36aeddd0e", "gitHead": "e378296c7a723804dee62ca4829b5d321d128965", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Suppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a source map at the end of it. You've thought ab", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.2.1": {"name": "magic-string", "version": "0.2.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "2116671c2b4b445a49e3344660064f60739e01bd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.1.tgz", "integrity": "sha512-UL8FpYba6oj9GzGhP9yt7QKoJnk4SA50wUil/oT48ysc0EJlKi7YvvhjtqLqkR3iO8nGtYGFxlhRBHCQNXjtUQ==", "signatures": [{"sig": "MEUCIHIPMVDVf/t4sVA5oQ3R8jH3pK+WM2/VhZp8mCWhCGYCAiEAtPKM+D3FVWMVrgZgdujitWBrnxfAsjdg0dAzRfa82kw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "2116671c2b4b445a49e3344660064f60739e01bd", "gitHead": "7b01e469dbc28981c371cf47dbd7a68a695ad235", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.2.2": {"name": "magic-string", "version": "0.2.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "0266f7ee3d9c45b45816d50af8c5b37f761a90e4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.2.tgz", "integrity": "sha512-NziWhQcu+19enDrQl/Ac04iuPBX6lLAsUAEsmHtiHoXZsBi9dxs5yN5JJUj1hlHNCni+4ht+Zs1MsdlOC+ykxg==", "signatures": [{"sig": "MEUCID7aW+YhZLD28pufNEXA4HFJ/tBRWOJ5jLyBtn3zCqCMAiEA3MVf3mwlRsrlH7Seq4cnmG01cjG0l7K/AS639sECSXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "0266f7ee3d9c45b45816d50af8c5b37f761a90e4", "gitHead": "30e7230f96ec1e1422057267aae1f003e5bf95d9", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.2.3": {"name": "magic-string", "version": "0.2.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "8288f6d0777aa4333f46cf6de696cf8d5e1d9809", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.3.tgz", "integrity": "sha512-i0YDH8zm1maXjC8WeZF5Ra2srG1Ay5+Xk8Xfgn6LJl8Fo0khCuY3OdktA3TUv34ZkX0JfZJOSenm2pP/KdZVhw==", "signatures": [{"sig": "MEYCIQCuCP6gpT74b1+NBC5Az78c5C9d1mqn3iE9eC64+uPxRwIhAPwRksqJoyoAle2IGHAALZIljR5DVo7fVc+Ssr95wi4+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "8288f6d0777aa4333f46cf6de696cf8d5e1d9809", "gitHead": "20e31295e81972c7ef91cee22dae7e75178de0cc", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.0"}}, "0.2.4": {"name": "magic-string", "version": "0.2.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.4", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "9ed11dcdd1da89a5adb43b09191a0802528d3fd6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.4.tgz", "integrity": "sha512-C4ClJ5pSLtxoR8B6RtClDFfatQMPTfzSDrSGHU4nJn40rIH5qRVeU4o5VkVEovg6dDeFGEJyM8BSVM5PRlCGYg==", "signatures": [{"sig": "MEQCICpoeGp57AITaLw0Iuqqf/GG1vRidddMvxq+vUOtvNv1AiAM4RSkjXTSenVJCW2ISbvpwsCRqx8jv09aEoPBvkjAdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "9ed11dcdd1da89a5adb43b09191a0802528d3fd6", "gitHead": "a42adbb778493c64bc0b742a989b3f39c0567e33", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.2"}}, "0.2.5": {"name": "magic-string", "version": "0.2.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.5", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d4c3290eee67bfd3e5541c486c15b6f0326d4aed", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.5.tgz", "integrity": "sha512-8eNYyM+y4QiTkg8Uqh6lntQbjdFeGGc6TJ9nZsiuOMAoi0/uGO6K/NbTapEA+3cHW/b29ygXq1J0bx+FFsA4aw==", "signatures": [{"sig": "MEUCIGKBGCOaTdwEwS254vU2A1+EDFpoBJ4HgT2R2ibmGnpbAiEAu4P4Bz2Cb945YhoRZqnZjty4Sg6dl2yieq0zfeoBYZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "d4c3290eee67bfd3e5541c486c15b6f0326d4aed", "gitHead": "7c1cc60841b1648d212092ca3be4998c861fe4bc", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.2"}}, "0.2.6": {"name": "magic-string", "version": "0.2.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.6", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "26ea8ac314c2cf28889333916d57e29929c11cbd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.6.tgz", "integrity": "sha512-PniDsViCF+sf9xaXzJFk6upAEpnJHYU3bSwJ7Ws6KkARJq64uPMiQbGxXiUOOqy+2aKlusDtCvsQlBVYsjoraA==", "signatures": [{"sig": "MEYCIQCCSxfmnrdrY0HXohipbGGETKIm27gUcqH5aAiGVACwwgIhAPBRWCbzJleqvBuJNWX4nawWy5CSGUggiBFtBrZu1zZJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "26ea8ac314c2cf28889333916d57e29929c11cbd", "gitHead": "8455bc71dd2c53c2fb4a78ed44b9b6e91a2d9676", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}}, "0.2.7": {"name": "magic-string", "version": "0.2.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.2.7", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "60f3fc3cf81b16496de2ee817218290b9abfbfb6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.2.7.tgz", "integrity": "sha512-vlAx0jAVrAqowy37VK05j8BoPtE+d8ef9rd7jcXfeY64xCZMxLL2VjoeFImi+wLbKwwiGoQXMGMKzImMg+MmaA==", "signatures": [{"sig": "MEYCIQCIK9bqIvptBz4yG5EW2s5P1F7oNtEpZ8Gnzgmbhj9IaQIhAIRsalP6A1sokpV2VtsnSayvrJBqookqbmxTXMPkcsa4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "60f3fc3cf81b16496de2ee817218290b9abfbfb6", "gitHead": "e7f1d9632d9cf3ffc7e9cc929cbc99d0ab4aa106", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}}, "0.3.0": {"name": "magic-string", "version": "0.3.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.3.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5abc016a1af2e36b872d39e3eb2c5c52780d4924", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.3.0.tgz", "integrity": "sha512-aD6xIZvm3VBW7v7Sk3/67Iu4BwGHFLwVUltdHnJaTtDnKRrVCcwTyNjl48ss4kaXUKq9pdNrVl4qGxS3AVZB9A==", "signatures": [{"sig": "MEUCIQDLFcAAeJfWcGQ1DXiwLXg5ZhGOkqJtzPR+v9nhjNJDQwIgMblJqP5OgOwRvlqIp+KYOaVGebm6Ye+x/TDkX/rwKVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "5abc016a1af2e36b872d39e3eb2c5c52780d4924", "gitHead": "ac5bcb859ddc49cec65328a8688b4d451d220cc4", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.23", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}}, "0.3.1": {"name": "magic-string", "version": "0.3.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.3.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "c18b321a9de17210e1154e0660f95c73b1295886", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.3.1.tgz", "integrity": "sha512-utOIiw8YsBQxg2wiGUf9RuHnrnGeefuBUkHZdNz4rkJVdflBpw5Wyq6i2aBE/3DuacYAjqRmUP5xUaNjFU6bkQ==", "signatures": [{"sig": "MEUCIC+ksXmkgdTD4FwLgc4oXV3DzzDau3DOpDRNCvq+UsljAiEA395GLZy7EcTay0y1Mh6bnCIOAsgyYunUjxqxzP8NU98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "c18b321a9de17210e1154e0660f95c73b1295886", "gitHead": "084785754ed0e650655a3037fa7daece9da73add", "scripts": {"test": "mocha", "build": "mocha; cp .tmp/magic-string.js magic-string.js", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "dependencies": {"vlq": "^0.1.0"}, "devDependencies": {"gobble": "^0.6.10", "source-map": "^0.1.40", "gobble-esperanto-bundle": "^0.1.3"}}, "0.4.0": {"name": "magic-string", "version": "0.4.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "bfacc5481a5a8e61bd2260103baacd3494d01207", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.0.tgz", "integrity": "sha512-<PERSON>zey8PZiNWzLw2t9T2iSbtzznnThcNvUEffJM6uz6mSQ/+J+kgUHnXYBux64YXrqmQT7o89KgwqXzHUA3tNryQ==", "signatures": [{"sig": "MEQCIEPlRnVb9+Zyy5mPg5p0pI9VzCpimXm1Z90+4zHEYgvMAiBvuKM/rbcSfxBd7qRRRmaVpgEy6YHOZLxFQNp6Ylj3iA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["magic-string.js", "README.md"], "_shasum": "bfacc5481a5a8e61bd2260103baacd3494d01207", "gitHead": "b4b20ffe870457e1e348aac649015384d15aeee8", "scripts": {"test": "mocha", "build": "mocha; mkdir -p dist; rm -rf dist/*; cp .tmp/dist/* dist", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/Magic-String/index.js", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.1": {"name": "magic-string", "version": "0.4.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "016fab5ab4650edb8ed4a568822bf08e94043039", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.1.tgz", "integrity": "sha512-jjHd1nvwKZFBOkasdaQnxRN2LgGg0m8YuIPafj5MOc0nm6yarEzupBYyW9HDzh4tfZEOpjZZhc7V3q9y/HWuIA==", "signatures": [{"sig": "MEUCIQDV+TPCV8eOcLYbMA4ftCBTM1Jgnl72gIlND5o+AeTuqgIgB2Ne3wASRVvppvHwX5jrWj2tzmN2h5vPb17TXrOQs5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "016fab5ab4650edb8ed4a568822bf08e94043039", "gitHead": "cc9217d43041c30059e963a4b5a1e7127b339362", "scripts": {"test": "mocha", "build": "mocha; mkdir -p dist; rm -rf dist/*; cp .tmp/dist/* dist", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/Magic-String/index.js", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.2": {"name": "magic-string", "version": "0.4.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "6c01090f23cafe3e6d723a3619050f1003289f33", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.2.tgz", "integrity": "sha512-GI8GYq5kuGRzeVrkdQgQZOsvn5hQhkZ+KFV1dfU+PlVts1cryQY/lTqnuheMw5Pt1Vls44gZ63+thL7kJUkXww==", "signatures": [{"sig": "MEYCIQCsAHlcQmTwMfRTuykN3bMnBmK6fqv5fn9nvCIqcXnBjwIhAOQltseZxB9oyowi1JooXkoFOqeiow3errXlii+UNftk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "6c01090f23cafe3e6d723a3619050f1003289f33", "gitHead": "d534afdee1a7b91ad72dcb9721d9b490dd4779fb", "scripts": {"test": "mocha", "build": "mocha; mkdir -p dist; rm -rf dist/*; cp .tmp/dist/* dist", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.3": {"name": "magic-string", "version": "0.4.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "e2e942e6947e4580ac6dc76a6e6e2763e6cec87a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.3.tgz", "integrity": "sha512-nUG65LNZAJbnbFMO9+LHLroH9/4w2UPCgYTl3uUBzuXgXP8pkV5VwKGFJmmfvK8mhsWuFigHCRYwg5Wx0QUXig==", "signatures": [{"sig": "MEQCIEOvIKKg4gY9azjvNDm+rZf0r95EV0EFTnugg62CBFqgAiBm7pU5Z/KDt14rVw9B7J9/DP3+BiOMKx++SqHYV6PcRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "e2e942e6947e4580ac6dc76a6e6e2763e6cec87a", "gitHead": "1316024b729cc4b02742c4bcaf7a43d3ceec84c0", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.4": {"name": "magic-string", "version": "0.4.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.4", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "29af535a90ad03ee3acb933399f436ec9739f636", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.4.tgz", "integrity": "sha512-Zl2r0QcGIARB/LCYavUQPGeS9ju57ZCJpTPD142M7t1W48Iwyjwhb5Zf1hhF1Nn9rdFLo3c5Yg0DYjhQbZVBUQ==", "signatures": [{"sig": "MEQCIHLC/Fu/m4nAV1lEfvvmfOGEc+7nWIOLDi+Dgh5yrFDzAiAHMFLn7SrP/e104lItJgeBBqIH8qT9eHPPVqAVf3/nhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "29af535a90ad03ee3acb933399f436ec9739f636", "gitHead": "6a5e7abcab70c34d555af9435c4c75a38ac56bab", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.5": {"name": "magic-string", "version": "0.4.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.5", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "0d7fec1ebc5b7c17b47a8a82186319cc69121db6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.5.tgz", "integrity": "sha512-DyfVXVINLT+kAEmZHLQChBAjNkz3akRseIKfIv6zfNf/Nf8rD2WxIQMhJrcWIJa/4GxqXXpJpG2LyP/jrndsVA==", "signatures": [{"sig": "MEQCICJBX+W9l3WextV9QkNrNM3C393YOw19zXNe+TUMqQjtAiBvEglOvMfscTxpu8AxXkTetle9TX7YSDSthgHqDboNaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "0d7fec1ebc5b7c17b47a8a82186319cc69121db6", "gitHead": "7dee9aed18ab82734e26b2af5a9fefdccf287af7", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.6": {"name": "magic-string", "version": "0.4.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.6", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "7e01ac73c24ef34f724ab7893db64d217341b3bc", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.6.tgz", "integrity": "sha512-GENvfwtB1Vl2yfaJADo7f+dnmfzkhfmiDpowsb6ATToVmvxHKRflVlzPuzEjQarjSfROaVKwW92Q235IyVbqBA==", "signatures": [{"sig": "MEQCIAKgChroTuztKNB+sCU5t63pRCGwJP4ApVHhTTgH3NvIAiBUFkOOIA+SfnLSfXiwPEsUdyri/kT1CzDwIjePZOGN/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "7e01ac73c24ef34f724ab7893db64d217341b3bc", "gitHead": "2271c63abf26dbebbce71f1b5fd945bcc33a0fc8", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.6.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.4"}}, "0.4.7": {"name": "magic-string", "version": "0.4.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.7", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "76dea11561e8ad1d3c118c50a1dda9c6e499e1e7", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.7.tgz", "integrity": "sha512-Lc0UqcKnZ26yzf1MpKezr0F7PhUX5OJK1g0bZwQX1M6jLmeizlbCZgj9rIsVhTnY+Y12Ei3JGdwshrwXfohVnQ==", "signatures": [{"sig": "MEUCIESdZ7EldhzhZWd47TBbZlhzVhWC3fG7NHcuY0Q4LrTDAiEAi2e8kRs5r78EaTOXY75tPcpJhpu3Y3K6ZM5pv/lBMdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "76dea11561e8ad1d3c118c50a1dda9c6e499e1e7", "gitHead": "05654d5e565748aad68b25163dd5f017553da0a9", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}}, "0.4.8": {"name": "magic-string", "version": "0.4.8", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.8", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d603df973271f97ebbaf15a25e87ff799bdb11f4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.8.tgz", "integrity": "sha512-B+K+mG1r67wUbhX+iHYt2fRp2b5U+hYfq4XzW+Hut00QN7Zs6SaloBQUh3+DrTIMA2oAyC8JdG/fCxHim1OZzg==", "signatures": [{"sig": "MEUCIBKGc/f5kR7zD/LiDbAT45GsFd16BpYMks24HxfIjHU1AiEA83QaszrJGTSL8IhBpBHpqvQ3Yk63q4zceGAW3Hvg69c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "d603df973271f97ebbaf15a25e87ff799bdb11f4", "gitHead": "0eb3ee4e76848378775cd23322002d17f721b2fe", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}}, "0.4.9": {"name": "magic-string", "version": "0.4.9", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.4.9", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "f46e9aaf5959cfbbc88f68a9969cd66d120e1a22", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.4.9.tgz", "integrity": "sha512-4+xFWOeaQsQHXHiA8B1xflbdOYNxYS1Ap7uU+F1LLgVoTdS6mQwFguOYBrqgdLydeAZGqNPcoq5HxTVKpnfRbA==", "signatures": [{"sig": "MEYCIQDDOFKZtUgwpvg4mv4XdZ03Lk7s5+2HFsJ4tOLyM+bdaQIhALyk345F4octAqLQVfDtXptwT9CbcigWD9k685vQVnc3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "f46e9aaf5959cfbbc88f68a9969cd66d120e1a22", "gitHead": "f42a5774bf2016989a164f80edec361a55f95a16", "scripts": {"test": "mocha", "build": "sh ./scripts/build.sh", "prepublish": "npm run build"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.7.10", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-esperanto-bundle": "^0.1.7"}}, "0.5.0": {"name": "magic-string", "version": "0.5.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.5.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5cc8991270b765a2a4aaaf667ad14bc7e94fbd7b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.0.tgz", "integrity": "sha512-CQHS+LB82teHiw7Lupp+r1D9RuybUmmfWd/ZyrOBmcik8ByUE2YAkI+2nRdCuLD1b8OJx9RYfwlSSE6us/xF1w==", "signatures": [{"sig": "MEUCIQDkWDgA7rpiDqIbaAzBeDpnkpVVbcwhm13ushF4nWOqNQIgHjdTlkj8byFSznlHM8jaJwlfQE/YL/Gf0p3R/FXkK3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "5cc8991270b765a2a4aaaf667ad14bc7e94fbd7b", "gitHead": "d4c6ceb66f35cc929df1dc030d7214dc4f1190fd", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}}, "0.5.1": {"name": "magic-string", "version": "0.5.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.5.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d8696879af84f46d0fb7e74ba13609e941d9e230", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.1.tgz", "integrity": "sha512-tVDVwxwb66qn9Wx6W31GuQSKkobBxlMMAH6XjW3tChbatjnV7yknVOXcz00aafPXvP+4w/g93OGhhtXGWZED7g==", "signatures": [{"sig": "MEYCIQD376nXsjv14pF+l+nal90WqdV6DLUaY0S37Sya0MkbzQIhALMPKO5ll20cI2t5VqtklxQnSTYn/KXgZUJGS6hyviE7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "d8696879af84f46d0fb7e74ba13609e941d9e230", "gitHead": "4f6a9853054717c64f1b9810ecca7cf33981e42a", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}}, "0.5.3": {"name": "magic-string", "version": "0.5.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.5.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "c377f91b7a2680e2c3d566996cadd597a572479a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.5.3.tgz", "integrity": "sha512-UILQsYS7Gt5AdgPnEWU4lcY/sKaKZLc87XVEl3kqRTqfI8OSwNMNWaDnrWZYGvdxws5BBCv7Ef/SeHIpzZ/dWQ==", "signatures": [{"sig": "MEQCIALD5vAttPdJezFcjRQGNgvNcH1jzNWkF4tJgUAxJHW0AiAHSnxOfq7NjzZcMd681emI/HDDPbW3IRUMYX+9ennNJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "c377f91b7a2680e2c3d566996cadd597a572479a", "gitHead": "c5706ec76ab45e88847a53a4ce618b5dc87a05ed", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-esperanto-bundle": "^0.2.0"}}, "0.6.0": {"name": "magic-string", "version": "0.6.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "f00e06174759bfd4dc82430e2fa2fe667c3f4864", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.0.tgz", "integrity": "sha512-x+p5NNrOVBZtt2jOGoJIjVofeh6dC23G9evrB9Y+mV3IUAkxFjZjIZKucdMw7LoqFvwQ05q0/5UeyKzX5xTNTQ==", "signatures": [{"sig": "MEUCIE/1Fbj1ZUqrshuZFHCyITEIqajBNk/CrDl/N5DAWo2NAiEAuKboP080RGPnCPKnrLTiH+IpXM1lJoUkgAwL4JANo5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "f00e06174759bfd4dc82430e2fa2fe667c3f4864", "gitHead": "637ac4d2e5aa6349962a30b797fffbe46b3cddd4", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}}, "0.6.1": {"name": "magic-string", "version": "0.6.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "4dd285a695f607c2019570b6ca6df028ff2baf46", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.1.tgz", "integrity": "sha512-K6nkAkxVjVKboDrpC4McGhdSMifUNcv2IsQ6raLzvVcoZ9WXrVOZt73GSEXZX6ExaIjiP+hL/T7dCuDXu/GJSA==", "signatures": [{"sig": "MEQCID+RKSfMA7WoEi6SaT4FVyf3s5IVA1AEPWfHm+fjpv1hAiBL0YLlb/y6nDkGIjc/1GYidgVXfCb5ZcQIae/wjBRzgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "4dd285a695f607c2019570b6ca6df028ff2baf46", "gitHead": "4ebb595315c7814e2e03b9d1b0f362cca32e2bf2", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "2.2.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}}, "0.6.2": {"name": "magic-string", "version": "0.6.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "ea0783d29b284e3a9d17fdcdf76ad695fdfa458e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.2.tgz", "integrity": "sha512-K3Z0XFPXHZy2KazshebTFXYoSDyjvNGj8/oVXahHAIhvMvgE//gLbpGCxvKC4rjnm+KNbufjGXGm0QGQeMYWAg==", "signatures": [{"sig": "MEUCIQDLrTw/v+NRXy+V3XdIMVTl6hT0I4EvYW5f9jKumPIvFgIgQrKZMSNYDd1KyRoBCoxFwMVhnjt0RMZUKX81jaPM1xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "ea0783d29b284e3a9d17fdcdf76ad695fdfa458e", "gitHead": "25fcafc73a204edbf84c442a3c292187714fbf1d", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "2.2.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.2.0"}}, "0.6.3": {"name": "magic-string", "version": "0.6.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "be1a5d16cdab51d136f77911b83021a2010807cd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.3.tgz", "integrity": "sha512-I+ix2CM0zQ4cYzwRy8nYuQc79+nvv4/QL3UREdz63vu+xVgX4fzKiF8o5DU/5hR/JrKuZT8PZ0mJFw/JYF3U5g==", "signatures": [{"sig": "MEUCICHRUwNaoAvOA49uSgCtEUmiGZ4AMzMLpzTKAooHcWb3AiEAizI2uDL6dJWB4GXavpDTxbLGa9OPmZn/0YQcRnCSaiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "be1a5d16cdab51d136f77911b83021a2010807cd", "gitHead": "71751cc6d6be2fb4c556b0abac681ea0ced58072", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "2.2.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}}, "0.6.4": {"name": "magic-string", "version": "0.6.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.4", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "f69208648d1477c3d23e5f1b47ffc3993c768450", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.4.tgz", "integrity": "sha512-yAxH+rTmp6UNsRBUmUZYnyPGQYNaL/TD15T79KB7R2s/dxeKYVKDokJZYWgP1ff9iWmieWD/tk6lUXe5sUbFrQ==", "signatures": [{"sig": "MEQCID7ILDuNNWgBaJ9SOCh0uitoZUIQLrBehlfS72thzcO6AiABLnYfpoRD1CVVb2UOz1y2LuZo87kxTW/ndwqUgfgpxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "f69208648d1477c3d23e5f1b47ffc3993c768450", "gitHead": "78ca5aa3331899d307a97b297d742d332413e41e", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/MagicString/index.js", "_nodeVersion": "2.2.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}}, "0.6.5": {"name": "magic-string", "version": "0.6.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.5", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "07d513ffad5736bab56f4cdd2e2a93dcc90cc9a4", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.5.tgz", "integrity": "sha512-nVfaivBWGTBKB5tXK3BaT/ToV4KrEfROHVGO73YRljxjH22d34qX6PtWe+gOX26oI9l82soxQr0yYRb5YoJTng==", "signatures": [{"sig": "MEYCIQDPbjneb+3MVnWjc/jefB1gvjMmXfflI06XQlgX/khqhwIhAMiglCnJz90GLTDSNNV3n+cG2WNN0A5KMqKII7OOg7W6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "07d513ffad5736bab56f4cdd2e2a93dcc90cc9a4", "gitHead": "82a51a39b0a62951cc7a2e1daf7e9d7468b1d21c", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "1.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}}, "0.6.6": {"name": "magic-string", "version": "0.6.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.6.6", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "35b5788604ec27f1774132deb3806d2369f8c36c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.6.6.tgz", "integrity": "sha512-XkkIybiqFympvl/GIui42rB5Mza6EUkmOL799/2OnSCcdSs75shp3dLy66MEbnWRQ/G3fz2gSDwhq4DyYpCq4A==", "signatures": [{"sig": "MEYCIQCSwb2AxfCCg8AE0dkZhyGUgpWo5SLJy1amI21DC3favwIhAJA6DPbomqUEPKd2ECX2spdOae1LZbr9LuXM7faNOasJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "35b5788604ec27f1774132deb3806d2369f8c36c", "gitHead": "772cb010e5b178e1b5705bbcba3fa7b01cbec2f5", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "2.5.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "2.2.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.3.0"}}, "0.7.0": {"name": "magic-string", "version": "0.7.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.7.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "7a244c8ab985d4b37a5ea36bbcdc2afc6b299e65", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.7.0.tgz", "integrity": "sha512-ZtCJ8dbIpTK2pXkg2Xm/vU0H6ldN8CGg6U/arwWHS1WNVslnMBRA2g9xdAZLmN7i1XcDsJvZhIK+5zhdc9mzfQ==", "signatures": [{"sig": "MEYCIQCB+MnCg3iNZlVGTH5j3ExojPm5C1tnuUo98JBAHffbmgIhAMX2JVxQ8lG551odYK1ISUZk9F6e4dZlQuhQEvEnvCii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "7a244c8ab985d4b37a5ea36bbcdc2afc6b299e65", "gitHead": "5149309b3fa2f86760ffe032086d89c4b7af8da4", "scripts": {"test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "3.2.0", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "gobble": "^0.10.1", "resolve": "^1.1.0", "gobble-cli": "^0.4.2", "source-map": "^0.1.40", "es6-promise": "^2.0.1", "gobble-babel": "^5.1.0", "gobble-rollup": "^0.7.0"}}, "0.8.0": {"name": "magic-string", "version": "0.8.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.8.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "cf0c0636093b9a0fdc8ecdfbd0dff4d365c7c763", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.8.0.tgz", "integrity": "sha512-1KQAfZT/eoj4RSRWCT3tdlfrUMiFHWypzLyQ5osBwfnB7I3jSgNJNuhDEEeyTYvlV6o/u6VsQDwDEMWh9SMmWQ==", "signatures": [{"sig": "MEQCIGSkkRSa5iRdaKJVyVp291p9Q9oeyVxk0vQobfEwvYRsAiAHG66UMzx5pXhDdwqX4UIEO34Fb3pjqqnRIPCbnuKVLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "cf0c0636093b9a0fdc8ecdfbd0dff4d365c7c763", "gitHead": "1a12f2c8f129930793d3f1aa3bea80f543f6f947", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": "gobble build -f dist", "pretest": "npm run build", "prepublish": "npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rich-harris/magic-string", "type": "git"}, "_npmVersion": "1.4.28", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.5.1", "gobble": "^0.10.1", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "gobble-cli": "^0.6.0", "source-map": "^0.5.1", "es6-promise": "^3.0.2", "remap-istanbul": "^0.3.0", "gobble-rollup-babel": "^0.6.1"}}, "0.9.0": {"name": "magic-string", "version": "0.9.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.9.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "eb27fe5c387b8b605b3465aaa62cf5cb29831e5a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.9.0.tgz", "integrity": "sha512-L5B0pMHGLFWmM809qQ7gHKkMSd2y8eYRwQkAjnzzyR6TxfvBt3Z8pU9u4VdZ9U0b/KbkJFeKsNdLJ9zn3rPEXg==", "signatures": [{"sig": "MEUCIQC/SuCoialSPjg4LHNiT3s3Hjchfwth6nr4DtUP08JQVgIgGx8b709uNONquIFEy9Y/4naZ4g1Syszy56+IDjXf3R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "eb27fe5c387b8b605b3465aaa62cf5cb29831e5a", "gitHead": "869f96d88b893c32328117d056d60298034c3fa3", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": "rm -rf dist && rollup -c -f cjs -o dist/magic-string.cjs.js && rollup -c -f es6 -o dist/magic-string.es6.js && export DEPS=true && rollup -c -f umd -o dist/magic-string.umd.js", "pretest": "npm run build", "prepublish": "npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.1.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.5.1", "rollup": "^0.21.2", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.1", "es6-promise": "^3.0.2", "remap-istanbul": "^0.4.0", "rollup-plugin-npm": "^1.1.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}}, "0.9.1": {"name": "magic-string", "version": "0.9.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.9.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "bc3e2ada28be59c65cd28dac47ba6527b96c8fa8", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.9.1.tgz", "integrity": "sha512-B<PERSON>CHvzVPOQR6YFa81HPtLhM9C9FKhyRPId5vFNF73YbgOnUioaUVcug691bpJIxbgLFAcYjnKTNr4MoZN7CZAw==", "signatures": [{"sig": "MEQCIGqsCsKcpKI52Zd8HjhruOdotu0yGdl51mV/uXsH1pnaAiBli9OE0jQPf+ZOvjU6DgEAfrff3nV/Asj5AwlP24npLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "bc3e2ada28be59c65cd28dac47ba6527b96c8fa8", "gitHead": "235967331801c0a008eab29c465f202eb76392a5", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": "rm -rf dist && rollup -c -f cjs -o dist/magic-string.cjs.js && rollup -c -f es6 -o dist/magic-string.es6.js && export DEPS=true && rollup -c -f umd -o dist/magic-string.umd.js", "pretest": "npm run build", "prepublish": "npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.1.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.21.2", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}}, "0.10.0": {"name": "magic-string", "version": "0.10.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.10.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "91d9678bd6461448abfcd19376f8903a80b04187", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.0.tgz", "integrity": "sha512-sOHD7csdcHzkIhEgtV+TowoBppzLTYMoAYDtUxzI0uU39HzafOlXrfyp2pZiD62qVgoiI+2jnfH2PUqSVc9qQw==", "signatures": [{"sig": "MEUCIQC47NT14O8S+OA8/DiUHUMLxBFPpPFtbXuZRYHm+ls4uwIgcsJ243b2FdsjlXbcG8S0B6p5vTf2EDhObsaMNR5oYqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "91d9678bd6461448abfcd19376f8903a80b04187", "gitHead": "27404ac658b6fecfc8e6b2c922b8e42a50dbd386", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "export DEPS=true && rollup -c -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.1.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.22.0", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.1.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}}, "0.10.1": {"name": "magic-string", "version": "0.10.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.10.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5a567c45518429316a8c84b804db7289654d75a0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.1.tgz", "integrity": "sha512-PQOjD/ppYw2LA7fGb76a627F+5YsFY5TE6I8SXKI8XHkKIl4yIoLRQMZFLwNPgHivl2oqNy5TQzMrxuHvvoIJQ==", "signatures": [{"sig": "MEUCIC69GW012scA3NokiyJoOf5bsHWN7XIvlq/JFWoU4+xmAiEAxTaUDx91ilRZCBbOU7kfm5uP/8Fbiej6OgQOF3qruDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "5a567c45518429316a8c84b804db7289654d75a0", "gitHead": "8c07c006b16f529d2354b7399c0e978163a26432", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "export DEPS=true && rollup -c -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.1.1", "dependencies": {"vlq": "^0.2.0"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.10.3", "rollup": "^0.22.0", "resolve": "^1.1.0", "istanbul": "^0.4.0", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.1.2", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.1.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.2.0", "babel-preset-es2015-rollup": "^1.0.0"}}, "0.10.2": {"name": "magic-string", "version": "0.10.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.10.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "f25f1c3d9e484f0d8ad606d6c2faf404a3b6cf9d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.10.2.tgz", "integrity": "sha512-Ah6T1JI6cPsMQ/7y2ZqLZ7ssNu9/oH95QvG9RTLbkRAb0OWskxFz1XP2IKzHmS8t91OzrPlMNsjab112Zd1vkg==", "signatures": [{"sig": "MEQCIGjgOz9uDcLHjH0Jtk/oOzgpgMeSADTAxphp+78shAQJAiBkLnHnxKgEl/gVp0tRF1xn2gl1/0rXIFL5eq38NXaMaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "f25f1c3d9e484f0d8ad606d6c2faf404a3b6cf9d", "gitHead": "512a75e6db287eae2b77a1d5efee41c68831bd5d", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.1.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.10.3", "rollup": "^0.24.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}}, "0.11.0-alpha": {"name": "magic-string", "version": "0.11.0-alpha", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.0-alpha", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "236458b2cf7cb1c0000ea526322873b03ad8f661", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.0-alpha.tgz", "integrity": "sha512-BNbpItywA0sO29jj5zZLMIjdI+ljB9P63eWJtznKQTHpffk7dXJ+FH3BNpwGKopDk8HgpKLnbBrO/LbTQdYryw==", "signatures": [{"sig": "MEQCICkzaGfaWRqjfciXMpFGgp9uTccMotCooVwwQWheXEyNAiA+QQzGUsDLm7qkNbWm6Cad9fWA/HXWrMxEZ1URRaniOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "236458b2cf7cb1c0000ea526322873b03ad8f661", "gitHead": "56955ccfa6cf7b1446bca08a2c180643f45b5202", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "4.1.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.0-alpha.tgz_1459972634494_0.2819540954660624", "host": "packages-12-west.internal.npmjs.com"}}, "0.11.0": {"name": "magic-string", "version": "0.11.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "630bdcbbaf369aebf36b967c10a3d4c070b0b25c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.0.tgz", "integrity": "sha512-qh5HizwfCljf9x+e2niYU0CaIgqxyF8cGvhcy4hhXjqe/KMYIachtbIKUj8i7HrVDPWtkz+GRN06NtciKdOo8w==", "signatures": [{"sig": "MEQCIETsCa7t6NL+Ly0+2O/PKybLDVqIBSlUehqpIOtw6QxqAiArUnR5T3sordjR5vjHw3+RgYaoAQLCxx8uJ25HUGC6aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "630bdcbbaf369aebf36b967c10a3d4c070b0b25c", "gitHead": "2f27683c5d05b2715b8c85b83ef206cb17f8e2c0", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "4.1.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "rollup-plugin-npm": "^1.2.0", "source-map-support": "^0.4.0", "rollup-plugin-babel": "^2.3.5", "babel-preset-es2015-rollup": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.0.tgz_1460065542705_0.00849955528974533", "host": "packages-12-west.internal.npmjs.com"}}, "0.11.1": {"name": "magic-string", "version": "0.11.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "921d8a65b6f0c7ee803fef4266e15ace2a33bf64", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.1.tgz", "integrity": "sha512-vM8qjvw+jooqNdVKJ5+Aq6H6XSgpVYBpbl2EZj6zvxl814ckUU9HCl9ecuDSsGgBASujJVbgB0ytb6vGEL4RoQ==", "signatures": [{"sig": "MEUCIQDhzIMnufjay5h490d2+FoTNrnfu3wkJAGBiAtVJ5UFdAIgKpL2+dUWQ/rZKej95X9a2zxJIubF352vZO1t66uUOao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "921d8a65b6f0c7ee803fef4266e15ace2a33bf64", "gitHead": "f4e30aa049cafb092f0aeedb310065f1ade9a0b1", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "4.1.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.1.tgz_1460132889179_0.4009906055871397", "host": "packages-12-west.internal.npmjs.com"}}, "0.11.2": {"name": "magic-string", "version": "0.11.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "28918f2c3325e6ba8234ed39503356cb02fb1316", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.2.tgz", "integrity": "sha512-ZiDAkdrHmKNLdSVknkj8LOramcS1UTW02ZZSg45xuWHrT0AHhhVBG6l/UlMkZSHhunW0FgM6tfFtflwgU3go1g==", "signatures": [{"sig": "MEUCIHfLU4AkEaYp8qlNyxdR6WtCEfVnrRKM0Gvq3GCRPIX5AiEA52Yhs1RYFhDvkqpKEjNjcRC438+HrmuZDPetq9ySR5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "28918f2c3325e6ba8234ed39503356cb02fb1316", "gitHead": "2258c673130d21def46c45b3ce1d97a9b884f413", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.9.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.2.tgz_1460318858583_0.017449898878112435", "host": "packages-12-west.internal.npmjs.com"}}, "0.11.3": {"name": "magic-string", "version": "0.11.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "8d39a02fa078c4ec267a0eff6dbc0a5a27e7cc18", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.3.tgz", "integrity": "sha512-ZaBFmnsCJSOA0sobd3w3KAgGjJK74UBc6dPsbwJgslbNVM+pZzNOCIDmhhCARKEmrc7tK7q1L1lZpv+mkEbwBg==", "signatures": [{"sig": "MEUCIQCaIp7iTuAEzCCkCuux9zh1lRU7/IzPTfalnylIPSWSlAIgU14+tcl6CH03eJMlBE39fScwhUB3/WJI3phM/kYrUAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "8d39a02fa078c4ec267a0eff6dbc0a5a27e7cc18", "gitHead": "71bc61d394fe76d30904a49707ab2ae80fe5bb6b", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.9.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.3.tgz_1460346538076_0.3160873153246939", "host": "packages-16-east.internal.npmjs.com"}}, "0.11.4": {"name": "magic-string", "version": "0.11.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.11.4", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "272646332541760c1a4dbf2ccbbd4083fa42f690", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.11.4.tgz", "integrity": "sha512-wRR5md5KnsIMe43bxw0k2fCTSmrn5c7npGzwOwkgXF/qjRxc9f+Bz7Aqg6wf0AU4TL6/oTkT5hsblEq5ZV+YHQ==", "signatures": [{"sig": "MEUCIEbxu56JUnQako2h4Upxvfm5sSn4lSLW4vTx5GhLLvNcAiEAu/NrgWxNmu3ulJWhe90sr99qs1p5LNlkNp5bRGc3/U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "272646332541760c1a4dbf2ccbbd4083fa42f690", "gitHead": "be29b5e644b30ca9c757ce90e047f4aafd6e0307", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "5.9.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.25.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.5.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.4.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.11.4.tgz_1460424825718_0.8625014517456293", "host": "packages-16-east.internal.npmjs.com"}}, "0.12.0": {"name": "magic-string", "version": "0.12.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.12.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "95acfbcdc052e0fb5c1d7056495b12f985b2c434", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.12.0.tgz", "integrity": "sha512-cZ+I5MK4WHDGFuCChD38XHR8+bipnKGBuNwuh9OO9CvrkZb98+sYsyY9AkYcsbrNCZVGHTf0Ujo2SJ8pfHO5Pg==", "signatures": [{"sig": "MEQCIBZR4aYXdINzDVrIxjfx/KbNPwbzlSoALMWqKby2XpUZAiBBQgmhtwiiC5PgO1jcTB44lN1YUdZ+s/ToVsqnpNBlwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "95acfbcdc052e0fb5c1d7056495b12f985b2c434", "gitHead": "c789f4dafde874541fd8f6878508e52eabe122ce", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.6.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.12.0.tgz_1462377275562_0.04048254550434649", "host": "packages-16-east.internal.npmjs.com"}}, "0.12.1": {"name": "magic-string", "version": "0.12.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.12.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "bc41764d11c41ec5eca048747691f0ea06dd6855", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.12.1.tgz", "integrity": "sha512-40AfENvb/vRIIy95zoXA21Lm85ydr3/IGyGd+Rw5Sc0H8H2PIhcCjZ66P47+Ni8BPFnolqOwwVWzNPif6F+qvA==", "signatures": [{"sig": "MEQCIQCjtgbJNAPDY0/gY0qWwbbgA0EGeJLr6pxpaSnlGSGywQIfUCjjLPBiAbGnQ1Hhs/RA7K7wuuhKj7iwjrCQ5zZjQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "bc41764d11c41ec5eca048747691f0ea06dd6855", "gitHead": "f2ef548b6cf07c2b7139ed76e282ce7fd6c16f88", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.6.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.12.1.tgz_1462389066314_0.07373582944273949", "host": "packages-12-west.internal.npmjs.com"}}, "0.13.0": {"name": "magic-string", "version": "0.13.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.13.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "eec260cff4918c1b5b09058271e7be5f8f6faed1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.13.0.tgz", "integrity": "sha512-NP0/v3ExfMHRrHqHX3GvNKCFlPk0xQVIjeu1XOwj1+FC2FQ/GWXJFBHmtBCxTUQe8N2SNvdXNs8sJ/GJ5++JCw==", "signatures": [{"sig": "MEUCIQCgXPtpe1bmY53vFBQrhr5KzpFASY1u1Xdf/6VompMtsgIgBr5cmLJZUOAbATYfSW/9nt0NpbCL8v8BYVgCECUuJK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "eec260cff4918c1b5b09058271e7be5f8f6faed1", "gitHead": "c0d535e378200051a85b0a9f6d6dbe6fb5673ad1", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "4.1.1", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.13.0.tgz_1462805201711_0.21729278238490224", "host": "packages-16-east.internal.npmjs.com"}}, "0.13.1": {"name": "magic-string", "version": "0.13.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.13.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "404b98c951619a774a2c92570c0d3b1e66779e3e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.13.1.tgz", "integrity": "sha512-w6NUKQC5MLSdWjZSEKGbwItsa41Rk6YLMcglbaWz0NA1Zj8Q/1UDHBayQYXYOYJ1qiN0rqBgHR0fe2Fr9I6HYw==", "signatures": [{"sig": "MEYCIQDCzPM5yBC33LpolB4bTQJ1+Ya5MbnFurkdgrmKbbwcWQIhAIcodDihIV6obgS4F1OnuAOknfRWS9zu9ZKcZL8UHIvX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "404b98c951619a774a2c92570c0d3b1e66779e3e", "gitHead": "ad4556e2a6c59e2daaa3b60bd0bc4ee96a1d924c", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.13.1.tgz_1463254521771_0.1730783332604915", "host": "packages-12-west.internal.npmjs.com"}}, "0.14.0": {"name": "magic-string", "version": "0.14.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.14.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "57224aef1701caeed273b17a39a956e72b172462", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.14.0.tgz", "integrity": "sha512-ASteqiQbpCPx2uMF5NkmrIUlo3nsSDcPOo+O+F+pdPML/IS560BwrEljpzDFOR45eOME7UPTxgUQVPs6Lj2mTw==", "signatures": [{"sig": "MEYCIQDAOxO7ZA8UfLCywN5k+EqHMaLPgmnWgZhFxOwVnGrsWAIhANJNL11gVVa2rbcGjB3ITJrcKRtkZXf8IWdIdxhWZenK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "57224aef1701caeed273b17a39a956e72b172462", "gitHead": "bc0f83ee126d0475aeef00ebc01d8c412a59cf1c", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c -f cjs -o dist/magic-string.cjs.js", "build:es6": "rollup -c -f es6 -o dist/magic-string.es6.js", "build:umd": "rollup -c --environment DEPS -f umd -o dist/magic-string.umd.js", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.14.0.tgz_1463368287313_0.27023717132396996", "host": "packages-12-west.internal.npmjs.com"}}, "0.15.0": {"name": "magic-string", "version": "0.15.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.15.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "4ae8a04a4f70a335ff17854a4b1bc020ef064fbd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.0.tgz", "integrity": "sha512-NFBH1Txkw12g2VEvKvXVUwYpa8SCJiwDOk61bARgALW7GRgngHL8GEMrZo1OEqt9kGjMPqBfuJ3Dr3lYiK/tzg==", "signatures": [{"sig": "MEUCIQDzBEgjZj0fag4mSjwmaGfB6LaaWboEzp4qLZWJ37lxUAIgRQneCCTXci03F4P463ZvKxbUlSQqwgvCAM9b31fL5R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "4ae8a04a4f70a335ff17854a4b1bc020ef064fbd", "gitHead": "eda2e5f85d6b74992271c6a48747d418b5d891ef", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "pretest": "npm run build:cjs", "build:cjs": "rollup -c", "build:es6": "rollup -c --environment ES", "build:umd": "rollup -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.2.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^2.0.0", "rollup": "^0.26.0", "resolve": "^1.1.6", "istanbul": "^0.4.1", "codecov.io": "^0.1.6", "source-map": "^0.5.3", "es6-promise": "^3.0.2", "console-group": "^0.2.0", "remap-istanbul": "^0.6.1", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.7.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.15.0.tgz_1463843671160_0.37603867519646883", "host": "packages-16-east.internal.npmjs.com"}}, "0.15.1": {"name": "magic-string", "version": "0.15.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.15.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "8390de9ea037897309d95eb345bd55011e28f62a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.1.tgz", "integrity": "sha512-fdsRmKHuncKCiXsnAgi7pO30DQxU1QWAGnPLdGSl5g52cFO/OPZnVOUhSukPWeUknq3rbZjIgqBujVTy/ZT9gw==", "signatures": [{"sig": "MEQCIEDiDRdrX3zZ0XKnVYmLx58Tynh3rlpRLWjyRPzF+1bXAiBMUugLKGXP9JKb1DQlAVYiK7x2XuNovBSVltMAb7Oidg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "8390de9ea037897309d95eb345bd55011e28f62a", "gitHead": "a19ac77e68eaa95e87f7eae1ea9c4ef8e14ac5b5", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "watch": "npm run watch:es6", "pretest": "npm run build:cjs", "build:cjs": "rollup -c", "build:es6": "rollup -c --environment ES", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:es6": "rollup -w -c --environment ES", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.2.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.5.3", "eslint": "^2.11.1", "rollup": "^0.29.0", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.10.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.15.1.tgz_1465494758225_0.16960501950234175", "host": "packages-12-west.internal.npmjs.com"}}, "0.15.2": {"name": "magic-string", "version": "0.15.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.15.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "0681d7388741bbc3addaa65060992624c6c09e9c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.15.2.tgz", "integrity": "sha512-xkHb690SyIbvr1x6PiHoP2M2rTkIt9La8gZLjoXIjcm7CI0a+9V9JqQWVQPsBLgt9qzkgtCuYN+/Thqw6crg6w==", "signatures": [{"sig": "MEYCIQD/I7/ynlKoorDvcR+KzVGG56913w46y5KlIOW4krC/GAIhAKOSIm95ppRzjWmbXOOxGdI/0PsbUL7LyZKDRtCF30vi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "_shasum": "0681d7388741bbc3addaa65060992624c6c09e9c", "gitHead": "e930c5ac613c19b2e5bc0633e67a1cfaaded3dd8", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "watch": "npm run watch:es6", "pretest": "npm run build:cjs", "build:cjs": "rollup -c", "build:es6": "rollup -c --environment ES", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:es6": "rollup -w -c --environment ES", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.2.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^2.5.3", "eslint": "^2.11.1", "rollup": "^0.31.0", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.10.0", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^1.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.15.2.tgz_1466695398998_0.367733123479411", "host": "packages-16-east.internal.npmjs.com"}}, "0.16.0": {"name": "magic-string", "version": "0.16.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.16.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "970ebb0da7193301285fb1aa650f39bdd81eb45a", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.16.0.tgz", "integrity": "sha512-c4BEos3y6G2qO0B9X7K0FVLOPT9uGrjYwYRLFmDqyl5YMboUviyecnXWp94fJTSMwPw2/sf+CEYt5AGpmklkkQ==", "signatures": [{"sig": "MEQCIBvKb7tKH9DRgE0NPDdA9j7khOUzCruoGz2XFMwiMXoDAiAj6LEbDfAxgjRWmO2tSAG0uqS2yye+CzN/f1N8p3uimg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es6.js", "_shasum": "970ebb0da7193301285fb1aa650f39bdd81eb45a", "gitHead": "b73dd5e57153bc5ec1856a934afcb37f9b8ec0d5", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src", "test": "mocha", "build": " npm run build:cjs && npm run build:es6 && npm run build:umd", "watch": "npm run watch:es6", "pretest": "npm run build:cjs", "build:cjs": "rollup -c", "build:es6": "rollup -c --environment ES", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:es6": "rollup -w -c --environment ES", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es6 && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/index.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es6.js", "_nodeVersion": "6.2.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"mocha": "^3.0.1", "eslint": "^2.11.1", "rollup": "^0.34.5", "resolve": "^1.1.7", "istanbul": "^0.4.3", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.2.1", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.0", "rollup-plugin-buble": "^0.12.1", "rollup-plugin-replace": "^1.1.0", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.16.0.tgz_1471014781556_0.07750329189002514", "host": "packages-16-east.internal.npmjs.com"}}, "0.17.0": {"name": "magic-string", "version": "0.17.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.17.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "c1c2c2f3e30d2a568f055a96ea11ce03664fb772", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.17.0.tgz", "integrity": "sha512-u+C8RigrXIrV88cuOjJuMtiyFci6BiKgGNTkVmrVTEst9dSmkCGsbWtm3sdbssCe7fzn2WAoXWYCRJznus9fsg==", "signatures": [{"sig": "MEQCIFOz0CI+EDf3UPzhSgCBWdaiEGKPqBQ7m+f4f6h4RohDAiBsX3cKtFvhdYdbl0gt4UjK1s/8tDiYuCRttGoIYz5W5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "c1c2c2f3e30d2a568f055a96ea11ce03664fb772", "gitHead": "5c24a580b9416890fb32f08f77f53c2c24b383ed", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "6.4.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.17.0.tgz_1480001839109_0.7467959106434137", "host": "packages-12-west.internal.npmjs.com"}}, "0.18.0": {"name": "magic-string", "version": "0.18.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.18.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "c5066abc4cda5d868b85eaf7e7731a15fa7903d0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.18.0.tgz", "integrity": "sha512-IDL/CDPXfLGbZ0IY9RI1BWVzb7nSL/nvgy1AQylncuWoaXUp5bzxGrulQZsAii+vn+MIzj9oB2MtmBP37R3y/w==", "signatures": [{"sig": "MEYCIQDUOFPfMFlqvtSh0TmgNAj3VTs038pCR7H6S+Qr2Z+qCwIhAOFRm5cMgVOw6ORnU3eGZCBRq+Uq4HPO134a1ig75Hap", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "c5066abc4cda5d868b85eaf7e7731a15fa7903d0", "gitHead": "891dc8ab8ee1dbb04f3b3c9aca2588d8b5ce71e6", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "6.7.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.18.0.tgz_1480620612902_0.2594221548642963", "host": "packages-12-west.internal.npmjs.com"}}, "0.19.0": {"name": "magic-string", "version": "0.19.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.19.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "198948217254e3e0b93080e01146b7c73b2a06b2", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.19.0.tgz", "integrity": "sha512-9UbmuWNz8mNNOmSnbwmvTa92AVjani9TqWUhBW35UVpl5W6KUu+9IM09Qs+xpbVvV74Q6TNXXfE25s0sx7Rj8w==", "signatures": [{"sig": "MEUCIQDDxQy4AMY4fPJ9hAj9Ss853adK9sA+eQfQvTH2VWTECgIgKsT0j1c131X8e7BzkFwAfz/jkBCZ8mvRetfyMq8/IKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "198948217254e3e0b93080e01146b7c73b2a06b2", "gitHead": "e4118a2d370e70119ba0e1a3a82b9182dffe85fa", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.2.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.19.0.tgz_1480695841438_0.9031513512600213", "host": "packages-12-west.internal.npmjs.com"}}, "0.19.1": {"name": "magic-string", "version": "0.19.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.19.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "14d768013caf2ec8fdea16a49af82fc377e75201", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.19.1.tgz", "integrity": "sha512-AJRZGyg/F3QJUsgz/0Kh7HR09VZ1Mu/Nfyou9WtlXAYyMErN4BvtAOqwsYpHwT+UWbP2QlGPPmHTCvZjk0zcAw==", "signatures": [{"sig": "MEUCIQCo1S+KxxsVcSEldR5ZJ7KXClUz2NAPaTZMzG5Aj/HdIgIgViKg/Kz9DxXrrUiKtcgv8w3X3/sJlk9RE0aBnpwwuR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "14d768013caf2ec8fdea16a49af82fc377e75201", "gitHead": "04ed25e2726a31ca8d1920e4ba620f3599d4c401", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.8.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.19.1.tgz_1494356645142_0.15994247351773083", "host": "packages-18-east.internal.npmjs.com"}}, "0.20.0": {"name": "magic-string", "version": "0.20.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.20.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "255f07e0b1459dc00d74636367e9200cbb5d529d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.20.0.tgz", "integrity": "sha512-hUtRcmCuD+j5eBzB42iSXw55hxrM1l72949VPangbxnas4RL5DhC/KQl6cGAa4XuceBlxqYOIhWKJLWhJ6ubsw==", "signatures": [{"sig": "MEUCIFCyju40BKKKbASjGIN24gnEpFZ9gNxTG78vcOrSpMDxAiEAp8Lp9lwd/wLAh7r67MCyqc24KW7ihyy5d84gEhusPMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "255f07e0b1459dc00d74636367e9200cbb5d529d", "gitHead": "19a4404c6e4f9e83ba8048112ee6beac920e8b47", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.8.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.20.0.tgz_1495059901683_0.20147866825573146", "host": "packages-12-west.internal.npmjs.com"}}, "0.21.0": {"name": "magic-string", "version": "0.21.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.21.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "a4ab14e93613630514e18c75b920e4f2225f5027", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.0.tgz", "integrity": "sha512-CvXZk4FnhCku0Bn4k6sSqSaBHtbcIuR+MziD3TQxaZM4ya3b/wYaTOt+PlrNTs99Qt5awP5n3IATTA10PZbcmw==", "signatures": [{"sig": "MEYCIQCfPmjXX4xSagq4EdTilFY9sJ4RErKUJo6eJLXXwHUF/wIhAJIDov7x3bhWsQIfg1VPr4OSv2tM5xhnjJwOiWWiJn2G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "a4ab14e93613630514e18c75b920e4f2225f5027", "gitHead": "9c4a4e9061d845fa8f52fc773c7bb7d5623e6cdb", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.8.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.21.0.tgz_1495801842870_0.4477760586887598", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "magic-string", "version": "0.21.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.21.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "ec76b6b6e2e831543caa43cb7952852d10e6d917", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.1.tgz", "integrity": "sha512-K8OMr/1OKX5ElwzPkcmO/ADDsbkujOh+kvVeDB6vxrOi2j8m+3Tq+NBkBw2XoAtmRIeDHMTnurMVHWZiHNSpdQ==", "signatures": [{"sig": "MEQCIHCqdKquEOLi3fB1C/CaAla6soutiZJi/mzAvpqVDoiBAiBIYUKjvfqfSWf6/7waqgCBKA2cMTIczn+b433KmQ49xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "ec76b6b6e2e831543caa43cb7952852d10e6d917", "gitHead": "db7c2271157f9cfb58ea77ceeb604d3d8223e8b9", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.10.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.21.1.tgz_1496068242702_0.49311729054898024", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "magic-string", "version": "0.21.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.21.2", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b115a7a00eeba7d23e81b24e98fa2d58d2f881af", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.2.tgz", "integrity": "sha512-OakjFUn+DHu/1J4XRfhWW/ejjpZX11guU9tSRAKnn28M7pIpI66lQg2VnrXJcL4Ju3ldLK0uwi9xZ4YsC6KoAg==", "signatures": [{"sig": "MEYCIQDBsfd6+jqRBnViAqwbh0h/4HGZT+WG8jDzVN5+sgAaNAIhALT42OPnySeH1+XErTISGkA/to5uFwu2Bx9ifWCNZk5o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "b115a7a00eeba7d23e81b24e98fa2d58d2f881af", "gitHead": "7ca62dbae7c160bc19cd34c0c9449019fd9c46ac", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.10.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.21.2.tgz_1496099187928_0.29702816624194384", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "magic-string", "version": "0.21.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.21.3", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "87e201009ebfde6f46dc5757305a70af71e31624", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.21.3.tgz", "integrity": "sha512-U66DN4L1NJsQTCaWbP9MTW+iydrqkYW8Is1QV+1tT44oZYfr0t1BUbtDew40YXrNOZSJAUYPZ8s4gd//2AZBUQ==", "signatures": [{"sig": "MEYCIQDdn4T+zELT2jNBGYLz3oR3V+4eSpmm/gRAGBlGtw6luQIhANIN0vCptyjThR9uF82nRwKM8L64qn9u+ucB2BJaOoJv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "_from": ".", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "_shasum": "87e201009ebfde6f46dc5757305a70af71e31624", "gitHead": "46ea184b4801f821b1f9c06c860a2ef43b9d0281", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "7.10.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.21.3.tgz_1496153131910_0.4547799015417695", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "magic-string", "version": "0.22.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.0", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d3a671d6a8c3c591dfeb82e75649cdec6b28445b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.0.tgz", "integrity": "sha512-s8grA/KB1/ccqjlURVfalCENqx/BQxa/ovfYIy9di1k96YhFIp45vY+qDniWuYoBmKA+rfEtDyIVwEvHPlGRpA==", "signatures": [{"sig": "MEQCIDnuqrdChiBXVdr4ozQzpmwCjWpZWRmEs7tqIOgz0c+sAiBfEPxlU1qVsfJxFzrQJGmWDplRNxqE1h1U9/WrcTrxMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "0da3bb23bd2d91d7e8ad47a3ee6bba0eda784187", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.22.0.tgz_1499449614734_0.9857330650556833", "host": "s3://npm-registry-packages"}}, "0.22.1": {"name": "magic-string", "version": "0.22.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.1", "maintainers": [{"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "a1bda64dfd4ae6c63797a45a67ee473b1f8d0e0f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.1.tgz", "integrity": "sha512-UG7eMVCbwYwrJ0wuA8yVykhTVWt0n6nnJ9UhWwRqJz4YXaEZctouKbO4mn82Leo8fw3LMCRXkXetU/tU18wfrQ==", "signatures": [{"sig": "MEYCIQCZWOthf2kDdJE2VJ58C2sAIb2mVv9MRaKWL6U/KLTaTQIhAOmZF4EVZ3j022cksABCfPLX+jt2JB4Zw820MHwEI13X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "0dfe7bb92a7ab2c24e937570253f08a14e68fa9f", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.22.1.tgz_1499449873651_0.29580193804576993", "host": "s3://npm-registry-packages"}}, "0.22.2": {"name": "magic-string", "version": "0.22.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.2", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "e0c54f1916d6264b34eb07634a5a1aacffded462", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.2.tgz", "integrity": "sha512-dcUSK1qjNRIe1u1UXfoCYyqhvmeTl39yJr6/emuRXXTdoiNX/Q4ZavOIEwpY6aoTiVBAoNaxBJfcdvmsmJRKzw==", "signatures": [{"sig": "MEUCIQDUHhoruNT4+2HuE23A5eQDgz6Vjwp0rQngqZ6HZ49L/QIgbcSWXwlh/8UwNv4v7okGRSf935pHZd9bO7ttLGCL3TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "ee7b6b77fae40444cab7f01ed165413158245538", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.22.2.tgz_1499485894354_0.20850203512236476", "host": "s3://npm-registry-packages"}}, "0.22.3": {"name": "magic-string", "version": "0.22.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.3", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "047989d99bfc7cbdefba1604adc8912551cd7ef1", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.3.tgz", "integrity": "sha512-pqnTW6coZiT8HvFKHoswarM6YTaYYN4imHlEtc+1LZC8hYdV+5sp0ZQqv7oEBhcDHx+c83Y1aYiUwPmoQlZmwQ==", "signatures": [{"sig": "MEUCIQDVFjBbuN2eewmW7CFc5OKVf0aObENe6nyhxeP16ZJ1VwIgZ+hgvH+3gXL9wiF+wrGeuVPfGvDoHzP6ZxO4YXQg3ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "098e8b33a681841f03b3ed398f7c44460a5f7ae3", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.22.3.tgz_1500680087223_0.9663583969231695", "host": "s3://npm-registry-packages"}}, "0.22.4": {"name": "magic-string", "version": "0.22.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.4", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "31039b4e40366395618c1d6cf8193c53917475ff", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.4.tgz", "integrity": "sha512-kxBL06p6iO2qPBHsqGK2b3cRwiRGpnmSuVWNhwHcMX7qJOUr1HvricYP1LZOCdkQBUp0jiWg2d6WJwR3vYgByw==", "signatures": [{"sig": "MEUCIQDRWDbeeFmMDWXyJPu2/+PO7DKTwgZL/OgIcCTg37mkgAIgOOUTz8IR4eKVrCWcljRzGhSIkvEfTGjprovqGPs7CDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/magic-string.cjs.js", "files": ["src/*", "dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "1377d84d7e517f049408e5720db71756af3d130a", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": " npm run build:cjs && npm run build:es && npm run build:umd", "watch": "npm run watch:es", "pretest": "npm run build:cjs", "build:es": "rollup -c --environment ES", "watch:es": "rollup -w -c --environment ES", "build:cjs": "rollup -c", "build:umd": "rollup -c --environment DEPS", "watch:cjs": "rollup -w -c", "watch:umd": "rollup -w -c --environment DEPS", "prepublish": "rm -rf dist && npm test && npm run build:es && npm run build:umd", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build:cjs", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.0.0", "dependencies": {"vlq": "^0.2.1"}, "devDependencies": {"buble": "^0.14.0", "mocha": "^3.1.0", "eslint": "^3.7.1", "rollup": "^0.36.1", "resolve": "^1.1.7", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.2", "remap-istanbul": "^0.6.4", "source-map-support": "^0.4.3", "rollup-plugin-buble": "^0.14.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string-0.22.4.tgz_1501439209483_0.48519528191536665", "host": "s3://npm-registry-packages"}}, "0.22.5": {"name": "magic-string", "version": "0.22.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.22.5", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "8e9cf5afddf44385c1da5bc2a6a0dbd10b03657e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.22.5.tgz", "fileCount": 10, "integrity": "sha512-oreip9rJZkzvA8Qzk9HFs8fZGF/u7H/gtrE8EN6RjKJ9kh2HlC+yQ2QezifqTZfGyiuAV0dRv5a+y/8gBb1m9w==", "signatures": [{"sig": "MEUCIQD4hUkqA42jDJjJ1SoiST7Un23EdcyaCT9Zm9kxPeXuVQIgGV7WXD3cqBK9TtErj7JHXDFW8vBx2IZN37gvWlCwhno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347442}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "581c1bfe3991662f972193271f2c44220a6ca556", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "pretest": "npm run build", "prepublish": "rm -rf dist && npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "9.5.0", "dependencies": {"vlq": "^0.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.22.5_1521039125131_0.5786631180712392", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "magic-string", "version": "0.23.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.23.0", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d8550fafb3bc34eb565b721f50aa6e44e6915c67", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.0.tgz", "fileCount": 10, "integrity": "sha512-4XPHnekXdHrQ8DQKMEW0COmjRzFsh04PXuGWWvg03p34WsIeON1eKYQUpbk5jpGJ+knEREiPyBEExefF+qdGNg==", "signatures": [{"sig": "MEUCIQCagMpMDwlAkaxVunFQApxWnPYAJyPQQGKq/hMzQ6cJNAIgXIMgaGd1P1cQ5rajoqGdBFD4730RQv0bgomc9jdNnTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 361677}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "59ad48e5cc5ed19462b00f91ecfdeb9a97433f87", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "pretest": "npm run build", "prepublish": "rm -rf dist && npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "9.1.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.23.0_1521057075714_0.714894302535668", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "magic-string", "version": "0.23.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.23.1", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "2bfedb1bdb482fab323fb2a1564831b9be6beb21", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.1.tgz", "fileCount": 10, "integrity": "sha512-F5xClni8mDuOFUX73fg4n3Ist1psd5ZC8z9DaQiVPuwbPuEt6GhpUBZsYSYw3bdDEGbzlQASd6IH3L5HXkSvPw==", "signatures": [{"sig": "MEUCIDXv0pcwYQ4IhJvx9Zz7y3cv4eaYzCfP1zvv7w6pG9PVAiEA3RWAz14D/LTDrEendBGx75Oquwtge05yo2CIhvuApQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359870}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "8aae0cb385e2727b0bd3b9429db119a645a98e3a", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "pretest": "npm run build", "prepublish": "rm -rf dist && npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "9.1.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.23.1_1521057222250_0.9694983614574857", "host": "s3://npm-registry-packages"}}, "0.23.2": {"name": "magic-string", "version": "0.23.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.23.2", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "204d7c3ea36c7d940209fcc54c39b9f243f13369", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.23.2.tgz", "fileCount": 10, "integrity": "sha512-oIUZaAxbcxYIp4AyLafV6OVKoB3YouZs0UTCJ8mOKBHNyJgGDaMJ4TgA+VylJh6fx7EQCC52XkbURxxG9IoJXA==", "signatures": [{"sig": "MEUCIQDTfKwIOQqcKZ5I9mXrBAttz/ls3Sk6yoB5YvScsWSC6wIgAZGkbyyFnuy7xoxxtfG5kmF6pYs2/OJ6aj86fTxhGOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360905}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "add91008ad88ed008d887a081e46fa6583e16957", "scripts": {"ci": "npm run test-coverage && codecov < coverage/coverage-remapped.lcov", "lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "pretest": "npm run build", "prepublish": "rm -rf dist && npm test", "test-coverage": "rm -rf coverage/* && istanbul cover --report json node_modules/.bin/_mocha -- -u exports -R spec test/*.js", "pretest-coverage": "npm run build", "posttest-coverage": "remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.json -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped.lcov -t lcovonly -b dist && remap-istanbul -i coverage/coverage-final.json -o coverage/coverage-remapped -t html -b dist"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "9.1.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.15.2", "mocha": "^3.5.0", "eslint": "^4.5.0", "rollup": "^0.48.0", "resolve": "^1.4.0", "istanbul": "^0.4.5", "codecov.io": "^0.1.6", "source-map": "^0.5.6", "console-group": "^0.3.3", "remap-istanbul": "^0.9.5", "source-map-support": "^0.4.16", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-node-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.23.2_1521118011262_0.49430524520870534", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "magic-string", "version": "0.24.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.24.0", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "1b396d26406188f1fa3730a68229562d36a1c2f2", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.24.0.tgz", "fileCount": 10, "integrity": "sha512-BhLyoHHzjhXGXuTHQW9odsmdkoBtBGorgElhMVlXP1fmM5xAk+Oe9wXwD6TAnm6Fjuq8ItQOePf2H1jLzor7CQ==", "signatures": [{"sig": "MEUCID8/3o54NnL48EC3IAjFzddJ/NRN01oocW9B305BAWhXAiEAtwYxOSOcNsnE6+BTPTNswrCm04ScdINgOj4gRZgJ6Mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343074}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "48a413e4bedcf2024ed3b00d551dcc3be18fcfb8", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "mourner", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.10.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.24.0_1521473452554_0.039500734892987444", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "magic-string", "version": "0.24.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.24.1", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "7e38e5f126cae9f15e71f0cf8e450818ca7d5a8f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.24.1.tgz", "fileCount": 10, "integrity": "sha512-YBfNxbJiixMzxW40XqJEIldzHyh5f7CZKalo1uZffevyrPEX8Qgo9s0dmcORLHdV47UyvJg8/zD+6hQG3qvJrA==", "signatures": [{"sig": "MEQCIBcnyfrB3xkCP6Rl+dd+Y6G9E9YUqvv90sinfal6+iCMAiBN5sbamf+tQhaVrfxMkyntxOffY7s7EdCH6N9CqvZYkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+tRqCRA9TVsSAnZWagAAJ6YP/2J7ZbjtNLRKHvBIqGIQ\nxjeGz39L80tYl7Lcl6NvddSwqjTYudc29S8uRbguLsCZtN8z0GKWrXfhDNuv\nJeRvLKKIynH1T5wuXQiH2AYDUGgtIub2tMt/2bRADqSII4TpGXwdxLEKRAJQ\npVoXbqRuQWldXgMBiuFXmmAHwKmCao8h3GZN2+WlrXMDfE4xv7JQgfW1cFBu\naRsi34Y62sjkAEYhOFGDJ9gRlbG2evwmv7qVtUr02OelZ72mD9rZrdbFhES9\nvZnYiVp9FjYmfcevP3H++2UKutvNHg99GG4qWXhpMyT48PSl6OALqPEC+oCR\nLtjuazmLyzq0mFEQP2xBcrnf3Z6yQN5ErVC7Eeicb4vpgvWCEMYk3fEtPyyq\ndTFrU9AEObGBhylqprVD4ciw/1m3A+dkZ+exCLQeFiEM69+3nxU9hAjzQpZy\nLywN5bzeIDp/PTqwMJNTbLVviGSSpfo/cI3PPkohdHPHb49iCq8ZAVM/L2Sn\n71xYxjgVa8Qonj274DyFto4Kq5rgTZUloMEAzfk6tRyzPR+zyDnmi+5Ui4Q7\nTDmngHO7mJ4trwh9CLaQt+2I62F9c0zoqC+BedNWFZgPdWsiCQE1p11EwjRJ\nUCmOsAwSigtwoJxUB2+3Gl52piuiOEDScZ3KyFUrfwjZR5NoG9SEXwMiLQCf\nEf9D\r\n=dbIp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "b32e32f4db0b6af2903691e8fc330c7d8cd3a867", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "9.11.1", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.24.1_1526387816915_0.17059658353412832", "host": "s3://npm-registry-packages"}}, "0.25.0": {"name": "magic-string", "version": "0.25.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.0", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "1f3696f9931ff0a1ed4c132250529e19cad6759b", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.0.tgz", "fileCount": 10, "integrity": "sha512-Msbwa9oNYNPjwVh9ury5X2BHbTFWoirTlzuf4X+pIoSOQVKNRJHXTx1WmKYuXzRM4QZFv8dGXyZvhDMmWhGLPw==", "signatures": [{"sig": "MEYCIQCB2Cci/GThEm5qO5EH/+Agog98RnRXMewRh3BPvPvXGwIhAKM3ARJei81QJfM0Yr7JablUd9DYE3AxZ5EpTbO9EBIr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD8B7CRA9TVsSAnZWagAAyRIP/japlTUNiyQe959zsQ1z\nZ8ocLuC/925tenOwp0O/PSVayA3mKs7iYAt3cweRk4qC5ym3i4sU0b9/S2SH\nzKo/mGAU8mLuacAFPEeR1Gll65w0xiYV0rmSsuFqao2sGJM2K3Z3uYaLr6Qb\nlXwGERQEvK3mrGQSSHOc3JtSLw+AaO9mLWMyNU15q4Jo8rHJpDx5kemYk6Sq\nXuxgxRBKwrt2vmM0xCzSNdx9KsnqCm4yfyo2LkyYgH6STtN9a3AG3jE372uN\nVFNSlHKujm+0wDoSOW6xoDWqQOtYd/Ra67/7y1nJIWkQMvc+yoLH55w1Pkwb\noG6/gH2eNGtdajrReQh1G2kx7bwxU85QTpy+SBOwA6qukns4bhBgLAIB7n1W\naYD83yWXnWqccP5kYh2m7GllRcqczL9zGPSvQMU2OTR4Bgv22fwvDrDC2/dn\nEeI+4i+CAYwIotVkoUZmakXzNXvYBNlkmv/S2X5+H2RVA2Jt0Nf6bE8ac6/z\nk+NzEQslTuLEh9MxeTq+LSyfH/9n+KCI37GsnchPX1rFOJ/DI5UH0iypzu8Y\n/w0PVK88b3bKwhiUQ5RFggKobfTA8WbvFRnZv2tHfBOJhQOz/YZRd25VXwuA\nLOFxIZFDF5nvQFktYdoScL45vtUkdACeUuB7LWyhn3kbLGQYcCzilgwJkhlJ\no53b\r\n=Cvyr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "files": ["dist/*", "index.d.ts", "README.md"], "module": "dist/magic-string.es.js", "gitHead": "3466b0230dddc95eb378ed3e0d199e36fbd1f572", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "mourner", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "8.11.2", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.0_1527758970235_0.28909391665292405", "host": "s3://npm-registry-packages"}}, "0.25.1": {"name": "magic-string", "version": "0.25.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.1", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b1c248b399cd7485da0fe7385c2fc7011843266e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.1.tgz", "fileCount": 10, "integrity": "sha512-sCuTz6pYom8Rlt4ISPFn6wuFodbKMIHUMv4Qko9P17dpxb7s52KJTmRuZZqHdGmLCK9AOcDare039nRIcfdkEg==", "signatures": [{"sig": "MEUCIQCXsJ73G8JNLwCF5vGbkGFrIqVlS9w2R1/qPAa6aiZf1AIgTlqZjCLaTtFjEXXKItpy1vww6N51bxp6NqPw2NI+Ykk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboWPjCRA9TVsSAnZWagAAJuEQAINm96lVOboXV0ZMezx8\nB9qPhdNlRbOGtuGGJZsisJJURR6JA6kLfzpRdLVC7XHWpsw+XI2WCSu0HRr6\n0ODRzSEUucdib1pZn/59Sb3HOkZk+x0tdCfbTOlV75VMGP0dKuXlNNmo8lh0\nKIgkq1S/1yYZlM0NFjogrKjpT1x2wwgGNb2uSIVrbyDytDheChQz9aMwxJ3X\neutbcM3ANwHzeF53WFw2teFqc040RRUEdVTMKi5Fit2hqmNff2Jr+VWOZmee\nns1y7C/Gji2G5bXe1I7ELtggbey8VcWXN9keS/QzxDh1oD6XnWUQebJlTYUX\nATWw3bVAp4u1xSF00QZERSanf9CCuANjvI/bNefB6HbB+9wToMc61cPH2H9A\n6fIPvtMYZFP2HBijVUikkWfrDJvxsGQbljExcTBjAc3gboCNh6PyTjItah1j\nMEJz6UISLsrHCePPmlkM1zt8BUt1tQzaCNrcU6e9YeNzFsBy7o4FCpNQ8wlt\n9xLykoz9DBFYPQFH+MzHZ4R2gB4Av27XsSseJM+hNwC8OLC7E727PBTZUO58\n0miLkfqpxEdPZGziiHUStNTQYuPPVYVgiFqwEun6jqDYJu2aqgLiCglc6j7J\nVlzp3ZuESKAhmq5ld6WZ3JsbH7hYmLhDaSP1DYFEBvgUW2uG26dY5XGuMW3T\n5iXK\r\n=I+v0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "b584ebf31119031b710e9b2cb1abcbd9b41daee1", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "10.9.0", "dependencies": {"sourcemap-codec": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.19.3", "mocha": "^5.0.4", "eslint": "^4.19.0", "rollup": "^0.57.1", "prettier": "^1.11.1", "source-map": "^0.6.1", "source-map-support": "^0.5.4", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-node-resolve": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.1_1537303523053_0.6478245276155192", "host": "s3://npm-registry-packages"}}, "0.25.2": {"name": "magic-string", "version": "0.25.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.2", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "139c3a729515ec55e96e69e82a11fe890a293ad9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.2.tgz", "fileCount": 11, "integrity": "sha512-iLs9mPjh9IuTtRsqqhNGYcZXGei0Nh/A4xirrsqW7c+QhKVFL2vm7U09ru6cHRD22azaP/wMDgI+HCqbETMTtg==", "signatures": [{"sig": "MEYCIQCHBrByefI190fosP94XGo3AoP/xqA2GvyRRYx5fUl71gIhAND3qcrobTOP0jGXed0t6orIekrtNclDsv0l7Hu5kgsl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360760, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVydyCRA9TVsSAnZWagAA8u4P/jnxPT2/FFPiABpPOcLD\nUQKTlJ5oL+oFAprge8Zl1pp4XiXumCcqpgNHrJE1d0w/yXmyRLc18mUWOmtG\nvBIwVp9UsW3LOLvJxEEIK+QwVlq8D+ogOF8iB7P5Yh3aymxi+TaW12kaUiKF\nAL1RSHhQ0J0Rs3bGmesJ3OIlQYah9VMwxo14O/FXOBARtLoXOuKiko81Fkzt\n7DyFnJxt6vpJIDh83eDFxblp3PzE46cLnynsfYBbzPSmVgVXY7nBvA+6AS16\nmuSbGnBjV+szCNM9XnncPjhHQqsP9tTG5yR+pjQH4NG4nwgn/n3K2G0yvNJs\nL5Pemn/cxUV96uSFEgxTWLllqxyO9R6VOKHzeclhkwcS42eTARlTdk91FEIu\n6nk58NuH5I4VJ14jvLHrUuuhZFZgXLI25Lys9Z6mXW0nneKFgaDaLJd5TbVj\n8We5LQzCb4eSertYn6vqkGxUkXYBRqve5/Lk+lJjtTgINLtaHo0n5Y7E6emt\nxgjqgYFjN/F6+Z+f0+uZ/36fcI6oUCFfn+tE/iA7U2CZYF6Mbexp5Orp8mBS\n3UQ8o0fst0fu2JqmeM4GIFznzia60c6RZZo9UyMWmZg5xQisMdOiwo2kEPVK\nITsxkRls7df16o598SZSPd4BIzKeqaM8C4oE3o6LrgHp+ts2ON4+it0FF/TJ\nWdfr\r\n=uUna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "751f1c00316ee9553974d29eef5caf9546047df1", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.5.0-next.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "11.6.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"buble": "^0.19.6", "mocha": "^5.2.0", "eslint": "^5.13.0", "rollup": "^1.1.2", "prettier": "^1.16.4", "source-map": "^0.6.1", "source-map-support": "^0.5.10", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-node-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.2_1549215601974_0.43090334683230025", "host": "s3://npm-registry-packages"}}, "0.25.3": {"name": "magic-string", "version": "0.25.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.3", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "34b8d2a2c7fec9d9bdf9929a3fd81d271ef35be9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.3.tgz", "fileCount": 11, "integrity": "sha512-6QK0OpF/phMz0Q2AxILkX2mFhi7m+WMwTRg0LQKq/WBB0cDP4rYH3Wp4/d3OTXlrPLVJT/RFqj8tFeAR4nk8AA==", "signatures": [{"sig": "MEQCIFPx44smiMKIRs1FR1+pYrluzzcuNSVc8ArmCtRrPf5GAiAHdvJh3S1XUeg40pD0YZNa+FJn1fMk0+m20xYoEaXBLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGe6GCRA9TVsSAnZWagAAAYIQAIqaDTd+2/aF/7DrbwuF\nXU5GBERFRr+OGTEAANcXK2GyVkWznVfVrmSquMCV1p0pNqMCC7S1i5a5G0Gx\nbh5xOWzHoMGbvPMBWAoUQYhZKlaj5pOlm6RO0i7IWf1+q10foAqwGiDYSfC1\nw2dmsJdzH+0oWCUpXNQJ/SlC9aaUFIi4yW2zTLjKWPMtsdwXgc+qXJZAvLs8\na+RwNUdMX7ETjuwd5Xog7pg2n6Y9J5jGhHmn37lOet8DSdmuBvc9zlpO6eS4\n/sowI057z4A3EtKcAfy0WovFWoJXGVIvHOBEnVE3u+mAkIava/cwGZwk6Kc+\nSCUgCNk0/aznHTzBCr4gxV9PrwDg3JB+xU1jAJlrHSdoPOmHeGKoNjbPyCeI\ndNyUWKwb4zggckKMyUg6LF6hMPgzsOgU+HVLp2MuXV0D1srT7Hgoaok8pt8Z\n4U3R33IJ2T15jNYH5WCWYqnAiDdWGhDbyLr5lsx9ZZFPvyLUcKHTY/OPgpOz\n10YSJFL7I553SY0zFHZCrQXVPn7fUXy8wEfveK19hwBnutBZ+BT3tut37Mak\nlbz7bCrUDhLdvQa9VdJszqhY5APsQvosxMcythsDy5Vf4/8MBB0Ngf8KdRN+\nDeqyVEafnjeCdEEws4lC95mYj0gpEkkFBcov9jvhNxPc/dl8nnYyNDgDmoBM\nZT9F\r\n=VUgc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "a312519cfe9caa78ade7f09cc2b07459d3d17f4d", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "mourner", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "10.16.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.3_1561980549322_0.20830084178974606", "host": "s3://npm-registry-packages"}}, "0.25.4": {"name": "magic-string", "version": "0.25.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.4", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "325b8a0a79fc423db109b77fd5a19183b7ba5143", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.4.tgz", "fileCount": 11, "integrity": "sha512-oycWO9nEVAP2RVPbIoDoA4Y7LFIJ3xRYov93gAyJhZkET1tNuB0u7uWkZS2LpBWTJUWnmau/To8ECWRC+jKNfw==", "signatures": [{"sig": "MEUCIQDo61Xl34jVHfwlMc73qEDLphw1shNNu2XV4cUKToE8qgIgDEFwmsrjEC3HgGlbPtHpbFBJaU56+mfXny8hS2CUdr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkhtgCRA9TVsSAnZWagAA7WAP/2PQu7y+9iS9m3XicGo1\nnPYnUMEn+ui83c5PJjbj4Kdi6MvLkpYPP50GG+8pNNc4mf315pbzViX+TJjZ\nxiHB0rfrqR2upBPr34UkTp2caly1O2pXslWtoI5LI7ED1MDyf2AkcxYsy/z0\nU+OfpwE4do4r82omLwFZeAQzC/AJAddtzL255kJV9dVsICjaSau1bxe35NPB\nJk36XtRFolUuI8aCsjded3ZW84F/mh0QPRjqG12oJ1NutQGClxu6MI6yIVve\nWoGrQ+scBtxW7ok7jlNvrbbGwXR7L4wTHmHDTvB5Vuri5yLZiknfbJjfoyBp\nnvFKw3EgDgFJDyJ5lb0O0DxU33Q0tQ/oNk3ORjOZmNuCSLVORr4mrqs7hknq\nHsDVMJk3d7WkqrK0bIR/yZ9CbriIKVVoqWhtw2QXle9WV72NKtf+TE+5/UEp\n8ZsAmBemIY0qRSYUoJj+/Y6sgQdRD5v0wqIpbJAsZZgcP+lodSGAJR7IGmay\ngIjjJDYrxzi3NVWsHuCm4oNb7RDU+XEui6ooAkiNaoj58wEBBTf3iVCpZVv4\nPbe9Br0BJnSYP08KgU3689cvJaHPVQrESaqJvvtC+memJ1Jj/cZuNp3kSwV4\nbByTdbuVomyaMUS4FBuWQP61+w4tmQJTEGMWEq8Y4KN0XjQiGNGZ53/7Eucn\n5//A\r\n=OhX1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "51e7f5f7e33a0dc051e936eee4eab61c1626c696", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "12.2.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.4_1569856351374_0.16227823583871825", "host": "s3://npm-registry-packages"}}, "0.25.5": {"name": "magic-string", "version": "0.25.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.5", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "694fa8c6b9a51d83cc4a72c5b6883a7cfa890e40", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.5.tgz", "fileCount": 11, "integrity": "sha512-vIO/BOm9odBHBAGwv0gZPLJeO9IpwliiIc0uPeAW93rrFMJ/R3M665IAEfOU/IW3kD4S9AtEn76lfTn1Yif+9A==", "signatures": [{"sig": "MEQCIHieGPAfIlhwp5+WrZGoL4qeLtAs3zoOI3fKxGzVPcn0AiA/tssOdIOcQn3nrS+tDL8ISjVw7hdwX/VvDP1fF+C5OQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDmjdCRA9TVsSAnZWagAAE0EP/1mXek2xeKgXKRmT0sLj\n8RGvzRCJoIbSFXE66r5vENypKCwKF1XxMWgKFfWbaR9/UWPqRTO/5hylDThv\nxh9B8MRCaG/3sXLM+1W7p/nt3RnMe+7Vvl22umeptBAM5UnRQ8B1u/fFKmum\nNXjp+UMmxhVziBNwNkLPM9l64Y8vVdQI1V3Z/QIVY5zf8pgFONrhyMjxjI/i\nFXeGMe6IZ1cPoM/jyQREjrKu68E8TzxjLnZHSIWvC5/0W9xUrYMP0KqoDIJc\n/pPudVMWYjtKLrRSQijQqdh5AYZdzecoKDOAbPRj53BvFaWneZ4DO/8tMKtc\n9PhFpY0AFNKP7Zwy6GofKDeiFvNGC1oHD0oA3QOGC3JdHQEuIZDqbMpG1Qzh\niMMgdY8DAJhQHDKSNEvs6STS02WL9Eac1+hiWtTY2+JPAby1fZX1lRJgXwkz\nOwb4qYHlx85rlC1lglZwg3au1q4lmwRYoZQekRdf60twavjk/19feiKVzMAh\nLUFn/wkejH6MtLF8d27RMUVSG8jZOKaK9ZslXM5qhOt1/qjlOKWwG0Ri9IVQ\nK+NYuTErqusKt5F5Gy1Iekl6c0JR8tVl5HuAwjRqzuqxHxtsgzKju+0NazzO\nMU0hWbFpOwkyVJ1k5QVyL07HJsbU3i7laB9l+Nb5wmoEwv4gHrj7ieZF9ck+\nSkm4\r\n=oP9g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "f0da0663cc64255c61aed47678e7dda376cdbdb6", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "12.13.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.5_1578002653394_0.2643820467968061", "host": "s3://npm-registry-packages"}}, "0.25.6": {"name": "magic-string", "version": "0.25.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.6", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5586387d1242f919c6d223579cc938bf1420795e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.6.tgz", "fileCount": 11, "integrity": "sha512-3a5LOMSGoCTH5rbqobC2HuDNRtE2glHZ8J7pK+QZYppyWA36yuNpsX994rIY2nCuyP7CZYy7lQq/X2jygiZ89g==", "signatures": [{"sig": "MEYCIQCdwz4HIHkGxdy43lvJH1noIlnlaNEhObUjY0lQaonWuAIhAOeecvvEtOg4tomYexGlZvbkNgRQvOU2NmZ45TI6UtoM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFy6pCRA9TVsSAnZWagAAdKEP/jXP+k3/lY+SR29/EWZ2\nlbzcA/nh5EXaqbPkB09D8rcj9uCIKpiSOZy/c15K3i0/zeU0iPPPtO5QY6To\nsDWlrklCW1I6zXjnnxQSERZefH9rZkMp0w18TrYRjDML1ig62734GM4zz6T5\ng226mfafYLphfyxsYmZapJ/Ag8yU7k0cX4z3/zbDr+touXX89rqNHdUXumnX\nj7SVzp6TplK96dZeSg8WiPY5iX8nRRqFrt3w2rDBrFy10w8nc6/9/RNVEaQZ\niR254queuik8wM5XjzeDhZ/GxZKPtekiy1BsOE6TpDt28ryJ9pcMN3eJt5Tv\nMoxZ62YEtz7d1jI4aL0GR4zbKReo/VeIZUOEQpyOR+GOrEfUf/GqluxcgAL5\nyFG4ySCgmGq2Ui3GWLwkBO8WKTkPnfCs6L1dbgmW7aHxXNNd5d/cJBgx8mXM\n4sEfrklgO1qGjiiRX1e/maOhH0QHRspintn+vcp1gfmqx5EWX1CS4omTkey5\n6eJcAxF7tDgUKDep0X94bNK9KBmK/lZpjOphuSfjfjuldBDLozJBEUrSVVRB\nwaV97mICwkPO+rw3iB8R4C2q4sF3A4t42doWElPmhi7WHP7ETkEtwNPBCa0l\ncCz2+PLbdXVW6Zb4Cz3VRAa419bNzJNCUWMgyyrCAmVcHI+eL4nV5YTCSOUg\nPag4\r\n=Nou1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "900c6cbdebb1bde039e5bb258935af0fa7565563", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "12.0.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.6_1578577576965_0.031620161565109495", "host": "s3://npm-registry-packages"}}, "0.25.7": {"name": "magic-string", "version": "0.25.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.7", "maintainers": [{"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "3f497d6fd34c669c6798dcb821f2ef31f5445051", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.7.tgz", "fileCount": 11, "integrity": "sha512-4CrMT5DOHTDk4HYDlzmwu4FVCcIYI8gauveasrdCu2IKIFOJ3f0v/8MDGJCDL9oD2ppz/Av1b0Nj345H9M+XIA==", "signatures": [{"sig": "MEYCIQDbn5cuNWnfL089im0WJqyrdXsGcW1w5/BsEsYD5AH9/wIhAKX0L45oMLfOoFB4MEdN0FUUPhgbAhtrJXWuIkuyBPSw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYkuXCRA9TVsSAnZWagAAOPoP/A8L30zQIZhJCVsrXHP2\nqs4UCE2fTfA14ZJxFkFgMfaObfTc1KbNbhKTFoGlkVyp9maVMPgzrOLyc1Uv\nL+uZYZ//I20cWVQvrwugPeGKpGLaSEzG6N0oOp/4hSwWHU1gu6sXPTV02giA\nq3jmmkj0TW1Gko/Y7Ui+9SPAdCjGXnbRp0cw+auao/jXjmmgzwib0ziOEqJn\nbLpgJSx11pvLkSzzPE1djaVVXvGW0jgoaztEGMt656yNYLrgQhZKeMZfVnC6\nVQl/mfI4sOYEwR6eB5yEDQ0AzIyEAZudby3ajEYe2OZIY90ESldJ/R20L2dQ\nXPhDgyCjcIlh7HaXNbR29XrpWkbeMKjahh8kQRwbqC0NGq2FHZpjgN4Q/zxU\na5bf4cAVcO1rEA2dQqlcT1shPYvRu5mQZmcnL78blKkolvTqOX2wV3mMmbsO\nn9WT4uITLzMd7fLhXkrubGFIzD836nNgw0+BFVT83SrnCv649uwl6vfU+f/p\nPajsU/QW48IVLMjIZc6dUhuij5cX6EF7AArUo5BAJgKp5RQYWk+bWjsiWu38\nRBWKsSEgzWw1jfmmPsvVt72A8yFnQUdE6jpXoI0tRw+o7nF7l4TJbOz9WSPh\nsHcCM0wHTSHmChIt30j8V+2katTVgRY/1dyNmfFqudcGvgTyzQh64egs1wnA\nDMKp\r\n=CNM1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "389c1f1688b5f58db6589601f57b901a2aa43ce1", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "12.0.0", "dependencies": {"sourcemap-codec": "^1.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.16.0", "rollup": "^1.16.3", "prettier": "^1.18.2", "source-map": "^0.6.1", "source-map-support": "^0.5.12", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.7_1583500183439_0.9874855561145381", "host": "s3://npm-registry-packages"}}, "0.25.8": {"name": "magic-string", "version": "0.25.8", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.8", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "9ee85aabefa5135cf8d80e96adc83b595ae2c5e0", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.8.tgz", "fileCount": 10, "integrity": "sha512-n9NlSgfkB2rPYjSd/EZDoQcsXzwYAv4CIB/vi3ZSvZ2Tjax5W5Ie1NMy4HG3PVdcL4bBMMR20Ng4UcISMzqRLw==", "signatures": [{"sig": "MEUCIQDEWr+136Ln28/u9kgfGi7VZl+cmvK+kU+TSu70R9yO2gIgVXO2xtdwRN196kbdO39ckhib3gw2oYyhDpbu9th05gM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH7ZmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC3Q//Vc1ZlTpSZ0AeWofOgceoUSQ2v65cp/o7QWEc7mipputDqeoi\r\nXWZv0d/InZQH5kEWkUwEzESTqjlVgCvgerO2lD/H5ccU24ve2WqeErKqmxv+\r\nEWwibxDiglO3v8m8Ls1zDnIdtX5dtVtU7U1hVU2cIUVimZKw5KhhTuUau5ab\r\nKAS953Cp9x3DLFfmJF0Qk85uyrQ4p9ADNA5N6AssJOPks0UQ6oPE3h9f9Wl0\r\nDQ2feUfk8PCaMHav8Z8FyAari+EVY0CZzfTV6InljrB929IU0X0H1hG8xUC3\r\n/cg/YHQhp2wHfVJjL33YOwotdhMCQdOYUtYYUrcnnXYa5sJihCUtDlifcwtr\r\nQypmGxb2gVHBFqfi4dors1LxLLmhFghhdbCOP3srbwmcuWuQ4YCQoxFnD8Jf\r\neLc+sOJCtV+9szlZqLgpWQkCPbx8/MioDDs/64ykOe393IPKTnu/nGI/6CZS\r\nUH0Dgp6PwDMzyiaMKWxLbett0Gqq63qkrMf2dQJ+vjbSLtUIzL2IwycypyKK\r\nbU5WjHwXGW7N9/iyVX+6yVq4F0oVv7PG532lntaK4iEbKuqG9THH4+T0BbjD\r\nBaC7PQVrzMocfbcQe8yWCQbAitML/QbWMF4jZr3OgvyGfw2WgGUoE5l3aZlk\r\ncB95kYMnzjxDeB9d8p4HeWFBB5oQ7L2uooE=\r\n=LzKN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "e04da4bf858ee0ba2d8f2bca3af6050ce4ef1bdf", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "16.13.0", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.8_1646245478480_0.8380779867116084", "host": "s3://npm-registry-packages"}}, "0.25.9": {"name": "magic-string", "version": "0.25.9", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.25.9", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "fileCount": 10, "integrity": "sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==", "signatures": [{"sig": "MEUCIQDV8jwBjaFd+UCnIJ2mVDguMYVlrR3OXkx6DiSS8W/f8wIgJXOLH/EUy9wYN0a6HdqJ8zH1CfWuDJ9Okiyb9ecS4Js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIHOYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7eg//b6qpgtc+FurMJQRYvhU9BQPkLx+ILEOPzmw5F6pik3p4vZfd\r\nnWm52DYJN60O0LOHqn4/oaccJoN9UgWu8O7XLWSYOJthVFFgpEjEHn/K7tFb\r\ntLSCAfH/z27pUJQMFVCG/I9Cqkm1KiwTtA4vcU0yLo/J3CUHOwzHhViw7Y1o\r\nQUl66CNGNJ1xS0mF/YW7I1MWbxlDAitYZWua+x+t+pZUmIZSim9eL4+QN2Wd\r\nEkZmd7qC8sUMU7jYwYUrjBBsYODyoHA4MT21AoEu2meOGEe799sxlxAB6ahs\r\nuTOKtzID+j9YrBi90nqWggIRBIPAHQKIFS3u6urub921A/Hoi7LYcdV06h9O\r\nwCq7qEi1ECPh8XjO4SNzu54FJxoXGQb1wAHzuVMJP2Wc2FU0SbcYi/zBTiwF\r\nJZNgGG/Z8Qsn8/He2LilwfwR6VZPloONEEcI7xpLufcxAQJdtUJDRgDc1AGg\r\nSPv25CPe4mU19Faojsybsu0xkcTtTAJ2DJeYpCa8SVh89G8FCZkKCDK49x6i\r\niuS3aKdH1TVtaHjVDA1siqRqG4iclwMVRPoLfPLhOAgBJ93sWW8HVtkssHrh\r\no9zASPgva3E5AWw2vkFKQljuK6SAqnIRh1iA+muBAI1GjTzX5EQViJ9c4goe\r\nonQO2QsrVQWLRQO601TqgWHTFJgO2xzQIOc=\r\n=bJY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "gitHead": "69336fccbb46e721d58faa5c7a0d0b7ed6ee09d2", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.js", "_nodeVersion": "16.13.0", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^7.32.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.25.9_1646293912158_0.5575766017796417", "host": "s3://npm-registry-packages"}}, "0.26.0": {"name": "magic-string", "version": "0.26.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.0", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "3b7027a9b44c12987d8af177872765369d8d4da6", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.0.tgz", "fileCount": 10, "integrity": "sha512-TK1tGYBWlWzWabm3Lr3w1nb2GcN5ShN23jRIAsHOdc00ZffX90QPwQOALki3F+EnI40BoiZwlb4K2jMM5z3Xpg==", "signatures": [{"sig": "MEUCIQC4ZaF0qP96FLT3aYLvQFt4gAS4/JtXLRgFaSQRX+wjkAIgBdbLsoB4BzVZaZzdeASUj7TsSKsQvkC6EC4vcGqer/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIHYcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV8Q/+Npwrq1qmYRfgdjM8kVaJuTLKxxD1JdsVC3t/lYOhA4yNmIYY\r\nCcgzJ0dIP00fUFoM+E3DeMl/eq23OO9pOuF93d/bajPiCBIJy9WEPliahpoJ\r\n80ZUmb57JXLVNsEFuHUdf16vHdl8EyALDY7sboW+DmCWIvnJzybMZKOpLLgW\r\nUEjt1OCBPuFNPEHDeIWPRQST8XZ/tqBJt979tgg84VZV6L1Mv4SRudWhGbx2\r\nqYDLqB3jXBx9t3pKbPlAXtJRISaEjy47fXfk6mhr6rVVqvrHNgfKUw9d4qyj\r\nR1ipkbmdP2WmqJ62IwlPhJnR/uc7x2bwfiPSum+NP/DWSC6zpV0404oOYbBv\r\nZChZXg1olVvXPvd9+T1WSe0Q6BDgJV9J306goBBcAorDkQT4a6j3g6tloNTX\r\nqybvte93LC1zjLH2H0WjWcCYcKRrFWdG8tiKjSjKmcBbz3/OEKqaGviX2DAg\r\n43TxO2eodOaeVdQUOXZnBXtpalMquhmaQSFRIhhkMu3YbNBzgEytSswL48p6\r\n/Gu5+pSLA2fT94Pn6FaMA3BLCc5I+PG1IfGI2jmB37/fdR16jxfMenHhWFAy\r\n2AQF/GFvyujmoroEiCtlhJfWCuhnIvB0md0xChYbQsnyYoK/6SUt61tpdRZQ\r\n93Z3IEt83eco98evlm3e+boxOtmcQ3g5SDs=\r\n=SWte\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "aa33b07a2eee748e743f26f95a99ffb1615c0d30", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.mjs", "_nodeVersion": "16.13.0", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.0_1646294555934_0.818248574044881", "host": "s3://npm-registry-packages"}}, "0.26.1": {"name": "magic-string", "version": "0.26.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.1", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "ba9b651354fa9512474199acecf9c6dbe93f97fd", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.1.tgz", "fileCount": 10, "integrity": "sha512-ndThHmvgtieXe8J/VGPjG+Apu7v7ItcD5mhEIvOscWjPF/ccOiLxHaSuCAS2G+3x4GKsAbT8u7zdyamupui8Tg==", "signatures": [{"sig": "MEUCIQCEV7jdAxaS44U4/Nq8rJYYy1aUlgKxk4wQNzKfL1o4FwIgCBeUg9W6gKTCTTgmGcHoPp/+Gww9BrbrlHbY65WI6t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIIkiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTGQ/+MIfu5fZnGArX4QNnl/o3nOCzmBLbWtcjUdktmfw1ZJLdwWlU\r\ncbwc9Ld2+kRtER/O+lE9ex0GHSFtVf5tCCZSBuwIA5S4czhsbB/dM8eoG3sE\r\nLeevVqfnvHSpPjELYqI3qaIK2epJJGC+T7lAygSlYz07uLGraNgn3hPE0da8\r\njYHvS9pb2528rrYW8ht4wFN2PLVC/9TpuT19d0lsotIJ68M6U8BXUOjir94U\r\nEjOGCQNidpso1G6G0xQdclqciL7IcZ3w8SvAmp/hGMsItsF9CQWvCHKliX35\r\n+RPErR49IQdX8VFa9tKdLF3huYPGzZGXtLC8LjHJQCn4d37XWA4DmPfoQWdu\r\nDBfyAEq9LgXJznuloowtWct/X9oQH/uEKvhUeyUZGf3vjXkF+5M84mdJejcF\r\nMEHtuRuDDBJJ+JNZFIzhgks+m9DPsS8EGjP6APiYE1OONvdHo2ACGeT/ZvQW\r\nyvukXVNF9bKbm6FLOlzDPV4NQWKGHSaNGsfOwwHryTJMn2yZdQvTsykmLAM9\r\n5giNvLkk2lx9/Im3zYmIonF1xpTBTLuXAclBliwTVNkc//XlnMdItsf/veRc\r\nTUnMhbBX5xhAFRz+O449Qh0uAfAKXodYD27lRHtSs1OC5hmZJ29TywzgXMa9\r\ndeCWPNtx8w2fSfR5lLnNsPTRrsorF2OwGHA=\r\n=XP1v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "3fa43434881526d084bf35807d172d9803de76e8", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "typings": "index.d.ts", "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "dist/magic-string.es.mjs", "_nodeVersion": "16.13.0", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.1_1646299426372_0.26556550705724247", "host": "s3://npm-registry-packages"}}, "0.26.2": {"name": "magic-string", "version": "0.26.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.2", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "5331700e4158cd6befda738bb6b0c7b93c0d4432", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.2.tgz", "fileCount": 10, "integrity": "sha512-NzzlXpclt5zAbmo6h6jNc8zl2gNRGHvmsZW4IvZhTC4W7k4OlLP+S5YLussa/r3ixNT66KOQfNORlXHSOy/X4A==", "signatures": [{"sig": "MEYCIQCPNCVhf8752ocVAgRNkTtHp9jDWOD3lQzpU6V/DAt92AIhANGlXzk1NVNel7l4vaPlncM+lCqzeEw9FkDWJ0w2SUov", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie3n6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouYg/9GLTEyQmOZEZT1VNpONGIdy32iMtD04gNqzxyoh7q71dATgmT\r\ni2knvfXg5M/r9i9kcVI93V4/c/ATSAUMcUe6SfQgCX4VwJ+3lWPIY07F18fK\r\nj2Ox9vds1xIusT6OSvurI/N7qHLBZnG0GAiZfDq9UflKCr22XBqRCd5TPkYq\r\nE5as/TH+nBkspH9misUnzgKSpCrtzT8l1T+6ZL9uUDzbhiCDCU+YxDH9y2gH\r\nYXIfb75rHBgcDntJuQNmkEqvDijz1EpcUJ84TgRUEHL6QS//SNituBbSeqNV\r\nTs3B27glSFWnKOY3yatr2kQK+9eokr8Svptp6+Rpq3B8tkZEmkqN3hh7M9Ee\r\nEBQiQ10NQ1I3QS1/G+s65z+qxBbkvMjWRDuRQroNWpyYKqf2sSQA6lRxvwNP\r\n6eoBEabn5BlgkUU20Kx5D2P3PXjE58LN1bd7tY/7OXaVgqekI40ZQ3I66QiY\r\nzcvmtatKy6GYvuq2csy4u9rhh5yXNVJWDoHhDVSeQuoUfVqzJ/xcfxNjOWtw\r\nckU4Z3vbHOIk/JEhnupZDhZWheO1+D8rtn/7S3l8oRTKZAW7gidKNwF7gfHw\r\nt0qYCWr8tyXNtvVVpFAXVc4oaPlemetJE+nOLFQ5qAlQb+p1B9VIKtmDhOrV\r\n7Dol2Z9H8R8/v8M8vkwx89xsIeQsremT3jg=\r\n=gMoz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "b4603a60a8bc8c1a17c1d789483fdb9f63f152fd", "scripts": {"lint": "eslint src test", "test": "mocha", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^7.1.1", "mocha": "^9.2.1", "eslint": "^8.10.0", "rollup": "^2.69.0", "prettier": "^2.5.1", "source-map": "^0.6.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.2_1652259321937_0.8852290360575652", "host": "s3://npm-registry-packages"}}, "0.26.3": {"name": "magic-string", "version": "0.26.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.3", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "25840b875140f7b4785ab06bddc384270b7dd452", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.3.tgz", "fileCount": 10, "integrity": "sha512-u1Po0NDyFcwdg2nzHT88wSK0+Rih0N1M+Ph1Sp08k8yvFFU3KR72wryS7e1qMPJypt99WB7fIFVCA92mQrMjrg==", "signatures": [{"sig": "MEQCIHTB8is4hnjQhynXeMUI+8d46VkXqlA6RMC60BNmfEB5AiAYAh0PsODlCX8JwYWiFXybLBUhIz3Wvu0+xlnHJbnHlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDcE7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpymw/8CWBfEMz/Hi5N8XY7wyiIvzBibQmcJ9WsL4h2f+uZBOIoPrbr\r\nMpPvMJMPiqqvPW64ZV5bwi8oL6BmVDOId6z2534o1TMg1G5ejRkkRSLqG+eb\r\n5rK+ETrkpBNm5n5IOHrW+3pH4QxGgaOki9G3tJ9NweTbycXfXSz1INSYo0gD\r\nxzp/oC1wK3NL0N2kFO0RDy4zA1l/2dz1beU/7bq/tIROB/2OEOWboznuJIvU\r\n+a67QN8VkU7cGbq+3Tr3wUTAxSWhPfdY13j1ABVPlbfiJ/FuBvetXpv71JfG\r\ndL56kdH/fZRjWEZ1n8cHiHpaiU1s2AHYVSgIGMwA+KsHTlkRAVuL3H9cp3Ye\r\nBtRfow31NRAzyxN6qOcYovZtocJopbG/uMntG7WNu4XuNEWR3XJFxfofMN8h\r\nemgaC05fhQhgSAGJFPJZAqU0pmLocom1Tod5u0a4vht7KvKzWu4l8a3FGGek\r\nEufi4Ndf6s7MQu27b9kudFLOb/1QKhfaO3AL34//F0eTCqAxkK1REFlnJGca\r\nQk+AL5qoF59b5AJx3xv50Xhw07+7cbpmgMQx1VCksT94kgOywVqlgL2trQw+\r\n9ptYB8YlhsDmXwftZnslNFB7dEbiyKuSVOjGaxnL1mVv2BHHJMqv/q+UEBxu\r\nO8570A/iNFaJZmfxz6YWSMA1SRMDG/6BJg4=\r\n=G/1m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "abf373f2ed53d00e184ab236828853dd35a62763", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.0", "rollup": "^2.78.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^13.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.3_1661845819321_0.5268383161172072", "host": "s3://npm-registry-packages"}}, "0.26.4": {"name": "magic-string", "version": "0.26.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.4", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "3d057d3d0234c3b179aa3f421b33fe5d8a4044a8", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.4.tgz", "fileCount": 10, "integrity": "sha512-e5uXtVJ22aEpK9u1+eQf0fSxHeqwyV19K+uGnlROCxUhzwRip9tBsaMViK/0vC3viyPd5Gtucp3UmEp/Q2cPTQ==", "signatures": [{"sig": "MEYCIQCGARXdsodhGT0sSAYO/ZqFt6StA7hbdcmEhnRHsyXGBwIhAOmyswJ0/5ZPZhh6wmeNEZ7wF7oRiLwZKnHBnTvJS2ag", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLBtYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx1Q/+KGhCE7Ha2OvprfxfHxJZ1gjntCSb5aUZRWjLk390AYd9wZlC\r\nKtNnvEVc0PJHSzVsWtmxakdqo5wU2aljH437jqsrLtBtHYf8gMbA/kibejJr\r\nyYzmPLqwKiP5z6s45Qsg7wYKjoYw2Eh9satMt0GJWUy01s0uQjtJ9bJhWlnn\r\ntDJE707u9rx5dIlOxrVXEKr/H8tfsEq6abtU7PN4LoiUwSqrKiw6A/e0yBw7\r\n7mH4gTfN/qZLdyM3rOLxdWkNJSrPsEdCsxppi3d8vjpZ1RfGCZ4rblLm2iti\r\n7H1idgwnjSRbln1awbSCU6BLFfvBCJeF+FC35fnfC3etbdHrO+QGKfxmxIod\r\nd7vpvTg+Gsb1x6a27GEM4WTjz4PbKj8mvk8dCHegHEdNN8ah1Oo8SeQCAJwc\r\nTzq1/cAw72GIDZwIUWVxFtuv7UNHbdnzuCznNqreUO1xQG7S3IgDPFj+/EMb\r\nlA8TH4T8xc96TWCkfL/MHN9/IzcxNYsh0k2f9yJc7yIOkOW0aSCRgnWmw/z1\r\nq7Hy4R2XTE2Kqmymo+yAL8dWDy+svBla1jNPtl+mZkORx4Jyg73gyptd8MhB\r\nEnDc6bJQuEnP7V395NXwQdNIpFVWTyYZCbg8WhypXr4iUCm2nnrS1mI5PXVR\r\nMtDuWLkKLWM7sPZUw/w2l0t0MG2L5fywtjc=\r\n=wogr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "226d3810ab48a50302138f57eec59537594571af", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.4_1663834968219_0.24947838454835125", "host": "s3://npm-registry-packages"}}, "0.26.5": {"name": "magic-string", "version": "0.26.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.5", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "3ceb5c60f546ba4e21e3865ab8de4d32bd8ed07f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.5.tgz", "fileCount": 10, "integrity": "sha512-yXUIYOOQnEHKHOftp5shMWpB9ImfgfDJpapa38j/qMtTj5QHWucvxP4lUtuRmHT9vAzvtpHkWKXW9xBwimXeNg==", "signatures": [{"sig": "MEUCIBXtquBihnqTrcW4huNY2eus+4VmyLCp3/393/pXP2qNAiEAoChKSUQNczpuiKQdouDvQeBUVvQ83sEtRnMV9NaX3Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGDRAAlOLL+wYxLOH49WyXnjzVjjlSlejMCbUb/baMHvcdpPDoyqQH\r\nMZISvZzD8aKJXsN+04XHIYTXEgTAmmMrjIA3eEDPMVS2U4uNVB9mi8iB3D58\r\nAtgnr7WsuxWAfO49I7XbQFivkrPcwE53tEmSSlQ5KJ3JBCNK+YFWBXlnnmE/\r\nRr9npjUV5JN5429W4BuNEx1qU1UA0ezvcpzFmXMmHJjyTBkiO64V/95NQ5rY\r\n+yE6eFC92cZUmmsphCgog8ifvzbli/CitCVdVO+MCq71l6fDZdLju26PSO1d\r\nNTOMMYVTPF+KPXSeGPD4isw/S4q7x0IITc4DLOAMJEAz8qRmLErx1D73Vo+F\r\nJe9gssiBwXUmimRc/xuaIuHUOcLKTFO06IrI6DQ6trfmJ2rESyK0h4DilB55\r\ncGcY6c3SAvPtCmbzOM2ePSVdPP1v6rtss3kp1eNZIs/HR8E3sMuisop8h6NC\r\n5QjiAEBJYw/7JKYp22VzeZJZynzYLoaq/IF+A1spT8kMm9IKU+QiR5Z/TNBI\r\n3Wlf0twAl8CNUw3yo3rX3P1sLIfZFhR57b8e0SykDj8sqdZcQMZusCsea7JO\r\n9+r+lK1pLfrkvZcHEEnV+Q4KtQ0dlV3YGD2OqLt2qAHN9TrnNhamDaYCfjWk\r\n2gwRYF7M1k8jYUYUKxjdQ0XwLWYOhkvzC1A=\r\n=RecY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "ded833ec547c27d135bf4dd8e558bf5be0e17750", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.5_1664531776746_0.8417537118280556", "host": "s3://npm-registry-packages"}}, "0.26.6": {"name": "magic-string", "version": "0.26.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.6", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b61e417c9f40b7b53bf7e73c0a803258e20d25ee", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.6.tgz", "fileCount": 10, "integrity": "sha512-6d+3bFybzyQFJYSoRsl9ZC0wheze8M1LrQC7tNMRqXR4izUTDOLMd9BtSuExK9iAukFh+s5K0WAhc/dlQ+HKYA==", "signatures": [{"sig": "MEYCIQCBqC4zx/T9Byeeledo4zjfag7lBxzKQsnZMc46KuA3bgIhAISSFLzypc9WUhDqpudk2MF5aZrFI98yg6W5aeEGn2Lz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPf/tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr//RAAj2Zcq//mwCiaNU/6EXwbt4BWcMJsdt9AIAXrDr3UO925o1CV\r\neEYKzzmJUBrChVEPCuiF5TZTudUxIQDqTRuc+TuK3NQvfBO7DTc2FQ5OKnyr\r\nyXiBy3ugtYo21v+kwd2JExoYWKuxHhS20LSiRLt1r9uyybvUWUA69bZTvprL\r\nBQwmcqQ6yTNIsn/lr0zKnViyAS5knkCj+ob1N5dZYcdItbHykPPpad7i3Yh1\r\nWRKmRrXONqN4TDEcD5sc8XzqIh8vQcAr14O2dJbNGy88jy/RW3Il7q3MMQup\r\njeWaH4hc4v514pWE3N58OyutL/QWh4405Pl3WfyyloUn+rvpKKvg8WZljtEF\r\n7hYgZByRY0BMOwTjrFLwltcUTQ0J9LDmV600eActv2rctfRsBKWSopb+bZp4\r\n6b+/sFy9nu0W/tjvbHGIOcn/apMydreJtT3R7JWCGf0/int4BjHr27QNzf50\r\nMntMXmfhaq3VA9AK2ztGQ3SZ2ZG8+D1vSHyvnCZaAvqG/q9LjOrCyIW062wm\r\nnnOBD7+h9KT3h3UhttMWp6e5GcpjNHp3Jk3IddNY/0r2aRez4/5HuXl7GBkh\r\nJYJee1BicZ/bOnoWGdvQ0AfqSoeL57vWaSg0NTBxVtLm7NtYaZQBUgFGcoyI\r\nOtPsLC1MNFpkJg2B2Wsu4KGthY+oPN/7ox0=\r\n=AKK0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "7b45b9bbb9890e78bb683edccc9a667ef159ddc0", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.6_1665007597656_0.31621281987410743", "host": "s3://npm-registry-packages"}}, "0.26.7": {"name": "magic-string", "version": "0.26.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.26.7", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "caf7daf61b34e9982f8228c4527474dac8981d6f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.7.tgz", "fileCount": 10, "integrity": "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==", "signatures": [{"sig": "MEUCIALott20PudieqzQBa+AqYRUn2XCyZ+rTCfTiyEVApK3AiEAm5EBo9gLEhU5zF3AE/+/YFQ75Zx8bbFMCdNMXOuSmQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ0SCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmEg//cqsN4bfXovGe+APFPmiIQeCCOhKAJULjqqzr97CoH5QNPgnb\r\nmg6hYxV5c/vTQaFZKoUOt4vMPNsfJVeJgAf7plOOkoaICGJc8OWsHvy82Yux\r\ngngDvoe4Fa3Tfi/8aqmY02Fz0KbOSGO5+4jiDSeOJg6k92TOR4m8N5587KCt\r\nJA2WyS4/BSz1FJvh7g/O6PtHGliVUOUqjMGI8xXE8/12g38AXDSy3c5iUIvH\r\n9Dm2rPFetvJTghK6X8Q0+FFqIpOk4TyoHRMogRjWIWbidQbMc4WnMroMKY7D\r\na9vTJtmgQJ6Ql4YdSxzWSJ8R5NQpN1hqmBarKziP6oS45RYsrErAaMXLvoJU\r\nVI5QifHNeRWXemThEQ+og5s+x71nBjrVYRmGGJ5syf3yxazEgrFJib5d16Vs\r\n+NP3bXIYg5wihhGwny2ApaU3dT8ylaassQ7MtJjoAhiHFcFd9qORiNEhp3dt\r\nNE2aPd2h5mCGSWmfPcEHGk+3sah//45oKwNViUUWPtCHiv+RHnJ+wcYnSsHN\r\n0L9DoD3r56hOG+DpzLrdvuYcOtQIM2/fiwEJGW5RQO5N2Qbcw3sas2r0lyeT\r\nSk7Uaep4tmO+1KawalMGS3p+C7T/IrnA4otCNbP6UCGrMEJCNB3sr7NwDCpQ\r\nAfoAWC02hdWSimky+AyMPwf2tV7bCCvQmpo=\r\n=IzHX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "e9b2ea232b816a7cc509cb3ec6b46546f8d370ea", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.14.2", "dependencies": {"sourcemap-codec": "^1.4.8"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.26.7_1665352834772_0.502152249132021", "host": "s3://npm-registry-packages"}}, "0.27.0": {"name": "magic-string", "version": "0.27.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.27.0", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "e4a3413b4bab6d98d2becffd48b4a257effdbbf3", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.27.0.tgz", "fileCount": 10, "integrity": "sha512-8UnnX2PeRAPZuN12svgR9j7M1uWMovg/CEnIwIG0LFkXSJJe4PdfUGiTGl8V9bsBHFUtfVINcSyYxd7q+kx9fA==", "signatures": [{"sig": "MEYCIQCjcPD+gH8KxSe8LEZ7pE7PsRYl5DRzrjPvqYNt2OjCLAIhAMCl//xjiz0YsV9MKPXvo1Gh2KXn5fOOjFkuy0LgZy85", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJji8dTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzhxAAooS7L+BRP9mpM6fdORX+H/eQ+JBs3cc/GB8RhnoRAU4jepOw\r\n/FKA7gATmzmZ/5CE5ei3CGJrIMVFr0J41qrLDXvlx11HpuUIi9KYia9B6tZ/\r\nrMUrBp3cHVT2WTkerdWizyjr5De/JXNtWRZeH2f9MHBumCT5swmZeLyyNTcQ\r\nN7kyrKqHnpwF00zBhN9nolw3Mt/Sqc5wWPJO6rzR64fYdwkjBh4Yxgx503YZ\r\n+RaRRu8kINIgNd9Vi7hgbRlPlqpVLyjfBF+EoLFv+FKIArg0pGqScMgWRxJ1\r\n1rZQyQm75tQSQ2pmjywmH4sQHvxtXjHi7aee7dUZIvd8cadf8sFnJK8hqHEg\r\n+yXpkjxxRqt7d5CQxs858aLtDWDiWh73GunvQuVLsC86yLjdEDA/BHfLDt/f\r\n6enCmY5WuoZx/65rgLvxCkmo673hb79RWFI6RIV/P6js3+qLRnp6PUcDAptt\r\njbz5tgAq5OuuSkyen/Meyd54MJl8ykmulJUjWjhP1UVKJ3X9u0MYHUW2i7NO\r\n0XDfRogyQwMo+yGfNU4Evpp6cz6WPP67o8uU4pIB0+z/alKlh9kC6dnBhon5\r\niK+a7YgLixyPSVZXuca9QwLYn2NsBcwKmLL9o8TXe4QPffAX+90VQmX0Jln6\r\n7TavgXPRHsXfvLi57SdoQX0E2LejqxC4G3Q=\r\n=6w74\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "c87d5eb7f5bece32c2b34480d576cbb190c31971", "scripts": {"lint": "eslint src test", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "rm -rf dist && npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.17.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.27.0_1670104915349_0.13682452367671383", "host": "s3://npm-registry-packages"}}, "0.28.0": {"name": "magic-string", "version": "0.28.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.28.0", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "b29c6129b82b9f2c077fffaa3f74cd563a938dcb", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.28.0.tgz", "fileCount": 10, "integrity": "sha512-B4A<PERSON>4hzryNcHPeIGfp0Xt+Ym5Tt/XdnSTIE6U5kyN7OGot6ZJs0xnlfhxWdjv9/61Cce2b4FRIvEOyGNX93xsQ==", "signatures": [{"sig": "MEYCIQDAaJ97U52ErFDJlKgaKkRY/5BYxUILkZfNhGgYB+DjbQIhAIeAlpbLqEQRkT5Dhntl9w1+biy7pJdFpAVTvkbzIpof", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj57xSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2hRAAilqw9u/mP0lzIoX0fqfpkeYrT9yX29slMrpGLZS7VkNM/KL7\r\nsXX8RfeaRKirlntsuMtJQ+S53qPBSOC3V+Mx19txmyY7o3EYkOonp2sgxhui\r\nWxr+gMWsRIK7gMWncZY+Vppjlk+N35/NzyvvVjxgF9GJcsdVp5QzJbXffRKk\r\nomugD1CJSO1deKKebRqrkxU+qCP2p+NrMkqPN1x8zeXxoIso4apkpw3GXSbK\r\nQVI1dfkuUbdpFdiqlqv2+zSkSWUPIDLdUIwYYKSiwmoXiuBTw9/EHFmV0gkt\r\nnQSoWoLu+5U0lis5NgqJDHq7oB2Snqf9vdf5hWgkL7jaf0ZKiRmCVBwKBpQf\r\nJwzuafYneFizFHvK2wohdxuhwXH+KVcznvJT37BgYnHIK8FVc75IGZERORtp\r\nNwLeraIQB50zWhr9eAAztiGsb2tIKPL8TIc4fZA6VrY2DyuHo89bqhEnWXnv\r\nf467sHvsCLsS3JIPlThNDEEzs6ETzqeuqwuiBdANrsFnslZCsypELnJGyP+1\r\nEqQlAxNF2LIY8dYLzjcz7dMPTEl/2a4nD6rnGbMWWMjUsouI2fEz9sTEN1iR\r\nFTL2r4+4g4DVAjhTrKUODcslB61nRwBRVrjlT34c54teJfg7PBIvbKZSKHDe\r\nJvlmod1z8f4/gjt2MYCnhjToD9mEnfPEKSs=\r\n=SCKL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "74cba9f5d5d1176cfa23e6eac5b6364abbb1564a", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.17.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.28.0_1676131410616_0.6127023144346688", "host": "s3://npm-registry-packages"}}, "0.29.0": {"name": "magic-string", "version": "0.29.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.29.0", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "f034f79f8c43dba4ae1730ffb5e8c4e084b16cf3", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.29.0.tgz", "fileCount": 10, "integrity": "sha512-WcfidHrDjMY+eLjlU+8OvwREqHwpgCeKVBUpQ3OhYYuvfaYCUgcbuBzappNzZvg/v8onU3oQj+BYpkOJe9Iw4Q==", "signatures": [{"sig": "MEQCIG4An8grRLG6JlgtE10x3B33sD7IEV3LgZ/Epk9Pd+VGAiBvWUKYgtLpdm4WDYFtVmPOE/D1jAShhERexFBc6g90lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj57yOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0dA//TI3DuxHioYTavCv5KDtqpiftwCWVTGcpBSifacE7XkcRTRqD\r\ngaJTGghwt/1/aqRyuTrZbrWRSCrgs5c+GLaywfuWOKesSyfBtibrvaR4m6x3\r\n4NYX3oM1ipUjC56iU9Et1Yp+C+Xu/U9RWfmghs8SiEi8TzsLHbW05bWp2dWT\r\nMv4iNRcm4wogRQfBxe4uLoc8VaBmhGX7ONnH2Xre+54Af8TZ1l4cVg62NE+k\r\nVBuN7CNqybKVYlxzZuVG7vLJUsK0WwX7liSPdo8ZswiXUVHv/GWldKbhh2F1\r\ncd6CZ9ckUMhFBJuHxreDsbrpQi8Q+3ou4dcnCzE7MtbSMoy8j2Pgaa4vmAf0\r\ngVHwswGpNPpEbebcEHvZUkmVi7F7ITR7Gq/4Yh9k2nPu+BEJskgnYfsUqOcg\r\naT7YD2jbDdn8YiQJAx4OonGCtCfCZAJ0rEAtjz+l25sVssSpKp+Gevcgo0S3\r\nZRcBQ6gMKKEVy5KTx9756VbXi2IQab/1zVAjX1lLkLi4liGP5lVOz4Y/t5/o\r\nY1yJicehbPsZVuHzKKshZZpPscV5wtMwY4R5Gtggs5cRlfpSga3RYIzNrPn1\r\nQvDIii1ADXPcRwK4tXProuaojv2QwulcjbkoIWjCkztLLD6MRXy8Yg2rDcia\r\nJdEQCEa1CqwXp3t9SMVdYzwuARVYiGHQs50=\r\n=kHOt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "60320034ab47ac830b3c57830603697526086444", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "rich_harris", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "16.17.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.29.0_1676131470726_0.33241285581159263", "host": "s3://npm-registry-packages"}}, "0.30.0": {"name": "magic-string", "version": "0.30.0", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.0", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "fd58a4748c5c4547338a424e90fa5dd17f4de529", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.0.tgz", "fileCount": 10, "integrity": "sha512-LA+31JYDJLs82r2ScLrlz1GjSgu66ZV518eyWT+S8VhyQn/JL0u9MeBOvQMGYiPk1DBiSN9DDMOcXvigJZaViQ==", "signatures": [{"sig": "MEYCIQCoU8qJttPyOYandk8iofJcQph/2gY1/TDTEVp7SdLLAQIhAO0Ue7zM+eeu13MDE1UbFB0otO0Mi6M4uxqDgA69z3xW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9fCUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFGw//SO8COJDAx+E538M6PfdY3Da+fcD2pZDXUuYxRLOVIKpXLanZ\r\n4C5LPJNiFi/1cAVIJByxHG1muFektIz4ypsLRcQFa6nYAYjACP2/Yu1e7buL\r\nTLVkCSiGeEDl4QoB8ju+HxBJrhXKhAnDfXaQUK88CJiYGHCVQcp24Sv7epet\r\n+1k69TY0eFWICK+vHrEKYxOgBnHHiwGG6etAZEe21Sce+nDN7S/5lUnQ4OnZ\r\nhMqvc8xXQk4BcUa/64yLMMiU5RK1yubLxXjPLseDCkXnRdldAwN+FRp8us/O\r\n+AtiKlBX/YjN77mtosJeS0cXGDzZp98iOHsQgNwreOhJqCO7taOBN9PAnv3G\r\nzkkceZGyyi497RLt9bbWqg0PNDJu9qUBH1+6GRwgZBrDxfiBB0sqXLZo4kf5\r\n6/N+pEOUKXs/pvAvxhrdcifU2amrN+MwO+5DToyOTVMFmc2fxkdRIn9Skhe6\r\nX7ZSVL1XjUE0Lb2WOIjpixX+pdMt6eiGdnNnmD087YwGT5SBBt9B42tI0yUQ\r\n6n2bu/vqFyIBSHmxINnVH+QNVLKtcWwPncUke6XX3vbFeORWtGfOzb5j0CLm\r\nO5Qb0O5remO8ImAGPd08hxOy1a8ZUUzydrCq6fF36eKBQgndW0dRcBgmwpRO\r\npoiXlxvbOFkXV6+NB72tQkAKy7leE+YTTZk=\r\n=guN7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/magic-string.cjs.js", "types": "./index.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "34935df2d22cee89f6bdadf8503d9a31602393bd", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "18.13.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^8.2.1", "mocha": "^10.0.0", "eslint": "^8.23.1", "rollup": "^2.79.1", "publint": "^0.1.7", "prettier": "^2.7.1", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^2.2.2", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.0_1677062292820_0.6977391269798447", "host": "s3://npm-registry-packages"}}, "0.30.1": {"name": "magic-string", "version": "0.30.1", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.1", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "ce5cd4b0a81a5d032bd69aab4522299b2166284d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.1.tgz", "fileCount": 11, "integrity": "sha512-mbVKXPmS0z0G4XqFDCTllmDQ6coZzn94aMlb0o/A4HEHJCKcanlDZwYJgwnkmgD3jyWhUgj9VsPrfd972yPffA==", "signatures": [{"sig": "MEUCIHFVolLHCVGMUXJrhCu++YanDouzIHiUXKCIrnwRjbQfAiEAosnFq5BNMKOehk/wEJQshLhmuF3bWwRjOUlfJ/qxsy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421073}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "5a00188874bc1c335e8e1dbb4053ea0bd1e0927f", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "18.13.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.1.1", "mocha": "^10.2.0", "eslint": "^8.44.0", "rollup": "^2.79.1", "publint": "^0.1.15", "prettier": "^2.8.8", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^4.0.0", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.1_1688448694736_0.5682053850669331", "host": "s3://npm-registry-packages"}}, "0.30.2": {"name": "magic-string", "version": "0.30.2", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.2", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "dcf04aad3d0d1314bc743d076c50feb29b3c7aca", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.2.tgz", "fileCount": 11, "integrity": "sha512-lNZdu7pewtq/ZvWUp9Wpf/x7WzMTsR26TWV03BRZrXFsv+BI6dy8RAiKgm1uM/kyR0rCfUcqvOlXKG66KhIGug==", "signatures": [{"sig": "MEYCIQC7MTefzmO8jornICNLFsuTd3hiapvMlJpME21T+drn4wIhAOBDyQHDMutaSXzZxBN3o+xNQZfaZSSQaClTE+iGxCaU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428014}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "15339e6ef36b45c9bc4c595757575aa33c5f7fd7", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.1.1", "mocha": "^10.2.0", "eslint": "^8.45.0", "rollup": "^3.26.3", "publint": "^0.2.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.2_1690509243433_0.9935206175456455", "host": "s3://npm-registry-packages"}}, "0.30.3": {"name": "magic-string", "version": "0.30.3", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.3", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "403755dfd9d6b398dfa40635d52e96c5ac095b85", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.3.tgz", "fileCount": 11, "integrity": "sha512-B7xGbll2fG/VjP+SWg4sX3JynwIU0mjoTc6MPpKNuIvftk6u6vqhDnk1R80b8C2GBR6ywqy+1DcKBrevBg+bmw==", "signatures": [{"sig": "MEUCIB3xJN5T2wZRztCqjUKwVmclBTOVJ2XryR2OEm1yE+yQAiEA7pcaP3qfk4aHtrDFYUFRcU0r9lBzbsaaZT1XONd6AF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432379}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "8e77538d1d03feefe9b114cdff31fba2ef243b4d", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.3_1692604437623_0.3959474768745732", "host": "s3://npm-registry-packages"}}, "0.30.4": {"name": "magic-string", "version": "0.30.4", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.4", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "c2c683265fc18dda49b56fc7318d33ca0332c98c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.4.tgz", "fileCount": 11, "integrity": "sha512-Q/TKtsC5BPm0kGqgBIF9oXAs/xEf2vRKiIB4wCRQTJOQIByZ1d+NnUOotvJOvNpi5RNIgVOMC3pOuaP1ZTDlVg==", "signatures": [{"sig": "MEUCIQDzEpbZiHtU7Fue4f80KPMHy8mVtlYlEz8fpus2yuAWigIgFhqZIUegsirNVNl1WFh3CQNUSIDWYuzp/jNO0isRKl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438682}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "cf554e7b9384fa09695d541cb84dc5118b8374e0", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.4_1695969123397_0.9914400158496466", "host": "s3://npm-registry-packages"}}, "0.30.5": {"name": "magic-string", "version": "0.30.5", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.5", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "1994d980bd1c8835dc6e78db7cbd4ae4f24746f9", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.5.tgz", "fileCount": 11, "integrity": "sha512-7xlpfBaQaP/T6Vh8MO/EqXSW5En6INHEvEXQiuff7Gku0PWjU3uf6w/j9o7O+SpB5fOAkrI5HeoNgwjEO0pFsA==", "signatures": [{"sig": "MEQCIBNeH2/GUfAifwiuHGnPxykbSDlQx1ejs6H2wMzbaUFQAiATPlsMVBsjjQ8yxAvgpBeIH85TgwxiFbbhCKv7kHVWeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438918}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "63c1388fc0ccf77408b405bcc133cbdb7ea6cb83", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.2.0", "mocha": "^10.2.0", "eslint": "^8.47.0", "rollup": "^3.28.0", "publint": "^0.2.1", "prettier": "^3.0.2", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.2", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.5_1697118338999_0.9039690225950743", "host": "s3://npm-registry-packages"}}, "0.30.6": {"name": "magic-string", "version": "0.30.6", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.6", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "996e21b42f944e45591a68f0905d6a740a12506c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.6.tgz", "fileCount": 11, "integrity": "sha512-n62qCLbPjNjyo+owKtveQxZFZTBm+Ms6YoGD23Wew6Vw337PElFNifQpknPruVRQV57kVShPnLGo9vWxVhpPvA==", "signatures": [{"sig": "MEUCIAWPd+2zDK4k6ZwOao7lXKCK/fzN84Xwva7jknIIR5/gAiEAte6P/30kAUCTumlWfmXTMHn4nddVD+1nwz1Bnii2/aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439043}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "bd3bf3106e9d486908ebf9e6b70006a3881fb71d", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^4.9.6", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.6_1706721669147_0.524655947169647", "host": "s3://npm-registry-packages"}}, "0.30.7": {"name": "magic-string", "version": "0.30.7", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.7", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "0cecd0527d473298679da95a2d7aeb8c64048505", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.7.tgz", "fileCount": 11, "integrity": "sha512-8vBuFF/I/+OSLRmdf2wwFCJCz+nSn0m6DPvGH1fS/KiQoSaR+sETbov0eIk9KhEKy8CYqIkIAnbohxT/4H0kuA==", "signatures": [{"sig": "MEYCIQDhL3HtpSJfoArfuvhylDNyMNcyJQNrxCrLKKdQ6y8kAAIhAOokSekEY5vrJB6AwUsCMhF+fx7z75U/r3NwwsUxPzd5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 447407}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "44b0407a6a0812f50bcaf2e0ab86081cd1015775", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.7_1707152908734_0.23882352980049815", "host": "s3://npm-registry-packages"}}, "0.30.8": {"name": "magic-string", "version": "0.30.8", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.8", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "14e8624246d2bedba70d5462aa99ac9681844613", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.8.tgz", "fileCount": 11, "integrity": "sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==", "signatures": [{"sig": "MEQCIEk0t9UUASK20ulRRLvhCXCHkKEs8GUgI4B4uUWP7mHcAiA/fXIaNKC2xmPecdiQyBQ3kw4oZOlATixXcQKK2yemxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 449430}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "6f6cd52270fdc8b62b1b94c73a5d19ba37b3d4dd", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c && cp ./src/index.d.ts ./dist/magic-string.es.d.mts && cp ./src/index.d.ts ./dist/magic-string.cjs.d.ts", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.4.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.8_1709503141079_0.7718581965637754", "host": "s3://npm-registry-packages"}}, "0.30.9": {"name": "magic-string", "version": "0.30.9", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.9", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "8927ae21bfdd856310e07a1bc8dd5e73cb6c251d", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.9.tgz", "fileCount": 11, "integrity": "sha512-S1+hd+dIrC8EZqKyT9DstTH/0Z+f76kmmvZnkfQVmOpDEF9iVgdYif3Q/pIWHmCoo59bQVGW0kVL3e2nl+9+Sw==", "signatures": [{"sig": "MEUCIHXHkxNSwSUDnOoV4GxPECk0tZVlakXze/6kmAZykaieAiEAoIQ7XinkN4NsbK1eqIjOK67hT5TKYwh0LYQcKlvgKD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452267}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "engines": {"node": ">=12"}, "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "4f385be91b95d52320583eebcbcd3647f3276fce", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.11.1", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.3.0", "mocha": "^10.2.0", "eslint": "^8.56.0", "rollup": "^3.28.0", "publint": "^0.2.7", "prettier": "^3.2.4", "benchmark": "^2.1.4", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.9_1712219275510_0.9651150340936834", "host": "s3://npm-registry-packages"}}, "0.30.10": {"name": "magic-string", "version": "0.30.10", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.10", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "123d9c41a0cb5640c892b041d4cfb3bd0aa4b39e", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.10.tgz", "fileCount": 11, "integrity": "sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==", "signatures": [{"sig": "MEYCIQDd4PQKR/hOBMHNbDcxlOJnIq66VytIofcnGxrktSXRhwIhAMQB65vKuoEv+s0yyKqnLn03CWDrNjj2YMQtbC5DlBEs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451985}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "d6b2171d5bb10c6400da85bf34594987f63c44b5", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.11.1", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.0.1", "devDependencies": {"bumpp": "^9.4.0", "mocha": "^10.4.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.7", "prettier": "^3.2.5", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.5", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.10_1713358498619_0.4855022533841551", "host": "s3://npm-registry-packages"}}, "0.30.11": {"name": "magic-string", "version": "0.30.11", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.11", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "301a6f93b3e8c2cb13ac1a7a673492c0dfd12954", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.11.tgz", "fileCount": 11, "integrity": "sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==", "signatures": [{"sig": "MEUCIQCIebgd0f6kDRQnNdchqWa45B3wH7To6Ox85TaSBhhGewIgVlgdxSxpAAa06+I+dVHsBXhAX1wI7RB3xKJGEADem3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463574}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "bde9d7e35da4663ead63a9c81130e7a0752c7a9f", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.12.2", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.6.0", "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.11_1722256436073_0.6214885253826006", "host": "s3://npm-registry-packages"}}, "0.30.12": {"name": "magic-string", "version": "0.30.12", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.12", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "9eb11c9d072b9bcb4940a5b2c2e1a217e4ee1a60", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.12.tgz", "fileCount": 11, "integrity": "sha512-Ea8I3sQMVXr8JhN4z+H/d8zwo+tYDgHE9+5G4Wnrwhs0gaK9fXTKx0Tw5Xwsd/bCPTTZNRAdpyzvoeORe9LYpw==", "signatures": [{"sig": "MEQCIBmz+cgC3wlLPfBwqVhTW6kh8jAESo/zz8qfEwJ3lCa+AiAd4dsbftDSKe2gYAMmHDkhizGBzgkbX9E0cUWcvGiYHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463745}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "4d037723254121a9685ff08fba3b8e9ad68b5a2e", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.6.0", "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.12_1728632040689_0.9391188034854534", "host": "s3://npm-registry-packages"}}, "0.30.13": {"name": "magic-string", "version": "0.30.13", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.13", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "92438e3ff4946cf54f18247c981e5c161c46683c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.13.tgz", "fileCount": 11, "integrity": "sha512-8rYBO+MsWkgjDSOvLomYnzhdwEG51olQ4zL5KXnNJWV5MNmrb4rTZdrtkhxjnD/QyZUqR/Z/XDsUs/4ej2nx0g==", "signatures": [{"sig": "MEYCIQD2G1egymw7T3FlUNtXOGZ+Rg1pjiGUxNYyUiEV3xaNHAIhAIfpG6dllFOxWCO92mmDUe8qEIkPOphk73VikziHcnQa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464692}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "f9da43ac28933e7b5fcfa3ad68504481d702804e", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.6.0", "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.13_1731955224911_0.24604126470174625", "host": "s3://npm-registry-packages"}}, "0.30.14": {"name": "magic-string", "version": "0.30.14", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.14", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "e9bb29870b81cfc1ec3cc656552f5a7fcbf19077", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.14.tgz", "fileCount": 11, "integrity": "sha512-5c99P1WKTed11ZC0HMJOj6CDIue6F8ySu+bJL+85q1zBEIY8IklrJ1eiKC2NDRh3Ct3FcvmJPyQHb9erXMTJNw==", "signatures": [{"sig": "MEQCIHyX1smUmhBVl/YZmtjfI4cwPM9rRb3Es90O8lAYtlukAiBw1XW7dZa4HslLesQ1WOFZa0NbkXlScdHTXnbmbNjpIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464732}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "5b36911ee76dfee960a024bc4d8a8635641b3d9d", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.6.0", "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.14_1732601939690_0.9641400664557982", "host": "s3://npm-registry-packages"}}, "0.30.15": {"name": "magic-string", "version": "0.30.15", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.15", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "d5474a2c4c5f35f041349edaba8a5cb02733ed3c", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.15.tgz", "fileCount": 11, "integrity": "sha512-zXeaYRgZ6ldS1RJJUrMrYgNJ4fdwnyI6tVqoiIhyCyv5IVTK9BU8Ic2l253GGETQHxI4HNUwhJ3fjDhKqEoaAw==", "signatures": [{"sig": "MEUCIErNx+lmxddK2ZSCpUzGag+v5Nc79Zi1yA8oCvtqN8knAiEAxtEMdm/idur5p3prxkvo0hz4sQJTioo2uOOhpxLR8HQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464756}, "main": "./dist/magic-string.cjs.js", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "1266ff665ce76c2962d343faca1d30fb573bd8c5", "scripts": {"lint": "eslint src test && publint", "test": "mocha", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.6.0", "devDependencies": {"bumpp": "^9.4.1", "mocha": "^10.7.0", "eslint": "^8.57.0", "rollup": "^3.29.4", "publint": "^0.2.9", "prettier": "^3.3.3", "benchmark": "^2.1.4", "source-map-js": "^1.2.0", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.15_1733808674287_0.6804355923425272", "host": "s3://npm-registry-packages-npm-production"}}, "0.30.16": {"name": "magic-string", "version": "0.30.16", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.16", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "225ab531bf824856ca4589ed3864249e51baf30f", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.16.tgz", "fileCount": 11, "integrity": "sha512-GOWufviMYkLKe5PYGHutkqYlntF0qDMlrZLObkQGbdmkcQAxwe7KDLlX8MKlLXQc8GwFqINYp29HpKw2t3iRoQ==", "signatures": [{"sig": "MEUCIQC5+yzbuyBuREMJ8LRuvEaG0+kLrJ1GWIArWctoVSF2TQIgehhieUioTwIQBuWdrGKrNt4QyejR/S2C/yHXJ5jSdpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467510}, "main": "./dist/magic-string.cjs.js", "type": "module", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "gitHead": "3ddb93dc0c13cd60839bad9c86c9765b3a25cdba", "scripts": {"lint": "eslint src test && publint", "test": "vitest run", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write .", "prepare": "npm run build", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "test:dev": "vitest", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepublishOnly": "npm run lint && rm -rf dist && npm test"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.0", "devDependencies": {"bumpp": "^9.9.1", "eslint": "^9.16.0", "rollup": "^3.29.5", "vitest": "^2.1.8", "publint": "^0.2.12", "prettier": "^3.4.2", "benchmark": "^2.1.4", "@eslint/js": "^9.16.0", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.16_1734328695167_0.8650057062747454", "host": "s3://npm-registry-packages-npm-production"}}, "0.30.17": {"name": "magic-string", "version": "0.30.17", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-string@0.30.17", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rich-harris/magic-string#readme", "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "dist": {"shasum": "450a449673d2460e5bbcfba9a61916a1714c7453", "tarball": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "fileCount": 11, "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "signatures": [{"sig": "MEUCIA+WyMv4NUbNgZ55x5RZG05+IxR7YIcarznfqFcrFnLvAiEAyIPD3EhAcNC7kTB+TlguH03go3PCcEX92RpcxhbGKBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467464}, "main": "./dist/magic-string.cjs.js", "_from": "file:magic-string-0.30.17.tgz", "types": "./dist/magic-string.cjs.d.ts", "module": "./dist/magic-string.es.mjs", "exports": {".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}, "./package.json": "./package.json"}, "scripts": {"lint": "eslint src test && publint", "test": "vitest run", "bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "watch": "rollup -cw", "format": "prettier --single-quote --print-width 100 --use-tabs --write .", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "lint:fix": "eslint src test --fix", "test:dev": "vitest", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "_npmUser": {"name": "ant<PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/30/nymxcyb909ggq2j5lwn7b_600000gn/T/59050b2a450dd4faf26dd0e68ae63fb7/magic-string-0.30.17.tgz", "_integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Modify strings, generate sourcemaps", "directories": {}, "jsnext:main": "./dist/magic-string.es.mjs", "sideEffects": false, "_nodeVersion": "20.18.0", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"bumpp": "^9.9.1", "eslint": "^9.16.0", "rollup": "^3.29.5", "vitest": "^2.1.8", "publint": "^0.2.12", "prettier": "^3.4.2", "benchmark": "^2.1.4", "@eslint/js": "^9.16.0", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "@rollup/plugin-replace": "^5.0.7", "conventional-changelog-cli": "^3.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-string_0.30.17_1734329693905_0.48249793288441256", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2014-11-04T16:24:39.084Z", "modified": "2025-06-09T04:08:22.729Z", "0.1.0": "2014-11-04T16:24:39.084Z", "0.1.1": "2014-11-04T17:02:43.815Z", "0.1.3": "2014-11-07T15:27:30.330Z", "0.1.4": "2014-11-10T17:46:06.913Z", "0.1.5": "2014-11-10T20:25:23.438Z", "0.1.6": "2014-11-10T22:43:29.585Z", "0.1.7": "2014-11-15T00:16:00.263Z", "0.1.8": "2014-11-15T17:58:37.233Z", "0.1.9": "2014-11-15T23:12:13.966Z", "0.1.10": "2014-11-16T00:58:58.922Z", "0.2.0": "2014-11-23T05:28:30.586Z", "0.2.1": "2014-11-23T15:51:20.447Z", "0.2.2": "2014-11-23T16:46:51.984Z", "0.2.3": "2014-11-23T17:52:03.001Z", "0.2.4": "2014-12-11T21:18:29.471Z", "0.2.5": "2015-01-02T17:37:58.478Z", "0.2.6": "2015-01-04T03:36:50.417Z", "0.2.7": "2015-01-08T01:57:04.479Z", "0.3.0": "2015-01-08T03:35:39.407Z", "0.3.1": "2015-01-15T04:17:24.566Z", "0.4.0": "2015-02-05T22:19:12.401Z", "0.4.1": "2015-02-08T16:05:36.414Z", "0.4.2": "2015-02-08T16:58:50.919Z", "0.4.3": "2015-02-15T03:29:35.016Z", "0.4.4": "2015-02-15T16:19:29.578Z", "0.4.5": "2015-03-02T00:10:22.740Z", "0.4.6": "2015-03-27T03:15:35.313Z", "0.4.7": "2015-04-03T14:47:41.035Z", "0.4.8": "2015-04-10T19:55:37.697Z", "0.4.9": "2015-04-10T20:14:12.641Z", "0.5.0": "2015-05-21T03:58:48.271Z", "0.5.1": "2015-05-21T12:42:37.394Z", "0.5.3": "2015-05-26T01:53:42.611Z", "0.6.0": "2015-05-26T22:05:45.859Z", "0.6.1": "2015-06-02T18:31:23.864Z", "0.6.2": "2015-06-02T18:34:37.299Z", "0.6.3": "2015-06-02T22:15:35.473Z", "0.6.4": "2015-07-25T23:06:21.200Z", "0.6.5": "2015-08-21T18:46:01.299Z", "0.6.6": "2015-08-29T18:51:02.147Z", "0.7.0": "2015-09-02T22:47:53.993Z", "0.8.0": "2015-10-23T02:46:28.137Z", "0.9.0": "2015-12-20T04:24:36.317Z", "0.9.1": "2015-12-20T04:27:34.052Z", "0.10.0": "2015-12-25T00:43:04.492Z", "0.10.1": "2015-12-30T16:06:37.380Z", "0.10.2": "2016-01-03T04:38:42.601Z", "0.11.0-alpha": "2016-04-06T19:57:16.932Z", "0.11.0": "2016-04-07T21:45:45.246Z", "0.11.1": "2016-04-08T16:28:11.638Z", "0.11.2": "2016-04-10T20:07:41.095Z", "0.11.3": "2016-04-11T03:48:59.204Z", "0.11.4": "2016-04-12T01:33:47.151Z", "0.12.0": "2016-05-04T15:54:36.868Z", "0.12.1": "2016-05-04T19:11:09.108Z", "0.13.0": "2016-05-09T14:46:42.955Z", "0.13.1": "2016-05-14T19:35:24.578Z", "0.14.0": "2016-05-16T03:11:30.003Z", "0.15.0": "2016-05-21T15:14:32.164Z", "0.15.1": "2016-06-09T17:52:40.930Z", "0.15.2": "2016-06-23T15:23:21.125Z", "0.16.0": "2016-08-12T15:13:03.169Z", "0.17.0": "2016-11-24T15:37:21.374Z", "0.17.1": "2016-12-01T18:10:26.267Z", "0.18.0": "2016-12-01T19:30:15.399Z", "0.19.0": "2016-12-02T16:24:03.705Z", "0.19.1": "2017-05-09T19:04:06.336Z", "0.20.0": "2017-05-17T22:25:03.723Z", "0.21.0": "2017-05-26T12:30:44.224Z", "0.21.1": "2017-05-29T14:30:43.956Z", "0.21.2": "2017-05-29T23:06:29.217Z", "0.21.3": "2017-05-30T14:05:33.069Z", "0.22.0": "2017-07-07T17:46:56.092Z", "0.22.1": "2017-07-07T17:51:14.860Z", "0.22.2": "2017-07-08T03:51:35.769Z", "0.22.3": "2017-07-21T23:34:48.473Z", "0.22.4": "2017-07-30T18:26:50.695Z", "0.22.5": "2018-03-14T14:52:05.198Z", "0.23.0": "2018-03-14T19:51:15.792Z", "0.23.1": "2018-03-14T19:53:42.353Z", "0.23.2": "2018-03-15T12:46:51.326Z", "0.24.0": "2018-03-19T15:30:52.638Z", "0.24.1": "2018-05-15T12:36:56.988Z", "0.25.0": "2018-05-31T09:29:30.358Z", "0.25.1": "2018-09-18T20:45:23.176Z", "0.25.2": "2019-02-03T17:40:02.125Z", "0.25.3": "2019-07-01T11:29:09.533Z", "0.25.4": "2019-09-30T15:12:31.607Z", "0.25.5": "2020-01-02T22:04:13.576Z", "0.25.6": "2020-01-09T13:46:17.172Z", "0.25.7": "2020-03-06T13:09:43.545Z", "0.25.8": "2022-03-02T18:24:38.654Z", "0.25.9": "2022-03-03T07:51:52.312Z", "0.26.0": "2022-03-03T08:02:36.087Z", "0.26.1": "2022-03-03T09:23:46.554Z", "0.26.2": "2022-05-11T08:55:22.113Z", "0.26.3": "2022-08-30T07:50:19.503Z", "0.26.4": "2022-09-22T08:22:48.374Z", "0.26.5": "2022-09-30T09:56:16.904Z", "0.26.6": "2022-10-05T22:06:37.914Z", "0.26.7": "2022-10-09T22:00:34.930Z", "0.27.0": "2022-12-03T22:01:55.526Z", "0.28.0": "2023-02-11T16:03:30.874Z", "0.29.0": "2023-02-11T16:04:30.947Z", "0.30.0": "2023-02-22T10:38:12.955Z", "0.30.1": "2023-07-04T05:31:35.016Z", "0.30.2": "2023-07-28T01:54:03.613Z", "0.30.3": "2023-08-21T07:53:57.836Z", "0.30.4": "2023-09-29T06:32:03.565Z", "0.30.5": "2023-10-12T13:45:39.178Z", "0.30.6": "2024-01-31T17:21:09.321Z", "0.30.7": "2024-02-05T17:08:28.888Z", "0.30.8": "2024-03-03T21:59:01.229Z", "0.30.9": "2024-04-04T08:27:55.687Z", "0.30.10": "2024-04-17T12:54:58.857Z", "0.30.11": "2024-07-29T12:33:56.240Z", "0.30.12": "2024-10-11T07:34:00.927Z", "0.30.13": "2024-11-18T18:40:25.083Z", "0.30.14": "2024-11-26T06:18:59.877Z", "0.30.15": "2024-12-10T05:31:14.453Z", "0.30.16": "2024-12-16T05:58:15.330Z", "0.30.17": "2024-12-16T06:14:54.054Z"}, "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/rich-harris/magic-string#readme", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "repository": {"url": "git+https://github.com/rich-harris/magic-string.git", "type": "git"}, "description": "Modify strings, generate sourcemaps", "maintainers": [{"name": "mourner", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "al<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "readme": "# magic-string\n\n<a href=\"https://github.com/<PERSON>-<PERSON>/magic-string/actions/workflows/test.yml\">\n  <img src=\"https://img.shields.io/github/actions/workflow/status/<PERSON>-<PERSON>/magic-string/test.yml\"\n       alt=\"build status\">\n</a>\n<a href=\"https://npmjs.org/package/magic-string\">\n  <img src=\"https://img.shields.io/npm/v/magic-string.svg\"\n       alt=\"npm version\">\n</a>\n<a href=\"https://github.com/<PERSON>-Harris/magic-string/blob/master/LICENSE.md\">\n  <img src=\"https://img.shields.io/npm/l/magic-string.svg\"\n       alt=\"license\">\n</a>\n\nSuppose you have some source code. You want to make some light modifications to it - replacing a few characters here and there, wrapping it with a header and footer, etc - and ideally you'd like to generate a [source map](https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/) at the end of it. You've thought about using something like [recast](https://github.com/benjamn/recast) (which allows you to generate an AST from some JavaScript, manipulate it, and reprint it with a sourcemap without losing your comments and formatting), but it seems like overkill for your needs (or maybe the source code isn't JavaScript).\n\nYour requirements are, frankly, rather niche. But they're requirements that I also have, and for which I made magic-string. It's a small, fast utility for manipulating strings and generating sourcemaps.\n\n## Installation\n\nmagic-string works in both node.js and browser environments. For node, install with npm:\n\n```bash\nnpm i magic-string\n```\n\nTo use in browser, grab the [magic-string.umd.js](https://unpkg.com/magic-string/dist/magic-string.umd.js) file and add it to your page:\n\n```html\n<script src=\"magic-string.umd.js\"></script>\n```\n\n(It also works with various module systems, if you prefer that sort of thing - it has a dependency on [vlq](https://github.com/Rich-Harris/vlq).)\n\n## Usage\n\nThese examples assume you're in node.js, or something similar:\n\n```js\nimport MagicString from 'magic-string';\nimport fs from 'fs';\n\nconst s = new MagicString('problems = 99');\n\ns.update(0, 8, 'answer');\ns.toString(); // 'answer = 99'\n\ns.update(11, 13, '42'); // character indices always refer to the original string\ns.toString(); // 'answer = 42'\n\ns.prepend('var ').append(';'); // most methods are chainable\ns.toString(); // 'var answer = 42;'\n\nconst map = s.generateMap({\n\tsource: 'source.js',\n\tfile: 'converted.js.map',\n\tincludeContent: true,\n}); // generates a v3 sourcemap\n\nfs.writeFileSync('converted.js', s.toString());\nfs.writeFileSync('converted.js.map', map.toString());\n```\n\nYou can pass an options argument:\n\n```js\nconst s = new MagicString(someCode, {\n\t// these options will be used if you later call `bundle.addSource( s )` - see below\n\tfilename: 'foo.js',\n\tindentExclusionRanges: [\n\t\t/*...*/\n\t],\n\t// mark source as ignore in DevTools, see below #Bundling\n\tignoreList: false,\n\t// adjust the incoming position - see below\n\toffset: 0,\n});\n```\n\n## Properties\n\n### s.offset\n\nSets the offset property to adjust the incoming position for the following APIs: `slice`, `update`, `overwrite`, `appendLeft`, `prependLeft`, `appendRight`, `prependRight`, `move`, `reset`, and `remove`.\n\nExample usage:\n\n```ts\nconst s = new MagicString('hello world', { offset: 0 });\ns.offset = 6;\ns.slice() === 'world';\n```\n\n## Methods\n\n### s.addSourcemapLocation( index )\n\nAdds the specified character index (with respect to the original string) to sourcemap mappings, if `hires` is `false` (see below).\n\n### s.append( content )\n\nAppends the specified content to the end of the string. Returns `this`.\n\n### s.appendLeft( index, content )\n\nAppends the specified `content` at the `index` in the original string. If a range _ending_ with `index` is subsequently moved, the insert will be moved with it. Returns `this`. See also `s.prependLeft(...)`.\n\n### s.appendRight( index, content )\n\nAppends the specified `content` at the `index` in the original string. If a range _starting_ with `index` is subsequently moved, the insert will be moved with it. Returns `this`. See also `s.prependRight(...)`.\n\n### s.clone()\n\nDoes what you'd expect.\n\n### s.generateDecodedMap( options )\n\nGenerates a sourcemap object with raw mappings in array form, rather than encoded as a string. See `generateMap` documentation below for options details. Useful if you need to manipulate the sourcemap further, but most of the time you will use `generateMap` instead.\n\n### s.generateMap( options )\n\nGenerates a [version 3 sourcemap](https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit). All options are, well, optional:\n\n- `file` - the filename where you plan to write the sourcemap\n- `source` - the filename of the file containing the original source\n- `includeContent` - whether to include the original content in the map's `sourcesContent` array\n- `hires` - whether the mapping should be high-resolution. Hi-res mappings map every single character, meaning (for example) your devtools will always be able to pinpoint the exact location of function calls and so on. With lo-res mappings, devtools may only be able to identify the correct line - but they're quicker to generate and less bulky. You can also set `\"boundary\"` to generate a semi-hi-res mappings segmented per word boundary instead of per character, suitable for string semantics that are separated by words. If sourcemap locations have been specified with `s.addSourcemapLocation()`, they will be used here.\n\nThe returned sourcemap has two (non-enumerable) methods attached for convenience:\n\n- `toString` - returns the equivalent of `JSON.stringify(map)`\n- `toUrl` - returns a DataURI containing the sourcemap. Useful for doing this sort of thing:\n\n```js\ncode += '\\n//# sourceMappingURL=' + map.toUrl();\n```\n\n### s.hasChanged()\n\nIndicates if the string has been changed.\n\n### s.indent( prefix[, options] )\n\nPrefixes each line of the string with `prefix`. If `prefix` is not supplied, the indentation will be guessed from the original content, falling back to a single tab character. Returns `this`.\n\nThe `options` argument can have an `exclude` property, which is an array of `[start, end]` character ranges. These ranges will be excluded from the indentation - useful for (e.g.) multiline strings.\n\n### s.insertLeft( index, content )\n\n**DEPRECATED** since 0.17 – use `s.appendLeft(...)` instead\n\n### s.insertRight( index, content )\n\n**DEPRECATED** since 0.17 – use `s.prependRight(...)` instead\n\n### s.isEmpty()\n\nReturns true if the resulting source is empty (disregarding white space).\n\n### s.locate( index )\n\n**DEPRECATED** since 0.10 – see [#30](https://github.com/Rich-Harris/magic-string/pull/30)\n\n### s.locateOrigin( index )\n\n**DEPRECATED** since 0.10 – see [#30](https://github.com/Rich-Harris/magic-string/pull/30)\n\n### s.move( start, end, index )\n\nMoves the characters from `start` and `end` to `index`. Returns `this`.\n\n### s.overwrite( start, end, content[, options] )\n\nReplaces the characters from `start` to `end` with `content`, along with the appended/prepended content in that range. The same restrictions as `s.remove()` apply. Returns `this`.\n\nThe fourth argument is optional. It can have a `storeName` property — if `true`, the original name will be stored for later inclusion in a sourcemap's `names` array — and a `contentOnly` property which determines whether only the content is overwritten, or anything that was appended/prepended to the range as well.\n\nIt may be preferred to use `s.update(...)` instead if you wish to avoid overwriting the appended/prepended content.\n\n### s.prepend( content )\n\nPrepends the string with the specified content. Returns `this`.\n\n### s.prependLeft ( index, content )\n\nSame as `s.appendLeft(...)`, except that the inserted content will go _before_ any previous appends or prepends at `index`\n\n### s.prependRight ( index, content )\n\nSame as `s.appendRight(...)`, except that the inserted content will go _before_ any previous appends or prepends at `index`\n\n### s.replace( regexpOrString, substitution )\n\nString replacement with RegExp or string. When using a RegExp, replacer function is also supported. Returns `this`.\n\n```ts\nimport MagicString from 'magic-string';\n\nconst s = new MagicString(source);\n\ns.replace('foo', 'bar');\ns.replace(/foo/g, 'bar');\ns.replace(/(\\w)(\\d+)/g, (_, $1, $2) => $1.toUpperCase() + $2);\n```\n\nThe differences from [`String.replace`](<(https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace)>):\n\n- It will always match against the **original string**\n- It mutates the magic string state (use `.clone()` to be immutable)\n\n### s.replaceAll( regexpOrString, substitution )\n\nSame as `s.replace`, but replace all matched strings instead of just one.\nIf `regexpOrString` is a regex, then it must have the global (`g`) flag set, or a `TypeError` is thrown. Matches the behavior of the builtin [`String.property.replaceAll`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replaceAll). Returns `this`.\n\n### s.remove( start, end )\n\nRemoves the characters from `start` to `end` (of the original string, **not** the generated string). Removing the same content twice, or making removals that partially overlap, will cause an error. Returns `this`.\n\n### s.reset( start, end )\n\nResets the characters from `start` to `end` (of the original string, **not** the generated string).\nIt can be used to restore previously removed characters and discard unwanted changes.\n\n### s.slice( start, end )\n\nReturns the content of the generated string that corresponds to the slice between `start` and `end` of the original string. Throws error if the indices are for characters that were already removed.\n\n### s.snip( start, end )\n\nReturns a clone of `s`, with all content before the `start` and `end` characters of the original string removed.\n\n### s.toString()\n\nReturns the generated string.\n\n### s.trim([ charType ])\n\nTrims content matching `charType` (defaults to `\\s`, i.e. whitespace) from the start and end. Returns `this`.\n\n### s.trimStart([ charType ])\n\nTrims content matching `charType` (defaults to `\\s`, i.e. whitespace) from the start. Returns `this`.\n\n### s.trimEnd([ charType ])\n\nTrims content matching `charType` (defaults to `\\s`, i.e. whitespace) from the end. Returns `this`.\n\n### s.trimLines()\n\nRemoves empty lines from the start and end. Returns `this`.\n\n### s.update( start, end, content[, options] )\n\nReplaces the characters from `start` to `end` with `content`. The same restrictions as `s.remove()` apply. Returns `this`.\n\nThe fourth argument is optional. It can have a `storeName` property — if `true`, the original name will be stored for later inclusion in a sourcemap's `names` array — and an `overwrite` property which defaults to `false` and determines whether anything that was appended/prepended to the range will be overwritten along with the original content.\n\n`s.update(start, end, content)` is equivalent to `s.overwrite(start, end, content, { contentOnly: true })`.\n\n## Bundling\n\nTo concatenate several sources, use `MagicString.Bundle`:\n\n```js\nconst bundle = new MagicString.Bundle();\n\nbundle.addSource({\n\tfilename: 'foo.js',\n\tcontent: new MagicString('var answer = 42;'),\n});\n\nbundle.addSource({\n\tfilename: 'bar.js',\n\tcontent: new MagicString('console.log( answer )'),\n});\n\n// Sources can be marked as ignore-listed, which provides a hint to debuggers\n// to not step into this code and also don't show the source files depending\n// on user preferences.\nbundle.addSource({\n\tfilename: 'some-3rdparty-library.js',\n\tcontent: new MagicString('function myLib(){}'),\n\tignoreList: false, // <--\n});\n\n// Advanced: a source can include an `indentExclusionRanges` property\n// alongside `filename` and `content`. This will be passed to `s.indent()`\n// - see documentation above\n\nbundle\n\t.indent() // optionally, pass an indent string, otherwise it will be guessed\n\t.prepend('(function () {\\n')\n\t.append('}());');\n\nbundle.toString();\n// (function () {\n//   var answer = 42;\n//   console.log( answer );\n// }());\n\n// options are as per `s.generateMap()` above\nconst map = bundle.generateMap({\n\tfile: 'bundle.js',\n\tincludeContent: true,\n\thires: true,\n});\n```\n\nAs an alternative syntax, if you a) don't have `filename` or `indentExclusionRanges` options, or b) passed those in when you used `new MagicString(...)`, you can simply pass the `MagicString` instance itself:\n\n```js\nconst bundle = new MagicString.Bundle();\nconst source = new MagicString(someCode, {\n\tfilename: 'foo.js',\n});\n\nbundle.addSource(source);\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"tstonelee": true, "flumpus-dev": true, "pedromsilva": true, "scottfreecode": true, "supan_20220713": true}}