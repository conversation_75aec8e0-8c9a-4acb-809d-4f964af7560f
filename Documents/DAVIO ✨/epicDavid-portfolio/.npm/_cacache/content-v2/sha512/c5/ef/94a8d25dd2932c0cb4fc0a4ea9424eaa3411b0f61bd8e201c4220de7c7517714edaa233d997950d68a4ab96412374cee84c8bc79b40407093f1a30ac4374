{"_id": "v8-to-istanbul", "_rev": "58-666717a27a5f423c5fbf8fc85a5720d9", "name": "v8-to-istanbul", "description": "convert from v8 coverage format to istanbul's format", "dist-tags": {"next": "8.2.0-candidate.1", "latest": "9.3.0"}, "versions": {"1.0.0": {"name": "v8-to-istanbul", "version": "1.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "dcb19789d67a5d85aef63ac7cd808438929f6a19", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-1.0.0.tgz", "integrity": "sha512-gVkpIIdBdj2BcrTjYZlhCU3qhwYzJITbicwrRLarhhIoGZHh7WkX8NLvqDtwFp6zs0TKWqUY7nhtMqvq0pnYbw==", "signatures": [{"sig": "MEQCIE59SsM/c5OzCyUJlBXnvTzIa0EaL37HAiJk6NiKgaVPAiBurTaqLnBUzQJQhwxisOl4IRd7uIuRXdvWVoijNce3/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "1db19cf76536467012a762f0d202e551f17a07e1", "scripts": {"test": "nyc mocha test/*.js", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "5.4.2", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "8.8.1", "devDependencies": {"nyc": "^11.3.0", "chai": "^4.1.2", "mocha": "^4.0.1", "standard": "^10.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul-1.0.0.tgz_1511584518700_0.22098653577268124", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "v8-to-istanbul", "version": "1.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "3bca8d0963501cfcc22e9ebc1cea81e4fe287861", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-1.1.0.tgz", "integrity": "sha512-srWgCTVt1HMcmzYuVvJe3zbT8wVBQ6Ev0/K/u+ZOhakUa5xYHWxOuTDn3+7DBTt+C7OQdlJ0fXvdsBBf36S3EQ==", "signatures": [{"sig": "MEUCIQD6dFLjmlipz/h9sSiA+qKDuQj4hRH6D9xp2lkaMUKxsAIgFn5Le4IgaiiYfWmzXVuTkKD9c9xBLkr0P4so5Y9w1Qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "38473183e6a2e034ae20310c42b6680e4dc845a1", "scripts": {"test": "tap test/*.js --cov", "release": "standard-version", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "5.4.2", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "8.8.1", "devDependencies": {"tap": "^11.0.0", "should": "^13.1.3", "standard": "^10.0.3", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul-1.1.0.tgz_1512113036272_0.18289228831417859", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "v8-to-istanbul", "version": "1.2.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@1.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "f6a22ffb08b2202aaba8c2be497d1d41fe8fb4b6", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-1.2.0.tgz", "integrity": "sha512-rVSmjdEfJmOHN8GYCbg+XUhbzXZr7DzdaXIslB9DdcopGZEMsW5x5qIdxr/8DcW7msULHNnvs/xUY1TszvhKRw==", "signatures": [{"sig": "MEQCIEbg5nzLErpGG693Qud1WO7lYpl+WZRxnjcg6kSBOUS+AiAa8kZ4Wi4PxDt41c3qpxD0oV2AXaLMYQr5ZP9FHlJClQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "67c0a6286032fae98c4dcbedd23477f82cd40e71", "scripts": {"test": "tap test/*.js --cov", "release": "standard-version", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "5.4.2", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "8.8.1", "devDependencies": {"tap": "^11.0.0", "should": "^13.1.3", "standard": "^10.0.3", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul-1.2.0.tgz_1512453192996_0.3012832272797823", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "v8-to-istanbul", "version": "1.2.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@1.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "8f63a94b7f91243f5dcc6a495540b5653beb1279", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-1.2.1.tgz", "fileCount": 17, "integrity": "sha512-NglPycIwSQeSJj7VJ6L8vTsPKC9MG5Lcx4n3SvYqNHzklbMI4dGcLJnkLPEPJ3uB8UyTdWviMhM0Ptq+xD5UFQ==", "signatures": [{"sig": "MEYCIQCcpHYmkT+QT/v13evcvJD4D1/4RXjFLEQFq/Y+dl32xgIhAJck609izKNEx13Yu95NiwdZ7pA43U6/IVByEWlzBOzA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmZglCRA9TVsSAnZWagAARFsP/RXiZ0+fq60865cVJqCE\nXOOsrejroirq47xxKg1/j4q/jwPYo0dE+KYfm7A9ykJmquifjxdhiRTW7Wsc\np9laXmcxYhIGQNB/SELq8TyAI/NsU90BhgQSLL3KfsyzieMxBi9Xv0lqGjCK\n/dvEqkxLjcyV/vOqsoEjFWWIRY0e94dNZKoKohoR0UYZfCvLJQzB/TAK8fuQ\nOhxhzLXM5DFaKNV9VFe+7vhXzmlmTRmnPDn8nmx3biPBRXlVjev4gWVjlyym\nveDsBBWv9RfaN78HUdHbsOwbaXZ8yqlRygRPRyVPKNMpxvb+zR/ZveF6vong\nvhfDWMq51SzperbqVfN5xiKAHkvngY4bzx7CpzJ9iyLq86eJZZwSQdgcoAFu\nAfmnBiLc6dwOKsEvDr4gQ6UtXzr2vqtgQ/EKoD1Zhf84NkSbQP5n9IdZu7UZ\nPpM/Lt3aw+AjnmSbMLs8eBBqsxsDRL3P/gG/eRdclX6HRaObcsH8P8HOr53J\n8TeoQ5ycv3Z2Uq6u7KPqkeTRq+bP2InaUXjB/w0x4bkvbLF9r9PYZfY9a6Q9\ngRy7dAbCv4AD4D34tXJH88FNRKcM9x1MPLX5CRSZpijQrpmvXQY7+GrTn/Po\neVm6rC4Zp2yeJgBLUOwaJGQstn/v3siaq/ZcjMwTAYxsPEaEHDRHhIYbkNvv\ncu2O\r\n=/5cf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "814ae299852eb2c5ddf52c2adc66a215d07c24b0", "scripts": {"test": "c8 --reporter=html --reporter=text tap test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.4.1", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.1.0", "tap": "^12.0.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_1.2.1_1536792613209_0.44539995947314903", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "v8-to-istanbul", "version": "2.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "26e7a33f551a323fa93d0c76eabf73336c51d3dd", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.0.tgz", "fileCount": 17, "integrity": "sha512-2swGput8KMKy61XglKvdf1+cwcJeQin00Ep+emQ3maiGEksNqtp3UlVII84+5mhinumPDaa0O5Y9JLTNA2ShGA==", "signatures": [{"sig": "MEUCIHTXn6gJmdHSYXOFk4i+F+tQVwvyv6Xt8PQ/DMhErf5mAiEAmlhQhdATxulHyoTB+jBEuFNhbtso1HyRXvcvfEjxTHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHDl6CRA9TVsSAnZWagAA/OkP/id1k+qXS2ueSFjIUY6M\nrDYaNmzqxPogzqJOL9j6vOPP6mHcISHtzv5423/Ef3KWIYghF74cuOixtuD8\ninM2tDGsDdMgDI3Hteoewb2GL1R1RouJQzy7JFRzvlP4twry1Uv7ztT0CX+P\npZ+JQM+bX73vt5nB77D3h8tDv9Sinsc+nfvurroh4vg6r7X+DA86bhSbgYQD\nam419tTCJg5VexSIJGj8UsPzVeap2abIqjm2gknSNu6F9VVCBg9w+uiSOuYt\nXK/vZDl808PH78DgvuTIU4v11pUGYke9hlayarH/r0ZSp1LV/ohXnDUe3AlG\nCI8umKUZtf7LWL0cw3CZt4wthCmu7gE+vTlB6TAoPTo2OecM0jbmmGW42q7d\noeucT52iSNuqDU17EGwAXZ3dVNrf1hR7RPv6SsPKP9+rMWtdZSxXYh1/bwyT\nrjbJPobzz/hB/oZHNEOzqvZnINSu5jwJ6SUiM0hMwoebm9wepIE7Ugopbbon\nSxi82GOK8WSRz/jFjPllXcSyajr84JjaeKBvsqLiQFakaHHuucCwFAagEs59\nU/hnyXi11e4tqc10gUrnPuMfRvnqxj/uBostrkumm1pwaL7vE3/f8O+/Kfh+\nC62tWMOlgDdhlwuP8Zvn+Wgvh6TDwkmhad2p2SsxQdVh4GV1Q4f1jwyR/ZbS\n4YR4\r\n=f8Ne\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "c3b64dbca696ea14d42036557be7bda5ddb9c4ae", "scripts": {"test": "c8 --reporter=html --reporter=text tap test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.4.1", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.1.0", "tap": "^12.0.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.0_1545353593629_0.5366970878493111", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "v8-to-istanbul", "version": "2.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "3443d153b1e92ea3d511f5da7a24e690186de9b3", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.1.tgz", "fileCount": 17, "integrity": "sha512-izRDEYI1HdYCuymMiPXMHgHHpJzE9dRPFGZdz9XQbdlx9krtXH+GLun5rwQlR8JUTPCzoKUgrTGdWh5nxfUHQA==", "signatures": [{"sig": "MEUCICbxfXPyNceee6+HSSrHTb+vxRzZYlDLPvIeqpVoC8fhAiEA5UEvwrP7kFosEnYA3JLbtRZ6QyeOX8ZAo6v3oq9guQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRNoRCRA9TVsSAnZWagAAvdoP/2pFet2AxhRyryJ3a6vd\no8QovqsFx7pi1t14sfWspzJHOw9vra/bpdUa3kQOcbh75Heb5Bs+XKLm4FBi\nsMGkQ4dGKE46eRv0/X8P/wXX1613JNve5+8ZRBGfCZPWGUZtqQ28iLAY+6CD\nQgdcVEgH2vTvSGxHzHfx7bs/EefLaM4iGwtsbms0q4FofD4EnD42/OrCq2nx\ndAmt1/eOeGmwzDcCtrBEes+o8eJBMI4j/6OfFh5qA5Q6MU68oHqQRMOAUNIQ\nR/hwFFNkmYMbn8AwvTJdFFe6yCN35poHNX61IjOxRcNzhF6OGMqT9DM60la1\n7Zey/u9+sOMD0ubHGEumDFzl5OyJV3ESicRtJewPoPKReLyWNHStHCCJWcL/\n5L7OLrhsWavFsnSxXz8b/4983vz2XHHFQR1ajPMDgzU3hwLoTrQcQAw0VApI\nE94Jtwrt+NXb/m4pTD9R5MtJlwwNrmZAPHwdH81GvYMGgKgPRCxuYyll3UrP\n1hqzy7mHwB6CVLYRUXzj6sqin9gFVergBmwFLyeNLce/DewioCQQbYIm0HIS\nEgZQ1axeM+IW6z/9/TPwsqQGVvbfosK+yAvRZcGhuh2hmJ3JWJ/IPASSvEoV\nZfTJmCgEnA1EUUaHNWPL1Hnl24FvuArAxDHthi2i4vjiuXjVPUVmDVo211Fo\nljYM\r\n=xcib\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "56d6b873437017f4d6dcf3bcbff334b47d5a7ca4", "scripts": {"test": "c8 --reporter=html --reporter=text tap test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.5.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.7.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.3.0-candidate.2", "tap": "^12.0.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.1_1548016144916_0.4415380614149613", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "v8-to-istanbul", "version": "2.0.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "dbb1ae6566b6400ed5325ce594c320c26bbb58a4", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.2.tgz", "fileCount": 17, "integrity": "sha512-39Z8JhWATTDDHlTEbSOv/G4j4xFWJy7ESMhfCLoj6IEd6yu4DOaexdTW89GTqE29d51PyH2lSS12c2HJhlRd1A==", "signatures": [{"sig": "MEUCIQD6e1nPBFO+3LB8hxvDWH8h8RN9g35EFP6a8rkwI94g8AIgRbrzdPtsq/UYdGo0TN3Q6z2jCEbYvMbc3ufBE8SgQQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRPB9CRA9TVsSAnZWagAAmG4P+QAkipis5PInEh9psKl6\nXmIls+8F9dyVZVeOL/4MRXxoWT0EB+XRxzl5UnBHsfWlwtl5Wqc2wEF9DZ6k\nkjS30c2w/YGjos6I4/oAykCiv8AkvQuHWA2HlhQfvSCJn0NWXLhqGpB/p/D0\nw8X5tDWeJiXZQEtBlyBEAqJD2LnKcGvQ0K77XAn7BUJwCjUVYo1ciGzcIyW1\nkx+D5qb38bC0Wxn2wHjFpU7iouuz+nWNuxrtln5hH33aX5yI5pRP6eNFPgtj\nFnhxy/NEkoL2NPpqccpD1Q805FWZbvNAt/hC3XhDQYEAG59KJ7m0bl4zWtcM\n7CtLlz+U2y0ah8K/S/RbMzihNmvotI1UPCqaokdEtxAAcZge3yO65ZG+p2vc\nVDerBB7aO20PU/d9rE1dMPp+2dMTJKq0/8eixmz8w2LwRyELqI5RC8tsEzA+\nf7s0/aVohkaHhc6JL6x1isfsI/lTBk0iI1AkhBV4hWYknoVTcs7/fyI6RUAx\n/iU/E9IDGJid6r9PDsXg+ptfTzyFrbP2ldr/ysnAWqIsvjrh67wv7Xy7zz0u\nOD+06ey8rG9SUIdsBGTpw1oC8/ZDrRPCzlmEXKyxkIVH7tW6OP7KaDLbmev0\n36Ep227P0UDICSH+J4SfHrtmawxYM8n8bC/FZdEn6pvAPUej3ht0OtBA31FD\nnWGT\r\n=VvUK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "dc9943c2d0a88847abd70c2ec071ca7f1844c80b", "scripts": {"test": "c8 --reporter=html --reporter=text tap test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.6.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.3.0-candidate.2", "tap": "^12.0.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.2_1548021884622_0.3368162655913618", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "v8-to-istanbul", "version": "2.0.3", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "bbaca3adb14a3a9bfb14a07487e35deb026c4f3b", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.3.tgz", "fileCount": 19, "integrity": "sha512-IS6wVxGrKA3eURi8+OgmlzG1bjgvihtoAC47SXJ3j21eN/4Otud5TJ+7aTcYGBOFVNfuBSjlHvSa+WYiFWT/Jg==", "signatures": [{"sig": "MEQCIFQqa1DBJ85aYp8GMRr/s67rYfei0D2+GvZ5jea1smxlAiAmSO2oRPVl++WOtbfbYb2oJGHdSAIBMRt85mTQDNBbdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqVLaCRA9TVsSAnZWagAAo/IP/ikiLsYtvnwNOpQwm9uN\nsN7XAEQSFor8W+rtejN3jRMZQ+E6mPINcxSyTbQPy4tCsZ4S6WT24KMko7mh\nbULESbZArxgiP2/5px3rF8y3WbhmtHpgrPdGSUkopaGElVo2hzyJfyKUVjV0\nzZ3IsN2arwv8jxWwfueq0RuLYfaEWqix1IpdBr6cm7oDTkX5pCzpP+Qew4Ua\naUXkdo6RKu1UmU2ytJmtz3Ams72riAT509f95yB/DIHpBvqTnE831eexn7iW\nIzacQnsEp5u7txneXEjgHAxqbZ6CR9/eHEPCB780snf5JcaZZ8zW5Fv28Zsh\nH0DHghv0pt7GLwikpjiRrFSwJOj/vtGBNVa3gT19zT868B6fAC9+bcgpkuke\n0P2/jRBAOZlAKU5xw3BK9wyflJXhs5/q7nrc83RRUSt6nfA+H30o3QySEjEN\n/i0AA6QYNh6FGON6XkeNagNjL/3SmZvw2BjiHt+kn+9zpJxcpnuUems2gqH9\noDdOUWU17mpDaIy0rCTEvTNZcUvpFV56Hv3z5RIBDj64sp99XSOcz/eH+DUd\n8jHX/itTNJaBtME4L/sIE3nhBYXixbCe8sbfB1oiRZc/o66toR2lIe9ID7ec\nxsdVx0IS4mhF6BRsiQTxzxV4LYwOP7UKW2IxMmwY7AJdx0U8KI3lFmmuuB2u\nz0UF\r\n=ejfT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "48ef3822e0cbfe1212117b3e91fd6de4bcc516e7", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.6.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.4.0", "tap": "^12.6.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.3_1554600665360_0.8870965725753195", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "v8-to-istanbul", "version": "2.0.4", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "2569e6f2f3dba90ff1cd2c7a1c26146af1cf80c9", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.4.tgz", "fileCount": 19, "integrity": "sha512-eAHPnQYNHlZWxfa76dUrgdBK7cTmSSoX1aPq1yH/0tImW2gAGLw2GF+hhMaKAD9RvyJUP6pfSLVmV3HkoRv5oA==", "signatures": [{"sig": "MEYCIQDfJOFo1ZLeEhyVdaqr1yGgRnUlrEs8KCW7+PjLp2m+0AIhAOGLtIMk5z6uEDHtfpYJqFfPVrdZOqLADguvuPzMmAQl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqV00CRA9TVsSAnZWagAAh8UP/i/AK0vd1yCbU5Ppn/Is\nVxWVwGSm/5vopcTygA37dGyQM2jdSybBfCszFZZUaf4eh9KP2E9wnPxjR5Ki\nmqGBOtNcsThfbRl/E29Ku2OJfNebBzN/bff6vVyUX4g4BeN3kWDZ8sdpekCT\nMpyGlZmjP0BuZfXZgbEbfe6qwLOdYh3l2U9EmffV0ol7WbFwubnUlj/cKT6K\nFIMXne6PYp0xtJMVWwb9WAaguGjH7XaNVAJa+JiWQML3vXmyFhFBYILmbbTq\nSBRxstia8lGmN0+fTUKSESqShPohc3q1W85yU8qyjE9JLn6TWNxzucnbwu8a\nbwfRvK6GJpC5Zq5dyNdZyJ8Daa9Ll80kIK4XYLqIvmD2F9UKMGt9NMsU1AXI\n3LZj43/VKUufcNs/gsRm3+bkJDQoTJZ/qXz8z8d3fE+ZdOIg51eg2dWdDebx\njvl7EyP/ZrPGskGsr+vuBn1gOdMBbdtUDelhlM6sj+Sr3FBZ3ZuLHc5Uv/WP\nVHL8JgbN6+0F0fadpcbTF/AuoCCyPJwkpjXum6fQ0eH4Ys/AUgH2FH7i1ANh\nEMrGv3go6C3YKqvHdMqFdD/ArpYObeKEWD1MecWoJqnUR0cx1+CDtV1H67tG\npS/QRzy+yLq6po2Nmf9w323v2yVFTwZjkK2CD4KRoDlaSO5YyDrjPBEYGN1Z\nGz9v\r\n=o/mZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "b7dec2b1155cfd4d9e103bcdb512ef73ce723c95", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.13.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.4.0", "tap": "^12.6.1", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.4_1554603315191_0.8548327134278424", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "v8-to-istanbul", "version": "2.0.5", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.0.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "74d5c74255a58be54e0269d1f1b8f31b70d8cba0", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.0.5.tgz", "fileCount": 21, "integrity": "sha512-DllgVqHN51hqeDg5icNaLdh6UmbHox/4DFYDEymSeWjHs61IRO+hdA8cYIVHv6Pw9XNVnFx9m7fkqClk50zD0g==", "signatures": [{"sig": "MEQCICbZ2Yu1/iQuU6eidUK8FRZZnpD30AbdUG/m3EpMQ43zAiAuMZ5YF3k3QMn55PaCzlSywViPANvvl5RjXVJUA6MRmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuNOnCRA9TVsSAnZWagAA2lgQAIK6LnTclW8dRg2EJ1o8\n1zIqGXY37uFEeX4U80FYd/XKcM7skmEFKvE36OUOUx2nS8f7+6MvcMTs1TRg\njyKSX/VtMqSyMFZ3baHnqEo6eWtgmduyIzf/xa1Ma9tu7FN8i8qFt84PaJfe\na6pRAXFi8xeUoZeKzwfvgbp2EqSD54j2FKVDFPLeZzYm2XgU0s2+MVann8QB\nq8H2ndy3ZLd0h5/Wg11PXCoETVgJGuoTT4BTwgL7owZv0IZgxQt1bBdcqCcb\nOho2RLtQlHoXAz0pv9R7Kt//w08tOfK8AmTafAsJFZg8CMWQLiwzv5uc4CSU\nhxPwpIGc+K+f/WCPd6/NOzLVEIQcmhckpJohjRKs6sYv4fKtNxO907XOyAwe\ngEGTuMak7dFmJcuYJO6VfnLu6GZTLnk/QI4m9pM8GUw2lcEeRe36KORi7vFr\nuy4VAqmgy89enAFETEGVR7cnn1rzPzheYBdZTV39YFiFWlUsDcbESTtS5hly\n4NhsPS+Z8B4D0EjKXU6qaWterUbtN/1FG7m7TgEkU5fdhah6oKMH8UPDwFg6\nS6niNKEWJheJ291C9pTsq/CMKFUxFDDgie8jV8wrnI2Ewf+bj7jsLbvVZpz7\n90lDHFZoL+A/rBQ/Z6jw9S4SmvhZ8SsDj3gCFacTxXN6EDAgT6AI57h9ZLJH\nODXf\r\n=JCwv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "20dd0efb7e76b4da089c99639f5bd0fcb31ea2da", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.5.0", "tap": "^12.6.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.0.5_1555616678318_0.19323563014926326", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "v8-to-istanbul", "version": "2.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@2.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "f74b6081bfc3a317dc1ed104c8b118f8ddc86264", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-2.1.0.tgz", "fileCount": 21, "integrity": "sha512-3XHoQH/BjY5vO26v2puZ4hs54z5O4s5R8AfKH5ktu0hANWNtYDOFwB1iBstkGISgEpohs/U81vY8B0KbQz3N2A==", "signatures": [{"sig": "MEUCIBeWUnOB4mwAJUN2/hTvFwULRa4Yh/7s45N5fZ5kq8AyAiEAihjXFPBpwM5cXpaXB6Q0xe7eX6fS7+mmUCT4J1XCK80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcu8FJCRA9TVsSAnZWagAA048P/iDibK3mGDWNIon4qnC9\nSJe65G7XKrNhxyj5GNixGWChfaiiLVOztmJtvXrbtcAawg8zf4N0mywG6+Ff\ni1WqCQTjDTXVSF3snX8Uuk6pUyNWqaz9n0OAazy7QgeKcvbr+/fHE5EbA6eO\nlfK1f2r6NUsFdMjUABhYltuEFk0plNNCazsZkQplvE0QJtz5eAS6c/uFSlgf\nS1y0X1UXLAj9VIUbqXclFCTq9Q5bWVRXRDGjDcW67oBShNzQzW204nIyt+vD\n8reVj0BAPw7QEyBq4CWArSO+SgA021Hif8Lc+g8AI33240Z5aR10mCRVMqwK\nm0hqV3y15KdO+M9GKkYwIIaGh53VNxh7uwAxGcSJyzjLxlbW5Qp9SvfAXPxQ\n5CFxvjM+Yq5uyKX4YhizL2jjASK01u20zpqQgyDBzBXZfSc2iIp9b7gPk3YA\npNngHx1orGR+1l+ydpR1cHrg4Hncf5Tfd68U1tJqVANTDnON7eBqgDCXlxbP\nXqJeGDODifHd06h9wGj7qy36q131rWgm4hSKULyVlb/tx14hkllVavIVUs1W\nJlB1LzJKjHMtfGBiVXcwZEMPUI3CC0U7z5kORdU3lDxn8TsD31l/JISc3Bhl\ndm8MlzUbUX9wlET6OuYhdNo351gnJD2BYYUbAybpQTpXX82kPvBToF80uVAs\nFu36\r\n=9DMm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "8aa931e2605012cd8027227714c3c2448a6a6bfd", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.13.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.5.0", "tap": "^12.6.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_2.1.0_1555808584213_0.5091924468755344", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "v8-to-istanbul", "version": "3.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "f794a91529e936a6a97ab0d36dfdfe2df5bac134", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.0.0.tgz", "fileCount": 26, "integrity": "sha512-+RQCRyyizDF4Fu7rkHB8TSBvWtf2543fCh1jWg5plgBZ4tn+EwTDdN7p3zC+Go6hhaiTVl0jMxPj67bSzOdHwg==", "signatures": [{"sig": "MEYCIQDEGF7sq8krdfyrTFKiLsnRg/G07nyln1sybh9pp0FkwgIhAJ4eLEcJs7hQNgbzZ08zvCPHRKm2Y/s02RZSVKcJNqCp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxp/uCRA9TVsSAnZWagAA+8YP/RBvdMJ9dKRAGb9RVR3C\n7el1bYUxxa6ovJfmhUPbmoBxdKVLiIxxPIAhI9ceQ2bwccu81G1sXBM8zPLU\nYUoAUwG+9m88Lqwmt9oub55GakSBTmIeuIVSq4e952KHVOtABgxXr4c5taLC\nUTsZYSfwK/8NqmWIiN5EoMCPZhk6bt6XPqbpI1z2sXuOnLKBWRc+60PH4lJr\nB7Zd8xRQ9Tp7ScxrzZwdXLFr/tlAPDEJd3uapvgpcA5Z4ROPHdxBtyIvRrLg\npoA/UktkW2xujYjmfEMD+GKUIqVaczvbKT6HfYLN/P6odRjoh4pnT+osqyv9\nwputvn7hUqdjn+Jl3GTgAfpwCHH3K35yO45S+jE5tBfLkMpwDGvUKIurqnMy\nhQRYzsKSm5Q8BTIaMnBnPQXzoU47cjZuOY6YgRUIDj8lz6MeiKAv9NlgVdkT\n6+JBu4HEWt322VEROFWUE6aM7OaQ1wZSpPv1XxJsT+QoX8tLkT8CjV+UEDyq\nIC2zZKIQirAp8iBwuNsKjl4GFQ7YiH8RvWYwka2WAARzpU6/slUhDTR9hiQP\nqBZx0f8ZpHypAnN/Wj1KbwGRcjD18dg42+mT4lXRQ8c9IMmV5/TF3gNidL1N\n0TFUpqKGiqZ/eODBmAFjZTSXPCaoXHU966bloxd7srzhpR4B6+QT5gfn5/ru\nWI6x\r\n=WOL0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "428811219491feed51642943356677a3aa67d247", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.5.0", "tap": "^12.6.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "source-map": "^0.7.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.0.0_1556520941866_0.005016098006378922", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "v8-to-istanbul", "version": "3.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "8154edda0b3c54ad735d53231d7918b3642bc5b4", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.0.1.tgz", "fileCount": 26, "integrity": "sha512-pJkVsOvClKyXS23MIysPo1IZI99jrx+0De6LpYw31o+eR/dIGffce7RpwT+PQKbF3tsVrQHvjbxjbVrhaXz5DA==", "signatures": [{"sig": "MEUCIQCFAFo3oDxPQBNukcvC8MBd2yJMCmH94pFzkal3YszKSAIgPLyyrPcPHlaZV50uxR/iOdPg6mBDbG40EmV+ILCe+AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyUBmCRA9TVsSAnZWagAADtkP/i4Nt6qodGUci36XBrD1\nSkjr7gAvahwHJ9+PyC4j+Fr/R2NFjMogDRy1xL6IWF/fWUV/YnhIVfQT18N1\nOCvaHYlAA3rc2HLippxIGZ/WHW5MKNLCTzzbYg+Aao4+rU7GmWNM0flqe721\nSARxPJawONME1fIJpcPDZUuBFSs0sg39oMdauTIBwKq63CX/sR20BZHs8fZ1\nPt3MHSuQ6FiARt+MiTRIM8e/t+eT95rCzwERz7as2bw5oDN6zGu//YinwJXe\nrVCNw/EaEA0u4GeCexGRCctI+Zy9gEtrJrudnB4FbCGOFqj+SJPuZOpbaFEF\n7oAGNT3HYYyrVE5INAlysBkZY7PTeyN6J15PedpJdnKDmGP51Dl6t+qRhn2K\nzCXmepu29LegqZukl9PArWC6VP7+6d3dDWe4c0rzbN1GwPl4k/h0WKepiVaU\nXA3kSUkfUaRKjekNqp30ZfX8+qBlRdM5GwD6hzvi0q/n11rRwOa7q7AotSHE\nBi/0miyvRk8EE7hR56a0en2KhoroWzNyp5JTCDck3HLbBeGlSXeA+OIg02g5\npl/4rSFi8MZFvID09xCQKk2k+nvpVtqUO6d4PAMhtE6/qX+NYsD3Gm7vjJiq\ntQu8VhL3g9gUeKAyt64yQm0/fmKKvyE+5ZwUJMI0KdH5UZgDkBh7YdwtYvgk\nJ1wd\r\n=TQpR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "2da01d6ce579a382a22b6b8f12d94b66d7aaab63", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.13.0", "dependencies": {"convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^3.5.0", "tap": "^12.6.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "source-map": "^0.7.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.0.1_1556693094062_0.013771300346025583", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "v8-to-istanbul", "version": "3.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "d490670707c0cffbe09d3bd2e8a7e33841762f3a", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.1.0.tgz", "fileCount": 26, "integrity": "sha512-uAZvNZzXA+4LxwqcwmSYZjR4N3K02tKu20Ludp793o6zffa7oK2lnuyZK3Qxy2BA7b3Ffi6lV+oNrqOAJ+PBvQ==", "signatures": [{"sig": "MEQCIEpktGFSIgtSfXLdnOz4no0qHA5hZm4AKS2bgWQGbeyeAiBOzIuXulXDvqYZGyAGibrxQb2jc16MIQfDnqSGVNVzqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcynvHCRA9TVsSAnZWagAAYJsQAJBywFKot7ta+KR4Ykeb\nPiAVoab3IjwQ+9AUZRGMZHnA0CTohk6f127L7Nk1wkL5OcFwWdNJD1IYzupZ\nSQ+s/IOkV5jUcrdO1D/7nCN8M0wUaZFqf8aD8KR0XpaC2UlzFLl5918jXzGa\nxoCwB/pMMSsgJR0DbK7b/4cgcLyCov861UKMIyN6CqR6VomKK5l1/woJQux1\n7R4WbPnRCVmDGqLqTsP3vhj4eXTarSjfOarWSp/Dpmx0bkMWol/O983eKzNS\nycuNYZqxv1AeQo4/6jb2IvzD8An8hKB14ugy9CnB1YKm3LsMk2MObM2pJoJP\nx6U5ltaivHCJQJaiY+azVY9nZIUV9n5HRmz8GutV7hmoM8L45xa2Cw6Lg1qG\nxPWbjkMFRDwKVZYiC1Dq8MtTCWmVcZE7evC6xtOVuAaULcm3UgUdK3PKYfNQ\nyeRtKj47WOXWRqVzQD60P/9AOT+mTP75h6wHqdOkdle7HFQwT6XC8/5h2AMF\nDlY5fendupi8/tiedwNhE2JC6PfeRux99GKiUmnNs3jwS9iTKFx30FcL6ntS\nxg19qjBhbbrRbZR3KdsKTBtHE66rCJkdiLqGVJJz1cZJjMA1Sisk0FDpU+hD\n4P+D8XHLSWq7yyAVT65B44cqUM7n3qKro2BSStPM7Yg6JYLOVFhU9GHl4rbf\nZQdx\r\n=W2Gk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "50c9f189b5dbb918c097743d79815c919e55a3bf", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^4.0.0", "tap": "^13.1.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "source-map": "^0.7.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.1.0_1556773831038_0.7636260873526302", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "v8-to-istanbul", "version": "3.1.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "525f0d572348a864103a5011ed27c2b39ed0d993", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.1.1.tgz", "fileCount": 26, "integrity": "sha512-eHpQJtskRnviG5/OHIUmj9vrUqPDO5WY0MGAuILKSgL6f8x4AVHxpFxCmXFshHw5zhOOznP6VRtxPr+6lNoK+w==", "signatures": [{"sig": "MEUCIQDb/qur8WcqwbmaEpJNqD/xXnpJsv9f57iqW39n2qveVQIgN+zFNvgNAb9mc8PhEzN2PS0rWkXo9j0ssv3cv/PrDJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyoGiCRA9TVsSAnZWagAAqdEP/A4Nrdsb/w81q4dgToHG\ntF3xQJNs4GdUBqvhKO9VT0/KYvRrmLNl5lPlG7Rm9A0cl5uNigIsj0KaC99T\nCZWH0EndZ6l7tAjcjfs9zzCdZGvCMEtCuLIZveVnjqmew2wAr7Yq7NkXg5lH\n/9WAkGu1vsKRbtj39Kd17NCwiJg5A6xdxQTyr78vkjz/E/pWnvxOKyoVZ283\nOGY5U6bw/VU4hMjQuZof5d4V3cNowl5/0B+q46fk7FdHzfooTHcSwREREaxr\nFOnerBbIbTL3iM3qzr1chb90RIYc3kEyvIEC6dm04kNea692JxCDdjOeyUXh\nzJvzFqx0L9gW0S/x//SYQwd0IHn/zYBDfLsVbsAzztCj0eeI2F/oYl8qY6zN\nCKmJuO6c/OFY80d0IAb9dnSYUYyj46FmomF5+67583bP/xwRSaf0lDvxNf+U\na4h4UihqMdYLSf1YvzjJ39iUJckiHfw05FaV3YdcZRKw6vUQeSlevkxC6DPr\nh/BV9Pbo5LhY23UlYEgMWtZ1Qsna+qjIPVUC8oul4/RD5gguqypKfssZVunD\nkQIdkUfH2R/KckhscxmOVWxyoFw8q65ZNly7dxZ22XZxgs5mTqTPSAgFS2d1\nklE8mKd5v83J0UJXOhS2yZlYSM5mEyMugKHO2ogRAbUYLD5ze2C8JhG463vS\nYNQ8\r\n=WzAL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "7ce4bb19f61e1bd79a66a3ca302f3bb5a1d2edee", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^4.0.0", "tap": "^13.1.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "source-map": "^0.7.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.1.1_1556775329809_0.8148931101370487", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "v8-to-istanbul", "version": "3.1.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "20e9105167aaf1353152f8acc526f327f8a07ab8", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.1.2.tgz", "fileCount": 26, "integrity": "sha512-2ae2rql4/nCb+E9sRoXmoiDyJAeFhPZ6t5ZQpb/Kls/vff3dD0wjWEUadkunLT0AkflMxQAB1deiiysL7DeN/w==", "signatures": [{"sig": "MEQCIBC64DP0yRTYqxZyuBPb8LHlmpqMrsd0EYcVousnoIbRAiAnwQzjQR/cosfGEDJmg4ypNmQ0iCty8e1hbZAeK+UFig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyozMCRA9TVsSAnZWagAAXIQP/jIVG2w7qjNUDMriUgrr\np9p2Iqds0cgwaSBv99McjVAru2fWGInwKpBlvCFPfIXAdbzmjbMJgooatye9\nexCXQ7HCAIkHw59xgZEJkrCL5hKhAs0YpO5Pw+vCfmW3w3AcQ3Cp6U2zDgRa\nWctTDfdGUIp2iLvWEEWLljH3C5GyXgIWgk12WO70vZR/5h3h7oi1Zl72E2p+\n188fNbfxF0WDr9Vlhe21mAjhQIMuUJkUeG5zeRczfY+8a1vml/7kbkR/zKyc\nvtgQjMWnfSGZELoVcA34AUG7dBEknwaBQWudvZiOH0ycntFuFYZUB4ZejHL/\nN9ULvVPInz4Hb6Sr8Ci5MvJGe4Ccir7l+SzN7tpuUAhcMQHorkdmnTXJ0MWA\nh+oDMYxG69GojgrI8DIVkoIGZ/t0bgIK1gI0BVaNpRtl+4wmfIi3MPy9TkBf\nOPSRmssdTdXQYcXxNwVd1zYyUjrCoHdPBj/yal84uA7L8DEcZRasRwQv9MDa\n5ur+hcl4xsiMk+JdVD9yU6pC0egaIY8zoKQgcNnLikft9NiIcCqdTAYAoGnG\nkID+7A1Elx1uZwc3+MiSz81dHwW9sAme1LrCkJDFhu44yA+ndrXlX/0nI7My\nTNLR1UYuncaXLkELTq414D2hrpE/55TeuaBJQpvapQIXGVpBA8QfVAHUY4jq\ngVov\r\n=GwaF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "05d692838035c0f548d236a2fff3ff29c3840bc2", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^4.0.0", "tap": "^13.1.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "source-map": "^0.7.3", "standard-version": "^5.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.1.2_1556778187940_0.5718581625408201", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "v8-to-istanbul", "version": "3.1.3", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "6ad6bb22c84c815cf27b49c672698fbecd549889", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.1.3.tgz", "fileCount": 26, "integrity": "sha512-btJfK2L4PhLMSkD1yiYEAAaz7xEwAiCJsxJ+mg+zc2ghYWGHcg6V3bgKpQmdBr7EmmnckZE0zvminOVF1tXk2g==", "signatures": [{"sig": "MEYCIQCNmE5YM7a3FyONRSPSBjwEQvRsud9dwPUMe1PfI708FgIhAOcfmqgXQZtfbrpqyj8YDqfdSRv0zdh2ChuWBbE3unx0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1hXMCRA9TVsSAnZWagAAFBcP/1XgIo8utn9HKLn31Pez\npFg3/1jSgXiX8AUaKfqtFto4F217Lz48mxywUJ6CTMvI7zL8NUSP7fWpbYd+\nqlEr/1VqKBdojMNvhw0YyQj4camrHevQiAbNbt92V+uG2R4gYsd/As5gAQ6e\neG6MrkDe2e38TjoAk7Xw/9JksA+ERMu3gjKRYfZFanbr3Ec70yaNtnu5ytog\nR18KPrhIE5cqyEDUON9EJZ2tUNiqErxDxk2zH/XesTTr7i+udbz3N/H/7ww+\nWfKtDePirv1waGSUC25/+5SygqOFFogD35QO9MSxffl/U12z0TNwLRUkKThV\nNFchnd/BCXXZMlIuFZqVgraItyXlvZ2pwZOKR2GQQB6I7aa9rV2x5ShoK00k\nJYdV+lQom1pw4k8dj1GJwUnfFlEQ5JsWGw1drkmFDh6GU4W2mWmtOhLZsvsw\n9Ex2LY7JOd+As+ZIpXVXS4XsWPAoSyLTH9cK9iS3QsyL2CEZh+9jYDv+hQhe\nBrlbyCo2V3bfAIFS/CqwCpKyeRCsmv4DMatQZsbFhdRtOF39KEXGLENepgHm\naWFZ3ldijQwLx1AjnhQJ6Oy1alT55Bq8QEAeU774HKYZ0x1Z8RjDuKtlJW1A\nlg0Tsl53hdUXv654XoaBI7Jw0tz8NupgbQyDSczUHCMw3fPJEgpXKH6E/Q73\n3Jnq\r\n=7MfV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10.10.0"}, "gitHead": "4e926ba71682e49ea357026c36d9a3bf04331714", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^4.0.0", "tap": "^13.1.2", "should": "^13.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "standard-version": "^6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.1.3_1557534155739_0.8517016060774281", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "v8-to-istanbul", "version": "3.2.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "3d5e746957f2d34e8a3314023eb5fb5a5b5cc6fd", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.0.tgz", "fileCount": 29, "integrity": "sha512-5uCtDHPOhP5SUiKIA9wYWQroAw6l0PBYIVns0QqG6NybVfUl793/PVXqNQV77HHWF3BD+G36pe8Z++WOm3Rfdg==", "signatures": [{"sig": "MEUCIQDSLZecV7VBhRHO+/Nmr5H67qiolo1adTsq+TOzWu9hZgIgUOvVD5VicOACbZ6HQ7QbCJinFMQt0dvQGJLHyjYCc5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD9wOCRA9TVsSAnZWagAABdMP/RIi9Rx3PVeZtOFR+lZQ\nm3BA9F4JG2rELqtOMXHK/nj6pb5DV5EHd6dAaJ2lyiOUqjwJNGB+f4vQunks\nDz9p+5x2/lyZkyBVpSGSumpMmDGD0Fdxp6JK76859kYRxK4AosUy1plvf6HE\nSXOTwVxoRN8htEVzO9fZyqFhEuYnKHhSI2svR4zi1juqhei7ODlAGkS0//n6\ng5uO5NThkFAPs4MS4hvZ+P3COvytVNRYWBkVya0i+e60ZxTRJoF1U5HSVee+\n/xon52/KqZfy9mjR27J8iIv/iARgqk5omyEqnsQwWNC+1Wc4wLFbUVoLtR3R\n6Y72hVjofZXp8AaybnT+7IL5sPnB4/9c4QBBmpBmwt8YQY3dA8RE8r9BbXBK\nAjR69PS4NoxuA+YUhEwU3lPrHNZ1mFSokQ/7xeistxbD1ELfFetNJR1YWVcv\n/2STa3ykWzrJyuEnoFQiuEblJkaer6s/aEAxN494vtoEvhCF9YBuIxOd4wOD\nokAnbnwT0u06FU2cwRLB0voiIz8Wk4KjRyphc1j+zpyOm5TOAxaryC3T1QLn\nd960vFX0yXqPvWFb8Ym+eWrPNCAlJslQGqKtPqHXBt7OxzNEj5qpzZKJOzVo\npkRNcBopLAQNdzddkC92MwQWzJkSSh9QOZM9DYc2uPoOlmzaWRdEkfPCxgei\nTAyb\r\n=xMZf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/v8-to-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/v8-to-istanbul?branch=master)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.10.0"}, "gitHead": "e952acc30c8b0e5c35cc2a79b9dded470a06181f", "scripts": {"test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "5.0.1", "tap": "14.2.5", "should": "13.2.3", "standard": "12.0.1", "coveralls": "3.0.4", "@types/node": "12.0.10", "standard-version": "6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.0_1561320462036_0.5030313431319937", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "v8-to-istanbul", "version": "3.2.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "e920838426c3087c2a10260ec26bacfc7eb0112c", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.1.tgz", "fileCount": 29, "integrity": "sha512-WrO31KXjsCmWP44UQmUx1n8RxgCfGzfe1Ztj0ozX5/r4oLRDsTcMlh+OILkQja7yNLOJNDTqnfJE8jyqJDyzEw==", "signatures": [{"sig": "MEUCIB2LGytKnau74srpvBjSVQ0o8EDt/fdeHXrV1aOwmUiDAiEAgAnuP0STQde3bTHvUxZ7HvV+X1nliLL8zxPpG2yt3Q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD+tGCRA9TVsSAnZWagAA3CoP/RcmklOWff51DlGVg1XO\nC5ROLJQ29lw94KNmG0XuJyoS/LL4IKQlzNyBKc45kgQIkJaH7DvTx1AAfYCT\nIUGBbJ1hBqku1cqIXh/HPLD/b6fXfkLrCzeW9H6qLVEEfxXcfLiM7dqbodAS\n9l7hkNDx4N3Z8rPeEjHO1BATuDBUUs+t7Hs860ybqGUG+MtLCYKyyIkHrTii\nNhFC2jkD1geDCfHtI8dtZJwEwcUNeHAplgJ6XjVGq6+wnjgHMU/I/+Cjd9cE\nNGGmhOzbLzNw6iDBDHvr3EQn3hTIh9T9TVnIzV7v8/rL9hUbyl6Q8hw5botq\nWNsqkmPDI3UeFvRUqKREdeXxdwKuDageoEcR5aMVHw3SldcRJgGxygCOrCL5\nlntt2g9pfCt/Q3BoUNGJZ+jlvhv1+dHpTA82rmTqvDHKu5fDG3owVdBeNNdh\necie8CnEkfLTf89uROZIh+bku63y60qyQbj1jnR5c+xQUPVrPIDBiwjEtllM\nhxGdTyQz2Hu922JOQE+VujflK8ENdIKv6gb1fZKwdjPqqmo5mcj5YIVIu9hv\nKjuu3TuAygX0ubU3RAx7zubk/mPLIFTE+7baWmjjBa9HVcFWCG1GaHB+30mp\nUSi/MDTRpqM09ddr3odvJohOTFOZjdSvav8kdvnwyr7EoV/iBrDySQad+/9R\n4S3+\r\n=M9xh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/v8-to-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/v8-to-istanbul?branch=master)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.10.0"}, "gitHead": "2b39d58be1f487b864a132df7dee31008970a84a", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "5.0.1", "tap": "14.2.5", "should": "13.2.3", "standard": "12.0.1", "coveralls": "3.0.4", "@types/node": "12.0.10", "standard-version": "6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.1_1561324357984_0.5965469542131492", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "v8-to-istanbul", "version": "3.2.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "0dcbfe928d738868c7b36bf73248152d62e4bd38", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.2.tgz", "fileCount": 29, "integrity": "sha512-R3NhjMpoTSL34OnHgkdeiWgYsKb8+hKLmGRBc1j7adslGUN6FMQCSPRARCRFHAkjaT3urTBcq5hW05PksFpBCQ==", "signatures": [{"sig": "MEYCIQDeUmtYVOe2WJzY/wDhizgMTkb6FLtdrlcqLci2RIcr4gIhAMPJnRq0l3Oo0dTHp5oLPcyiBCBWK7qFt7WnoHRG06AA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdECshCRA9TVsSAnZWagAAwr4P/0NFRK/xLXlFb7Vosobg\nnDroUAXaU2qL3yaMEg8aqKeJOa6pYRzlq4/XgEVHVKqikTqcv0oS7Bi7+FmU\nA8RXG75Gtpuu4gUxLmt5TNMe4cGf8hq9h6HCqK3vhOAP6CkCOVyZUSyJFB+9\nip4gcJ56jhz2wrjuhkC32/t1DthGS/aC4W6hhsChdMZbRxbSlrVS/PWU1MVT\nUgAQ2TWULi2OOagZQyQTL5+3u/AFob17REn5xvGWN18ranxrXjg3yOd1+Z8r\ngdAr3Y1u9URyRSQa/MatsmFnEo+bhejc+JW5OtE0YBRuCAp3QZ2ESukkh+9x\n+9YZ0XZe4ueeCQ9i8SDHx1M4yqYrXhaYA8OZdpMZrcE2v4gTxLLmEamQY0Vm\nDdkM6MamQwr794Hf5fX2eYGBIG0uC72i4nMGXJueUODzSSFxqY6rEwTZ9GTN\n2hVzJJ0m4CEZiUY+3Q5YYouUtEC/o2aN52WnRAkQ/1DTTEPBL//8EOj+4hCy\nPedJNNxbQmVWtlg1CB77hDu9cNKzpC1u1w5wTgDlz7axn64B2aIbKYZeOdLs\n8yvlQMSKTNWC8Ca0JBYZFqqMzKF+FB4uVAcT80eRSzIhF4RxgXv75BbxDxIl\ng2ozXvBedgaiDag52QwLEd5Kb3XHjp6dHSdnfvAIKMNik6JHou8PydKdd2zV\nb6ZH\r\n=aj2Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "e507d8088067e984bcf86339d213bc9fb91c7712", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "5.0.1", "tap": "14.2.5", "should": "13.2.3", "standard": "12.0.1", "coveralls": "3.0.4", "@types/node": "12.0.10", "standard-version": "6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.2_1561340705101_0.5962004816581101", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "v8-to-istanbul", "version": "3.2.3", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "5978ca1120ebaf868738fd51484c15795db2f71c", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.3.tgz", "fileCount": 29, "integrity": "sha512-B8d/oxMtc/x0TYXr9b+Ywu5KexA/on4QMQ9M1kTYnoGZzKdo8LLk9ySlWePdDOtr2G0/2Injgcp3sOR9gU+3vQ==", "signatures": [{"sig": "MEYCIQDUuHUTI3NcdMZk980czefsSUCyi63NFYMfTV6F1Z3fkgIhAKM7xt95CMFstUDsitynZntcRfb6lW9+XFdA/C9ZIPZK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEC9wCRA9TVsSAnZWagAAjHQP/jZkYlnRkvnpFRQ907h5\n724yOcikXVt+ia1HU23gV9RTRNxTe56vL0NkiLbbs9U0hjmAYwy18OG0TZyC\n7mVhMPX6UoaXHlDSaEynYGI5qEPl4JtzPu3zCgIhS/mU7qFR14zsd41vi2He\nb/0roJ7HXqf2qHe6ck+eePEVMIhBLsibPU6FLtG+XkoMuvp3aH/6PMbBGuVR\nidAg44GLdQk8P5PUUwZv62Tg5pKsi9LmrG4qQH11aoQefWv50qi2tYjEP5IW\n6UJMWowiQLI4sZ/6bD8CJePOlzG3bdSxWu6ash156SHL91ZCWtUZami0xJjL\naEK1tOeT6r+Wu+07ODe57OSWBF4DOWKdtESgO+/HAu9bFtgulBqVhVFNqwln\nWrKqOBO3TjN5ECjH51XVxBEmjvkHEGErmrbwoSsI5WjBqgMEqbsGhs3EFM5m\ni1k9i3juUgLMBOOWdadYYQ1XqSatgTSGDwUGvH3vSST8i2GK/e8sQW+Wc46S\nI6+gprUufmYFfI8LcUhN3CdKuNEQrL9yg0Krok4hprualVxW586SLRyXVa16\nwjf6NajD2hr6ZCU7otumVd4Jkswy0fCz7WjZKmVTjPWzoV9F6bw/FS1CHtzT\nIJTU1icZfCBdVp1nhOzydCa8TjE8ibuR1oHJEsOcZagrgvTxrdxSCvqgBnSP\niHx5\r\n=TrhG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "c2ddd1460b0a3318ddaf8d2a81eeb5bb4ba57a55", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "5.0.1", "tap": "14.2.5", "should": "13.2.3", "standard": "12.0.1", "coveralls": "3.0.4", "@types/node": "12.0.10", "standard-version": "6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.3_1561341807525_0.24251135460616258", "host": "s3://npm-registry-packages"}}, "3.2.4": {"name": "v8-to-istanbul", "version": "3.2.4", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "dc157d8579497291fdaca386ecd1c984294af15a", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.4.tgz", "fileCount": 29, "integrity": "sha512-yA+3cfeZRaFrvx7XyntfAQ9JtFTcZaCDiv5WfMJUDcQ5QbMLLhppUAKRYRSytcTg8cskzn+63qJ4EFoDcWUYUA==", "signatures": [{"sig": "MEYCIQD1kYhvK298b0J84frOlc5yPnWr/WuixOVhNwNR3mcKxgIhAKHz4wROLTpX6DUlqmHh29pPepprsm3+5ddCCc/KctXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmW9MCRA9TVsSAnZWagAALq8QAJ34tsfXxj9gBKNhLtXJ\n1xTi4orFmdr0Nz1la04usFpxb8HZZ50W+wxlUPhSv2dSvpZv5V0+cvn3am5l\ns/dLH08aO5pGQhCfabH0G8nh4c/StoRCjwNAWlax2LrjxfYE6Uud1Jt+9XWt\nKgTMBuDYhukEQrT/KLz2piD3b7r4YI8Z7s1yiPVexeD5jIRZ88v7+o18YFTm\nG0p2/FsbdWi5pXC50wXWQ8uzHyxDDMB4RGo0igDo9tdvtMzyat+iorVg5Yas\nzcgCJwdhwN0IOKYSvO8shWaj7nfDa8zsHiHUVRwWsLaKCUWADhZL56NJntkq\nZUqnRv1Bal0LNzAQtXo8+aV/utma5C7CcrrGfkLT2eGhSnAZqkO7kFUZeika\nArt1YqM74nEKNAW8/vbPH5s+dgJfY/gyU7yOLhObC6siHbZQZu98pEe+UfwK\nOb3npMiluArcL44fCbFa9CE+XzILR4am1aVhX/nvg0m5wQ3DHWoAnnD3tW8W\nZ+vPfcjHaILmb6hQ7yxx6SQX7fRdngfbxQF3fPOxMCejIdGTOdanYVq9Hiac\nVX5e33vnMtuYsHPX8p5p3Q68HvT4PSxDR9Gqf2phBD68d4vaLBosuVbYq0Jz\nvjYgBwIm8AeL2bq5cADbsLH2QtWb42s1cY9Zi44M+aQX2tZRmkHPxYBeM0N2\np8X2\r\n=LjN4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "3715f725bfc3544edf28e547401fe87e9f6b45cd", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.11.3", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^5.0.4", "tap": "^14.6.9", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.6", "@types/node": "^12.7.11", "standard-version": "7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.4_1570336587277_0.9995719904942832", "host": "s3://npm-registry-packages"}}, "3.2.5": {"name": "v8-to-istanbul", "version": "3.2.5", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "43a4d1905dfb25bf705b9f197d43eae4aa9b3859", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.5.tgz", "fileCount": 29, "integrity": "sha512-l135Njhu4nELb12HTowzFqrtkRIk6JZ7A/469pTXsCwSrUGsNeTtgkjP5pVgVamVayJ7x/sxm7UpIxGAOMmi+A==", "signatures": [{"sig": "MEQCIEMdfMnm8wYFgzJGCfDciWcufORdLyw3I6kUttkv6CTIAiAviXbHvyamB2XqC91TiY7YR+FPOUEwKokU47imzr+EJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm3tICRA9TVsSAnZWagAAzs0P/AvyA9q04VKnJjOZzsiM\njqtlpgB/KvaPrg0JT12OZkta09tZzvYj0/gjK/YCo+GhcDNkjIVBr9A6jdLj\ntjnWs0UsGdtcojQANc5tLjUJlL01U6ZOzS+I2D9B1l+Zt64rie8HBWQPD0lA\nukZGgI2PI0xKa0L9mHsr22o6H77SgBimFKXds9I4MW/zAXO9mcxg4t+G9lW9\nR3DIACJQJTYIXkLypLrvuX6QN9HzLUvPBXsiSifQvDyCkZOsFZKi/QuHylzX\n5OCnsKABCiCkZT7XpYM9D8UylJqPA5Jm0CaXtq7AGtBxK3GrL8Q14uaWY9fH\n0hKRwoVh6usNTR2dRc+zFtdNqAHO3mka7ufoDaaFifoz+fRlHhfTcAtswuKS\nr9lJGOBLBB+kNUF4CRdOQ0JqxlFS3tu0+Kgn8FCOY/+E7G7E4l/Moh/aYe60\ngWELeA4rlPjJddDLRBZ7ZPVNmTcg27Y+m9RYXs7o/IvYhKSLhJYMtfI1wqju\nN/uSU5XvG0K0nJX25EDo626gt1KAfyjqQ7qglnOdQVneYqDTzyOAiIj5HRTT\nF9TtJpy1hS2QL4N54z38aVSEbWHflTM5tskUOVwOnsNsiVDj+JzwO1vjvBvo\ncra1sRjdyo9FUda5zPjZdmuaw/szkYuWQgrBMwP7xbKs8k/+qfQ2GBM+urYx\nGTue\r\n=DScl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "d6bd85ae2fb905a259217bf4bf9b0a78576a3681", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.10.2", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^5.0.4", "tap": "^14.6.9", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.6", "@types/node": "^12.7.11", "standard-version": "7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.5_1570470727991_0.4568018901994002", "host": "s3://npm-registry-packages"}}, "3.2.6": {"name": "v8-to-istanbul", "version": "3.2.6", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@3.2.6", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "42333fe6b2a9849b108ff781af6faa1a61390eac", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-3.2.6.tgz", "fileCount": 29, "integrity": "sha512-M6zzkVjsr+6sFdWPCuq7fjg9oCOXlssin05Yhobt9jMqHlEhw8AQ4/ClDiLCVWzXjpS2ezik53mhgSivw0XwmQ==", "signatures": [{"sig": "MEUCICeiKBJ0y7PdHUV8hh4E7JFLlMWDKfIu/V4PIYovdqQcAiEAtTz634PclA3fhYBZtVFzLnYcXIFo5fGqLw39mvxuzo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsO1XCRA9TVsSAnZWagAAxB8P/iu1n8IqRSJlNOfGfn/3\nFVPiD8Gmnd/MQyrhpcERz3m/C7W8mzMPLhIwFDWs6IM5eAqU6zpiZAivO1Ib\nCnO7wi28rtM5Ay0uKEY1nuo/wBQMJU9g8OJNsF7vYrq/5TTEavsVK4UnZh1q\nT/RS+lknwU99EhAC8mkldqQHhL3lW4Xo0bsqLvaZJhA05eF/vfmPSyhbFdJ2\n93kFSqypwVzQZA01iYFxwEGVTzkDE4Z6EzidrHfh7C9iO4PrZlrUJ4sVLkjH\nptXIjTTcs6F98L8HWmwJ/+nOxDNH22PCcWEVbFyhY1OkJ3zVJPC/0FjUuL6O\nI4Ga5w8vxHOIWre32pKph9hBXeRa/9az52OMXlsJxlAoIy2Aa5K0fRJpj9Ma\ngKELiJPdDcn+NWZ1ihRpimdM6PWHUkP2ys94PIwofcAHSqypv+jRoVF27HVz\n/wS/DfObw2ezLUXHaZ5j9Yre5JnsEGGdowS4NlimzvTSsDUR2mmfM3DOdF5O\nzJeTauY0SVC2yczgjyANN2mLNJ+VwcchQ+GMch694j/NyyLLT7vWNlPSwC7e\ndsQ6vEO9C5czxHX6p4w1GIwY28K7J2tWYHIN8M996G9YRI4zWUFLreqUKRGB\nhNfKsLg4VT6delpaXStBhaUJccs1PXS49mMP/jUDErK6EyIOUcqtxLJPikKA\n5+J4\r\n=zYnG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "b038784897ffc08af4b1f3618e6c35b3832f3bdc", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.12.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "13.0.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^5.0.4", "tap": "^14.6.9", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.7", "@types/node": "^12.7.11", "standard-version": "7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_3.2.6_1571876183191_0.5850926341152383", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "v8-to-istanbul", "version": "4.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "6d420f519e82a5f9a74f2b7275ba9263e19ab80e", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-UqOveasq5AAXnhBo7Wg+PQUkNvCEOoocECuR6DA9CxfHEcCz24vnjH0lgeFGLVpGk12XEy8xup/MFBjxT6POrQ==", "signatures": [{"sig": "MEUCIQD6rl9Z//vOAyL2aAOoVwqk8J9+qCRFdMgvGJ5vCdVL8wIgDi0MwpAtheIigBor/VzfLUCmeCdvRHrLGR1KGlfTd5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2ItgCRA9TVsSAnZWagAA0KYQAIF+TEArp6QwvGjjDNG+\n3XRwyiOrplTDkKSEFD4P93+ywHnR6ZNw5/aDCqnF6WWQG4hzlK3U+zgTId/I\nAYQ/bKD2Rvg4lcUcJh3SdY/RCouzNQRt//Hw0ze9EUIanR9Vb5okIToitqaY\nXMCn1fa6xaOfd6LuuF+B+mM/cnbPK2U2laW0l12e1GqPkJ//EPwVGjZ3UifR\ns604Foq5YvaqBV77KXqAsdPeqVCRm3eXrGOLmj5xeHs3UAtsCgCuteUhmLXz\nXG8+z+VdcyPKRvXMZWLgVEngjtTMMdVnGgnDlaB1/G8JINkOmz5htnH6E93o\nKdHjFZqK2K5bwNLgfGAqLc+qWFZfV0NichKKbVP3U7GZSJPmbBjrGwbuNgkU\nJbTqhEoHMHe0nWYQwT0JH1kNpvB4TMdM9baJPMljuytPkgBunCzsV0HIw7cB\nd7TJRcmw+yszM9GyFbHER5KsU3wROHXv4LP8DGENTqM0Yznr1ujk8yyGYpUg\nKBVTJAGZHQgWWY1VdbMhTAwFYUH2MKvGVZSa0kdZf3eRowaF6IZHmRRm5hna\ntRNW2S0MjIoKqV4dIaEUOQnCcUdMzYKaTt7x+16+Z+qQAMxecy2uq7ZVZT3t\ngxRf63ychSyBdmfQXIlYPJ7kW1hGPPxR9joMWOnOUaqti5gWZ9HN0GBTCg+X\nuBx+\r\n=su6a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "fa5f077e1443b48b183e6bdf255b61fccb147c46", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^6.0.1", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.8", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.0.0_1574472543890_0.5277873466864653", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "v8-to-istanbul", "version": "4.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/bcoe/v8-to-istanbul/issues"}, "dist": {"shasum": "d6a2a3823b8ff49bdf2167ff2a45d82dff81d02f", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.0.1.tgz", "fileCount": 11, "integrity": "sha512-x0yZvZAkjJwdD3fPiJzYP37aod0ati4LlmD2RmpKjqewjKAov/u/ytZ8ViIZb07cN4cePKzl9ijiUi7C1LQ8hQ==", "signatures": [{"sig": "MEYCIQCrGF6z53nhrklpbxaiZgL1UkxR57t7GVfg9ebu+K2f2AIhAN+6tPULI8l1EPSqks93Te7zDgau5820OokGerzIUPCK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd96hKCRA9TVsSAnZWagAAmmsP/j7jz+ouDYq+05KaWz4c\nYtpVa4xYNJzIT+PGbz60TnEN/h3jXP0bAYDPVjLQjD5PKbAVe3dmsH8r60KZ\nUj5ZKY0PH4QTZ/pujYUb1VKdLoyq8w92JQ9PhLCbSaIo29OeTzKLMuGcCAb2\nNmbHfMvVQlo7fM5qcVe9X4NCCSNc4uGOKBUaZDBjc9s0yFWZA8FAB7XeSSqG\ncwQGqmXXcqKlu167xS9YZADQCiQYekKUb+6EVVpw5My0qS7KJpLAvI20J+t2\n9MoHVu/BKX476WDtVvOoywDrQmoZgUaZCFojL/DL1qLMUyLSNjP8jwJ2U1va\nAnVzQ4ar8t+M7SIAWe529mihBp+SO0Sg5BTKf6Sea81NLkipijuPX9OlVEug\nal/3bBFsDlBM+YkEFk8tgBvTxEtq5Wo3Q11HDxkEQZBBoFUemI4K69u8rF7L\niDfA8Vt/RGVdADmGPArZ+gAgJ2Xx3Jc2aZnk0DNo/B7rLPBKfGiX253s7Efr\nML0fARSbcaV9kP1bCoz/0ctyFu3qYAMc3fst3+1DmB9XZujNM52WwwDl8Ilz\n08aehz5rwbaPX8zro5t/focvz42XD7GJwN9eMHNd3IgPiVMi46GSifWORuRd\nKm8Oe5Xp9uLtXm7ijs8cdcItOaivyTkXlCdAypDYsquNMqAEdUed2IVmeEvD\nIxkT\r\n=280f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "gitHead": "897062d69fc880e50115eb1d4211594efa0b58d8", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/bcoe/v8-to-istanbul.git"}, "_npmVersion": "6.11.3", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^6.0.1", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.9", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.0.1_1576511562088_0.7720180080469556", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "v8-to-istanbul", "version": "4.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "9473d836b0cba708ae8daea9f8ce44512e2b4a2b", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.1.0.tgz", "fileCount": 11, "integrity": "sha512-4aRtmKCJec1VEo6a0wF3NGwiyS1W4wczJsS4ZAKitBwxZbj9chBhHqBm+RFgcwDSEarilX2kVs1YgGYrP5kWOw==", "signatures": [{"sig": "MEUCIQC4zxEFI8bPXN+xISA5hmEv9DXYWoMF8a06xtBhL+wKMwIgQlddYxDf0aGtZYO046fHQfk1XCQOFIYl6hWnqPI9B5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePctACRA9TVsSAnZWagAAncEQAIO53c/DADt8Jz6uGkCx\n6XVomz18IFT/pRbQw3XCY8xTMFdGbVxhFPBzCyV2qyTb41EbhTmk/q2y/8vc\n9+ne1vw9LRtGdH1noPZFTeS89mY7szPWcjWWbaujBBKePrKfz+sGbNqCLCXc\nSPCVUTW0tUZZ4TQ7AMLCSulemX6FE1ICu7bI34qKwkOi3HMrQAhAYQePIcet\nvH6/hV1sN28BKgndfHs5mQy0WbDDH0CKTTwtpJ/lnp5UqoyXph+WHAB/0hXq\nSt8TCMs3siwLG2LShveRrn9khScjCkIoefmARqs3dn9LPSozLgBHYHw+oeVw\nmJ7TzRe0fqtvWpUqznpzvh7zKX+vo60HxIbfYM8bATs/unOVvxgoRJsJY37q\nNio74qjhuOQunSyDKcergAleHUGXXpRkcNRumHxrllxIM6yJZ9tMWKzl2kIA\ntM5/uIg+HjghicxC49+bP5aeUqtGw1y827vVJwRnweeRj83uRZXtrwvisykv\nF3ndcOQHfa7PF+uir8gu/uNXsjz9qDhmRyEd6q9LvYunqpADRkOfLKqFFjub\nr2RUw5xXMkZNQbdcZhLQIQG095W5SFK56F31iVyT8Jie5NtmeZ/yWycWfLFj\nNbBbpUwshAVq1dV2nKgnMisbqq5B2Y7PEMYUQ6CQV2Sphku9NpIvV//h+NNm\ngBds\r\n=97pu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "gitHead": "b275a2947152a0bdaff646f196988480898e8699", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+ssh://**************/istanbuljs/v8-to-istanbul.git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.0", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.9", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.1.0_1581108031533_0.8688120097982506", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "v8-to-istanbul", "version": "4.1.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "cc2a6737bb6ee2941ceed38027d7b93083dc9a3a", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.1.1.tgz", "fileCount": 11, "integrity": "sha512-eDRcudafj/GAV2MSoDNmsV/deeIY9bJ94QuVOzZDEbBd4uX+5v/kODX+p/fqZ74/VYLK1BZv8BPJlNnCV8vDhw==", "signatures": [{"sig": "MEQCIDsKC1a47/NZsdC9YhZwudB5dKpgWSDkQNu6lFBxuo/kAiBdp07p99wNAAL/IA+kvrFJFlXTD9vpvl1W7McHYFbb+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePc/8CRA9TVsSAnZWagAAr+IP/R/Yci2L8ULJW/o5MU8m\nOliIG1QvWNwTG2jXZP2CVcMv4/kkvdrHppCv15+epD2xFWaf7rknROPKZpe7\nwLccY3ZAexMIT08XzH1ZLP8l3ih92ZmPfaaBF96CKR0epoQuQxlPYkPH1ho/\n0CjnPP457CrZ/ZbhRM+EPEfHfEzZf6qdHo5wFwW423vwnyeBMC5eIUnffzO0\nnPhN1Y7yBdSJX0n1+UMtnec0TBbUDFA1SPXhrvghA6AqIjce3NrdDeUN7j4B\nM1JbwLezZgKWeN4oMN3i9rHS0tOCQK+Liae2k9dXj34IQiL/HmXwSkaGTsTV\nugGr6iyDc+1/y1RiU8b3f0++q6zrwEg0gvHK3TWqSc5oSZSue7W9xfJnYzCC\nWQhqTSKSyN4sT31fmVPu0AMCkc2jV3ofbOS/Z4GXmtAj7gIaW+pZZMAN3Itn\njmys4K3nmP+e1NsOewZz1/OZZSaUUB4uUkAIURQaoDRcypb3Dsmj05bTjhem\nZ989I4FSXsdLIxkGII9/UrXkjSBY/DWiZeV09PoeO6dNyWTY0mNVwAK73WRr\nBOsFxD7jBXn8Z+1GbcdBi+AmIbkRhgd3Gk9Eo5v6poJNq6gD8m2Hrgm7sjiK\n5v/yvGX7Fr+zUWl9mSQCP2BfwiF8/IDNHah00Rk1i8ueTwP9f2oMpJdaIwYy\n9ol8\r\n=tOW0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "gitHead": "374767f199eb814ef43e610b2f2ae28d6351901d", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.0", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.9", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.1.1_1581109244029_0.27355113828036726", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "v8-to-istanbul", "version": "4.1.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "387d173be5383dbec209d21af033dcb892e3ac82", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.1.2.tgz", "fileCount": 11, "integrity": "sha512-G9R+Hpw0ITAmPSr47lSlc5A1uekSYzXxTMlFxso2xoffwo4jQnzbv1p9yXIinO8UMZKfAFewaCHwWvnH4Jb4Ug==", "signatures": [{"sig": "MEQCIEF6m+msEqIndOHK+XTTp0Lp1gRKgTX1m4asDuaRYSJ1AiA+jqo4aXRwYDOSew9HcNq/Z06RapzEP606wY1EB3H1kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeP4/yCRA9TVsSAnZWagAAxwMQAIV2w/mmpaZcinzaw7KS\nnx2RD/07zktb1HcuJPNJRu97xNlFe3uvymCGStgD1iMfaeUIFsHo+l77ZZcI\ngLbeRhHUVyPqTmeyNIhVkSn1Y4uGONbcFXYdR7PuQJB22mCtX6YvJFujU8Li\nJYHIZ/fb/Lr4qDqr4RbvqIGvuxDoUrjVwRHKRQ56p74WQRG3BLtgdC6xrJNI\nbrInd2RFH9A/lgtBY2QoHB15zYQwjGF0Rd0vymWy3i6/kpOTFYkqqlFjdggZ\n7h9aaoLMCiP4cnn5rp/N5JCfBcywbEzorFUEJLrKEecTvTP+f3F16kr8m65n\n9N6k5mBw8F4jo3T0JFXxw0Zmig9u4+QMYmXuT05v6QQcZ/ZKXz1EOATeTHoM\nWl1w1D6glhWGS96GdxW5q4Dx5PU2bKEX00/ihQLGwi9WiKT6Co/LRNcosu3f\nXCaK1OgQt/CI1m4rKeCXoM2jc/ALMiPZeVedlrzzYK0uhI6sBnZTjhBD/6Wl\n1wEcHt1sIfpIFGaw+9E48BaNA/vBJ7+RnOMwqTYrEVv5JubK0kiyiWk/JAvy\n+1hUQvF6V9Df7Ps6TxzQgz0JnEPbqWzqb+rHOie6XnLYHaJQN9iSq2ecXuMO\nECDAtX3PiiI+KwXiY2VHDNwVQ8qyx2oIhEWOnN52dKABLdAHVpI5sNU7qGl4\n2LxY\r\n=74dC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.16.2", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.0", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.9", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.1.2_1581223922319_0.10426435196992734", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "v8-to-istanbul", "version": "4.1.3", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "22fe35709a64955f49a08a7c7c959f6520ad6f20", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.1.3.tgz", "fileCount": 11, "integrity": "sha512-sAjOC+Kki6aJVbUOXJbcR0MnbfjvBzwKZazEJymA2IX49uoOdEdk+4fBq5cXgYgiyKtAyrrJNtBZdOeDIF+Fng==", "signatures": [{"sig": "MEUCIE6vEOlAEZrxa/Xo9QCNM/YT12hZsKJ9MI63Gz4OrWOiAiEA8pRK8kpWPhcypVGnd4fGdMBO28f3Yqocugg0AddGUVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefnbdCRA9TVsSAnZWagAAANIP+gMJbRC8kAu7chLVnv0j\nd2ad4egWUxOfsIX2dTw96ctyQoc7jrhKpfAvxMDaJEgskwq4fIfUHjgljTDu\nKMY43huDiz73wlSJTcJK+HWHA3dwS+bMyLzGpu5UUbNcQjSHFNeQu122UazZ\nSpHDbcOx0+awRflFbokSY3uPe6e72xgIIkblz1KNXTji2wEG3IVZ09uN6Z8m\nr+RkESfxa6YhaQHuRNn2OKFlMcK0nDK5sUKHCKQyZbyKoy1TCx/EQ1ZKxglN\nvDw8ZbHX4PZ9YV/L0AJ5I/NFsGev+3qQm8cKCVflu+ekmKKCqloNhggIsb2+\nUWoSoffRBl/1NeNhRIwXaipc0roWSL3XuMGqDww/ipAet1qtqJK62wYCo7IJ\nzBrUTOFM70LnrORXY/1WBSqiI6f+u/UKAf9aacIj/1xKJvJVKwxFs8KtnGwF\n1dta1u2wimUtByLfmJv6PxWmXWWfoMuPun/B6l9n+YZJm8PYFOvIl/mO4oPs\nqrAe2L06HNrX/EqAJwGf2Oaa6tQyvTXcmCE0zv9+30WvsCa0gN5QVLG8bwki\nKRtBRJPydwCJEar64FvFo3i/eI3J+JTipQyExhz2cf4eD5Um0vRR1MMIQXqq\nkM3+l37hzdqjU16IhP47d/F+/5OazLURoqUOFVuP7PGkUESEb3ryw9iEYb3c\ngrqy\r\n=sWqQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.0", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.0.11", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.1.3_1585346269076_0.9915633275487963", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "v8-to-istanbul", "version": "4.1.4", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@4.1.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "b97936f21c0e2d9996d4985e5c5156e9d4e49cd6", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-4.1.4.tgz", "fileCount": 11, "integrity": "sha512-Rw6vJHj1mbdK8edjR7+zuJrpDtKIgNdAvTSAcpYfgMIw+u2dPDntD3dgN4XQFLU2/fvFQdzj+EeSGfd/jnY5fQ==", "signatures": [{"sig": "MEQCIB5tX4cxMzXBmI6k0V0GtKwz/uWFDbEf4r0NBr164P6uAiA1jvnejaLBQfDRT3mETxJZVQn5u+28krGdU+lEr7M5Eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJes0ZZCRA9TVsSAnZWagAAFv8P/09s8LY2WCRrU/jii6PD\n/iW6YYKQZIbEg8I68aAdCJOqmElWdsesYljWRnan7mQLMXez4d66a24AOCaF\nnEDAKqzahF9KDRbXX9xsgnUVYXf0+4lB4DcvXyBlYafWROgCQhbQ0M2NwxHB\n+87ruvp2IB5mHNsfl+8UfxmUVz4/F6w86o/RsgBILKKJUfYm1LpDziHAWQMJ\nQfch0pqVujrySLwNzjr9k2JahCG2INq/IAtcmND5aI+fbXvH06I9WuHdXxI4\nHD8Iwn7zfb5O07rz9YV0/iJJK0/Uw9isgKJxM1WSmHeeSp+Uk2VbIJCuUAmQ\n0C7+M54QNi2D4LPC3z58w5hYv2rfBWKopRUi9a+9xiOMy6urqdyn1JS48Jlt\nZKyhAyLVmHVj4fLvYIdASOcUH2u/p/d7uLUzLk1BbNqXmel/Oi7ZEkD0Ejqq\nPP8hBRjEW2FR5/pwgCRFTX67YGn0i/qHNRx+mVCk2fRPmnhhlwyL+/jYgZdb\nDRNM6YpFmkcY8vVHx4nV9r3EpiPeqGo5FisvUbuYq+xMNijrhR49hY2R500P\nwI0/YVO4rJ5HPuBqaM16Ia6U6wPSpY6UD/XnhyMrl5dm9FzdJrAROk+ReS8W\nUaB/5hGefPlY/cFVwm5WjOm8SvVyIEkeFidcHqt8ilwFObkfGcTQe/yu6/xM\n9Tc/\r\n=nv+D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": "8.x.x || >=10.10.0"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.0", "tap": "^14.9.2", "should": "13.2.3", "standard": "^14.3.1", "coveralls": "3.1.0", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_4.1.4_1588807257209_0.3686803128057272", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "v8-to-istanbul", "version": "5.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@5.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "87d2d1827ccd1821b84b5bcbe2d82ad8b6f3787b", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-x4Kbx2kGS+N+095L7Wgim6BECRTQUPtrbDhRFcfL1KLnzvhViKiJOy8Pl9FCRelja9ss4VsIzEHTT3cbc+fNFw==", "signatures": [{"sig": "MEQCIEwHyqAWdhrjk8fI/QdvkNz1cCcn8EIPGZJYGAl8b+3eAiBb/oMIiJZw1/+I05WvUnXhLM7AUqEsTAiHD7jZR+qN4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJ0aNCRA9TVsSAnZWagAAM3MP/iJ3gcMpPXcPDHz4J2zp\niWTcnYvuVcSS73x8OrHukT9Xu7rC2yq3cAvaYYP/3RFDMngGlEUdC6IgUkBI\nH8zLp1B+uLpGJZN3o25ZxblhqdR7N3bVsj2nuf/19PXtmAptPBmc/h8Q04zy\nfyULkI9vSUjy6C4ekruO//IpfBL6l2IqR1ODguzyJmYGwc99adeU5M9w5pB5\nzP2lcn6ilD3Y4a/CEszlktXWgj23iqvTQa2j7Pzzehx0LGiFvi+XEmcB64gj\nVy6M1WCmJNanucU4m//+c/CmogUj/H9DskMV18AAoULaPVB5Ot/S9lpapMe7\nhQGBLX/qNGkjtHdU/VcrQu7VbHWR8vKUN+UqlHVEKSSFIEvj7imYkDOtLNcY\ndJmQqzm7E6TO96xl5qw7sX7aOPUUYalwYK07vVZtUxv0rro7cxT0hAE+x4lc\nemZuYbfSggmFuTClzIrihr76QqokeRcPuL4kin0V3MLxdHB9w4d3yd6wjl85\nccnaFD3gTuaRreIEDc6gQzFzQxht1R6NNRpw/EAy3NzC0EtrXGL0zGXnAp+r\nMHfmfu/Gfa898R6+a177o7v2PHcVyRtnaXDzcs0uLOjrrw2aaESOqB5NBoOf\n6DJnRBdYCMrIkZDC2t0a8iPriwU4uIWm9MefwfVuMUCjTIIg6TzJQF/O2Ug2\n58W4\r\n=rmgY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_5.0.0_1596409485310_0.8042066232796807", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "v8-to-istanbul", "version": "5.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@5.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "0608f5b49a481458625edb058488607f25498ba5", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-mbDNjuDajqYe3TXFk5qxcQy8L1msXNE37WTlLoqqpBfRsimbNcrlhQlDPntmECEcUvdC+AQ8CyMMf6EUx1r74Q==", "signatures": [{"sig": "MEUCIHqg2Yj293v7xyOmfTejpxCEm+TZpuXd5oQZZfXzE/4JAiEA7ufeE/PL7RkbCzgJA96fF8qimPCgZBLO/veiN3miwuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLJ6DCRA9TVsSAnZWagAAjg4QAJVxhwIG7gCyIzoczloP\n/Kkb1pZpGzObJ82D9TWARA+3EprOo5Rq6vVKmS76f0BaIng3X3BPi6cezK0Y\nM5l4CPWhevdOSjAySlD4NqQwTwgJcarLBS4XtnjXEWv5185yAYvaZIds7Xah\naVNmDULl4+15xnVyA3nEg5AsmW3sglAHgDZ65c0YPQXMGoE1PQYJhi1K0+CI\nAvKViYfLuSVDcypJ6pLgy2ZPEqYUY/diu0ypUFhjkRvrxDjs3fYSL6WE1Nuz\nd0WuyQZb2xQFaXP7TqgraIhww5cxC79bzh6aLvqV7KLHEB4jJcl1XAWUXHlW\nvZkFJLwFBujHPtvqFfDkIeSWt+GNHxAyuh0wnreMlbvKX4oYrEwAcDWBsSIA\np+bZNObkv2AQKYV8O5XQB4f63QbqCqBws6TsboFeKhJWUXSKm0824z8+4kmX\npEV6CUFJqKnECS1JaBmfKKVa2q8U6dqorI1mrXkoF0MwsSxny/YMQo1rXqGn\n99sqU3SLKRVD4Hhip77WAh+8k2xViTk8gQu/TgnnfiLSr6hcn//bC5OKuVb0\nduNzu7bYSNrBMvUiRCsAjPTF0JA3ieISn4dokhUGKcc1XHNPg+qVQGuaS7LR\n7oNrcKRhXQA+uOX4+fU3OijTZ1vuHPDRQWWxt5SCw8+vNwQ6BQk5HtOsIDHe\n6UaB\r\n=/5Mi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_5.0.1_1596759682755_0.5639262413679884", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "v8-to-istanbul", "version": "6.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@6.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "645c3f4a0cdfc8a1ae5dc48ec6cf7328e7bec50a", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-o01hYXgLQVFfF1QOo7LeSqxC6J2BdnVS+dT6YGrKFMouLtLS7UTrjWVqYgkeef/iTpovP1WHyeDq0lRuLCEyIg==", "signatures": [{"sig": "MEUCIQD5uFfJ2CuV3DVneSo4u1Hkla1dZ/6faZjh3FzGSRy2oAIgC2Lq6WYpufOjNrIdLlIhFVqUJtrZ3SnGKpR+e2TtBZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffpxtCRA9TVsSAnZWagAANagP/0IHe7iElO179gekkg8p\nAmOVrOtyo6D+5TNFjuWxd1aiJnA7SPGgdxOQiW0oyZwKD6r+Q5Ex4zNnPYuN\nShIp5UYMHJSrl8OdZU4+6c/jfzAsYuQbuzNJwrB/xLiWoAaj9rgHfSWF9j5b\nGeS9NpANcXhSP9Lz3ZZejkJMQ018wKUIGGNe65YgE1zWvPq4dUsrggBkQwRk\nvgcyiuNyoLbE5+dLJptdvINS1xk8MsQgxyagHPbuU1T8thTrScs7P+GAtJal\n7LuP904jfoJ8eDGJkqHwbyvOVzfzYzrVfwBSjvu5Zcon/B7Cs5IgKSd6MkhG\nJ6vbmujGDXNSb/o6X/byK66FAcO8OmHyGuYDkDSLPY0R40SZkB8TrGr3Ukx4\n6Vq9cILxbrX0WyZwJ70nOnmq198tJsqaAchHFZ18b0w1Il5dSLrazRbHIzhJ\nNHFVjOHQY+MnEu4Si2/1h+p/mdQwFhgbiCAxxVxiNvFrz5LfJT+2+gSh44kK\nylzQlzJC6vjAyws5jeY3o1qMvIzOa0DE0NinEnEO7/lZk3ohtJVqD1PLt7EI\nM2fdwtUnkERSeeliCWPuowR9G/akAedoBhTvau9Ls5OR7ArKD7Zl86zlwGhR\nICBQvnxtGcuIsenuZQtpvrl9qj2IfaPxx/tfUsIHt9/xoIiedZtFdGEptwT/\n0ldu\r\n=xKwK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "6d44899be019a099afc49e853bebf5f8a30723d2", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.18.4", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_6.0.0_1602133100819_0.1789661441941992", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "v8-to-istanbul", "version": "6.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@6.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "7ef0e32faa10f841fe4c1b0f8de96ed067c0be1e", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-6.0.1.tgz", "fileCount": 11, "integrity": "sha512-PzM1WlqquhBvsV+Gco6WSFeg1AGdD53ccMRkFeyHRE/KRZaVacPOmQYP3EeVgDBtKD2BJ8kgynBQ5OtKiHCH+w==", "signatures": [{"sig": "MEQCIEriLDBJb79kHqz+6I0AMLZgIdclBBFxjK+9MucSKsIsAiBWkenoH+DhJY0CFwT0RX1OgR+1F4HErUvWAc6/ZmZ0rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJff4F2CRA9TVsSAnZWagAAEHoP/RlobT3P2vhdgxOHTyxE\nroSc6kGlGsQBS+w4CMwtoCVD2OafBtIfmEJ62mJqt9vmh3eROYLqr3jYhV/f\na38N5p2fvVvpfocxGxAcDxxraA/ZLazIm9OQccpuN7u8MdEGFZjyN8dCwQjK\n4zws4YJ7Q4SRI3l76SzxwhtMcCOz8xUwxVEWMGZNlFnuebzbQlw9llTiLZy5\nDDK/CR3xMZWcCByV6P9S3Ex5O1XBZ1unobq2/SkRCmJTCLvpUrYMyGep2oes\nsqy226PV2ihfArpaqfO6Hk5E9/st7ebDFz6V7F85pDRvVyZMqQWanPlTVygI\nZTfQcLe+3Vl6low4SeP42ySjaZhZukTY26niR1rEr9MDwfSlHeEx6RDFwm6C\n3/eOo2AW0B8xVOuaFxeAf+KeFkwsq8rqOz4BVO3zNojCMWwZtaXGKwLlJVZV\nGrcnVMqriGR6RM+YJi6z6lkogo8q0IHw+smUUakwLoX7QUQYjyQhpT7TOP0b\nAVIr0Z0QRHyG5GhVw5yfiexmvvOCEklUWKPYdSktvLp2UrL8BwUyXFdjCszK\nPfEiwwGbTWg5ZX3heVSPesQMdevYO/cJ6UhO1Y3Bn43R6rcf8gaZ0Rg2wKcv\n8Q04TZAo/bNYnD0dovKc5zNyYLGLE8bQfklzB/+nahKlrHFbh6NK6kmKFC2D\niM+0\r\n=77yz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "d375ee3d6431afecf402377e781e5799c495cdab", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.18.4", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_6.0.1_1602191734259_0.28203835718733994", "host": "s3://npm-registry-packages"}}, "7.0.0-candidate.0": {"name": "v8-to-istanbul", "version": "7.0.0-candidate.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.0.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "67d2f5859a3bf3cf21c9580882b5fc69b2d951cb", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.0.0-candidate.0.tgz", "fileCount": 11, "integrity": "sha512-XV5S+t4m4EAN7ErbyDj8+BX/F6nATESDLGDDSI+owIsbGiqrc36oGaDK1apwBb06AgDKLnSNWmnA9dFv8rztlw==", "signatures": [{"sig": "MEUCIFtQXmnyxO6Xbo0hfaydisKbbO4YZo4ha/V1KynSZMkmAiEAzOKEn1JaFJpZMPKXN623iiZ7IiauPQWQL6rdcmbfDtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflHdMCRA9TVsSAnZWagAAmCUP/3kmym6i7jtnzN8bfdj2\nVih3MFFyGYH81M3V5lYPKPnSeIkiG+WYLYKuTXGRnCp9rNZk8t8dLWAUUWhF\nVlCRYnwXNALfpRjKU0RtM42dIysq0xp8+mjD3A173BnxLkz3UYISjFuWsZ1J\nMUyPr2TXH3mQF7PQsdhy0arOT0u5XjOjfWeVM47cM76m7e5Wr/JDxj3xDHBF\nXH2hGMmcfI5Gtm9639Pp6kazxKYbOb+7E4jST61hW0PCSY31hkiSt/z7PcX/\nGaPv2BcEfd5EYBEuDnOTu+vdv83gN7oMC5SpO6lNuQj9ElmHxAnK3r+vHhQl\nNb5J7cetuRJqczshUiwP43b1hUguhD9G2oRjPnm1rkb0rCZPIPSiaO+dfBka\nL3PDggzjRKM9X/PoMkKhH6z7VqmIs9l0M8I3jXD2dX4+nOwI1B4pAFKee71b\nA5mgjNUYMpydVchgDVbVV/LUqwWX+peAHuM+wTs/4uB8SpTwQ7tlu8lJob0a\nkxQWFdH+E7Mj6hDwkuRDLQtCcxQxIutWAvZV0Jlk12PwMwdSZasEI6bXOxy2\n4E4Y1oJbsa+PSA9annpJcOfw/t0TRc+PXyep7UqJXILGGsQFczOX+I8zZ3+1\nafuId1zFts+GFsGgONgd9q3o5y8FcHNf27yioPZ36ajmCjMMFsFbiQBMvNED\ng2nv\r\n=JyRg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.10.0"}, "gitHead": "c3170fcb62bdae83746b9f6b72333c69350e4e17", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.0.0-candidate.0_1603565387754_0.9395572759193105", "host": "s3://npm-registry-packages"}}, "7.0.0-candidate.1": {"name": "v8-to-istanbul", "version": "7.0.0-candidate.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.0.0-candidate.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "8624535a48a822b4c1d6ed22c4ee668f19123984", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.0.0-candidate.1.tgz", "fileCount": 11, "integrity": "sha512-ipx9z+kxJSmUZtF34J2lQqUm1zz379JdwFmCpnlOcjojIJbAuXnpWrM3XhZQkNQgyr1/MpR0AGyt85WWy5mG6A==", "signatures": [{"sig": "MEYCIQDoTXptUHEY24dXnJJQC2ZG7WXQS8HhRjk4V8HhXmgulAIhAP+1RmAsEiHR7mYAKk9njRM9+phL4rRGHMkwiBYRm7Pl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflKW/CRA9TVsSAnZWagAAHcUP+wQO59uGLzY328tlI64R\nxnDBegIUhxBGT6YoT4YYRXxVMClC8LAzoCUSuvCkCNPY1SExyF+kHs9/7vZv\ngJ1K4FC4nh6SKc9Tx3Ai+vhX18Xe3TN4FEah/Bu+S/WVLtz58v/sp4iHraD4\nyLiZ2WZ0wVZ7OMaNSDMeaJPI6fyqlmRRpwWK+rArjRBmVnLrBhgElRsBMiCs\nDFYYLSfykSQxultgQaOroy/tgRiOv0a3yKkzxvQhB7N/EQK53ERUpx7ObBPM\nXuMerO8q/X8g8LmMFCG03PhflEKQ7aJJpfX0M+7nz4RReQSzTp61dYS8blEK\nHWzwT6ACjA69z/XX8YCCnJDo5dp/RzvV02ZPOmDI5HoQLgGpxQeWzQbPlduy\nmlmZsv2mXdEkozY9NzzDLX9wxjuatmx4ymVc5R9tv/hbNeRe3uUNggvkfJqA\n9S9titl2Rjt9Qm/qPLAZo92etrAyUnsIfywlzO6yTRLETm+BAG4VPMRB6yd6\nV4NUihkKw2EIUGwbMt1+BZE6VAO+uB+emx2HHcbuDfGTdzOy6WBuy3tRvIdD\nm1FXes9TV7igZrh/VkobAemHrCFDAOIr7Rc8DkrVMj5YR1xk5/0sH0JcPMLd\nJO+2QByn7lux3wzfOJcku3zvG+CHnARnt0P0r3LAFpGBBc0wW1j+uD5Zauq3\nInws\r\n=Qe4B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.10.0"}, "gitHead": "c2feb2b44dee53bf8be0eb3453897416bedea8ba", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.0.0-candidate.1_1603577278660_0.5469904288816254", "host": "s3://npm-registry-packages"}}, "7.0.0-candidate.2": {"name": "v8-to-istanbul", "version": "7.0.0-candidate.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.0.0-candidate.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "df9bf520a398e86e0f3421d18f1d2ace24f6fdaf", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.0.0-candidate.2.tgz", "fileCount": 11, "integrity": "sha512-Q1PRfcZNtocQq9g5rXSck9oqcronUnhUIKCwpZa6aOqY8WMEZ/phmj9tgZzBW1wm0WkWq9kQrKQRQjKnfcu0aQ==", "signatures": [{"sig": "MEUCIQC5KppwaTiREVoMY1GqC0sV+lKVe1yL04KtVyzImYX+sQIgZKO/f57X+Xr65XVs1h4oBFnFa1cPqIMpNRIv+QRTCJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflKccCRA9TVsSAnZWagAAVtYQAIqkKnpiHB4yZzX8CXIj\ne5C2DrsIs8tX2vCr1zWbmSg7XF7kcn4udNyf0JNrTFHNABGUuSx2PUaJgX5I\nhb71NVlDFOwdhDzkGSz+fGIIbYGzAcV882h8kkZfkhLnMRJo4HPD9yvLK9x+\nlscGZhseSoniKPcjf7HYNWdcPqzU4IEmNN97lqLHMXnFsAe6gqqhO/5hsQYJ\nVXSIH6sOvrbQpSUW5GxGq7GmUx4cQHvwSOHDyp7PJDY8UzLvuhWniF3SZYub\n645gBnjQzWW8O3Y93wdqMtseWHb6jHI9+x9ZNcJuHIYSZ4Z4QCIkh5Y4TF9e\nSdyGjL+ZmZKCY/80LqzueruMG0MrP/+kQQoqbdd307+GQjkqoLGpZcYNhaAs\nvp36DDqHMpAUSD4SaW4gekKE5p75TivY5rLn50KScm9HwW9wRqE80kHsTXKX\nXFwlfNH1WL/icfbYKWQ+l75sFW53XAnKP/BBiDv4MFsMHFWHKEs/ovHdoKPV\nPdpRzftV080+PltRU7qAo11qVTkWsikNyx4tZeZ2mBz7pvtYiRUa8JazqV/0\nxHs2aJLjWv7MOHrOHAHZ8R4QXzd2gS34bCEYIzjazdCxD4qE/2O4iuZp1NqW\nskKHkVJTRl9/zrMigXqV/qgwI4rOag0TcrNQdbc9ycLo4b+lVUOjl3gjYATL\nPLK2\r\n=REyb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.10.0"}, "gitHead": "12b6ef1e14a91a89bcb6d98f730c3f81473a7e83", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.0.0-candidate.2_1603577627910_0.8990162870081739", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "v8-to-istanbul", "version": "7.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "b4fe00e35649ef7785a9b7fcebcea05f37c332fc", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-fLL2rFuQpMtm9r8hrAV2apXX/WqHJ6+IC4/eQVdMDGBUgH/YMV4Gv3duk3kjmyg6uiQWBAA9nJwue4iJUOkHeA==", "signatures": [{"sig": "MEQCIHUegS5ARScx3SPQa+zNshwIdKn9ht4RUS+so0vqDmoyAiADUKfKRfJRW7S7PITtMSF/GGV4kvRMoYWwV7lCPtsw5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflMSOCRA9TVsSAnZWagAAcIgQAJxM2wCj7cBRV+LF1Y71\nyEaLanHykwWC346MgBqCiXAVXsaB4YQI0yhkCtALSGN9/u9ZXX4p0GlpKAVN\nNRYsHE1/nGMkwgqljMqHTi970O22fEdGYbgaz7w4I5h4+n7bVl8BZ47//lJ6\n7AXETgLjdiPDTFd8tOqpHvFEnZjgdk3VQX5pUE9mKyewjfh/7LFDg/2TsdNZ\nknJjrqs/MRtwbZQ25MJ5IW9xbLxFoHHOms8ywWnwZrmWoFNeQ4q2hQ47dDvV\nZgCylmHmYDLWzbmApWrzZR1W07fpor3p+6AAVP+Mb+Mf1+1gkEcKFSCWW3zP\nTfAdd6mnUu2j+t88gSXL29rcZRxIUXH91GDAedjcv8w9qkOPZvqyJAsFSdze\nwgWYCoKjujnq3O5G6/xnGV0w+oFV6sgxdCrprmhHQ3cTa0Zx3S3r6i2qk96w\nTUv8xHeknPz4oaVfjvSDZg7ULbtCtvpYsYBoMN9rx1J/lXoK5kajFoOYlqJ4\n9e6LEIm2mW1vxQLarvgGh4Nq+7I9xdfNNXZWR8P1Yj5g1SwF1Vei6iZZduHk\nMUVTk73qDeOPV+ZCDTDIf3GhUrFKio2oqgQm/rv+bo0sJgR126Z32OPPMXVU\nRoPlNLuokzaeYfP604OYARDv8bj7drcf8i+J/hkKRtKss0MKAufYuCKlq+Qm\n8shG\r\n=wZkf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "08c4c2c85ee13c84fc82844ac62c1b85540b05b3", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.0.0_1603585166146_0.3920516440676154", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "v8-to-istanbul", "version": "7.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "5b95cef45c0f83217ec79f8fc7ee1c8b486aee07", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.1.0.tgz", "fileCount": 11, "integrity": "sha512-uXUVqNUCLa0AH1vuVxzi+MI4RfxEOKt9pBgKwHbgH7st8Kv2P1m+jvWNnektzBh5QShF3ODgKmUFCf38LnVz1g==", "signatures": [{"sig": "MEUCIQCgHTSe3H03ojqWjnAVhSDf7lOlb9EDUWkUTTmPo5tZuQIgKPeiryUTH16N6dAWGkPfeObmvUOjowKZNJHq65iaZXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4T93CRA9TVsSAnZWagAAwKYQAKDSZcVO6zkBhjVfvoIs\nd3Q4e28R3kqdednDIBZ6Xmkk42MXQsiNIvn3yIHWrA7A8D0k51zsiJYipaxf\nkwrCCh7C2JKpPPcYxnbG9zA7xr8PM2vPeIh9i5Djbrl+WnmHY+9P4g+LPkHD\nMftOyxmkILJf6g4z+cganQIbuhentfMSuOvvHTuXJ92gJ7MyXWG4/3THl5+C\nc0zICGBeAOCFLMYmd0ZFQ+FOLHBpBtqBy3yPw9I94IARf9c5CLB58HpC8tMe\nbAHJ3Z5nzBuJyroFVRIeBdzU8eKxuI7W8f4Rf64e1tNgMMVUOvns3QJmEDiN\nrlzF79frXm0ES2JIhUXNsbTpP4iAt6usRibdAfu/iWELMHNUZA2JLIgUAPrB\nAGvP1alsxzoARzNNNm1pGOv/HQKCyoFpJDsK7YzHp0+2CnxalQdvUrQ++vmu\n1g5O2N5kqKnjYeYXcbWE+OsNuYpCJBNXClkh/0RgmWqSUQ9N5yXicu4yzuVj\n8g4A/QReAAjVUP1CSM9tvDAg28lZ50CiBfoGzhOchXVv5UMFmnllb46xpjHK\n8A1fuWvDTgl+pXeXhDF08qKyGSNmjeIm7Yex81O9CApfF50oQQjWxTK7UGPE\nSEufqcKdHWbB8Ftuyfz5uyR6FxGKKzsfg3dXP6h2ym8MTaBmiQ6WmKpWvG7m\nKbbm\r\n=8gcl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "d230f654547ca15b3c0eb4089e73ac19eb407d81", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.1.0_1608597367027_0.05757715648834161", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "v8-to-istanbul", "version": "7.1.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "04bfd1026ba4577de5472df4f5e89af49de5edda", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.1.1.tgz", "fileCount": 11, "integrity": "sha512-p0BB09E5FRjx0ELN6RgusIPsSPhtgexSRcKETybEs6IGOTXJSZqfwxp7r//55nnu0f1AxltY5VvdVqy2vZf9AA==", "signatures": [{"sig": "MEUCIQDPXafz3v2N2PVxRgt0Snz/oxOQdNmtPjj4SESFpuap2AIgdzr5SAIIzCes84xqocP4i0uTsh85/viKmyWPM/FRak0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYq9mCRA9TVsSAnZWagAAgikP/RPZG157wwkYMtVMI3BF\nJFLer/PPWWQSgGWGAx+09t+LJ1P22/xxi/e0jHUUK0lNz1NEtU7STeDjPmcE\nRr/ESUdsOw0Z05yLRkXDZBrOl4JZdjxsjTyWvfEpotMkH8x79aVjR8i9AiGn\nFK6XZqUmTP3gVe6Nvkz3JfmNSgM0U/y0l8L9hJW3XKUh5+F9eghOfKjcie98\nCxnOyqPT8pKuf1EtWHg9Fnl9hfLueSKWqHsi1yLBdHYdhkxzCuEe96JYynP7\nWffOh2fmNC1RzD/B5BXVpAZxFqBLIc3wR6GrDEvh1hf86udXbvVTewhSx2nt\nbOoUaqVCqDlRex2qZXnMUZ98v1EVB3odZ77y1xfgMiF5equO1MYLGkIV8PBl\nWmMZ8Ng9m6gxM1iIiRTyI0yLhmpbVDNJsHnNvRoMkF3UYP7crgpmvcbhZDn2\n9fnDoFVWv3N4WrHZROPocrUdQyA2bZtvjSYQRaPdzOq9+0YvWWwJbBzBrdNb\nr3DLCqWPdlcSmWliSvz8dIqBxSKflOrUxcCW2cNktIBjL+3zWODQIF6bhqlx\ncFh2LnZ57nGxXHEe5C6vW0WmXp9e2LYyuMaQSYjIfpjrZ0B2GOCtAxbowUIK\n8r9Joras5/HYs84Gg4Ze5mb4A+HTPtpAqlqjiPcxd3ugU+LCMmTJbwTB28/F\nFaSa\r\n=SXxr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "1df3c54a81fa82ac229895f354b249ecd4af9a66", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.21.0", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^14.3.1", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.1.1_1617080165570_0.06776723972231324", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "v8-to-istanbul", "version": "7.1.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@7.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "30898d1a7fa0c84d225a2c1434fb958f290883c1", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-7.1.2.tgz", "fileCount": 11, "integrity": "sha512-TxNb7YEUwkLXCQYeudi6lgQ/SZrzNO4kMdlqVxaZPUIUjCv6iSSypUQX70kNBSERpQ8fk48+d61FXk+tgqcWow==", "signatures": [{"sig": "MEUCIQDLAZf+vwvsN6SdrUqTtr/7bq8y7EIYsX53clrtnf74bwIgPzVtvRfZSn7uKvyE3Z2LTMsfIZtbqJLghIR1mDVk+aQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkvgKCRA9TVsSAnZWagAABqEP/ibYW/P40ej+8ZtD7J1G\n9KZeTAuPTBPKBTVPxotaDRCa9BzJ5KFtH85qMrARhbEo5ceT2CoZjYVWAtFI\na8XlVUD/Cwv6FsafhS+Pv6D5uMgfnd2TJ54IAcjNZn8RFFgYwnATRL+4eir6\nng2hamubVzA8AQUNHeNYik1o/r8BuFrzfeCetxNDDuMl1JNnM5sZ8sQmNQry\negy78MDt7kQon19Gsydw1HHY1xpvmYqbTHSHEQ5/A1fGGYyr6mFe9MN0l8ia\nzQZXu+dAaCCD2nXtFbcnSbL1z9Fmqv6I+eNDqL7rDJRyn/ZFHUXNyab+PgCc\n8D34ZfzXwmzWpSYgqpPjRvxAhJS8le7YrnXppH152kSAK/DFvRAaCXlbD4hx\n/WYPjoArFrnKoNOVMqTajX3lwncDLsSjV3Q+PaC44nVT447fTBIEioYRCgj1\nARfVV9yLk7eEUgfXpgQKJpj5r/DODPbPXo9kocB+qfTlUGSLL9xlGIppBkSe\n1y24o96x+RPiGp2Dm3ym6X2ie0Ewu7KJ12VWUAfoK4VcrGGJMuewX6vRN4E2\nJoWIsPJOlpLqeoKfOS8wbwoD1gXwjT32YIF2/QRpn8f8e3nM4mQIU3sxqz2h\nseeMFJaihZg01GN//cgBBm07km2MBJzh9i8XAw/zFNJOYcmLrOltTPEhXWom\nhw51\r\n=Aa+g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.10.0"}, "gitHead": "8b7bd88b850c0e6571023bc72207b6663e96b85a", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage --no-esm test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^14.10.8", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.0", "@types/node": "^12.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_7.1.2_1620244489640_0.8617862459487262", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "v8-to-istanbul", "version": "8.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@8.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "4229f2a99e367f3f018fa1d5c2b8ec684667c69c", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.0.0.tgz", "fileCount": 11, "integrity": "sha512-LkmXi8UUNxnCC+JlH7/fsfsKr5AU110l+SYGJimWNkWhxbN5EyeOtm1MJ0hhvqMMOhGwBj1Fp70Yv9i+hX0QAg==", "signatures": [{"sig": "MEYCIQCydoqI+1SZXyL1yH3BL9H3PTM30DcG6jqPE38/Lc9aRgIhAO4JPCTnMN1qSPY2zI+kJ4Nw0tztDMdiV43gCbZcMOMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguU6fCRA9TVsSAnZWagAASn4P/24Q6KteEJAK4qtcQmrn\n2iCOXPKKq8makytrkZlY8QwPzo/20+wXIu774yRQ3uLKXI+mGAUxahMeIv/Q\nF0BiNt5xLrulOtcRIyc4lvBUKzAzVrz0sE1rDXKgFVCr8q3LJVTUK7oOg1C5\nEGKkPzqsW0mLxZlSacCvRoYhrT1C3L18xaB9SGAveIHS3YDnSgmGnArmVOVy\nPA4URwhEwlX9a+q78g+PV6Ma4NvrPpJh4yktl8m6+djCPDgU6qpZMCoUWN0R\nNdEgPSayYZ5eqv+qxHrSOtXRVJpFEiSpVNTWqEJ5NPblLc9aBYDNmQ4CXE94\niwOCNZ1Z8i4U9Mh2BSjuzyEoV9ziS0hFFKcRQE3pI/XugMWRKbD9X7kE3lIS\n01Fp8yt+6Bltgw5OzBP5/E1DbaS3qgeL2SLkTo0+qvvYHLmbIVOzYnDBziPk\noAzCc/bK0teJX5fPx5jrzXN2mha79spLZQJIj/zklxnOTCmJbxIcUwmsDXhj\naqBo56+EkoFvHnOnglcLD2JJwt3b3SBrksh/FylVdjzT8Yd35zIPqrNuTxU7\nKPRxEFYflbErKgDfHXJrG8fbclzLzJJ3W4A5DAVy4dpmSkklAEsKx1gwyh/J\nZNpKrgoUIyry8C7ryeoFj8VD9+0ix9GDOV0MbHVK9HjZyBrFW5FLMiyZNOG+\nobi5\r\n=1T1G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "5cc49957e7276586cc2c8ececbf598ec90ea159b", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^15.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.0", "@types/node": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_8.0.0_1622757023027_0.950233782429603", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "v8-to-istanbul", "version": "8.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@8.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "0aeb763894f1a0a1676adf8a8b7612a38902446c", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.1.0.tgz", "fileCount": 11, "integrity": "sha512-/PRhfd8aTNp9Ggr62HPzXg2XasNFGy5PBt0Rp04du7/8GNNSgxFL6WBTkgMKSL9bFjH+8kKEG3f37FmxiTqUUA==", "signatures": [{"sig": "MEUCIFPqMXxDwoAMbj3C4AyLsHUER6csHQmE8KYYf2KW/xtkAiEAywXQd8Buh0B3KKzA+0WzgTaDtGOSefu8FCsCsJf+PGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41831}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "aac059d8a234b69af8da762f4eb8ef44f8edc34a", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.6", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^15.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.0", "@types/node": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_8.1.0_1632740079308_0.38779631882582133", "host": "s3://npm-registry-packages"}}, "9.0.0-candidate.0": {"name": "v8-to-istanbul", "version": "9.0.0-candidate.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.0.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "6664d6f46104ea19bc916a135f0143af0084eac0", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.0.0-candidate.0.tgz", "fileCount": 11, "integrity": "sha512-Ww3d5PMDyKqsBbvD/elLO1J2COsItxzy2/OYCcLT8uP8wpeyRG2zUu4lE/sAynp2Xigt8SEthPaaSGibg0WLMA==", "signatures": [{"sig": "MEQCICugqYUjoya8DcWDT87Vn9XUKT2+O7fJ4bMxbySx0LdoAiB7g5j23e5S97kGlCCBPM3TLz2+1HEG/WdY5bwU+LZjxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzzlxCRA9TVsSAnZWagAAvZQP/il3zBBCO3NagF8kmANP\n0HgIk1mQLdF0Y39meWciCtpi5iUb/IYQHhE5p1JuVnS3NCaC5Xs3adRJ9jNG\nDhjmKBuFlYwElQK6ht+UDFN9D72P7WrJ56/XwUaDTtuw5UIUlPZcpsgdRib+\ntZGydsjtgPchrtf4Fu7KJq4ob4ij8gT9lm/OX/7Ft5f04aB7U6LLe2V0C+EL\nlMsDWzA9UzVkGbtZjcvcoUusAoPD3LiAAvNKg+vXr5sgVRUiO2Uw7+hIMbec\nwGIU3/4WYcgezOfGRSIcF4kIDE9NabNACaBG96LI0pTyKYw9BWT7f9iCIkTw\npOCAn5ElcFwfIUZNPYqG7hiCeRvqaLKCLQf/R04bEaajIWbq1AkgvVUjyCnb\nyRIMowZTGxg5A6sE0YcDTRpbliFSskuIbO/KIBZHfkxNBa32sKiDeKujKUND\nLyNYElLJzh7LQXAbMjNWGRCSxf/RjOWuPjrFo/0iz2cwDZHm7plfElWgQgtB\nY7Eu/l0EClZZj1JP8H7eA+dDe5lFwwh+WL241i0EQ7ok81x4+dTwbMyCZbOD\nGiwTCyks3L++zZH396X/TiVCS4+t75jHsDIiu94X/dTim4L9qSS4J6RBMpdB\nRYWZ61vZh4Xebps+ArBumsmq929LVe4PAobd/Hf2bc+92/jQsgYkOuI8UjoF\nortd\r\n=UsOl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n\n// cleanup resources allocated in \"load\" (i.e. by the source-map dependency),\n// the converter may not be used anymore afterwards\nconverter.destroy() \n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring all lines until told\n\n```js\n/* c8 ignore start */\nfunction dontMindMe() {\n  // ...\n}\n/* c8 ignore stop */\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.12.0"}, "gitHead": "567a415896a28a2448177a3dd8b423ff4edab418", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.2.1", "tap": "^15.0.10", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "@types/node": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.0.0-candidate.0_1640970609659_0.8639808193857905", "host": "s3://npm-registry-packages"}}, "8.2.0-candidate.1": {"name": "v8-to-istanbul", "version": "8.2.0-candidate.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@8.2.0-candidate.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "0dec0f42d77a6f71fa488735602ad4a4a5feb026", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.2.0-candidate.1.tgz", "fileCount": 11, "integrity": "sha512-HpTI95S0T7PjbAZl1wcHbZC4Wzykr4RqQ+k6g2f42sl38uSsy0y85mGEvSancAXluhpZtQJw3HDbWisQGRuarg==", "signatures": [{"sig": "MEYCIQCcEBBD0ocXCMHC65kixSaA0BJF6G6I+xB35B4WmmRoiQIhAL2hYZHmAAR9TNP1RLv01O4E4rH3sZZ6IENdAJpkWYFv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0IfsCRA9TVsSAnZWagAAIygP/R9J6MhvMBghYD4D0YnI\n3TQ6SZguhU4c90ZLEIIclDpAH6yXXQc5rw2KUId9juQ7PNsbVWtM/EuxKk8u\nYhxW4EqohsEkLLRNQRl8rbuEn+GQMnjqxXs8tqcpEZZ75Pa68FUKtwycimdL\n+oLUnpDCgSZtRFfbVxxUZcPKddjCzWpo88UFLN5uzxjfBUTsjZPNI8wze/HY\n5dFnkgdTpppJ8ZHDeQxihOgPDULvRc41RJA/Mw8y/sY3lMTcg9t3+pnBvqVI\nLAaw44R/5SB//WcxoBewhQvknjbAiOngYekFWfZNIUvffSjQ8lb69HyTYu3B\nfAp+L4C25TT70+Fo3zvgdY/YF1g/BSWjBrIwn1rrDshz46rncX8WOK0KF3Rz\nSj1vE03A5ChJZeWxXQ6LUNq7f3ceximfgyrNljNQ0DdFSSk25JES/cFlRcyW\nvFPgwocBiTFiJyxTmQu3sj6SrPsF3TNRaVvHuMtSC5szVxjwbbSm4oSd+y+h\nCCF0FnBH0pgKGkEh0g8v7UPMReMU53bo8iwbjIDHZ3HJbhpW3aNFia1e1CGJ\nWOGm7Urs/QBpIGRcJusUUu7xcg/sq1jOR0h6qgkcpwp8m13Hht9rKYQpWyy2\nkI+xUf+78wmqPbP+FoL7QfnV30ELW8hje58CIfB93QtarMtYogORZ5yxDQ3e\nyhaH\r\n=kg0O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "readme": "# v8-to-istanbul\n\n[![Build Status](https://travis-ci.org/istanbuljs/v8-to-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/v8-to-istanbul)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to the async source-map dependency.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n\n// cleanup resources allocated in \"load\" (i.e. by the source-map dependency),\n// the converter may not be used anymore afterwards\nconverter.destroy() \n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* c8 ignore next */`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* c8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* c8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring all lines until told\n\n```js\n/* c8 ignore start */\nfunction dontMindMe() {\n  // ...\n}\n/* c8 ignore stop */\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* c8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "engines": {"node": ">=10.12.0"}, "gitHead": "825e320a6ad33deb72dd172ded6cd8a63d2e7b64", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.2.1", "tap": "^15.0.10", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "@types/node": "^14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_8.2.0-candidate.1_1641056236642_0.9940655171253869", "host": "s3://npm-registry-packages"}}, "8.1.1": {"name": "v8-to-istanbul", "version": "8.1.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@8.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "77b752fd3975e31bbcef938f85e9bd1c7a8d60ed", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.1.1.tgz", "fileCount": 12, "integrity": "sha512-FGtKtv3xIpR6BYhvgH8MI/y78oT7d8Au3ww4QIxymrCtZEh5b8gCw2siywE+puhEmuWKDtmfrvF5UlB298ut3w==", "signatures": [{"sig": "MEQCIGCerDpsRYuPYNuoFtE/LnVQdbUs0V9r3cl7iBk57ZckAiBPfli3YYlYFgZpU1jr3nYEyED2IOTVU5dlN+x4rw1qYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh24y2CRA9TVsSAnZWagAAj2AP/iGIbh7gToaBYzRCGMaf\nOTg4+JNt1SeMC3Vz5tYmcTc/2xBJ+ECwX1Df4HRFlpcuVlLIgcB7JAhhHY+G\n8IVxxyLSX3Cu1fojqWw47Q1R3D4CJ2BsCNmSQK078U8D7ZudKZAmEnXbwWB9\nXyqivrkyxfgBZUIcmvnGZMjHL6HaVQCKvTxzJZRCBMxynlDJNC9AoclpU0mU\nlv0MoEcYzAI9jzVFCzz2N+OPm0QMcDvicOXyTRKlp3+fiDOTQrmKLj4LkX+B\nxqb1rRJ0cRS7v00MrBtBQtY/leKP2ASuznYrFJlL3eZvCGyFQErAF6rOKi2J\nEAKh4I8Jq3/nad2TYwVjaLhf9MkwCg6rhEsRLp1rK/INlDMRJFkn1FZFW4dO\nTqPmdtLGbu6VzMwRQ73WepvBg1Sc+CxnQXKSADJRHJ9rMjmtBy49BMYEfmUj\n9ddI0FjhopJsMXzCON6tlvEZNvRaDFOeWc/+k3D3RgOA9pd8BV1bon4PemiS\nuW/3fS51LchgB9ty0/NgtOiCTWePe0wFPlBY6DMjJJ0L/fCyZ/FTzLfxyw3x\nJfdOvxTcWzWyog1hKsSJp/J1XdGkUCjA93ckl6TJWBIiCMMtmWpXY6xDOXTB\nRkmriwgXDp3Wb8NG7QN6WdvXzv4SaWqMDIBLpEvqRs3KN47iLmrFDwTZeqEw\nZXWM\r\n=GZtj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "1f357fa2e994385b56ba505ca199eda46b233a89", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.8", "dependencies": {"source-map": "^0.7.3", "convert-source-map": "^1.6.0", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^15.1.6", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "@types/node": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_8.1.1_1641778358758_0.35646083272133766", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "v8-to-istanbul", "version": "9.0.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "be0dae58719fc53cb97e5c7ac1d7e6d4f5b19511", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.0.0.tgz", "fileCount": 12, "integrity": "sha512-HcvgY/xaRm7isYmyx+lFKA4uQmfUbN0J4M0nNItvzTvH/iQ9kW5j/t4YSR+Ge323/lrgDAWJoF46tzGQHwBHFw==", "signatures": [{"sig": "MEUCIQC8c6aEf+DFgC9Ps4I1+jh2NHu5OoGRMQpGnnSpPU4xDgIgA83PzbFcpYNsHG2oybIax9Bf7JJOPDldYHtoU9+YuLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAFwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7rg//fJmfQJ9wxKuVJhCQsPeDC5+eizpf9Qupf4PBZX0ilOLqx+NM\r\nr9dBF1AANNkEO2gjNg7YfTeuWk4atmrO/cLE8PAbVXtE+jDuqn/9eNvwKYJW\r\n8PzrKO3Dv31j2M7oeLYYwdaQiKqrTtF3yIGnPxLDwxvf3ZeCrct1erGNERQT\r\ngrmlc9+tzK4PYW2wDisyL8e6uxP2vU471ty5WSHplVZQE/4vlbpez8Nc/OPz\r\neSUwKVepsjlja2eBcchV7/mLBsBSpSx5qlYQkArrX/RajFLm1vUPZz6yYUZk\r\nBYn6UKfyDFYCsf/djxkwwbK0O3ELqEhW8+4OMB9pYJipWbJ5xLsPKDWiNz7W\r\ndIN3LjdaN8AI1EtDkFy3OaEczCeXxh0Mx5U+BFhh+PulFghlLHuiuXZkebTb\r\nr5OCSvr9AVEheUYQNwZKhzbwLBV6HKrsyURQeMefzIV1+9zIL07b6mGhCAqL\r\nWVVMEb8bPaW6ARfrEvd6ltCm3gncydVugGYSBTa8kDFzE1sihNfWIXKVbMBW\r\nf2w/LgXgkqpGBbInZvEYCEYlART5uNsPo+gghbY+Nnqfrwe+OHhUwt8QtOA7\r\nxI/RULuRFj4PJbRL3wdV2E48Ss+R56Hw612wO7G4tcfYLYt4p2M6mlc9WoxD\r\nAn70Ux7Ie6mo3hrEV1K8YUbDK+6M76UcNfw=\r\n=7sUn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "62bde4b534ebd54807fdaf8a3db46bca6f1acda1", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^1.6.0", "@jridgewell/trace-mapping": "^0.3.7", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "source-map": "^0.7.3", "@types/node": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.0.0_1650458992284_0.21308019568826042", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "v8-to-istanbul", "version": "9.0.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "b6f994b0b5d4ef255e17a0d17dc444a9f5132fa4", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.0.1.tgz", "fileCount": 12, "integrity": "sha512-74Y4LqY74kLE6IFyIjPtkSTWzUZmj8tdHT9Ii/26dvQ6K9Dl2NbEfj0XgU2sHCtKgt5VupqhlO/5aWuqS+IY1w==", "signatures": [{"sig": "MEUCIQDTsWd5qknhtcF5s4+5urzBqrXiTGhp/S1nP15PyOyGfgIgYxJCs08wnLF6nsyZWpN2VM/WPR6k6AQv9IRiHbhuDVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisNhnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqJQ//ca2kn2glw3dstRrAHuZqb+6J97z5aHm/xsyibubhC6KNrEBV\r\nORfELNfdBMa6doHNLubvay9PQNGzh7W1b2t8LCnXMex46NczbPb4PW9PdciY\r\nZlb7OBolXIaBy6NS2J4dQu8CibMhOB7iY8mMyHGdrM+TPUR/lSej4QUV7zzI\r\nMAKOgfSXJ35d/mbIqe3TgdTScZ1dpS6uNAlrK6KidzbmAR3R6+hvgLc7GDBa\r\nrqz5t5xricQ4Q1zVclEttpCjL+yQXNQGTSN20/Bc6jwUcJbgBqEb1sEx49XM\r\nBeMwxDs89fLTgl7pTUQH2e679ryYAOKJFSQavBUPN8su40jfZlRUxHWvb1IG\r\n/cOfJfZ8CUc3kTiuHsHfJiYjZXYX/74WFoLpI2rb01eGfQQQ/GqME301H71j\r\nyRtNlceWr1pYy/Ahu1mRqoYhCXwu9lcE/oWHrMcJYMAr35LpMsmNrSwJy7Tc\r\nk3DA2q6DkRT9KgscHgVjGVpsn8GtR/ricPAJ+JiaWLVtNw+3g3k5OIq0QDNh\r\n8eDLGXW4JRzyzU6AD+bKPnNsYGpXlYAUrhslQW1sdFuA1L4BaIdgNu/vSNig\r\n1TxOeuoZ69TnuDMHrKq3V7S4fbRalHvW8LsYhRs3XMONewWPdLI9yyNunEnC\r\n9C3ws1CB0wBmwWznC+OMoJevF9g1RyC8G2k=\r\n=eZgr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "b5ecd8242cf64389c9a656d78530bd0428edf195", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^1.6.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "source-map": "^0.7.3", "@types/node": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.0.1_1655756902985_0.5235100968202715", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "v8-to-istanbul", "version": "9.1.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "1b83ed4e397f58c85c266a570fc2558b5feb9265", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.1.0.tgz", "fileCount": 12, "integrity": "sha512-6z3GW9x8G1gd+JIIgQQQxXuiJtCXeAjp6RaPEPLv62mH3iPHPxV6W3robxtCzNErRo6ZwTmzWhsbNvjyEBKzKA==", "signatures": [{"sig": "MEYCIQDrNBpVuTC7XRkhrvsrePtwxWVHDga08s8X3c/CJ4nhhQIhAIX7bcZDm2ZlSp9mZYo3MiucbOrvipJ21PtwvVvOcjHS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj69pAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTRA//WktkukzKwv9AA3hUY3my0dRxPv5JWkTJO9j0Aga445B6udkF\r\n1TfVdGYG8zV2XmlJr3fcy/WrG+u82FGHeGLqAxjazl/p1Nr8nyCu0GirVUqo\r\novoVkD7h/xcq0rQGbm/Fz8EGM77ioeRJMVnayEPr95/ZA+nPGGx8I/+kt3ec\r\nXSzdhfWTVFyV1cxP/hOpYUTOvxQ6nY1YdzT5jJ7P6Eoj566lElNIHyI/AxfV\r\npNcZ6VcUfkg6KjYDlOspZdJh0ZurvjQEylFEwjXhgA2z22QNOmuOr4TuEjGe\r\nf2ua1IiMPKatVKLYKkOpVFNWLdh/9unfeC6mtMTZWcd1qFd372fsWHwyJMTd\r\nIT3+7DXg57J422pDUIjHLNQ57lAWGpD8DchloKOU86+sGF6hAxPa04+HFsGg\r\nGx0UMAB02yuG/2YmHATWN8TbqYn+8RbHSbbKk+ZfCInfEpRMvg7cWbNRmA9y\r\nzaZW0H51yKSzwoJ/wDB3s5w9dR4DjI8b4jF/gTAyipQ4Bl9dorBKuPluw0pq\r\nXlQ0Cu0hlnr7SYnlkL4Z3f3vcWJKWE4Qha7L510W4Of8gxn45HArDqMij4Ws\r\nlRUtdTqcbZqxNNLwm7TKax/NSsrpzndWPL3DD6ANbAWGgpaF2K+6bH/FG1Aj\r\n11ShdC8++rB1YZwC4M6+B5tSg7486s6HVao=\r\n=6vre\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "fca5e6a9e6ef38a9cdc3a178d5a6cf9ef82e6cab", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^1.6.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "source-map": "^0.7.3", "@types/node": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.1.0_1676401216489_0.615261662007532", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "v8-to-istanbul", "version": "9.1.1", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "9230b91e651e3056c5910b13fbf8000413cf968e", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.1.1.tgz", "fileCount": 12, "integrity": "sha512-bmENUoymRMtYsEokIE+jiA0x3Gpx1jSlGEm44auyeFtSeN/gz7C6kyop+8eg0ojux/5pjLe8cu4Mb+AMlszlaw==", "signatures": [{"sig": "MEQCIHZg4zXSGEBPGqX0VFfPe3c0u4UawBtJMySuAZ7OE1b8AiBvEZU83tHSD6qlPex9Bk4dYlFV+BxFXxlQRM6p5buZxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45348}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "1bc7d7df86e0d3c18b5a47919e21b599bc336887", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^1.6.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^16.0.4", "source-map": "^0.7.3", "@types/node": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.1.1_1696404943698_0.5579549790299254", "host": "s3://npm-registry-packages"}}, "9.1.2": {"name": "v8-to-istanbul", "version": "9.1.2", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "51168df21c8ca01c83285f27316549b2c51a5b46", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.1.2.tgz", "fileCount": 12, "integrity": "sha512-ZGBe7VAivuuoQXTeckpbYKTdtjXGcm3ZUHXC0PAk0CzFyuYvwi73a58iEKI3GkGD1c3EHc+EgfR1w5pgbfzJlQ==", "signatures": [{"sig": "MEUCIBCMDL739THuOnLWALD9Xhqb+5Dp6V0SVDEriqqeCt7/AiEA3jyPHQhdvsDb6NMnZKyN3PtZJ+g0g0JNKFGlKzyxs+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45820}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "12b38c3d18b4d2bcc0a9c3ea6e60ddc8ff84f011", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^2.0.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^17.0.0", "source-map": "^0.7.3", "@types/node": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.1.2_1696406820990_0.9234740054111794", "host": "s3://npm-registry-packages"}}, "9.1.3": {"name": "v8-to-istanbul", "version": "9.1.3", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "ea456604101cd18005ac2cae3cdd1aa058a6306b", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.1.3.tgz", "fileCount": 12, "integrity": "sha512-9lDD+EVI2fjFsMWXc6dy5JJzBsVTcQ2fVkfBvncZ6xJWG9wtBhOldG+mHkSL0+V1K/xgZz0JDO5UT5hFwHUghg==", "signatures": [{"sig": "MEUCIQDvbc5zwmTgVTge4T66+2Rs9cYQbJLPi+Zd7v7y2lJllAIgTF4Vdv/iRU4XLoqanWED/adYhJUHUw/Nq5sSnfgYk8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46393}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "0a44bda0b20e5c8f353fa45a0d027745e9fb5b82", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^2.0.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^17.0.0", "source-map": "^0.7.3", "@types/node": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.1.3_1696508822054_0.9828491633779755", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "v8-to-istanbul", "version": "9.2.0", "keywords": ["istanbul", "v8", "coverage"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "v8-to-istanbul@9.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "dist": {"shasum": "2ed7644a245cddd83d4e087b9b33b3e62dfd10ad", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.2.0.tgz", "fileCount": 12, "integrity": "sha512-/EH/sDgxU2eGxajKdwLCDmQ4FWq+kpi3uCmBGpw1xJtnAxEjlD8j8PEiGWpCIMIs3ciNAgH0d3TTJiUkYzyZjA==", "signatures": [{"sig": "MEYCIQCRPBbDT96k7j4ldl+FPcXOCgN7wYWhfH99o338LEJHaQIhANWg84ejVEhOBspeNM3MrYb6q3XPEvy4J1WyFVpwHFLu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46888}, "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.12.0"}, "gitHead": "b0eb41056feff1863d27a1952cf2441e59e4daa7", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "coverage": "c8 report --check-coverage", "posttest": "standard", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["**/test/fixtures"]}, "repository": {"url": "git+https://github.com/istanbuljs/v8-to-istanbul.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "convert from v8 coverage format to istanbul's format", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"convert-source-map": "^2.0.0", "@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.2.1", "tap": "^16.0.0", "semver": "^7.3.2", "should": "13.2.3", "standard": "^17.0.0", "source-map": "^0.7.3", "@types/node": "^18.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-to-istanbul_9.2.0_1700643299937_0.9218566411969655", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "v8-to-istanbul", "version": "9.3.0", "description": "convert from v8 coverage format to istanbul's format", "main": "index.js", "types": "index.d.ts", "scripts": {"fix": "standard --fix", "snapshot": "TAP_SNAPSHOT=1 tap test/*.js", "test": "c8 --reporter=html --reporter=text tap --no-coverage test/*.js", "posttest": "standard", "coverage": "c8 report --check-coverage"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/v8-to-istanbul.git"}, "keywords": ["istanbul", "v8", "coverage"], "standard": {"ignore": ["**/test/fixtures"]}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^7.2.1", "semver": "^7.3.2", "should": "13.2.3", "source-map": "^0.7.3", "standard": "^17.0.0", "tap": "^16.0.0"}, "engines": {"node": ">=10.12.0"}, "gitHead": "46b972458b40ea0d628c80dd60223cc909f81cd0", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "_id": "v8-to-istanbul@9.3.0", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==", "shasum": "b9572abfa62bd556c16d75fdebc1a411d5ff3175", "tarball": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "fileCount": 12, "unpackedSize": 47644, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGBuZLLuzwLTzPuAm0s0foP6kjRESOyZCWLNiDIVOmSDAiEA1Ha/ibF/6iaDNa00KzFFNal6FCQIc1dyNuMsY11Z728="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/v8-to-istanbul_9.3.0_1719044030313_0.5053154597681602"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-11-25T04:35:19.478Z", "modified": "2024-06-22T08:13:50.722Z", "1.0.0": "2017-11-25T04:35:19.478Z", "1.1.0": "2017-12-01T07:23:56.340Z", "1.2.0": "2017-12-05T05:53:13.187Z", "1.2.1": "2018-09-12T22:50:13.381Z", "2.0.0": "2018-12-21T00:53:13.743Z", "2.0.1": "2019-01-20T20:29:05.022Z", "2.0.2": "2019-01-20T22:04:44.859Z", "2.0.3": "2019-04-07T01:31:05.470Z", "2.0.4": "2019-04-07T02:15:15.448Z", "2.0.5": "2019-04-18T19:44:38.592Z", "2.1.0": "2019-04-21T01:03:04.305Z", "3.0.0": "2019-04-29T06:55:41.991Z", "3.0.1": "2019-05-01T06:44:54.185Z", "3.1.0": "2019-05-02T05:10:31.148Z", "3.1.1": "2019-05-02T05:35:29.990Z", "3.1.2": "2019-05-02T06:23:08.092Z", "3.1.3": "2019-05-11T00:22:35.880Z", "3.2.0": "2019-06-23T20:07:42.254Z", "3.2.1": "2019-06-23T21:12:38.182Z", "3.2.2": "2019-06-24T01:45:05.267Z", "3.2.3": "2019-06-24T02:03:27.631Z", "3.2.4": "2019-10-06T04:36:27.453Z", "3.2.5": "2019-10-07T17:52:08.200Z", "3.2.6": "2019-10-24T00:16:23.356Z", "4.0.0": "2019-11-23T01:29:04.141Z", "4.0.1": "2019-12-16T15:52:42.208Z", "4.1.0": "2020-02-07T20:40:31.643Z", "4.1.1": "2020-02-07T21:00:44.201Z", "4.1.2": "2020-02-09T04:52:02.438Z", "4.1.3": "2020-03-27T21:57:49.247Z", "4.1.4": "2020-05-06T23:20:57.370Z", "5.0.0": "2020-08-02T23:04:45.438Z", "5.0.1": "2020-08-07T00:21:22.981Z", "6.0.0": "2020-10-08T04:58:21.013Z", "6.0.1": "2020-10-08T21:15:34.365Z", "7.0.0-candidate.0": "2020-10-24T18:49:47.921Z", "7.0.0-candidate.1": "2020-10-24T22:07:58.778Z", "7.0.0-candidate.2": "2020-10-24T22:13:48.059Z", "7.0.0": "2020-10-25T00:19:26.264Z", "7.1.0": "2020-12-22T00:36:07.224Z", "7.1.1": "2021-03-30T04:56:05.693Z", "7.1.2": "2021-05-05T19:54:49.828Z", "8.0.0": "2021-06-03T21:50:23.165Z", "8.1.0": "2021-09-27T10:54:39.462Z", "9.0.0-candidate.0": "2021-12-31T17:10:09.820Z", "8.2.0-candidate.1": "2022-01-01T16:57:16.796Z", "8.1.1": "2022-01-10T01:32:38.877Z", "9.0.0": "2022-04-20T12:49:52.417Z", "9.0.1": "2022-06-20T20:28:23.230Z", "9.1.0": "2023-02-14T19:00:16.718Z", "9.1.1": "2023-10-04T07:35:43.885Z", "9.1.2": "2023-10-04T08:07:01.163Z", "9.1.3": "2023-10-05T12:27:02.334Z", "9.2.0": "2023-11-22T08:55:00.082Z", "9.3.0": "2024-06-22T08:13:50.497Z"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/v8-to-istanbul.git"}, "keywords": ["istanbul", "v8", "coverage"], "license": "ISC", "homepage": "https://github.com/istanbuljs/v8-to-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/v8-to-istanbul/issues"}, "readme": "# v8-to-istanbul\n\n[![Build Status](https://img.shields.io/github/actions/workflow/status/istanbuljs/v8-to-istanbul/ci.yaml?branch=master)](https://github.com/istanbuljs/v8-to-istanbul/actions)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/istanbuljs/v8-to-istanbul)\n\nconverts from v8 coverage format to [istanbul's coverage format](https://github.com/gotwarlost/istanbul/blob/master/coverage.json.md).\n\n## Usage\n\n```js\nconst v8toIstanbul = require('v8-to-istanbul')\n// the path to the original source-file is required, as its contents are\n// used during the conversion algorithm.\nconst converter = v8toIstanbul('./path-to-instrumented-file.js')\nawait converter.load() // this is required due to async file reading.\n// provide an array of coverage information in v8 format.\nconverter.applyCoverage([\n  {\n    \"functionName\": \"\",\n    \"ranges\": [\n      {\n        \"startOffset\": 0,\n        \"endOffset\": 520,\n        \"count\": 1\n      }\n    ],\n    \"isBlockCoverage\": true\n  },\n  // ...\n])\n// output coverage information in a form that can\n// be consumed by Istanbul.\nconsole.info(JSON.stringify(converter.toIstanbul()))\n```\n\n## Ignoring Uncovered Lines\n\nSometimes you might find yourself wanting to ignore uncovered lines\nin your application (for example, perhaps you run your tests in Linux, but\nthere's code that only executes on Windows).\n\nTo ignore lines, use the special comment `/* v8 ignore next */`.\n\n**NOTE**: Before version `9.2.0` the ignore hint had to contain `c8` keyword, e.g. `/* c8 ignore ...`.\n\n### ignoring the next line\n\n```js\nconst myVariable = 99\n/* v8 ignore next */\nif (process.platform === 'win32') console.info('hello world')\n```\n\n### ignoring the next N lines\n\n```js\nconst myVariable = 99\n/* v8 ignore next 3 */\nif (process.platform === 'win32') {\n  console.info('hello world')\n}\n```\n\n### ignoring all lines until told\n\n```js\n/* v8 ignore start */\nfunction dontMindMe() {\n  // ...\n}\n/* v8 ignore stop */\n```\n\n### ignoring the same line as the comment\n\n```js\nconst myVariable = 99\nconst os = process.platform === 'darwin' ? 'OSXy' /* v8 ignore next */ : 'Windowsy' \n```\n\n## Testing\n\nTo execute tests, simply run:\n\n```bash\nnpm test\n```\n", "readmeFilename": "README.md", "users": {}}