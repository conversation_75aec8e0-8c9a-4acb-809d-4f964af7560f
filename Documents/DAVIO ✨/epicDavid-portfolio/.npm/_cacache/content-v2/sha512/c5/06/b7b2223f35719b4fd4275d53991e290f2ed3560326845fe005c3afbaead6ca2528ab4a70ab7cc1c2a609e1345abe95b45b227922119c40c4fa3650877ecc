{"_id": "@babel/helper-validator-identifier", "_rev": "55-199c288e6c878a2b784407faebb690ae", "name": "@babel/helper-validator-identifier", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.9.0": {"name": "@babel/helper-validator-identifier", "version": "7.9.0", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.9.0", "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "ad53562a7fc29b3b9a91bbf7d10397fd146346ed", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.9.0.tgz", "fileCount": 7, "integrity": "sha512-6G8bQKjOh+of4PV/ThDm/rRqlU7+IGoJuofpagU5GlEl29Vv0RGqqt86ZGRV8ZuSOY3o+8yXl5y782SMcG7SHw==", "signatures": [{"sig": "MEQCIC9eyvwypHHCD5C8iX3T1/YznosxtTSpxI51ecFpVPfgAiBfj70LfXSmzz+p8hpL+eUvciImiGPJcEJ52CsMSZnglg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOiCRA9TVsSAnZWagAARO0P/iy9Jnxchk7TV4YymJ9n\n8U0ItbMsgI9KCMDyRH4Z7Ig1KsXK9iGQZeveJIEqvk1hxU/SpF00hokoxXZ2\nEs5pgJ4A8xYHHIEYtDIe17oeWQw1pjrUMZcEHBMJufbSOaamLor/yik0yvjv\nX98oQy5/Mk8+BHH0GbVR579T0eiV1s0FI0S4yYybKmlgnwcp8yrs7wJ18Yya\nfxs1jsEuD0+SFe83RxgYCPO/Dog8l879inb5+9l/FfJ15lVqIczOJo0VBQMt\nyROfjNj1Q0xpHVM5J2ABXtHzjnNS86uE8YjmeeOHlH18hU9cGIH3L66NIjQ1\n1sdg+8lRVNyVzlUNp1G5YfYmThR0QqU2yPhTRWm2022JiV6HoDRGF6O+BAkW\nmx+qL+jQ4TloXECyq/o9SPH8oJk59+3kyd5YFP2FaC28Io0XuULyqy9OEDYj\nmgvvrTE+kgPXJvkpnGql/VUInXgCEPf7Ymo68xx6VI/yxN6akzX4FSTIkZGb\nStRUnzTkjGD1KRAumoNyomABXP1VweO1rAjHD7/grDUr+HKMH8JS/UQbHlvb\nK7KkOtVTiTFhv+iJQ/7d+LfTY4xBNlr8PMKKRlf7u//y+VcPOefJIXy5k8z8\n/Uvd5GHW4Q10n8nxfN+CbDuuQAXMGCDczUCFZT4glNIwE2miXQzYJTw9I1mF\n0HDQ\r\n=Yo2u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-validator-identifier", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Validate identifier/keywords name", "directories": {}, "_nodeVersion": "13.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.9.0_1584718753919_0.9651042569180548", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/helper-validator-identifier", "version": "7.9.5", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "90977a8e6fbf6b431a7dc31752eee233bf052d80", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.9.5.tgz", "fileCount": 7, "integrity": "sha512-/8arLKUFq882w4tWGj9JYzRpAlZgiWUJ+dtteNTDqrRBz9Iguck9Rn3ykuBDoUwh2TO4tSAJlrxDUOXWklJe4g==", "signatures": [{"sig": "MEQCIE2iGGcHdRq46KMfIvMGuss8VEvw7JPfW7sKKyXJ0p4PAiBogiZNcM76oBmWAaUAajfHn/Ma/p8vIeNKVRKy1qlfpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOaCRA9TVsSAnZWagAAaDAP/AwShg3rLvhxN9eKI7xH\nwjLP7kJMK2rxihQDOQ5BOrqW2/J99fcSHhKpQ4bmuPyBzO15tz00MOG+r0g4\nxC6wJPrQmDsN9RB3TLheedociuiumN6Vyg1/Z8rWJgVI4pEwgd4LUPIWDB4c\nLrdm5gXr+cLiob8KIMSH4FYlRmr/ExOnYtxxu+D9tQNxavCdzagQrx3Xx+xE\nojMI6q4fBxMdoe1Im4750MbYfVuF42ulYkj++xJYLIztnqMnIuIrcWXqpn/G\nbqPuJTlIL7FI+zeElJSdClFR5C1EmAMs9zotvb/E4U3T2khW0rD9WoFmdYg2\ngnl1VucUbFLPjwUNHLIMwbDV38Pu7IUOkGNgTN0AF/C/LcdVzoyCVSnCBGIM\nsb+Zw/pMLqItWXsNTYidXi041Y0ayaiM1vBk+GFsYOaAdFMwTOE38AJWxcLR\nrM88Y5GMkbxuNLfvgACok5DmzacSvsM95rBTBqpnjzqRnKatXfv3njpfFeMq\nXwatQTsbF/ApSAbZc+OEfUXNlCYMQbCcIWfqrYhd9263ZhdQulihhaQhI8O4\nxCJr/0utB9AY4Bsiv8pgpQqOGv/ZFmQVbHreU9569oZu5yu8q+dv3RsZzZYV\nKQYvwvuykLZN7szZR9kIkdilcMo4mHNzrk922ipG8BlKIXeBqsjl73kQOcKO\nHJFB\r\n=Gfmo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-validator-identifier", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Validate identifier/keywords name", "directories": {}, "_nodeVersion": "10.19.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.9.5_1586287513888_0.9235828215531656", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-validator-identifier", "version": "7.10.1", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "5770b0c1a826c4f53f5ede5e153163e0318e94b5", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.1.tgz", "fileCount": 7, "integrity": "sha512-5vW/JXLALhczRCWP0PnFDMCJAchlBvM7f4uk/jXritBnIa6E1KmqmtrS3yn1LAnxFBypQ3eneLuXjsnfQsgILw==", "signatures": [{"sig": "MEYCIQD4alz8rdJ3+J/Aa4NNNR34+AeNhCNJDGRm1bqKBT3Z9AIhAIzELDuGXfMLzOne9y7cmbKpPKAirS4idKm4/TUogk+6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuR7CRA9TVsSAnZWagAAR3wP/0cXSytYAF7k1lPixKbE\niL7U40G00RQOLqeoiPyz9ZpkrwS6V75R1aCVlRXInjbGl8XtT4w6UvTU0QSL\nNZ9/3MoIgAEH9QciCHnNm0e9HvwpcvBdmj2V9/yat7w9kuqetqQjj+0aGPSa\nFP4MvzRxS1Xi6ungCaHrSi4Nc5vUfNybhs81YNXS8OAtv4TvZdZb8YHl5cLr\n6BW+zzoTdBbyLbyRJFVRq/bHP5FQvkoCZkng/UdBmXMIeQGTLTMDprUKoA5I\nwoi9SpWCyzxQWUnuPhXK2A1I86eiPrpznX1Kj4KpGbkWlKq+PDgGSg5PfvNX\n8NTYzTMEZSmQKfIlOTP+BXrcRQL1Lj/r13r562AVqc+AYeAsW/Qu4yFrHJhe\nuPWp7s0I10vYiMTDIz2U3BEzZSEtGy0Eyk91mS0j/nLmICNuazw9i9irB140\nxfnxcIhE2hYcMXCeqj6WQOQpj1b4Q599sc40BlRPT+TJgXKWHmpKSKaxicJU\nI135PL81dXNyDG5fMLVoyO0dSJQ2TxPqFeZDwN+xpNzKcpyQLoXPCGkeMD6o\nWj0eIjfCpuMaU6x2pfGCsNyROcSqA/v6KZcVo1kqsLNvzX8K+A17zr0ZL4mL\nS0duVyNAw5xIMQWXZ+9ow4qkbmnHKkbdmQMjG8J84MVGtdEIyVC6zzwBMuhV\nYcWI\r\n=Ic+r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Validate identifier/keywords name", "directories": {}, "_nodeVersion": "12.16.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.10.1_1590617211141_0.10617264348204825", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-validator-identifier", "version": "7.10.3", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "60d9847f98c4cea1b279e005fdb7c28be5412d15", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.3.tgz", "fileCount": 7, "integrity": "sha512-bU8JvtlYpJSBPuj1VUmKpFGaDZuLxASky3LhaKj3bmpSTY6VWooSM8msk+Z0CZoErFye2tlABF6yDkT3FOPAXw==", "signatures": [{"sig": "MEQCIAnqrU94q0NZoUCA93UHVUq7f6x8sMv1YgOMKPqM6P7QAiAESMbkxRV4kRr5ylTw27HeXTG/dh2REyoJC+mchgp3qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAAXyMP/jZ+pr3GuYxya9M0G7rK\n2eWdIci4T0Jo1y1zwx5ZqsL1Gl2j0X7t7bZG7MYzLukhQajEAVjc63YpE8sm\n3PlvnZ63yk18giGt30uMKjZUMfXKnuB7QTqawS1ndQ+er7ZXRmUUItHstT+g\ngfu12Esidl4VzcQOr/bwdDpYYO8QFwXqV8xodiuS8ljJ9GGgknA0bdhY4JZe\nm7NC5QyogD8ly83kpioWj4RlqP7XH9Z2rhzo3ULdB9JBhfKdA7wC9jF9CQBp\nUOGmY/L/srKBNxhBZzRZROlA2BaMKc/32mivTbluKa1mUJd3gMocSFKLKK8w\nRUJMARxnXSSTbsEQN4frTueH0RSMEl9cMOTac6xNhZUI3XX+SfyOTnCxclho\nuE+OZt4U22TUVSJZSAULbTqad2GCfCboSxtTAbuhTh3amldC1TknNvTbycbj\nkJzAPEv8fh/fbYOA+3o9qyHz6kh7FZvUtFijf6x/ci0gC5tZnpfvO8UoZea7\nX/7ALgK7ykKe3VFrWBAKL+7Z+AGhiVj8WK/ELCukGAFF7pXN4MHPKfbMhw4+\nNMVcoG4M1EBaseD4MBawXPFMkQkUQO14tocA60ufbapZz78C7LW3SPy3Hx7o\nCL0QwBvKcyFpxnbNPUSela3zxC0ayXYULOkK4tXCYOfRYA/rcJm05/rZhbHx\nTa6C\r\n=GsCe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Validate identifier/keywords name", "directories": {}, "_nodeVersion": "14.4.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.10.3_1592600047692_0.340037691683142", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-validator-identifier", "version": "7.10.4", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a78c7a7251e01f616512d31b10adcf52ada5e0d2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.10.4.tgz", "fileCount": 7, "integrity": "sha512-3U9y+43hz7ZM+rzG24Qe2mufW5KhvFg/NhnNph+i9mgCtdTCtMJuI1TMkrIUiK7Ix4PYlRF9I5dhqaLYA/ADXw==", "signatures": [{"sig": "MEUCIQC3/Q16zCW7FB1CYakMQ3Jp+JvDBIMdWESaYlDjtKUjzwIgcui+EK3bgRRJ1vpAzPBEu2M8wGerl5LJ05jOtpSfZFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zn3CRA9TVsSAnZWagAAyUUP/i9TjYTcNJnhd0ZwRAno\nXTZpCx26ZulGddnralt24xiBtWpHGzHLDJvgdILlnGm4p6WbsRq+FSEqcQCl\ncLL089TCZiFZzGYbQpMteC8KswcgYhl5s6kHIHComGuHJPtC3j7lubw3DHWu\nlhXqfVeLxQOVLUZ79gA4DLuz3O7F7dQIe4EiFmWOzwBSXb8pukV6nFGxPFPW\ntLqxenWPvciRbVnHsJUdwAw5MlIybSscxQSNKHj5od5aATTyGC59MlFjMrFc\ndfZwwHayY/s5WbAHkZVdhSDidnoi1wdFHrVyih4jD4XRxH7d9n0I9i79Ggog\nQPhSfVA6sjMsVXzkrRZHDb1JDu40VP7DiyVOdGmcqwrqMvNZBNXp5EEuD6et\nKhjYjN/ZSN4D7SjKO9M4d4uzOi0rsOP/BMCJFF8TjppEnjrRG5W23qU/uUzn\nXfpYafBWuy7s2VKsAsnbUx3ZQuzmT9GHGr97OvbNppK0LvnrERjTp1Mwa9Ui\nL7CnLCssWEP2kW28rX3fkVpL/mWr8767RpACUSU+ijhZ8sI4isPRnGkEWa9c\ngDZAT8zamX9+GZOQW3g5zgYGBgW3iP1GSEqUAHpMa1tgMpoXWR4ZGM8hcC+Q\nETw+6OA0Uz5NyadFmdlPXJN1jOJ50GOFBSJ85fkqug8ve5Xv4iphDjlDz0XQ\nT2Ip\r\n=RtA8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Validate identifier/keywords name", "directories": {}, "_nodeVersion": "14.4.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.10.4_1593522678752_0.5058194301908499", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/helper-validator-identifier", "version": "7.12.11", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c9a1f021917dcb5ccf0d4e453e399022981fc9ed", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.12.11.tgz", "fileCount": 7, "integrity": "sha512-np/lG3uARFybkoHokJUmf1QfEvRVCPbmQeUQpKow5cQ3xWrV9i3rUHodKDJPQfTVX61qKi+UdYk8kik84n7XOw==", "signatures": [{"sig": "MEYCIQCiqXztBdGSDpt6xuq2f31vtwxSS/kyZ+bIEkJORFgClwIhAJ9KGQrpFT3TDo12ZX8WqQx9S0IrZpKjZgbO2AdYYAz1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3UCRA9TVsSAnZWagAAzvsP/3ER7+hkFDd8NJK501p6\n9lJOicJBOA4J0lVmj4GoTbbTZx73qcB+/LoJaVQiwsfD0PzkJkMCNv8hEfUH\n+cOUk40Skn8ee+/4g4iJhBLbAqTLCGvtfDkqK2LV+VJUeQ59ZLs6BuBSrYjn\nOIqSekdYoe4bIA2MegiAgrC2lDPQDZJ+Ma8U+K2inCdcoB8abkIChOajfD0t\na1IYiRKn1nASiRNqG0W1EHLmpeDUF70nH0e92t3R/E5UiDxWljOiJfKQSaBD\nV7Qu38j1IlBez+ppMuV2AYcDpSelQYrVwTfZS2bTMKlk6G5Kp1Dg2IydjzCn\nCJg3Rqmowxfl4oU6muMRImtiGgcrLtYQK1+ZPWczg+562JBkQ/mGsL8ojdms\n57u5hfPwEQ14ojJcXpBSfQ7l8AXJvzCvfo9wYmmX5wOLSoAy+q5QZfc/97AC\n1UTFgYraqO/OF9icPkVseFv0j0QdYn0qBPBZXko2Q+qlqgCpessX2cQ3rKDw\nsO/2g9vxcwzHPeHrqf2E3Y72t3vnJ9e9Zw11wPxLf4U23DoykLZeFFEtyliB\nGjusrCuG9UBUdIvCQC0EX/4mSVUGAk7X7mW/YgSkAvpvP/jMeL2f+q30fwer\nnH74kkwE4buBBLYfP3wBRJbTiRCflJAEkYkCLVCP/B9LrSqH4t16gTF15+ZY\nz+sY\r\n=Zawe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.12.11_1608076755904_0.3526415949267667", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/helper-validator-identifier", "version": "7.14.0", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d26cad8a47c65286b15df1547319a5d0bcf27288", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.0.tgz", "fileCount": 7, "integrity": "sha512-V3ts7zMSu5lfiwWDVWzRDGIN+lnCEUdaXgtVHJgLb1rGaA6jMrtB9EmE7L18foXJIE8Un/A/h6NJfGQp/e1J4A==", "signatures": [{"sig": "MEUCIDbNkHmFGn2hPmXm/1iU0ogBm0m/dXO/cQg3ye8WQy7uAiEA62uX+BSEai6ter1tJaU/ksFs/VscXwfL33GOQrgLw/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKRCRA9TVsSAnZWagAAv48P/ijp/OaK6o/KmTZ/c3tQ\nu2/63DQ8q0ZAvo/gZJq3Vw2J1Kh72ayeqeyt8y9dd1b+5MKOCQjsC6rt+XiH\nA6oNYmQIlYqim+RBVw/bzXWButiqxLa6j4sZstf2OOZpAz88sFkr8Z94i5wF\ng45Iv3ZO7Ow6TdKCbp8GNWYcgEj9uyAkq8Qvr2yoI0x9yCh3BiRK/5fsMhds\nA8Rhy2dEWhCXd8yBFVIjwKOi/UdX5Si1ucuQIyPz56IjjFg81ctq7IGJiddr\n3SdFTY483/CrrZNMj8atSvPCPdNHsVmeVQgQAIjTgkcsxK1rajivxI9b9skb\nYbCvNLe2JIbKq3h1R8Pcm0N9Sa7HRm5feEj8sXxPSKsDkVL+3uy9ruzWaYEY\nBfpqnPc1T3MMtqB8xoiV39S+QtgkXEsEaw9jZjgSYlK47XDmZRmXH8WHS+nu\nI8nt45OCk5VjkmMAK2wezZ9S/sBJ5juYbyleSGYAGAeaSbygjzpG04fiimiP\nL2Vu4Cri8EN4knIGJyFRHSm2kB7jGr/jxSlVOAI/YPMUV5x7dKWW4IQ+W8p3\n2zAi4kj0yN7J4Dbq/8BmTr7CdHJmYYgi7K713caxAAF2knd6CVmye+czgDpW\nigsuq9g3+7I8jfiZFENYOcMzqv81OVM/BUdCKksdgA8NkiurFAjt0IVDxDsA\nCCMQ\r\n=/q2o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@unicode/unicode-13.0.0": "^1.0.6", "@babel/helper-validator-identifier-baseline": "npm:@babel/helper-validator-identifier@7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.14.0_1619726993161_0.5287000418031536", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-validator-identifier", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d0f0e277c512e0c938277faa85a3968c9a44c0e8", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.5.tgz", "fileCount": 7, "integrity": "sha512-5lsetuxCLilmVGyiLEfoHBRX8UCFD+1m2x3Rj97WrW3V7H3u4RWRXA4evMjImCsin2J2YT0QaVDGf+z8ondbAg==", "signatures": [{"sig": "MEUCIGr0xZXefZYjAHg8AK1eb8XqkXOlzSbFfjf5lSySTzrNAiEA8H06b6B3VXZuP4PdKACz6YLtDiEi/25rDM8NaJR45FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqVCRA9TVsSAnZWagAAJrgQAJGbaAIAmsz9928BROFT\njJL7kuIgUf0wcLJ5KB8RzUBqZ5vAILCddxCKDuMUERB9VKTmkkxtpgV43/S4\nAaj76roCxM3Qb6flzCap7FR/I9p5zqv1x/iHT4LhRMTpBWy8Kb2GH+j64P5g\nYWZP54koOErrIToRq8NKCrmhvg09qpaaWa7JnUGSk2yqoRDpox66wrRTSv33\nkkldSy5UtP7vZ+v4HQDmurKGpX5VfaiOQCkcecFimqpfvjn+2b9trW0Weg7P\njsRuLxhTo6qg+0AkR3rd4ZblG5LIYviWGwXe+gE5rWilsiZwXIhRDYH92MGH\nmWqsDa8Np5xgjbiSRYJ4pBJPSIH9TAminLa/V3gb+4zDaQti4zk499WvLSyw\nm6wjE7Ai22sZ0nxyYpGrGjSlRCy2a24XgEs01lwvhA+714LEuZKcBxgYhyWb\nFwft/1NgollTG/ucjvkZCUJF2fh1LMP7jeEu8wLIqM6hqGjHcCTON9oBPm3x\nm0vBlRY0bRN26fGhgok84zbBslel4Wwsc7h+WMnVBfOj5rmyUkXPwJHEbr1r\nTOLW8wflJrQ+pxRhJmDUUb7Zngubx+I8oFo2LaDMJmnUygNIGsQ3vZ9vtzoY\nuO4+y4XkkFN4FeSyEUuS0b53w7GxIjG9na4NIQedc0SvVs+kiwarggrglsrJ\naJ4Q\r\n=WOyT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@unicode/unicode-13.0.0": "^1.0.6", "@babel/helper-validator-identifier-baseline": "npm:@babel/helper-validator-identifier@7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.14.5_1623280277234_0.7036901637197999", "host": "s3://npm-registry-packages"}}, "7.14.8": {"name": "@babel/helper-validator-identifier", "version": "7.14.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.14.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "32be33a756f29e278a0d644fa08a2c9e0f88a34c", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.8.tgz", "fileCount": 7, "integrity": "sha512-ZGy6/XQjllhYQrNw/3zfWRwZCTVSiBLZ9DHVZxn9n2gip/7ab8mv2TWlKPIBk26RwedCBoWdjLmn+t9na2Gcow==", "signatures": [{"sig": "MEYCIQCPOQQ/bnRAIpdp20CZ5H3tr5j9tOJ0Ftr3N6hp1NlJjAIhAM7V5QhLd7CPB2RoS072065mAK+TykJLTF39Aeyj7McG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w+6CRA9TVsSAnZWagAAZ0gP/jTKEOvIlaTHviLB7c1l\ndxg5/ggDneMPbrFMM2/2AD0aEmKZ6KhnDJbjv04sMzhpy19XhU7wlEG7ntHy\noOt5c06CyEv42Z6PVYKWWU//HVt0drJ7H7QjQORSCSyFh2y4TQA3W6CVr8gX\neabeHNEnx/5CR/lnshgVuHull7yKOKO9sWp8um24byCCTxR7gnUH4lB4RmBg\nSgkBEYR2S4E6r+JkwtZLQSsl7pQBLEa9/C/7Hnku7LXtI7jYZadk4EmjS/vy\np/xjhYc6mmWrIBECcXYNxxvP9un+b3pltM/r1YEIHx3r5guuivf/IwOEXek2\nSSOp5YPiCubAXg/catltCAPPW+k+QonbkMUD+FmF8ygDk6YNf8Gw4REy/yhl\n+k/fedbK2dE8ssfnBXNbfWQwzz8QiD/VrDsow9W6YVfuQX3y0RQd/gq60w1z\nvMTz79cPxyZhn0zxC4a5a071D1E00M4pbKthyf2mt8y9bJvTvlRl4fTG15LO\nty9SVcNNeVTZLrphoDwJXubsv5GDE0bzt2tT8b3dQO+p2EoNFxxoRSXlGNQP\nHej5NX1OPZYguAbdJou5pa1p/N6r87/e+/OmGVMi8YbPdy8WKpzxrj5fZca5\ni/Og4pEa3bJnmKTcJMrm37oupgJBd/2FYH0Vh7HWOxqj3qEuza1L/DFRtnN0\ntR5r\r\n=vmxw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@unicode/unicode-13.0.0": "^1.0.6", "@babel/helper-validator-identifier-baseline": "npm:@babel/helper-validator-identifier@7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.14.8_1626804154803_0.2522255907407023", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/helper-validator-identifier", "version": "7.14.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6654d171b2024f6d8ee151bf2509699919131d48", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.9.tgz", "fileCount": 7, "integrity": "sha512-pQYxPY0UP6IHISRitNe8bsijHex4TWZXi2HwKVsjPiltzlhse2znVcm9Ace510VT1kxIHjGJCZZQBX2gJDbo0g==", "signatures": [{"sig": "MEUCIEBjsxLwLuOdO0GXuKx2PqMEN9PYULlsbIBSED6W7eS6AiEAsjGr+GTDabkW/Q67NpHcuvxWIdQDj0MUcScHz/FBWpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLmCRA9TVsSAnZWagAA9fkP/j2dJT8l4HSH6YhAqDqV\nA0BvcukuY7nG3tnkYmS4Ndq1I4qBqMjczcwAliv0yLujyFBZ90Q4tj2gmGcc\ng2qeD5cT5EG2RGeYByPiuqE+8pngYXa87NTPfwON0TrabjAZ0MyHxpC9k48f\njdG+mDNOO3uekCSM3EYka7GsFeKuS7CebkeIIt4zfN48nzOh3/k3AzbDyl5d\nqmbw+1/TaeOo+iOy2NVfL/eDSoyOy/+tDPsEqbO09x+LF2QHr+/vcsp8Ksrs\ntazdfQnq7IEnJrp7sha97qjs2EqCu9CNRMfivShfJ+W27n3qbVWpLBVfM6qn\nyKsTKx8e6b/z0Hrd8edHH2FvHRdjTkTQ/F41VX28Pik2/icIo7sUmmP9nV0B\nTGF2D5JIOGmq5HY5ynoHOcRqhWKUMYEMJ8HFkLJZS0RRZga83nR9gi4KeBn5\nguKR222XUorlJhoaYDBXs1awMRV4HZOVZXUcqGgaQLIZSwxuZol/oaxdR6oT\n1Soh1hdLljsRm9TOy5/fWPScjZk7pwh1ZR8q+YiGNAJSMyW7f6SbnQjNY5nL\npB9hZ78dk5KtOZ67K1Z2AdWVC8nYh2a8tziOzj/lykjJlIrRNqOZWAmS3QRT\nJE7POzctiQeJzMMMucEOtkoXPfYHrTejhOtB2r0Usnc3dlubjWKl/YMYJGQA\nD2IT\r\n=FOLp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-13.0.0": "^1.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.14.9_1627804390847_0.3658820223244814", "host": "s3://npm-registry-packages"}}, "7.15.7": {"name": "@babel/helper-validator-identifier", "version": "7.15.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.15.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "220df993bfe904a4a6b02ab4f3385a5ebf6e2389", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "fileCount": 7, "integrity": "sha512-K4JvCtQqad9OY2+yTU8w+E82ywk/fe+ELNlt1G8z3bVGlZfn/hOcQQsUhGhW/N+tb3fxK800wLtKOE/aM0m72w==", "signatures": [{"sig": "MEUCIGEpzSr9l83tVg7gg1Y/XXU7CfYa0kxQK5hrZ31h+ZmfAiEAouOPDLuTrfUNlZflmnmwtCvZrXedNWh8JNl91+sWO30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHEPCRA9TVsSAnZWagAAjhQP/1ZdVhenAg3D4HjXrbvy\nZGU8Iu5gaq6w3RrZxuOZopadc2iGca4WMCmXY69zsLVaijdE0PnpUp/J8paJ\nNmzgD1yOsmEjj+7NvhU/tlNrJ31xgcZvzeBrVxL5Y0tDB19h2DhgPOHjZWcg\niBmEgne3zD2bXpLd87IIjaKCkbOQziBip9gasmfzs/ihI6hCJDL7/rwoz3XS\nueRIdlG98frXXGwmqJ2u9BJkDgd9uXa/o9lCACxMu//X9dN2NIqv33wzKPwG\nzeaUoRD5rybFYjvOZGnbuoC9wloQKXuglzm2iZdb2lYXrqnSN+MJioAu7XhF\nofoqO1/lJxe/LVbj2hQZPs9OCoTqIt+YMowTva/JkQJact5bRi8a87QRyWME\nafISsPa/hOj5dZJNv1+s6dvPmvhVumaesxovEv9Dy8X7pAMcv8ZL1cbmjwPC\nd/c/piow/Rvl4nhslZtYWE+6vhDrSvFsQfLyHfhWZGGigddSp54nh0XgqSOk\n9m6pk5m3bt2RZKI2BLh5iO2y9SsZqhigc3oSFh4VEe4cAxbgq11ZN70C7fe5\n9Tk1WVZjAXIE5lKH5FoJUl8VzU+aR0Clv0G+AdKeHNKmRwe1bPGdqeXIfGx5\nJPlfrXSr3yRSDzs3ugzdtUX5OmxqChfmKsMNLjQ+pZUMdNehSlaudLUWUv4o\nIGUk\r\n=rkcG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.15.7_1631919975896_0.1177393285591739", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-validator-identifier", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e8c602438c4a8195751243da9031d1607d247cad", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz", "fileCount": 7, "integrity": "sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw==", "signatures": [{"sig": "MEUCIQDeOHabOZlKnpTAnpPAMDQ0LSbv+Vt1v0/AlCMdyArJsAIgPUWIvwtQ0Udd15tnCML/d+SoAA/E0PyB7TfummClHjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzkz9CRA9TVsSAnZWagAAspwP/iw4lYANLQMNk8PUZytD\nxclXe1IimXruXPbKUUp1JuWP2ceX5yMEJ//Lc9ujn3LwXIkEFtwreAEd8+ct\n5MoNbqB6MKdBxFpdPTDQ9bdkqXoJwfhiukTLbgy/5mhMRFKnxp1lGw7O5ih+\nQBphpl1uc4mZ5+Ol2kAGJfdrk04vGQwevuijxX0MMz8AzogyBspYuwx0TJo4\nC4jZh4CZTfqZ/JjNUHRpru5+WQMz8IxnDjbPVeoTEFv4Zb2IDHysaVUqnKB3\n5SbH4OEzgXBAT86DblEteSJjb+vVYKg6dDwp14sRTHQk0OwT9+zzlgNHOs2k\nhOsJ1BoxPk7X7FAkhqoUWD/GtH8m1hd1Fsjs+kMjhxAKchuXk7y0skoCqG9D\ngfzhq7Uq7SE+cX9DWVoAL6oCnKayHBN6aeE3IFAOooS8P126UquKVtMEhs6w\noF1dhA/Wi68CUo7LshYs8ZE48lNUXs00yJRAeEnXUF98502TIw/j8c6Gke+w\n6nxWMcogOlj/sJ1TVCUfAzrIdOVjVZHl/Yq4YJcijMe+mGAJDRphH4M5Yy8u\nbR4PrICbxxyq+ZmpvTKVFRKQh4t2UI16hZJJSMYt42+eYA47t8mCTTdiHou1\nMaEqhoCLFU2GNqrL0qv45JdpFBdCiy14/NNiVdXU1pI4TMY4R2t5Roao+n6r\naqRU\r\n=DHQh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.16.7_1640910077376_0.3028733183734953", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-validator-identifier", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9c97e30d31b2b8c72a1d08984f2ca9b574d7a076", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.18.6.tgz", "fileCount": 7, "integrity": "sha512-MmetCkz9ej86nJQV+sFCxoGGrUbU3q02kgLciwkrt9QqEB7cP39oKEY0PakknEO0Gu20SskMRi+AYZ3b1TpN9g==", "signatures": [{"sig": "MEUCIQCI4V65AYHlCI/n5e+/jXG2ZwKvfkVMJ7LvbX9fKcUXwQIgJHZxkKGgmjhsGkzgBWl0B2sZ3LPpPdnY1ygwAyUTBZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRfg/8CJvDNLZsyylE9QcPIyP6bVB1PVjv4npxg64X2SPyXYn7vCvC\r\n501hTRRZ+ZEHo1IbG3WpIBvp6KCyxqONeYUtjaNttWq6eF+732FdwXY78pEI\r\n3kaNf5qUQ5g6oqu17T7UtS/rn3kkOxXQk7kyGyWPWtawPI38VmrnZFnPMOMy\r\nYFNlDMtoQyONyA5MXvEk+Rnnpwihljb1u4Vw0E8+OP2D4bm+H8zgHSzSSTl8\r\ntwNBW99+aAxsZYtwpF31LgLZeeYYoNOJETASgMknhRg1pQe0wXO98jf7h26z\r\nlmDb3iWurxSz0gngXGt8bDEzPJw91ZEKylWsLpyvSVO73cU0fbXUjJtUEZMv\r\nvoIgSo4tX/GFD3iDzW+91ZZTgKYWNl+0SOBYttbnmiZjimIr2C1lXvekypRg\r\nsfzxcJmJZ1MwD/hqyirDovwvT9Fk5jJq0NBjCt7kGnAKq+5Tu38bWc/ZurDP\r\nniabM23l/EP6Ei3xBH6eLCSJFTUa1OtsIHE2Ba5mC5TLX1NyqS4RXgr55h4x\r\nMnOaXXccoQxsj9lxa85Jo1BbD8S9FOXJxT7djZYDgcrLh6D0ZXvhkMsobGoG\r\nVctxlOoxkBJZz/sG4TTNoD6m+kgZAETaBjaHCe0RHePb0Opmdnpf9KGMwRLs\r\n2LrYnUSkcNy6qE+iFkQ+zKj81kyyFbj/KhE=\r\n=k4lt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.18.6_1656359384967_0.3581104062900762", "host": "s3://npm-registry-packages"}}, "7.19.1": {"name": "@babel/helper-validator-identifier", "version": "7.19.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.19.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "7eea834cf32901ffdc1a7ee555e2f9c27e249ca2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "fileCount": 10, "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==", "signatures": [{"sig": "MEQCIB25sftBfyZRotRoPCBWw0WgAdrHrg4Zq4CW7GBlzei9AiASbPhvh/KrNeSMyH7/j+yfyLvR1e8ajB6S3+/MhiescA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBnxAAnui7K7tlpWQFmlZc2sQ/JcFW8I5YlX9D7Op6/NVBZp2hM51G\r\n7LnKW8A0QtLa9svjiAO9yIrum4+l+N0Exx6ChLyqMg6ogJaWy8Hcx3KUeVDE\r\nW7wV3bK99NuIpmYCGxc1j9N7mEPJBwxTp8trtiwRrme4yIsSqxsw8MOqOKjo\r\nAO+KCE8BbQFYqeluDF1w4m+3ZlFpwTAY4Wd6A+oESTSwG0Ngy/pUKyaPJtUt\r\nb2pFoF5gkavxBunCZiKm7VxE76lsZzfokuzSNV4fn7uhyLHvNbrlJz/KvLwe\r\n/w5Zff+3V4cA79fhY4D6c8SVgCk2gouQbw7q282+NkbhGIzLE0v1LEI0RwyS\r\ng8BNee6/RWn7n9s0izlCzHzp9jThx5p7RoAb1dMrO/fpDk0RSqWzTxrUJt9Z\r\nBMCH4Dz51X4lrWJJQkyNZCUT4vXIgVowytrjNac00k3pNkabU9ecrO1CK1BV\r\nn5oF9ElZJoXYVaVW0PQPNLI9N8tWBMIxlvjFM2nGmj+DgdiRISU87NpCZRrq\r\n/HvPDZzb/qZ/yIIStl3PWBSGbHJ5hcY1RI9cjQ6fU9islBVEp0S434CM6fqO\r\nrCJiWuolSNAxzjtezYdYan2Wk0RB82la1TXOnPYFIEnSeLdsN7x/gX60Zt/1\r\npjSKogqcbhJAgwgq8XVKqtZOtYU3W9zSEoc=\r\n=ebyd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.19.1_1663169350242_0.65735442443565", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-validator-identifier", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bd7a6ad92abc488e8c0d9539e7fd3653511950a4", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.21.4-esm.tgz", "fileCount": 11, "integrity": "sha512-y8Vnb87h1k+OH5Ugd8KZ68C3GcaVz5GGPh3Vre6BJc1lS9ZvxvztPBG7W2nbnFt+MS2gd0zdPST5OO0mnsnp1w==", "signatures": [{"sig": "MEUCIByP6MopdJjn6zHEUfiNb0LzZrNe/47MvfaHdEGUjHTWAiEA4obFGkw9wgb2KUoRbbZz5JAQF1CsUAu4LOwQY7QAX5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC98ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzYxAAk1MmxTbpYEfZDlIuN7ruCTFNR3y8nyKlVvcGmH3sNKJMwnIQ\r\nbN4pe436Um2vRpuGuFKd3SqJrAt85C7NijXeZSxVjEGjeqBC+wOs3+ru3hRl\r\n5kam6HFZFUozKvOrcjw8nXuOKgnMDBsBkGuq51w+nnW9hewJwyU+lF/pvjEu\r\n0qppCK4whBQKGDSaRX+THgVfIa7BmXgWth8Uf0De/L8zQfl76auItRp//XbV\r\nlNKBzM021zf6RjjkZEx/udFeToYBR+1dFy6jdTy00DzKB20dL5fFsGBoTQx0\r\n8J/Q5h8/QlA47WDFGjg4P1KBfmooyctaxXGnk3c3WPt1FQvZ1rcnmPrPhkjr\r\nxN4q1Kdvwm1qqM8I+U+Tb8AluJd1aMsolQ3HO2JMkhw7eLJhsrymjkSrsMrD\r\nDhIfEWNSvVgTrMNAJGw63bsyhP8xiFaVP3LzDj3HDoMm4zDBaMpzpbh53p+s\r\np8LyVDdnjgkjkzGnqOWX4lHgO879V/qRYKuoK7MnYyX5D0JclSgVvl9VXu6M\r\nO8wB1mH2ThYo7RgKao6o/2VxaOkx/lRylFQdWBA6OSAuvYbODc2XMkSKVunf\r\n/r60CvFIqXc3zHL/isU24g5pgjxWgi7sxnZKt76wV1nikd+Ml3ONflTepqRf\r\nnT3YRXJmUK29EYBstagg0ma/GHhf6fP8h70=\r\n=Nur0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.21.4-esm_1680617340660_0.06636096427258464", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-validator-identifier", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c69ea00d9356976e5a5e70f93e5e8158f76e79be", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.21.4-esm.1.tgz", "fileCount": 11, "integrity": "sha512-L92wX7xeebKRvWxgCKFn6wRKUlmDTtSFWvDK+YPEGtW/VFt/lvxHHSC0TdlPoT99YC9cfqD2/qt0CfRY/RgJVQ==", "signatures": [{"sig": "MEQCIBUmeqsxKqQBlykxArWv997KUiSyr8JSLefAmOorsQMPAiB9QkEBweJMbMjsZYpCTygrePDUtilbQEhRh8V4IS0SEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHrA/8CdI/qYVBPmnwtJuq2ZuUidxDa0Kk5ilG2gY5xD2ecKQoXkRo\r\n/ByZVV9FqUWvfzvGdrHeyZg/3/PiIlpgi96NQ826bl+Ovg0oH4e3UB7ROdce\r\njQDCDHpCz9660Vnwmm2JaiVJKiv7Jg0FHZlqTzhORlMGp2zMkYiI09iZI4i1\r\nbUzot9kcbTGukMKwlTpHKPEnjGLH0/Y00rn7wEAarUEZsuEsVssglMtFL3tL\r\n+B4u2cpODFbpmIaXZPjB67YB8vCB46NGKmr7dP6CpHilVTvQ5Rai3brztFRh\r\nh5+haTe0VCtV564LpBzCok6uBsRp+sIdYgAWh3vq7nR359CwjqvIxknHt6so\r\ndHY5TkoBjDrS3WcQmmX+vK3JJJkiOwqQyBjnNbciTWL9HEl9EdVpzQvefncG\r\nxTQLgdivrCLfaRVD7xSqQdQhmr7j6b7zWyC+rS9sSC7lYZOrq0QRB3p2BkAY\r\noIdQXIRMRHy2gdQ+Oz7PRaNrhFgyrZOxoBZU3m8LrXT/hfJgAPLQy9DmwqH0\r\nBkM2fYBFRh1FgwVbIKgL8hSjuMO+xH4r+Bt8DyRMwl3U+V1eEdReMrR99FD3\r\ntkbxjXhB6iCu1ApogLZe/hABxn8TOYwAgPlXQen9eYYMt/z7lhK76etNKVmN\r\nY+g3g+X9ohO7jZmfp+FxUeqlMPLvkMizpJQ=\r\n=SF6b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.21.4-esm.1_1680618052494_0.9639273493367222", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-validator-identifier", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e1bc4f5bfc4dd08cfc59916abe8f9baecac2e89c", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.21.4-esm.2.tgz", "fileCount": 10, "integrity": "sha512-3l5DWDRh55yBX6r6bkTMnUSYB0t66tkHsGjiXKHa4amhE8yIKDYFQUUSdOvvfUtXl6vo7nelZFjs1zxptD8PWg==", "signatures": [{"sig": "MEQCIGmToT4fMuprRJatQ6SAQBTlkHMK6/7jxpKhvjnYL539AiBWas9zdiUh5Y5UA9KzOe5BGJ8k6RNi95xXDe7PqXBgYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPtg/+OgE8eAT+cNWsYiXxVu+F+KhLAlCMpO4/h5zIzr6kXc7Y1NDg\r\nbj1iKJ4UoddvZ2bmqKAK2k0DMqvpvocVEZ39nkm1HRRlLSh1O46z650a7oio\r\n2yxdnqnsAgQkexsOT64i6KUlxZ0Gf5YWFVwV42wtQoQZwsEhpgxecpfIxG9p\r\nPT01rP7dDjclXyTCc5fD/9zEGXK8du2AKb3nL7KhWm4yL1BA6tK4s+BtDL30\r\na54qNcXpb3WJcv3LIWLYySDObXXUtSo/C5peAVOWQ8YaIWfd4Ev0iphO6XkG\r\nk2SLSISpiCgW/QabOdA0aK0sAqBtTqKJCncCesi1AIdnmWZG5JFV7RImqzkR\r\nZW3M6qXaUuehLECecHdOpgpDcuzayUvnqaKWZSQTSfGqZu8JwZM8uVUkBkcC\r\n7/D156qXpqbcvNsTWTiaVxQUbMUX1srP1hrd22pVZSX0FB/JI0jZammz9CS3\r\nMOzh3fVOkNRVHUygkC3vF/FbLahEVqqbB68EpsyiMB5ILF2vzUyC1nnMtJii\r\nUV9V2sRoYZbrOTyGIjL6jVAlpGybIgkPcvHV2oRG9fKtrv7JgoAQztuoSOoA\r\nKb4qMUvVF2yGAcGFGgrAI+OHr5W+oruWmmco3qZ+SfvCJw7Aqk4PwGk8krOp\r\ny2+iRvT3gQvPU49Sl/UIaCzspqmczIZCmEw=\r\n=wsFc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.21.4-esm.2_1680619115352_0.07269721580465371", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-validator-identifier", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "052328c66b4df6519465759c9acf62c4a7cd705f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.21.4-esm.3.tgz", "fileCount": 10, "integrity": "sha512-Msg3fxl2m9E1XBHuPqQ11oglrfIi9meDhb7Bg3ZG7eStaAfe1+jWtnFxSGw53rLWNhxUXGAtE71rVY3U4PadKw==", "signatures": [{"sig": "MEUCIQCaFR9Un75oz/5SanZpmbxu0ZD+Gnsan/B32BQLTg9wTQIgJIOW0OcP16zGO9eEGJMtuQU3bnB4pPnIqQnjcuEbbDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoicg//Z0SY1+KJsQUPOGxfG25Sr62CgwrD8sdxbrRoTxnT469GPhNe\r\nQBDRgkuKpEMo0imKz7oYYMwl2iEJYJe0jvYzFkeKd1RNoVsgd4kZds9p2wJx\r\nmn4cGz1ZyKQaV10mw6Aj9XPO/HbvVT24VWS9+n6+DQu+1fStLJgCRojbDsgZ\r\nVoWKDuW8W8Yq51UFN5wsPycHykFyNwJEovfsLAHG8brp3FVKCsNQLi57e9CG\r\nqLF1diNRiJI4XwpMPp5GuxAGtg69xmqdfFXzhpeIj+5DYZACZLvSuzs/rY47\r\niS8svNrpaG8Jx2dtsK0z/kFq8O2IPx0IDUHR38o3WU0uQqYB3EJQEklL6tvF\r\n1L7YxvDFeTUMaLYeNBdFG2ttiXyAqo+kxo+1TAb9BbJS+BHHGiAtzHASu75j\r\nsU80JQKfGY7V51L/xgOIvpdKjbpdmsLN5T7V47jX1WnUM5bkTkNkzlL+oj6z\r\nWQBEl9T8kiY+HybpmT6bRicEFmtPs4h0nuxS0x7KMjjoEat6APBwnYv4FNSg\r\n929ar+P7f8UPC4aJ1bU7WEVADilC7u5t60xtLr7LvQUmhoZEZcQ1mG9L7W3N\r\nRlzpC3ooBjT8iInnS1ufsX8UzilqYMYRTNLruTmWAGxRNlOMbZ/UV7FXtlZz\r\n5cGZjZdTYUYb//+QdiGg8nFUK9tPCaLBflQ=\r\n=74dm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.21.4-esm.3_1680620147940_0.41270874994867146", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-validator-identifier", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f377f7d5a6dbecf697d0b04304531a35dca9a3db", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.21.4-esm.4.tgz", "fileCount": 11, "integrity": "sha512-DP9RX/xVkBSK/fYzCDrgjtkx6cf2omQABZUDwlraoyFKnm7F8SiWsMH/AwXpzfieeLg1wqt6QWzNBOwlwiHr0Q==", "signatures": [{"sig": "MEYCIQDRRo7Wpk1VCLZa9OtLgsLds76YyOplzupFn0LtHy+qTgIhAIYVnOgqDx568nT3MG+v9wxU2dzLjhxjNsO4uGxadTAe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD59ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbJA/9Haw7JehEHMQ7pfW8nYmQoyUvu75AiDE14ZSnOYTnVPV1yqhW\r\nQrqYebXubU9hHUvXBa7Kxa/ii3N5+qqgQrnP3zpRNBAmSD3oshYSWP1x1mT0\r\n4fYNW9qtbPTKWvdChpc2myImgh4suYPuQOscJwajdiv2eDEtl51tB3O4TS4A\r\nmp8QIG2KFiAoXOQt5uIXajbN+c3KgSBmiUywr2Fogxrki4JpMWvlKClkXvwX\r\nsagaWuW0wMcZmuB9V0EM2YEt6kUx8kgPIpTNOxWJvdumVOwaPjoeVqNSZoD0\r\nIvwFNSBaV4219QDaFmvreWZV5NP6ZD9VdhboHnSRRy5ajNPtMTMB9laaDfbu\r\nVR4Uz7TNb+/HWBbc+FbZddFP7Lr/UT123Dh9CdE/KyeuTXWtv6XSmf9dPN9p\r\nqn0+CWkkuMwDRb9HMn5wULF11p7ndgP6SkmS73s3wIL1y+1DL4Hzyl1vn70j\r\n61JrisMagLR0eRYyWBctxKcjHqj+sB00WDXnhQp4i0HGHvyJnsRjm4jo3RtI\r\nvJW49WM76utw4sIS8N1RldF0Z+NIGb/y7chjeE0o09PuNHvBI3XclkwwRXML\r\nIIvv8wDpJJC8PwRX5iy4pmT2r0N61PwplKZo9DKkRwK8tympHzQ1iLEuQbuF\r\nPoLQmBeTszWmLRs4KHe2Z/AV6xvvsNj409o=\r\n=+b8Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.21.4-esm.4_1680621181588_0.4294147456898014", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-validator-identifier", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9544ef6a33999343c8740fa51350f30eeaaaf193", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz", "fileCount": 10, "integrity": "sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==", "signatures": [{"sig": "MEYCIQCNoDQaJVe3UhzWNxz3+sMTjvG/a6jkgJn5NC5q4KvS1wIhAJHvkXMo+mm+0eSGJ63HZxG8IlHaPmW++ysmv4p1GlXG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49042}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.22.5_1686248461937_0.5433177172484251", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fccdbb7988148fe18eb199042f0939dca8fb1650", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-u76CWEpooIkoxRSzzJ0cNAphpnT4I0rWcrC36TUBsc4E5TnkdqvH9wJfSuLRm4IeYRCKhlq6EFA70zdYEW3QtA==", "signatures": [{"sig": "MEQCIGe6Cab0xMKS1vOEJMNZTjTsAfGpxS/aGpX8bAWR+Z5CAiBvJyrRnL8Ie/G52mrfAeSm18GD+KaGy8ywzsEPazhR5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89840}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.0_1689861574118_0.6718244956059349", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "badd89ff00008b84c6292d2b36e49ecd80a64987", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-sNE8CRYwXFczisuxlBjFd5mL7yEPXv7+0W6U2hMyMb0BMTec5R5DPb26LCddy65wQHtVWBiedgw3Gec+ZIfj9Q==", "signatures": [{"sig": "MEUCIQDFDhAG9sAhnlFzjzdxnkKf0BH/pi0/aL4ug97UvXBUgwIgL2o0O3wpCMfBDE47qu0It7+oIOiQP/l0sFA1UzTv0EU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89840}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.1_1690221055204_0.13423258603365285", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "18df01415baab3c66ea8f19460bf37ed48f2e411", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-us66RLKkCzBzNJ1TVAHe2euihYrYaXxbuQ7yJogKdBKBZ0R7gHEpB+e0ylaMGsmLzqlpPUyWWcz3OPTCsq1FDw==", "signatures": [{"sig": "MEQCIFSqAF2GcVxaSjdQbFItTpAgBKlpgeyCdxZkTYrHTVG+AiAiXUB2V5ggBKwUipcbIt2npp8sjVjhq1Hz+w6I3J3cBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89840}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.2_1691594072186_0.5518588458686047", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-validator-identifier", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "601fa28e4cc06786c18912dca138cec73b882044", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.15.tgz", "fileCount": 10, "integrity": "sha512-4E/F9IIEi8WR94324mbDUMo074YTheJmd7eZF5vITTeYchqAi6sYXRLHUVsmkdmY4QjfKTcB2jB7dVP3NaBElQ==", "signatures": [{"sig": "MEUCIQCgPOU3H1umcgqVG4M3d4/6YxV7/9zEiIOMmZGUZkBADAIgUuxEvW84I4QOJtzO0McaCCmePkLwoNemKQPMjLdoc80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49064}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.22.15_1693830301740_0.3175490954988751", "host": "s3://npm-registry-packages"}}, "7.22.18": {"name": "@babel/helper-validator-identifier", "version": "7.22.18", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.22.18", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e86f7570ae97a02b954048c921f5378f57e2c789", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.18.tgz", "fileCount": 10, "integrity": "sha512-Tm70AqMTRM1vSr8XHaMOMT4g6WbxFvgs8GNQTxLCNqhRZKaUMR8W9QgrwINrtCxKj5ztpCfur6Cn5jvvOdpEHg==", "signatures": [{"sig": "MEQCIGeDLxdZWn3xbTfwabqQIFWTfDwuWZ/oRDzzBGSU2pDcAiBsTrwlWURSPZHiUAzgkc21WaeTaStrngdFVC8jL/tD7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49152}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.22.18_1694707123960_0.15679879735175573", "host": "s3://npm-registry-packages"}}, "7.22.19": {"name": "@babel/helper-validator-identifier", "version": "7.22.19", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.22.19", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2f34ab1e445f5b95e2e6edfe50ea2449e610583a", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.19.tgz", "fileCount": 10, "integrity": "sha512-Tinq7ybnEPFFXhlYOYFiSjespWQk0dq2dRNAiMdRTOYQzEGqnnNyrTxPYHP5r6wGjlF1rFgABdDV0g8EwD6Qbg==", "signatures": [{"sig": "MEUCIQDYbCVxUOd8sQ03/pdXTBm6qDboXKD6kRGY8SuYtyDg2wIgYkRTo6ioT7cnfeL2MDqMNGRLaM+qzLsPWwIAvkkKP7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49152}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.22.19_1694709121424_0.31665535185133176", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/helper-validator-identifier", "version": "7.22.20", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c4ae002c61d2879e724581d96665583dbc1dc0e0", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz", "fileCount": 10, "integrity": "sha512-Y4O<PERSON>+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==", "signatures": [{"sig": "MEUCIFXiFVKNw+kXCqIufC1BD/uCgsPwE5x74wyo2aCwIRQTAiEA+VVnh8uWdLkQOXCUeCTaNoSaL19re6SlW47TEOdh+U8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49103}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.22.20_1694881716186_0.37240542084552053", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "c12ce7c230b7554d72dd6a709b8870b7c1aa08dc", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-Yiljt3cW1ztluXzpdnrZHNFAmFFRkTHorHsUum0aEByBkxjvtCoTtTp9lVAif0ClVZV8eBkpRLbpvBxDaAeSOg==", "signatures": [{"sig": "MEYCIQCyDXyV+5PKlXjOwYNBxls7/n4bw6db3b3Q3uKBQa/JsQIhALE5O85KmP8QNjvk9M4kR6hGHfZ6IpVc/lcOvWQQngUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.3_1695740184474_0.513022062381538", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "44ded699f5c349f9206c1ad39b24137208a15824", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-nbcDbkNVXaLNg5Q4fWLMlXrOmwIGkeED/lpqghIE2CAEH1A9MaC4pO5iY0nnhGW9csEeWLJYvMuenvHHuTcosQ==", "signatures": [{"sig": "MEYCIQCrZi6V+bAxVpD31NRH9OJ9Ek/f4asR//yIDc2AiYDkMwIhAJnR1X4xZNEIwqIECtG81kf/u8qSTCMIneIX52ug/lMr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.4_1697076352475_0.9085024299112061", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3acfc880a443fabff73747c9a4e240051d64d0b9", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-kcoGwdxi58Npdfof0ghzycZQ1n7USE0DGNDCvR6f2pYxmAQpxrauuJnGIgOqCyOAT+zLQAnjl/kYgLCWYTs6QA==", "signatures": [{"sig": "MEQCIG5mow/Hg3sek/0dOsGveJrCozTWeNR7cmnkozZbCov9AiB2eSXUsLuAySqo/9WNxfVRVpn+vrePzBiVMLzcuQcLjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.5_1702307895804_0.7113563651040178", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d1224fb1968cd9be24810e8f7782f806c51075eb", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-a0YnAx4TJCgds07M2v0A3WeyZUfls2YOgNHCb1akGKlz5KdmTCqf2N2hw86YhjY6oDqb6Omb8gFKz/3TOKRevQ==", "signatures": [{"sig": "MEUCIQCmXlE+fCaVK5BHok8Vx1v1y2sLljoVcArr7B/VbiHZgAIgA/VqVOVCJfdmcYhcLdTeQcJyQdme45cI8fT9HKwbJZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.6_1706285620779_0.027819417170139582", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "e1dc25c81787cdad5fced7efdb14c2497219c836", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-NB9FnW6HJ+MtOxjeNDqBvzt0N/vkbB94NBGcUU5FdWyUhwwvF/eZ9bQw3j6VErAhu1vTA8IX8ocpd22Q1951Dg==", "signatures": [{"sig": "MEUCIF4qpTZdi57JB3dqljCrsVJL11YLpL3MuF35TU95+mG9AiEArkuQH0Eo1P3dhQZMTXROgozWe4uoPWwp0KqDL9Ui9bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.7_1709129046007_0.9152024247464359", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "0ff1660de276a187012a9571a4885ae6deddedff", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.8.tgz", "fileCount": 6, "integrity": "sha512-7uCkkKAnoPa864zeoJ82mhXNVhRtvVbquSZ+i98qG4jaUu3yRe3ZKWV1K075FTthM4gNgRVMWTK/KjrSuX9w+Q==", "signatures": [{"sig": "MEYCIQCKRtoDDTOSf9YI70IugoLwSnrz6BOfUvj4lWdGJ+UIjgIhAPO7b93Fcot0vSh91aSX9g8v5Z8/hEse0ezVwBZ4nRx8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47360}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.8_1712236763827_0.2548098268702683", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/helper-validator-identifier", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "918b1a7fa23056603506370089bd990d8720db62", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.5.tgz", "fileCount": 10, "integrity": "sha512-3q93SSKX2TWCG30M2G2kwaKeTYgEUp5Snjuj8qm729SObL6nbtUldAi37qbxkD5gg3xnBio+f9nqpSepGZMvxA==", "signatures": [{"sig": "MEYCIQDugIRtGPYW+LwdoE6Fcy0rVSOFM8JJAFzwOfbof/i1hQIhAKZBGc/Gvy8FvyXG2Bstghl9oAUiQmV8VMcHUw4EVa3k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49154}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.24.5_1714415645783_0.412636829629901", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-validator-identifier", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "08bb6612b11bdec78f3feed3db196da682454a5e", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.6.tgz", "fileCount": 10, "integrity": "sha512-4yA7s865JHaqUdRbnaxarZREuPTHrjpDT+pXoAZ1yhyo6uFnIEpS8VMu16siFOHDpZNKYv5BObhsB//ycbICyw==", "signatures": [{"sig": "MEQCIAcHl+iOn9pa4Uzx476dntKQ4S+rnLRCIDc3vfK8s6wbAiAo2XHtWbtQ+CBvJVS5L2S6DA4P1BAzMrLTPHb2nm0jyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49214}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.24.6_1716553449325_0.18612877541626105", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ffbf61d4ec908d6b6eeb15d16d5c9c6df75abef3", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.9.tgz", "fileCount": 7, "integrity": "sha512-LJgiSOWgAx9glOL/y77Onutpdi3NQ2/ApAnDfoS3uDlZMtbWWweqTUBblGbOxKDsSEkLwsBShgAMqgjg7DZfmg==", "signatures": [{"sig": "MEYCIQDsUwRQTZwX2hva4pYi9RjFSNnzIxCVrWBV2yLPmqVjyAIhALNyMvkluFouTe8jfSuvbUmypAs2O5ugQQ1bqDwM9suj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48662}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.9_1717423425496_0.5886953984828385", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "75f1871ba4e6a0574a928133ce8c4861d039bf61", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.10.tgz", "fileCount": 7, "integrity": "sha512-P62S/nI9tQHVs7xALzMPvGxSfwYpRVb8cbReTPjDCXKJO+nfX9VkwY768xHUQEn2FIUbwvP3EUzsa32eFOzhog==", "signatures": [{"sig": "MEQCIGSHeFFqdvavK1XJtjx0fwdHgLgJnYBkO5PjvGqHTZPkAiBzru91EVWIIIGEapVvtZCgzQAJC6djFphXbgbFvlDG8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.10_1717499979013_0.7585457292209177", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-validator-identifier", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "75b889cfaf9e35c2aaf42cf0d72c8e91719251db", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.7.tgz", "fileCount": 10, "integrity": "sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==", "signatures": [{"sig": "MEQCIGWW9TJoUWwsk3/REDmPrcbIoN9oNfutzTvuILwoxgTsAiA7X4IsplZH5lNq8CyVC0/9ETxdoMAPkE1lcKV0rFQmRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49304}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.24.7_1717593295567_0.08240650494955304", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "38bee4dd4a6638a73ff79131444f42eb3ee16501", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.11.tgz", "fileCount": 7, "integrity": "sha512-HUtrpV2uaXv/kkFH0M2svUCoX96pW4qC1ioTu8dpNUU98xc2QciHmApW1zRWvwpHGQ/46Mc3f9QEVsMkmxM6Mg==", "signatures": [{"sig": "MEYCIQCjafanBQgye7Ho6A1L5ZlCxPC/tM1k1LT+Xdnv3ZrLOwIhAKlUms9OH3EfncCzwl1lUZ68G292jMreFs0kjl3mzrkE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.11_1717751710527_0.3374524021740217", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "da152563f9b91d25564ff39c5e4f676c86f5c8f4", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.12.tgz", "fileCount": 7, "integrity": "sha512-NVe4weLycSj/dFTRNgpdkw16uD1HMzeOKHp8MErkVus8WiPsHVPfMQeE3aCa9ApKDxNYcTSR05vtvEmruRcV5Q==", "signatures": [{"sig": "MEYCIQDxl9vFrI2D6gKWfwTSx7nyStja0KAkjKiW8TWThZdO0wIhAJwzXASwod+ckS8FaGefeoKf/W3q44YHhcuWydC/K0yQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48663}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.12_1722015185355_0.4696202687332285", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-validator-identifier", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "77b7f60c40b15c97df735b38a66ba1d7c3e93da5", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.7.tgz", "fileCount": 12, "integrity": "sha512-AM6TzwYqGChO45oiuPqwL2t20/HdMC1rTPAesnBCgPCSF1x3oN9MVUwQV2iyz4xqWrctwK5RNC8LV22kaQCNYg==", "signatures": [{"sig": "MEQCIAIxWlayDpk38jyvrUfgPEreg5eTZDVHFs059N7L5njwAiAl2cPU4in2YfwZTmnuh1I62devTTnH4LwWOGm4iXdJSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93338}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.25.7_1727882063922_0.6169695126626855", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-validator-identifier", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "24b64e2c3ec7cd3b3c547729b8d16871f22cbdc7", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==", "signatures": [{"sig": "MEUCIQCw96TY7Vp9MnWOehXsAKt2X5mK8r63MyTCfrN1VMXm6AIgOBqkCM7up7RmU8EnL3hiTr+1z7eL4wmn6a5YVkjaJJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48330}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.25.9_1729610436762_0.6627856628897884", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "de7e3dcafaa4015726048f21d530fbec9680c918", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-/IZpaAHTp39t0YIZQT51w7xEAPVlEYA9YsxA4tUsveqjvkK47rc/teUWzhQnXKwMwWDiSnP9vLfxYCR2DomrWg==", "signatures": [{"sig": "MEYCIQDkJHDiFWA1K5UTrch74fbZsWcRkN5ZSictQoraIVdXogIhAIApcRo6+gJV3GW9ChMNt3x2Sd2+Kxvo1KR6y+SFDgPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47689}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.13_1729864422749_0.35357590486246915", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "eb53cb8986bbcb1a9d01903d094cecf36533fc08", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-f1yqQwE37DcHBRnrEslgr+StSsjtBYJyVh/jIi8Skk6rPoeyGiesZCbvs5bKcKr5h7VSLDVpPv11Y5FhpMdg3w==", "signatures": [{"sig": "MEUCIF/CUd7SKCC8SsxPrbJF8WQp111keaJr7RfMoSYI9WA6AiEAy9CiXZJUu7jlHqA3y8b/moMlN0oFSH2wUOhdrAf3dLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47689}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.14_1733504014278_0.9237170539052812", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "9cf9e781d86880773fdf8f8860cc3932f594b1c1", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-NJiiQmj91uu2aaTm+JCPoVLQg3WA3ZpZeFpxUbuYpSzf4KPEb4TemNQbhRQ8w9IW7Mc6+uSgefyH9Q2AVazXmA==", "signatures": [{"sig": "MEUCIQChOI+opoIBLbBwojaofNNcevWpwIMjVQ9Bp1yDQ7w9/wIgGIJ1hqY/2Nf/wzSp+5rusfvX3wzkVARhzJLlyB1/qdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47689}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.15_1736529838461_0.6074880577922477", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "daa4493e85b3c9e5882cd6f2aa580167dc8a8734", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-Q/HYaEOMPJWSdpzBN029uNyxnqGVPI8VUjlM8o8/JsVsXqWmBXyltOWUzkFbvnkIHiQJfd4WkNrBVd5knMBBIQ==", "signatures": [{"sig": "MEUCIGG0ooRQ+gnfe7VZA75z+sIxkFrzh2nCFufPWSvoDDPRAiEAj7VTFLdCUoBVHOrTH7xAoRvJJsmaI5Y7rMQm5ruW1+I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47689}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.16_1739534316607_0.28792743818794153", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "feaf223793560bfacb5fada82512cf0794b06752", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-vHzGnLwOKHxyC5fXW1r/95ymXrR22iEw9z9FY3fa4vBKSDJ+Jg/4OFzGLXb/bX4VeM8zte0t40ZCGHI4WtWgTw==", "signatures": [{"sig": "MEQCIExI+ZLMvTl5u77N4w9MPwqj2fj7vjOg6b0eXCb+Uf/QAiBFoOARlHwueRJfKiKLHmTqbasNz5BAO04yig4my4ud7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47689}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-alpha.17_1741717466653_0.24054400892765027", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-validator-identifier", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "a7054dcc145a967dd4dc8fee845a57c1316c9df8", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "fileCount": 9, "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "signatures": [{"sig": "MEYCIQD49e0iqDMIfkAAyV+uJ8S8Bbc0DrJry89PsvmEYTxd6gIhALlFd0QR23I4TGDdeQbBdRacFvIlHmjMV6eSyAQ7YTCu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 48330}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_7.27.1_1746025707381_0.4286643243469266", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-identifier@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "4a9f192f05c7fee10abb7010df34a45593bf2a02", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-vk2UDvO5vaggpE/UmaAO3P8Sp+S1x2NEkjDZ0GpF0guuc/WgKsw9GAp1lsSKxjd2GP4W7qF7DA5L3MXFuki2tQ==", "signatures": [{"sig": "MEQCIF1qm/twvQt4eVlTtUalVuDMYhyahFIyO6EhfaHwNVZEAiBK64gXvmE8zQmNjhFulOVcD1ejWZ0FHX9XPvz4DGguXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47676}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@unicode/unicode-16.0.0": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-identifier_8.0.0-beta.0_1748620239097_0.48009765084938993", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-validator-identifier", "version": "8.0.0-beta.1", "description": "Validate identifier/keywords name", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.0.0", "charcodes": "^0.2.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/helper-validator-identifier@8.0.0-beta.1", "dist": {"shasum": "8d9a2b3024620b372f85ab47a6208acba33d77b5", "integrity": "sha512-594m9shQr1aVpJwhoqrKYdA9rp4XIFCtNY1ImV6ZMg2QIgCkhE8sW+Ld4WftLzpEBKlpZKCfFZnYF66Uv+B/yg==", "tarball": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 47676, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCbQt6MEEqwwrMQPMRv5gEFaOGB7uVYxdink5GcxE9nQwIhAPJnu+JY3Enwh4mMDjU/XcPapQo2c42dLR0okpF/sEca"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-validator-identifier_8.0.0-beta.1_1751447034186_0.591405404428879"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-03-20T15:39:13.517Z", "modified": "2025-07-02T09:03:54.616Z", "7.9.0": "2020-03-20T15:39:14.099Z", "7.9.5": "2020-04-07T19:25:14.155Z", "7.10.1": "2020-05-27T22:06:51.324Z", "7.10.3": "2020-06-19T20:54:07.806Z", "7.10.4": "2020-06-30T13:11:18.900Z", "7.12.11": "2020-12-15T23:59:16.043Z", "7.14.0": "2021-04-29T20:09:53.338Z", "7.14.5": "2021-06-09T23:11:17.389Z", "7.14.8": "2021-07-20T18:02:34.955Z", "7.14.9": "2021-08-01T07:53:10.989Z", "7.15.7": "2021-09-17T23:06:16.235Z", "7.16.7": "2021-12-31T00:21:17.537Z", "7.18.6": "2022-06-27T19:49:45.147Z", "7.19.1": "2022-09-14T15:29:10.424Z", "7.21.4-esm": "2023-04-04T14:09:00.806Z", "7.21.4-esm.1": "2023-04-04T14:20:52.707Z", "7.21.4-esm.2": "2023-04-04T14:38:35.499Z", "7.21.4-esm.3": "2023-04-04T14:55:48.097Z", "7.21.4-esm.4": "2023-04-04T15:13:01.783Z", "7.22.5": "2023-06-08T18:21:02.158Z", "8.0.0-alpha.0": "2023-07-20T13:59:34.298Z", "8.0.0-alpha.1": "2023-07-24T17:50:55.413Z", "8.0.0-alpha.2": "2023-08-09T15:14:32.391Z", "7.22.15": "2023-09-04T12:25:01.944Z", "7.22.18": "2023-09-14T15:58:44.153Z", "7.22.19": "2023-09-14T16:32:01.689Z", "7.22.20": "2023-09-16T16:28:36.444Z", "8.0.0-alpha.3": "2023-09-26T14:56:24.804Z", "8.0.0-alpha.4": "2023-10-12T02:05:52.686Z", "8.0.0-alpha.5": "2023-12-11T15:18:16.011Z", "8.0.0-alpha.6": "2024-01-26T16:13:40.960Z", "8.0.0-alpha.7": "2024-02-28T14:04:06.189Z", "8.0.0-alpha.8": "2024-04-04T13:19:24.019Z", "7.24.5": "2024-04-29T18:34:05.954Z", "7.24.6": "2024-05-24T12:24:09.483Z", "8.0.0-alpha.9": "2024-06-03T14:03:45.691Z", "8.0.0-alpha.10": "2024-06-04T11:19:39.143Z", "7.24.7": "2024-06-05T13:14:55.751Z", "8.0.0-alpha.11": "2024-06-07T09:15:10.700Z", "8.0.0-alpha.12": "2024-07-26T17:33:05.608Z", "7.25.7": "2024-10-02T15:14:24.143Z", "7.25.9": "2024-10-22T15:20:36.986Z", "8.0.0-alpha.13": "2024-10-25T13:53:42.934Z", "8.0.0-alpha.14": "2024-12-06T16:53:34.505Z", "8.0.0-alpha.15": "2025-01-10T17:23:58.628Z", "8.0.0-alpha.16": "2025-02-14T11:58:36.800Z", "8.0.0-alpha.17": "2025-03-11T18:24:26.923Z", "7.27.1": "2025-04-30T15:08:27.591Z", "8.0.0-beta.0": "2025-05-30T15:50:39.260Z", "8.0.0-beta.1": "2025-07-02T09:03:54.355Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-identifier"}, "description": "Validate identifier/keywords name", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}