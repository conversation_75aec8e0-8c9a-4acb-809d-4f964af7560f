{"_id": "acorn-globals", "_rev": "36-5bb89d65523e4f6532cbd86b815c6981", "name": "acorn-globals", "description": "Detect global variables in JavaScript using acorn", "dist-tags": {"latest": "7.0.1", "canary": "7.0.1-canary-4"}, "versions": {"1.0.0": {"name": "acorn-globals", "version": "1.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^0.8.0"}, "devDependencies": {"testit": "^1.2.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "248351714f0ac83da37a24357ed10ca90038b33b", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.0", "_shasum": "affa938bdc8d7877a96c3e8b13f36ccac572f70d", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "affa938bdc8d7877a96c3e8b13f36ccac572f70d", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.0.tgz", "integrity": "sha512-XNXKpVNpSUrFs6hs/VRqSSfHyerjK2zAhUWlyZuigXCgr6g8Tju9x0kBCDxdQcch4nW113cjCahhTzu+7Qr5UA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoWmjV93vExvNLZeN5xEJq1YhccvCkgpm088GI3ukJ8QIgXXDPEI921HJ8DEas5gVzqHv9YlNtsnVQZ6pksFbE/ys="}]}, "directories": {}}, "1.0.1": {"name": "acorn-globals", "version": "1.0.1", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^0.8.0"}, "devDependencies": {"testit": "^1.2.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "fdcea5c0e7903b1d630a4cd032f3347406bf6665", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.1", "_shasum": "00013368c24311f9fc2c2d420df6c311b9df64d9", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "00013368c24311f9fc2c2d420df6c311b9df64d9", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.1.tgz", "integrity": "sha512-1+bUCgh4QBH8MyLypm68xmnHLR+amDOFdlsc0IVEfVQYY/CL+CnA+2K/mjApqq5LL06CEGzMZxnLTReSEfBsXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlt5R+WHWWBXmfbLes+d2EpcV0SMdEpRqO5kPU0Vgq2wIgYMN9fWjlOZ5vOa9n2EcMSovZCSwWIeAiZ16YrElvRlI="}]}, "directories": {}}, "1.0.2": {"name": "acorn-globals", "version": "1.0.2", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^0.11.0"}, "devDependencies": {"testit": "^1.2.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "416dab9dee14bdb2b8fcd6c52e4b3ace33e54281", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.2", "_shasum": "f90bf21844405e649d00add61a688fc6a3462158", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f90bf21844405e649d00add61a688fc6a3462158", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.2.tgz", "integrity": "sha512-JKhzRRu5tQJGN91M/0RaLA4fU3lbG7WSVKGCwvN566nKZfc7a0WmvYoDlJgKcofW/zklru0iMfhxorrpCKKDqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAqf0pSS8B8JTH4kqluxStLHa5gx8gA6YIDziQTjHxV9AiA1gCDFJE2VLe3xWos7pgYWHtD3OHxItpGlC4ksxo3cJA=="}]}, "directories": {}}, "1.0.3": {"name": "acorn-globals", "version": "1.0.3", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^1.0.1"}, "devDependencies": {"testit": "^1.2.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "166b011c06ffbf94b30806a917948244a5c6be5d", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.3", "_shasum": "819f58202cd767a90f2798b24fc4caa8d5ce45db", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "819f58202cd767a90f2798b24fc4caa8d5ce45db", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.3.tgz", "integrity": "sha512-AuNOvihFTXQcq4h+MF+YPLS8CL2bnDcbPa94WLOwH18mwPEweJxKA2ewuq+NeqLbsD3i0TJDfHYER1/09yMHTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICoH0B/pJlurxJY7Tg8hyiGAivO+30c1i7YVBGXaikfAAiEA1k8hx+dn8gd+szU8CXtdt325xUeNIVhulMkiRlng/74="}]}, "directories": {}}, "1.0.4": {"name": "acorn-globals", "version": "1.0.4", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^1.0.1"}, "devDependencies": {"testit": "^1.2.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "4ad8a64daa0e4028ce0d1596dbceec26b433235c", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.4", "_shasum": "4e8528e724b4fa24ba553ad479c4c78589afbfcf", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4e8528e724b4fa24ba553ad479c4c78589afbfcf", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.4.tgz", "integrity": "sha512-c07qtfxLQPXglcT7ZjXWQu0vIYFAvl5EtSNCgk2kanKmq8wrX2s5FHBRCndTJ+D0eXu0iGeutAUabT0GQuo5nA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0C6UEtyTfHG9gfOUD73/OYnO+g9imM84g5C89uw1tBAiEA+FEJ+feYkto24R2YTNgLeKwFeD+34F9gR+SrqQsRLDc="}]}, "directories": {}}, "1.0.5": {"name": "acorn-globals", "version": "1.0.5", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "46a3a9ffd93b8e53c741c2b9fae83ba8a041ecda", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.5", "_shasum": "69dd5f77d6ed218006946a50fa73ce74194d7cf5", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "69dd5f77d6ed218006946a50fa73ce74194d7cf5", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.5.tgz", "integrity": "sha512-avElomqWHnx34+yFEj0drAqtmjN7/OQKbtZF9PkFtAlM0vrg8Iaf8A8tX7Hl3aJaoU4gdhB1rTVQ386zGRmpMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkiQG3sQARHKacFcZeD784Xe+8GhHY9s6EAp21gwwdjAiEAy8QsoWSGdZG7M9kXFEM0FqZPdBx4dtBZS3XbUr95OMc="}]}, "directories": {}}, "1.0.6": {"name": "acorn-globals", "version": "1.0.6", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "30c33145858d0ea0c9e5ae8371d1399e53464039", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.6", "_shasum": "df2b32097504bb4da3b48d01fd8dac44b346b26d", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "df2b32097504bb4da3b48d01fd8dac44b346b26d", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.6.tgz", "integrity": "sha512-jcB/mQAwZkNmua6nT1CcWGEWCTMwlyjbMSu331UjhlBRr972CdU9vMHt7okhb88EWISsGEGEG4N9r9dQCkQz7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXTsbDpV7RIBhmevzM9fx8ffAxR9WbJsAEtQXBNAnCpAiEAg1PZiWE7I5dUkV8YeEOti7rBV+L5uPjmkmbfd4o9Uc0="}]}, "directories": {}}, "1.0.7": {"name": "acorn-globals", "version": "1.0.7", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "ec9964ba519ebbe483d8ff289af1f5db75d6cc5d", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@1.0.7", "_shasum": "6b9262606718894a43eaeddfe03255a15a29b0dd", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6b9262606718894a43eaeddfe03255a15a29b0dd", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.7.tgz", "integrity": "sha512-IvBGUzHCt4IUKvkZ8pOM3MITOzHPJrFzASZGvboF4asjBf/z25iRAfJQwJjvS8k9mhWWOBzwWi8LcZwGJojPPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID/LpflCTIeKhtabbxXKOw13ISXGU4jTXCgpwRWOcUIcAiEAuuznPpQS35a7EQ/HLDrawbGjw1q6tyb+rjCQeUx2e88="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.8": {"name": "acorn-globals", "version": "1.0.8", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "ab75936522e006fcea93ab30b6df9ed77ee0d90f", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@1.0.8", "_shasum": "3d84f91c49bd9120412d04ecdb09366565ec64a4", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3d84f91c49bd9120412d04ecdb09366565ec64a4", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.8.tgz", "integrity": "sha512-+b6k95tQm5pVBDsdj1A6LJPIWOCospEI09vR1ZzuoZ/ubFHR9WxiVxGVgp+u/lhqixddL5f52rMTBntP7ZKnQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwmB2ran4XwblS7VPkiz/1dpTFD3lnF75YhHzI7/N8AAIhAJzV4E6P0gYpbNcdC6TpiAzlyLtd3dA2ws7wkPJT9zHW"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.9": {"name": "acorn-globals", "version": "1.0.9", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "808cab09764b63679138b012602ca1bb51657f97", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@1.0.9", "_shasum": "55bb5e98691507b74579d0513413217c380c54cf", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "55bb5e98691507b74579d0513413217c380c54cf", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.9.tgz", "integrity": "sha512-j3/4pkfih8W4NK22gxVSXcEonTpAHOHh0hu5BoZrKcOsW/4oBPxTi4Yk3SAj+FhC1f3+bRTkXdm4019gw1vg9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdCisGBru52A/xza5eXM8DPKjtRA2G6EYNafCvy58IQAiEArMRIRbQkPtqh4rMlNXLkrANK0lWXnRSyyBoV6A0KsQQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "acorn-globals", "version": "2.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^2.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "abcd15bab1ee268f9b36f94bca97ea60874250bc", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals", "_id": "acorn-globals@2.0.0", "_shasum": "12d6705f6f0ea924ce744e1bf9624f62e9c81f7b", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "12d6705f6f0ea924ce744e1bf9624f62e9c81f7b", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-2.0.0.tgz", "integrity": "sha512-YzlFbvScq5jID8ydteozdEAqHlwfwArJs/dVeorL7MyAyJkapA2UtB3i+DBsbXe2PGNji+671CUyBhMua4BfXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD02W7M6v1M4Wo36WZ1eaRdfkH4VfrqbxOwwmXbS0hUjAIhAJdpozVfxv+PWXHGBVRWD/L6v29xNTvgVfD97QStJYTK"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "acorn-globals", "version": "3.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^3.1.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "15fbb44221f34e718ba48e5b183b41d15ca54356", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@3.0.0", "_shasum": "1a64dd8fa761288594620649526e2625917a56c6", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1a64dd8fa761288594620649526e2625917a56c6", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-3.0.0.tgz", "integrity": "sha512-I2JGqRcPoBB3eN4W6UQX5xks3iy+Zdr2q2lF7qkvVjD8Iz9DotAYw3gfw4fsengyvAjYAUPsX607PtSNqyaTYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRMD9JNNMWmvJNDUbeC1R40SYQsQIWGdT4o0OFn/cqRAiEA8GoPHr7tgNllZJhLE/UBakbdiZJd2Vjnd6ZhP9xMd5Y="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/acorn-globals-3.0.0.tgz_1464969407290_0.16545027447864413"}, "directories": {}}, "3.1.0": {"name": "acorn-globals", "version": "3.1.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^4.0.4"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "c42fb028ba9da9ac7301b7925ed72f02c76bbbec", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@3.1.0", "_shasum": "fd8270f71fbb4996b004fa880ee5d46573a731bf", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fd8270f71fbb4996b004fa880ee5d46573a731bf", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-3.1.0.tgz", "integrity": "sha512-uWttZCk96+7itPxK8xCzY86PnxKTMrReKDqrHzv42VQY0K30PUO8WY13WMOuI+cOdX4EIdzdvQ8k6jkuGRFMYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrtUbT/nyC+kQ3yUoRyFbU+NNsNhHOH85UjVIRmn5auQIgCox9vjTx1jt/uLGjQ13X8mATsaqYDMfR/LrOt4erc28="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/acorn-globals-3.1.0.tgz_1485797503325_0.67978948331438"}, "directories": {}}, "4.0.0": {"name": "acorn-globals", "version": "4.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^5.0.0"}, "devDependencies": {"testit": "^2.0.2"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "c78d741d547b9c9245426cc6c5cd618345bb118a", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-0ih/qJVrAalX7TjjAnQdz8u+I1QOnLvLq+9zkyqcczObOii07ukuUSd5mTgVDukhqikAs+gqTm6cMd8VFwTrwA==", "shasum": "0d771eb8c5b8e244124af193d006e21bd7d309c6", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCr6iKZQkj4EllGrPhb/6xzqPBwb3XDJezdweMZey/ZVwIgPIsTZl4MF+I34l1/EtxdGOuFF5c/Tcw7Y2/Gi1eQPG0="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals-4.0.0.tgz_1498216793125_0.24124875431880355"}, "directories": {}}, "4.1.0": {"name": "acorn-globals", "version": "4.1.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^5.0.0"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "9e2139d5a43be44523f06424bf67a2d1fdf85882", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-KjZwU26uG3u6eZcfGbTULzFcsoz6pegNKtHPksZPOUsiKo5bUmiBPa38FuHZ/Eun+XYh/JCCkS9AS3Lu4McQOQ==", "shasum": "ab716025dbe17c54d3ef81d32ece2b2d99fe2538", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFD4aUBJnCdqmt5uMSYcwQT/FlzE1na+HO90qcybAInFAiAR2KdYfbCZQGddlTMfRpTFQ1UrZYULy5lwQ+i78g8G4g=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals-4.1.0.tgz_1508886325881_0.2606661815661937"}, "directories": {}}, "4.2.0": {"name": "acorn-globals", "version": "4.2.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^5.0.0"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "aefa66f617c5b4d076b8399f71c9edf4e99be5d7", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.2.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SJO1hjUPtjNmggjhYzepuR2vSFpwbaMpXmLuVoDUKVNdlBA7u1Hyk4Frdo/u9FlXakDUcpNJzo/VELP86z7TIg==", "shasum": "502d5503cbbc772adb31d38a39336f670b2327be", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.2.0.tgz", "fileCount": 4, "unpackedSize": 8282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbnNtbCRA9TVsSAnZWagAApiwP/AuhNAp3uviz/CImgBDN\n8O6iJfvcH+sBXwRuJtg6EO6Gen2Gb2BanRbfoSmUxB/B7VXdj7+DwFHq9Ylv\n46el4EtOAi2UepW1dQeTN454jfKCHZEszNR7e7/sFV8AigomIyUMylXYI+VB\nw3rvlK5cjnNgPex7S5v1PrdEYl7FNa6bqRwCC/Yo/+P69A9eg7cLO3awMRhf\nQCuOfVO2Lw4OYeGLrx0w1l/h1xA7/LPCe802AHPrBnxdIowc+vIQ+wfHZz95\n+n1yENxKbJenP2isAlGgwTwQSSMWDVOOVC8WpVfVCgBloo3xWUJtUi5Qq68z\nVZliMsgGSZzJbQVhAgamm0jnXxEWVQbu2jWc1Wp8xHOfEZE7E0aRleZn1GhA\neYq+jeokuXHaWg3w5srR2SjTnlJYPmZCW07nV6JbZuI+aQAjsbQO3lV1y/bW\nZenThrTGo1foG2LkHJF5JzaB0b/QrSA8eUMezmDfvjodiy/Pz5GMtWDKLEp4\nig1TgewMVJxup+ytql5/8zZ/Q2QtkFttsocoXJZivfKWf7siYiK2auOyRE+I\n8QjeEIuG5wZzZp+wczPzCIPNSXKLr5vxLJyurhlG3+cJXXuysfqCICJllEpF\nauhm/oASx0B6u0yvQFGWCEpoaOR3vzKRRC4SQGBPttZXFBhziFxLCJaFzRhC\n0ERy\r\n=4KQd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQDVfD6kcdo9fqODDjAvVfSU9gJkpoiZE+R5T4Fa054wIhAM84eFCDrTdnWcFympZsZEbHTReDC5kJBe/6qgAJ7WZ9"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.2.0_1537006426831_0.3137548476866072"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "acorn-globals", "version": "4.3.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "616ac9a312940c8bd8aff49543105ef7e329bc78", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.3.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hMtHj3s5RnuhvHPowpBYvJVj3rAar82JiDQHvGs1zO0l10ocX/xEdBShNHTJaboucJUsScghp74pH3s7EnHHQw==", "shasum": "e3b6f8da3c1552a95ae627571f7dd6923bb54103", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.0.tgz", "fileCount": 4, "unpackedSize": 8305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbnNt9CRA9TVsSAnZWagAAYxAP/0hliK4wiPswu/Kp7qND\nvAalsnS88XKlHf4s3RiujlXeglcIU0kK23ouUB2JRKJ3jwJV931ljq+GYS17\n47ZQ+wWbpkORsj61bximhvt0Zx7aSo82rfpjfQVTHVBjIbCOzQw07GSL2gcG\neuAnUNU3lIDATNWsmY0ZZAI86nmhj+S8BWRrdYaotqmjl1FLAWl2viYFRWzn\nmh33lS0Ot5McNJUg4CqpNR9ynFnFWKERpO6mJJPbXCz01Ff3eFEnnKPiTNGu\nOJ53rONLgYfcKMYQMp4icXNNLN4eX5tyNT6mo/jiyK+3WOZi5mrmUvQLzix0\nFYhBabPVABYOVYK2yD+Or8PJXOwBx81CI0aPM5RbEdHvv6vWICzyyxYKExWo\n6kdY8d4+EoZ9p5QwE5LGu82qu7GqbSgmgMgfUA2uK56Fu1wNRbiKF4jCfRJY\nGWn60LFguCpMiA0WQUIuaA7XWP3tZfZJsq3G5c6b4SAEPN5lW+7V8cDxSYAY\nnu3IjhzlrgQxdJM1km0B4zgLFWV+/JJ94tRO6p248YS//BpjNw66nEQ2DrmK\nXB5nnR2aeUBkC0Yx46kprd/I/m9aB0lyBAoTGsfUTB0cQOXXmA9vB/2xrv1x\n7dR8+r/7CeqFTDzIExkP6ZCu+50/RVZNtUf8xem3cSHqXAPu9pBWtzpyy2fU\nLMGu\r\n=D4ss\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGXHwCJdqzAjUMgAp0ZQMQiVgU+QkrPYpO2hFnPjHZB8AiEAugBDee1l58W29kJMleEehl10ZVr+r0sk3HxN5lFb8UQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.3.0_1537006460943_0.882580479753529"}, "_hasShrinkwrap": false}, "4.3.1": {"name": "acorn-globals", "version": "4.3.1", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "300c3652094f5cd2c4d53c50f045ed23f0951ce6", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.3.1", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-gJSiKY8dBIjV/0jagZIFBdVMtfQyA5QHCvAT48H2q8REQoW8Fs5AOjqBql1LgSXgrMWdevcE+8cdZ33NtVbIBA==", "shasum": "deb149c59276657ebd40ba2ba849ddd529763ccf", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.1.tgz", "fileCount": 4, "unpackedSize": 8384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsH63CRA9TVsSAnZWagAAr2EQAKQbLYnQBRdyhOzWbk/U\nOob5VGTB1GfXnC9niw/MJH5NXUXJgImRftT5UzV9WLf5etdV1wysqZjcbP3j\n/eoEPNfjMzm0vXnIBMmfc7Ha7ImRJI68/QSqBaDgaxmPgdrU/5TeEgUne+iM\nn9ijZv4caEIWXdihyDSrh47efyIYe/gbp3NjhH87lUYlmfV2n2SvUq42Lrik\nCsc+yeax44IKB3omITKwQcSELwStTJCMd6MQkhoalLnV+IUk1NqHqwag8zpe\n9PFDmKTlLJqPE2Tjngca13QPB/YW3KZZJKmMq9SHKCTSUcwSPZEpxgSkgcDG\nahUy7i9KMdYT064irEUhadVDqDuhI8lPS72+N/0fXe82U9dSuvF7ESpTUimH\n9i+D7a+4FBmFIjYP5B5GvoMC0KcJGdDvjfcFpJgZ3paLcyIqBu5bHYBFcc+n\nHbeWip/yTgpYhLdxFoX0Tb2lGggWC4EOloDTRSR5DKXsAlZ3rmg+nyYcpWPu\nX+e8znid7OFvE7b5ex+aMyvliqvfawj/dsUr1WE7ZH1j4Eg8vIDrrBcpbxQs\nQ2Y/EF1oYTc7MyjKhJV7G5rMewNAHRN1dGeyt2oWO+EYA9EZkXPktOHMFUog\nWE3IvBGeEJiAMHrxKJguY9HzaguQ/vFUeMJd3JqidDqpqIQZaE2v4PIgvRNa\n5Vz4\r\n=ga7l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNi8pyC12Lu5Jfq2byNs9uu6GpZvm1SZ6mz0hkReBSTAiBDdgtp4K639Gofbxn/5mX88xuTscXfspM9bMrJovDK6Q=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.3.1_1555070646533_0.4875622794436336"}, "_hasShrinkwrap": false}, "4.3.2": {"name": "acorn-globals", "version": "4.3.2", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "b6521b3ea3ea6adfde5c58a3ce03403e5fee43a4", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.3.2", "_nodeVersion": "11.12.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-BbzvZhVtZP+Bs1J1HcwrQe8ycfO0wStkSGxuul3He3GkHOIZ6eTqOkPuw9IP1X3+IkOo4wiJmwkobzXYz4wewQ==", "shasum": "4e2c2313a597fd589720395f6354b41cd5ec8006", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.2.tgz", "fileCount": 4, "unpackedSize": 8770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvMjyCRA9TVsSAnZWagAAQ/gQAI0Wx/un7RZjqJ32ovPB\nSu9mfKUcMf7Br8rxZXE8ux4Eoo0Atu0WKYxjq1EUEYFVXS4MIeKOzNr7hIbW\noE6uFsvz635P12pJTAvIbBweJdQp2Q93ag3ysg0YOT5tNPwffSnZToGqFFJI\n94BsF04Gv5vDIMAlOUeNzlNHsZ1+fImIGu9xeMr4N6sE8c1NpCIpU0acXP9i\ngdISAfmt/BYYJBxAHtwwjrMj6GucAhu1LEycUAQsOcfVGTjfGmAXGKtydyF0\njXEC5IEcfv1JRCKJGtu177zHdNOTMKLKeCQvAVG+AkB8IWnFdSMWa5b6W00J\nrsPgMGOgtx5pzUlSjlYW+2ecJVMW2SIXzSIDetnVSE3NDGv9nECFNGuIfQpP\nlm+3AG0AB5FcIsoQABNzn9BO/Gq9AFFWAgWe0q7N/iYMbQc3nPztBdDlOzbG\nZhTe+qaUHsEIpLUyD3HlFaHj72XiFNLVVR4lwzGvsjUKPAdhykw3gxNbI4HN\n3sh/Kd6NSZyMPlPa+GWIzSAeKvUM51L2J8/kdl3dh8tH0XLcl9rw4fSPOFSJ\npSH+A8q/e3+GKo6HWuFnqy7i1maQzpyVsi01MlQHgPcSJteakc8rE5TvkNIp\n/scB0/guFby66BqBShS/x6gjb14dvE6QzcvxGeQwPEmNBoRL/qBsQfg1slhK\nuht7\r\n=GvQQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHh4tMFORUnZknzhxrqfoa4mUqlWhQHgFKo/0O5lrQdZAiAw3q2Rkd9THR3vEejqcgNKGu3vza/9b/7O2ho4wWOtIQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.3.2_1555876081863_0.3827506610070919"}, "_hasShrinkwrap": false}, "4.3.3": {"name": "acorn-globals", "version": "4.3.3", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "5833c929dddb267b4fc918ce1e2b9e7aba5fb3dd", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.3.3", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-vkR40VwS2SYO98AIeFvzWWh+xyc2qi9s7OoXSFEGIP/rOJKzjnhykaZJNnHdoq4BL2gGxI5EZOU16z896EYnOQ==", "shasum": "a86f75b69680b8780d30edd21eee4e0ea170c05e", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.3.tgz", "fileCount": 4, "unpackedSize": 9105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRNI6CRA9TVsSAnZWagAAcSMQAJPVWW13eo/3eqDXKAZL\nO5TVzRyIGrTCsCgNDdxzorDUMMZ1ylJ37gFiN43eFbKOuhWPEzoKMF6DKpok\nLEEsehXs/XwH+GDHd0iRydL4uLDyMoy2odxzWsL3C7h0Y6QbCPA8lUnmLQt7\nVfLuqhjSuNLs+9mbTesW/je1yTMi11yT0SJoXfgbyVBz4RaWBOMVFcvyhW1h\nBXSIGqmLbulJXnxas7/VAJiAMpd/KpmOFDkb3cZNOOqo2CRH4+qvR53e9sd8\nlVmQ7XfT9iOMpupezA7Jd2XuC0u39O5pqrL2+tCPYXWueZg+koSF6OeNFLU8\nBAcXS1rirzVU81xaHcwwRGaRMX7y/zgMH+L1SQL5dFBmeTyZsAJ2e34GuHOk\nRMk94Hbj9okgYlVqso4+oCuGnqBlr4ZcR9v4nCo9qOZ/o95UXebmmvf4iy4G\nFwv6UAHCzoKtlf3uYPIGjKkrOdzsKZwNnKvsnmjITDTJ3o4p/B5YokQy6cPq\nGxw6RCiUa+yGwxJmuLmwBXT+tF1unJAThMTDmy2wGk7CDlraRNWhhHAJJnBq\n1WwZU0pC+Aqwy2dlFmYgr8BpO63X3qTUZKlw5xCc8lgdTiAj39S+zKv6ScY8\n5ru5pgl4WqMYYOsez/W/wJ9OcCbskd4GmPCrLUWnDSeQAMcThg+WiCWbO1IX\n4k8w\r\n=TJ62\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDWRThQygUQk6LiMFhzZuDV0dhRXk8OlmcDVeS7hkGU5AiEAqYciK/jl+eyIOdazyG6C5vFrX6UqJaf5KVl067KlHdo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.3.3_1564791353406_0.40102051875783684"}, "_hasShrinkwrap": false}, "4.3.4": {"name": "acorn-globals", "version": "4.3.4", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "2f959b04c5f887eeda2f0341585979bc0722a1a3", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@4.3.4", "_nodeVersion": "10.16.2", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-clfQEh21R+D0leSbUdWf3OcfqyaCSAQ8Ryq00bofSekfr9W8u1jyYZo6ir0xu9Gtcf7BjcHJpnbZH7JOCpP60A==", "shasum": "9fa1926addc11c97308c4e66d7add0d40c3272e7", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-4.3.4.tgz", "fileCount": 4, "unpackedSize": 9102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdd8VtCRA9TVsSAnZWagAAnP4P/356XDWmY2o6n4siCvKH\nNtk3JNqAauchNGI9eIEj2OpPJvVI0F8xpDfsLPoYiPBN1b32TjQBl6hsvv/P\nIO0QcHbCugMNOh+u4aSn/bMiWgwZwqUVPAhZT+A15VhMeatu/Tr+t+T2SubV\nJBRfmw8xSp7c5DCdsIR9IoQy3ab5ZtaU8dOGnZcPYleSKbNFifQNnz7EdKSt\n8W0zh2cYReHkwl5BgQ9QSkaqPsOTrWi4Kq6B9xuPX2RCARfQCH8PrrcEXOIE\nu85u4/fMeFhARc9EYrWiHh3YFj1pHhO/7wvCdK522kvOglw/bjwtL9j5Pr19\nw4UFDccFyFnae+VpH2zirdKfnvzD8nBLzNmjOFL7SZnRDKye5bGAUceYqBLS\ncAnDp2Ig4LeungH+1khq8h+DyXEehhTAxgUPsYUgxhpheBBg7H43RHjdxgI7\nMAnWMdU+cOiTUSLGrSNGLwAyONrj8siZbfLbBhJn6D/RUoNo4iKj4kDl0Z6D\n9I12+nIUQyDQ5cax1kPdNAu5rtXUkP8Mux7dcJ6OTHIztSscuQfajRXniFRO\nHRYkibjrrcpF2xp0DVqnWgHX8MRPr7UXjK0+Ol7EDQAld+6ZZnkW5D1S6qIc\nwuU30tDNVnFOWa+iNawMRyklzQWrLm8xpag39wP/CX553CjYEnyYFkHlMW6T\nYc05\r\n=FXiy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMnY4tqJOhxKYqXjrxd6ZTilkvXDWMm+JTHODkIc9/nQIgdxiQNxVajv3+T3r1rhbblnIZk6ec/AxXx/oQofSVlWQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_4.3.4_1568130413087_0.8540497032448573"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "acorn-globals", "version": "5.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "devDependencies": {"testit": "^3.0.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "licenseText": "Copyright (c) 2014 Forbes Lindesay\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "acorn-globals@5.0.0", "dist": {"shasum": "****************************************", "integrity": "sha512-GO75d2uQ00xmo8ZzdaWIFeTSZRRwCj1hllQI90b5Slz/QwXkPYTAKWmRN+IHZvq8xcAJUA2OUDxTV4mpaD8+kg==", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-5.0.0.tgz", "fileCount": 5, "unpackedSize": 9103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZHa+CRA9TVsSAnZWagAACfEP/031EboetOlsB5ZPgAFI\nCc5XJJjpXSiPUasTf5LNd9Nlrjf2VuQke717x23aB0XiOa1fTcPU2FX6O+Iz\nL3QqOQq88DSqNvF8ufI0RYFlsHKZCRt2FrSbe5RF1taHkErQ8xQnpVTJs05a\neqEv5Xi3ot9hqLC1lxMtO7zfrl4poXPgTJ1rjcj9PUcV0mKVexMcvja0UkS9\n41HlQPQgvViNp0VXj2k4WHMlCOr7z9F9UlXQP68oXup8swilnYk0wpjPyNDX\nH1bAsBKE+Sl9wPf1dugoXYcvucdjPNTh01KRpdlFUTX4cDtTpf0RjyJ5zzLh\nusgAjbXDlpsKrdCneiUctT42pfH0GrRPw3aJwO6+f3eyuRStWHXTYg0TTxKY\nDchQa6o3XNYGzPkyF4WwUxoeEDEk/xNPLV4ag+Bq/cbf6bL10NghJK14n2sL\nt/ipf1iNBzfWfyKT5aw63M29N0EOvXFSCmWfxub7KVnsfevD9A6UfCKp1BBx\nw0iGAxtuW9zgRMY/ncnrJgcg3gVGtIMgkvLvfT95Q2azccDSJjktlKahmI1x\n12gZrLgrhI/z54lKvbJttyCjCyqyagZdo3BlCbsEKUzyxnPEqNOGBsIF47RP\nPLPNdiZtGzJ0QqxsDvXmhvLq//t+/dqrgHjDy9ehGah0qqf7HhZC0O3Fkuw7\nCeoJ\r\n=cxu+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyeD7pLhqgjOHdCDsFQbe1NDhMx4T2ls9p1woqb9hx/AIgOMM4KZ2xlH+4P5bB4lIZ5aaVGtffbJ1WsuHOJKvyL1w="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_5.0.0_1583642302189_0.6155909032247044"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "acorn-globals", "version": "6.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "licenseText": "Copyright (c) 2014 Forbes Lindesay\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "acorn-globals@6.0.0", "dist": {"shasum": "46cdd39f0f8ff08a876619b55f5ac8a6dc770b45", "integrity": "sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg==", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz", "fileCount": 5, "unpackedSize": 9103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZHe3CRA9TVsSAnZWagAAqz8P/1IGa++a3WxPbKLOB4a2\nfoQ0S35X3j29Bny1kS7N1chwi/AnqYXXw5UyF8ECNr/2E1jmzOUM0n0g2XRg\nm4fPETIAJwMo2oD8+BS/JI7nzIMshfxmYxp5jjuKyNtjXe6U8xsPqB7TWPoj\ncp4swzMXUiGlfMF/2ZqLsW9Lf1e8yTh2N6tqI4d9iRlWSZMu+R5xspuSWHWt\neYoa1o29f9cKFpzqoG021O+ckaBOm60iQbeF/QqU0Aa9r9pOKeGfONmPJ5XD\nhQ8pg225uHKeKrPseVWZYVHh1jVjtnvaMcPBJ62N9GluF7R9+U+Y2qjRKYz7\nrTzxwW3moH9VoQ7/pFH4at6mXG0PP6514w263XbSzZGcAOQHXdtp2iweuKFJ\nxTckX1Ksld0IuXRawnCqtZJGzN1A5QlG6eiHXOVrB3cRrxsXOgGZHVojpVvh\nm6HHnIDDc1+xMh7Z/ySjSwjcONMzyavcugvwTy/6Ru1GDvyqfc8SXWroMxAf\nJ+T1fiq3klbIqz4R3gZd2r/FDMs31qtOCkBLGmNpJSO+KEAP6PCh6i9Nys8M\n0ouwramRy0voFQnBd7GtwG7DdPEfSV1EjVjJPUAYqOX21Qfb6/yAjmsC0Yx1\nDhMeGSyVhJDqw00ec7yBijLqxDY5vf+1P/yjjza8/+8yzlk5Gz67QiH5Kn7R\nrQAa\r\n=NoSB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDsQZClo9tq9R7kk5TQq1/8BpN4st1KueM6zzBtZyi9aAiAgRR23KQ1ZUWxFbB30fH96jz1OnV8Jw5tTCEYj+YleIg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_6.0.0_1583642550799_0.04107310781659401"}, "_hasShrinkwrap": false}, "7.0.0-canary-3": {"name": "acorn-globals", "version": "7.0.0-canary-3", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^8.1.0", "acorn-walk": "^8.0.2"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "readme": "# acorn-globals\n\nDetect global variables in JavaScript using acorn\n\n[Get supported acorn-globals with the Tidelift Subscription](https://tidelift.com/subscription/pkg/npm-acorn_globals?utm_source=npm-acorn-globals&utm_medium=referral&utm_campaign=readme)\n\n[![Build Status](https://img.shields.io/github/workflow/status/ForbesLindesay/acorn-globals/Publish%20Canary/master?style=for-the-badge)](https://github.com/ForbesLindesay/acorn-globals/actions?query=workflow%3APublish%20Canary+branch%3Amaster)\n[![Rolling Versions](https://img.shields.io/badge/Rolling%20Versions-Enabled-brightgreen?style=for-the-badge)](https://rollingversions.com/ForbesLindesay/acorn-globals)\n[![NPM version](https://img.shields.io/npm/v/acorn-globals?style=for-the-badge)](https://www.npmjs.com/package/acorn-globals)\n\n## Installation\n\n    npm install acorn-globals\n\n## Usage\n\ndetect.js\n\n```js\nvar fs = require('fs');\nvar detect = require('acorn-globals');\n\nvar src = fs.readFileSync(__dirname + '/input.js', 'utf8');\n\nvar scope = detect(src);\nconsole.dir(scope);\n```\n\ninput.js\n\n```js\nvar x = 5;\nvar y = 3, z = 2;\n\nw.foo();\nw = 2;\n\nRAWR=444;\nRAWR.foo();\n\nBLARG=3;\n\nfoo(function () {\n    var BAR = 3;\n    process.nextTick(function (ZZZZZZZZZZZZ) {\n        console.log('beep boop');\n        var xyz = 4;\n        x += 10;\n        x.zzzzzz;\n        ZZZ=6;\n    });\n    function doom () {\n    }\n    ZZZ.foo();\n\n});\n\nconsole.log(xyz);\n```\n\noutput:\n\n```\n$ node example/detect.js\n[ { name: 'BLARG', nodes: [ [Object] ] },\n  { name: 'RAWR', nodes: [ [Object], [Object] ] },\n  { name: 'ZZZ', nodes: [ [Object], [Object] ] },\n  { name: 'console', nodes: [ [Object], [Object] ] },\n  { name: 'foo', nodes: [ [Object] ] },\n  { name: 'process', nodes: [ [Object] ] },\n  { name: 'w', nodes: [ [Object], [Object] ] },\n  { name: 'xyz', nodes: [ [Object] ] } ]\n```\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security). Tidelift will coordinate the fix and disclosure.\n\n## License\n\n  MIT\n", "readmeFilename": "README.md", "gitHead": "ff46a531dc1c865544470d0f941a000d7f32e2f6", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@7.0.0-canary-3", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-4Z1NQ8jU1lwwrbcRrNS4v6iaPngPY0JDgeUukSbh80WYJrGqZmGsp3srWgXzE9+CJITSmgXruiL8SvZuNBTFwA==", "shasum": "2b9fb3cdc404ffb7383cfb59838d4aa08a64e3d5", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.0-canary-3.tgz", "fileCount": 4, "unpackedSize": 9373, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwL7FAHfjBUamtHIkv/5EqZW2V92LMPN/hxRkv7RSBxAiAKdLQO2NFsKQCPzr7TWVwF9rcrzoTeKIxyyf7UT+j7dQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD0LMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/IQ/+JEbKXq/YiAQVHmlgPi0x07E+0EcDQCk6ry3q4dNnZyRaCdO9\r\nndfqoJsJamLwtgteIn2GxG4HysVpGV6lLDEeHs2MZzKeHZSd9znqvUhaoKy/\r\nuewM2PcLo61OvF6Y2+QBXY6b5OdsupPRkB/1hZBGcY82HqW7J6WUr/i6i+E8\r\ns8WspQwasDIrA/IICuoT/tHwcisZ35WAP8ZIRThtBlpnip9s/xcjq9npR9qv\r\niV5jokTnsONA0OUNSS8ZDl6+3Zx4bgIEI2jTcq/Q0ovEgkUbywIyh2eNms6O\r\nLl5y4IH51/Qr3hsYk9+3MBEVjeIhLvIBSkJgsl6S+yG40wne6IUfWQuKhb/3\r\nRNkEH8/ZEfU+vqYGPH+RCbeeuX4UudOpNXYGoM9rfONZN4xJA2HzdVypYTxp\r\nPl035iqcwLLieM08ejZF/K+t1uEKP4rSNeD8XA+PaW1OoQEnpUWte0N/zZRK\r\n1SfWHFMFzebF5P2IVhwqD3ZRNBjd1g6xUIo0l4rwuS89YfiFNtCOjtw9uhyj\r\nI9L8Ba2aBzAAR1rPCTT/ay09hYCZPjf53901AkTpobG/v2p1kVxOQnFSjWbX\r\nTOagrgQ5mW5hEkmBt8OaS0Bt7lgdMQZz1F1iMjFtS6Z4qZqSCszRZlYlpADu\r\ndquP3U8zPTRDvwl7YQRboMqhSyYWhkNRIOQ=\r\n=wzIq\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acorn-globals-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "acorn-globals-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_7.0.0-canary-3_1661944524163_0.4863953007405555"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "acorn-globals", "version": "7.0.0", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^8.1.0", "acorn-walk": "^8.0.2"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "ff46a531dc1c865544470d0f941a000d7f32e2f6", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@7.0.0", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-ruQRXOLHo0oiVDce/f01Ln9eMBSwIJkAnugNgdblXbTB72f6vYHVDQbH07UZ2ZOPO8UCC1rBAtm27/pfJKNPMQ==", "shasum": "73e59ac1a983cf50dcca4d131a12cd07656389d0", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.0.tgz", "fileCount": 4, "unpackedSize": 9364, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFF56kdKHc1epLt/qi0hIGBCT+fd4VTzCa4rDH9HUUjRAiB93/OVmbT5HfxOrgf7Re2tZIn6qoOO45ymmacaMxV3yQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD0NCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruLRAAge+7cDQP93Qu0l3MpjCaDcSOre3t6radCMyko4FyvzFb2lwV\r\nEwAsCOViuNflSoEMLALPIqcDV8H8yiuv0dOwS/tOmyN7XF8fZb2igyuNonAq\r\nAeIA1WW0ijeAkcT0jz0QxlBsAGcLUNDK0AkZ3odLNDUnXltYC6iXALCvRTp8\r\nc9TuDJ90UMQBib+GD7I9v89BXM58Q8S5KmdruvVQR3ra9fTsle0kTKE3z1wy\r\naBxZ1KlTomSCb1UThkXvN5GxOKrcWEnr88A3GNk/r097RrtRRWy0ugZX3UBg\r\nrPLHovVsYAjQuU1ss2ycGprfxwx+uG69ny4tpA4NDezC4u2kGvwTbULprCtc\r\nitjqUmQv3brSl54bdYMZkOoV5KByNrac95uo8KocnIlQupndb+HKiAFnbjaj\r\ngv0i679oWxsr+2su5Cekw5jrEg+ByYcoVuqbsF2Y5vihufaEXSzgPD8E72Nm\r\n96rsLZ/H9O0OHXEdLpd30aDsHOA0kitBef1yc4CiM8adCjHcio4Ek2yJD4sH\r\njgVvSxHYELEPj+GXkNsAgHPIf1cK1OYZCSgbzBRoEODrrwWC7OUPyqizKABK\r\nXBnI6PnF8HT3dUVTTuXBjIyZG45YaotHeEpe9tdkg14B/kEjRSwWSmDljsft\r\nzQmgOvy1Ykh6NSA+hDi7PXDgHpuH1vMP/TI=\r\n=jpKU\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acorn-globals-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "acorn-globals-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_7.0.0_1661944641976_0.4265841440049394"}, "_hasShrinkwrap": false}, "7.0.1-canary-4": {"name": "acorn-globals", "version": "7.0.1-canary-4", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^8.1.0", "acorn-walk": "^8.0.2"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "readme": "# acorn-globals\n\nDetect global variables in JavaScript using acorn\n\n[Get supported acorn-globals with the Tidelift Subscription](https://tidelift.com/subscription/pkg/npm-acorn_globals?utm_source=npm-acorn-globals&utm_medium=referral&utm_campaign=readme)\n\n[![Build Status](https://img.shields.io/github/workflow/status/ForbesLindesay/acorn-globals/Publish%20Canary/master?style=for-the-badge)](https://github.com/ForbesLindesay/acorn-globals/actions?query=workflow%3APublish%20Canary+branch%3Amaster)\n[![Rolling Versions](https://img.shields.io/badge/Rolling%20Versions-Enabled-brightgreen?style=for-the-badge)](https://rollingversions.com/ForbesLindesay/acorn-globals)\n[![NPM version](https://img.shields.io/npm/v/acorn-globals?style=for-the-badge)](https://www.npmjs.com/package/acorn-globals)\n\n## Installation\n\n    npm install acorn-globals\n\n## Usage\n\ndetect.js\n\n```js\nvar fs = require('fs');\nvar detect = require('acorn-globals');\n\nvar src = fs.readFileSync(__dirname + '/input.js', 'utf8');\n\nvar scope = detect(src);\nconsole.dir(scope);\n```\n\ninput.js\n\n```js\nvar x = 5;\nvar y = 3, z = 2;\n\nw.foo();\nw = 2;\n\nRAWR=444;\nRAWR.foo();\n\nBLARG=3;\n\nfoo(function () {\n    var BAR = 3;\n    process.nextTick(function (ZZZZZZZZZZZZ) {\n        console.log('beep boop');\n        var xyz = 4;\n        x += 10;\n        x.zzzzzz;\n        ZZZ=6;\n    });\n    function doom () {\n    }\n    ZZZ.foo();\n\n});\n\nconsole.log(xyz);\n```\n\noutput:\n\n```\n$ node example/detect.js\n[ { name: 'BLARG', nodes: [ [Object] ] },\n  { name: 'RAWR', nodes: [ [Object], [Object] ] },\n  { name: 'ZZZ', nodes: [ [Object], [Object] ] },\n  { name: 'console', nodes: [ [Object], [Object] ] },\n  { name: 'foo', nodes: [ [Object] ] },\n  { name: 'process', nodes: [ [Object] ] },\n  { name: 'w', nodes: [ [Object], [Object] ] },\n  { name: 'xyz', nodes: [ [Object] ] } ]\n```\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security). Tidelift will coordinate the fix and disclosure.\n\n## License\n\n  MIT\n", "readmeFilename": "README.md", "gitHead": "8de559f1655841506ba56493d3433bb6d54cc56e", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@7.0.1-canary-4", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-F6SF2/C1x2l15IdKUn1SB8nRE5f2elG+Ud1rYuoRlZ55A46wlLKkpIryr5qiwFRWfQAT/OofJJ4xh0QhR8xqXg==", "shasum": "8ab262b7b0eec1e1974c386af893c1a92cf48ac1", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.1-canary-4.tgz", "fileCount": 4, "unpackedSize": 9454, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfm5luRaVyXU5Dv9TBL8HjzkRct15R7OY5hzM/FVSWlgIgYVd4MCi4pGpSItVWBS7T6LZGqILNpcRDcQkf3FDZKtY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEV4xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2xg/+Mcha/LLycbz0pRBv3KvaWnt2fmYQw69QhfFSNKGQ8DhAIS82\r\n2kJVnv4NA4bwZMJKkOy4IXBaqVLmpBc9d9Q/Oi2kbSRpKKSUR6L5Ji6JBFtc\r\nipHRIy2Kq6g274nDkHXwUTSBNccL0osBQJ6V3dxo3Q7pMXQWgzT9nFrAhhdF\r\nM+rMxZfsgG1m86oRMfgqB/2dUpmzuj5BG2LFU7qoWxvdbiNdA9eUFJche8Z1\r\nqzUZEg1jtnOVowkbNip/QDWjNpR04iuqm8gZSF2f3Luslple8TTxY3n8wTdw\r\n886Q2Mc9ItXm06ChjynpDviwZgY2Tv0qr0oMs3ToUMi0SN6NRy6p5iE3ydKA\r\nEG5Efs0JxEjNQWSPpO/dWod5MvRHEPATvT6z1Iuih1St1Ecno5sfATFQZjx3\r\nfoX2V/A40DfrCoIcBYejnwKe8FFw7ZpWIRfB8JfOtfR6KilKbHUErL/oZSv2\r\n2bg18P0O2csCbVq8mj9EH/9qG3Z03/OZKB7sE5gmP9No9sjqo30ulXHS7vB3\r\nK0MvwW+KC34boBF+0x9ZYZweVpcHC4TRfQXqhaRLhBjBJywaHD7TkTtqTBVu\r\n581YYcdsSls/7S8RL8CRLiFpd6RwZQeJYM78hDFifzbp6Habez5rTh0kJ/Uj\r\nA91RqRmnOzkwWFTZZfF0kpulltqZNnz5Xjc=\r\n=R0Fj\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acorn-globals-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "acorn-globals-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_7.0.1-canary-4_1662082608901_0.107909897240543"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "acorn-globals", "version": "7.0.1", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "dependencies": {"acorn": "^8.1.0", "acorn-walk": "^8.0.2"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "gitHead": "8de559f1655841506ba56493d3433bb6d54cc56e", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "_id": "acorn-globals@7.0.1", "_nodeVersion": "12.22.12", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q==", "shasum": "0dbf05c44fa7c94332914c02066d5beff62c40c3", "tarball": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-7.0.1.tgz", "fileCount": 4, "unpackedSize": 9445, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIASbxqACWYwuspd2qIh4J1AfcBfUDsnkRzDbfE3FyyT0AiEAmxD3P6YyPsW8Gi6mTAwy6D9QehwGilHGGKUkdx24HhM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEWAjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg8g/9EZyno0EEx3smrAxAhPff3yGV6NzXCGuzL+64EP4h7irR9xLX\r\nAUjF6AaU1iPWpjsyxonZj+2nCSAq4Pn/PLn73a0WEHIpBtQZD3BirzhcZWVG\r\nJTCaKWajqj/kRHHnOZH6s62BbGPD5IBA4NR0rkwm6ZEG6gYZuEbLcbCbZJN7\r\nmB1RIKYd5OvesH3hOEDNN1tP6synQcKa7+qB8TqLzkDM9UPloHDDS0suF5hz\r\nlVGsYttxSQc9j8B8jv2rGB2YdlcSvuq4niSCUWltW1l6ZDCEPf4yrfROgPd1\r\nb8b+nH3wcWTOX3GYEPJEbXC3EU4Ytg3/gth6AWnbOQpviP7/XEilnhqVpbWD\r\nDnmrNBPG4dXmLyjgzGI6qmJbu1dJW45HevShMU44kkGPYAY/HR2JBNO/RZsu\r\ntP141YxQhNCKdnpzzTP0Yf4PT1a5gdJgqZ7TuZi2SPVrnVPt5Gh7UYTB0FNU\r\n18yz8sKfD6ERm+6lgiAm9NALUdoayOeNPGc2WkjhcRPipI5yjE00VL/NYPsD\r\nnbAi2TmEG/6cTYJYPIx+XA+QUoReGRA5MVCliSK5q9cGQkhxbCqaT45OAO2b\r\nunb85trlxcz3w5UtqzAZiwQSkA9bPoeV63MEmzD39Ux5xyVUSTYu7gUMerBd\r\nDGMNzJPrYBnucRwAnkBQzI7kWnC3LxYa59E=\r\n=rIJW\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "acorn-globals-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "acorn-globals-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/acorn-globals_7.0.1_1662083107374_0.014798031087405406"}, "_hasShrinkwrap": false}}, "readme": "# acorn-globals\n\nDetect global variables in JavaScript using acorn\n\n[Get supported acorn-globals with the Tidelift Subscription](https://tidelift.com/subscription/pkg/npm-acorn_globals?utm_source=npm-acorn-globals&utm_medium=referral&utm_campaign=readme)\n\n[![Build Status](https://img.shields.io/github/workflow/status/ForbesLindesay/acorn-globals/Publish%20Canary/master?style=for-the-badge)](https://github.com/ForbesLindesay/acorn-globals/actions?query=workflow%3APublish%20Canary+branch%3Amaster)\n[![Rolling Versions](https://img.shields.io/badge/Rolling%20Versions-Enabled-brightgreen?style=for-the-badge)](https://rollingversions.com/ForbesLindesay/acorn-globals)\n[![NPM version](https://img.shields.io/npm/v/acorn-globals?style=for-the-badge)](https://www.npmjs.com/package/acorn-globals)\n\n## Installation\n\n    npm install acorn-globals\n\n## Usage\n\ndetect.js\n\n```js\nvar fs = require('fs');\nvar detect = require('acorn-globals');\n\nvar src = fs.readFileSync(__dirname + '/input.js', 'utf8');\n\nvar scope = detect(src);\nconsole.dir(scope);\n```\n\ninput.js\n\n```js\nvar x = 5;\nvar y = 3, z = 2;\n\nw.foo();\nw = 2;\n\nRAWR=444;\nRAWR.foo();\n\nBLARG=3;\n\nfoo(function () {\n    var BAR = 3;\n    process.nextTick(function (ZZZZZZZZZZZZ) {\n        console.log('beep boop');\n        var xyz = 4;\n        x += 10;\n        x.zzzzzz;\n        ZZZ=6;\n    });\n    function doom () {\n    }\n    ZZZ.foo();\n\n});\n\nconsole.log(xyz);\n```\n\noutput:\n\n```\n$ node example/detect.js\n[ { name: 'BLARG', nodes: [ [Object] ] },\n  { name: 'RAWR', nodes: [ [Object], [Object] ] },\n  { name: 'ZZZ', nodes: [ [Object], [Object] ] },\n  { name: 'console', nodes: [ [Object], [Object] ] },\n  { name: 'foo', nodes: [ [Object] ] },\n  { name: 'process', nodes: [ [Object] ] },\n  { name: 'w', nodes: [ [Object], [Object] ] },\n  { name: 'xyz', nodes: [ [Object] ] } ]\n```\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security). Tidelift will coordinate the fix and disclosure.\n\n## License\n\n  MIT\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "acorn-globals-bot", "email": "<EMAIL>"}], "time": {"modified": "2023-05-27T04:10:14.699Z", "created": "2014-09-22T15:19:11.866Z", "1.0.0": "2014-09-22T15:19:11.866Z", "1.0.1": "2014-09-22T16:19:24.327Z", "1.0.2": "2015-01-13T11:26:48.784Z", "1.0.3": "2015-03-29T00:08:03.189Z", "1.0.4": "2015-04-06T19:53:44.130Z", "1.0.5": "2015-07-21T16:33:30.972Z", "1.0.6": "2015-09-06T18:02:58.665Z", "1.0.7": "2015-10-31T16:21:44.598Z", "1.0.8": "2015-10-31T19:43:47.042Z", "1.0.9": "2015-11-02T13:23:33.643Z", "2.0.0": "2015-11-02T15:37:58.207Z", "3.0.0": "2016-06-03T15:56:49.518Z", "3.1.0": "2017-01-30T17:31:45.178Z", "4.0.0": "2017-06-23T11:19:54.110Z", "4.1.0": "2017-10-24T23:05:26.732Z", "4.2.0": "2018-09-15T10:13:46.996Z", "4.3.0": "2018-09-15T10:14:21.132Z", "4.3.1": "2019-04-12T12:04:06.674Z", "4.3.2": "2019-04-21T19:48:02.063Z", "4.3.3": "2019-08-03T00:15:53.511Z", "4.3.4": "2019-09-10T15:46:53.270Z", "5.0.0": "2020-03-08T04:38:22.302Z", "6.0.0": "2020-03-08T04:42:30.909Z", "7.0.0-canary-3": "2022-08-31T11:15:24.324Z", "7.0.0": "2022-08-31T11:17:22.127Z", "7.0.1-canary-4": "2022-09-02T01:36:49.883Z", "7.0.1": "2022-09-02T01:45:07.650Z"}, "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "author": {"name": "ForbesLindesay"}, "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "users": {"flumpus-dev": true}}