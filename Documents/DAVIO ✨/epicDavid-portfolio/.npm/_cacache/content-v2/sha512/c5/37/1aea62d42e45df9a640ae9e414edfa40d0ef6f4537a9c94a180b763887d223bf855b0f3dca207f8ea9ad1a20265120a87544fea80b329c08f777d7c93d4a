{"_id": "saxes", "_rev": "31-293e396a3e76f3db7c970e266dba537b", "name": "saxes", "dist-tags": {"latest": "6.0.0", "next": "6.0.0-rc.1"}, "versions": {"2.0.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "2.0.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "c41d3c217283879c7bf674b05b71731daeea7b23", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-v86oGj4g6+HXSdsrFeRLMyneJCigJZDzbINnSb5+IbbIw2BvGannncgBpPR3zlrlN6NjNQ/e6LqFVnki2g/cYw==", "shasum": "0f328a57ee145bb6072ae572b4525685cd857ead", "tarball": "https://registry.npmjs.org/saxes/-/saxes-2.0.0.tgz", "fileCount": 6, "unpackedSize": 75147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVfILCRA9TVsSAnZWagAAEykP+wQyep/4aFPK3AuZlpg/\n++nFuK+sLEJBMV6DfOOW+XoLYfYjIytC/5VaiGSANdtNgdf7Ub5YzB0U5o31\nNinE9yg30EFHFJlnjbro618+YWkEAJ0DquYWKZI1BmTC2uU2iGPEf1bP2C7G\nb1X6wA4v1BHy6dbDh3+UTdrq9Owl2FyZSCElpkFdm4VeQxb1vSu43xvpCYIa\nV4C5+2gBsEeCFVGNa6D07KOnW5iGhcHvI8wYxhjvKQdxp6qiTZLLbSQ8rSw3\nXx0Dbwt6PQi8c3H/yqJabpzbhe0OhOIf+vzpe8ZWE85taegk5Xds8oj+euzA\np9UiC5MfJ6WopJsrca6Odd84mm1JofyRoNSmbahABLEvJAJx6GtBnhnT5x1B\nmbXGkVJYIBdmbLEpEaG1DOabF14CljakcdnKqfyUTzguGAM8Uy+8izezmh+5\nGpfGvi1zm47li0KUjzOoaBx9TGr+5KyNhngGr1Ive+ZoPRvle9mqx86sODgD\n/LxuH8dVuU4ek02mKANzJnbmic5Wap92FVo84OT7M0yGi0hHYC0roHI5uBmS\nGtVepqoplgefQLlAZZ9aXUKKhyN52Gh1GAlpNzYzGcFBYB4TnPUCPpUIGilo\n/R3ZNHTUekdP2I+a1PvdafXwrbE9Q6+GkqX7QvbekayiSsyGdf4oui4RfIWF\nNvE0\r\n=Csjf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDJ2XcDwN/sta8kGmgBiODARE1SXPLjWdAehUbAu35WmAiEA9GELhWkSYAkYPY0Tx+R6tbjfcSDLP8XLa634J/Cx0PQ="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_2.0.0_1532359179441_0.5190454835833116"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "2.1.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "41cc3ed12ca18a9479508376f3d17c886d5fba5a", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@2.1.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-/hcrGbRlX/N4ubyGdEtRVDqsMsArDRTx3QEDOkr/xp1Lg9j0w8cEW6nkCMJwm/9ay5zy9yYmUSIMJn7mVIjH2Q==", "shasum": "148c4140eb7b21ec86f1209581ff9af1c2071fdc", "tarball": "https://registry.npmjs.org/saxes/-/saxes-2.1.0.tgz", "fileCount": 6, "unpackedSize": 75195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbersICRA9TVsSAnZWagAAgsoP/2vg//Wv3LlEjDhKZScq\nvjnHE9fq+BI50JlbZaCkDfAtmX/hWGNsXMrVSqstXSlx9cqVEttd7JmbXb+J\nRA90U9D7WTPbuCEtEriOEuCieqq20+JGePMBS1pdqTtcpOBhfxoYpoQBbHVf\nhi1SF+hObX7ZvBAyf4Ee+nl2SweF02E29k7b/ogOo2F2MzbsOpoUNG0ZX8eu\nR30Dqg2VWhDnRTRxXa94gT2Eb9oaX0MVVc/rgYRgKGZu+OGpy6VWpAFqeW3d\ns1F7L0puaK2SvSyDZ9Z/ZIIIScJrFiIecdxo5tsYhN8LuJJtMwQ+bZVRgdN2\nJqMF1L52e0Bg/M+rB9e+ml4xjQ9z6dFoFRQNhq8tTpou4EKKu0AzG5IaPXNQ\nRFP/JxsST1ix/endIau681wCDcOU0Uuqit7O5mRhRLy2S/mxdCBY18NqO9rV\nQBBnt7chAZ1RSQWxXlUutLXi72miaJLQCvRsEW6IZDrAo1K/7gd3tL/mBSTZ\nJ1/VznC/2x2lIhtG1PtxkJMQcB2kvlqBxEWFe5OfyYtNvkdTwb8r/0Pgqenu\nMuy63Jv/AlRlziTIO6LlxTxAgIU3kKjb2+OGDU7r8hCg9wbqM7nRqY04plA3\n7YOgwWzGGnfS3PnQxa9waZKcAjSoBTuMVzCt2L+SFLEdhMu1UKb+gP7w2u5d\ndYmO\r\n=BfQ8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGhapU0yc1H03+l/W2RDVjtGkeyOfc9ONuJJtkrNyUzrAiEAnCQ1zZZ/M7LjLROdq1Ynewa3heGWZrmBPQCInzbH+Dk="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_2.1.0_1534769927867_0.8728760664083817"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "2.2.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "6113398adf78b5fe559fb9e34c156618ec8ed895", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@2.2.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-LYVRySDuRKAqGrvXim2Eq7TGhrbZXY21rNJ9GNEnpKuPweS1lQaTzNUSieak8c+zpEz3CYofVd7bNlxEKI0iPg==", "shasum": "9b51f96eb56ba5b8498b5b0103f5f818e760e9cc", "tarball": "https://registry.npmjs.org/saxes/-/saxes-2.2.0.tgz", "fileCount": 6, "unpackedSize": 76352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbesWbCRA9TVsSAnZWagAAbp8P/2VGhZuQoesLqjFvohEd\npvnMY8prc13V2Hx1UN7xTUR10A9iYUb7FfD2xHLPYglwQE7GiiI5DUTvLa7A\n/Vvtxqy6nxfyjMGw9w4bHdl7g7nybObqn6SOKBTjnjDB2R1IMEKNysC6mg4d\nRPmtpn7HGiKZCH1SissK989SIXBY+xUFsKTJBWJEu1BHK4anPspS7nT9Y7iG\nvxqNoHKfOrZ3Kz6hCS3kePSC1YdN98uymDwbPLfagEKH5aCnDiL5hwgG4D+m\n7hz3J9UNMTi78ZSqAKpnvf5Lgl3jdofQS8gtSAaiQLpMNjtDIBuW5thVGsjN\n8qeMZTOcvFvzQA2ZTuFbomfMF+2kpJTnaAaJDJryjvd+QoVoAKkjJrjVOgaA\nxx912jw0CdRiFUIjBobshVUZZxjZJ8kdsaS+8tfRYaZkd9dLt2qALLKDpuni\nPAG7Xifk719X8KCQh77MKSCByYjzzfqu6mEX+zvw8ZswEb0d3JPk6mueDnSg\nN2qHfclohUURZ9HTBTxitazjQ5nfKJkLX+83F7uhIoZMI2ZlKSr3RlNr1HSx\n3tZJbLUnxCKczhws7+ngLDRrfFHDNVKd7Ub8Uez89wtsWNinrPmddBcol4N/\nxpI1unjgxt5UBg3ayQ1UrishVakL8jBewGIuylQF8ZBHCLnjeGnC6hkCKc6h\nEP54\r\n=PCk1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAmWLsuRPU7RHxo/3oYav/OMkTsxKhI0RgCfVqCcuYk9AiBn7gONzYGDiiYoIIv/YCCHvQwfzQNmshpKksXyBLVvew=="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_2.2.0_1534772634556_0.7574169325294002"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "2.2.1", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "fd3ab1a778fe7c00cc03ca5b780ebce32fb0b528", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@2.2.1", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-cpRDccnfURgetVoFLdmtCCAJcLAniAwUtPFlCFVKj1o9sDIMBfWtGszT3lBfectEjYhC1TH82NpZ1h0tEko+Dg==", "shasum": "dad623d9bf0460e0c1ed48bbc8acfc8e6a6efb51", "tarball": "https://registry.npmjs.org/saxes/-/saxes-2.2.1.tgz", "fileCount": 6, "unpackedSize": 76581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbewV2CRA9TVsSAnZWagAAIs0P/3cniUCGZVnS5R8iO7/n\ntTPm6NlTQkl74e52NDVzwh1g+F//wQQdzNayTGYyR7zECj+Z7ywWlM3z6ifY\nv+0nvuE1zq6nj4TNceiI/tKHx12uVR17ETHr2ZFJaRMTnAZ4WGUmb2Q446/Y\nwC+NaEtsh1euW5T18OfOtPOV+aAYa6IW8JsxyDQiqq+YF+XxLwTJMrpxP8Z0\nnxkD+QJshUyIW86ct7r9oq4mLF1h5zazxetQ45TB+QNQnjmW7N86ZMriJao6\nsfdidM6hfB4DDizzfa8O4hmSaFyWJ0fgCS2ZEKZ4fZozL96bL46rNOBSSpeB\n+eL1EGyChYRrrtDck1A7MEQlqz3mre44tFgEAFgkG8VdRptsW/PSByN564Z3\nWstu+9KFcfn+fdHONBEvw1hAZkrtUg+PLiTTJ9Dp0Exm4nDolV5QTl6kSdr4\nh3RMZn8uuxOs76T/tdjEuGdMNNJ9SG1M554bK4DxwooFYZKY28SidEBVm3Yc\n/H1nYrwacpj4XlxImhWhIwnQJTdXLOMG3GlU6R/Q18GJ4fEGwRAqid7Xvzzc\nrXii0gdGBVSgSnqMK/C+xZnvHi+17rbYZwoTbDDoOtuGhBbLq0O2jXp1nfWW\nV2IhMkARmxMUIzlpZv+Ne3Ta5roL5k519z4RVedq2IEZZBnCPsp+nCIB3l6D\nIV5p\r\n=QELa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6bRnoW0fuo0qjuwkFGuOaIJoShx3SQNHvUULgf+OKDAiEAyjNX0wLZVI62Wy7t/QQRwCIddEa8M/J05hAbd15yN1o="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_2.2.1_1534788981949_0.42264376979502183"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.0.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "60d1dc530278728904af2e8528b5ffe5613a57bb", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.0.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-oqyL0tVBp+2uMZZadMGlxd+V6sIMEwe5FoOIqvRb16JesqSsPkUZGEQf5SP+9T1Z2Z7DlYT5Nos+WMlDpbKN6w==", "shasum": "73217c47ca44c216b1ce2ea2cd316cfbf58629b7", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.0.0.tgz", "fileCount": 6, "unpackedSize": 77498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfDs2CRA9TVsSAnZWagAAOBcP/1BUSXXDKEHdOkuUfKGy\ndL28wGtftIEPIVpLc5BMpV+mEn7bdZuKusXzf98YF/SZXy0HdhgaF/vClYUk\nOZq1lCLdUh3nGieqeCdAajzXrfnRof26yPqyfF0roTPfsJsCDrYjvziVzGQd\nGvdi4360U4n38AZfN7NUFJZhgXBtIODQTQ6k7srHo34usGHUF17lHItnIIbQ\nxrMBoKT2F/PQAojwZ54h+NN8D/ZV3NPuYfpC1Cbjot3xxTp3a1tUrqnMuQo8\nHS0CZapE1y3GBnCr74DbQyuL1B8Id5DxhzBZczv8ZBNpZqa7YAfuK+RieUm5\nONK3Qsgadg2XCrWHnwFMoUVdCouuwJ3O4Sg8t8EOUF1uMEyJYLLdUN0R02ca\nZMNwLWwLEMJv7N3fDTqerNrILsN9kuhN0h5KZJs/arlZM44v8MMCsxP/RDsQ\n68iRcrCIjnVCd1bJqIMvnoWw+/wzSPht6p9bgMF366/goBmwJBbLXsu6LGSB\nXOYxK1Twtgby498l8h+2kRdPR6YxDbeIIKOXdv5fsa42stfUTK45IAmYetDb\ndnJyZHX9CnUen53gfPaXniwTnI4vzj2UhqZomeG8RS+licf5OMoPF9zJWUYW\nTnaULFIYkb6GbtXaKFIoIZHtxJGKvUQ4VkOKxTuXByD/B213lhLR3kfVRAdb\nTQXf\r\n=sg83\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH6J5/+3tugmAnETiAgdGuKYDgqGNtAnlSsZaRTexLkuAiEAv52DHAU2G9ESJGPmPUt1eTLIKFuf3IZI0f1b0I5X6lM="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.0.0_1534868277406_0.5019613594151451"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "files": ["lib/saxes.js", "lib/saxes.d.ts", "LICENSE", "README.md", "CHANGELOG.md"], "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "66efbb5d34e00ef6c4e7f8159792e673b1769310", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.0", "_npmVersion": "6.3.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-gmqyHgzZKufy2Dm7LmjD0ws49wqoj9TdbJK9b0xUAz/XQjfyrxnipnOI3/Rhp8nyFiIIQ2fx/yppjE+48Lj8eQ==", "shasum": "cd812832234ea731a892dd8a6aaff95daab160ab", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.0.tgz", "fileCount": 6, "unpackedSize": 78942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhZPuCRA9TVsSAnZWagAA4W0P/1/5M4AV6hUHTHY7xW/v\noZH3Xin/Fuq4ljf7fk7aF10+e5Cx2IaEzZ6jHdCgIRK8J1QYPr8Mxt/q68/K\n/K/93Dyi8KJaeT2HJqTohrJFgnYWBDyEjBIU7pa5LjtT7faS7ScQ69n/Zk+F\nrclkM1BVD7XKC6UkkrU9TjWpebCCncUpfk7BJ9BeOi4iz6M1OZJVeOq4zfoG\n9K/2v1Pzpl4/ad6QvcgCg3pl6InOIl4PH+BWgjtzlSZ0AMovd/MNCqsXYbAD\nIyH8rifN0vjkTobyWuJai8OepTVakTHsBKH1rzrKsn94R1NyArz9z4CXx5r3\nF0GBUgp3elFJNL2/2kuGi/J++Lt3bs7LycpK+QX7HFwuwKhi/PnE/cDMWP4/\nh9PRMh8Z5LaHCOEHfL4kNdSlLV6bPa0KQbxewQMra5XgOCfu8y0HM3xiDdSG\nUeDHkag840NAI5IBA5pXVudInm6GeoqFhyDOgmziSb8VKuQUju7cv/lTMWhr\nJ2nBgMfCwqBodDKlTsS75ZdEgQhOXPLDu/10jHhHPH6WvVxvfTum8AqCDaix\nQB8RJ6JvarMOe0YSxU4uQWmpZK3nK0kPZgG8txLy4FuPHRC7d/xiF8fmwCSH\nU4rEE9WkfW30ckZkQsnr9uaE274kWu38+x7cu37MMGHYbuGb01mr9zzqGR/z\niPeT\r\n=QqJx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB+hl1vM8rYhhnT3CyuPSlgQE3V67Vn4DfFOTkiiNoxAAiEApGXwCO+5WFtXBD0Xw9N5Y1SMKrkAnd70ypLMf0U2tw4="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.0_1535480814044_0.8484123284771747"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.1", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "283bcfe1cf8ea6bbd56b9cb32b31551ce0d5fe6e", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.1", "_npmVersion": "6.3.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-n+c22uEHUsanE54wCHdXRDkk1agx9AO8GADpE1lp93xjvTpWy2FiAQOUZcdhYze80bWaYH7kzriyyckwF6taVg==", "shasum": "5c62b6e8ee7699879c5d645a76b8e2b9ad209b5e", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.1.tgz", "fileCount": 6, "unpackedSize": 79199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhqX9CRA9TVsSAnZWagAApO8P/1tBc7G+WqHth4Pzt3j2\niWMZ08m7dbdClyFQDDq8Wibrw3M7bLYMSgWim+taRUO4XUJYFEEUOUqYuEwI\nmaMgDixLQV8tMvCN6A7zJvBkkIEFKyNiN4Y5CBD7ZibVFSJ7366lUW7imVhf\nbPGMVJURapVHaCNz4jmOFVEA/S8WXbvuOpvFFlPnu22kZsE/ii0uf6+yYKp5\n+zVRykv05q2KelVHKgByTIXNwuwVD2VOA1O+iTyZrImlRmY44S0Hfx4czc5z\n54NsQ7iJsNPAfUqYQdVodgbTx6NvS6HE/Ajg9AiIhE1ulnH6B2/YwEZnNOyK\nhV1ysb+JsiUREuv7G1cLwoW9ox2HZJ0GXB/dk7ovmT431T7BgsxL1ztBwbRU\no2e2Vi53w1SC5/SXJioHjwMVAIvaa8twSnWIxWr7q+Pcd5ypD/jZpNE8UUAA\np6SC0A3lrBNRZzl5WY0/P1vermcGDTrKgFWiUHY62HaNny1Mf4YbhZjjHvoy\nFNFLeZjFbcaF2eVUl6Zoo+Y/WJcObLmkVuAaqzGI8VPor7tNUpZ6WfuA43NT\nC+DqoTiwNQdl8zc8ISGE2f6V/Cb+mC4dYFi6+A+ADhRFa5uoU9Kw+EvI3p4Z\nR7Erw6I2TTywbfa31NPxAl3p4UgY0iIDQ5U1gQtz5JG9Ypu9XWk+L53DdSct\nXLTJ\r\n=7k/O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCT0v7K3klDumlNSHqW8KnSVC5jpSOyU9IJ4Pc4IHkRSQIhAOxmdZxkdcQAIA7CLWfpvZf1izUcDt+2rAiIIoY/JBDH"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.1_1535550972787_0.8548960468019826"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.2", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.1.0"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "e981a25d93778acccc618f9bb55d4db8d3c43f47", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.2", "_npmVersion": "6.3.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-u6e6AgG8AZSrw4XriEgKoja0qoFVBR8uGn5TPu4fnRObeeMAyytaCswAMUqVmebv8jcAyoLJeOcTxhbx505Cww==", "shasum": "f18440ea0fa6a625d3d1a7e13f4c3f10effc5f40", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.2.tgz", "fileCount": 6, "unpackedSize": 80548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiY3jCRA9TVsSAnZWagAAc+0P/RBUpzUb3280pA9gGg6S\n/vkUOPat1J8gOzeT9714g3/iaQ13YapZw2VfDuLEvGnJUN4R6ZzuwHs1zmmW\nADjZjXzHuWJnhptpnthAioyTzUpWuOC0aE5k1oBySk0zd0nu3aA8yVvWZcHP\nAHyJIczqWaSMRLDCQZ1NCTkeSmycY56l1GTIT54ld8cJPK1aAF+9WbOv7Agu\nRaXn8EY7+35pcv0hH1aaV+wb3PSRlH7Zi3tMJ0J6RlNBxdMHS5ZUn8qm3rc4\n/kPgRNzjGA8i/TqqbCNtmBNbbXLpgwQba9O8uxWpnwDF6NSBvrozYJKxQDNT\n0D9qdAbnIRsPlbnHJcRABo6RmQaF5ElkLwF50kPizx8Yx0sFTxX6WkJuNJTR\nYPS6MOH52wMQEkvCwww+QKAo0XxGELY7B3EM7H7E4n0tZp8qDv6hq0E564ta\nXHEEI5BVfF21t21x2CWtU0flJsiZets63TNRd3pwoimS5VemGPgDU9vJkFQu\negXxlRHfze7yd8/coGhbmxPYQqWMQwYd9NYfMITD8rFwg7KsV4ZU6qBWt6jO\nNGYJ8IJmeGDMu/sC41hxhdceSueO1VXA4WMRZuWev1qmVRS85nqeOdbry1VP\nP+nfVLhVIf7nZAU1dJfOCtbLwjkeP2kis46svrbeisKEJGUXt+F/AkaoIeuC\nt6U8\r\n=2Z+d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHFMjsQ12Qg/A/ptjNy0lqSxP+/C98CaAUJnyaw4I4O3AiAM35fAn+g/nlgR//5X+BUHjb0/xx2U3T88LT4dByrG/w=="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.2_1535741411049_0.6059172294013646"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.3", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "commitmsg": "commitlint -E GIT_PARAMS"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-angular": "^7.0.1", "chai": "^4.1.2", "conventional-changelog-cli": "^2.0.1", "eslint": "^4.19.1", "eslint-config-lddubeau-base": "^2.1.0", "husky": "^0.14.3", "mocha": "^5.2.0", "xml-conformance-suite": "^1.0.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "cf0d0dfe8e45c834a7121ae1fcb8bd7818e58b60", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-Nc5DXc5A+m3rUDtkS+vHlBWKT7mCKjJPyia7f8YMW773hsXVv2wEHQZGE0zs4+5PLwz9U5Sbl/94Cnd9vHV7Bg==", "shasum": "334ab3b802a465ccda96fff9bdefbd505546ffa8", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.3.tgz", "fileCount": 6, "unpackedSize": 80777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsgO0CRA9TVsSAnZWagAAzIAP/0ix3kzwp05uLHGTPggJ\nFqPXniNRWVBHZiOhoqhk7R34qd60l5x5JZvXzLLr0eqFS6W/FscMTsnhMYds\nYuJpuWBU89yGkkiBjev4y/P7VJRu6jEukml+lVHbYSWDqRDl0IAbcTXDROlq\na5CpNqgITUN1zZgZSY1gxM3ZEf15EdPugcJQlEK9pZearydkBU3QSkkl/yAq\n84xfh9Jq1d7Cj6XnBXYYytzIpQmqI0cOsSga/NYijxOubsE4c2QD2PuKoqQY\nI8PalFnCpvf+EINvpvQH29yl1Hsl+lnjYvPyzjXcbpMPf4koyDy5co4ryAL9\nQ/W/FrpkoE42G76nncp8ZxR+tzBHlw3Y4fEySq6mZqqEOwTQ+lJnuW7Ye6qe\nbOoeL8bMRmpnLlRmP1CuGcHd0Snm0uyKXd5lB1oTPA57nQ0y4sCaiLLnkg7K\nIzhq1uivgP+2M3ENCNIwTd0B6DC4R8wZ8IRDk3B0lP/G7Nv12Ho/VGz5oVtW\n0qk2S+SWOBJ00Ma2cyx0dnkE8rEM6YnW0WUjyGmA1/w70QD/brIti9ETas9q\nmu8bYnJBuzIsJJTflZokm0XK7vCcjy6YfocTWmrvwOSRbQGwG+LKBufYBsxc\nhmF73PfnNZamWvETcAasnkNrodlWjas4T1TiJsCX7doLKUiR0Cjbdl3kBJVZ\n5YrG\r\n=+o5d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwX9XJwyXjdX45R/5JNzodVdxAhqSUvs7q0o5GMIAeEQIhAJ8NazlMiMy4aLidaCCJitgHGzXTx0kLNxcxlXahOhWB"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.3_1538393011743_0.2944931286053647"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.4", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-angular": "^7.1.2", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.11", "eslint": "^5.9.0", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.2.0", "mocha": "^5.2.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "bc92befaf2a05fe674e3dcf8fe587f38fdb69827", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-GVZmLJnkS4Vl8Pe9o4nc5ALZ615VOVxCmea8Cs0l+8GZw3RQ5XGOSUomIUfuZuk4Todo44v4y+HY1EATkDDiZg==", "shasum": "4ad5c53eb085ac0570ea1071a07aaf22ad29cebd", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.4.tgz", "fileCount": 6, "unpackedSize": 81146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBTARCRA9TVsSAnZWagAAz3sP/1gHfZ8ScQC487fPv0WF\nhu3B5chyfJUEb7WczjmKnp6EKpMyxarUcDHhtCE+r/bCHddwNd9SOzSKKSlI\nmfGQbIhfGTUzTzvYhReNnDo7zLO1fShbKku+BC95HxPIZqzkhCXWkdG2mYfn\ncPaIRGv2510kmeNwJQ5kUl138PLhgzZyheXj4UeCi7H9S+X0cAJgjO1Tkil5\njzrg58sd4TlijZhtu3nZVKgl3c+GnOAJyVbmNnNm00Ow4ph3MCRBCGfv03qC\no3qWikVaJWzmFHBBVLC9tGRe+B7DI9pODSwCLJXcTa2a0F3OEbkPe3MYBe0n\naE8mt05NBEMHXqcPd6lOuLZbhGKzEJ9ZZ6m3Pk7mublr8wAe2QFU3TBhraao\nWcOn2c/D4Uk6LFCFjbicKTdolvigo5+7Gj3+YmrZ1RdI/PMP3ow3I582vxSt\nu7EqQb5IH06SmFiv0WnNBf6uck30SdvCYXB1uZCN9B3FYjoHwtZXYGM9CZIv\nzd7EfQ2dMqDE88GnoQJQFUlA3OpNvJQ6GGR+9XK7f7oZUmn3xUEdix0ru/0B\n4/XdI/chK2lDOhTWY+GxXJ9RKn3T38ZIKzxLmZU0dE2ox0/TbMC//Ef1MKCu\nOm8b3twtSwVAuyzApA797N3JZt82KtsRDeo1QPSa7ybbPZOOYdzh2PDUvGHi\n/9LB\r\n=kOGQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaMpRmPa0+wjXaHrz7VIK/QIdYF3SmMkd9anD6VJ+A8QIgLeg2zlxZIt4gNOLrDpBqlq3ADwR1AunsY3aJJlsmP94="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.4_1543843857025_0.8650368661772478"}, "_hasShrinkwrap": false}, "3.1.5": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.5", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-angular": "^7.1.2", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.11", "eslint": "^5.12.0", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.3.1", "mocha": "^5.2.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "7346426868403d2dc5f5cf6a13f1420eeb9dbb92", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-2mgiX2VOarcQv8G40WdJ5QJniYdsPr0yGedkd98PqApodsS9DG29qyHl/X65OILm7Bapd1/zUUvTHVZwNLhXvQ==", "shasum": "ecbba12c7ca99f87f70dbd14a6c57b2b5de8b298", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.5.tgz", "fileCount": 6, "unpackedSize": 81350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNJX3CRA9TVsSAnZWagAAT1QP/0v6CHynwrC9SSihgbqA\n2TI0ykpAP+2+GYMBgmFJyZr5BcX3GnvBtJgNQEuXKHGnkjLDk2HzHnEmwtY7\nr7cVMnym5nsfEFCJoKYKwxVHY1Qa2d+i8BzNGfAsqEhX+e1KvDHMsleU6fYf\nnA5Znqu2XGNzWZVDKviJdT+Z2bX/QN9UA5/7hiqbFpquGgOhYNDgGggq0kAS\n5ShqerekrGl5FTl+Th9q15bG5+h/tXMvzt6ivWC084yRmZxRLXzxKDwIz2mx\nFYCHOFLys+i2gEAUrUmwxPnIDL7cnM3cTCOt/dl//gZMW1X4YH67bfC0e5qZ\nLigqPnK2HnU2CeyT1LzDK8w40RN3sHbSiQksB88srIJ9mLE6RbGxeNS42UxL\n+cjjvkwmGnaHfwEztKkdcyafPmr7R4hpZS3yzt13jpmJdRSxgaSZbOtqaI29\nXbfH6MPlotC3z6v1vEZfDmOQtkyUIxPVSvHc8HVsMhX3yDciOW2BuHdCY+IW\nazDmGB+pUsn3PaVU/Ya0eE1/R1dl4zCgowKgfVIZs77mfuzOuuhaX3avCKEz\nTVJKcaDpYexBat1bIXSxxDUrO1fZhR1q5SmIqrbhP8caMViHONrUnUdn5yVt\nQmGrfmbWKUcc6vS/XhCzz4OqSdKmgO3x46LMcCosagmpIAXs+jV7+F/At3ec\nlXMo\r\n=UZvZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCHyzaKRh7ikpwqakfDWIkGKYCWYCnGlnvA/WxU89AamQCIQD8nyzftejWfWxRGK0CwtSZZN0b0pZGM0xk5zZXDOpFgQ=="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.5_1546950134606_0.38532773000219467"}, "_hasShrinkwrap": false}, "3.1.6": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.6", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-angular": "^7.1.2", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.11", "eslint": "^5.12.0", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.3.1", "mocha": "^5.2.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "7c4fd67df03f81d6c518ce948af8be5b019c606c", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.6", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-LAYs+lChg1v5uKNzPtsgTxSS5hLo8aIhSMCJt1WMpefAxm3D1RTpMwSpb6ebdL31cubiLTnhokVktBW+cv9Y9w==", "shasum": "2d948a47b54918516c5a64096f08865deb5bd8cd", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.6.tgz", "fileCount": 6, "unpackedSize": 81684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcP8YtCRA9TVsSAnZWagAAAe8QAIW1k8pMUDFprTcLeQIC\n3W5Cb7IaHmbruUefAjdRFKNs5CO1E2r5H/HDijlL6AJ0/+z3R+0uAVtks2Ad\niRbXGVfsZBLmoCMIhrKbctDVdRZR6F1z0vPNU9G9RJvZN3fWOTJ5SbcxxAh4\nWD+lOxqysdWQz6Xy/tCVVQcr1JZqtxOXXYtL+bHMemUkjo6nfGW4L98sC1YE\n++vq+aohtXSlFd49dyAqFctjy12HuOVbW8qwmpdB6xU5VtAjiK4T+3vpRhu6\neJ/ycmmTlmVkCLlp0CfUYD/cRcMZw0xULW2/U0onKPkpgMelcLNyz70Z40fv\nFeaGPkFLt7VAsiVxywJ7n0Ia9VM7YHAfG2vnNBpgv5TUcR1U8N7remdhaPpE\nwmVLuMRmqQ7feIyRSHSoRehqolXQ5pqU9fCc/tUkGPL+CitJuxPGcMytdLnr\niYER5hBQC4soVKF5RO2QiMkJinhLbNbZdZQeHkesRbiQTCJ+XfDcqb6EQZmI\nUY3oJrlucy4zWfW3yewocEsdWSTaZG4B8YHeHTcJdC/zyeujl2ZUVRCfHakY\nw1NyU+fyEx5YaVvQkW1gqCd2Bzcci0n03x3F10Eo6sP0NVUJYm/nR6RCCHmP\nHfB4aTvRRnaE0WVE9ZTs0ywMVHHU1iG7zqxJFckzRV7qvzBkJWn2Ct7LcVLv\nxM5W\r\n=WrWa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+WBWrxXVgDV0l1/UAn09WQfTCrABXsqIgYHIBxHzLnAIgaDQzMYoTWQI+AisbePEorAGyqtX9uRaCGZsNbQan2cQ="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.6_1547683372944_0.7534978988674492"}, "_hasShrinkwrap": false}, "3.1.7": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.7", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.5.2", "@commitlint/config-angular": "^7.5.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.12", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.3.1", "mocha": "^6.0.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"eslint": "^5.14.1", "xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3686b8edd27dc5537610061ffc21b3bae1cc6fc8", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.7", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-HtgHHSzIpk3klUoYDucEhpRfKSkmaGUXVGcy1Sqzt6V41Pw8r63HrQDmE0LzJlWKyCrmI24z3pRuL3KLymtuZw==", "shasum": "d322d8295b410521a183c659a456a5c1a51864f4", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.7.tgz", "fileCount": 6, "unpackedSize": 82136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccIk6CRA9TVsSAnZWagAAQNIP/0hfAthi3InBnO/Du7Ry\nf6qIsppkQ6dmdUUosz7KNROLFTof+WKGrLWZGJjXdg8vXdHzGjV+u3jdunPl\n1hPu8BYI35RnklKSfcA7dn27yXBEFgi5HDRvEYLY5uYV++ugA74KhH+G/0BF\nWZl3y8nLzyw0fM+35s3gSwiopz9SNN8zvEsdCk2QUFa6CB9QLmV2M2GT0lDR\nZPhCTjHr/fsAhbe8WidVOwc+F5Ey+Uw9ietpV5v7Rsm6nacV0/hq+IyHuNaE\nrzTFkfeUWeHc9dpHcbJYhNp6kFjVFHl1y2w2JgrHX7ILYxNsuzLLRivZuxSV\ng4kYMRBto0adoODyT6z6MNiHFZiR2n/6ZvS0rEgJ2dfzBwLBFuNdriuNPL9z\nypBLMPPJrHExDeUBr0Bd4ssUT1Ef1eezZIwjH4mVOPgzXRYrVqFcIB3pqe2j\n0MNSfrn8tZ0DSxFNa42CQTzqJEjoqo9/C1dy0wRZ9p5KwZfg3uCJl45bj3Q6\nQDGc78g+R7uJaaJMLABD0+QCCII4ke85adrQWR7aCYh14O4O7zA6VbQxvMZP\nPDON8ohlKVNIyONrYfV4lBgC04qxjeUpqf6ySwSlJqyGcsiQMY+UW40YQxtU\nMRjg+Ny+cHTEQEnkf+EiL84e6UMUtEu12aHrwX5GgR1IzB253DSWUONJ+yMe\nTYc6\r\n=AMFa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMI83ls0/MdGqLX50jV/QqGh8jiDyloxqsp859at0ScgIhAO531KlxAqz8SgBvFFFEJtTux1K3WTIxblYKXKrZRV/U"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.7_1550879034225_0.03343473991318535"}, "_hasShrinkwrap": false}, "3.1.8": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.8", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.5.2", "@commitlint/config-angular": "^7.5.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.12", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.3.1", "mocha": "^6.0.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"eslint": "^5.14.1", "xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "1a4b4804d0c82861910a30389098ff03ca89937c", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.8", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-KoRNL7FNNa7cQNNViHJzFUTujswjJonF7ZMYaA42AyvpQHL6JgCFLa79uSDRn09PqlSHtx77Z550aESiohCetA==", "shasum": "dcee101bd18b220d06a7576aea0440ade329a8fe", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.8.tgz", "fileCount": 6, "unpackedSize": 82243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcc9rICRA9TVsSAnZWagAAySAP/11wViP69R82S0+nhJ/b\nCbBNpEx34tYxmoWGH6cYy4Yfn3FwEQhHt77bE5MCDRz7H3Yt3W3CV9g3qSng\nyIqwyFz4Fy7pxxfKAXkEN7G1hLpsYCOwuZazm9Ht82+8LaPWqoIK2JrxOjBx\nDvpW8gWuS2oQjBS7cyYf6MiYqf2vG0UWpYCxyXjuwsDqe9zwoghaK3+hu0U9\n1hGCsyH0mwWdLNrHVMU0pkaPcbCOxoul9OPpI6dJdKB+0ANs3WhmfUEci0zk\nYj+rtxPHzb02qil+EDpUYJkdgYeAR18X+o53iqu+iM4rZxV+p+DCCjH71dgX\nFRgjYDtiLzDO+n09+LyQ9U0TJexVApLEfXdzyog8unfsMXqGBba4q9foiZfG\njNvtKmj4J+GqlpD226+VVqr2C5atBrVlAVZHb9/YkKDg5ufCBGR9cKjHj6Nw\nyr13FqhbCFfr0TScswPktPUbm9okaZNgnMXjgna+f44eXDKXcbny6VyZxKYo\nfZe5rEo18KVnbqRWD1woJFIZJV4lEPkuEEp5OxFkJuMwP9QwEgmB4Xfw+pxs\ny2HbCFXbZ/s9PbIiqD/9FOPgqtBjGxim8SAr+jpZ09xkhPGvwuVhltYrR0Bc\nVbCcZ0UWJcgjslVoAb0uWbAtgd6DOZXBaSyb/cBS3Z+D4YFdq/SHm8RgNrYY\nXwLI\r\n=GPtn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+5W1Zdo3F7QJ2nVvJpJgqlafsmJKd7FX11xMYMy3dHgIgXFu8GkV9okTHI6txiNiFveqKqZP3bSTWgn/yMqhf/KU="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.8_1551096519798_0.08304439401365338"}, "_hasShrinkwrap": false}, "3.1.9": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.9", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^7.5.2", "@commitlint/config-angular": "^7.5.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.12", "eslint": "^5.14.1", "eslint-config-lddubeau-base": "^3.0.1", "husky": "^1.3.1", "mocha": "^6.0.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.1.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "d45ed6e796b08fe4b45fcb428ba4f2196fcdd1dd", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.9", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-FZeKhJglhJHk7eWG5YM0z46VHmI3KJpMBAQm3xa9meDvd+wevB5GuBB0wc0exPInZiBBHqi00DbS8AcvCGCFMw==", "shasum": "c1c197cd54956d88c09f960254b999e192d7058b", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.9.tgz", "fileCount": 6, "unpackedSize": 82462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcc9tyCRA9TVsSAnZWagAAmxQP/14HBYGUWNcsAKkE4/Om\n69vULztLJWwQ7NPp05IHhYEnRKeJKfnXswQ51/A3qyaPZ+NPZiKuqwjpKYea\nAjekfO7/EKE4ok8+5s7lum5HS7k8+qVkA5vi8/kwPWpJpgcv4zFepBQXLqXS\nZXL10gDhA5NGhACRAwH+OiCMxgnZOYWwCBbz1sZXfV9BFalj1440LS2XGAMW\na/1r25TGgtJ5LCn9OydGtGgpGUov2j8WjQ276Fypna5dR+oq/2g4sqNAaYUS\n+B8+ss+K9Mv+eHebc/H1VFXrlYO94LIdcMKNk1WEhgeF7FG/GwxX35+xip0/\nFkDEUgiUtEVk1HgjGf9ui9zQsXUV4mZc9d1aOlgkffouaF2TJGJ3zdgCYjw/\nrh0rGCXknBn4u9W/ps3ouqIj388UkEanXAOqxCyiuyd20/ewLUhijp5fJ+A3\n8yFsElxqqrBAyjRM/h5k7YP5LhqQob+WjQimDWQOrsiIfEzw6jx0pa6a5wbE\nbpfsIB92DEZRDbh2ZWNg1OLa7pqHWPc7jIaqtITSbUSxK3JBQnZ1m77INXg+\nnnIg37G3SSA19QBJVk2hQy0Wht56+4Bu8A8tlbbYQxYPf5cXtshCywX55GCI\nw4GBx354/VohvtSo+5HFYPPrze3kkNKTb7J8uVxE4blCKQcMt0gpacE2it9S\nNtN7\r\n=6lNC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEpZWktRUN75bgaPm1CWlzbpYLAyGqRz0Ye09ngnInvSAiEA1bW5U4RoYIAgw735cMywVescT1yuOLmpkByjlN9Us/g="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.9_1551096689526_0.5263190406630593"}, "_hasShrinkwrap": false}, "3.1.10": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.10", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.0.0", "@commitlint/config-angular": "^8.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.21", "eslint": "^5.16.0", "eslint-config-lddubeau-base": "^3.0.3", "husky": "^2.4.0", "mocha": "^6.1.4", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^1.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "1a574cd38b4dcaff3080b81eb329b66c612f0227", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.10", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-G/mVZCCGhJqgS+I7wT5gBHyTNXLe2SQcu2qmhwl1OKcSHyJEXKQY2CLT+qWIxV+m6uiGMLfSOJGLQQHhklIeEQ==", "shasum": "6028a4d6d65f0b5f5b5d250c0500be6a7950fe13", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.10.tgz", "fileCount": 6, "unpackedSize": 82992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdACwSCRA9TVsSAnZWagAA87IP/1KNq1poMfSumRYj/NBb\n+pB9X7uUEXBnFNjN9HrgQkRMLgYt/2XRRKOljOZ1wq8iedybEBg2FI2R0/Cw\ncVHPg0ju6eTldFLLg/Rjdwqss5LBZg6M+5yGLVKIKuA+Fa4N9ZK2asKhYt8o\nHdKXwk3tg7+MQ/vaedqw0r2wM3+qspoqgJQnR3h7vDkhD7MsV2AjBGs5EapJ\noY7+VxDpARC59Kxlh8OhVwOmXwrHNJ94tNYoCw1GCru3zpJDXVFRch797Wim\nrpHR0LU9C2L1L14lvXzAFGjYzCW+nFR/RENkOhURO+W1NOamWcLSa90lfWcS\nVYcIZ3qvinxsO+nYEItzBdMfJs3AsDZdePCwHB3aqC2A1u4zbiWpROo5337v\nNR1pIyi0Oo8Axabb1Ud8PqZgQwHoUDkLAZKqvN/krVJe83vKoRkLmQ7CEtmY\nz00fwI0tJ+7n+FpLDQpCtTn412H5NVmC46DzFefvZ+TxX3mWHe74lN+PVmAs\nco46jZbQ8JGU2nszQ9nY8b/luEgDKNbwJClm125/0036eyfsl1PmNG36DTNH\nrHvxpZQpnIyUN6qQlySU+MW7noEHkFdvYqpsmoC1ml92vImz/FrpwEL9+gpC\nIuNI3M4RxbAFrNK9IHPIYCQxmzSQIcSN/6RUxL1E0bMf+ZBjJhi7fEC4J/Mm\nf63j\r\n=mUys\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBn/rVLJdpQSEr/+4mFujsvbiOh9F1VoFIrvT+06aAdiAiEAma+xPiIW3oQ8fq9l6N7AHy/HUjZWwZoTR6Yc9lE91mk="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.10_1560292369568_0.2954979455542188"}, "_hasShrinkwrap": false}, "3.1.11": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "3.1.11", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.0.0", "@commitlint/config-angular": "^8.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.21", "eslint": "^5.16.0", "eslint-config-lddubeau-base": "^3.0.5", "husky": "^2.5.0", "mocha": "^6.1.4", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.1.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "174947e60b12db7919b5ae8c84860a1e1f171a79", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@3.1.11", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ydydq3zC+WYDJK1+gRxRapLIED9PWeSuuS41wqyoRmzvhhh9nc+QQrVMKJYzJFULazeGhzSV0QleN2wD3boh2g==", "shasum": "d59d1fd332ec92ad98a2e0b2ee644702384b1c5b", "tarball": "https://registry.npmjs.org/saxes/-/saxes-3.1.11.tgz", "fileCount": 6, "unpackedSize": 89081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEqIVCRA9TVsSAnZWagAA/ZIP/RcQjieAz/2or02nn1XX\ntndNBG+v308Mi8UWtFUTMq1uQ7WWuDBuOoWNW8/jqofc3BapoI8qC/PIoCdx\nGMgC+dAffaMk+pFUPQlBjTw5WF/AunqmjMvL43MUwOVEljaYguczW062pXUG\nsOWzlqF3B/e6iNb2OBX/4pUzF9YAKj5Y9w/Iy0YapVHmnkYnpvZV9eCXRK19\nEctSX0sVBZ+CMN4IFkoUO8j7R8YXAywStzPNJJG/NYAmY7cqPR1mCEpg7V+S\ng2tKNub+IcQhko7toyjKWfBCmhpJnVUZcABcPtHwDskiICFcA5r73JP+yPSB\nB5JVdU/wMCXbNVrgT/+EICAqHbqyOfpk+hRwnzQrl2JywAEWX+w/5HK9gh6E\nhaavCEye9KNRcD6NRsOFzVxrHKIiM5Jf6UNoN6J9hglUT3anqAfz9z2E7xIe\n709CVDfF/HGxNBbJTlc7EKeGxpYAgOV22/XgPNYuMSDfyMVqWGDQTs6Pq4Gz\nGeRchNpTr1EmI9ZXnAbCoHcLFsYmyYMxnLLUnCpI84OkdhKRV7vyP8Tz50wa\ntuXhiJb++6XBgynNroYEm58KROtUY6mBXJZMkaIU4SXTZlzP2TB5KOlJw4U/\nL4DW7tLfKsUxO65H3KD/drmTbJNQFxGUBjOaCRXxZbaphsGpTmyaSywaHKNS\nz8Po\r\n=242W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGKX+PPhM1EZoLOJKl1HPbODhGAJuZVfD3AXIwfrowjOAiEA5i30TueY4gTVrf2ciSKhrtR2vwNiJo7n5dYrhiZGo/U="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_3.1.11_1561502228160_0.16307492002002522"}, "_hasShrinkwrap": false}, "4.0.0-rc.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.0-rc.1", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.23", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 8.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors in DTDs cannot be reported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `ondoctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe elements of code that do not have JSDOC documentation, or have documentation\nwith the ``@private`` tag, are private.\n\nIf you use anything private, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.onerror = function (e) {\n  // an error happened.\n};\nparser.ontext = function (t) {\n  // got some text.  t is the string of text.\n};\nparser.onopentag = function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n};\nparser.onend = function () {\n  // parser stream is done, and ready to have more stuff written to it.\n};\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nPass the following arguments to the parser function. All are optional.\n\n`opt` - Object bag of settings regarding string formatting.\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset, in which case the file name\n  in error messages will be `undefined`.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `position` - Indications of the position in the XML document\nwhere the parser currently is looking.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "6b91080531b53d26cd11ee9160dc990eaf7e8841", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.0-rc.1", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UiWS5qmmT6Eg8qOqY0xBo0+0by7/dpdwg8NZhRmcIAganVbojHSBog3Iljz21qY91Wi1V73avCvT+wfCLytl9w==", "shasum": "b03218b891334cffa543fc2ffc3e8dc32311611d", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.0-rc.1.tgz", "fileCount": 6, "unpackedSize": 102049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlMWACRA9TVsSAnZWagAASIMP/jmpxr7B2Jw5yzsxCuRy\nZG2D3o8mGcTQAaRqY1y6pFjwLOI0ApWnz0I0y6GazTssgHjaS7fhYuCTXELR\n1eXKiFHJZdXnKoB3++kDR6jSQIfpoUVfIWxEb/KDhU7UaC9EGKkruoEXU7+p\nI4Z0kZ0/Wo0u5H9Z/nTsYYIExM+dtwk97ecgVW/3a/9yBFpkDLLnjP1QFBTu\n+1hHd6WV0OkOu01JeGMQObmWkE8jcqTFNKCERBh0NMkYzkQ+9p1YKpb6LvdC\nOWvP4H4wRgcCo9E8faoX/FNm4pSU6x8P9S38cNns2jJ9yqzg8OZKS1910n01\niX2XtV4QgH28SRqhjq8B0xP9JwnOVB2ywHC6oiYGOq8C0akudYrMvZJ19RGu\nsvfh1/rujL7tdS+pm2o0loUM3BAT8wtypR+X7PbJw+5uvM41nzIhgZ9kiZk/\nzM6rsC3vu+Pu5b1GJ+7rHb+ephnj3cOPnlR4XfgYhQF7Zqxppx043oo1ts8M\nGtjYdLTfIw1xbybcNaE2GlEcgHDjDu2fjWj0h8aNiqw7e+poUWUFsugyutqG\nmOOMQOsHRCrsII6uZhVu53iXiCPiCbpmoHsTW2Kb87OvbjoWOKiFvwc7CugR\nvPYZxYPqpu+yNISx8/kM3T/++xLQBTPtVPmfCE4adXpDDFfMZPM99A+D2v5B\nO115\r\n=dS6a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHNp5YtiQ8ScSfuXMm03KayjAVAiiyF8C+WJ7B54YcjgIgFKQW6j+P1Mnkto29qEC1ZGzY9oAmK3ObLjpdzan+sGE="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.0-rc.1_1570030975888_0.7156065601476864"}, "_hasShrinkwrap": false}, "4.0.0-rc.2": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.0-rc.2", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.23", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 8.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors in DTDs cannot be reported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `ondoctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe elements of code that do not have JSDOC documentation, or have documentation\nwith the ``@private`` tag, are private.\n\nIf you use anything private, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.onerror = function (e) {\n  // an error happened.\n};\nparser.ontext = function (t) {\n  // got some text.  t is the string of text.\n};\nparser.onopentag = function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n};\nparser.onend = function () {\n  // parser stream is done, and ready to have more stuff written to it.\n};\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nPass the following arguments to the parser function. All are optional.\n\n`opt` - Object bag of settings regarding string formatting.\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset, in which case the file name\n  in error messages will be `undefined`.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `position` - Indications of the position in the XML document\nwhere the parser currently is looking.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "d2fac16508c179fc3d2d4a137d0d614bbfb27616", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.0-rc.2", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eECk0fjhkR7bQngKwWU6EY7RVLZzXTfp6Se1/IHkjzrJVFiL9tR1f65dJUboBsjW8HckIzdMgAlblwVGUZx4HA==", "shasum": "d57ecab0406164d1e01ca9eb3e23db62f040b7cc", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.0-rc.2.tgz", "fileCount": 6, "unpackedSize": 103126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdl0JbCRA9TVsSAnZWagAAC4kP/2AnDFOLGthcn8Xq3jN+\niBko/G3RNxclFJtw7l0ZQdwQZHcdNb56Q9FtPpmsgiFAViQbaaIQC6Yw9L/h\nWcgQNw+qM9pt6YN6P7zoK8odX2HXkFVMGrhqro7UXvsfBVaTBi+yXlv1y+uk\nxJgcX+J1/YlVuPb7xtrqW1rz6QtP94mkk848ujIgUMXizw+whfKQ/qFv1ExK\nzWTIVH2cmAegalYcrZkIBD1NoNloBIKC4AVcX1NDpBuD6USZZslqaXb869rA\ncpX1FZbN5PQ9dJgNGKuJEVXbl/CqtR3JKm30F45CSnyZ1cD4t9curAIbgBtN\nWd+V9dl8ul487JXbJqOLrjzmcHeIKnzBpYNEqgdyEZF+x6zoXrSmWRQNpKBy\nOv8xl4aPH3onYf9tl1GaHgVGog6R28JzQV9t4PuRAWb8GrAZAGTwTcGR5V8a\n/vQCsGIF4Y1Bh0x+bSClkM1Owxin0D84TBhi6zLHAwaNOXZvumM/cDRSXJlo\ndOFpvpSfnCGHA4YXZ6oS2QwJ3wKJYnUbQ9lsWsGK26M62iuoXNMlo71hUkvx\nmIfkbk6kpWCz5EN4tSDW+0Ed+tAgx1hGyjWtp5aZpOruNQEpgjclGLIS9Nd7\nIMAYi9wb0Cqt6C4+YRn5hiN6YRMhI0sKk8Yl7vwBniEqsG2CI7LiXa3q+Iec\nvg5k\r\n=l7Qp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCidgRxUXPcDRb+EcGIKXmF0Mfmdb+jn4kSSX5c7/GZ9wIhANSMCoXox3nlPwSAUoMzwIVxxgRSZcXvSPwYGD7JUVlt"}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.0-rc.2_1570194010839_0.712478351272579"}, "_hasShrinkwrap": false}, "4.0.0-rc.3": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.0-rc.3", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.23", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 8.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors in DTDs cannot be reported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `ondoctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe elements of code that do not have JSDOC documentation, or have documentation\nwith the ``@private`` tag, are private.\n\nIf you use anything private, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.onerror = function (e) {\n  // an error happened.\n};\nparser.ontext = function (t) {\n  // got some text.  t is the string of text.\n};\nparser.onopentag = function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n};\nparser.onend = function () {\n  // parser stream is done, and ready to have more stuff written to it.\n};\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nPass the following arguments to the parser function. All are optional.\n\n`opt` - Object bag of settings regarding string formatting.\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset, in which case the file name\n  in error messages will be `undefined`.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `position` - Indications of the position in the XML document\nwhere the parser currently is looking.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "97a6894104b1db7f4c0b8c4e7c4be0a2732ee4bb", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.0-rc.3", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hejzXKjIMkrBrts0P+BSLq3hEPpb1q08I2EIvmR423yuB9fMRiT3yfhyHBRiVV/YJ4HdTK4XboGk0pw/h5Q+gg==", "shasum": "26ebb41cd63dc9a8269080a5028e2b88f986951d", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.0-rc.3.tgz", "fileCount": 6, "unpackedSize": 104445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoMNuCRA9TVsSAnZWagAAq1YP/0NbyLGj1hRFJTSXuyo4\nN5YLNi8fcbpFKjJhjJ4NNRutkHnDWVR6j3PmsORTn5fh44czfu810VbWAeQB\nCw0rK/4EwfrlK9ArN4uQPv3wjYb1r1YXftHhFGbi8jl4EMF9taUVGtxP6wHd\niT772iCiZVURZhWsa6xcL2noZx7WA3Z4F9EonDr40197EfwLkFXv7nnp22bz\nwxbGlW/cuY6KamEIgoyJNRkxTm6dAZHjLwSSwXP4tmw1OqKuSGNXF5b2CpXy\np1ZAZe/u8JNnTcbeGgff6xeIMdZJFnMDe/M8BGd2hPw9T18GDTtL/j/eRe2Y\nOGYq6QEc+VOTzA9UJT7YnQGNnmr+C+gyUqx/YOZhuPFBkQjQVMf1OYYzKSkv\n61lx9bJ60F3tusjJebM04/1gPOyq95fV10pv4DGBzA6u9FdeyvMqglBhlfjW\njHcD4BoIdFIeo31lF1mXmTrECxxu986A6ys6c8rKnRCCl7mty3EyoCX/rk7g\nTeyxsoQsiijWsVAkgLMIuvl4MhUJTRg+6bjISUkyZLKsYgNhQC7Wz4SJI5Xc\noI/ynLvA/aHCfl4eqg9KYZLAG4ZbQPHiyF4aB3t1fagVXAbdzAuacb9rAVmv\ngfmMt/NWZ2fdYj0e1ISAj9uVR8D0NzusiD6Gzm/LraUQotBWNIMjlaGHBc1K\nrKQ7\r\n=snWu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICSbTHGRAShQyapXMbgLh2137wB0ZXNn+XGYatPJ5PypAiEAmX0s+t4nwOFmRzvU7PNtFsrNz6c86xAxBXkQmp8cglk="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.0-rc.3_1570816877541_0.7527308385296894"}, "_hasShrinkwrap": false}, "4.0.0-rc.4": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.0-rc.4", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.25", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 8.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors in DTDs cannot be reported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `ondoctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe elements of code that do not have JSDOC documentation, or have documentation\nwith the ``@private`` tag, are private.\n\nIf you use anything private, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.onerror = function (e) {\n  // an error happened.\n};\nparser.ontext = function (t) {\n  // got some text.  t is the string of text.\n};\nparser.onopentag = function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n};\nparser.onend = function () {\n  // parser stream is done, and ready to have more stuff written to it.\n};\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nPass the following arguments to the parser function. All are optional.\n\n`opt` - Object bag of settings regarding string formatting.\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset, in which case the file name\n  in error messages will be `undefined`.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `position` - Indications of the position in the XML document\nwhere the parser currently is looking.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "f18917fa421355e9ee9e2078ce3ac706f49198cc", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.0-rc.4", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b2p7sNqZSsNcM55buRsaSPa6bPALEZNDJRK8NegBBgAExhsI4d/2TH5CQr43vmhL5+VEtyz2sB8h3gm7drIC8w==", "shasum": "060aa581756903e996530247164a8fe4d20e94e2", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.0-rc.4.tgz", "fileCount": 6, "unpackedSize": 104948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoMPsCRA9TVsSAnZWagAANxgP/2yTdn8H4uIqm09o+GTJ\n8uEx5j9Ted0v2iEcnY59/ZyVVEipatTX/REk87S8ct0a+BEaH0YlkEunXksQ\ndE1SB0Sl50aoCq/R7lf2773zMiMkdhu3Ihxt2H+diKkmF5XPQMEZ1B3bEq0U\nR5x7iKRigL3qYcqvT+Gmd9iKXNn/vXR7V1d7TRyB1fEMXZ++02irO6TccVil\n38EvzxccJw050TSayRC6t+ravzo/Vjo+N/9tfC0oW0S6D51o17RbC9dDIp81\nU3IO/Q82BZmVU94jF8chVFmjPIboUSSyg9hC15BZYx6bjEIJVn3q7RFWe8Lq\nEox9h9HQKF3sT8K0qWIfwfdlR6VE5Ypba5OQ9sWoRMYBzCNzbXNyv1EDIs7E\neJQ6z+Oe/4QEuhX7kf39X2lGajVgRkdoMUSPO461YVUiW8oMZr1tXUwo/T8n\nZMrrF7VM+cEZQVFI73Tp3LjEIngX3JG9jPbzOY0aADZ48sozhgo8YuwPVaPl\noTXEw0Wz+oXh9CbjpCXWVADVq6HbTh+COv4Oio+qF8lYvym/kFYMD49HuVne\nnRewwrfm0HrPuxAz5kA7+/jHq3Xn+nKGzwyzt+/Gkq+tjQES7hQw/FI1uJ8p\nUiMb0g9pFHfr4X7C/J7JRRnIt8MsL37Nk00bwOAL3e2rlsAD9IPrTBORUwei\nx7ER\r\n=U/Ny\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzoT+6xjN1ZuaXeLRPJg8J9dBGuCMvGzijsawUXLMR8QIgb3SO3/6qO4x7FpP5pqMaKOg+a5kMuMN5j+z85Ky8LEQ="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.0-rc.4_1570817003733_0.30763606527616516"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.0", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.25", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "b3d68b11007f6a52613fbc4d5cfd7da3db2e4831", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cx8b+eUvxgv4+vn0V5wC6TLhroaZgtfwvUBLf3XnxOFGb0QFK8LRmxDfTpt001C7OebB57tBeZFHYAPlXUlxSg==", "shasum": "7be34e8d99ee409ea3eed080cb300659a5c56bf1", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.0.tgz", "fileCount": 6, "unpackedSize": 105054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpIycCRA9TVsSAnZWagAAhrEP+gNO9lIVM+VGjT9g7KjT\nPuXTc3Uf4zW+ExDpTCumqPIZ9lkIWzevkPKsgwFgUpw7wz+942YasDxJMTlF\nrceTKxc2O5hftdnFjdE7LH1wBsfmkt/jUoMzD3qUSYTALvfzEuqfWaLdzsfe\nPIYkGWYMkq8mb0vbAO9vW6GxorGxaU52duhBmYFWJz+8MkxtmPm8TxGY2B6u\nf9QyCg4qZyE0XZGh3kxbDfW6gl7+2S7zvdCJB1F7Nii5DC+Ga9fWi4GNecTI\nTIf7Xff0uTNsrPvoAzQ5L2L64XA3NoJaQTxdQ8bjIUB7wf9B80LKvaVKBWQM\nnZzl0fuTUJFQTQsDEVLZTWRok9vqvh1XndN6Lsp25TKORcffPieVHA7qUE2P\nu1WLFWb2SA1Y9NYr7U2CLy0SG+jj+Z6Ke6w74OoNSLGep6BwCpJhGRFtkOv9\nWjnYTJ0Xhpf54VP2CzgEfkyVUaMZhhWcgipRZOwLSSEcEZWQas3o23f/sGMC\nIHXEqGIbNqB/ncAaUm8FbwNno2EBJSFK64xfkFoK440o1IWIElP7B2WC8Sqh\nrWyB+JPHUJtpPAiOBzMlHeOuSexmFhUq92g4ZUsLFfi/EScr/9TB1Dm+X4ed\nnUwB3MpVBkB+B3Wx94xKZ2GG2a+N0R5agln8ww+tHpbcuF2qnhVVonHtNTuU\npWb+\r\n=naoQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzAxcxq0i69Nlp2tAwK+YK56+UzKJ0mFgKTw3IAufDrAiEAwWiVvKAz4pmD6d2WWWeeyuAMwLgkx/m2rcNKLAeniBw="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.0_1571064988024_0.791730015076292"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.1", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.25", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.8", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "7b06b747259b370e22473eda075ecb74744b8bf1", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.1", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-12OwRCWgTS7NeFWY/ewrSxkWgBvTBOUhvPe/Lq4xe7KVQ1dRx2GOW+jX2v01R9onhT9V5y3M5MidFGJjrvzBvg==", "shasum": "4721d7a648da040dee86dd9b91738832cab52f60", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.1.tgz", "fileCount": 6, "unpackedSize": 105161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpIzACRA9TVsSAnZWagAAKscP/icNilM7Ey2IZ/A5wLwL\nSMq1TQBnaaRbbuGmbhg1/DdZfrTan59D80/FZvmCV9Xd+mYIGV+4p+jCjo4w\nxitc2pHlj0k7lNL14qGLHI2dVIhdkzDLb4apTxUARSzFfI7mU7CvY1ogr2b+\nX5ApAW1Mt6g++t+PyY81TMa2sxO4tY5zjssMrjkJh0eOXdD0T+8x76nO3+Qv\nmjFfVJtr0hAfyAWLWDf8f2kSqluOey6Zh8Pj7d7KVYtR+K4HaRr3PtlUFP3K\npzqs93hKFFqei8YY6i8dChlMNV8CKaJEY+TN7RnwceoPSc2oA58qznumu1lV\nQEEIU8YzkEiNg9oZuud/F+sbaREx7NhpphZlDf1MHEoaY1DjGb126qmuDlZg\ns+/bO3KO91MyGTewJqscp3fmOKYSrqOZmMzmVa2pbN4R7Pjglss8fUAypVnI\nsWspg9qhi62pB9jyt0uGUaoUBjAHf0QS9sc4oXQ0fGI2V4uBGwrg62J7KLEt\nzKv4xBuR0fNueeH4ma7qb/jLpZcMSAmNfMtmO/WuLiz34uIMqL8MU4ni+5/e\nSJY09V/2dzxpw3MxdW9KebliuIVsUTZu0b1ycOfqR89kx6py3C4lA9f5nUtp\n9J8j0xwlQQKwwmlp/U0kZ+LITKy7bfzCrNTtyDC9MPAN0Mx85+2/58cZJzc1\n8BGd\r\n=dUHI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIzO6IK5cjyFBMLnzGmxZL8yA1UKqkvZvZRXqcwwrLOwIhAK2q2/BsJqE/MG1DMNY/qVYxZvRw9DolEnb+ikn+uMtS"}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.1_1571065023793_0.6380178673733936"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "4.0.2", "main": "lib/saxes.js", "types": "lib/saxes.d.ts", "license": "ISC", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --delay", "posttest": "eslint test/*.js lib/*.js", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-angular": "^8.2.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.25", "eslint": "^6.5.1", "eslint-config-lddubeau-base": "^4.0.2", "husky": "^3.0.9", "mocha": "^6.2.1", "renovate-config-lddubeau": "^1.0.0", "xml-conformance-suite": "^1.2.0"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "20d96b62b782705cd8adaa009aa23485b107c802", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@4.0.2", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.1", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EZOTeQ4bgkOaGCDaTKux+LaRNcLNbdbvMH7R3/yjEEULPEmqvkFbFub6DJhJTub2iGMT93CfpZ5LTdKZmAbVeQ==", "shasum": "76f8e762efc96ec4af5f885d8151c50426103165", "tarball": "https://registry.npmjs.org/saxes/-/saxes-4.0.2.tgz", "fileCount": 6, "unpackedSize": 105273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpIziCRA9TVsSAnZWagAA1ekP/ipzj2VnS0RWUwMZJFmr\ni7NrN9abh/Z1LDzdsVsws2EtotgtoexJLjvXf4VEGzfWmifG2jXEF/JOhtA8\nTch11J/JabI22h0OU8FjzntOdP6FShl5RfRqPbcZttlNkZJdJwrKGO3Pfzqk\nx3am/3YegMVm/tTFSk71aON2rv+Hyl1fx86f0Tw8Jq2nUjrF76J+CIyEqG++\nNAV3Vn41MR1bh/RbX/yB3QujJBUSo2WmqVK1BsYjlQUur1cNNLP8tx1D3eYz\nasKeRfZmQqnqhEGhVlq0BXh9GvqbajrgtOGBbmyLTCNdn6+3wjIG+WiVQhsK\nphAo2TMsrvx7Ft0X8/jM2IUx0K+zPsqmOHtRRZaus04ajf0yX6M21YZTlhWx\n9hqa4kN9e2Sla06cVuVr3GmRZyKRrj7r2zM/cVE6x029bhypUz39z26Y7LD2\nFsODdN1oRC0CNokgjzqI9XILSkAJBlfgQxSiwMx35QTpLk5VD7kMo6qQnfFn\nkWOye2eh9wNWCxmXvsuu5vK/8LZ97fzcuJKYb1ERFnSnIK09JTui3Z6Qlhjg\nU3Taz3eZykH8aiTFR60yLHS57ZIt+imlB4WqAugoOwpY4HfmCk2fIwuNo5is\n43YQ/CwaKJv+Uon059uUCTC9QfpV+VAmvPqRDZbvsBWZadi73cUBg/WxDU4M\n1fD6\r\n=f3uO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAg1eD9K5ukTYt8V4PsTHf+bZay47lhOkypa1A/8YzKwIhAPFapDtM6Ch4+HUBI1XPIMmRxLzb0XIxx7NvkPSZtFQy"}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_4.0.2_1571065057166_0.4012723335487307"}, "_hasShrinkwrap": false}, "5.0.0-rc.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "5.0.0-rc.1", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=10"}, "scripts": {"tsc": "tsc", "copy": "cp -p package.json README.md build/dist", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "preversion": "npm test", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-angular": "^8.3.4", "@types/chai": "^4.2.9", "@types/mocha": "^7.0.1", "@typescript-eslint/eslint-plugin": "^2.19.2", "@typescript-eslint/eslint-plugin-tslint": "^2.19.2", "@typescript-eslint/parser": "^2.19.2", "@xml-conformance-suite/js": "^2.0.0", "@xml-conformance-suite/mocha": "^2.0.0", "@xml-conformance-suite/test-data": "^2.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.31", "eslint": "^6.8.0", "eslint-config-lddubeau-base": "^5.2.0", "eslint-config-lddubeau-ts": "^1.1.6", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prefer-arrow": "^1.1.7", "eslint-plugin-react": "^7.18.3", "eslint-plugin-simple-import-sort": "^5.0.1", "husky": "^4.2.2", "mocha": "^7.0.1", "renovate-config-lddubeau": "^1.0.0", "ts-node": "^8.6.2", "tsd": "^0.11.0", "tslint": "^5.20.1", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.16.9", "typescript": "^3.7.5"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 10.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors **in DTDs** cannot be\nreported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `doctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them. What follows is a brief\nsummary of what is available. The final authority is the source code.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe move to TypeScript makes it so that everything is now formally private,\nprotected, or public.\n\nIf you use anything not public, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.on(\"error\", function (e) {\n  // an error happened.\n});\nparser.on(\"text\", function (t) {\n  // got some text.  t is the string of text.\n});\nparser.on(\"opentag\", function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n});\nparser.on(\"end\", function () {\n  // parser stream is done, and ready to have more stuff written to it.\n});\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset. Currently, setting this to `false` only\n  results in a cosmetic change: the errors reported do not contain position\n  information. sax-js would literally turn off the position-computing logic if\n  this flag was set to false. The notion was that it would optimize\n  execution. In saxes at least it turns out that continually testing this flag\n  causes a cost that offsets the benefits of turning off this logic.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `columnIndex`, `position` - Indications of the position in the\nXML document where the parser currently is looking. The `columnIndex` property\ncounts columns as if indexing into a JavaScript string, whereas the `column`\nproperty counts Unicode characters.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\nThe options `additionalNamespaces` and `resolvePrefix` are really meant to be\nused for parsing fragments. However, saxes won't prevent you from using them\nwith `fragment: false`. Note that if you do this, your document may parse\nwithout errors and yet be malformed because the document can refer to namespaces\nwhich are not defined *in* the document.\n\nOf course, `additionalNamespaces` and `resolvePrefix` are used only if `xmlns`\nis `true`. If you are parsing a fragment that does not use namespaces, there's\nno point in setting these options.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n* Don't set event handlers you don't need. Saxes has always aimed to avoid doing\n  work that will just be tossed away but future improvements hope to do this\n  more aggressively. One way saxes knows whether or not some data is needed is\n  by checking whether a handler has been set for a specific event.\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "d1aae025747241be940cbad47722b24bfc1d6c61", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@5.0.0-rc.1", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-62fc1HJzfuEnX5A9LJ2Aj6lcV3jfTLTdvruqMYBCWOdRx8NNPo65f4UI4fO5GAhW7V7IvA+uh0ElYtbMVsyxYg==", "shasum": "35c291ce0f51ce5cb4bd9868411f148810321548", "tarball": "https://registry.npmjs.org/saxes/-/saxes-5.0.0-rc.1.tgz", "fileCount": 80, "unpackedSize": 3127460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQ/w9CRA9TVsSAnZWagAAo6IQAJfo7jp+w2k7XQVqPXHb\nfVb7OvohJZx3TmK97+xUgytYEAEh3iBGzCzrt1jzmpi5XoNdPBtF20ZD4W+n\nD47oj7uvMO77BlbM4+md3Qoi0MVm28Di63P2sV/dbB/SVVgKH6YNi1Lc+A08\nhHo0iZ6DhPG2jcIXiRXeUJYkiuOIOezJmMERE3REPeAtltYfTlnEPx4G6GTB\nvV4YMH2AUM0rhpzZ4vWe7POpeHy2yltpc75ZXa8n1DaAeZxVeleJamuHCcSY\nqQuBNoFQo/2gmrPcrz59Ay4nHRj+detNfSHUBNSxcDZeeuQEMv8/aEHTczd7\nOqXGFEjoYmFHH750odutntH/trEIG0mnpJTu2LaBjSWv7MBL0LSXX3WgueIa\neXXkOuDc56MUxPbMyNiVMxM2Hbscp4cMcsoTa4Vpwe35q7c4HwPsEXBTFgP8\ny/uCeBFC4O11pg5+DDbPVJPwucDEjxr6LCpUTgZEXDMUGf4AwlCDRFC9cCi4\nbAL+IMZ0bolzxgJWLRTGVZc0JpUuWxxQiInwzBeGVDsWatn0JvvrjCBcQGHi\ncwx5JDULMNOBDrnNao4VoRjPHawcGbT6XXhzLg23rL8QJLKLxY0j1a0AKADU\nhi+AzxkWeJU3QNq0QhEO2KJcUnw40ZC2mJEfVS5FOIsQqvjljzUIcApK7D66\nEODF\r\n=jiFs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAP1wTJo901GYGSrDGqsUHtzioqEISAUERkV7FytyzxgIgUMpn2hadOjs3Ou9aLkbB2dArm1wH7rgzwlwFQQWhQ5s="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_5.0.0-rc.1_1581513789141_0.6748096907963621"}, "_hasShrinkwrap": false}, "5.0.0-rc.2": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "5.0.0-rc.2", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=10"}, "scripts": {"tsc": "tsc", "copy": "cp -p package.json README.md build/dist", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "self:publish": "cd build/dist && npm publish", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run test && npm run self:publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-angular": "^8.3.4", "@types/chai": "^4.2.9", "@types/mocha": "^7.0.1", "@typescript-eslint/eslint-plugin": "^2.19.2", "@typescript-eslint/eslint-plugin-tslint": "^2.19.2", "@typescript-eslint/parser": "^2.19.2", "@xml-conformance-suite/js": "^2.0.0", "@xml-conformance-suite/mocha": "^2.0.0", "@xml-conformance-suite/test-data": "^2.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.31", "eslint": "^6.8.0", "eslint-config-lddubeau-base": "^5.2.0", "eslint-config-lddubeau-ts": "^1.1.6", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prefer-arrow": "^1.1.7", "eslint-plugin-react": "^7.18.3", "eslint-plugin-simple-import-sort": "^5.0.1", "husky": "^4.2.2", "mocha": "^7.0.1", "renovate-config-lddubeau": "^1.0.0", "ts-node": "^8.6.2", "tsd": "^0.11.0", "tslint": "^5.20.1", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.16.9", "typescript": "^3.7.5"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 10.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors **in DTDs** cannot be\nreported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `doctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them. What follows is a brief\nsummary of what is available. The final authority is the source code.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe move to TypeScript makes it so that everything is now formally private,\nprotected, or public.\n\nIf you use anything not public, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.on(\"error\", function (e) {\n  // an error happened.\n});\nparser.on(\"text\", function (t) {\n  // got some text.  t is the string of text.\n});\nparser.on(\"opentag\", function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n});\nparser.on(\"end\", function () {\n  // parser stream is done, and ready to have more stuff written to it.\n});\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset. Currently, setting this to `false` only\n  results in a cosmetic change: the errors reported do not contain position\n  information. sax-js would literally turn off the position-computing logic if\n  this flag was set to false. The notion was that it would optimize\n  execution. In saxes at least it turns out that continually testing this flag\n  causes a cost that offsets the benefits of turning off this logic.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `columnIndex`, `position` - Indications of the position in the\nXML document where the parser currently is looking. The `columnIndex` property\ncounts columns as if indexing into a JavaScript string, whereas the `column`\nproperty counts Unicode characters.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\nThe options `additionalNamespaces` and `resolvePrefix` are really meant to be\nused for parsing fragments. However, saxes won't prevent you from using them\nwith `fragment: false`. Note that if you do this, your document may parse\nwithout errors and yet be malformed because the document can refer to namespaces\nwhich are not defined *in* the document.\n\nOf course, `additionalNamespaces` and `resolvePrefix` are used only if `xmlns`\nis `true`. If you are parsing a fragment that does not use namespaces, there's\nno point in setting these options.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n* Don't set event handlers you don't need. Saxes has always aimed to avoid doing\n  work that will just be tossed away but future improvements hope to do this\n  more aggressively. One way saxes knows whether or not some data is needed is\n  by checking whether a handler has been set for a specific event.\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@5.0.0-rc.2", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lEHu0OzYdQ168ONXc3Y7sTnTGNub06J7LMzCvI9eN1Sgy3tIS/+rfV//lOqYEDqsNogMqymO821yZn4eWpWyuQ==", "shasum": "2960ac6ae5fd8bf6d616fe7784c73c8d101a448b", "tarball": "https://registry.npmjs.org/saxes/-/saxes-5.0.0-rc.2.tgz", "fileCount": 5, "unpackedSize": 163963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRDkZCRA9TVsSAnZWagAAjkEP+wSAiJnUU2j3BWIFCrqd\nAsOxLxlFz0QM21z9Y+ZIEMiHNCiOr7pEPm/Un7pS/YlafVOUuo6Oad9Ripvr\nMAf3pEHghUUk9UL1I8jHY5FDZxNaSIbSvsf+JFfTL9e47NN+sDcAGW47JfO1\n/H+3USdW2exp7VvDwJuVCEkm87tvNhLxVMEWHInrcoDBen1TOGHzD8YpCUME\ny1KyEz8hzr8LO0JWJ8w0zOlu7RmjenhSgftoYux5R0p4OiYTaKU8+DOkmQ2C\nn3bODGMks9ZJiucJBs+ghXAp6CKFEtwzj7iN7Na9UtIDFQt1kTXkLwzdZl67\n/Niydg+iRq+1BuRsPvoIeun2t1QVqHsW5C0YfGaWdeX8jsBZOb6W83a6bqHG\nPs7uKAncT6HwOO1Sq4dniG2BbOF6FNiV5+EAwKxhdSj2YmqJUENyEVJW4xgQ\njvOR29bvTqfKzOoFtjSQvpN8lcc1m3m+jgYwKZJ6npDrdK28fDrilk1VYGeS\njYSVZ6tqnsTm6tnVLDI0M1fUbx94epuHf0yJXMpQ+WGmCdFib5km5unzRYPG\niDSGTfG9OuC0951Hhk3Ue3FPo5tiV69AUGekgzSkoD4Vb1CMft1WL/COrM7j\n9opBq1OaLsxJ3PPh0LWBOR/puKDYO4QdV7y+NaX6yfrmCq9IsLWNHzqTgi6D\ngqng\r\n=Ju+c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAX0H1ElKjT1WYHMze6nzMg+mXlSKOFJTx2IBf0m7EgNAiEAxbpM5zlwQwJZ3GKKWQcwuVUfRDDyFxJSFwv5xlax8j0="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_5.0.0-rc.2_1581529369534_0.8618295954687742"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "5.0.0", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=10"}, "scripts": {"tsc": "tsc", "copy": "cp -p README.md build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "self:publish": "cd build/dist && npm_config_tag=`simple-dist-tag` npm publish", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run test && npm run self:publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-angular": "^8.3.4", "@types/chai": "^4.2.9", "@types/mocha": "^7.0.1", "@typescript-eslint/eslint-plugin": "^2.21.0", "@typescript-eslint/eslint-plugin-tslint": "^2.21.0", "@typescript-eslint/parser": "^2.21.0", "@xml-conformance-suite/js": "^2.0.0", "@xml-conformance-suite/mocha": "^2.0.0", "@xml-conformance-suite/test-data": "^2.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.31", "eslint": "^6.8.0", "eslint-config-lddubeau-base": "^5.2.0", "eslint-config-lddubeau-ts": "^1.1.7", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prefer-arrow": "^1.1.7", "eslint-plugin-react": "^7.18.3", "eslint-plugin-simple-import-sort": "^5.0.1", "husky": "^4.2.3", "mocha": "^7.1.0", "renovate-config-lddubeau": "^1.0.0", "simple-dist-tag": "^1.0.2", "ts-node": "^8.6.2", "tsd": "^0.11.0", "tslint": "^5.20.1", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.16.11", "typescript": "^3.8.2"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@5.0.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.7", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LXTZygxhf8lfwKaTP/8N9CsVdjTlea3teze4lL6u37ivbgGbV0GGMuNtS/I9rnD/HC2/txUM7Df4S2LVl1qhiA==", "shasum": "b7d30284d7583a5ca6ad0248b56d8889da53788b", "tarball": "https://registry.npmjs.org/saxes/-/saxes-5.0.0.tgz", "fileCount": 5, "unpackedSize": 164012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWXciCRA9TVsSAnZWagAAWJgP/A37PfYkh7A59lRgAikX\nN0r5ZXL8WUhzK/o1GyLX3/Q4dC7uh6nkAQQr6n9ul5M7kOL0u0+032HsZBaS\nrA+pH/SyklWsyGkV/ICWHs9VnN+fGADzyBG75aggSUG8w+ZoVdjLrokiqQRW\nrY4qmxpc/Q8bf9dcKwVhcN9B0PxAzqun7kujOg4muh5QJUT+u22mgaTWiY3v\nft/7pLR++6vLUOp5eKEiGauR3Wv3LZ7AKxG44RaiyZJO3pJEB5bZU+j4Br+n\nxl8yiK7osp2H7GCldejH5Dd4W2A5M0JWKuWu2gXbJTj8IGRYO2U9PCBR7BFm\nDQVoZR0r6kGvI6gbZ41C7zu0VaWri4P1Z+Htx5oAv9JN07wjTeTe1lyE2Mhp\nuFE79+8+xdcITSGx3+UcTft2j5n1iVuIs3kdDQMMQBZQG/TxWdThK0jIyl+W\nTaD+UG2y2sisxTTP4FtMsvuubQLBmBDNrSg5gWvSrBSlMkx2NY/l4Iq1qxBr\nuIpNlCnuw1eu64REUUBK7UHrvdNJfuZPKiuZH6r963LqsNXEnWKZgmFMzWsH\nYTZ3X2TZ/K+VGhl5urhcUfxAK+GOsNXTwbH5Xgmpo9xN4JrJA07cp3oUCo0Z\nwGpcBneYdf3Q+VDERUYgjIpUsRjMfI9JRE0h6VRMOAio0O6g/olAArr7/XTJ\nb5O+\r\n=BJyr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICINruHQ3tbsz+7wrCXUEZw3PF8DB+uG9DsXbEcp7aDKAiEAqQRsWV1WhWtUs7scD4xmWzPDN8gMdTNin/H4jo1paZk="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_5.0.0_1582921505763_0.9110900629748271"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "5.0.1", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=10"}, "scripts": {"tsc": "tsc", "copy": "cp -p README.md build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "self:publish": "cd build/dist && npm_config_tag=`simple-dist-tag` npm publish", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run test && npm run self:publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-angular": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@typescript-eslint/eslint-plugin": "^2.27.0", "@typescript-eslint/eslint-plugin-tslint": "^2.27.0", "@typescript-eslint/parser": "^2.27.0", "@xml-conformance-suite/js": "^2.0.0", "@xml-conformance-suite/mocha": "^2.0.0", "@xml-conformance-suite/test-data": "^2.0.0", "chai": "^4.2.0", "conventional-changelog-cli": "^2.0.31", "eslint": "^6.8.0", "eslint-config-lddubeau-base": "^5.2.0", "eslint-config-lddubeau-ts": "^1.1.7", "eslint-import-resolver-typescript": "^2.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prefer-arrow": "^1.2.0", "eslint-plugin-react": "^7.19.0", "eslint-plugin-simple-import-sort": "^5.0.2", "husky": "^4.2.5", "mocha": "^7.1.1", "renovate-config-lddubeau": "^1.0.0", "simple-dist-tag": "^1.0.2", "ts-node": "^8.8.2", "tsd": "^0.11.0", "tslint": "^6.1.1", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.17.4", "typescript": "^3.8.3"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@5.0.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==", "shasum": "eebab953fa3b7608dbe94e5dadb15c888fa6696d", "tarball": "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz", "fileCount": 5, "unpackedSize": 164263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekO1UCRA9TVsSAnZWagAA3hEP/R7LQSUKk/C7hfIFhPax\nC2PX2Wm2H7Zfn3fePZsza6VsgSNb8u7pQlk0tXjZp+Yz3caPhZLJOnePkslt\nXHa5ZezLwtxD0F74s5HRc38lhldWt3wCmgGw+YhiKKBcuuWKnbS2+toL3XlQ\nrlS/P3Kr1qLJHyOAFTXYDMu/mN/iaAveOqkNqU5sDEiIudetoPO6VulidE7C\nMNMNaBGMSz0ZfdIqGgycal5SQ6K1QkCrIqxOO4tGTA0jkvDPqpL5RNuUOYM2\nyqkYH+dgKXzwgqmGWSO1TzDs7a12nnjXQxdgiH5r1h4tWCcA+sgCeL/ZbSxR\nFRl2CDysz8mxS+WgVfQVeKrbSpuDBUrWMPO1qX81Pa8leh7MDnfXabxzVkWt\n40bNDjnTuI4g0wrDN/x6XdBp5AD8XnQp7fVcljwhYOsjizWcxFUWgh63tzvU\nZpJoKnnJtskivqw5tro7x/PucntV2hLqZREdV5v8ZAAUlMbWnhHCnU6iBjhn\n0742JDSvp4k6uRaqYVyQ0wB102U3cgYYnAC/VZ8m2rxzmZ0+Sf46PbLjIHun\npH0quKDkISEAkVoKzJ8VSAp01L0EfCgkgXNTf1HFZxJtivBeSVdcU5yfgWek\n213yzeMHXtaG1Xzr6jEPNoLdqjAqE0wtQOpR0e4i2gO15i5AGDdgtfgOPkaa\nD1zd\r\n=yaHO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAl1P7fx9KT3ApvWWnCp3ZkD2bwG7zMZLCwnCB591Tb9AiAWh5qmBtUKjuXYYF7P06fMCi5GT+J8Gp68pe9TF1Ulqw=="}]}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_5.0.1_1586556244178_0.4979480270971004"}, "_hasShrinkwrap": false}, "6.0.0-rc.1": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "6.0.0-rc.1", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=v12.22.7"}, "scripts": {"tsc": "tsc", "copy": "cp -p README.md build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "self:publish": "cd build/dist && npm_config_tag=`simple-dist-tag` npm publish", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run test && npm run self:publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^14.1.0", "@commitlint/config-angular": "^14.1.0", "@types/chai": "^4.2.22", "@types/mocha": "^9.0.0", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/eslint-plugin-tslint": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "@xml-conformance-suite/js": "^3.0.0", "@xml-conformance-suite/mocha": "^3.0.0", "@xml-conformance-suite/test-data": "^3.0.0", "chai": "^4.3.4", "conventional-changelog-cli": "^2.1.1", "eslint": "^8.2.0", "eslint-config-lddubeau-base": "^6.1.0", "eslint-config-lddubeau-ts": "^2.0.2", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-react": "^7.26.1", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": "^7.0.4", "mocha": "^9.1.3", "renovate-config-lddubeau": "^1.0.0", "simple-dist-tag": "^1.0.2", "ts-node": "^10.4.0", "tsd": "^0.18.0", "tslint": "^6.1.3", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.22.8", "typescript": "^4.4.4"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 10.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors **in DTDs** cannot be\nreported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `doctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them. What follows is a brief\nsummary of what is available. The final authority is the source code.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe move to TypeScript makes it so that everything is now formally private,\nprotected, or public.\n\nIf you use anything not public, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.on(\"error\", function (e) {\n  // an error happened.\n});\nparser.on(\"text\", function (t) {\n  // got some text.  t is the string of text.\n});\nparser.on(\"opentag\", function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n});\nparser.on(\"end\", function () {\n  // parser stream is done, and ready to have more stuff written to it.\n});\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset. Currently, setting this to `false` only\n  results in a cosmetic change: the errors reported do not contain position\n  information. sax-js would literally turn off the position-computing logic if\n  this flag was set to false. The notion was that it would optimize\n  execution. In saxes at least it turns out that continually testing this flag\n  causes a cost that offsets the benefits of turning off this logic.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `columnIndex`, `position` - Indications of the position in the\nXML document where the parser currently is looking. The `columnIndex` property\ncounts columns as if indexing into a JavaScript string, whereas the `column`\nproperty counts Unicode characters.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\nThe options `additionalNamespaces` and `resolvePrefix` are really meant to be\nused for parsing fragments. However, saxes won't prevent you from using them\nwith `fragment: false`. Note that if you do this, your document may parse\nwithout errors and yet be malformed because the document can refer to namespaces\nwhich are not defined *in* the document.\n\nOf course, `additionalNamespaces` and `resolvePrefix` are used only if `xmlns`\nis `true`. If you are parsing a fragment that does not use namespaces, there's\nno point in setting these options.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n* Don't set event handlers you don't need. Saxes has always aimed to avoid doing\n  work that will just be tossed away but future improvements hope to do this\n  more aggressively. One way saxes knows whether or not some data is needed is\n  by checking whether a handler has been set for a specific event.\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md", "gitHead": "b8da9429778ccd97e31b8052f3857352e0710302", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@6.0.0-rc.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-aTHj3ZHNlUhBDMUqGMHyMrqoOGNy9kUdNcMmIIZK+pkbL7kGKMSIEJbJEpq6nOeOAm0iq2UNQuYbFZoWMPSb6g==", "shasum": "9dcb86897315f6ee06ec1cdd9d9e808e3af8ca53", "tarball": "https://registry.npmjs.org/saxes/-/saxes-6.0.0-rc.1.tgz", "fileCount": 5, "unpackedSize": 163779, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHvZuxdeyXcoLLCajondr86dpP1rcQVZonjbRdeugBNeAiA1k2ZJRlwSgDrEk/lRyDQ5jrGH2KEvqpBdjI7YWtaE9g=="}]}, "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_6.0.0-rc.1_1636299098353_0.8414971291482427"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "saxes", "description": "An evented streaming XML parser in JavaScript", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "version": "6.0.0", "main": "saxes.js", "types": "saxes.d.ts", "license": "ISC", "engines": {"node": ">=v12.22.7"}, "scripts": {"tsc": "tsc", "copy": "cp -p README.md build/dist && sed -e'/\"private\": true/d' package.json > build/dist/package.json", "build": "npm run tsc && npm run copy", "test": "npm run build && mocha --delay", "lint": "eslint --ignore-path .gitignore '**/*.ts' '**/*.js'", "lint-fix": "npm run lint -- --fix", "posttest": "npm run lint", "typedoc": "typedoc --tsconfig tsconfig.json --name saxes --out build/docs/ --listInvalidSymbolLinks --excludePrivate --excludeNotExported", "build-docs": "npm run typedoc", "gh-pages": "npm run build-docs && mkdir -p build && (cd build; rm -rf gh-pages; git clone .. --branch gh-pages gh-pages) && mkdir -p build/gh-pages/latest && find build/gh-pages/latest -type f -delete && cp -rp build/docs/* build/gh-pages/latest && find build/gh-pages -type d -empty -delete", "self:publish": "cd build/dist && npm_config_tag=`simple-dist-tag` npm publish", "version": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "postversion": "npm run test && npm run self:publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "devDependencies": {"@commitlint/cli": "^14.1.0", "@commitlint/config-angular": "^14.1.0", "@types/chai": "^4.2.22", "@types/mocha": "^9.0.0", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/eslint-plugin-tslint": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "@xml-conformance-suite/js": "^3.0.0", "@xml-conformance-suite/mocha": "^3.0.0", "@xml-conformance-suite/test-data": "^3.0.0", "chai": "^4.3.4", "conventional-changelog-cli": "^2.1.1", "eslint": "^8.2.0", "eslint-config-lddubeau-base": "^6.1.0", "eslint-config-lddubeau-ts": "^2.0.2", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-react": "^7.26.1", "eslint-plugin-simple-import-sort": "^7.0.0", "husky": "^7.0.4", "mocha": "^9.1.3", "renovate-config-lddubeau": "^1.0.0", "simple-dist-tag": "^1.0.2", "ts-node": "^10.4.0", "tsd": "^0.18.0", "tslint": "^6.1.3", "tslint-microsoft-contrib": "^6.2.0", "typedoc": "^0.22.8", "typescript": "^4.4.4"}, "dependencies": {"xmlchars": "^2.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "gitHead": "211fa0ebec9b628affc09219199639887174bfc3", "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "homepage": "https://github.com/lddubeau/saxes#readme", "_id": "saxes@6.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==", "shasum": "fe5b4a4768df4f14a201b1ba6a65c1f3d9988cc5", "tarball": "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz", "fileCount": 5, "unpackedSize": 163774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh26Y0CRA9TVsSAnZWagAAohkP/RUOoFgRjj2DmMQCBCef\nw7iqt1qVkFSl1G1GZ8IKfreJKmQiHdtRnwxJtv03Bm//KVqkuwO+JHiojMJO\nPGDlHzCx/NJUo27Y+BL3hRUQtdCN1PMquQIlZP6bLkM8OPwLcrRj7xIyQ6Xp\nOQM4oeoXiwAKWmYPSelmFeXLO2gBBMEVZvfSjWSW8NuGgtBHN0XOFy/fExov\ngv9GD611RPZsZKyn05WLwNMyeB6jIssX1NcTEwWA1RlpVbBdkiEm6kypVwMx\nm5U5suKkUyKZ9kPtJxcNUhytzyzrUb74fec0QXozkIbvC4yrcf0oxSD/LXZU\n8bnD+qSmaI3+S+wEJiQHfO/MHjgPBSC+QCwR3Zn0cDmqlvfz5ShQ2Qs3CIqE\nNOwy7JXhIfzMMdlM8o0Jmv64OeBLus5ELM91Tt2T4ejm0y85UTGjkrXGhclY\nwP3O2vCzFzbEHR7CDB+SN1KcHt3Z7kK3TvFwH2F9ry3/QfkEzXitnvfOPHvR\n93BGG+8mhICYsrv2BHN+6UPrMAQDNJuazAECG5I0fzn7qDESnhUDSfyuCFBN\nHT2XveaSxBMSOy83Jzb5GS6mf5RtuSgaX0FaAbrfH+e+koN7MoC88OEoDv1X\nZgu2dVjwWdb2PD4bO9e+fnLql4h9Qt/PitVb0ISjwN5CYfqiMTskf214BsTe\nlKJn\r\n=cdou\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEh9ie7kr9Lfo7fxKJxjPMxIn7WiBNPtBXMAX/DkUCnNAiAKaHKUSl26HoAV+VRW88hxl1Ikhfy8wL8YfzgrEbHu2g=="}]}, "_npmUser": {"name": "lddubeau", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/saxes_6.0.0_1636311991048_0.8608806273766367"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-07-23T15:19:39.440Z", "2.0.0": "2018-07-23T15:19:39.544Z", "modified": "2022-05-17T04:15:01.302Z", "2.1.0": "2018-08-20T12:58:47.962Z", "2.2.0": "2018-08-20T13:43:55.427Z", "2.2.1": "2018-08-20T18:16:22.083Z", "3.0.0": "2018-08-21T16:17:57.466Z", "3.1.0": "2018-08-28T18:26:54.173Z", "3.1.1": "2018-08-29T13:56:13.015Z", "3.1.2": "2018-08-31T18:50:11.292Z", "3.1.3": "2018-10-01T11:23:31.951Z", "3.1.4": "2018-12-03T13:30:57.301Z", "3.1.5": "2019-01-08T12:22:14.954Z", "3.1.6": "2019-01-17T00:02:53.090Z", "3.1.7": "2019-02-22T23:43:54.342Z", "3.1.8": "2019-02-25T12:08:40.004Z", "3.1.9": "2019-02-25T12:11:29.650Z", "3.1.10": "2019-06-11T22:32:49.714Z", "3.1.11": "2019-06-25T22:37:08.359Z", "4.0.0-rc.1": "2019-10-02T15:42:56.021Z", "4.0.0-rc.2": "2019-10-04T13:00:10.955Z", "4.0.0-rc.3": "2019-10-11T18:01:17.661Z", "4.0.0-rc.4": "2019-10-11T18:03:23.910Z", "4.0.0": "2019-10-14T14:56:28.157Z", "4.0.1": "2019-10-14T14:57:03.995Z", "4.0.2": "2019-10-14T14:57:37.444Z", "5.0.0-rc.1": "2020-02-12T13:23:09.312Z", "5.0.0-rc.2": "2020-02-12T17:42:49.692Z", "5.0.0": "2020-02-28T20:25:05.881Z", "5.0.1": "2020-04-10T22:04:04.301Z", "6.0.0-rc.1": "2021-11-07T15:31:38.538Z", "6.0.0": "2021-11-07T19:06:31.264Z"}, "maintainers": [{"name": "lddubeau", "email": "<EMAIL>"}], "description": "An evented streaming XML parser in JavaScript", "homepage": "https://github.com/lddubeau/saxes#readme", "repository": {"type": "git", "url": "git+https://github.com/lddubeau/saxes.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lddubeau/saxes/issues"}, "license": "ISC", "readme": "# saxes\n\nA sax-style non-validating parser for XML.\n\nSaxes is a fork of [sax](https://github.com/isaacs/sax-js) 1.2.4. All mentions\nof sax in this project's documentation are references to sax 1.2.4.\n\nDesigned with [node](http://nodejs.org/) in mind, but should work fine in the\nbrowser or other CommonJS implementations.\n\nSaxes does not support Node versions older than 10.\n\n## Notable Differences from Sax.\n\n* Saxes aims to be much stricter than sax with regards to XML\n  well-formedness. Sax, even in its so-called \"strict mode\", is not strict. It\n  silently accepts structures that are not well-formed XML. Projects that need\n  better compliance with well-formedness constraints cannot use sax as-is.\n\n  Consequently, saxes does not support HTML, or pseudo-XML, or bad XML. Saxes\n  will report well-formedness errors in all these cases but it won't try to\n  extract data from malformed documents like sax does.\n\n* Saxes is much much faster than sax, mostly because of a substantial redesign\n  of the internal parsing logic. The speed improvement is not merely due to\n  removing features that were supported by sax. That helped a bit, but saxes\n  adds some expensive checks in its aim for conformance with the XML\n  specification. Redesigning the parsing logic is what accounts for most of the\n  performance improvement.\n\n* Saxes does not aim to support antiquated platforms. We will not pollute the\n  source or the default build with support for antiquated platforms. If you want\n  support for IE 11, you are welcome to produce a PR that adds a *new build*\n  transpiled to ES5.\n\n* Saxes handles errors differently from sax: it provides a default onerror\n  handler which throws. You can replace it with your own handler if you want. If\n  your handler does nothing, there is no `resume` method to call.\n\n* There's no `Stream` API. A revamped API may be introduced later. (It is still\n  a \"streaming parser\" in the general sense that you write a character stream to\n  it.)\n\n* Saxes does not have facilities for limiting the size the data chunks passed to\n  event handlers. See the FAQ entry for more details.\n\n## Conformance\n\nSaxes supports:\n\n* [XML 1.0 fifth edition](https://www.w3.org/TR/2008/REC-xml-********/)\n* [XML 1.1 second edition](https://www.w3.org/TR/2006/REC-xml11-20060816/)\n* [Namespaces in XML 1.0 (Third Edition)](https://www.w3.org/TR/2009/REC-xml-names-20091208/).\n* [Namespaces in XML 1.1 (Second Edition)](https://www.w3.org/TR/2006/REC-xml-names11-20060816/).\n\n## Limitations\n\nThis is a non-validating parser so it only verifies whether the document is\nwell-formed. We do aim to raise errors for all malformed constructs\nencountered. However, this parser does not thorougly parse the contents of\nDTDs. So most malformedness errors caused by errors **in DTDs** cannot be\nreported.\n\n## Regarding `<!DOCTYPE` and `<!ENTITY`\n\nThe parser will handle the basic XML entities in text nodes and attribute\nvalues: `&amp; &lt; &gt; &apos; &quot;`. It's possible to define additional\nentities in XML by putting them in the DTD. This parser doesn't do anything with\nthat. If you want to listen to the `doctype` event, and then fetch the\ndoctypes, and read the entities and add them to `parser.ENTITIES`, then be my\nguest.\n\n## Documentation\n\nThe source code contains JSDOC comments. Use them. What follows is a brief\nsummary of what is available. The final authority is the source code.\n\n**PAY CLOSE ATTENTION TO WHAT IS PUBLIC AND WHAT IS PRIVATE.**\n\nThe move to TypeScript makes it so that everything is now formally private,\nprotected, or public.\n\nIf you use anything not public, that's at your own peril.\n\nIf there's a mistake in the documentation, raise an issue. If you just assume,\nyou may assume incorrectly.\n\n## Summary Usage Information\n\n### Example\n\n```javascript\nvar saxes = require(\"./lib/saxes\"),\n  parser = new saxes.SaxesParser();\n\nparser.on(\"error\", function (e) {\n  // an error happened.\n});\nparser.on(\"text\", function (t) {\n  // got some text.  t is the string of text.\n});\nparser.on(\"opentag\", function (node) {\n  // opened a tag.  node has \"name\" and \"attributes\"\n});\nparser.on(\"end\", function () {\n  // parser stream is done, and ready to have more stuff written to it.\n});\n\nparser.write('<xml>Hello, <who name=\"world\">world</who>!</xml>').close();\n```\n\n### Constructor Arguments\n\nSettings supported:\n\n* `xmlns` - Boolean. If `true`, then namespaces are supported. Default\n  is `false`.\n\n* `position` - Boolean. If `false`, then don't track line/col/position. Unset is\n  treated as `true`. Default is unset. Currently, setting this to `false` only\n  results in a cosmetic change: the errors reported do not contain position\n  information. sax-js would literally turn off the position-computing logic if\n  this flag was set to false. The notion was that it would optimize\n  execution. In saxes at least it turns out that continually testing this flag\n  causes a cost that offsets the benefits of turning off this logic.\n\n* `fileName` - String. Set a file name for error reporting. This is useful only\n  when tracking positions. You may leave it unset.\n\n* `fragment` - Boolean. If `true`, parse the XML as an XML fragment. Default is\n  `false`.\n\n* `additionalNamespaces` - A plain object whose key, value pairs define\n   namespaces known before parsing the XML file. It is not legal to pass\n   bindings for the namespaces `\"xml\"` or `\"xmlns\"`.\n\n* `defaultXMLVersion` - The default version of the XML specification to use if\n  the document contains no XML declaration. If the document does contain an XML\n  declaration, then this setting is ignored. Must be `\"1.0\"` or `\"1.1\"`. The\n  default is `\"1.0\"`.\n\n* `forceXMLVersion` - Boolean. A flag indicating whether to force the XML\n  version used for parsing to the value of ``defaultXMLVersion``. When this flag\n  is ``true``, ``defaultXMLVersion`` must be specified. If unspecified, the\n  default value of this flag is ``false``.\n\n  Example: suppose you are parsing a document that has an XML declaration\n  specifying XML version 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` without setting\n  ``forceXMLVersion`` then the XML declaration will override the value of\n  ``defaultXMLVersion`` and the document will be parsed according to XML 1.1.\n\n  If you set ``defaultXMLVersion`` to ``\"1.0\"`` and set ``forceXMLVersion`` to\n  ``true``, then the XML declaration will be ignored and the document will be\n  parsed according to XML 1.0.\n\n### Methods\n\n`write` - Write bytes onto the stream. You don't have to pass the whole document\nin one `write` call. You can read your source chunk by chunk and call `write`\nwith each chunk.\n\n`close` - Close the stream. Once closed, no more data may be written until it is\ndone processing the buffer, which is signaled by the `end` event.\n\n### Properties\n\nThe parser has the following properties:\n\n`line`, `column`, `columnIndex`, `position` - Indications of the position in the\nXML document where the parser currently is looking. The `columnIndex` property\ncounts columns as if indexing into a JavaScript string, whereas the `column`\nproperty counts Unicode characters.\n\n`closed` - Boolean indicating whether or not the parser can be written to.  If\nit's `true`, then wait for the `ready` event to write again.\n\n`opt` - Any options passed into the constructor.\n\n`xmlDecl` - The XML declaration for this document. It contains the fields\n`version`, `encoding` and `standalone`. They are all `undefined` before\nencountering the XML declaration. If they are undefined after the XML\ndeclaration, the corresponding value was not set by the declaration. There is no\nevent associated with the XML declaration. In a well-formed document, the XML\ndeclaration may be preceded only by an optional BOM. So by the time any event\ngenerated by the parser happens, the declaration has been processed if present\nat all. Otherwise, you have a malformed document, and as stated above, you\ncannot rely on the parser data!\n\n### Error Handling\n\nThe parser continues to parse even upon encountering errors, and does its best\nto continue reporting errors. You should heed all errors reported. After an\nerror, however, saxes may interpret your document incorrectly. For instance\n``<foo a=bc=\"d\"/>`` is invalid XML. Did you mean to have ``<foo a=\"bc=d\"/>`` or\n``<foo a=\"b\" c=\"d\"/>`` or some other variation?  For the sake of continuing to\nprovide errors, saxes will continue parsing the document, but the structure it\nreports may be incorrect. It is only after the errors are fixed in the document\nthat saxes can provide a reliable interpretation of the document.\n\nThat leaves you with two rules of thumb when using saxes:\n\n* Pay attention to the errors that saxes report. The default `onerror` handler\n  throws, so by default, you cannot miss errors.\n\n* **ONCE AN ERROR HAS BEEN ENCOUNTERED, STOP RELYING ON THE EVENT HANDLERS OTHER\n  THAN `onerror`.** As explained above, when saxes runs into a well-formedness\n  problem, it makes a guess in order to continue reporting more errors. The guess\n  may be wrong.\n\n### Events\n\nTo listen to an event, override `on<eventname>`. The list of supported events\nare also in the exported `EVENTS` array.\n\nSee the JSDOC comments in the source code for a description of each supported\nevent.\n\n### Parsing XML Fragments\n\nThe XML specification does not define any method by which to parse XML\nfragments. However, there are usage scenarios in which it is desirable to parse\nfragments. In order to allow this, saxes provides three initialization options.\n\nIf you pass the option `fragment: true` to the parser constructor, the parser\nwill expect an XML fragment. It essentially starts with a parsing state\nequivalent to the one it would be in if `parser.write(\"<foo\">)` had been called\nright after initialization. In other words, it expects content which is\nacceptable inside an element. This also turns off well-formedness checks that\nare inappropriate when parsing a fragment.\n\nThe option `additionalNamespaces` allows you to define additional prefix-to-URI\nbindings known before parsing starts. You would use this over `resolvePrefix` if\nyou have at the ready a series of namespaces bindings to use.\n\nThe option `resolvePrefix` allows you to pass a function which saxes will use if\nit is unable to resolve a namespace prefix by itself. You would use this over\n`additionalNamespaces` in a context where getting a complete list of defined\nnamespaces is onerous.\n\nNote that you can use `additionalNamespaces` and `resolvePrefix` together if you\nwant. `additionalNamespaces` applies before `resolvePrefix`.\n\nThe options `additionalNamespaces` and `resolvePrefix` are really meant to be\nused for parsing fragments. However, saxes won't prevent you from using them\nwith `fragment: false`. Note that if you do this, your document may parse\nwithout errors and yet be malformed because the document can refer to namespaces\nwhich are not defined *in* the document.\n\nOf course, `additionalNamespaces` and `resolvePrefix` are used only if `xmlns`\nis `true`. If you are parsing a fragment that does not use namespaces, there's\nno point in setting these options.\n\n### Performance Tips\n\n* saxes works faster on files that use newlines (``\\u000A``) as end of line\n  markers than files that use other end of line markers (like ``\\r`` or\n  ``\\r\\n``). The XML specification requires that conformant applications behave\n  as if all characters that are to be treated as end of line characters are\n  converted to ``\\u000A`` prior to parsing. The optimal code path for saxes is a\n  file in which all end of line characters are already ``\\u000A``.\n\n* Don't split Unicode strings you feed to saxes across surrogates. When you\n  naively split a string in JavaScript, you run the risk of splitting a Unicode\n  character into two surrogates. e.g.  In the following example ``a`` and ``b``\n  each contain half of a single Unicode character: ``const a = \"\\u{1F4A9}\"[0];\n  const b = \"\\u{1F4A9}\"[1]`` If you feed such split surrogates to versions of\n  saxes prior to 4, you'd get errors. Saxes version 4 and over are able to\n  detect when a chunk of data ends with a surrogate and carry over the surrogate\n  to the next chunk. However this operation entails slicing and concatenating\n  strings. If you can feed your data in a way that does not split surrogates,\n  you should do it. (Obviously, feeding all the data at once with a single write\n  is fastest.)\n\n* Don't set event handlers you don't need. Saxes has always aimed to avoid doing\n  work that will just be tossed away but future improvements hope to do this\n  more aggressively. One way saxes knows whether or not some data is needed is\n  by checking whether a handler has been set for a specific event.\n\n## FAQ\n\nQ. Why has saxes dropped support for limiting the size of data chunks passed to\nevent handlers?\n\nA. With sax you could set ``MAX_BUFFER_LENGTH`` to cause the parser to limit the\nsize of data chunks passed to event handlers. So if you ran into a span of text\nabove the limit, multiple ``text`` events with smaller data chunks were fired\ninstead of a single event with a large chunk.\n\nHowever, that functionality had some problematic characteristics. It had an\narbitrary default value. It was library-wide so all parsers created from a\nsingle instance of the ``sax`` library shared it. This could potentially cause\nconflicts among libraries running in the same VM but using sax for different\npurposes.\n\nThese issues could have been easily fixed, but there were larger issues. The\nbuffer limit arbitrarily applied to some events but not others. It would split\n``text``, ``cdata`` and ``script`` events. However, if a ``comment``,\n``doctype``, ``attribute`` or ``processing instruction`` were more than the\nlimit, the parser would generate an error and you were left picking up the\npieces.\n\nIt was not intuitive to use. You'd think setting the limit to 1K would prevent\nchunks bigger than 1K to be passed to event handlers. But that was not the\ncase. A comment in the source code told you that you might go over the limit if\nyou passed large chunks to ``write``. So if you want a 1K limit, don't pass 64K\nchunks to ``write``. Fair enough. You know what limit you want so you can\ncontrol the size of the data you pass to ``write``. So you limit the chunks to\n``write`` to 1K at a time. Even if you do this, your event handlers may get data\nchunks that are 2K in size. Suppose on the previous ``write`` the parser has\njust finished processing an open tag, so it is ready for text. Your ``write``\npasses 1K of text. You are not above the limit yet, so no event is generated\nyet. The next ``write`` passes another 1K of text. It so happens that sax checks\nbuffer limits only once per ``write``, after the chunk of data has been\nprocessed. Now you've hit the limit and you get a ``text`` event with 2K of\ndata. So even if you limit your ``write`` calls to the buffer limit you've set,\nyou may still get events with chunks at twice the buffer size limit you've\nspecified.\n\nWe may consider reinstating an equivalent functionality, provided that it\naddresses the issues above and does not cause a huge performance drop for\nuse-case scenarios that don't need it.\n", "readmeFilename": "README.md"}