{"_id": "ms", "_rev": "412-9b46914012a40a31781084dcee9cd49c", "name": "ms", "dist-tags": {"latest": "2.1.3", "beta": "3.0.0-beta.2", "canary": "3.0.0-canary.1"}, "versions": {"0.1.0": {"name": "ms", "version": "0.1.0", "_id": "ms@0.1.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "f21fac490daf1d7667fd180fe9077389cc9442b2", "tarball": "https://registry.npmjs.org/ms/-/ms-0.1.0.tgz", "integrity": "sha512-7uwYj3Xip4rOFpe5dDy+C25Ad0nAXkT4yAVMSpuh1UYR2Z7tAswSh4wb/HghRa533wofFUsvg54OQ90Mu1dCJg==", "signatures": [{"sig": "MEQCICUgMnbu4P+yWGs9zpkWDoZJsS3hgrwmM1OE23QR0d98AiAaQaF6n0DTpxKiluPo/kDhJPZM3eOe4ydmWBn7qdFQvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./ms", "engines": {"node": "*"}, "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "Tiny ms conversion utility", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}, "_engineSupported": true}, "0.2.0": {"name": "ms", "version": "0.2.0", "_id": "ms@0.2.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "6edfc5a063471f7bfd35a5831831c24275ce9dc5", "tarball": "https://registry.npmjs.org/ms/-/ms-0.2.0.tgz", "integrity": "sha512-3hmNMG0TYmTiQD6+s+b9eKLYWYTbR+6AgZtOu60jiedzeu2JK9NS6Ih1vosLwxLutvG45slW7/fVaCM8WDXGRQ==", "signatures": [{"sig": "MEYCIQCbW6HLL8Ey4ocELaj55R0v4h1Y0IEM/ruv1ZCA3ysjtAIhAPKjWgoNAewkRA4Dyt06S4tl+ltcrCQYE7f5QtUYV3Cq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./ms", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.3.0": {"name": "ms", "version": "0.3.0", "_id": "ms@0.3.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "03edc348d613e66a56486cfdac53bcbe899cbd61", "tarball": "https://registry.npmjs.org/ms/-/ms-0.3.0.tgz", "integrity": "sha512-25BVmSAdN4KRX7XeI6/gwQ9ewx6t9QB9/8X2fVJUUDpPc03qTRaEPgt5bTMZQ5T2l+XT+haSfqIkysOupDsSVQ==", "signatures": [{"sig": "MEUCICl0UM913l31pPCLrluPWicYNhjKL8fq2sDRaP01B9BNAiEA/fe88H1OpqYmtUUjhZ5KNRWpZrRBDG/uRPsFcflAyE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./ms", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.4.0": {"name": "ms", "version": "0.4.0", "_id": "ms@0.4.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "77ade5470b099bb2d83e232c25763c18cd6963f1", "tarball": "https://registry.npmjs.org/ms/-/ms-0.4.0.tgz", "integrity": "sha512-64oIDtd4AvWd9+PXu3mS+e+83nD/4+vDjORXYUrMsUodlxSgxHt6okjkFO94XAG+zDoBz7GPkCYFXd5OD++kJg==", "signatures": [{"sig": "MEYCIQCz14OGpHKaDD24wOoieXnwbeSlCqRrEsfmph2o3+HmDwIhAP9I/6kfqCCePbEME8I/sip0IU032f0UZ9dQ/GfuDw0o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.5.0": {"name": "ms", "version": "0.5.0", "_id": "ms@0.5.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "8e52e7e1bf521f9cea30f726de958822eab0ee27", "tarball": "https://registry.npmjs.org/ms/-/ms-0.5.0.tgz", "integrity": "sha512-l+4vT0spctuJn4dEuiTHFJg/o2Gu7lcPPVmoEkOvCJ7q6btdsvokZscv1rAj5rokCmiqZRWpA/apQSpgDv8ZSw==", "signatures": [{"sig": "MEQCIBMCGPbnJt2mm9aZLXrc2nvN1SoqdaEr5WnXpTwQdV7vAiBM8iGSLAHXldFSeSQTgDK3sxFpMymGVpYCxfKWqgu0hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.5.1": {"name": "ms", "version": "0.5.1", "_id": "ms@0.5.1", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "98058c8f9c64854d1703ab92bf3f1dcc8e713b4c", "tarball": "https://registry.npmjs.org/ms/-/ms-0.5.1.tgz", "integrity": "sha512-DgU7MSi4T3XY43mZL/Lgk31wqwe2NB56QsyVMcY3m5rICuAp+/uY1/w3lnjhPSaTYVdx1vZQ+ppUlH4AlJ6UAA==", "signatures": [{"sig": "MEUCIFZflkCSwiIpOjLgUAFLDgJwYRXjT4qLaAnT6zAkgOVwAiEAkCg32KSxT/4+atWti00h8UTyaQl6K2SZ3WU/7pstohM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "_npmVersion": "1.2.10", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.6.0": {"name": "ms", "version": "0.6.0", "_id": "ms@0.6.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "21dc16a7d1dc2d8ed244dc0e6a71a5c2612b623b", "tarball": "https://registry.npmjs.org/ms/-/ms-0.6.0.tgz", "integrity": "sha512-twVBDoonss/A6chyHOAQkx8Y+daAablgQy4khn8vYnrbcU4UvLLLFX2TCVhbGOXxTxJ4pqQtlTzjBErRyq/NDA==", "signatures": [{"sig": "MEYCIQDhv/3tf26sR9H8GzwQMHSVcinaGJZwuKbmTzFDoozSrwIhAL+fJiKJ20Aj0cQ2/QctUHyJVXamqw0+AhAQGi9NMr4q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "_npmVersion": "1.2.10", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.6.1": {"name": "ms", "version": "0.6.1", "_id": "ms@0.6.1", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "dist": {"shasum": "ed57e5f3fc736e09afc85017c5c912a47bc59ab9", "tarball": "https://registry.npmjs.org/ms/-/ms-0.6.1.tgz", "integrity": "sha512-TAjpu7RNwH/eBQfmrVg6eA6hClZfmhd3B2Ghp/Di5HMLjNBhd44KtO5lWjQj0EayygL1BsfZEJe3Y4sBHMQQEg==", "signatures": [{"sig": "MEUCIB+OnvSUlnmsavDONG0bVMAdqiV7NovCVOZQQ3bxNupwAiEA6lNDCDxtfrMbenGmudYz2QXJopvskEwHujahKewJMSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "_npmVersion": "1.2.18", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.6.2": {"name": "ms", "version": "0.6.2", "_id": "ms@0.6.2", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "dist": {"shasum": "d89c2124c6fdc1353d65a8b77bf1aac4b193708c", "tarball": "https://registry.npmjs.org/ms/-/ms-0.6.2.tgz", "integrity": "sha512-/pc3eh7TWorTtbvXg8je4GvrvEqCfH7PA3P7iW01yL2E53FKixzgMBaQi0NOPbMJqY34cBSvR0tZtmlTkdUG4A==", "signatures": [{"sig": "MEUCIQDCgitVLdKKJz4S5O5PNLSgVL8RtKD4gCS5gxJ9TsxtawIgJeLTkc3mJ3TYQWywa7RFPbSYpIEaELEoZ6NTfB/xL3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "repository": {"url": "git://github.com/guille/ms.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.7.0": {"name": "ms", "version": "0.7.0", "_id": "ms@0.7.0", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/guille/ms.js", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "dist": {"shasum": "865be94c2e7397ad8a57da6a633a6e2f30798b83", "tarball": "https://registry.npmjs.org/ms/-/ms-0.7.0.tgz", "integrity": "sha512-YmuMMkfOZzzAftlHwiQxFepJx/5rDaYi9o9QanyBCk485BRAyM/vB9XoYlZvglxE/pmAWOiQgrdoE10watiK9w==", "signatures": [{"sig": "MEYCIQCtVALwxJFNy03+cbKpigsVH5j6aF2phAMKFWQ1So8InwIhAI/lcstQyWGjS4MEqbxhe61oQtzWjrqBy3wlB73tQ2QM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_shasum": "865be94c2e7397ad8a57da6a633a6e2f30798b83", "gitHead": "1e9cd9b05ef0dc26f765434d2bfee42394376e52", "scripts": {}, "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "repository": {"url": "git://github.com/guille/ms.js.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "Tiny ms conversion utility", "directories": {}, "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.7.1": {"name": "ms", "version": "0.7.1", "_id": "ms@0.7.1", "maintainers": [{"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/guille/ms.js", "bugs": {"url": "https://github.com/guille/ms.js/issues"}, "dist": {"shasum": "9cd13c03adbff25b65effde7ce864ee952017098", "tarball": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha512-lRLiIR9fSNpnP6TC4v8+4OU7oStC01esuNowdQ34L+Gk8e5Puoc88IqJ+XAY/B3Mn2ZKis8l8HX90oU8ivzUHg==", "signatures": [{"sig": "MEUCIAh/B/bDLriFcyK2amvVwmthEFeNv1XC0b0oEDSJbLfcAiEA83Vcj0kgtbPmVCXfJPGF7BxNZBca+FMeCIZCbD2rGCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "_shasum": "9cd13c03adbff25b65effde7ce864ee952017098", "gitHead": "713dcf26d9e6fd9dbc95affe7eff9783b7f1b909", "scripts": {}, "_npmUser": {"name": "rauchg", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "repository": {"url": "git://github.com/guille/ms.js.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "Tiny ms conversion utility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"mocha": "*", "serve": "*", "expect.js": "*"}}, "0.7.2": {"name": "ms", "version": "0.7.2", "license": "MIT", "_id": "ms@0.7.2", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "xo": {"envs": ["mocha"], "rules": {"complexity": 0}, "space": true, "semicolon": false}, "dist": {"shasum": "ae25cf2512b3885a1d95d7f037868d8431124765", "tarball": "https://registry.npmjs.org/ms/-/ms-0.7.2.tgz", "integrity": "sha512-5NnE67nQSQDJHVahPJna1PQ/zCXMnQop3yUCxjKPNzCxuyPSKWTQ/5Gu5CZmjetwGLWRA+PzeF5thlbOdbQldA==", "signatures": [{"sig": "MEQCIEYNaFIYpVNHRPffvUdupOK9tTNlIohwSMDFUl9gyeqaAiBEcqCkVOtW/XhlVHcvhj2xo2Vn88U05QUt8roCPdDjgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js"], "_shasum": "ae25cf2512b3885a1d95d7f037868d8431124765", "gitHead": "ac92a7e0790ba2622a74d9d60690ca0d2c070a45", "scripts": {"test": "xo && mocha test/index.js", "test-browser": "serve ./test"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Tiny milisecond conversion utility", "directories": {}, "_nodeVersion": "6.8.0", "devDependencies": {"xo": "^0.17.0", "mocha": "^3.0.2", "serve": "^1.4.0", "expect.js": "^0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/ms-0.7.2.tgz_1477383407940_0.4743474116548896", "host": "packages-18-east.internal.npmjs.com"}}, "0.7.3": {"name": "ms", "version": "0.7.3", "license": "MIT", "_id": "ms@0.7.3", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "xo": {"envs": ["mocha"], "rules": {"complexity": 0}, "space": true, "semicolon": false}, "dist": {"shasum": "708155a5e44e33f5fd0fc53e81d0d40a91be1fff", "tarball": "https://registry.npmjs.org/ms/-/ms-0.7.3.tgz", "integrity": "sha512-lrKNzMWqQZgwJahtrtrM+9NgOoDUveDrVmm5aGXrf3BdtL0mq7X6IVzoZaw+TfNti29eHd1/8GI+h45K5cQ6/w==", "signatures": [{"sig": "MEYCIQCPT8epXfmpMGvrTsUkcuWcK3MQdTy8KB408Uf8bPxeIgIhAMAjOW0HmRkZI5OyL60oN9xdNQb+Tvfr9eT1yaIt68x3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js"], "_shasum": "708155a5e44e33f5fd0fc53e81d0d40a91be1fff", "gitHead": "2006a7706041443fcf1f899b5752677bd7ae01a8", "scripts": {"test": "xo && mocha test/index.js", "test-browser": "serve ./test"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "component": {"scripts": {"ms/index.js": "index.js"}}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Tiny milisecond conversion utility", "directories": {}, "_nodeVersion": "7.6.0", "devDependencies": {"xo": "0.17.0", "mocha": "3.0.2", "serve": "5.0.1", "expect.js": "0.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/ms-0.7.3.tgz_1489010366101_0.14404030703008175", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "ms", "version": "1.0.0", "license": "MIT", "_id": "ms@1.0.0", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "dist": {"shasum": "59adcd22edc543f7b5381862d31387b1f4bc9473", "tarball": "https://registry.npmjs.org/ms/-/ms-1.0.0.tgz", "integrity": "sha512-85ytwCiGUnD84ui6ULG1KBFMaZgHW3jg5KPr9jt+ZPYt75+XK+JGbYddGrBQ+RSHXOhekCnCZwJywBoFvFl0kw==", "signatures": [{"sig": "MEUCICKGVNMx/wRg/73WuAJk6cBE7jiz6Q2yxkbeq6B5Oz82AiEAxy5ucslI5Ct+mjRIDp8Q+T1X6mn+a3acKHCKpAG5vRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js"], "_shasum": "59adcd22edc543f7b5381862d31387b1f4bc9473", "gitHead": "7daf984a9011e720cc3c165ed82c4506f3471b37", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Tiny milisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "7.7.3", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "devDependencies": {"husky": "0.13.2", "mocha": "3.0.2", "eslint": "3.18.0", "expect.js": "0.3.1", "lint-staged": "3.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms-1.0.0.tgz_1489959793252_0.42147551802918315", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "ms", "version": "2.0.0", "license": "MIT", "_id": "ms@2.0.0", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "dist": {"shasum": "5608aeadfc00be6c2901df5f9861788de0d597c8", "tarball": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "signatures": [{"sig": "MEUCIQCYXYoFltYf81nBW7DQpNjYSZEegqIVzjASdvw/XwCIGwIgAQ1zDH6y0Dzva9FcQbckvTwThyFbVDdT1p7PIGfg+LU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "_from": ".", "files": ["index.js"], "_shasum": "5608aeadfc00be6c2901df5f9861788de0d597c8", "gitHead": "9b88d1568a52ec9bb67ecc8d2aa224fa38fd41f4", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Tiny milisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "7.8.0", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "devDependencies": {"husky": "0.13.3", "mocha": "3.4.1", "eslint": "3.19.0", "expect.js": "0.3.1", "lint-staged": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/ms-2.0.0.tgz_1494937565215_0.34005374647676945", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.0": {"name": "ms", "version": "2.1.0", "license": "MIT", "_id": "ms@2.1.0", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "dist": {"shasum": "9a345be8f6a4aadc6686d74d88a23c1b84720549", "tarball": "https://registry.npmjs.org/ms/-/ms-2.1.0.tgz", "integrity": "sha512-gVZHb22Z7YDyiiaoGld9LD4tUuDDxdkDJUEfTIej9LFePFqiE9JxI0qTFfu6tD7Wu03lg7skmVwTmA6XkeMlPQ==", "signatures": [{"sig": "MEUCIH6ct/FoEj38rHsHVN+ZLKlXQfH0Swo0I94hVtIoQUxiAiEA0LUEHUYdZhIZnAgw8V+P4QNbmNMfY9N1QSkb65ZE6cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": ["index.js"], "gitHead": "845c302f155d955141d623a0276bbff3529ed626", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "9.2.0", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "devDependencies": {"husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.12.1", "expect.js": "0.3.1", "lint-staged": "5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms-2.1.0.tgz_1512060855394_0.6358025514055043", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "ms", "version": "2.1.1", "license": "MIT", "_id": "ms@2.1.1", "maintainers": [{"name": "leo", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "dist": {"shasum": "30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a", "tarball": "https://registry.npmjs.org/ms/-/ms-2.1.1.tgz", "integrity": "sha512-tgp+dl5cGk28utYktBsrFqA7HKgrhgPsg6Z/EfhWI4gl1Hwq8B/GmY/0oXZ6nF8hDVesS/FpnYaD/kOWhYQvyg==", "signatures": [{"sig": "MEQCIFNwDicPfUKOIZ69PqiMYUAEqnRA+H4zk0kq9GpcOAqrAiA+oLpPxjd2opwatXoRpO+5VwyQyHaqAohY6RW8E8seyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index", "files": ["index.js"], "gitHead": "fe0bae301a6c41f68a01595658a4f4f0dcba0e84", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "leo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "9.2.0", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "devDependencies": {"husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.12.1", "expect.js": "0.3.1", "lint-staged": "5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms-2.1.1.tgz_1512066615982_0.7117063472978771", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "ms", "version": "2.1.2", "license": "MIT", "_id": "ms@2.1.2", "maintainers": [{"name": "alexaltea", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "b3nnyl", "email": "<EMAIL>"}, {"name": "caarlos0", "email": "<EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "fivepointseven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "hharnisc", "email": "<EMAIL>"}, {"name": "huvik", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "manovotny", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "olliv", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "sarupbanskota", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "t.sophe<PERSON><EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "zeit-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/zeit/ms#readme", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "dist": {"shasum": "d09d1f357b443f493382a8eb3ccd183872ae6009", "tarball": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "fileCount": 4, "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "signatures": [{"sig": "MEUCIQDVNTHsphMsdrWmzEq1T6dFGHe80Vg5ZmIWN1NIKOAHewIgE0sscC2rehxwM3V43Nil6I4auXhiwsdK9Kb1JtejdzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+U4MCRA9TVsSAnZWagAA71AP/2rpu0zYdK5Z/BXrrKNW\nljsVOs4oHNJ2jeZrzpcV8eZUZ6zAi78plyxcnMCbbG+TrpjXrPcb8qFq630G\nS6+srbEF0lCGCc+ktJrNJPTeXkDxukQXVrepgZ2kxZ4m3q/QIAVoK4t9ebuH\nNYa+39wwET9oPuPsk+YY0Z7fQ1vadyuzHYOrRmtudV3ZtyT0k74Ec3IhKamW\nlLDJtCklD7IGcwirrvPssxmYu8WP+PAyFnrVaOW+iior1o07oWO2mk7sk3Fx\nwBSBFf7vZqFJP6Qg1m3TVBAiipL+Pf+b3Dy8fhmn4NhTGj/9Wl7f/LcqogOV\nV9l77qsZldCERBwmwLsHlMyCSSl/b2qaz28ZBTRwHtHdo19QT6MqX8Yvomy4\n+gyPBBAHC6bqqLZ0veRKzSNFfJYoFw8tQzyjSjpmYcdxaB5w4z4QPZAkZCku\ns+sooI5Xo33E9rcEDWmyqxdUud+Au/fTttg0dReYe8NVrUgzyk4T1W+D7I4k\nu3XV7O9bOaJiBTNsb22lGIC6E/HtjfoqW7iwl0cdZ8iZcPTBClkzsy9Hz6a4\nmNKDARFL0wjzWF/CoXyKcI6t9ruOepTQRfbAtZDAo4LEYj/bGiqm2kbX5AP6\nicCOlufTNip74l2bXv2sJNwtjGzEYF/S79Oyc49IP/ovIua4quXXtSjAh8Bg\nLrV/\r\n=GrYx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "gitHead": "7920885eb232fbe7a5efdab956d3e7c507c92ddf", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "styfle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/zeit/ms.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "10.15.3", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.12.1", "expect.js": "0.3.1", "lint-staged": "5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms_2.1.2_1559842315767_0.4700607530567853", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "ms", "version": "2.1.3", "license": "MIT", "_id": "ms@2.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "cleishm", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "will<PERSON><PERSON>@bbi.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "juan<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "codetheory", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "574c8138ce1d2b5861f0b44579dbadd60c6615b2", "tarball": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "fileCount": 4, "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "signatures": [{"sig": "MEYCIQCpp8dz4QhYMwrrNgFARRXozR4wAyDcUVNXEBw0PmSj+wIhALlCZH2KJyCo9qv/3CfMFsvx9bXKQNQBOqmLstIPXP2L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz4WbCRA9TVsSAnZWagAA5A8P/jNowbVOl1ORENKivAXb\nQ3NncrkWHdRjUGeUhX89Ih3N+woNugnSTOEKACswARtqXMf5M1Iy8GODorDp\noz+pqU0HGU+KjLO/sL+TGxJJJAMfX3vhRZTHk5ZzKDi9s6iAM3nMeE5rwNUS\n7wprOzbKNE9hev82zLgfY8kF7UhxY09BH/GBS+kWGD3ViM8R5vl49JEfrvN9\nSKris0FTSP/YL1QrRNjvMMfGh9WhMOC/FLkJnIErcw2I8g/XmBOApjqM9KhG\n42/ls4gXuaUinNXC68wAbntxhHtJo2403NVmU7UJDDdulEBbTXZ18cKHt520\nUkRZp8piQb1m3QR8XPjvpnShlOutYdQJfjltY5z12Wfwj5OBVsurWeFtJRme\nBxn9pdrKW45doypT1Lc7LXoIftLBtToVtWRThEVihq4I9f4zpR9Uzc3qp1jU\nlEo9ndqf9rg9oVV8fSK+dIDuUUyp7NrI5uCfcUMfKEgwWortapNKNvMuHp7r\noZhuGRekRc1kG8YmsYfLKv3kRS8uiXa/jbwD4PkNGbev7KhEptCnGZm78z9k\nV0KOdaCU3Igo6rK23kgsAFhxDvMANHby3dLYQMbZOoqkZLv4qiPS/7raPOLc\n5q/ezwT2JZLWZlTbZnigAVuZ5aHmLb6QEuMLcIQaelDkH7XWCNpED8cM2pFX\nTllW\r\n=eZCP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index", "gitHead": "1c6264b795492e8fdecbc82cb8802fcfbfc08d26", "scripts": {"lint": "eslint lib/* bin/*", "test": "mocha tests.js", "precommit": "lint-staged"}, "_npmUser": {"name": "styfle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "_nodeVersion": "12.18.3", "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.18.2", "prettier": "2.0.5", "expect.js": "0.3.1", "lint-staged": "5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms_2.1.3_1607435675054_0.7617026089064693", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.0": {"name": "ms", "version": "3.0.0-beta.0", "license": "MIT", "_id": "ms@3.0.0-beta.0", "maintainers": [{"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "aspctub", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "d970e06f8b1e384befe5acae5c27209e9b93916f", "tarball": "https://registry.npmjs.org/ms/-/ms-3.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-x620WtkfdGJZPaGRIJLeTEJcHiq6fHx0DR2KVfMgn4bLB3N60NUFrTTfuo7mcNPc5coqyu0ioK5m92CXnJKYGQ==", "signatures": [{"sig": "MEUCIQD+jRNIkA56bvZw4h1rzaZ1SIiPaZEQeZb9o1gnY9G3KgIgP+ZTSGpNasc0hM4f2g8jhBrXzBG9/3qXFQ2TEDqCRwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhH8IPCRA9TVsSAnZWagAAg5UQAIbpkc8lAv6vhXdAdaG9\nB68gErEeCr23hE2FgK2h/p8FiSbFe9/7ypcroMZS7So+U5AORtaT62E44yOa\nxPQPpLZ+BlK7tzsrNZ7+R6MHQ9HLIKbAro0h0z/XOWzMSGRdWJ1oZ7oAWnK/\nqXluUHk/2qrRh9GnH01Ad1+Ji59bdrzVLG4fEqd1z9A53Uvy3FukX6aAvbic\npn6P7hqWsI+FGChjLlB57mGAtK1VazKSYjh4Y9rG5HY11D/pvXhcH+ca1ZJj\nYYijM6mcACB5HU/IJpqZoMYZMzlGTGhIVHjbm6GDs47d0aGPlnh5274737mo\nA5gzHabPeBj15ad3p2y7K7Lka6zxWkGsc69CRkE9LL6+fcCprynxqOD53Zg3\nmR1zU/druwYyOY2hrxseuqVKGOXyQN9Zz/SCC2jH+vltzY3hIOZ6GwfeyRQE\nHxIIfqpNFTruzDYQvVb48BlMWnHCXYJfrVdfQfBeVKuO0Nh/g297l1hKnj9n\n7ENP5kzi8wvaZBYfFYQaMTxT5FbgXL7h2sIdxD+8kj8o3G4saGXKDJNAJJA+\n/T1u9pRoCcnTvOdrq0A/76fJ1sxEyVMN3AMLDxOSwdpMNd7moWo7wZDZQNCr\n5Blg+qB/5Nm/FvywIgB9Lp0Al932k9xkWtOBQvD5LMvTKHNRkz//RrquxkK4\na/yk\r\n=vM3v\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "main": "./lib/index.cjs", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "readme": "# ms\n\n![CI](https://github.com/vercel/ms/workflows/CI/badge.svg)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n<!-- prettier-ignore -->\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\nms('-3 days') // -259200000\nms('-1h')     // -3600000\nms('-200')    // -200\n```\n\n### Convert from Milliseconds\n\n<!-- prettier-ignore -->\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(-3 * 60000)        // \"-3m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time Format Written-Out\n\n<!-- prettier-ignore -->\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(-3 * 60000, { long: true })        // \"-3 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [Node.js](https://nodejs.org) and in the browser\n- If a number is supplied to `ms`, a string with a unit is returned\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`)\n- If you pass a string with a number and a valid unit, the number of equivalent milliseconds is returned\n\n## TypeScript support\n\nAs of v3.0, this package includes TypeScript definitions.\n\nFor added safety, we're using [Template Literal Types](https://www.typescriptlang.org/docs/handbook/2/template-literal-types.html) (added in [TypeScript 4.1](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-1.html)). This ensures that you don't accidentally pass `ms` values that it can't process.\n\nThis won't require you to do anything special in most situations, but you can also import the `StringValue` type from `ms` if you need to use it.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Using the exported type.\nfunction example(value: StringValue) {\n  ms(value);\n}\n\n// This function will only accept a string compatible with `ms`.\nexample('1 h');\n```\n\nIn this example, we use a [Type Assertion](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#type-assertions) to coerce a `string`.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Type assertion with the exported type.\nfunction example(value: string) {\n  try {\n    // A string could be \"wider\" than the values accepted by `ms`, so we assert\n    // that our `value` is a `StringValue`.\n    //\n    // It's important to note that this can be dangerous (see below).\n    ms(value as StringValue);\n  } catch (error: Error) {\n    // Handle any errors from invalid vaues.\n    console.error(error);\n  }\n}\n\n// This function will accept any string, which may result in a bug.\nexample('any value');\n```\n\nYou may also create a custom Template Literal Type.\n\n```ts\nimport ms from 'ms';\n\ntype OnlyDaysAndWeeks = `${number} ${'days' | 'weeks'}`;\n\n// Using a custom Template Literal Type.\nfunction example(value: OnlyDaysAndWeeks) {\n  // The type of `value` is narrower than the values `ms` accepts, which is\n  // safe to use without coercion.\n  ms(value);\n}\n\n// This function will accept \"# days\" or \"# weeks\" only.\nexample('5.2 days');\n```\n\n## Related Packages\n\n- [ms.macro](https://github.com/knpwrs/ms.macro) - Run `ms` as a macro at build-time.\n\n## Caught a Bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, Node.js will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "engines": {"node": ">=12.13"}, "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "gitHead": "4059199878427d37197270ee7852d7c18206e92a", "scripts": {"test": "jest", "build": "scripts/build.js", "prepare": "husky install", "precommit": "lint-staged", "type-check": "tsc --noEmit", "eslint-check": "eslint --max-warnings=0 .", "prepublishOnly": "npm run build", "prettier-check": "prettier --check ."}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"jest": "27.0.6", "husky": "7.0.1", "eslint": "7.32.0", "ts-jest": "27.0.5", "prettier": "2.3.2", "typescript": "4.3.5", "@types/jest": "27.0.1", "lint-staged": "11.1.2", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "eslint-config-prettier": "8.3.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2"}, "_npmOperationalInternal": {"tmp": "tmp/ms_3.0.0-beta.0_1629471246957_0.5534162813430621", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "ms", "version": "3.0.0-beta.1", "license": "MIT", "_id": "ms@3.0.0-beta.1", "maintainers": [{"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "aspctub", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "1796d327b201d04705b8bf70b67442246c1e26e4", "tarball": "https://registry.npmjs.org/ms/-/ms-3.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-72RBgCsIUfh6MtK1FyAqWVYjMhvYsU/5WbiTrAksNyIcv/uhR8r6g7wU5JEUIzhRYYI1uF9+I5S1vOb41NYxkw==", "signatures": [{"sig": "MEQCIH9xLNQt+Wq9N34218VR3qSfJEMeQH+MAyMXdpB6TjWrAiA6fbt9dFUCnCu1ZEMCkEn5bH/iI4EhijfNqN11rXMnww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhH8pPCRA9TVsSAnZWagAAGCwP/3DbSG2smcKeDj9lFBPU\nmpBf0EX+gm3YCCoz1ixYWF8TaIYRC6uFQFmxWP/7o+ag5B4wDQNLFh0kNlbi\ni8z+9fkUbKus6DKh8SUZgQNRm+NYNCel1KsjQtzxyIslB2EAGShYgSYTMsP7\nD6G2d5euZ+TPjyobhRY46N0NKtpeR3c6tI37/4I800LZyltoZYz6IHGz045l\nd3V5OcqdTQOSi/7AFd0Zc13HUOPqYmSJlW5wb9Txyn7Q3z7KviqjhwK0jCF1\nrCVGDwBJKnLwdy/SzcARY3axunoFEBe/tp8zriXtghNMrS/bfnLgG4HFWdSJ\n86cmCYpdKbgX0gg4tjBzq7XvsgtVbnYJqKIE+lZ9hawMJ/Z2+3JNXIQdQvQq\n4QiYjfRpDmEzhMsDxzc1AE+k4DqJzSvfMby40ZL6wWjYaxqYHmh+Hzs7AI9j\nHPwQqDGJjI8rIRJfIE3bforsqXKIYl0hhrX9EiGjQi4WTHYvKnHqwM1fu2IG\n81FvA3r3TlMAqElVjyTweA+aC8ZDo1rotEZ+o4GwHJdzq/o/Cl0TnywnvDbg\nQ7+0yUwUFhtvhG91ALA7+//88EtoZQWA/5pvAjhCmPW806Duj/gYph2rJusx\n2MCeY96ymM9vNPPP0jPaJ7clMcDJgxG9MDlBtQWS0saV9YSQJYs1sBBArjAK\n10TE\r\n=wU56\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "main": "./lib/index.cjs", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "readme": "# ms\n\n![CI](https://github.com/vercel/ms/workflows/CI/badge.svg)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n<!-- prettier-ignore -->\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\nms('-3 days') // -259200000\nms('-1h')     // -3600000\nms('-200')    // -200\n```\n\n### Convert from Milliseconds\n\n<!-- prettier-ignore -->\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(-3 * 60000)        // \"-3m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time Format Written-Out\n\n<!-- prettier-ignore -->\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(-3 * 60000, { long: true })        // \"-3 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [Node.js](https://nodejs.org) and in the browser\n- If a number is supplied to `ms`, a string with a unit is returned\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`)\n- If you pass a string with a number and a valid unit, the number of equivalent milliseconds is returned\n\n## TypeScript support\n\nAs of v3.0, this package includes TypeScript definitions.\n\nFor added safety, we're using [Template Literal Types](https://www.typescriptlang.org/docs/handbook/2/template-literal-types.html) (added in [TypeScript 4.1](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-1.html)). This ensures that you don't accidentally pass `ms` values that it can't process.\n\nThis won't require you to do anything special in most situations, but you can also import the `StringValue` type from `ms` if you need to use it.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Using the exported type.\nfunction example(value: StringValue) {\n  ms(value);\n}\n\n// This function will only accept a string compatible with `ms`.\nexample('1 h');\n```\n\nIn this example, we use a [Type Assertion](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#type-assertions) to coerce a `string`.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Type assertion with the exported type.\nfunction example(value: string) {\n  try {\n    // A string could be \"wider\" than the values accepted by `ms`, so we assert\n    // that our `value` is a `StringValue`.\n    //\n    // It's important to note that this can be dangerous (see below).\n    ms(value as StringValue);\n  } catch (error: Error) {\n    // Handle any errors from invalid vaues.\n    console.error(error);\n  }\n}\n\n// This function will accept any string, which may result in a bug.\nexample('any value');\n```\n\nYou may also create a custom Template Literal Type.\n\n```ts\nimport ms from 'ms';\n\ntype OnlyDaysAndWeeks = `${number} ${'days' | 'weeks'}`;\n\n// Using a custom Template Literal Type.\nfunction example(value: OnlyDaysAndWeeks) {\n  // The type of `value` is narrower than the values `ms` accepts, which is\n  // safe to use without coercion.\n  ms(value);\n}\n\n// This function will accept \"# days\" or \"# weeks\" only.\nexample('5.2 days');\n```\n\n## Related Packages\n\n- [ms.macro](https://github.com/knpwrs/ms.macro) - Run `ms` as a macro at build-time.\n\n## Caught a Bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, Node.js will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "engines": {"node": ">=12.13"}, "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "gitHead": "7068bb390311b2620e65f992cd3ad6ff19d13400", "scripts": {"test": "jest", "build": "scripts/build.js", "prepare": "husky install", "precommit": "lint-staged", "type-check": "tsc --noEmit", "eslint-check": "eslint --max-warnings=0 .", "prepublishOnly": "npm run build", "prettier-check": "prettier --check ."}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"jest": "27.0.6", "husky": "7.0.1", "eslint": "7.32.0", "ts-jest": "27.0.5", "prettier": "2.3.2", "typescript": "4.3.5", "@types/jest": "27.0.1", "lint-staged": "11.1.2", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "eslint-config-prettier": "8.3.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2"}, "_npmOperationalInternal": {"tmp": "tmp/ms_3.0.0-beta.1_1629473359666_0.4804227855905876", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "ms", "version": "3.0.0-beta.2", "license": "MIT", "_id": "ms@3.0.0-beta.2", "maintainers": [{"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "dglsparsons", "email": "<EMAIL>"}, {"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "c1a586879b489759c44be2ac402ff1df7c314ed9", "tarball": "https://registry.npmjs.org/ms/-/ms-3.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-G/4X0GjOFFpeGVj0D/yxd7plnMjizeLa2mBu2yNRPQlDlvmERfqZ2alTIijo9QNH91b9g1IlJAYsVV1g6GbWvg==", "signatures": [{"sig": "MEUCIQC1hBJF3CyIp43qz+NutYJQROKQx4EJrU27T+WPDAVO9AIgLOgW7rV1gAQx5LTNjsPyNTUVoncio7Cm79Bqjc4tp+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJnYECRA9TVsSAnZWagAA8LAP/jr9VE4KB/K4xCmdrgkG\nZOrWFvAMO0Wu1TZXBEgt0SnCBKXP5jFN0IJggJSmEMfEmsrwkgdId9nk0ys/\nEP7jUr0RficCQOQTU2uizTPnhOMrq8bfrNiXjnMHpGZC1eMPCGb31pzSb2VO\nKjHuUACp98wf84+VCYt9YADukcCg80wNT4+9rksEob2AGOf4KfwDae7zLeDt\nqs37qJ3gUDHZ7KthRdbBiBVKlUqlUW4MYKtFg+BecZ+gF6xeQv3GCEnNSMV6\n0Hqi1YE9psDv7dTHYEEG9yH/W3kq6ebQM/QeaSEATOAXuPhb2U84eVuOHbYJ\n6XAUKWGeQ0rilGVMuJ81anRdp3nlogiABou3hMieqSMiuVFYFajhhdQdUmLO\niHyBKMeY9+5z3rGG0styw4xp0EZcUcRNOGtniHVyHMvCwDiw9kIihhVFyLEe\n63F5u3Q8kQ+IYYZhZyZGDODoBcy8nD7LVmEAFQmIobqdPOR5PLg+RjzxXWYD\nLwQ9OBmB6UIjLVVNKvbhG+TF7gf5ENUgx9q4LA7ewyydbQ8CsbQyckzU+ARS\nm1Y/9Un7pjxExBp0h1P6yCJLf15U8v2S6XrFB7tBiLXxluO21SWNAonf2eo/\nemhpixBmFsVyObD9a9qVI6EKww6Kpad9WF6mungV8DhJ1mWalFHHbED7xDr3\nXNyR\r\n=nwPo\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "main": "./lib/index.cjs", "type": "module", "types": "./lib/index.d.ts", "module": "./lib/index.mjs", "readme": "# ms\n\n![CI](https://github.com/vercel/ms/workflows/CI/badge.svg)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n<!-- prettier-ignore -->\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\nms('-3 days') // -259200000\nms('-1h')     // -3600000\nms('-200')    // -200\n```\n\n### Convert from Milliseconds\n\n<!-- prettier-ignore -->\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(-3 * 60000)        // \"-3m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time Format Written-Out\n\n<!-- prettier-ignore -->\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(-3 * 60000, { long: true })        // \"-3 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [Node.js](https://nodejs.org) and in the browser\n- If a number is supplied to `ms`, a string with a unit is returned\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`)\n- If you pass a string with a number and a valid unit, the number of equivalent milliseconds is returned\n\n## TypeScript support\n\nAs of v3.0, this package includes TypeScript definitions.\n\nFor added safety, we're using [Template Literal Types](https://www.typescriptlang.org/docs/handbook/2/template-literal-types.html) (added in [TypeScript 4.1](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-1.html)). This ensures that you don't accidentally pass `ms` values that it can't process.\n\nThis won't require you to do anything special in most situations, but you can also import the `StringValue` type from `ms` if you need to use it.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Using the exported type.\nfunction example(value: StringValue) {\n  ms(value);\n}\n\n// This function will only accept a string compatible with `ms`.\nexample('1 h');\n```\n\nIn this example, we use a [Type Assertion](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#type-assertions) to coerce a `string`.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Type assertion with the exported type.\nfunction example(value: string) {\n  try {\n    // A string could be \"wider\" than the values accepted by `ms`, so we assert\n    // that our `value` is a `StringValue`.\n    //\n    // It's important to note that this can be dangerous (see below).\n    ms(value as StringValue);\n  } catch (error: Error) {\n    // Handle any errors from invalid vaues.\n    console.error(error);\n  }\n}\n\n// This function will accept any string, which may result in a bug.\nexample('any value');\n```\n\nYou may also create a custom Template Literal Type.\n\n```ts\nimport ms from 'ms';\n\ntype OnlyDaysAndWeeks = `${number} ${'days' | 'weeks'}`;\n\n// Using a custom Template Literal Type.\nfunction example(value: OnlyDaysAndWeeks) {\n  // The type of `value` is narrower than the values `ms` accepts, which is\n  // safe to use without coercion.\n  ms(value);\n}\n\n// This function will accept \"# days\" or \"# weeks\" only.\nexample('5.2 days');\n```\n\n## Related Packages\n\n- [ms.macro](https://github.com/knpwrs/ms.macro) - Run `ms` as a macro at build-time.\n\n## Caught a Bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, Node.js will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "engines": {"node": ">=12.13"}, "exports": {"import": "./lib/index.mjs", "require": "./lib/index.cjs"}, "gitHead": "6d2221735dbe9ec197e0753c22af4ce406ac512b", "scripts": {"test": "jest", "build": "scripts/build.js", "prepare": "husky install", "precommit": "lint-staged", "type-check": "tsc --noEmit", "eslint-check": "eslint --max-warnings=0 .", "prepublishOnly": "npm run build", "prettier-check": "prettier --check ."}, "_npmUser": {"name": "mrmckeb", "email": "<EMAIL>"}, "prettier": {"tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"jest": "27.0.6", "husky": "7.0.1", "eslint": "7.32.0", "ts-jest": "27.0.5", "prettier": "2.3.2", "typescript": "4.3.5", "@types/jest": "27.0.1", "lint-staged": "11.1.2", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "eslint-config-prettier": "8.3.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2"}, "_npmOperationalInternal": {"tmp": "tmp/ms_3.0.0-beta.2_1629910532683_0.9012556075786939", "host": "s3://npm-registry-packages"}}, "3.0.0-canary.0": {"name": "ms", "version": "3.0.0-canary.0", "license": "MIT", "_id": "ms@3.0.0-canary.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vercel-release-bot", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "dglsparsons", "email": "<EMAIL>"}, {"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "f965adb7f3afccac672ec0b2cf6bdc5320b755c5", "tarball": "https://registry.npmjs.org/ms/-/ms-3.0.0-canary.0.tgz", "fileCount": 3, "integrity": "sha512-FrdRKJ9G+nm9Bw6atUMrbIhGTJ2emtf0KRgtzgSrAW1nnV9RuQAXc7sbFWdr1RfOH3fcNk0MyK+ma4iihdLmyA==", "signatures": [{"sig": "MEUCIG9fP6snzEazBzbtwGebA39rR56jidykhpdO8FFQ4sR/AiEAxFhwnZyHkBJ0/RWH+qTfKSQBmWWVmxQnMyaVtXekPCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQfUcCRA9TVsSAnZWagAAQcsP/iMuelcMKc3N/pGuuY6u\nwIFV0jvr8Eg34xc+7wB0Udatyc0gGRbUtfLbjp7dpAfgRahYEyoC5g3FR+5p\nP1MnSz6pUpDJmUyTwFvrOzK7qcOaJ2GhVPZfwAlxOMBZZgHnwV8kxaGFtzfp\nnF3bm88rpdGP39euQZ+nmqF6GbLk75w+n/hcZR7Cn8/Td/cCPEZml2jRRAzD\nXatLIMpQwt/zmp1tYwTyfedfL8jbiG8cCZhIoFcSxKUzzUxNHY7DEwkxSGaw\nhK7kuf9HhFuhIJ5TCt/2JiVVNk5UbnqECY9XmQ7AZe3iaYRBKDVGtdnlhiZf\nsYZJ1zEYF0gu7GKzrJ+V7fJQhq54GDATOqrVshkY0C4hUbwlle2s4GdtzhaH\n7Tcg/I0vh7vrzCHBxq29STEkwIXGEjlwZNjcodliHqMDBa58xmr9EADCpohD\niW5Hj+2uU7Ck3Kq1NA/+1PM1rnh8ggwOox/990XyCaTyHPGoiHIbGus/aNz2\n6OJuUMYPGEEFUxTYJDu/donxBjhgpBo2cPoa0duDohrkoXDWZAMq4ru6PBUz\nlKBm2zgLrbhx3RjjcbXfJUutU7bqk684+7TsjQSXMr2vp5DfB6UekivBn7E/\nwqMDJyAw5YI13mLWkywaHRlgnQaare2gnrpcentKrwkjT04D8K31yV0YFL+T\nl+PD\r\n=v81M\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">=12.13"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "6dd3b72e9b0a920d5ca04b989390ce89b12f62bd", "scripts": {"test": "jest", "build": "scripts/build.js", "prepare": "husky install", "precommit": "lint-staged", "type-check": "tsc --noEmit", "eslint-check": "eslint --max-warnings=0 .", "prepublishOnly": "npm run build", "prettier-check": "prettier --check ."}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "prettier": {"tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "7.23.0", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "27.1.1", "husky": "7.0.2", "eslint": "7.32.0", "ts-jest": "27.0.5", "prettier": "2.4.0", "typescript": "4.4.2", "@types/jest": "27.0.1", "lint-staged": "11.1.2", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "eslint-config-prettier": "8.3.0", "@typescript-eslint/parser": "4.31.0", "@typescript-eslint/eslint-plugin": "4.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms_3.0.0-canary.0_1631712540574_0.010946113352426678", "host": "s3://npm-registry-packages"}}, "3.0.0-canary.1": {"name": "ms", "version": "3.0.0-canary.1", "license": "MIT", "_id": "ms@3.0.0-canary.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vercel-release-bot", "email": "<EMAIL>"}, {"name": "geovanisouza92", "email": "<EMAIL>"}, {"name": "dglsparsons", "email": "<EMAIL>"}, {"name": "redacted-vercel", "email": "<EMAIL>"}, {"name": "gkarag<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matheuss", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "leo", "email": "<EMAIL>"}, {"name": "nkzawa", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "timneutkens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "iamevilrabbit", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "joe<PERSON><EMAIL>"}, {"name": "quietshu", "email": "<EMAIL>"}, {"name": "dav-is", "email": "<EMAIL>"}, {"name": "styfle", "email": "<EMAIL>"}, {"name": "zeit-bot", "email": "<EMAIL>"}, {"name": "lucleray", "email": "<EMAIL>"}, {"name": "mglagola", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "paulogdm", "email": "paul<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>at<PERSON><PERSON><PERSON>", "email": "ana.t<PERSON><PERSON><EMAIL>"}, {"name": "timer", "email": "<EMAIL>"}, {"name": "umegaya", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mfix22", "email": "<EMAIL>"}, {"name": "lfades", "email": "<EMAIL>"}, {"name": "rabaut", "email": "<EMAIL>"}, {"name": "coetry", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ragojose", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "paco", "email": "<EMAIL>"}, {"name": "skllcrn", "email": "<EMAIL>"}, {"name": "jani<PERSON><PERSON>-ralph", "email": "<EMAIL>"}, {"name": "atcastle", "email": "<EMAIL>"}, {"name": "keanulee", "email": "<EMAIL>"}, {"name": "spanicker", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "<PERSON>usseindji<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gmonaco", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "jkrems", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chibicode", "email": "<EMAIL>"}, {"name": "nazarenooviedo", "email": "<EMAIL>"}, {"name": "samsisle", "email": "<EMAIL>"}, {"name": "okbel", "email": "<EMAIL>"}, {"name": "hankvercel", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rizbizkits", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "cl3arglass", "email": "<EMAIL>"}, {"name": "chriswdmr", "email": "<EMAIL>"}, {"name": "ernestd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "is<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jhoch", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mrmckeb", "email": "<EMAIL>"}, {"name": "kuvos", "email": "<EMAIL>"}, {"name": "creationix", "email": "<EMAIL>"}, {"name": "aboodman", "email": "<EMAIL>"}, {"name": "huo<PERSON>", "email": "<EMAIL>"}, {"name": "cmvnk", "email": "<EMAIL>"}, {"name": "arv", "email": "erik.a<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ktcarter", "email": "<EMAIL>"}, {"name": "pad<PERSON>ia", "email": "<EMAIL>"}, {"name": "delba", "email": "<EMAIL>"}, {"name": "catsaremlg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dbredvick", "email": "<EMAIL>"}], "homepage": "https://github.com/vercel/ms#readme", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "dist": {"shasum": "c7b34fbce381492fd0b345d1cf56e14d67b77b80", "tarball": "https://registry.npmjs.org/ms/-/ms-3.0.0-canary.1.tgz", "fileCount": 6, "integrity": "sha512-kh8ARjh8rMN7Du2igDRO9QJnqCb2xYTJxyQYK7vJJS4TvLLmsbyhiKpSW+t+y26gyOyMd0riphX0GeWKU3ky5g==", "signatures": [{"sig": "MEUCIQDHBNEug7mB/RfCh7+qYSJ76i3rlL5OdUVtD0d5t3XnKAIgIWO/BOxIA0pVwJBJTsOoDtZjphznmwWADAwFq6853vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQhP7CRA9TVsSAnZWagAAS20P/1TYMFK4KBMo9NC82jk1\n+ZHpdsPG2bz+huqFkQphBtoYRZva5A/8KFcRofaMYRUEkyfP7u3EEhLthjIZ\nwTgcmcZ/iVFjvOxKNOZWyEXt6gs4HoVkK3Tq+mEwhrO6/dkPneID9XZP3fzL\nY/b2IBVsUkmwHtdkebH93psNAr34fW0+54rOEFpjCxI7Dq59za+5Yf4exmgx\nTMAaTPttWxlgfzUV3Z7/KQpZoe8/jTkneyxBFnmzk0ItTEl9tdX/EuZOp4/E\nOKG+nHYRT+42Ku/9l1t0IWhWRIUuyVn3GogapxAtYlVsYhljVf1jW3oTsx5H\n4c3Qikhgt1XSQrl6DgR/7jWg0z6Hyw+cm9agd0uvgyxuO7+ryO/RVo1+6E7T\nt/SqI5XK5qFLYvy/gOT90P2Xez5pfOWfTE9eJ8TQDkuNVFWMRVtTgIP5cKVP\nNJEENjthusiOc+4aPneNMeGLbgLXQEu89YQnuTHr597hF47MmtN5GYVgZ1BF\nW/WoNBwjouEPKUELXrVEk1i15IlDZlVbZUgtiP+3DChpvOTzCdaeHukagQIQ\nohl34zyBhqAlcVbWtSY1Kx7UwgDKTE74Wzx0E5JHX4HO3YDNlz2Zy54tnuR6\nQfKTzTrrcmXY+KRD1S3IY5sXToYbngX5O7jQRdOu/Rq9zJ2UGz3XpxRIGL64\nZeWp\r\n=JrQO\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">=12.13"}, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "gitHead": "1304f150b38027e0818cc122106b5c7322d68d0c", "scripts": {"test": "jest", "build": "scripts/build.js", "prepare": "husky install", "precommit": "lint-staged", "type-check": "tsc --noEmit", "eslint-check": "eslint --max-warnings=0 .", "prepublishOnly": "npm run build", "prettier-check": "prettier --check ."}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "lro<PERSON><EMAIL>"}, "prettier": {"tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "singleQuote": true, "trailingComma": "all"}, "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "_npmVersion": "7.23.0", "description": "Tiny millisecond conversion utility", "directories": {}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "*.{js,jsx,ts,tsx}": ["eslint --max-warnings=0 --fix"]}, "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "27.1.1", "husky": "7.0.2", "eslint": "7.32.0", "ts-jest": "27.0.5", "prettier": "2.4.0", "typescript": "4.4.2", "@types/jest": "27.0.1", "lint-staged": "11.1.2", "eslint-plugin-jest": "24.4.0", "eslint-plugin-tsdoc": "0.2.14", "eslint-config-prettier": "8.3.0", "@typescript-eslint/parser": "4.31.0", "@typescript-eslint/eslint-plugin": "4.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/ms_3.0.0-canary.1_1631720443773_0.6805463476444127", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-12-21T19:38:08.664Z", "modified": "2025-01-31T01:56:24.684Z", "0.1.0": "2011-12-21T19:38:26.538Z", "0.2.0": "2012-09-03T20:33:06.093Z", "0.3.0": "2012-09-07T20:36:45.931Z", "0.4.0": "2012-10-22T17:01:26.046Z", "0.5.0": "2012-11-10T00:39:49.944Z", "0.5.1": "2013-02-24T20:27:27.010Z", "0.6.0": "2013-03-15T15:26:35.127Z", "0.6.1": "2013-05-10T15:38:08.059Z", "0.6.2": "2013-12-05T15:57:45.292Z", "0.7.0": "2014-11-24T07:59:08.195Z", "0.7.1": "2015-04-20T23:38:57.957Z", "0.7.2": "2016-10-25T08:16:49.773Z", "0.7.3": "2017-03-08T21:59:28.048Z", "1.0.0": "2017-03-19T21:43:15.128Z", "2.0.0": "2017-05-16T12:26:06.610Z", "2.1.0": "2017-11-30T16:54:16.315Z", "2.1.1": "2017-11-30T18:30:16.876Z", "2.1.2": "2019-06-06T17:31:55.859Z", "2.1.3": "2020-12-08T13:54:35.223Z", "3.0.0-beta.0": "2021-08-20T14:54:07.095Z", "3.0.0-beta.1": "2021-08-20T15:29:19.828Z", "3.0.0-beta.2": "2021-08-25T16:55:32.842Z", "3.0.0-canary.0": "2021-09-15T13:29:00.734Z", "3.0.0-canary.1": "2021-09-15T15:40:43.956Z"}, "bugs": {"url": "https://github.com/vercel/ms/issues"}, "license": "MIT", "homepage": "https://github.com/vercel/ms#readme", "repository": {"url": "git+https://github.com/vercel/ms.git", "type": "git"}, "description": "Tiny millisecond conversion utility", "maintainers": [{"email": "<EMAIL>", "name": "matheuss"}, {"email": "<EMAIL>", "name": "rauchg"}, {"email": "<EMAIL>", "name": "nick.tracey"}, {"email": "<EMAIL>", "name": "vercel-release-bot"}, {"email": "<EMAIL>", "name": "zeit-bot"}, {"email": "<EMAIL>", "name": "matt.straka"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "leo"}], "readme": "# ms\n\n![CI](https://github.com/vercel/ms/workflows/CI/badge.svg)\n\nUse this package to easily convert various time formats to milliseconds.\n\n## Examples\n\n<!-- prettier-ignore -->\n```js\nms('2 days')  // 172800000\nms('1d')      // 86400000\nms('10h')     // 36000000\nms('2.5 hrs') // 9000000\nms('2h')      // 7200000\nms('1m')      // 60000\nms('5s')      // 5000\nms('1y')      // 31557600000\nms('100')     // 100\nms('-3 days') // -259200000\nms('-1h')     // -3600000\nms('-200')    // -200\n```\n\n### Convert from Milliseconds\n\n<!-- prettier-ignore -->\n```js\nms(60000)             // \"1m\"\nms(2 * 60000)         // \"2m\"\nms(-3 * 60000)        // \"-3m\"\nms(ms('10 hours'))    // \"10h\"\n```\n\n### Time Format Written-Out\n\n<!-- prettier-ignore -->\n```js\nms(60000, { long: true })             // \"1 minute\"\nms(2 * 60000, { long: true })         // \"2 minutes\"\nms(-3 * 60000, { long: true })        // \"-3 minutes\"\nms(ms('10 hours'), { long: true })    // \"10 hours\"\n```\n\n## Features\n\n- Works both in [Node.js](https://nodejs.org) and in the browser\n- If a number is supplied to `ms`, a string with a unit is returned\n- If a string that contains the number is supplied, it returns it as a number (e.g.: it returns `100` for `'100'`)\n- If you pass a string with a number and a valid unit, the number of equivalent milliseconds is returned\n\n## TypeScript support\n\nAs of v3.0, this package includes TypeScript definitions.\n\nFor added safety, we're using [Template Literal Types](https://www.typescriptlang.org/docs/handbook/2/template-literal-types.html) (added in [TypeScript 4.1](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-4-1.html)). This ensures that you don't accidentally pass `ms` values that it can't process.\n\nThis won't require you to do anything special in most situations, but you can also import the `StringValue` type from `ms` if you need to use it.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Using the exported type.\nfunction example(value: StringValue) {\n  ms(value);\n}\n\n// This function will only accept a string compatible with `ms`.\nexample('1 h');\n```\n\nIn this example, we use a [Type Assertion](https://www.typescriptlang.org/docs/handbook/2/everyday-types.html#type-assertions) to coerce a `string`.\n\n```ts\nimport ms, { StringValue } from 'ms';\n\n// Type assertion with the exported type.\nfunction example(value: string) {\n  try {\n    // A string could be \"wider\" than the values accepted by `ms`, so we assert\n    // that our `value` is a `StringValue`.\n    //\n    // It's important to note that this can be dangerous (see below).\n    ms(value as StringValue);\n  } catch (error: Error) {\n    // Handle any errors from invalid vaues.\n    console.error(error);\n  }\n}\n\n// This function will accept any string, which may result in a bug.\nexample('any value');\n```\n\nYou may also create a custom Template Literal Type.\n\n```ts\nimport ms from 'ms';\n\ntype OnlyDaysAndWeeks = `${number} ${'days' | 'weeks'}`;\n\n// Using a custom Template Literal Type.\nfunction example(value: OnlyDaysAndWeeks) {\n  // The type of `value` is narrower than the values `ms` accepts, which is\n  // safe to use without coercion.\n  ms(value);\n}\n\n// This function will accept \"# days\" or \"# weeks\" only.\nexample('5.2 days');\n```\n\n## Related Packages\n\n- [ms.macro](https://github.com/knpwrs/ms.macro) - Run `ms` as a macro at build-time.\n\n## Caught a Bug?\n\n1. [Fork](https://help.github.com/articles/fork-a-repo/) this repository to your own GitHub account and then [clone](https://help.github.com/articles/cloning-a-repository/) it to your local device\n2. Link the package to the global module directory: `npm link`\n3. Within the module you want to test your local development instance of ms, just link it to the dependencies: `npm link ms`. Instead of the default one from npm, Node.js will now use your clone of ms!\n\nAs always, you can run the tests using: `npm test`\n", "readmeFilename": "readme.md", "users": {"*********": true, "ash": true, "pid": true, "detj": true, "dodo": true, "l3au": true, "neo1": true, "rdcl": true, "usex": true, "aaron": true, "brend": true, "cshao": true, "dexfs": true, "eknkc": true, "panlw": true, "sedge": true, "silas": true, "subso": true, "yatsu": true, "yuler": true, "chrisx": true, "cr8tiv": true, "daizch": true, "dgmike": true, "dimd13": true, "gtopia": true, "h0ward": true, "hughsk": true, "lestad": true, "pandao": true, "rajiff": true, "shlomi": true, "tur-nr": true, "algonzo": true, "dac2205": true, "hearsid": true, "hitalos": true, "jerrywu": true, "spanser": true, "abhisekp": true, "anhulife": true, "awaterma": true, "bapinney": true, "crazyorr": true, "hayathuk": true, "hugovila": true, "huiyifyj": true, "losymear": true, "mcortesi": true, "nalindak": true, "robermac": true, "ssljivic": true, "xgheaven": true, "zuojiang": true, "ccastelli": true, "edwardxyt": true, "fgribreau": true, "jesusgoku": true, "jondotsoy": true, "largepuma": true, "larrychen": true, "miguhruiz": true, "mikestaub": true, "mjurincic": true, "mojaray2k": true, "myjustify": true, "roccomuso": true, "shushanfx": true, "snowdream": true, "stretchgz": true, "terrychan": true, "travis346": true, "xiechao06": true, "zoomyzoom": true, "coderaiser": true, "gerst20051": true, "isaacvitor": true, "lichangwei": true, "machinabio": true, "manikantag": true, "monkeyyy11": true, "princetoad": true, "raycharles": true, "rocket0191": true, "seangenabe": true, "shuoshubao": true, "xieranmaya": true, "yasinaydin": true, "ahsanshafiq": true, "cbetancourt": true, "flumpus-dev": true, "ganeshkbhat": true, "jondashkyle": true, "michalskuza": true, "nisimjoseph": true, "tunnckocore": true, "wangnan0610": true, "writeosahon": true, "xinwangwang": true, "ahmedelgabri": true, "andreaspizsa": true, "battlemidget": true, "dpjayasekara": true, "francisbrito": true, "hugojosefson": true, "zhenguo.zhao": true, "humantriangle": true, "intuitivcloud": true, "parkerproject": true, "bhaskarmelkani": true, "danielbankhead": true, "danieljameskay": true, "forbeslindesay": true, "kamikadze4game": true, "shanewholloway": true, "pensierinmusica": true}}