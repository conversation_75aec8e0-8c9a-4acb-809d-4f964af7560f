{"_id": "@babel/plugin-transform-modules-systemjs", "_rev": "142-f1d9861230653dd8d1005d9cd9662046", "name": "@babel/plugin-transform-modules-systemjs", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "533be976119f750a8d155e4607c86358076eca32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.4.tgz", "integrity": "sha512-PKmxvFt2OjAsITH24PqsPfB+D6xgpnXAx1OL4JKDxdWj/P+GB97DmQ1saDHs0kWsEAZSazhFLnlB2tX9ZSoi0Q==", "signatures": [{"sig": "MEYCIQCEDfWlkUyFg40rxKE9t/zbT1UzfWpAPaUhIY8w6H4wrAIhAJy5ZeqgI1udfhShV1LzioG1ftiJpoM+VgFn6/K/ptGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.4", "@babel/helper-hoist-variables": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.4.tgz_1509388539064_0.5707535815890878", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "769d6ef918c75184fd054a1daf591d81a4b2e0fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.5.tgz", "integrity": "sha512-b/ofn6vcR5F+XqOCjoGTCY6n7ILuDfTBl9b95bCXXrMPZY2ifRTWkSfkZo21m3xAd9bzpal2qb+aWBaFZQjyCA==", "signatures": [{"sig": "MEQCIGkUJPxgcMGYtSCP9L2st2XAjY7guZoJIgaE92rTXBLyAiBGiT9LUC3Y6SqHaAnnBECmvyHH6sScC9oxEwpH7Jhr6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.5", "@babel/helper-hoist-variables": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.5.tgz_1509397037548_0.7783535500057042", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "0f600f8f92a326e648e8f0fff8541a0f39c22860", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.31.tgz", "integrity": "sha512-B/MT8Y4cQ97BurBwDtUto56W11GxQy1sfdZGfkby2u3p94ZwmJXvU6pXAFXF19CJTcVqE91DLN/amm9DoftLAw==", "signatures": [{"sig": "MEYCIQCI3JWGgk7DUppCRS9J1SBMsn+m4L1JzlblBybl5UwaAgIhALRbEPmRfmdtDvRliTiIkiVgeXOfIoSgkigGwlPLmkVa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"@babel/template": "7.0.0-beta.31", "@babel/helper-hoist-variables": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.31.tgz_1509739443657_0.49310054699890316", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "8a2f14e0c5fbf6c0bb77ad4464c4d1f2e89c3697", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.32.tgz", "integrity": "sha512-JMGVoc03u1BHebJVuDSZ37ZmNAsxsRsVq3rJb7piLOM4k0TTdD0wyAPjNk8+/90kPaFFeP6qhpkUqgAed1Y2Jg==", "signatures": [{"sig": "MEQCIFh1bzty1eyABGIh8glSL6SZA7gqWj1L+7PBQ22wagzsAiA17b+NaXd2kh+gcZ0OV4o14c/lxvhSrFF2EGv4MLE4sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.32.tgz_1510493627055_0.5729620789643377", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "a1c4c013d69fb6659f452179e4d935b91af1b364", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.33.tgz", "integrity": "sha512-Wrb1XGhawm/Xb1sxVzyCdq80opRgd7kGHqMK1NvsrBvpkzGTKEV+JFtoiI/QnxJXgkt2SrjNnzfM7xJasfMT1Q==", "signatures": [{"sig": "MEQCICocb9wyRRmqMOLqIBlfbQHzuXQ8NAKNqYeZO6Cy26r5AiByR6r+wXY6OpA9fYXQ8kkcGoSkxlEPQNobBrF4KNRX6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.33.tgz_1512138541236_0.06988815101794899", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "2f9a5f9b4448b9d308e1755170eafbee18ab58eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.34.tgz", "integrity": "sha512-Z0xcWUq52XASvWgsqAMW0XRzxJkF5Ymyt5xuFaCWOyLgKS65tkD5U6sElfh/TSq+3x6ooDsWUeKWo8kJ7F76hw==", "signatures": [{"sig": "MEUCIGI5DNxFM47WGDSQIKpXIlLlv95zC4gIkc8+ate+g+BJAiEAg6CmgaYxuFTnd4R3OS2PFmoBmNBdudkMnUqTj0cWiiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.34.tgz_1512225598069_0.10024980688467622", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ef9704902423054fa6326f9d0596aa3a46e5be80", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.35.tgz", "integrity": "sha512-oHGov6ZhsAQeGW9Gpz7Otrau927948oi1khhKS7GmXcTalDpublxDWcji8FfcJXWt6tyuxVPweuxtTTjeUmtqg==", "signatures": [{"sig": "MEQCIHLEiARsSpHKRYSlhvGmhRz4PVlAbepngGGTxDVQvX/bAiAkY5RjMvmaikdhI5KqkOzLTwggg5OdXFCssZVY2qjTeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.35.tgz_1513288095708_0.20327564561739564", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c3ce365abbaf1b4726d09b1b458d57e338aa5d09", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.36.tgz", "integrity": "sha512-o+1uL5N3+9mcqj4xxOsb97QCBX9RbezSpf5GZp+nN7zbDuL4/tBFUZbR6RnU5dTw+y2xVGPsqAWH9wo0Hs5QhA==", "signatures": [{"sig": "MEQCIE4WSvIMwQ5JjzQ9UK9TB4ri1LLNNtZB91LyN87m/K/sAiA+sA049PXAfC4XzpgTrYBmf+DVKhJsCHCzzPFdB37HSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.36.tgz_1514228721690_0.4824665836058557", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "9459dd8651b1746332feb8cd48e76a60c1df6480", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.37.tgz", "integrity": "sha512-UR8zwgPSMTkW01wyYTgvuFCgS4Hwmonhk8lDz0XiltuuGljqtNElye8pTzB7kotzHW+PxaI01VluKrpzVpMnRg==", "signatures": [{"sig": "MEUCIQDdvl/fC5oX2SE4Z7MSxFU6YBvDy+6dbjR5XlQQqZFJVAIgVZEObBab2VcSkNNMDK/3Y7Zb7eW9gP6wfCy+U3NE+B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.37.tgz_1515427411358_0.8083731182850897", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "755a8561a3a347ea230970b4ab9ea8c3b444cc95", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.38.tgz", "integrity": "sha512-iS7AVaR46bdzx6mHFyc69P2EDt9Dk/U+q2a/hdWrKo6JKdyquYC1O2saaCVTR7/o9pYrgk/xdAKyZGNvpNrLaQ==", "signatures": [{"sig": "MEYCIQDCEQ/BeSKcggCteprmaWA4a1ZebxSu+yVl7h4OwtJcHgIhANLRrUltHLbQAVNSuQo2t47iIl8Ivj2/gwTHpg+SBPXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.5.1", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.38.tgz_1516206747644_0.19550029071979225", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "3ec4ba068077ac618e106273bb9636b2ee467406", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.39.tgz", "integrity": "sha512-Qm0PMsmmBq5IGwRQHrS5ho6TsFrjj+8yg0M9u+uz4PDep1E4pDU8NQxFXqfyBhDsKZ3+BkEoDMtxQ9SdaKU53A==", "signatures": [{"sig": "MEYCIQDFr0bmj8CnW3l5P+xu8Oh8fhDe8Dqw6DTL6lL5KllK6QIhANvlbJWgod66/DEoRmHu47DHf6WbNLKAGkxuL3Tzw2WJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs-7.0.0-beta.39.tgz_1517344075223_0.4451635326258838", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "808b372bdbe06a28bf7a3870d8e810bd7298227a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-q5IpFXNlzrK2ObpHkH5jzTCqRVzoNzmH8RoE8ZHQvLLiaIT346u8ynNv/BH1ltA49SPUPWyYpA+Z7OqCM4d3NA==", "signatures": [{"sig": "MEYCIQDybcVJIaX/WeJn08k/Ff4LcTn8hFHKJ9YS2eOwD89yjAIhAKhazGSjBJ8/kNFRhs5xB8lcPy93qu1fGWKKWBQg23NF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17017}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/helper-hoist-variables": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.40_1518453740251_0.9817911203831011", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f4159e05c1f82994be1e82e351a4edfe83354ac7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-goKILKk4KqZU67mtbto+5N8P/Siqocs8ns8pIgX37AuwfR86xSkkSzKup2uD1NoAELTFsQjWGCpEoShh58KsDA==", "signatures": [{"sig": "MEUCIQD5CjAn0Tj/4iPi9hlBXstzOhgWXT/kAy3X/O9z4RAH6QIgX/Y575RD1/mV+Lv71YSm+GEV10B6CMEdEzBNiNwvU7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17225}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41", "@babel/helper-hoist-variables": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.41_1521044790594_0.0882852821430753", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "424e25542b4d6ea6ea5f933df6ec9c345358b070", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-IEN3HVHpMPWrnI2EAEHg0248+Axkg2nrdspajefIyeZMlrN0dUt0gDgVsZtza5PK/1a+RZlpli7dDgcA7heTMg==", "signatures": [{"sig": "MEQCICGzJlPJSb8r1rNj6sXrpIL/ybdD4ZGPOqCTep2i/EQrAiBNV/sCMz9B78oFjT3q0/4uvlnaHFsm3hyIrpgYkL9Jyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17225}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42", "@babel/helper-hoist-variables": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.42_1521147103914_0.7219796125397275", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "58f5f7f126f1ea48996e7ae7236eb98bf970daca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-sQGc2tCLBD1wk4b4FHzz6BHYtb3jbtKg5gYXZp8kzoGJKd/ujPNj/LzITmJUj6KS+teuboqgo4rEVKmN0rKHmQ==", "signatures": [{"sig": "MEUCIBy+ijLLLvzUGDVgsXS9AaUuDXBaRRIDqjy0YDxS4T+jAiEAu/PTzuraQqgl9JB4Lmo74iiEjEwTbMnITL8uLQ6SsC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14398}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43", "@babel/helper-hoist-variables": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.43_1522687720128_0.502801631178565", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f27e97e592dd9739c8c5df478f1729bb4b63b386", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-sFY/F3a5WzscOsHqcEyKLxySZzpQLuz98ZBwzDOplpY7BlkJGWNLwwh98Z/F+EddvHJ54Q9WgSw/PMC9LUKXSQ==", "signatures": [{"sig": "MEQCIEMbQ0vqTWVMaWZBn4vZ0FWeuj+RFFwh/+KFFN1ByThQAiAa9gS3xXVJ9TF//NCeWLEVg4r+LH3+BlhroN7hd9gelg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17701}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44", "@babel/helper-hoist-variables": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.44_1522707620545_0.587103387260077", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "48e8cb68e396342d59a9e97c0a51f7b181492771", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-+ki50329yZDcG6fz3hp94Be439PSDQVZf4+K5gekwY7qpkfE7K48nNbPHX6FW4N+jxEKKy3ak2lorQQ1WLejEA==", "signatures": [{"sig": "MEUCIFWSc15TD5AlZt4m2axDzXlQWfIfLmwJqvwln2NfWRxWAiEAh4CcvVZXQlfqkKzI1luDJelnEt1xhfUBz/wOzLBvTN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2SCRA9TVsSAnZWagAA+noP/jtcb59qi8yVzQzo7WL+\noJnjMlYnpoovl6Oia2lmy/8sL5G55I+6QvBdvewf5vKY7znKzsuwONG3+8vO\nJKn5CK556g8DwURUO2XTHT5dRkvJgdR9okmhr06MWkg+FNQmiXDnN2QiPvc1\nds2jg/khDfBmUxDxM7Bt/nscxUpo5C2dIhmD/AKbAuAYGERnvsfyOUPTWnJc\n6L/486RLBcjwfAAWDa/QtyXmHT2EWA0Wm4OpHq1F9RVBv0QZCIb9RhBJO/22\nQuQFMdz4ieo8Ur9kuslSpbeUYsP3A80qT2twvJdCIjwwnSmaq4omt5nDgajC\nsLxgZFS+8I3bblspCgFKkFhRJGpLzr16cMyGQB7lrqOUNebX1jTstAarczru\nwoQhWTifPXBUqR6lIGKxWb6zBgNKhoJTocJzhfAr0Ywof8FyEwlIK8csClIE\n9elQihrt6tOLAPDBzqWvuwQT+XLiygnxKBJoJB+dusVb54hnteu/xnDOBH0z\n1VnnA3C/WyjRNhi6s2SZkGkAF31eVxKVJ0pJM73AIw+0GbIUMKQqGFXcf8vY\nBthWbWNlH3K9Yxs2FJ6xAg+Wa7r8DqnOCmzgzCrf5GNUVlugSFZGeSCNXqRF\n7K1PX8uCR81mxuUO/IXNnm12Yx6fyW2w3U9aRcNW7Z/ZGyiW3GSupddQh59T\nBp3N\r\n=B6uZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45", "@babel/helper-hoist-variables": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.45_1524448658532_0.9902766011646476", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "313e13e8edccaae6c645e3798a043521cf73df04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-dCIuCGaE4UyM6cYC/a2veO0vKT/iSjBZ6ux4v/LePXA/N6v9648nwE6CRmCK1/LtRw+eU0sCo+c0TPeALCszYA==", "signatures": [{"sig": "MEYCIQDora7LU9rwQbQj5xQvQWUkbzGCmarWK+DIBjFGcXe2PgIhAIZjvX+5sHe5gfjuOe7dANlt6nDVCNnC6cSByo3u7rZV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHACRA9TVsSAnZWagAAvDgP/0h2B/hip5XHuua7wdD9\nOnrbfnAXaIXS73rMkyrbomSbXRVlVP4ri3MTESz/UEqJ6mALGnTmHyhdwC05\nN/+vf6D/uYBtZ43AlxoA6CCMKDnRR5tBkusMHIFA1fwkDywkYygEAYV0RSSs\n3QlrLnyIAUD423BAhGcoiniLBXWCzEfMMXF9gjZhBCU0+VOMKnbsFj7SqvY1\nbZQdaIBHYvOnkz5ywngTTzClvRhuKI+0ZRXophU3HTREtyreXJQIlFDDW3AQ\n3UzbJwVGEKIWAQ0s6InceLHAfI1Zmz7imDXxgRX3rGn61SXAoOAqtMrb4lgI\nZt9ESBg8bjv4bZMy2AMheGQX8RxYQ1hphyvkhajn/Z/X63WSwBoIA0e8bwLt\n9z0uh5xbn08FSYqQ/Vv6aQz/qssz/x6U1yNoTsvRklnA+wK61DbiXZnIPCsH\nEP7uTtnKWMBPmDaJiuFqzVXtTwTGGufhkBPleG6oHBeki1o7IyCGEVbwLrJf\navcqhAednsLHnoXUXqgIP8LQAj5TLjEylvZyNgxkmUqkwBhE3PRe9KcRT53c\nlMGKYa2mIAQLwFtM6LlBAQGX9YbK2bt7iPP/MyCfcTECraLWovk3q0pJHmEB\nohY4WhmohAMquIo06FKJCMBOqZLAu+5wfwaG/VafcDJNyrhULmby+NGTUyyr\nEH42\r\n=1IGa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46", "@babel/helper-hoist-variables": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.46_1524457920032_0.5920753484700156", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8514dbcdfca3345abd690059e7e8544e16ecbf05", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-bMQy3/jEZRpoUg7RdOouphBO8+7Sfjl7XrO84PtgBx4ck+ZPc4xOlBQyr2rkmsJNmmGLi42nnMI1cZZJT3LVnQ==", "signatures": [{"sig": "MEYCIQDXIzivPfyMjUFCDI/fd54duSl9EId7b9gSYk423QAxLAIhAIhqM1QIYAhpUc8KFbnn3VnraMbeQLlDCzXlFPeo2VRL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVTCRA9TVsSAnZWagAAH84P/RhSfIV8U925Nku5T0Rn\nx9Q1KdjOiAi5nKIZsB/WmMDWUbQDedSKa8hLaIhRm8ABEOPv1W2nNzbBLiWr\n+CCU5TX31fYn5UAo7YUn4idyHKxfOoh6hzAZZUz2X05LXiMbv7JYSVWah4/d\ncdRLvaRCSNDsokItixGeHjNayMHG15pxhoP/Aw4uVypv1s+3w73mxUxw5xph\n+x25f/45aUWQ0eCoLiB3yx0tuCeBd8WRU3hatwgmJxkSo9JSUTSGQ9KvXa7f\nebc1zR3XWy5mSUrsbb8nXXUQy+46WIIHPqDVnnKbEfMH83UIr94gsWVau8/Y\nHh3/xwWKHdA8AybSPGaUtlC9gvtSN3w/LFiBTPstA8bzaiPxcAeWXoTpuxLZ\nKDVpmIhmYqCjspbenNUi+PnmCrB2LjEafDcamOKcsG06BwSFChQK7kUCM3pB\ns9/HyOuzQAzmIv2D08rp6umAnP+0Y0mzUNyDY37FPuiKrVleTTX559HrwujA\nXbg3LGawY1b8WzKmeXMKc/jMBtWlD3q/Vj05AznGM+Jj8LwV8glAJB88TBiS\nPQBiVta6R8m8H7mW/YEANJv99eYuWiVjch6i1xIoJXnlYVLwqZMyGrPqGiqT\nYXQ/6pgJPveH3hsi/wSDekK+iP1QtmI3vF1EWNmpFAkMo/TX1YvfklEo/yjV\nu4He\r\n=xMKn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47", "@babel/helper-hoist-variables": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.47_1526342995373_0.817803861530773", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7cb42cde7567aa4082da04326c7811542b2af8eb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-A+KCLWb8kNWdL9srvzJT/CKCBGKrmMKBhJQxFfFmYoFoqoD+y8paM4spRaTd2HvwlMXBgNKy3WI5O100Cy6+QA==", "signatures": [{"sig": "MEUCIEFL1kIfu55W1ynKcVyrxz00dFEd9BVLYhH1fgsDMLKzAiEAgi3TXTaPbFmfSEIEsugZKsI358musZojmz6lL+2NCNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFLCRA9TVsSAnZWagAAUZEP/0vWEuQfL5rjz5C6IxAa\nYOmgx7+he21QTOJCCv1Qj/4hdaixhnfEjMg5EFZi7dF7dViW4vkpluN8rGoa\nb1jaw5Jyleo6iZUfo9BeGmbtXuYaFyxA0/4fVKK2QTf+aA20M4DkAao72QgH\nXF1StcY7T/LTjtQOIQPMgJ9bIX7sXf/VshxOcFoyQLdDpYcWIqLpAOg2d0PF\n5R5RgyDg1BykBGv87ZGk7R+nED6x6c75xGY+DtbLBBmfJvwPIEqI27y1hxjx\naGylep63F1Ws9XwI75PjCfvridaStaBtdkx//HOm18oOd2VyQgPNcsalR5Pc\nkEgB1i7tqIAaqNeiFAHCBVddwSFRXmSgITxDJF6iHCRCEBFkjjDvhWqCjcS0\nV5wjnYRiFgV/XtX432V/T6DMHvskHh5NewsPYB06Gn0nH63ZnCM7E4slFiyA\nCs7ihMu50uShZYoqvlr4S4f5wO8L3+ulSbnf6oRM53UWvM744Oq3iyRpIdWM\n9BFYAfHwJxCNoKF+Oq7sl3DzvgR2O4EJnV1knoJNbH0nzY/QGInGRxTu4pat\nE+hlccxQzaMegWN+V8oe71/xwpapQd6236AQ+RP6WLdEHN1MoJujMWifCnfM\ndiImfUiHTcpajflTgMHXdv1/3ryOds6j3/kFRk027EO13pgF0hgIKqQpsh2R\nTOI0\r\n=ZmQx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "5.6.0", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48", "@babel/helper-hoist-variables": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.48_1527189834716_0.8361662529735354", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "68225a3ae1312771bc5a36f71ff10d02c1243d9f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-zCenb8jLPbjGIPRWJWunmFQssWmExS5YGrrQDrcelxCXYKuauktjJlfPAaydCeGgGy0a4OuBMHXReUNKr+Q6Lw==", "signatures": [{"sig": "MEUCIApZRd62GNYg1S/45O0bwSHkuo+lQnpLu3MvYilseSsjAiEAvyTfWM3+V0sE4DHihsEJTV8HRHxArYxeyXhU3ZLeQ6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPDCRA9TVsSAnZWagAAUkMP/i6ajzFqO1uoxA8vx/5f\nGzCJ/6twuU1iGeG3cxyKtH29T9/wJOYAu0h+xMUFJ8906lpkBcAbFD43f8Pp\nKNj1atkvXPfyJCnoX9JRzGFVAJ4MCzxJGt+SnMaPdc6B2z5y76+oZfYtCmMD\noay1njm5Gsh+BNGbIwaiE4S48B7Y+kd7tbA5FhksWqlz0nBtg1Gg1mgQJM/B\nPc09LPbaPHirtvVPSLNHdKRmtxMtlZmHpNBeFrzk6L+Drs9C51lvJWo7oZ2t\n2Pc5mxMPydRt+ZOCUzwgInVhhWGHVSNBGC5Pl2yttcJKt6JHrwUWCiMr9Mm0\nvCRwlQysdMPAC6mjlr51wkgi8hOnEkOmRFW5yjHCdqEkGaI+6bm4CKr6KArw\n0/D40xcsyIy3FnEIHFFbZ99rTikU01apdQ0PilJCxzUY4S6ERLxkvg5+jsRV\n3hgH5kpCFgxBJi+Br7fHvczF9gyltUPwHodMTpYfXvaAhdy+BgfCN2vrdrmE\nGmBddnayQpacgI14+kAuVGeWPdxzDmV6JQ1fZQcw/E7OHtUke2z6BgeHc0jE\nnn2w5vLVqUfjsOOE+sPh44akioYq9oKuaqjw5BfaOZa6J0TdUlhLVvefPYRa\nZeQWTgovsYPL7ltvULjWFC1YRxrHYPQ3qb9ynIujmxDnK5oTfedLnlSRKQmk\nnqnv\r\n=kUNU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "68225a3ae1312771bc5a36f71ff10d02c1243d9f", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "3.10.10", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49", "@babel/helper-hoist-variables": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.49_1527264195453_0.41343526900458594", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "87c5bdee1c50e0588fb552e4e5649e5a6dbf5201", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-88jjvUWD46H0WHJunVzAsOUcxPOlZnO1CboHV2Nf1XcZDeCgaexdvYXFFG0ilJGk3OF54OyxHF2fNOrsCWRIog==", "signatures": [{"sig": "MEQCIBJhXXmuA0c8Us5XCjGIbBS2LRFfWyTYqD3Yg/zQIZ7ZAiBzSw+DW2ugjzfDWaNk6L38ft9Ohsl4u8AdE/zzMcIGGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15742}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50", "@babel/helper-hoist-variables": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.50_1528832858307_0.19380664348838605", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6e7fc4ad9421b725cddf37cc924eaf777f228c27", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-VmU5hszqxs8Ic10/giNo6VdR31NSZUS+bo2kyheyx/j++7ZfOp/RARMrrlbYKdOanFCSfeI/gVN2UIMxQMdDfQ==", "signatures": [{"sig": "MEQCIC/USKTsjai3mH00+KZWdS1gagzKZlci3Mr0hRrkX5dSAiAt9mdUd1VhjBvzLp0lOi5txByThclJZvMcy5aPFjcbeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15756}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51", "@babel/helper-hoist-variables": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.51_1528838416015_0.5663464561706937", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "38223827dc79486dfdf125ab64886ed3780626d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-JV7yFKpdEarbyySA908S3mNuxS2CljSsrlIaXK75r5FLm8vrNLeZVsI6LfKn5LUn98qChPfC3emEzl9rjFlJ4g==", "signatures": [{"sig": "MEYCIQClgo2P2NyXPS23T33TI0d2UE+UerkGPqcXv2EjiLE9OgIhAOykOXukPKHkManqZIUX71Y7NcleuxgToSf+247V4BHu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15755}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52", "@babel/helper-hoist-variables": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.52_1530838776324_0.8451855433119837", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b80fcd9c15972dc6823214f5248527860bbf058e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-ZCJZ18qEd7zf9F7Caph/z4TUARMlZiC5aI67wzOSeNpLhqSOrno5qxN7uukOuYvpsk3ji04S4tJOqHSuG+NUTA==", "signatures": [{"sig": "MEYCIQDz7ZUYVXh2+2XSKsch0RqmE3MsetPkMRdVZ2Xk72jhtQIhAJTuLY296NA5sDtw6Qgg0g5+iv8qjYPTv4UfuMWS5LCO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15755}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53", "@babel/helper-hoist-variables": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.53_1531316435286_0.24434543076189263", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0923f012ac252e037467245ff27f8954f4ce6803", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-5K7BY67TxD06l91aKBXFjHFuhsG447sVffcpgdZ6L6ev0pK5N7W05KKkg4PWU5St9HTiRUR8vcJEL/7zo8L9sQ==", "signatures": [{"sig": "MEYCIQDdoM7ud0vJzojxqQB1/FL9iX/lTg2afrwSJLTznwjyFwIhAKFvVaj0pF2y0NxyqeetQVlSRcpmhrI3iECRCXrrAxvG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15755}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54", "@babel/helper-hoist-variables": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.54_1531764017451_0.8606189194709408", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9e36a7d48c9137781484c5442da426873289594e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-H1ypfGmFhkBedWlFwF7L9tTRDh+20fIllo7hmvPMwO7kOdoJBo83wu4pbtUAGYa4OFNhB94kbdoEa3SnFmUh9A==", "signatures": [{"sig": "MEYCIQDJxfrlHirFxcQEgNWb9DXyJkA5qxhi2WVEoJxWZ2icmQIhAJuNGJIed6hhXIwh8Gf459ler10uPMS2VwFTdlI7AsSB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15755}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55", "@babel/helper-hoist-variables": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.55_1532815661172_0.792681004815103", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7aecc2a886a151aabfe86e5c01811f5cf9c27d04", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-tr2KAI4jhBfZLkSIHU4vlS6I1RmFYfMxM49ese+iPryvj1aH7x1VSz2+DEA/Xr+gM4CTXpWk0Xf2r2u260lPEA==", "signatures": [{"sig": "MEUCIQDBQTBmLpPZGmWF9VKYYBSu/vT9e855MPLa6VUtDt+kIwIgSBFJcw+vKeSVyvJ4rcUbM252A1w4IoHApLV9iRwG2Vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPw+CRA9TVsSAnZWagAAe0cQAIR2eRe32sAL/RPqf4or\nwPZmlLoAXl+DCBbGffJYElO0Q+7/CbkyeZ6/6V0Hala13CiOc3m3lqdE47Xh\n8V4vpbUTZSeY0SSuaeFUgT3b7t83Fj/nJPPZ0BTr3jAyEe3ZzmARb/Em4vwX\n1Jq6tSL7Col5qNDf50FsH55MCL+yX06Ftq7Qbk/xLC8JsgoR3YykbA0dtDeG\n7KDcWINMieKaZUHCftc+OvcaK/y0fGCjBPKWZm1k4sd5wyOvLNOXZcHbjbGC\nguzetKB/d/q1JU+B3A/fLCU2CP03Q6kOxYnTF3/L3ggPBcK7rrAnKnsVaz/E\nUXgCllVbbOx3EPLcyRnFe1GxQuOCDjHyS1/fp+HB0mt7mzs2+1rV5a2SX+FC\nnZRjHhZsQO+d4oxwi41KVNkg/61Pwvh0pBvIts9T9VHWfu6AUHogDslzc+Ng\nIQrfEUCDW3F1Sa0b68X+W8i1Qo0/qzuuNh7nQiE0gIDAUly32Hh9oKnLukUM\nDVOnHeG2RNWMNSKZt2LZB0WQIZXdG4z89xWtb8Z0gCh3mw1rshy5j16A/ZFb\n87jo9PMrFh9R65kpeiLIX2MEMsDqI7g5NTZvX/i1nwk4Wadgb7XW1lva5ijm\nF3mTH4ypBU2fHoFm4CdfS6a5zYTS5YrAq6T0b/MTLsmDc1NSOO/vQBwhSXUy\nw5bZ\r\n=Rsxw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56", "@babel/helper-hoist-variables": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-beta.56_1533344830419_0.9781009161472181", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b6c4894afd071780a7204e101535235c3f90fa8f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-R64U2qklRZUHjAux1a6h8gzyTMruQ36VKoqYCgqpm8u/N646QbKVuVVDsZ5SECmfAGf2qtv7ydUUAvV6UWHwQA==", "signatures": [{"sig": "MEYCIQCUvtRWyIquTyFakFi1FATD5GbA1kZghcvtdHHOd9iLdQIhANb9hpN1jNLLwk2CKr3xRBCe4+6qHOM7myrXilvrGxHI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTNCRA9TVsSAnZWagAA1KgP/0D2l1RYEKbmlCorhHSR\nsqlrP9NOXDXhP2fKtBvJ35UG3JEhXjDhdL2TmPHotjoMFQsP5wjaWbDhttRy\n32Zel3I6dAaKsThMSW4z1BxLA6LpdNc+6cGW7TkMvj7aXY1pnAnSqqLxGz7J\nIn9EIv+QmgxOx0x2bZ3FTHtcH6nA6IEK5Rvc9zQd/QbgpNIcyuXia1b2Hn30\nsJiQHYsFwaYaTlo0tgkvPLDa1kP+GXIJUVo676nRMLykOBvwASTYfxBcPs1f\nCg5Q6KeaD14zvKQzsyYngoFc680xgx3jFj6caFd4AvqZ9AdiSItGqO7AdGb3\nBISD3Y7PInLwyvXMZbNWjT/8Wm8pxgyuhE0rTiTIUMOiaKNAILdhOlTDsNUH\nWoA6DgUty/lMOFkTS6+kIc9bzAGO5AVe2fgx4+pkEIGikXZgWdLz7QiTUfVi\n6sjm/EWmcAv2ZB1Om8PJVt7z8TgnymP4FoivDJbNaOrNNr4+DC49tRQZ0VrA\n3hQuXfGou/E2Xr5zJrPfnw6bQjpEu0GSpbw4GfbaqWFg43H1WTv3v2nESMA5\nPfwfkDOcvrDYACm3nmY9+2j31lDxFsw1sA60f2EyW2hP7+PtsPl1pRCMKDVU\nriNkqvA62JrA1aSz83mF2qhdWB6sSfZP8xPLO1g5VLu81YDY3uBlGIypMKE4\ncyjy\r\n=znn0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0", "@babel/helper-hoist-variables": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0", "@babel/plugin-syntax-dynamic-import": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-rc.0_1533830348977_0.1690391611555555", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6aca100a57c49e2622f29f177a3e088cc50ecd2e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-denli1X4utH2boaedaCv3uDmrmBH0CMioIswTxViNY4M8nti3DV1m7wfKE4kDYq8UrIILLYwxxOsAvGxOS9/Ug==", "signatures": [{"sig": "MEQCIC444kxpFkL9q1+g6B9ZkMunRqTOUhta5HDx2FsODox+AiAsR07Lz/7GsNjhHPgOGFJ6ND87Fm9Qk+RXc92TRW4Qiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9VCRA9TVsSAnZWagAA83IQAI79teJ4RpHeM0P/vPHL\nNR/gfFOwqWFzPe04alrj/TGcy1VjsbW96aWlvkPJ1upnyCsXbDlrkzmbSiTe\nHDRoWi4tutCCyncXOmAOL1uclvbmaBqSXXANtpFnlLDNL9pAPpNtcQbr/s9U\nqQaA+crcJYonjTrCYcd4KvKeaV73dYmnUUqXLX3+2JyXsSD3S+z+LvteezG0\nmEV1RV59RYoTj/iy6+jOaWGjemYJ4efHzsbe19nmBwVPqzxja566YWEp7LI4\nOTi3ugVVIPCcp1d80PdgmDeSo3BPGGBvsKvGCnBfMQXi+pZHdZ1h1d7m+Vcg\nb34WCpaoNVZArn59/zTIrr+XLtTra1DMMw25x6dsHoSwMyhsdw24UizQJfJo\n0GDre0rxosY+XBv/CpEI72L8zUZdpVnqx6WUIU07EhOTb3IxLMLugteoCo4h\nQGybogFFbzpeSXcDfIMVOoiSvLDkt2MuQ6fSkM6Nw22agpbChorOcnojbBd/\nW2O11C5KNdHWCGRohv5n0b6MKrbAm73fStSXHkURwy5Gk/WZ/FdBrnpd+mK0\n3taG7exh+N3NoqrOYvE/jSMP+MTp1AX5gb3iQdL8gb9pVRkXLNC/p9SmWsuI\nlOAo/G5BFKy2LBhrhO87ivWDUUbdwcy8KeTfy0pUXSOQQYSJOTAK1YI5kf47\nIq41\r\n=LYnk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1", "@babel/helper-hoist-variables": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1", "@babel/plugin-syntax-dynamic-import": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-rc.1_1533845332800_0.46839622790117663", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ced5ac5556f0e6068b1c5d600bff2e68004038ee", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-uIDk/4+OzSSrW9whicWupSfcfDixWUxppUCkPEHBA3UKMNSo7ageneh+35rWKDvZh3f+DrgJxbDfyolFBsr7kg==", "signatures": [{"sig": "MEUCIGPPbKKAeSXxjnnVueQpACmXfXqpHvIXtQ0rYdQCYFg9AiEAtkxaaGM9c1qa4VtH1rEVToLbmVvkEZp6fDbV0qqpyC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcPCRA9TVsSAnZWagAAcyAP/AnvWiKGFeH9qaKFjuRW\nCKZZtNMKwUR+5dCCtFZJXD+UxsGnyS/Yqj4GntMmRYcHPw7P8sN/tZmnialy\nvXPjukKeVZpnqXXHBuoZMWWVkypmi1Nn87ZdW8n6RyaqbtY6KOL6wyy6UWpG\nMVCsebkdslOLmCV0401J7gLLQJXV6oqR9SNd/waVUb9WglA+JmhdiypqMkjZ\nU6xy+Fb/ihYZx2ZZUcIGGobMSOqLuAJFcjwIiRPnyvDbCvqlFIMgpAdmEaxp\nCBOtdYPdhn8P+a1J5x3E5zbE/SZjdXQJvHb1XhHTXaCfNrwjZ+Z7yoEGvnpt\n1kOTJtUjkNqSCJFHp1lbn27B0IfDoeaGFTkTklfp+pDkCGRyxYgokCnC8xhl\n/PJb2poDKp+owPB02ABznHHj/nyKkM+rT/5p10rqmBQe83yv7uh7JW3E3/DR\n+8eAASNii+FHCKSIS2/xAYg4XJrYzNKw2GiCDvZq05xUBDLrfosct/SVRpM1\nvQBCmz6AajN7XJcPCLKVSq2jH2ADGqW6h3TYIsAq2xVQU3GJgWMtDTemWHfT\nIZ+qxuJjk0IVpn+PCf5+uUOOIafhNUcUc/IsZjvy4omzHWh7Brf08NWV2NBT\nbAxPx8kO7kJZ5a20KfvEBeopx6i90XUCAclylP0oMJth9Tn1ATpeUQ1WPi2c\n/RvR\r\n=ZTUm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2", "@babel/helper-hoist-variables": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2", "@babel/plugin-syntax-dynamic-import": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-rc.2_1534879502860_0.7134449422047684", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "36a9ca740fcf9989e02f0341a02b438cd8e9528e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-v0McTNJXpvzJyWNLZ+RBnvZ1HmFZm15B2nbrHHvWsVBq8w4+e+WtqO+Oht1YMP97dJlZyaATeELqkAe3bVAXVg==", "signatures": [{"sig": "MEYCIQCFN0W4ylktbPJe9PWSuZLd76211SnSpXFK6H+Sw8lotgIhAPylsxASE49x2Gf3cmp72j5H6cJLrcz9v9JuOAivTfVg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEmuCRA9TVsSAnZWagAAZ5cP/2X05lG+tLIDuPpRYNf+\nyiIDdCL45EwAfBewA3w9ouFrUGuKTFD6F1aRl5qrKSmJYFFlnBad3HJxrWuV\nB2JXd5UZMLdm94ASvPJ2GmKewzxoLwidL3XEUoFNZiIeEk5hvwpryFV499Y2\nmd5r/R0PHAxqOmBjnXjRxXQGVc61N0NtARA5RS9AB0ADsjieZcfFONo+prXL\nFK+ANbwNhsCUwIXo7CGvrX7sKxgpBOz5W6qcVnFnmeaG0qzpfIgn+0KkOnIc\nj8MwXEPpUsfVZd/s/SwawtW+qHaL31lFXMNdYsgYVJKD1zhSB8qMw2K4OzDX\n9Nr3fzcjMNqsiNB0Ipp05fN/7ffETw0CAv3eEheohQbD4B4yfl7tnRx56PTY\nbPBTiaaXvhj6yhALaeFEGG1NWVFhWv2u1ZFxJjP7ebJJc7XFZUztgMpDP7LH\ngf1KAbLCe7cW2ZJ+uEEODM3iPvsGATSgoXonII1NNxLo0y6/f9r/WkQ5tyI6\nrbCbjktUcv7HGOXo5Ba8g1ce5/jGHgF2IpLmHMuFSB6YJ2UqwxzQ2G+D61aU\nhG7gfbbY8RDTi6TmXXVNrS/JlNKV/jKZPoYJIybYgapQMUEV1bFdyz1AQj+H\nPuV1aQu5zQD5rVhB7BgrtRiecGQI7gbeww9QSscvOuym9RVRHYzyX5eFT9Vb\nK0/h\r\n=gc5k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3", "@babel/helper-hoist-variables": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3", "@babel/plugin-syntax-dynamic-import": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-rc.3_1535134125620_0.4820935825567867", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0da1c3f3d2f52dda2d6b2c6e62707691514c5ffc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-VQ8PMRNbD5cFLc4HWKjlP/UhW57S99oLarMoyHC6NYxzdNMylBQ4u4lgtG/eMRDutAlNVs2YSmKZCCA29TPIgg==", "signatures": [{"sig": "MEYCIQCGV1El0OM+ZVONwWAdPNLfDvQJGUdUccFVqRDYt0sJtgIhAOSv2vSuUSCKyxCtBLbQlz2i0Hq/5OI4roFA51lVdtTF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCqXCRA9TVsSAnZWagAAe8QP+wTjN+YYTfwGQw8FYitf\nthU7bXD+jmA82txE5WkMykwUO6XYHuNPqfzYsvSgkyM/D7xK9E9CRRiBXV/t\nHTjX0RKjpupAoGHQ6o9xcThuX1/2YtPimOe8j8SRwoJbMzNznKqe2nnqqtbB\n6fBvyio3ojDtM0L6DrDvGHc6byEnUcQzuJeeu9e/F5K/doJ1k2Nol5mR6/L0\ndueTRIyQcjCjFtHjfOHsj4iQKckgWUMN/LF7llE46mhh6/jg46YkenzhfPoF\nP2isAvtbr9jcCeSC6wiGhnEed25i1KdQ1y32JiiPF48+5XEEFlK8WKPxWO9J\nNJp0a4BLJ/qPl63be+8FhubHpc5FT/etbvaHmai1tIxRtIMIDJQx0waJftuD\nEbrz7QZ7i8u52xV2j7BqqrB9odluIRQteKaVffy+8Aj2CK7G2Ax6KZ3fIvKk\n68AhklJRkoh2TaUprSvwMonqOasgeivTPdcdg80p/H2YFmwO3+o0ZKZ5SaXN\nI8LXJM30lPqaK1cWfy7AP+rHchasb35e7W1VdxoUtUouxUDaSOjRlfHti+ea\nT1exINM38WobgynpR4D1etCRGirdDo0yvacit0yK6Tj7PbkqcNCeAU0VbfYU\ntZhvKiwsLg2UK7OqvrlxYlKQrXABI8uBw+4J2c2kjCADgSO64tquQurq9PL0\nDCAq\r\n=gFcR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4", "@babel/helper-hoist-variables": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4", "@babel/plugin-syntax-dynamic-import": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0-rc.4_1535388309883_0.2995297724715973", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8873d876d4fee23209decc4d1feab8f198cf2df4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-8EDKMAsitLkiF/D4Zhe9CHEE2XLh4bfLbb9/Zf3FgXYQOZyZYyg7EAel/aT2A7bHv62jwHf09q2KU/oEexr83g==", "signatures": [{"sig": "MEUCIQDV+v8ktJVS2bURPMrN9UBJdWzbIDLADvSZXbfi/wMypQIgc9Enh4Va1SXNFymCnlH/Mp9Nzy+1t/l1egbhroll/IY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCjCRA9TVsSAnZWagAA7PsP/Avw1LBO3MS9cZQcSoUU\nDpnrJ4//StiGP1EzxYnFvB8bJ7vufQbe1GmhPtDMl7MlG3npNyKsh8pHfddT\nVVbZxwmpU2XAAYg2RxgJ16+uG6qKio1tGZbOsrXZWU8Gc2Z9jnW49c6I9V29\n8b0PW+0s+uSRuuEckyngAmBgS5bTwprnTgg2+sQ2Q36YIPHmwUydWjVVNlJY\np8jBE8OmQuWL6Hmq5O+M9T+Zpf8uITAsG5mGmEpvXiBcHavh4osHogNM1BPc\nG06O7WUYuxHj8gZPtjOFvM2eVCKVzTOVOwqSET7r7k4tsK0271HnsL5SBx2q\n+bKLujNheAFm5tg1gsh6FKjmJY/TYhqrrqSmjnFy2sdqGxz2F00vLVYTukN6\nD7m3zUZcGHZOlOUtjQbiRPuSlRTUG0hMaMQsSIiVwh9YeLRGkyovVvl3mgjP\no7Xw82IQDZqA45OvNd/yHSyLJTzm++wK44BW0Aeglf487a/Ujh0p4hNHiMYl\nvp4DPJF5m4HJBKW2fuL/I35mqaHS1WSkspZ7SSXl808LZXIctEWg64VpXFzL\nIsQB5JgJWUJLreygv1zmtlyt16G0pJArtkPVmPV6bjH1HLeX0R7IGQ+9dmjJ\no5f+jin3UAkkuYPr8OgSiKA8VAB0OWEuOnyGcOOq3DPUSeukQIhtM4U1Muqi\nce+M\r\n=LQtt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.0.0_1535406242374_0.46066457588825216", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.1.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.1.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2119a3e3db612fd74a19d88652efbfe9613a5db0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.1.3.tgz", "fileCount": 6, "integrity": "sha512-PvTxgjxQAq4pvVUZF3mD5gEtVDuId8NtWkJsZLEJZMZAW3TvgQl1pmydLLN1bM8huHFVVU43lf0uvjQj9FRkKw==", "signatures": [{"sig": "MEUCIQDS/sJDdb3jFfvHvDRoBiEkuBkjqsXhK4Vac+p5m2Z1pQIgWfotXV7SDlItAPgC2ZY0MVZfrW/lyjeMtpa/Uu3z75c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv3G2CRA9TVsSAnZWagAAVUkP/3jrkaIpIQiAKyp7gu6O\n+jwahL6rRXif6rQ96ApmaQOZXJxF/XqmbHitzE5ecK894iID8hpulINwZyJY\nk4FthXmJqA1tCShYOllLTM8NfHcEH7fW8+JcmXOoXHUbgQjdK+i3UDIUFajm\nS7hfTxnL+6Ioo8qcTf5AP8FWF+VKptUSk3wQrKGUfKX4G3a/NYwwQnahv5tA\n5BD2EtRIEWj33g+S0xXYNMXD+yKG2LWkKMNws/GySdT0Pu1aYkinyYGucO26\nFvkMl99g5GxqAFC4VwM2G8j08bgVMB8IAYT4ZFvANBdqpV2WrgBvNEX8+u5H\n27HmubwPkuyKTJcRUy5jnxCKwsRHDZYAAAlUxaZ19RYaugRGQK9Snjd0PW4S\nXa8+R26JDwWV6G3vpzZL+ljCvM1msccxEl5fS7SvoJD0faA0N4rkBgDIgE11\nS0yG9d/hijPGp2CotkRmYTX1yJ4MqRgWBpiY9/QEfBYnlwcPVZTV95M+hjMp\nruOpSc09v+R1186uWmmrAckVT4XkwcgvHaeiNz2O2vOoBN4pSaZvtepJutZi\n+Fg55Qd8GIwp0tUPU4Vs5tH6f+0AXO4ljBZ2VU7piSluUkAXC/Gb4ZqEQixa\ntEpKWh7cgq5rJd7+k7eIExW7J/Il8Y8Ucvsb1YsZlcIkA7J1V7OtIXYvlrzQ\nLSKN\r\n=njEu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.1.3_1539273141257_0.21668491411725332", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "912bfe9e5ff982924c81d0937c92d24994bb9068", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-aYJwpAhoK9a+1+O625WIjvMY11wkB/ok0WClVwmeo3mCjcNRjt+/8gHWrB5i+00mUju0gWsBkQnPpdvQ7PImmQ==", "signatures": [{"sig": "MEYCIQCcVOKDeRdqGk/bdgQNuplxgvNfEinTHswJz5IJzHjNPAIhALrjelMKMVvp6cqAGGVNA3HXOUjRtR3FERWDdYM760vC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2LCRA9TVsSAnZWagAA1qMP/0/Pw93ptyxIjYJF81vn\nJ+ZtS+ggqRBTIbsQFTwWVLrDZIyyxRDhZ6C/1s2ShJOAzU/Ry7ztyTElI7jd\nuYmUpV8C6Q96mLN08s2mAch9rJM/9xBprOF+4zUtnrliVgufL0lgdxwdTTe3\ngSqWH1yj8dEY6fdY6MN8lCLvD+Rd5nzelaDvnI9sDw2BNy3iegVf8yHoeDuK\n1NjQTC5LfInR1FJPjJ2m1zZnGOnKrxAtQIKnzpiFRvT5/KtcDthJRxWeRiyS\nhHKk5b3tH0ATfhA984bsKHL8+lqYk+DrpfxqOmetzG167pdHUP0DlAtyMJ6y\n55/rDc+sPnQLckCniaYNbK5DDOs4i7+M1up+lmhcbldZD1/ZCVFlxXQabqVo\njr7xisdSseKnjHEq71uMIZIP18rffu4uJNggKjxcwS7KV9fovQygdlhIqBXQ\n7+DQhQYD/ht8ZJnMmSYNkGam5AAh5gGlFUVPxTNveyMdEoaoEcTk7SfqzeYT\nHP47zu3y/hT34CNXQFu21BSbgxinDgXRxqhVajJjMvO4QZuV2xSzOCcG043b\nIQ33AdDs5QA1e2+l5YODIzbiN3Fy4LOcdaFC/Bo8XIaSEFyTXQ7PhoEes995\n0bVvT/0G4pcSQ2DOt705KYLyk7HRFTmru9u9qlHtUm7jHE94C6/WrUsPlmUk\ng0Vg\r\n=DW/8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.2.0_1543863690786_0.18812286302237236", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.3.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "813b34cd9acb6ba70a84939f3680be0eb2e58861", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.3.4.tgz", "fileCount": 4, "integrity": "sha512-VZ4+jlGOF36S7TjKs8g4ojp4MEI+ebCQZdswWb/T9I4X84j8OtFAyjXjt/M16iIm5RIZn0UMQgg/VgIwo/87vw==", "signatures": [{"sig": "MEUCIDuSyj8TN7N0W/JXcMVjEJ+t9CqfZQbHLpO5r4+aVSY7AiEAyyem+o+2KlvoiS7FY6TGuPLH6C6eNkC1z+YmAKszR/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDVrCRA9TVsSAnZWagAAuhkP/R1089ljwDdvJbGUkJI0\nww3SbqJIKwJ0c5B/QytxMIXIWeoTt9T7lEL9ScuOLNpnUNjEvcDxRegsxuaB\nTnny+XOHgOgBqByGwu7y9IvYELre/mUPvIVm+BG/J0DQWp30Ygdrt6433ske\ndKXui6D4NpA3njoWga6R+KUKefoSICmT+8Gnx1534UeR1DykTiOaTBW/0kcb\n5mkFDN41Z6siaYirz3/yUfrPEk93OU0U39mi9HAmAYNFj8eE+m42sNsLb+Ue\no0MdwZ2Q6nUuHmoavDD85HZEs+UujTzfDKO+7ooL2/iJS4rEsxCtBUNSzh1j\nwWXM79B0Vup1XUDe4xwiCKIwmFHAkXQY7xZATvgN1eae8MWX7MWLKz5iBLsh\nqQLU/84Stpc3CIUWloSK/VBfrQGpUOzYbRftPMwXLoHytFYO62cTnXAdrL37\nx0xYzox1v7M8phsJFD3uRvGUiw6GphILezBaIulbDVDzYYxy+RSm+pBTrQBi\ne0HW8NrTSdLakI9c33RF0xVOxEPOe7qYN+Nr6sTRoOOnI1bus3wf7sapeSqS\nbwPptfReTvGxnpQbC7Fte2v6/9+hZiFyW36sKdLbAlogO5AxZaLl6mnZCM2Z\nYHmkHljKm4C8dlSiygR+KTjS6pw/VCEaZhoaLNZtFbChr2qyozTop0zAaFsD\nc70h\r\n=QCza\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.3.4", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.3.4_1551119722694_0.2348511507342248", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.4.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "c2495e55528135797bc816f5d50f851698c586a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-gjPdHmqiNhVoBqus5qK60mWPp1CmYWp/tkh11mvb0rrys01HycEGD7NvvSoKXlWEfSM9TcL36CpsK8ElsADptQ==", "signatures": [{"sig": "MEUCIQDM/FBxnaPA5QXUrWGV/kFX/9Sql4hqzii0CPlEbRXE+AIgSQF/fUxEpWTmqbHfayIOSYJQZkk9LyTfr70ZGqtM7B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTWCRA9TVsSAnZWagAAvVEP/0R+N4DBFWPbwFGw/Txk\nYGIWmazPr5Q81hh0OU/augopsYzdA76pXYDFsiLuysBlJbLB70QowJGraJkN\nK2QubPJ4dYUZasFofwUBJmjqLkVYdetY0O/XjOEFSUT+SDJOZk7/yTpJ0wJw\nXnOsmhV7a/kKXmIocaiY+Q4H+dpnlES1JGWjUYJqMXznI739ZL31KtK5gTN+\nRgbHq0jKEVqZvbmN9L4voThI0wpqXZM3CMWjdynu5LoPV/nT+z7AgA6yir+d\nwO4Gz7WmXhqJaBpIAXYPr1h0FvGn5npZLrRJig1cP5Kh5xKytfH4zyfIx/PK\nfGuZnV5AI1u9AfUWJveJVbMfVfbzuLx/6kk++T803LnHtYawy9Mv4JLyeuIP\n3FQETBs3e+MlkXwiSBDgsGJurKsU9OlNNTnBQ4zxQ5IMBD6CEoG6f4x/aDWN\nyToHWT3Jcv+Pcy0O49sjq3z0M9SE8WVztMCcHR87cAnrqSI1DHq+3ltIP58W\nCAD/iB5tAX4ESOrVmg03YiYhkAzRT58MqMVWsxh+04fjuyed93yUOgDlqNqg\nqPHSeAQwQ7pnLPsT+LN7fpXDqnQxWp2/0zFRF6LvWGM9CT1H8qanOAIPOl2H\nnd+7i0RQjEVoQSVqdlEu812eZGVSm2eSwNQ/Xxxd4nyR528AB/kWdcx0Yve8\nX/Jk\r\n=cg0o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.4.0_1553028309888_0.9768425328140422", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.4.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dc83c5665b07d6c2a7b224c00ac63659ea36a405", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-MSiModfILQc3/oqnG7NrP1jHaSPryO6tA2kOMmAQApz5dayPxWiHqmq4sWH2xF5LcQK56LlbKByCd8Aah/OIkQ==", "signatures": [{"sig": "MEUCIQCtxq39MdU21K4JL9eDGJVeyN680ne+aVtoqBe9zSEGdwIgIwVgSVRh+AmjTN7Jyo2cSIfSV48tlV1JCvKGNxBR5qU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JrCRA9TVsSAnZWagAA9woQAItEII/qlMdDXOeMN+Fb\ndOy6otVeU5CL4GbSjNRtDNB2VyeNvHYtVLUKYQ6SK8561UlcrW2dquFvE2e6\nMa+2LFccqQaB0IYa4HHqxYl+DDSQ+GJYxMLWjADUzCizsAw6MCmQipUyidUe\nRpz0RtOO3Fna0TUyqMfAwurJw0D6X+02vS3uAQBy7wpG/ulEiBHF6ajtspDY\nNL0M8SPCqntpHxUYIiaO9jbhxw38l9BRohHW3wvAg8RJEZ2S+2vjIh3zCSQf\nWjR8tP+3S97Ecf6Jz5B+qvyvVRNmS0xiotRR9ql6gh+fP6isFdJMvBNJ/a7w\nMMDXn93U+dKDpRkExp1qfMN0peF3UM3n5HvzAiD/uM6aLVSDi8e8nhX2MqhX\nzgY6K7xGtZHoUtYKRwuiQQRzYphQcPIeOack+x3O1mEBHQuVI/kNzswZ7211\nyG400jqQmTeQJSyfIQWrC/ajf0+JHjMmVat9hSkctpq9BXctwfqa5HQpcV08\n4U+mAOShE4idVfhlM+Aq+j4xotqtKEWswjfNwkRBGhWgUGWSciB8sKBDgeFU\naz4sLdrWr0SGwRrDPzOnBw9bCkYfcaJkfuOwWLX3Nrl8PdJscMqQc/RrJlju\nj0B+R4D2mnYZHNhqRgHSeRZbFNmxRJ0zBi0ft28QPtVWwl0pV/Djyd1t7QoW\no6mc\r\n=RmeV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.4.4_1556312682749_0.396827568305101", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.5.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e75266a13ef94202db2a0620977756f51d52d249", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-Q2m56tyoQWmuNGxEtUyeEkm6qJYFqs4c+XyXH5RAuYxObRNz9Zgj/1g2GMnjYp2EUyEy7YTrxliGCXzecl/vJg==", "signatures": [{"sig": "MEUCIAeFZPwcCtDdNIXwdM6GQJ4y2lxAeNJEd6XRShohkEOAAiEA/t4y7u/zud9FT8xwhHICnME104zBBDC7Rw2khqgWtVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffZCRA9TVsSAnZWagAAoFIP+wQYadobpKkgMCaD4dYS\nvCVD4NAIO0gzG/xeePapdTNc+pX67271YNCtrb9XAbs77Bd+ocB1wnT6hi7g\nzjZyMbVSdek5K7YjFeAq8rsF3H/a7FDaktAJEu/n5PE3telivnRtis0wRx5h\nBC7EtKdxngxrwxv0EdZ/BQFYQvOFX8Gz/PG9gjMpTkkseddJ2zmqeo3hzROq\nOxdf1p3+6a4k9S2UDsqb/NkX2NgVIGHq5+X3FxNBk46EAqpQYdjlGGr0Pm1F\nWGwqK8GcpsoSTFXPhVe6xBe7EqimTKmTnZjzUgdagecpXiLDDHYT8zMiZubA\nt5gEHALhsEEYeDiCF9eQSx8s1DNPDc/aPDzEUmzdcXu3DmI2X++hiDGZnMid\nmHlmXluYPCj/b1dc7qtTZWLBtmBaEu6oqby8AIykEupalJfBdAer9LZpeOPE\n5AssmwxB+GgxRAfqT5TS9ulkM0jSZ8DK7+vps/NE/dz/NTSfhMCdPwjCyeoz\nYKaRJrQY4TGa/tFDGK59mgQF9u8VijFOmuQrMmkrGJlj7MgC1QEi5stvyM/m\n8Bl+Cd6aiPHwgXk2tvUqTd1seC6OAOyBE3ktnF2J209UUKN84P2Q2540re2F\ncETgqb1b+lN4S6JqlYG8tRO5o6GUk5UifS33jZ51Pxn09ms5jxCMTfplMOHu\nOaRD\r\n=Ln5W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.4.4", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.5.0_1562245080911_0.42822366498816256", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.7.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "9baf471213af9761c1617bb12fd278e629041417", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.7.0.tgz", "fileCount": 4, "integrity": "sha512-ZAuFgYjJzDNv77AjXRqzQGlQl4HdUM6j296ee4fwKVZfhDR9LAGxfvXjBkb06gNETPnN0sLqRm9Gxg4wZH6dXg==", "signatures": [{"sig": "MEUCIAJzUF1OdYstjlo2xNbQsu6YdduYyvet4mbU4B7SNeCHAiEAs0gOP64FJCJ+L+VuL8feUYFgBy3fjcypIQYHKbq6xhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVS2CRA9TVsSAnZWagAABAkP/2d1TmNtwC37XrcOis5a\nVjatSfbzMlG+NaOwrN0cNR+11ZaSv6fd6APAC40HlQjvD2RxyOnHSAt2gWmq\ns3lDwrwpcif5L0sKv7cC/3C9idTbEhvFxt8iT4QoBOiVMN6QcCfK+5f+c9jZ\nrKUQzZ3I5Qcvu93C5opn7o7XZPs+XgVzwbZuDQUB39zhy/M2Pj22mAmThh00\nbrP3CcVjqAGN2IWNsmTwDo1tnbW7gGNFYwCICZh7hmMpihwfAFvH+6ob8IA1\noJIAkommQ+U+84pSDU/HBFfQNe+Sr0KjfNGt9Q4Pbd2DcLyRLegKv2CX5eoI\nYFFOSY9UGUoikPGl50OR4NY+R50Sky1opwRxB5MeswiynD4shs/4l7tXVdW8\n28ot5Xse0iEluP493ch/CR0dVANjfuXnsZcqf1PplpEw1O5LuMyAJiUEN9aU\nBWgC63Skzw23eWZMqn+FEDQ4qeiJHaKw6MupVNjfxhl3qslA6nHxTaRWkKvI\nCZtmAccG/qeoG1zWqlTUhYXPvyzENz5F51erYURxiYrTIZxDXaGZYarffccG\neGv3MvozeMFQ7NC7Jo13/xKk0TEliUkY/RlVr04ZRDP41oA9ihfi1Y3ytiLo\ntWempCYZoNZmr9IUR5PGCHSWKb9Z1mgh54bdHaQZgDIVBg6qas5AyiLx4Jn+\npN+N\r\n=DS9k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.7.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.7.0_1572951221679_0.20189513064907016", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "cd98152339d3e763dfe838b7d4273edaf520bb30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-y2c96hmcsUi6LrMqvmNDPBBiGCiQu0aYqpHatVVu6kD4mFEXKjyNxd/drc18XXAf9dv7UXjrZwBVmTTGaGP8iw==", "signatures": [{"sig": "MEUCIENmbQsj8HsyynxJeABe/NoxVS540BbfEvLGHiKVMFCrAiEAl8mP3P6kTmHSncMBtR1hBys8ohZzNh6eeuZGkS69c9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBXCRA9TVsSAnZWagAADloP/RdJW4s53iRLYOnByJYd\nV/eXiJZ/heHvx7HCRCx0DUScp6Cx/4AEpFg01tGGIpGm9FJ6nWOILfOJ9MZs\n7mjQYBA8sF5Sl8taNxstDQt0SV/sjJiVv5qzdpkMcSNNls+0CGqowbwb5Lcy\nESqv126i8aVM/OSQiguuec6OU1rLChy0jlB9tCCK3UU9jkbFJwtxS8j/LyVd\nC+LkEcYxs3p35Sq3S5Epx95sqsNXkEtpWkZEZh4B2JBLegD46I4n7s/WVeEg\ntD0AyHTMobRe18+hle/uRbQzNTlhUFrP3YlialPNqyj1ZoAwPgSaj55M1iN9\nWHUvsc++5GvZNwaGacC/w7zZyUgYCy/04OGXx3zvrx6M+vfOUGuSSgsisjjX\nXTWs1Kp2tmenRzTukA0AKfCCUSRnnbRciMrlIUxhtn0ni9M3Q1lJxyBEnK6K\n7f56Er84hS+qpa1zMyvDLRpOTwEa6nIzDwd9yCcz7ML6XOEBsWlUDzaGiVJJ\nX+CogH77y0KwVReXbV3RRsCeBNvqhDW48yhhuKhLWgbRuLM7ZneHlG0EKsNX\n3/7a6KBje7lanFGEGSdXzK2mS72R/abAPybOCo+O2e8ZZW35EXCKIExbw2n8\n2sqr4Hf4iywsVnZpQUr+w4QU/8/JLFolEN3JUgORRMB0g0a1AUdQ5FA4zRFj\n/YGA\r\n=cPwQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-hoist-variables": "^7.7.4", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4", "@babel/plugin-syntax-dynamic-import": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.7.4_1574465620116_0.2162169378942309", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "b0ff0106a7f8a465a75ce5167c88b648770b0a0c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-tKF9KLiIsiKyWTVU0yo+NcNAylGn7euggYwXw63/tMxGtDTPsB9Y7Ecqv4EoXEwtoJOJ0Lewf17oaWQtindxIA==", "signatures": [{"sig": "MEYCIQC+H0ihnGTeymgKONxJ+7X13KtZCP5Ml6Mq7jGSi2NxSAIhAJORgbaoD0DyxtMYJa6h9wwr/VKl60OudiZ/hXs+e8e5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWeCRA9TVsSAnZWagAAb28P/jpWCH9WzMefILg8+6Xu\nQtI8mAh+n4vaSiQeyldIspv3oxUIlgdAgYciW7FbHj+xNRSO41dlipSwHlVo\n0YsGNd/H+aMw6nx8XRI2IYD6Cc1a8323oOc3Unsqil4RkvHheAbUyI7U1O+H\n/xfCjiML2kmYFRsYYokMMjEjoZ0TJQc57nbhdkNUbP1sbr1bt59H/03w01Ei\nCCj1rg3iRXdi319IYQM/8+LCAMqHnQ5Wmda+T/v7LEU1Va9BD4et/gnL8pB4\nbEsVowbMffDXjHQur80jHf0qoFpnHSO1HhS7yk66UfXy1JK/7gLltJB7B4bT\nl0gTvHjW9NxMrZad7Gi6GtbjQpKqSA3eNKcj+bCUeH8BC9prKi4V6gpXxf6H\nfdUtj6RNgHWdvHV70L4RHiD/RSDwc1pW1gB+GqdlzqZAmJcq4IYCNLBgf4QS\nFw24fTsskGnHTz5LaYbpKaWEZtfSFU5NHy3RBi3cC5gy6hxMQ8UO9BCoH7bX\nfCnuV9ZHsNDEl9q4oupsI9k+nzkyT413vOGwlhB8u3bFeiZUKFcShF/lJsUq\nVHnFEsKRbHuZomxR7jsf/dmcs3glOImNqlqhV8yL+cqsQbnwCkMpxyj7Jo0S\n6RyACslGQmLdiCYn34Aq3q/65cv+S9sv7z/YqSwdhOutWDk4agbRa5+qhLMO\nNZdH\r\n=C6yC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0", "@babel/helper-hoist-variables": "^7.8.0", "@babel/helper-module-transforms": "^7.8.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.8.0_1578788253899_0.04115702394166432", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "d8bbf222c1dbe3661f440f2f00c16e9bb7d0d420", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-8cESMCJjmArMYqa9AO5YuMEkE4ds28tMpZcGZB/jl3n0ZzlsxOAi3mC+SKypTfT8gjMupCnd3YiXCkMjj2jfOg==", "signatures": [{"sig": "MEYCIQC4J2DLILJMz78T5xyf5szE1C2yqOI3W3AtUILJbK/6dwIhAJwS2wrTuu0T+nxutN53QPm2lWMg8nDnggKOcRiYBDA2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHORFCRA9TVsSAnZWagAAZ1oP/RtCV6tZnkF7UyoXZq7q\n4OCRSxCdjiOZnrgMTvMNfuxznjeQucvsC1GYNrFAeC5YdzM+NLZlg7reKaIZ\nfjyL+WxCf2bteyErqgPoKm2lQCO3vrzScb3qQhfz/m+iY7D3s7HdBiqXeNj2\nbfwyGXJdMEcIepkvcAnqvRA4250AfPm66X0lZJqe3CpLuhOFk6jgZrB64XAL\nSYTiRIjP6d+zR2qLZX3xlxiuvz2lFZmbRxOCs9WX7Im0iDCDYjrgndhvdhY8\nArqXV0mIXlUehrADLdQoO0pPaMbcuJa9Th4L0Wy7FuDsBqTkUEkykOEqgIU4\nYRFoWDN/MA/hCX4W/lpjUMqghw+F/BwDlr8ITwWlmp5tkRVZXsWsoWP80uZX\nQktX4mAN1CEHVT0gS4Nrq+N+fBDVyWGqQOuX6q1f5YBOUD01b665B6EdTmei\nMoIKqLeHzT+aw42WsLVv6fPJwm+kHReeX7cIa136+ZHO8c4K0C9oSzMiGqlC\npWM95avcpXyr9vSbFjCUZeUsoByQfiywLcg0SgT8tuM6AsHzjzAGnErWphrK\nlCqi9Q/lUt25kfBNthdDryAEwPSITMcNKSIUDa68ARzsGmcvmFwhRH0/Avii\nr5LcDEDlHOAkpilw6eIC4Bv3vLBfZMTFGE8p17G8/OBBI82Lus11zbFVMqJd\n2ntH\r\n=uHLq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-hoist-variables": "^7.8.3", "@babel/helper-module-transforms": "^7.8.3", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.8.3_1578951749123_0.8511912853503827", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.9.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "e9fd46a296fc91e009b64e07ddaa86d6f0edeb90", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-FsiAv/nao/ud2ZWy4wFacoLOm5uxl0ExSQ7ErvP7jpoihLR6Cq90ilOFyX9UXct3rbtKsAiZ9kFt5XGfPe/5SQ==", "signatures": [{"sig": "MEYCIQDypYescw1Cfw/Xp7XJ5Mx8BI/l/s5ceaKFaGkVnpp+3wIhAMgbA+ui1SD3KQ/s/60DCSMXm+G53p4MBRHWWWaMjkc8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPBCRA9TVsSAnZWagAAHbMP/j55/90NlceW6pM4OPTh\nkFiKOrDb1nBoFMPeVg4EQ9jaPT4sNCEk4WysbcZ46M1hAZedENhbMfOmqJNv\nN2eH8zgsTxlTxOwIIxvODMo5fKPkR/xnkIMVX1zZT1NmGiSptNa55hgjVMr5\n2yVTNxdU6Xhi7+k0N4B7oE/6NJa699taCctz7FFTe01iOSPzSd6/9yzsMX8k\noHwY1yv6rcK2P/32UeLeYJJmy9IRyXd5ajjr+pTpHuv/xUJiZ4A8mzCsgM+/\nIOxTUHr6r4k3pOUXb7XyB9gPQQ27XUn7ozLVnXylmkG4WBrlufsr4sGdJbXD\nC3mQ7sI9qj6WV5Qj+/Ih6e879WkZLhNugb9qwrL+ZvzTtjN27vb1ks9GLwtH\nCVJo0Z4cdSRlA9rPIMJ10Y7gyXbhl5YlylJUazs6wphhbJoXc0JH3qM+fg2m\nblGsB8UKNd27OkwcRjDhUPZ7IGW7HUlumfxC3d1iDNUxliqnvsua5nwhrKAJ\niMixeazWhF8Jhjmqq/GmQU8qNzfeyzNF9CLIzYi6f8PQv0keLJHNv85sG3rn\nyXdA3GMXDJ+jpmwM4C1+Cew7TxLU0kn5MN5U/i/dhCzu0xyx11nHTS5JhHhY\nNzCNNonogDcP7kbaiFEl/DZleDvL0AvHQxaXYBW9GUzEU+7EOTlGT5R0UhIG\nZHTL\r\n=r42W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-hoist-variables": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.9.0_1584718785323_0.820243223196232", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.9.6", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "207f1461c78a231d5337a92140e52422510d81a4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.9.6.tgz", "fileCount": 4, "integrity": "sha512-NW5XQuW3N2tTHim8e1b7qGy7s0kZ2OH3m5octc49K1SdAKGxYxeIx7hiIz05kS1R2R+hOWcsr1eYwcGhrdHsrg==", "signatures": [{"sig": "MEYCIQDfd69oNl5/7yyvDWQiudqqdVvVMQE/yepgL42c/q8q2gIhAP4ouwOpv2iDDqMtjWfOrG90Vj9WNrtYi1Hc7IuwCDYH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmFCRA9TVsSAnZWagAA4T8P/irG9g2RTryt2oKj22en\nwjoLJhTq0ca36Aa+k7Z4/CH8Krwlf6Jiv+xfCxgLwHUlIk/p0jrv8DeP/F6P\nfEUtqWsP+azZp9PC9BMKIaEJ7d9I6wm4WqR8hqnrwm5VH0FiUKqADtWoXxrq\nNOQdrJIKViW8zp/WVB1T1mexXAQ0QvkZDU7DmQ59Mom4d5zvR4qtElc17Hcy\nOayBOOM9ukVoKHRaUYSedvkaO9PkNhDFv/fdGthvYL/cWLb7RdDW+1CnHEDT\nR7t2YlBZKrDoJnGf173cX847ZjbK1BzurqPkafRoYc/dqhaN0guT97xbV4aF\nKc3asDgR/v9rGbTv5q6rXA+4qOpjkKPrvvhkQ+9NsM/tlGuv1A9sL4ykFoLG\n7WLtUUX0hwtXC0tx1Dh5OQvTDqkid62E3qOyHtPGlNaLK8Mdwb26KL15VXsm\nP4aa045aP8FWODlr0oy4XkbFbbW44NzhpSn+ZnOgt/jX+lVyTIkYj4wbgnpf\nlBvcyRKIZhs1TkyOmX/9P6VFhOEzvzS6/i+aFF9LxnyjtIrEOwhZwnvxgjeB\nEVFuzxYIrxGUWeWOZFZ2V+6faIAygnD3h14rorfdnmT7/GK+hMn1tlHBdKpw\noUq8MTBCZCAJ8vqyFuHj7FHLyxvPExh5AyH0UDXWWPi27iKxpPvo3fXuxiB4\nqIh4\r\n=02hz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-hoist-variables": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.9.6", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.9.6_1588185476859_0.08352679106609084", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "815aa9b9d59224ed1bb5d4cbb3c86c4d7e12d9bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-L/1xADoyJeb01fqKiHhl4ghAJOnFcHvx2JQA7bc8zdaDFDU4k62CJmXqDtNtJUNiOwlHZLWg1l7/Twf1aWARQw==", "signatures": [{"sig": "MEYCIQCJQ13lDLeRdA08iMLXuJaPD/zaCZmyTPIMZcCTefLWaQIhANE5gKgGMsbBfO+5clpO8kIRTkgF5xQ3HbVkPn1HSFeo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1yCRA9TVsSAnZWagAAfcAP/2qdD4/1obkAI0zHZXAm\nD34C2Zm8hFRwKGP92wSCyEW7XFHN20bD7+VwSuxXZJxniS/4D89JnbQRw1in\nnuqkHbG/f1qAhjxATGZo/hMLET9iEhGAj0AZCBq989P7UXoy3OCv3nuBx+QJ\nXQsN3kBJN7Bu/ohkvz+YXSHPCs9gcDk+c0fZXZKL2iY3WSH1IMZMTBoUmqVT\nNDXnXeErt4q2Xln5B9b3BHRG4ThWP9yXd2FHnP/PiqhPPeeApKwsRCwTdqYD\nbJImp2/WM4FcucSlIPgYOALrZM1jwayJYh1sbumfzRuS+w6E41TC5PiH5d2m\ngk5IMEUUlb16eq5IJluvB86d/OwgLRnNdnWXmv2+F9lutQq8tHNAIU1EqCj5\nYstbAdjJxkNSyXnOTvgWNJIA/bfta/SeBYjAb705fkqcPvp3B8d5iX7z7Upb\nSrHtmM4IKLIF6Ys7uD8ZceWVoxwEsDjKD+wRtWrmluwVu4TyesStOMEOu7s6\ncy/KhI/UKIgXvgencG9316QizQt36RYgQQOIW8TpcsuhrmXS6PhJ0X7klsXo\nCRGrR9yZhemNVnn4C5DHNN9HpFPy42ro6CKPPRIS3kf5k874DHPF8VmTpMYj\nn2/iPKpy+uXaEToKyqEkfuSCXH9b3c8uL08jQiU6HbAOY+NuVixResM/CE5z\nDXTC\r\n=Y8DD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-modules-systemjs", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/helper-hoist-variables": "^7.8.3", "@babel/helper-module-transforms": "^7.9.0", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.10.0_1590529393810_0.8319336750713804", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "9962e4b0ac6aaf2e20431ada3d8ec72082cbffb6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-ewNKcj1TQZDL3YnO85qh9zo1YF1CHgmSTlRQgHqe63oTrMI85cthKtZjAiZSsSNjPQ5NCaYo5QkbYqEw1ZBgZA==", "signatures": [{"sig": "MEQCIAwqU5OGOdNnZFVYwAGC14J820kKfOEnuaBkOOK6SNCqAiBYZUQNGpg0pIenIdyn2NBKOM3J7EqUKBrQQRmzeL20/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTgCRA9TVsSAnZWagAAGDcP/jZfPgJWhGA4w6PKFbpy\n/vP2LDaXuBWiYTWPThvx5f3lng4KV5MUk4Sg61uc9a2iRPqPStuybJlE988M\nq5BRz4g0DMmw+GlLPIZGfd+BNZlDdptsEBnIrtULmoKRdti6smxg5HfRRdqI\nfAy1XgId/4QbZeIqao4Po+pxiwtYute2s6O5tK2QQjtevMudVa7yUPdlBcvI\newcas/VtwtRAqXy2qin3O8HATlo7cNqe3RAL/qrr2NMpsK7G7oqVrnQMXpuo\nvdnWL3MsQoWF9KuU8JRpfTkkgMQ/Lj98aaI1+S0TImVmktHHdzApVdM9oHbM\nnmzrbB5rGRivaxILhS2SgEvYt8hwdM18H7VKuamxemPbBueLlLEWt6kajiyA\nlsK0tpUUP4xwTwAPP9YO3Ekr0GGjpljYVRozgiSbfvPkf1tW2gVjICzNbWt0\njKqgox5oMV3tQEEKOMcS9QNvHyL8xYN2Y5VI5ze3XSdH7Gkf3D9j6dvAcXVX\nY/Q/Suo8HD4QJ1ZZpfHHgTGYQvzDmH+o9YAoJVoikYH234lV3gLhBXKVtirw\nge7fl127wk1nDIF9HkMDR90WBqR/EYwKTFeSmAAYO2aevtPWFmStqVlhkD+R\n2XP/UtROC4HInGEnt7rYadd7UmjZ+1aJkKyzPn3C3Uu9jzHoBbDHPu0puYxY\nKN2/\r\n=0Q1v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1", "@babel/helper-hoist-variables": "^7.10.1", "@babel/helper-module-transforms": "^7.10.1", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.10.1_1590617311915_0.8861673481037948", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "004ae727b122b7b146b150d50cba5ffbff4ac56b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-GWXWQMmE1GH4ALc7YXW56BTh/AlzvDWhUNn9ArFF0+Cz5G8esYlVbXfdyHa1xaD1j+GnBoCeoQNlwtZTVdiG/A==", "signatures": [{"sig": "MEUCIQDbVVPFZsRrXzWi+Mvl/MKe8fgOD9gN3m47E1APSNC9EAIgdkQQB0BXLehnyq4JJi9E9y1/8SdGMygVKEhQZpuXFDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18706, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYKCRA9TVsSAnZWagAAlaIP/jCw5uZsKOIAlRDMYm6B\nWaW3IbeN83JsOSccfRD0ax3zA09+FeV4PEcamB1umshCBY/7wEoEiALIDdcf\nRSLXocqKS9CHY1T2F7VQm00dAQhyANZ0vVVLNl3Q1CGSzvPtvHloi5aucTO6\nPT/m5f4CbOfxmGsqHle4MXeSu7+7W7+FiwUb5jbOaPaoayDwqB8C/iRsswnn\n05KldTE1TQtd4+SiNvcL2SbkFjzBtEkInGw0aWU5VHxdmbobG0lxFw/0VTkx\ne91odGqnaFPs9LRXXEfR3zleBdqZPAbSXriYoP5WGXeca6Y3tX4AGEbwTolK\nW5IPMt5mvDzO7EfXvN2jV9DUWr/p/OEfMmAABCOyxIz2D697D0nYhCgdiOVH\n7KSgOBIZVTU44NXEOBSOAUVvzKiuqBp9D5pR4DWUCAuADhB1zVJKhz/jE6Wz\nNfp3tvNMGXm7Fnyb7e5jyKZ49cQeV/jhqKec2Mr3ni0x7oUZF6yw3DjbGZtx\noKxwxXEHU7s6wDu4MTQKvA9GghFfwSFQmC9wVt77X1i67XF9snSSHpK3cst2\nvbP+qcACJZ+kaSRCJFBU+GNoCw3jZCJRG0AKQgjwhgwfRrHrVu6EwTdF/CaW\n+m2Wfb+jBX4TKSCmeFWN/UuTc7dmhBw0VWh548gpZEJh3BVSJduS4n6wBbk4\nZggJ\r\n=hVYB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.3", "@babel/helper-hoist-variables": "^7.10.3", "@babel/helper-module-transforms": "^7.10.1", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3", "@babel/helper-plugin-test-runner": "^7.10.3", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.10.3_1592600073686_0.7860943649373575", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "8f576afd943ac2f789b35ded0a6312f929c633f9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-Tb28LlfxrTiOTGtZFsvkjpyjCl9IoaRI52AEU/VIwOwvDQWtbNJsAqTXzh+5R7i74e/OZHH2c2w2fsOqAfnQYQ==", "signatures": [{"sig": "MEYCIQDtvURJSHUY7PRhPXwKtVT9dbNXSE9x+iD5W9WnQMMD+AIhAKNXpIsyfq6UQ4/6mxi9jZ0febYRzVsOnz5qGzlK5CgG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zp7CRA9TVsSAnZWagAA0JAP/jVYrkV5tTMeoOdvsiCK\nDwbqxulBJl1ETOraCYd/TPi1oTMyBQv4iH4w4XQlvOZI6JJyWYMC97cqUC4Q\nkjTZgcNm8S5VPV16f9fq3RD1B7mxkjURGZB4bNXZhhdZxn8R0o1RNzktd2Q3\nZc8iilP1x2ajKdsktqHSL3gwkfE+ID0eW5yjn5M8LpKWZO+ZxqkA2THepIC+\ngSdDW99Q05ey8ypWR5R+EL1q136lHW2H0oBatf51VJwv5AytjJ+UPrz7QW5r\nzj4IaRWfZyu9zeMkQBDkAzkscM0hMs+KH6rYOWvpt5vOU4HjV3CSAWE1YJHc\nwFOBbW2CFgB49AYTciD9OplmtI3YNp6eKtLrwSS7WCAqu/Tz9+qDAQsTOtI2\ng3QRzcJqw41JOjjZ8sSG/F938pjcDYr2YWGn6yqma7+IjChO9v8bwrMoPamz\nFfw5fONE3RsDcYrC7i1pF1lTbmKMwSOdbkXa2RRB3pg955DP5n1W3EIHtdR8\ngyYo544jrKbh1ksO4IV0tjf/2q/wobeME6UajgZ+a1b+P3IYg1tVWdUlsDbI\nmvTh2iYUHsZYTc++Mz04OSKi91JE+xlImyHhMEhTqmrIO0/we2f1gqtKjOlw\nC265VQmN9db8BTUQYe15TWxTgQU/RDnrVOHSFu0tx6GedBQio5vNTBfxpwnm\nu7Ci\r\n=K/F2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.10.4", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.10.4_1593522810718_0.04679352569429729", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.10.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6270099c854066681bae9e05f87e1b9cadbe8c85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.10.5.tgz", "fileCount": 4, "integrity": "sha512-f4RLO/OL14/FP1AEbcsWMzpbUz6tssRaeQg11RH1BP/XnPpRoVwgeYViMFacnkaw4k4wjRSjn3ip1Uw9TaXuMw==", "signatures": [{"sig": "MEQCIBtKBzTrAgG131QgvWs8MD4fNVgpQK9LfMxv3FJlMEpfAiAQILpKojtyZr4UUW6mkH7+aHsL66wtrwyyiCMYaeCpUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbmCRA9TVsSAnZWagAAwuoQAKQwbXGco2SeYOnZNF9G\nwI5UWWnJ15/mlxgCbTP4xIvV//Pc5DS54z9ojOfx9om37EmQpLhOlDOwAiuf\n4Jxuw+0HmGL/CGxn7GNaC8WrTVGk4aPy90VNpZo0zVyzSJc8HkmVAMoK0z5K\npt93c+pLKGp3js0v7nlu6tk+zSpkwEAtXNYAsmYqRtCFAl/T5yp1zNHFnUwr\nS6o9lO0raAUhktXFZfF0AoC6eWPr+BnNgW1bay+mH8SVD4sYk+Nn4/sGSk2R\n9JalhzHyiUQyAdqJy6rrf+eILsqoU91HrlmiuJMO/22+ufeT8824FdNCdgIZ\nmtUbAA9DtnSHrYBekC4e6P07fzjjntsJ6vUno8vs062fK9PVBc68/eE50KmS\n6RwlE4Cm9ttJfmkfKCeMza5xY66p7ZRBlGVNPQajQ53MnrDtRtxrFdyUrbWg\n4LM0nTZDF7I14a/NZhnMMRnDwEX7JJ6NpEcGEfiXEKstDtbE4rYXnMykh1pS\nRKL+RIYMtLwnuZ5y+Zl0ERGX5isXyU1BilsVyZ8op5NpV2J++FncXvfvXneD\npT1EXsZHx7tHo0mFQmamdZUOfPvrT3PI8Ocl+Z/9wlpzQLbaeomFe6O8Vi/5\n9/N2PDyWhMJtE/DGRJCPGVmDs5MK3ny+5GnD1VsRvulZjMKf2iNiAZhlghke\nlx64\r\n=1I9n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.10.5", "babel-plugin-dynamic-import-node": "^2.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.10.5_1594750693842_0.4382641289334943", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.12.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "bca842db6980cfc98ae7d0f2c907c9b1df3f874e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.12.0.tgz", "fileCount": 4, "integrity": "sha512-h2fDMnwRwBiNMmTGAWqUo404Z3oLbrPE6hyATecyIbsEsrbM5gjLbfKQLb6hjiouMlGHH+yliYBbc4NPgWKE/g==", "signatures": [{"sig": "MEYCIQD65sADa0jZjAndFuBO8sRwx3Sw3YtHXTJmvjTWqVUwNAIhAMHWPp7Tu/tbMgsX89j38X7P4GMyvol6TOnPCOSTpakr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mUCRA9TVsSAnZWagAAhcoP/iM8BJar9aOkRaVQ67Pz\nP8T0pfMCPtuYivqc3YstZ4OcHr/i69Nt1F2kSq/sqqsYt9jFiH+RdYdPwTJz\no7VcsGowK0yl65sWTuQDekTpKGd8yzvq67ZhMgtbnOAlCfra6KiGy+5dVvke\nM1qMCf7to04hWbXyFqbHpfGXbWcfoBwlOOguGxneVDNJj3XLlcZPsKmg8Scc\nDT0UIvfhEOnywwGgPMwxA/4RnTSFNXT3QPvekHNsS4BAzjWWyJEaZQlE9mfh\nKBbu2SDDPCMeq227oYTW79vYVeiM4md+gQM4bt7b6SYryysihEtfMBdX4c2+\neb0/qqTZqJTYFksDSDS+lNHnwqrG5wHTE13rV8JbJGAr+Pa1nx4JuVmt1xvo\nOgYi4slagJwlFCCpos3aMqYSAzLFIkuUDn4nuiGGLloFgaBeAJHyznJVLoZS\nwiEHpE1KzrMsvG94g5cCLJisxPg7XDtpkl6px18AXRMUMIKY73pMKRCGRZBu\nNkQbUcO1eZVftntjKsOu4C0DfBdsR/W2EkhM9LDwR98u/tQcKlYkW3L4VS6d\nb3gdwqkQmgy2ZRWwriTflWa3K9HdZLO7sgE0FLkohBQaE8i5G/3H6mLVtDfH\nEp6a/juCs++fA3iC6EMnp0bwZWxX2xHlvHyRsvc0YjsrRCg1l7Zr+p0+AqMz\njwrO\r\n=W9A2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.12.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.0", "@babel/helper-plugin-test-runner": "^7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.12.0_1602705812002_0.25796952930829975", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "663fea620d593c93f214a464cd399bf6dc683086", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-Hn7cVvOavVh8yvW6fLwveFqSnd7rbQN3zJvoPNyNaQSvgfKmDBO9U1YL9+PCXGRlZD9tNdWTy5ACKqMuzyn32Q==", "signatures": [{"sig": "MEYCIQDfOM0avGnt4klmEYEt4LZnRsflPhLTi+VzxI5zut6AAQIhAOVllJxHA1n9fs/+4bARZQHFgYieSebEv8b41UY/sXSj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20410, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNBDCRA9TVsSAnZWagAA3CoP/A+NH6aV7RWMbmwvUHX8\nRUSc9S30xTPvJYaV1GdxJpORFqNXLJraif51xR4ux/yKBzY6u1Xz0sPhl0if\nNlTlruMfM71roWY/HiANAdcsf55B432r3zfk3JNDNxFXFscYH727Mcpsnx+R\nd9Ps4gmWmwMt4ZWnpp58+0T/V1LikEdGrSjgu5nguVe1r74+gKrQ9IH9/aCr\nTEiBpHifptec5YuvSGKSEn+mz3RY+GGSM5/q4DqmXhswBax7LDpx0evv/7rm\na1r5zVGgXK1khPrc71rpvdQmNyCKHLRX0ZLuqIslJUKFJISBWF7ASm57EufB\nO0BXZmqB/aAKSEGuvynKZrYnlPHzLRj4ODEzoh/Yr2s3WrlUeVGs3VXC1I9/\nn5jrvk+q2nUcoK9AW+IT/8YS0BAKWgW1fyz+IoX/aBdfxBdSBbfdUTj8G5q/\noBrGmJdv5qPKDEJI2Pf8WgdaZ9x7lUaYufGh8EbT2tgOrNINf46sWL36pQ7O\nBiMjzhHQMNO5yckZ950wQawFd5z0KHX9bOLxJwuKpNwWqXge/9GGueeLkLeT\nb46+YOML09LiotMza9QYOwVrDtOKjr8s8C73MuR+O6ZT9gcwVkJpOHmZRC9D\nipy+c2yDPh2WalKzHObu9Nud+y2Ktsn0sMW5Kha6SjLeiA11ssg5oPrMSBsQ\nhWG1\r\n=0o+Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4", "@babel/helper-hoist-variables": "^7.10.4", "@babel/helper-module-transforms": "^7.12.1", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.12.1_1602801730801_0.8430002384079152", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "351937f392c7f07493fc79b2118201d50404a3c5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-aHfVjhZ8QekaNF/5aNdStCGzwTbU7SI5hUybBKlMzqIMC7w7Ho8hx5a4R/DkTHfRfLwHGGxSpFt9BfxKCoXKoA==", "signatures": [{"sig": "MEUCIEbr0l5LZYXp0KAZZYe8eqss6fqfUJJHt7jbueNVwgiqAiEA8nuh8glwsFZRv/BWxnB0fN6BKwJbnLrKwxHRjS3wTQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhxCRA9TVsSAnZWagAAQwUQAI85Mn/O5MV+x0gGbuza\nwHySCujBiv30/gAZsZTp/wcD+eXBtEvrtnX59susHinU/eRFU8LPUtsX90Sm\nj6HCKiITEpWLg2bbDh3dCgzMpusjsJgDvSxgt2+1h5UncvNEZKPfbkqki6OI\nhfb6vecT6FQiIUWM4GSK7kJ/mTG5ut/gB9RboSukRND930IAQfKCj6eL4ITn\nxojtIesNwU2K7El6Pc/8EHqWxkb3LdAquKqvhUrYN5FdJQ4h3NcpsohMi8o1\npKv0bD5xzY5FEAHZtApsvQ1xpoiwwcDbbSV0AJJZ6NExq+ahs76LRaktp5HK\n9BvV1jdOdY3G/mc0ePUJXqYHDmtRZ6MUjeJtFmnk1gcX0GiJrRduBzobYlY+\ncl+dgpLmSG19s+rKLS8WqQCx8NAG+ZY2zQMzjWPoa9bJ5k7pgwfUKz2QVAfS\nKRkzgR4IESxzrG8582JTZo9UcEJEA+2CAMMR3nHyENcCDyFO0NxONBJCGXbH\n3VOTYuuFiU30a9angmaEzhTb5Zsd9McBr9V+NqgvGuhhogKbQJIBm9goYwzk\nyWlVTBHV+k4iFu5Z1Uy4BZnYK2kPGaW76xzLsU/rSyGIbKYiVivhzpKh0KQj\nLwvRICxsT/L9auGWKhnvbvDcfyD0l0vy69rjGpE2oqAGPfJ9O9hXAnKsmJHQ\n3yRA\r\n=wnRh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13", "@babel/helper-hoist-variables": "^7.12.13", "@babel/helper-module-transforms": "^7.12.13", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13", "@babel/plugin-syntax-dynamic-import": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.12.13_1612314737470_0.42269373988801573", "host": "s3://npm-registry-packages"}}, "7.13.8": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.13.8", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.13.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "6d066ee2bff3c7b3d60bf28dec169ad993831ae3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.13.8.tgz", "fileCount": 4, "integrity": "sha512-hwqctPYjhM6cWvVIlOIe27jCIBgHCsdH2xCJVAYQm7V5yTMoilbVMi9f6wKg0rpQAOn6ZG4AOyvCqFF/hUh6+A==", "signatures": [{"sig": "MEQCIF3g0PbPKTS1kOtfD+nRe8aRL81O/K9aHDkE/YoQ2tifAiBnIqsZ3Q8zoNRb0NCsLpIHPcJct77HIQmUEKi1cm9u6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOYaVCRA9TVsSAnZWagAAAAsP/jXvEan3s83KbsLj8rew\nk19MpxSSHsGGyC9y6zQUun2/vkYX4QMliRyfaWODIWaMb/hQZ5tE0Mj9ArU0\ngpfY2gcoYQREPVzlNteRz6CXFEoaTBQ3bFMy4MKSiiNkc5HRDcQH08lcTw5L\nrGripuwRxcLrBOkWt2hCMm1dhV4yB3NCvHV+HIjUtf2Ye0yBrkW8W7XapqzX\nP8kuT+LrAMfBXZSZ1Z+N94+zcaWZeHHWZlvygOKlKDfKby1OQ4NYkECCTsap\nKDdxnNFvg2+LgeIA/n0CMPh10ED7VSA+/vvMihRFAdSTHOknLGM6bn/6tneE\nwZDKn7DbIlaQmIwrDcXfCxroJ9qyEizvaGJWYEiHZBm4zwrNqhOI8aLKmird\n9553fydq4CQkF/NnsTtKstTyouBW9b3n+7x4JvHObNf6fgvKYYixe+1G6z6+\nDzb/6GXPGcbUa+iYbWdbuOzr2I4e7KTTu6iso8i2Ht1GM5ha7G55sW2YDV30\n0yzVjVa1/5D55cL1XWk0zJtP8u1Y9y62pRQthIKB/PDKlCSketiqH0yZ9Soh\nZEbh7Yji9UhdXx3VRXwVVhrmtyHWd6dQn20t0c6vKe8exU+AwJnIPSzwQy1E\naxA2nEUUmnmCKAd6Q/qOn2yX6zmACMkn/bZaQ91DBkoR7nAisqxfT7L8lXjg\n53XN\r\n=9rEX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.13.0", "@babel/helper-hoist-variables": "^7.13.0", "@babel/helper-module-transforms": "^7.13.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.8", "@babel/helper-plugin-test-runner": "7.12.13", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.13.8_1614382740768_0.398245216608075", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "c75342ef8b30dcde4295d3401aae24e65638ed29", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-mNMQdvBEE5DcMQaL5LbzXFMANrQjd2W7FPzg34Y4yEz7dBgdaC+9B84dSO+/1Wba98zoDbInctCDo4JGxz1VYA==", "signatures": [{"sig": "MEUCIQC8i+mwlhB9xsavWxy98au9IBWvUWBsYIkVUZWRXYRbcQIgXhTk8FMxGuYguukmZx/topCt54Ohrz4KO0q8nzKLlxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsUCRA9TVsSAnZWagAAotgP/1nRGnwVhg/Bsa9cOuzh\ngAEdjZezFJI16gk8ihahmh/m/iUZK1eeidBWzBtGEWdyXrEmdU4WyX5J8VvX\n08k/toxmylJYLKMcE3EaPZiiFIzcQkLaSBSQChuqPeuWGSIEm1EEHz58AoD+\nqFJs1IcgN3/QHPbtExzmSjL4rtLyoNDPYXI2ZFfHQSrG1CvSEJt6bgslx7zA\n3AlBa7l8aq5XS0sZIwzSn4awKj+mgRMBYAEneFgjXGXzYW0QLt3EijxJYtxH\n6TSuB6Q9P+P3LCF1yB1qGaCgPDoYMl2yyoD7GDlKn//B2LHqhzDT++VgKST3\nntp0uD07V542lFcccTZqbbFB+1z0pMY3EFPIHU+Yl40lLo6c4aNRobyHx2Ye\nUqJ0+VQ2UzDExoW3RQd/EtseWB601rhiJdu3Iwp8FLW60YMzwQmMUDNofQVb\nM6jLC/5lWVVnPfj/28AQ6btxt8aLS3pvKy79sy2Sre6U9KFWBw9O8Fe/tba1\n0soX6EyevMzBfqh0sFa5tlCfyRwoQuyAV+L8SszW1kSw2nY44QoK1hth8h4q\nv6IkDkki7fHWKIMWqUQmvs6pYunmRoPgM/Owb16W1+vglYVvr9ulVpP4Jv2H\nWdleSlCaLDXsDUnB9QfQ+QKCwcPujZ3+NCQnSZWuga0rWqo5ZQzHpySCmvM+\n+Fia\r\n=HrN7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-module-transforms": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.14.5_1623280404228_0.5088212698947481", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.15.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "b42890c7349a78c827719f1d2d0cd38c7d268132", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.15.4.tgz", "fileCount": 4, "integrity": "sha512-fJUnlQrl/mezMneR72CKCgtOoahqGJNVKpompKwzv3BrEXdlPspTcyxrZ1XmDTIr9PpULrgEQo3qNKp6dW7ssw==", "signatures": [{"sig": "MEQCIGsuEyrmj4dAaqc4y4gA1KXMUNqAcGFR45tSz6QCjXPjAiB092HMwNVw+MRHqftDGXEQjPO7WSVFLAnuBoy3ltGMEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSxCRA9TVsSAnZWagAA+p4P/2wqAxUIVGH4XF9dfMXW\njmrKLlnXKkgwz+ghIJ+yy9uhPZXIj+kDH7t4kw2nDrf9h+FZAlufUk9o8fyB\no/oYm/j544gA1Zj9EHgWqd+dOtVn2PAz4Reb0ESuAsPsfSRVzHrbMNlb/AwU\nC4TIa7mF4erI+Vau0rbtBscRVhVkVv7q/WpEIi0IrRdr6EmFjN6K0qQCHiLf\nHhQVTwWGmOpCBFYpfJD1ruI7nqg+B+OGhJ/oAuA7Rx0MX/2Av+p99AWkAGcn\n7zCI/JjfyD/icnDF2lBRVSppZkWSUqipQlSbjVKVQ7zwL5igRdMqFXh8WEac\nAXC4ex2zanUAD2bAMEeSOzybUcU8fXxTjnBvKoliekXd/mM5VMBFM9MuMcE9\np1WktiszICBKcfoTzeSm53GLgtMk03/xUdb/f0lAAeOnEvxK620q4nT4dNdB\n20Qw6bGMIs/hU0bUVM1b5O117Xk9Q34uuDqC1irqTCEagyrA5ft7fiZ4B/5L\n4WGE4g0Eetk5NkqnA21Ykz70XMOEz6kYL1hAi0D3Uzx+N3YLgIRgU2uzW1w+\nN2iGYw6zLQUgDSwXw4UG/R/jjCItiNf+w7X1wntMJQBdVkZmRafLpCG8jHw0\n1yXSyczqBvGLcyHNEKHlTTlQ1LQuMcoVp0Sjucuub4Q5SeaZ6FVTQI8Rls27\nQ73y\r\n=p1wQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-module-transforms": "^7.15.4", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.14.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/helper-plugin-test-runner": "7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.15.4_1630618800967_0.12879073494643611", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "a92cf240afeb605f4ca16670453024425e421ea4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-yuGBaHS3lF1m/5R+6fjIke64ii5luRUg97N2wr+z1sF0V+sNSXPxXDdEEL/iYLszsN5VKxVB1IPfEqhzVpiqvg==", "signatures": [{"sig": "MEYCIQCgdZRQgpR2dkORCfgWZJ8J8gNpPFn4MuWt4ZQec6Ho8QIhAJy8WHm/sMivWhmKG3cQoSmr9x1LW2i+3r0oweC9Acf2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20720}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-module-transforms": "^7.16.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.15.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.16.0_1635551280405_0.3559705831384845", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "07078ba2e3cc94fbdd06836e355c246e98ad006b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-53gmLdScNN28XpjEVIm7LbWnD/b/TpbwKbLk6KV4KqC9WyU6rq1jnNmVG6UgAdQZVVGZVoik3DqHNxk4/EvrjA==", "signatures": [{"sig": "MEQCIC3eFCgKoTFD2qS3JorTowRdpKO5RhSat8eenSk6Zs+xAiBFY4qzN4leMFSTldStuHZlGCd6qxY/ozd5mru9mfNnrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kmCRA9TVsSAnZWagAA7dcP/Ruo12939Un7eqFTOQWa\ncz1MVWD1e+vQ1w4AdQU9yHyWsVvCqAFQ21f2lebDYjIZS5h01crrJyqY1TLC\nJGry+rrXLLz3GAhtKgGOYQ8ioEcPodtaCrOHRN1hqklqRF2QgenyyszRZEeS\nXGE+13hYBLVI+X32+vU42m2D5YmBNCWDP52vOddLVmITcqoW74fbR97Btuwt\njc5mRdod/SAwR0z65AGYTpKc9DGecdIhHcAWgeDvBgqe2ATpvDmny8/4v8WR\nfVH1v9OGU4v6OnM3u+9qr2yl0FAyDILFNqedV3RQpmiWzSd8+XZtEptJ4MvT\nH3vEJVXJnjF7Yo2ybYk+osD8z3bxQBC2pEIZGiB6Of7XYeOUu3c5mD/4cPqc\nmcWpXUt3BUXCLQtnxdanQqR153/70ma0nh1KzUZVqtd3BgOAEs0iAyiBTuyp\nEitkoJWe+F+mVJTQOLuO3q0VTEwJ+TgQUm4rHYlV3FlGv50KqDPBDPB1gw70\nIAwbfhYU3uWXT7PC8dH70chEOZnxyeW+zpRaQj6acralb7sJYX/Ai+KRfgkq\nxGtWLKeEDiq/KhfsdpZW1fWfwIc5910tsBTPWELjgYbvxg5uhpRXU9IEg07H\nar1dGi9L//kUUrT5rlXoFQfWlWMJMQwrdeaXekOgmr9kE9oIgV8KoTQS9qVx\nxqgI\r\n=SsHa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-module-transforms": "^7.16.5", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.15.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.16.5_1639434534114_0.7213498256200617", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "887cefaef88e684d29558c2b13ee0563e287c2d7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-DuK5E3k+QQmnOqBR9UkusByy5WZWGRxfzV529s9nPra1GE7olmxfqO2FHobEOYSPIjPBTr4p66YDcjQnt8cBmw==", "signatures": [{"sig": "MEQCID72KzniajEVPNNxGl1TZ6UyI9LrNuEK6sbVUO0Q2YBTAiBn9AU/X8dMk9NqoHCnFPGEKQ+YG2zCPtVTkkDmal6Y6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1qCRA9TVsSAnZWagAA8eYQAKR4elId8s4zBGEMTERR\nMhmQFfp2tMJ2pExoIkeqEMZRlVVSZ2ggnd878A581NaWwu7ePBtnspXXKERv\n2bg3n3o25fS3HmsQq/Rd+qcYeMICMIhlriX6sjYhUKSBT6REzD9Eo7wP5OoL\nfkyMiaTX9o0enN9FdIsVLXQn1+7KQVadbOiBpl5QxPPnQ6lEJu92fBRgCpsR\nbWd/WZulmU02roGsg99rGat1RnQgDbXqJaV0iP1j1txU45vjv6q2VGACzBVE\nyUcBlTY3qIpW1OsHumZ0Bl1SGKrb2tqDI/0RAKr755mpRFUvBO5bJieVtZfk\n0376+8berZc4hsG5ykZBLBs1kVOUtyCwf4yrLKn3SSb3XzUziElvvPpbQIpy\nlsisuTepVFP2HUBnmi/vNbAqo8yjdkZPJ5prqhdenPCM9cJ/7uIy7E1KW9mu\n5MjmGER+xxFErOufSwD5nP1nJtqn0W9ZXJDEK4u0eg2tkBJUC7rChMFXnLHq\nPuSW0oHx7br6jtaGVvYtoZ14HlZzeFDZ3dVfYqY1ajEreaCEAAWu3ScxfcoD\n3xU3QBcq583A/+UToYuHAzHtXjV+1ITsTepoDEmh06utJpI9GoNeB27HVNjc\nU7Gd95XwfcoAwEy582n678GZCbT25WGV5k5oSRWucMGmKyVaIhk8XcqJ0YJq\n/InY\r\n=CQKT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.16.7", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.16.7_1640910186259_0.9363700811016511", "host": "s3://npm-registry-packages"}}, "7.17.8": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.17.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.17.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "81fd834024fae14ea78fbe34168b042f38703859", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.17.8.tgz", "fileCount": 4, "integrity": "sha512-39reIkMTUVagzgA5x88zDYXPCMT6lcaRKs1+S9K6NKBPErbgO/w/kP8GlNQTC87b412ZTlmNgr3k2JrWgHH+Bw==", "signatures": [{"sig": "MEYCIQD01o0QjQcSeGpo2ZJFAzLtLwQaTVqjMlpP/+57MclaSQIhAOvAIPZf3t1dAmbR8kLHjEGK3eIah96zW5ER9DChC6cA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNOwNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9+RAAoUCo8bwN3DzSra9ZDmIy9DPgpLLbGzkaBD05nIHHTVshiQsf\r\nL/+3pGe16fL/OlGwBsx5njtJ/8Fo9U02Qox+EcnvNX4d0Nlo/r6rZNfMcDXc\r\nvjm1LNeCbH9xUhpHJTsMLZNqofe4kmfMTcDJdcaIJVlnQCTg5X3fKH/RjS4D\r\nfewBZef5m08wv3pFdZalwIbCr9byewQkTveT9PWZrK7jJi1bBzMrnhTxTjab\r\nVw4ViEvXmLlBTkYEl1M0H1beJWBmf9AgrsLsCFM1S9AwyWFBighmwvZFq2/7\r\ndh4YBrNVD6KTAjqaHbDTP4Pwk9LsQsHJG7K7Q2Ke+z6a1cQ0HvgxWhla/TDc\r\nvUP5o9PNZwibbHS70itg7wTk9uSKbojGre8dSdaE9xm3863X1+EJBcNxKWf2\r\nkjfzu1UOI5x23oaVdV5PLESrJgw52zidNqQ8NqJqAZuMskdjMl97/N/jAFTr\r\nU/fR04J+q2GSWv7GKpz7EqXAtCvjoZ4elPFakmQAiTacOXkt+IfVR1bvaAw/\r\nWC3KvX3G7FmEeRdHqSrKTtlIanUkblJ5jIPwIFID4mrh9NWcmTbAc60qhFAK\r\nc8eTOMvrewTpXs6MB2nUn/R8/b0HMCVOaXvQoG8fpTk+aAt3OAHbipBsm2tn\r\ngqRz1RYp8sRhZ9JGA2pATWHuPED8BemKeX4=\r\n=zdCa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.17.7", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.17.8_1647635469418_0.38278608346903686", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "e631b151b99d25401cd9679476cc35e6e5bbc7d4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-NVhDb0q00hqZcuLduUf/kMzbOQHiocmPbIxIvk23HLiEqaTKC/l4eRxeC7lO63M72BmACoiKOcb9AkOAJRerpw==", "signatures": [{"sig": "MEUCIQDgLVadWJuHFSoMgN+x/+tTir3RAmDVOz180LrBgyOLMAIgQttNQwF5tpXodCBdycYGsHYKPEfnRbBZ0bPnqF39HVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD0Q//cNcUNBCnqrx6rHpHOmqTOnk5j6EfTXFsfOVfpCv8CkV/0uKb\r\nm/dy8jWJTX/nK9lUdVdpWAmi37hf6+ZYAyAVnPUs8gWJDAhrJMkOdE1iEmEO\r\nYBkTQj2DQjd+9EaxSnIQcWH68LJ3+on8ql1DoiPegN/ixWT815+lOkI5qkbs\r\n4DF1jX7Avm7h0EYvvDqvdCXGgXnvWSnizWgVvO4oz8GUjGeQkaxMDXTgvEuZ\r\nl7saG7GNREWxRYW6V74xFgWN4miejNzVt4+NfaHvqpsy6AvYwvKAWsDozjzp\r\nNVxV1KuciHta33GhvwmjaP3D5rEbswQSBl/2Pe9kzj2BIvF+eiqMVyAIT8SI\r\nPfFi9zM3V9Bs9vmUIJNVmDL+vRoDMbVAfwD8mi5Qj75ZKWK6o3XSYXwUlvQZ\r\nPbMKFUK5jLPtwtEMibBnXupb0bE3L0M0yCWIhLyU2hBD1/j2Z5XuCv8y9RbQ\r\nuvrzHs6dWtmuOgTME8E1lolKwJ0g6xDHl7iFlRSwrPMXv/E3eONScd723icX\r\njRWaD70NblOqehmQWF9xXbbJg1oXaAwZdkW1Goq167VS7kM2ir/7R4qFBCLo\r\nspwQz9wu+H96qla9dXMGoWYrOW0m/VhxKhvjNxLEPgTT2MN0XXde4TsQGQdD\r\nH7+dRIVZ7nFhW4ee6dmr9fyq3HU33FkhBl4=\r\n=t3w3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.17.12", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.17.12_1652729595683_0.997915907417257", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.18.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "50ecdb43de97c8483824402f7125edb94cddb09a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.0.tgz", "fileCount": 4, "integrity": "sha512-vwKpxdHnlM5tIrRt/eA0bzfbi7gUBLN08vLu38np1nZevlPySRe6yvuATJB5F/WPJ+ur4OXwpVYq9+BsxqAQuQ==", "signatures": [{"sig": "MEQCIDyWsB1uf4uGcc64oCwhHVZopHuhjLBLvTYprjP0MOTPAiA8GM4zHGQSjpau6GLmy9DfHT0uFRXaPNU8u7UTJUpx1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWmg/9FsPKov39H/WcvBTcMlKwVDHIuMJcFPdFxmn4jBjn4NFgyfJF\r\nI60ceaMSN2hKf9GsEbmMNfceA4aQPvEc/BsbBDUFBieFKxy4vCTwqJoff17W\r\nXsD5YfbyiZHPptXK5MHI6o/TEu6zNKgEV3Fk8LKpRQTE0qPGQeCYcwqp5ekT\r\ntS4FlcrgjcWvDWXEhLfVBRjwLmA5t9aJpkjY4fKCcCIiWgLFQmDZ37UqMLI/\r\nsLTUsrgjoy2OewL3rSmvDkGDp02XKHrTUCbdf2z/alBsLpEV7OgLAuqfhZXg\r\n5dtuGmx/ETobpp7UTEGmHLqO9YKGn02Ri1FW+Tiuh3dhxGDWOJnKeFA8jBkx\r\nbmNrgP1y2QKaJgOsp7X6iGmSFX+X2MwXXCEmaOKjYX5ZTwbk8tg1Z8uHFyiE\r\n/g6MfSG2o65Is7MFA/SMFkQJhPdffFBSBYfcJJiNJD4N3gfB30zQpZA9CSvF\r\n2UKO5h3eB18LFFwK6qDhXimqZGk7vf4rylS5YmPA+lzSOf5jajoiRRgV8W1j\r\nh/InwYBwFow9R/0QaTFOdnHbg0UqfEXrFHvbROXra+2KUHbfjSZhCowjuJzw\r\nGLvw4RqHies7Pk1bDhj1g95bnJuEBTG9dKEDfFplCYmThLuloOD/kFHcFkGS\r\nQAKtJcUgBI1958OGkiHtKb0+6QIxclCFyVE=\r\n=6oPC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.18.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.0", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.18.0_1652984200560_0.215762090901519", "host": "s3://npm-registry-packages"}}, "7.18.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.18.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.18.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "3d6fd9868c735cce8f38d6ae3a407fb7e61e6d46", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.4.tgz", "fileCount": 4, "integrity": "sha512-lH2UaQaHVOAeYrUUuZ8i38o76J/FnO8vu21OE+tD1MyP9lxdZoSfz+pDbWkq46GogUrdrMz3tiz/FYGB+bVThg==", "signatures": [{"sig": "MEUCIQDWlb3V3ohRm03zMLBgRozIG9r/WF//biHifIZ3dXapxQIgJxhIwoo124umDr+GGjixyoiI3Y7AMsqBi6ap5UqW9AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPng/9EnK1D1arJpaBlucC8K7GbtMuu4tUKBsvah4aNrKgX8Ada3IL\r\n2Ty/TITPOU7D175yyATBKUhvN/BKaK6HnNIoIGHmUUakXfxyqf3bl/rZnPqv\r\n9G4Xo2HxhdrRieNX6RDqFrMERyhm8P9MDSN8nbAo55n5aG7M5FeMYB+rOr8Z\r\nXFj8RQlK24ePkZEGZcNC/5Wj7E67kL1tq1egclaEky0IWztSq4XlwZxTMuqn\r\nq2ZRZA1EFkt8Gtt6U9BtOQuvXZyGLgCFsFgp3v6i5VGIykUKA4pfVNigQOpD\r\nc4nbQ4lvnd57KA+K7lNFGGRKQ3FVD8FHRT5dgwh+U8iA3rUj+V+NksUtK6lQ\r\n+tcIXLBnVvJOagBwwgbS71iNYcmV9d00aZuLJoMf5XCW2Tgp6YpQ9rBeopzc\r\nGbCAeuXmrnQCngY868ecMkDlmbbyM2n57aMLBp6EPVLljA2mb1B8iOHJuUVy\r\nUdv2PWDcrucvLLVGDKe7XwrPye6ItOo3J8nAmXbAKNTXmcWdXh2zCL3XaJan\r\nRqcxUdBMtRV3hRbn1CZ/+i6vltU3uog2Vb/xyCGwfIFYcmGcprrB26Z1D63Y\r\nWXB/0/cmciueRHTkKpJaD33MkNSwq/PMX6U9XcjvDLWNDvPoy+mVENApdiYC\r\nYWb7+Cg9R/bSRm2T1ouMtmALtKDnTG83xY4=\r\n=9RJx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.18.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.2", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.18.4_1653861011473_0.5565975750495034", "host": "s3://npm-registry-packages"}}, "7.18.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.18.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.18.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "87f11c44fbfd3657be000d4897e192d9cb535996", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.5.tgz", "fileCount": 4, "integrity": "sha512-SEewrhPpcqMF1V7DhnEbhVJLrC+nnYfe1E0piZMZXBpxi9WvZqWGwpsk7JYP7wPWeqaBh4gyKlBhHJu3uz5g4Q==", "signatures": [{"sig": "MEUCIHpM3BIqW58GLBL0DxNzi/PrsVbunghw7aNcWzGGSxIUAiEAzlbB+p5r57R4E8a8jlq1Wh2tn1uJmBacnItSMvQDpr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiptvVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoihA//e4GmWeOHhagYaqvYnJ+5QLHugFjB92LnJs85MM7Lg9KAa88G\r\nac5QwR04hBSz8AF7Yd/qN5hu+y+OigdQNkiOEJn/WKCGTNgSK1EPoMT9k/mr\r\ntuP8VrdNhE8Kt94VlMoLc49TQZh2iVgivM3nFRxaSEEIrBACzTWBGEXNFcdD\r\nvfL93qxVbStCcno8JX96zv9DvuNYNgIYrfWpnEZ6xu9MsbbqKcUHK9xqU4kC\r\nupL0yVDSWU4B1GzSRdO6i5LXR8fLqCQVEAua5PnAhak0Gdak6uYUlsSN98QR\r\n3WULFCwQAWsLGeADJPZH6rnK8nAVe1eaJNTBraYO7akjmwv2PI0Xps4+T1Ti\r\nV4G8WZLG56uYWcyUU0aofq4neFij8bk16cKcUnasjWQPKepxy0evu/ZXQkEM\r\nO9Iu2ZqL4ERnn/j4Xo0LMRxucqrZ1wMcfGMjv8tNi2bdmdgwn82mMkVMF7K+\r\nJdLLO3tbQuk44TmyP0ZV2u3LKXUUfqiwsgSJhy6G2Et7skYujUYKSW5LPKOk\r\nCnkNJVcrKVLFjWVnvFQBVaLDDwMN6iTHUeMcn4fuPZs9Og1P4IC+gQ7f0+Tx\r\nxSBFzjFzshQbJzY14T8u3uHMh7G/4fRkvyC0zr966KChh9ZWBdY4YE+jcI/Y\r\nseT/VpFLeSvoLRyIWo8i5vbL+4l4DZBYjyA=\r\n=yvCY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12", "@babel/helper-hoist-variables": "^7.16.7", "@babel/helper-module-transforms": "^7.18.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.5", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.18.5_1655102421098_0.5663365787487922", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "026511b7657d63bf5d4cf2fd4aeb963139914a54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-UbPYpXxLjTw6w6yXX2BYNxF3p6QY225wcTkfQCy3OMnSlS/C3xGtwUjEzGkldb/sy6PWLiCQ3NbYfjWUTI3t4g==", "signatures": [{"sig": "MEUCIQDTaxBVETOWgi3BAIshlqA1gIr4Lir/nMmyBV2mihi0CQIgIAdJrV3UYosRP458pY7izvjrPsOCZAUyxOUwY9Spfbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBsw//X2BKYFDRAcEk/KZ98ZsL8FjHXAU1toveOcpZnaYcO/BehkqY\r\nP3/smGV0Bb655JaDOt1tlnR68z3rCqJpaSgt9A/fqC85yvTXNVmEEvoj0gQ8\r\nNQlnZQBubQhBEYj1zctvzBqYRWSKvS4dyh+ZCovFhFGzxmORlSze6Ify00cA\r\nqS8h+/5c+9ZPLYjW54yIuyUuJiNtrx1gt+IxivLKD/AqcDYiJ+CMyo+Qb/8Q\r\nlRbBh600J77UhWIFTO0PcNPrdf3IJocAkGzWweYjMP7lfjoQEJsqvPlINlfS\r\nzyWkGqEuWCFhOj93oJdT682A3y1FZLzUd9fzvmSnsc2YXWtaUN+gHqHdHiOb\r\nlLUID3ZbNTjvRJ2niHjAmmGp57qnZGWJivpY59KZ0h7M4ySOv1bhI/Apjzx+\r\nSkw0dKY/wSHJd79OOoPlikn/BRnk3FwPAWUTvejSvGKcULnaSSRioTmeECkx\r\nWF6TlNyiiA+bnKjFP8z82wmSmdDuLBQ3mnKZ4I/Ef7lAKz4jGQ2oPn8TwOfL\r\n1RWaEfNEGXYFdpnINcbS0EEEm97YWksqJ9SAig6lFPnjE126McA7Woz1zqbY\r\n7iUARbA68RzXbXrStYlOvNZEuF+n8xTWcDoYSms7g7Xd6hWtDlyUHN0qXKiJ\r\nyGkI35EcYMsFMHw2KkZ0eVbmmHik2vGJExQ=\r\n=GNf3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.18.6_1656359440641_0.6696324126065913", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.18.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "545df284a7ac6a05125e3e405e536c5853099a06", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.9.tgz", "fileCount": 4, "integrity": "sha512-zY/VSIbbqtoRoJKo2cDTewL364jSlZGvn0LKOf9ntbfxOvjfmyrdtEEOAdswOswhZEb8UH3jDkCKHd1sPgsS0A==", "signatures": [{"sig": "MEUCIHuBPe9fyY3DHIP+PoyySxuLYTUJG3bqbAH42xs/3dCAAiEA8ri9i7yz/3zCS/44FyADv2NkFzPOrYefWoguILrlG0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKIRAAoeunrnsxUYjmZnjc2qyLIrnn6TPsR1u1bJzjdVuUzluEFbao\r\nWEmY8u9bOFS97EcTtdr9nNTXAiq7SC68ZzSgsiSyDOllu7vytLdK7/7EPb6v\r\nJc/Hr1jVayEhkPi82E0eW2RSjIHmCRvUjuvtTovHBdY1YCepnj5GKC8e7b+w\r\ntY6pjvkVMxkjG19pAAMtXQrjyDDsCLYuEy8s644Zq5Ph/r+x+WrEGv04fzb1\r\nzbXxuKC9A69zKjPv/byOxfD2uMfM24MN5O87heckt/pY/9nqkchYIbZ6uchB\r\n2bmtpE1F+ry0sAG1mCD0X5Ca0nFLzNDpaYWIHT0Isjaq1i949W/C2YqW8ILC\r\nAjsIfQt6qMdXeYFeIQV5MQWN9l+V/OS+ept98GtLOxYRsUZ/S2VdCYC2dkl4\r\nfWmdun1jAU7Xm960voBz0YqT0xgxyz7DE2mELkatBLinnCXpkXuvRVqCzlwd\r\nMea511ZasGzlr+vxDhjrhk7fVT2H7wE07fm2lllccO2w+Z2hohtnvcaLaV1z\r\ncsiADobrl9OWXXWEwfKcidN4v/HH4zudIfFHQmXVJ0FBLPUnyiTZQL3KC9Hp\r\n3Ab7WlQ6OSi1o6sJ1jADJCaH5FO3RbKVkJ4WHDPKDHeyMkhlWMU82tJjEfc6\r\nwPAue7qZdVmzTHxWk7JhZZCFBmq5YMPi1p0=\r\n=4Sln\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.18.9", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.9", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.18.9_1658135863231_0.44076914494876784", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.19.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "5f20b471284430f02d9c5059d9b9a16d4b085a1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.19.0.tgz", "fileCount": 5, "integrity": "sha512-x9aiR0WXAWmOWsqcsnrzGR+ieaTMVyGyffPVA7F8cXAGt/UxefYv6uSHZLkAFChN5M5Iy1+wjE+xJuPt22H39A==", "signatures": [{"sig": "MEQCICMo6g8XTlnE+G7qiYy5kxjXtvZa5k0iWayX3sxvIgWzAiAoziSi7WMD3vZPvwnNU7oxlF6+xfXz3s400OeHf24EfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNsg/+NZF0DSwW6cpItAijsK1FvLPTmOajej6zy/bLiVjg/WMgmtSa\r\noaTuDPFK7u11BPJECxEbpQGdRBoJ2KcdNAE5dsrTgt/aEt3LWPvlYUk0s3jR\r\nwqIANWvfsmDU0k5CPZTy4UEp7DHhjSOAzG6qqnSs8JHUqnSYC0T/D9pUgcP/\r\nCxJaYtHyRuEYs/XLnQKyWEBmdteE63WsfVlyjUFs0a09/Gfod6SlJkTUw4Ks\r\noYdvNSHEX1K6HjAadf0u6g2jgNVGam499eD+fcnYh0puqPxf67Jf7EHsr2eg\r\nbO/aaGcTA4qdKowarsImLcmm/vYQHapY3d7CZLSM3tuKSsVtUKjUAcW4U38t\r\naiO9V6ufJRX1mJOevdFesB98Y1rtJW91glojweHNMonpMrXRX0zxT0LPFVT/\r\nQ71ZeK0N+V672FPij5IuF+ips4siYOrygyTV2FYkYWKKueqqvtiVIWKAXJeq\r\nB8Ajk7KKVp8Xy98NRJaxmgwihAFugRGAW78riCO1X6sGeEGxwxrch+hoJcA2\r\nmCDIYxvqW+JoXkZcSGLm/bf5V7rDJZBHuxihsexgnwNbkzp75O4Mo7fEpiEC\r\nEvw94N/LWJFpOPlIeJ0PJlgSjZMWTeJqX888HJDfBgRvTvRA7V4u9YaCvIyc\r\nkMlxxV55b5egSknZOlNn0ARxWyHNRfVrhFk=\r\n=/pCe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.19.0", "babel-plugin-dynamic-import-node": "^2.3.3", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.19.0_1662404543202_0.5392949585477451", "host": "s3://npm-registry-packages"}}, "7.19.6": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.19.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.19.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "59e2a84064b5736a4471b1aa7b13d4431d327e0d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.19.6.tgz", "fileCount": 5, "integrity": "sha512-fqGLBepcc3kErfR9R3DnVpURmckXP7gj7bAlrTQyBxrigFqszZCkFkcoxzCp2v32XmwXLvbw+8Yq9/b+QqksjQ==", "signatures": [{"sig": "MEUCIBkMWo7VueUVIHSwfha7V/+bnM/H8s3EcQnrnhbjcjvEAiEAixiiKFbwLxabv9HXV14SaFexVIDehd5arY5iLaFb0MM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7Pg/+Nw5faFW4cpJ3tknVGAfS4/BAabsVnlqMfC2cBhuyPRvOWTuz\r\nc/mzu7C5jcKR7iR5hHo0P7oXQyJ9lY5vBCxA3u6arrcgR9GgQocCC06GmbYo\r\na2osn2zKQCGNY1R/YlpNcpKegqYd2qoTPZNjWJru2sBEL9Ub6MFPXqtt7oGF\r\nYIW6tlXRvlelUV1x9sv6moP5dj4khpGQNKusICwCWajSK1z8naBZ10oO2b3x\r\npCBPf58+dgPaQM5K1GbttJybZqmCO6VCwNomr01yOw2+eGUefFWJ58XdVnnj\r\n81x9X++jpOuTDAfuZp6HDUD4jTW23EsC4HZvAL8H1Ve6+vW3wE5bBYRc9XHq\r\n2G9exrpBYphTAjUJUZHFZVLGuSPzJdi44cXGKH6CyF5o+edst7gJT4tGNlue\r\nQ2Ej9CpPK0qHBxllLVp8siLAb3g7/Qnw6NNgGIoFfe31vGiFpwSvPgzYPF6w\r\n7hpu2Sfq3CC5rlydiOMr/qGQktbtJ+1Cr9AzJNYz/GRmQhrewWm0qwwJh2wv\r\nRBcRN4/gJfpKddmRL5B5RA4yAIILvviX/WsA03PZkoBPWCvTYz4iKsyq1Ztx\r\nZdH5+ojR4vbyEXUTyiWAkrz9MUkRFStegKGE6qukzyFpbOAzZrJYR2HF7anM\r\nNYyiqWkq6+659wCb60sIDq7cn2Gy/13Zglc=\r\n=qOrv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.19.6", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.19.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.19.6_1666256615759_0.8901596558378406", "host": "s3://npm-registry-packages"}}, "7.20.11": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.20.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.20.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "467ec6bba6b6a50634eea61c9c232654d8a4696e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.20.11.tgz", "fileCount": 5, "integrity": "sha512-vVu5g9BPQKSFEmvt2TA4Da5N+QVS66EX21d8uoOihC+OCpUoGvzVsXeqFdtAEfVa5BILAeFt+U7yVmLbQnAJmw==", "signatures": [{"sig": "MEQCIA2zI1Lis604i7M5SXMNmLhBkVzaNevFcK/apB6CXUCMAiBPIrXTS4d4HVP5KyP6ICO3QkhhQBzh6zzSonQ4w9SICg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjphi4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSNg/8Dzo6uNgJxj86PaRCPWLW3mZXTQqVoOP7kxN2Tt9wbBO0X5FP\r\nO3rrRMNgmak6SmxFDx9vh/waukZkbOnsYBHMWWPKpLA7X9qVTTYZhu5JkNTX\r\nC3DOPBvdok5KZY2m55PO2QJ8xPoyoZ9PhjvVehhcgvK9fPOl+wmvsCzw03Sa\r\nAJNn7Fvw6NlzoEC6+d9ff2Lqq4ZqefGqP+aDJrm6CZD6YZR1b4r61Cd8bfnz\r\nouEvQ0OVg5fXOMfYE36TDFbhc8fEh6H1TpFc7kyqkfusVmj/eOlDVUqNqYzH\r\neAm7CUbeNEePl/HI6w+Y5I2jda0AInORPgOAK0Zn0fCfbf3nBicO3rV3ASFJ\r\nOX7EkkLlruoHnQjWGRJl26BGr01sY4W30/bXx4MW1Tvts2WRiEOG4TdfskJa\r\nMmT3F5mA3qK/iFVa3rG2e7gNOC4ruAcUTsxKTevvBMyxubVe3rb44L8l64DY\r\nVC20G7URlDmVGEMV6TYD637fsx45l0VZHh/4HRlSlGRx78onQFyhbFu53lA7\r\n4BnPOXI0rXnZGGpUAW7dsp81f0KWayORUzDtHPHiDkhq0JwuMixU0lJMDX9H\r\ny3uuFhlFgYwDB/IQ19CJIodmbIOIQw92tnCfsZ8IPx76UXOdQnU/eb+N9DFI\r\n6+AdVZacCeY28Ah0I1is0SkTrwHIle04Orc=\r\n=FiRj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.20.11", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.20.11_1671829688396_0.5108442778216726", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "411353b58c9d731c0f27b8c6dfda2b2dc5c3a00f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-VmVV9eNyis6Xk450dLzO6BRaTw40OPdxy71kEI3Rkbyvh+CfchGMwV6CEXxCuIgfQ7penx1yvSDqEYV++GiLMg==", "signatures": [{"sig": "MEUCIQC7nglR6yf7Q++sp4Ku89geZEC5j4r5Lu69y11SUmmRiwIgMccK0Dalx5Nv2epheIMLRqhvvkL9GaYQN88Qe6/kY2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPXw//fD6b9g6i5nWp2R8J1p3uQqReaPLWqg/mfxLYddpxCFTzS943\r\nhXp+pn1S877uIowYW1nUWdKrVo/F/uR7yrLrJ3t05RvOMNTOKMsVQcj6BHR1\r\nOu+CWnOLk3nIidMvyqMEyPgeffkS1thbaAOjmb6HWrUkdJCTy0Dpof0kiAfj\r\nEISn+xS6yEro20yKY0+UrNE8layh08Gxa6lQLoEGRLRceXz/XoMK0Fw0tV+A\r\nYdqL6xNn2zJ/gogUN3wL5Zfxi1nv6VPTMs+hSEk0Tg5JEDc8R73ZZ/+fPLYn\r\neUlG45+HD7MWHQ7KwDqj01BESQ7gx8gHCmz8VOsr1BA6wPx68JrB5/5bnIHF\r\n2+1Gw8Vj5jP5lO1c0s6+mPJz3ptZqfZe11vzNk7bVpJs085HHd3a4HX51W03\r\n9LFeG/fvDhBpx15VHSai/at+D0BMJg0kCpmU1VkDF22zsIKQZWvXZax/kxU/\r\nm/Wn8I6PYI4VqtkHP8UKhFM2aZRE/hU2ZQWpx2KwixHFrqSfzu45r+arxnCk\r\nGo84lz2BXcauLEpIsFNTXlNX5EdLzreK8p6M0PrX/CyiZHoU2LeXFiO1OuKE\r\nMy/ppxLjIl85afnYj18xJR/RvxsqIqFOhEjQpZS/3MiVM8nagVH3S4xCV2G3\r\nshhE/2KMMLgcogshlDrqy3JVJFaSF4yDqyo=\r\n=6lZY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm", "@babel/helper-hoist-variables": "^7.21.4-esm", "@babel/helper-module-transforms": "^7.21.4-esm", "@babel/helper-validator-identifier": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.21.4-esm_1680617397532_0.22085448494622417", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "18adf074b1690a4f01ad839c5618bf1c7f25e431", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-dQIDtm/UX731h9kLwfHZMEz+vageZIjW1WIaKZraIgmwaa9sBf5+awJKyStRVf2NjOotdioC0lyQf9/1s+P8Ig==", "signatures": [{"sig": "MEUCIQCrMgApFDFBcAoIhKCG52dPdSREoSSVT6UPNtpkFAaOUgIgRH1BYotOJMzIEwFqI+dtVYThaDHhDSYNicesqqraH4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqwhAAhUZblRmTRjNo7qNIL+V+lkTmYsXE3kD0FAyC9lhCCxO84PBE\r\nBDEBPHwIW61p/4y2cC8+56q3yXiVuAjYmYQW+/RBlzKevxlCjQowts5Cv4Cg\r\nvURcU+Aqsh52VlPPnDOqMcNF7wXkPu4Q5dXAMcQLP/JIzJ+cDsbdKPGcLsQa\r\nD6MG5PItVjfqRHRXMJaG+afaMB1Xr7N2+cqg05BoH2h5GGBu1t4CJAWEHMN9\r\nMKK4ZK+5Vm3/xDkz3CrvfkZZjZ76XFI2BZ+YZxKz98Ku7//s/AL+ilf5Tvp7\r\nQuhvvG3oU1//lPvn2sBoQNLpZcIUMQXUc4Vr01iyc5QHJ+vSdxLRAw7uMd/I\r\nS6z7l+xrFDiYD91ul0Ctch3xUZxBaNFcu5Q/DynUMykzA57nouyLwrbF//BM\r\nY5h9Vwe3WY20jGm0dLpeID0F7Oc3psbbMJWB4isDjFldnBKwPCczCFEMfkZh\r\npo5DBvWU6o4Ft0+OuGYsjIN/zO1PE2KJGAqS0yz8/zJOnCl1l+jjHk73tON8\r\nGlJUMslEnuoHODCUnombuwYVP5mgxcngkHjad9cRM3GDnwQO8zvdI9flyNCt\r\nU6URbERqE1xZ9E8pFrBl9CsGhO8JfkGkkfkQ2UAlPUM7JRoJJ0Je6Usg7ejh\r\nzc1s+Y55VXm6RIQhYYfPyokztutoajicDRw=\r\n=IQ5p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1", "@babel/helper-hoist-variables": "^7.21.4-esm.1", "@babel/helper-module-transforms": "^7.21.4-esm.1", "@babel/helper-validator-identifier": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.21.4-esm.1_1680618115248_0.22529635978209117", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "c431199e8a103ac766b62441df5a7a22e301e6dd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-+n9KEX2CrIDHpbn+k75ibWC1kncHypYkYqL+r5jXVe+xojpSR8OJGyK4bWffQDB+wfcz0vplxr3je+jo19cNiQ==", "signatures": [{"sig": "MEUCIE52wUiP15B+Qh6e06OpI1P5Tdqcz+Gj0OoB85oLOc5UAiEA4IjU/u7qZCOTyq1g/21OfrjcrFXwZbzf6YEaSeiAMgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDbAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0ZQ//aT8qHoNXHlpdfUa0DqfZDFd6kaJ4/MCSt2zPO4tcftfi+iE1\r\nhyqm/SBefq5s+hmPXH2XftON9asnW5ipdQRoHF5uxuQHN6YaPAYvGfIOXM1B\r\n2gJfpbV0NqjCVGDTNcwz0v+V0MgkIopXP8hXg1Gt+uJmOEhIORa2Op83N/zu\r\nzlb48+blLZs2C6md3qLuHuU/aWo3JrC63SRsVzB8GxBvMm/NVxKOZDQDTidp\r\nwkLDq3JJb3g3PiNhw2c5WP38H5VVYs5fy7T2qfm9y2LWPIkGo/WLUzCAxtuU\r\nEHkRH773G50NqFC8wOzb76OrPHuK5zf4QTGbTZyftAFIcJzy7dnB47mK8nlw\r\nsz2d9dedLZO3l2mqSZZsmCo+bMpbWrknnuvS2LbOP4QBPL43oHAKjAor5wXT\r\ny5m73Lqdl+DBRpotnkEJ7I8EFYbbgX/60N9yZwMKYVigbyTpNkEU2lUqa+uM\r\nx1s1RJUCbW2TDJC4sCfYu7rHAIBbQCjiJPSpBPd/vJ7gRM5EZTSAR+XDRP1z\r\nS6WZV/IqFlUysNRjZCZ6UW7QlM+rHBE0NK47zGyCCAS5rcGBHUnguXjF81Zc\r\nml3tSjJcfoxZY2rg2dhTOlYDwMR0NDs8R1SKIPgDJVPAnhmp4sjgs5IEw9JC\r\nAlT75HYSaaMMP0gp78v/Fv8CCJhbpmFiWkU=\r\n=EhZ8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2", "@babel/helper-hoist-variables": "7.21.4-esm.2", "@babel/helper-module-transforms": "7.21.4-esm.2", "@babel/helper-validator-identifier": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.21.4-esm.2_1680619200538_0.5305936400993663", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "5a3c3a52d5e7d831956c56bf232ec1b19e1fc0d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-KfqwRT7lou/ZX4sdFoGRwDasadHBBL42tbyIIe5E/NA78GnYs2CqEcwAxPVCP6fT+9KEJOaPmIQE1YYx2ekL8g==", "signatures": [{"sig": "MEQCIH7MBHPCrEtzDr1mimQNmCzVxO+6eGtm8JOSSiuWLIDZAiBH+3iFM8G9z1lCwaa+Vvbj6R7Nn38twToG+XiFC/bzDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3NhAAhUJg4VSPrLDf+fA408rifXMiK+lTaExkjMYgeu3IPtvhYPZZ\r\nuM3SOqvzpuFVNkO9p8KQapWGr042dIYOQ8NdCEjB8m6bT1yXbnW0Igzsszin\r\nVskENFhTqh5Ytl7m/UORi4zWWrzd+EEKHImwweOMIEqG3XM6XPKfIHPeGyTc\r\ncCPiEbJr1PsIBvOqAcurvdL6TnjOb83r1bbCk5/WpLt+0t1mZ68T6/nyNBhN\r\nM1LS3vgS3+5A/WsEiX/lo4eBV2Htu8DfGUsP9QsJ0yh4KAJtuO0pbLInuCc5\r\nPFaeWxy5xSceIzLef7c0zgsRc9vHH1HUb2iyJG6+QEzJNI/MjMlqDDcUXzEz\r\nHRuH6GgMvOVACHh8pgLIEcslJwdlS0y/jg7Bi1mD/xo0Z49llLfpMVQYYq19\r\nyiPQz0TbTzaIcMpOs4T1ZFy1S3++aM0LFzfm3gKCAdYflPAGKdz5RHjrKMyV\r\n/PhusIVJEybdfGYmOFreWt5RFKm5jVTB9+OmrEpfh+y8a1ifUCNnbYWyLmcC\r\nsmSlM4qOjQUSs4zgIHHJTtqHsSAAd73OXCdndiiBF6beeF1ATqbd6BvpNqzr\r\nSrAbj0DqvIe4dILMnNDTfGp+5MtT6alDFxZ3gihz5r3+JufVznQRZ7HjW1dE\r\n/yYqYB4f0uEPrt+w2bsx7TfIxlDceRcMDpU=\r\n=23qr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3", "@babel/helper-hoist-variables": "7.21.4-esm.3", "@babel/helper-module-transforms": "7.21.4-esm.3", "@babel/helper-validator-identifier": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.21.4-esm.3_1680620202598_0.6289799319125933", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "ecc941f4b02fe3dea2027a24520216a0241d3123", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-/Ico0UikWyUqxZ68n+lIgeMVCxjjzpkMZVu6JhFuVZHjck+PapRpxxktQ4eNRNLW/D0hZtS5Pa7nhIAeKNA6WA==", "signatures": [{"sig": "MEUCIC1EBVPA96SSKLJDMIgkoc47HVFjJQLvBBJBzVr9r/B7AiEArLxDWskP+bkgU7sKpok2XqHipBlC4E7l4Rxk7vFhS7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIyA/9FJNM9us4ftbXtIsfvEo4UZAmY2yjvjHxKLVGzsNTQPoqkt2R\r\n3GUPeo5THUZHGbsEYBlJKtPBvpbsL/tgSm4+Ojqx2hxxbPFKW3/m4CGS0QHX\r\ne9lSOqInwj8kOuus5D1rAESRkjQCEnuYJoCQKh3MTA7pnKYJUiWj7wLuLWpD\r\nAZ4Px1v2W5WHRGYXo4Wc4ziVppeBeJKPuS1b0u12gofFKm8JPxnmOV4uM08W\r\nxEOufvWBXmpuw89tQfsrU4sZ9vRdJHhQSr28g5KcidgwxhGWqHWy98P/zZ3u\r\nHi3l4oKo2cO7mtr4Y1XPEpzbrS01R95Tcwh0TOv0ULW7UysMPpP4m/YzVcNZ\r\nkgKHQwrzPsHRPJLmDj8O7tI0tobh1U17JOlyywNVnkLmj0NfIkmHimXYAnlC\r\nrmjAjz7fFGhVGUmJZfsEO/7Q7oxhp7lM5EU4CQj22nH/HRBLgQYyo5MB9D7w\r\nSPQimIs7PebY4RQHr0xXrUPetQr/xHuzu4h1gla254LUYqe/oL7h2/S8fBPM\r\n6oWlBHdmpPM8IRNVaJIqSUth/4BIZJAMXwsA3v5nsoMufQ21GJOAntqMzNmF\r\nWswveHT86pnrJ3V3YVEBaH8EqJu5eckMmhSE8wHEX+UgMfmUOPx51zfCNPMP\r\nU/gIA1pZLiWi9AlL/1Ej3PpAexpgPYRUuYQ=\r\n=kf83\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4", "@babel/helper-hoist-variables": "7.21.4-esm.4", "@babel/helper-module-transforms": "7.21.4-esm.4", "@babel/helper-validator-identifier": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.21.4-esm.4_1680621230734_0.3000862186890405", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "ffaad58d858f3efc1f9135cdd47f8ba1cc99a282", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-hSo/4vBjCjwsol3nLDJG3QRDuNzvzofnyhKyCiSXpzqEVmkos9SODFC3xzDvvuE3AUjHUMgTpTRpJq16i62heA==", "signatures": [{"sig": "MEQCIEibnS6h5aMqFOQGukLFcZgQACQQBnGMXtWljrKdNjaMAiB3t79s1Ls5tXMrrZG7m6Bw7xmr2oRzen5+suw5lWfE1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64493}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.22.0", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.22.0_1685108753711_0.15482100877839033", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "cc507e03e88d87b016feaeb5dae941e6ef50d91e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-V21W3bKLxO3ZjcBJZ8biSvo5gQ85uIXW2vJfh7JSWf/4SLUSr1tOoHX3ruN4+Oqa2m+BKfsxTR1I+PsvkIWvNw==", "signatures": [{"sig": "MEUCIQCmgBVBKuoLZFMBM7fK7UvkoXWEYs07Keqm3bDDeIN+RgIgciF1skBx9Tgh9bC5FYK0o1WvBN/5BCxA1Bf33MwPEC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64590}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.22.1", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.22.3_1685182256639_0.5392849901642578", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "18c31410b5e579a0092638f95c896c2a98a5d496", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-emtEpoaTMsOs6Tzz+nbmcePl6AKVtS1yC4YNAeMun9U8YCsgadPNxnOPQ8GhHFB2qdx+LZu9LgoC0Lthuu05DQ==", "signatures": [{"sig": "MEYCIQDds/qkwMIc7sJ+3mvzNbRCyXQMz+pUX1XcTZr5EFITUQIhAOzsxhYY/8B/J7V9z+JP6KGUl48rYkSDMVewOQFDyNAP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64590}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.22.5_1686248505567_0.8359548129929748", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "738b0b7f3fee70b6d37132099db06580289efa75", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-XNq6peFi+V73zUBagu8V68YTqA0fs2S4oyr4Atp/keDlOO4T+RSVSs4dUUA911TGOofYLk6qisPSnSTVbFQS4g==", "signatures": [{"sig": "MEUCIBWeEk2158klWpKhd9BZNIt8Uf+PDmfUPaidYnQiHnswAiEA4mEVYcGC1eG8ospXLyf4VHGfaYI2scF68LGpOO7wifI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-hoist-variables": "^8.0.0-alpha.0", "@babel/helper-module-transforms": "^8.0.0-alpha.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.0_1689861621705_0.7606573336284146", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "feef43114ec1566ebee7b1df303255099dd97a1d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-i3fxuW8hw+LdtW2vGWKzY5sT6FeXSL9FBRl7BwT1qBMO+U1McLVN81X7S/tLJ+C8V9rCogiqrKGihCzlHboPIg==", "signatures": [{"sig": "MEYCIQD6MdzYrcORr7nikxmR3bLG/NOHVNMLDDGFyEcwqkoU+AIhAIt2Sen5RAwt4R/NFxFF5x7U7EdoPlqv/37IZ1PaJ1Xx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65005}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-hoist-variables": "^8.0.0-alpha.1", "@babel/helper-module-transforms": "^8.0.0-alpha.1", "@babel/helper-validator-identifier": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.1_1690221175028_0.15860794968586522", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "c4391311999afbc282449faa571f95918c94242b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-4vArVlhpJJE+M19L1wZI5jAuBaYAOhZaA6Dxsxc33TGKhx5FrhQavqc/gZjtPVBq/7Ei7zmlvUzJUe8+5ysVAg==", "signatures": [{"sig": "MEYCIQDJGIa9wpCLwSl1Fa9H8Uzxu/bwNlDAh6U9H5O2ZgWYTQIhAN+gyaXAYG9nh+b9B3SbSED6mM5F1/tzGguLUD6QUgAJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64952}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-hoist-variables": "^8.0.0-alpha.2", "@babel/helper-module-transforms": "^8.0.0-alpha.2", "@babel/helper-validator-identifier": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.2_1691594118001_0.8601458195059877", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "3386be5875d316493b517207e8f1931d93154bb1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-rIqHmHoMEOhI3VkVf5jQ15l539KrwhzqcBO6wdCNWPWc/JWt9ILNYNUssbRpeq0qWns8svuw8LnMNCvWBIJ8wA==", "signatures": [{"sig": "MEUCIClbzKqIJQ9UU1MKrzQZtrC8127Q2oODnJw26TJfNPfBAiEAm4KsixmaCdoG7In59xL3iQWLnnUrFhlQzQwqJkcl/zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64536}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.22.9", "@babel/helper-validator-identifier": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.22.11_1692882518872_0.9748955930632588", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.23.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "77591e126f3ff4132a40595a6cccd00a6b60d160", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.23.0.tgz", "fileCount": 5, "integrity": "sha512-qBej6ctXZD2f+DhlOC9yO47yEYgUh5CZNz/aBoH4j/3NOlRfJXJbY7xDQCqQVf9KbrqGzIWER1f23doHGrIHFg==", "signatures": [{"sig": "MEQCIEo1eKOc1WRI4WZLFHALKnW1E2Hw0d5nngKAomIMoBrcAiA59/hWN6wNda06qSzzjYaORtYZBOx/TWue5IU21ObOTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65651}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.23.0", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.23.0_1695629492657_0.9546748815731159", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "199af8fe40e3f6aabf16b1d7c36a305bed75f96c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-wxwJ8wjjgWKSKF6obSrlymMsBWxbZ/zCdvXZMEkepkNu8TRPDVoOa7/BbCgcFtjmyZIcp35PttsFwYc82TcRSg==", "signatures": [{"sig": "MEYCIQCMWjJJKXqDEukWHiTTmfJx96png+2zDExodNE2lN3rRwIhAJZr6kBXSjrjf/zIVRzrckl7+IJuQhIpsrhcE4uvfF+M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66092}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-hoist-variables": "^8.0.0-alpha.3", "@babel/helper-module-transforms": "^8.0.0-alpha.3", "@babel/helper-validator-identifier": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.3_1695740250667_0.21153741798929548", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "1546b929c6587ada49782043b9d5eb19c4ade518", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-IylLWfkDnih3vCWEiovCqfD9fgnyceM0V6hzkUG044yJj8FG9tc0GgVpH0O8KCUJ9Dag4WE+SZujKKqtYhq86Q==", "signatures": [{"sig": "MEUCIQCXde/a2B38yHd3akj85zT7nNBwbL+t13ao1R+z+sWjDwIgeM5sgR9UreWOkR8fsePUy68Sj+KGU2LvZ73+AF5BIxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66092}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-hoist-variables": "^8.0.0-alpha.4", "@babel/helper-module-transforms": "^8.0.0-alpha.4", "@babel/helper-validator-identifier": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.4_1697076403811_0.7930553636246469", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "fa7e62248931cb15b9404f8052581c302dd9de81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-ZxyKGTkF9xT9YJuKQRo19ewf3pXpopuYQd8cDXqNzc3mUNbOME0RKMoZxviQk74hwzfQsEe66dE92MaZbdHKNQ==", "signatures": [{"sig": "MEUCIHWfgBAfRorXVkG4sH3DXkqGuvEx1asWa3D+H2mgB7QyAiEAsd4vFxSnFVXuF7bWZqyM4W+L6kkVNlc2z1/bsaWppS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65733}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.23.3_1699513452883_0.01948173668206965", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "ef158e6d1a275f9f8771d913dd6c77228df5d1a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-hc5x0IlyJxnWzvU20+oSWEBAD7FLPdNXtAfh8O/hLpbz5qDvbxzqoy0YhDGnGKvIDJlgQXwggN69jsvLI3O+Vw==", "signatures": [{"sig": "MEUCIDgL+x+nfTNZyBEYU7VGCiwbr7aOj40GvMpe/iuc/Tp4AiEAsETyXexQk00an4cdgWPdElthk1vmutvIUooO0qlZHSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66205}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-hoist-variables": "^8.0.0-alpha.5", "@babel/helper-module-transforms": "^8.0.0-alpha.5", "@babel/helper-validator-identifier": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.5_1702307975714_0.5065757763457859", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.23.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "105d3ed46e4a21d257f83a2f9e2ee4203ceda6be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.23.9.tgz", "fileCount": 5, "integrity": "sha512-KDlPRM6sLo4o1FkiSlXoAa8edLXFsKKIda779fbLrvmeuc3itnjCtaO6RrtoaANsIJANj+Vk1zqbZIMhkCAHVw==", "signatures": [{"sig": "MEUCIEad1qKFeyuhoykLD/EAOcPPs+GY7Z8UKNh267hiFR80AiEAsfSNIyy5AnYDhxkAgb6HgVPhN95nsZDR7MnvGdl4ejM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65991}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.23.9", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/helper-transform-fixture-test-runner": "^7.23.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.23.9_1706201868038_0.4969705286242829", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "4e48886daf26f2490d215800a874742e14a80447", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-4ohGV6T0TDO3rxfOE9BEwEIuvcicFGlWKCPToBr/9Vg5FYbjptJ70fQ5NBgeFOtGHUwjQUt6QJLX5+Zs+VCMvQ==", "signatures": [{"sig": "MEQCIGs3d+Q/7G8tulaOPKk9iHDwdRri+lAG0oJMhrj/XESaAiB+YjSAPFnyfhRpESg2iXcpLYC3nwpAacAccOnmEGdggg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66475}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-hoist-variables": "^8.0.0-alpha.6", "@babel/helper-module-transforms": "^8.0.0-alpha.6", "@babel/helper-validator-identifier": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.6_1706285676026_0.48213472757582165", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "4a063a373ae5a3f452c46f5ed43fd0a5ff855116", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-BXv94NnKWfW5e+9BRwgK9HrGVl3LkUukFfDuowHFqkhW05K5Ki/6CvfeWArCQct9vh9e5++5EbvoL7Vh9oJz5g==", "signatures": [{"sig": "MEUCIExBPwEd0EvSxFr8grOtjXi7uH5/Viy04/+3nF6xyF/cAiEAme+RbJuIi7qUty3SI7zs7ZVB+XsH9LtH6ljUmFqswUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66475}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-hoist-variables": "^8.0.0-alpha.7", "@babel/helper-module-transforms": "^8.0.0-alpha.7", "@babel/helper-validator-identifier": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.7_1709129134963_0.6651056212280197", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "2b9625a3d4e445babac9788daec39094e6b11e3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-mqQ3Zh9vFO1Tpmlt8QPnbwGHzNz3lpNEMxQb1kAemn/erstyqw1r9KeOlOfo3y6xAnFEcOv2tSyrXfmMk+/YZA==", "signatures": [{"sig": "MEQCIADx4eNDdq7I6FGZ37XJm0chMFXS++OkwvfqF6Dca1f6AiBapV06yv5ZCSfEVCwZmkKVaup46LWyKC9lVqY1qKh84w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65922}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.23.3", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1", "@babel/helper-transform-fixture-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.24.1_1710841721853_0.5771997625706793", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "7496497f80bac1f8e6eda6cfd567779645ae9e5c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-T5xRV1H4B5gEssCb7dDsMKL7GhDP7GPsi8hMUGqXdg1/hzGUvinar1Tj/GdEL3YFHbMOkK5EPvPmN/VWEJhL6A==", "signatures": [{"sig": "MEUCIHU2fhDD9H8r32Nhsh6h6ICwFOqwBD02u6uRt30mgAfgAiEApnP6mN92l7U6/I+x7hMBQUtwKWZNNRU6cJE3QAYC8dY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66389}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-hoist-variables": "^8.0.0-alpha.8", "@babel/helper-module-transforms": "^8.0.0-alpha.8", "@babel/helper-validator-identifier": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.8_1712236813826_0.321674650356516", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "c54eb53fe16f9b82d320abd76762d0320e3f9393", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-xg1Z0J5JVYxtpX954XqaaAT6NpAY6LtZXvYFCJmGFJWwtlz2EmJoR8LycFRGNE8dBKizGWkGQZGegtkV8y8s+w==", "signatures": [{"sig": "MEQCIF/anrp/NJhUMgQAeany2zeMfb5QNbUmZcwHsgWP69z6AiB2wo33Bm0q3Ei7sHojWX2zt/e6tGWrXMhLl831CNkUCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134041}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-hoist-variables": "^7.24.6", "@babel/helper-module-transforms": "^7.24.6", "@babel/helper-validator-identifier": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6", "@babel/helper-transform-fixture-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.24.6_1716553505183_0.8809828122755872", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "f836c9d57cb927c9ce7e94a23625e9c940280a30", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-Cwn1z2guEGvfv7kR2AOqKAuDk6tXl0pOKM8bxwrGkx+Olrh00lw/LK3qxQnh0Ep1kE6e6Tq2yPWW0r7MwsXxEQ==", "signatures": [{"sig": "MEUCIQCECBunRAPvuo34izpvNpVQitvQ3pOLmjlOaPCmJrXeSQIgI2GfhCuuIfVyA21bXRSwNzrBesb9sTzpDPtyM0dPXZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135183}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-hoist-variables": "^8.0.0-alpha.9", "@babel/helper-module-transforms": "^8.0.0-alpha.9", "@babel/helper-validator-identifier": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.9_1717423543858_0.4238569697008403", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "4542feb5bcc069017866dd4cb6dab388b611fdce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-PIhJAh8zArrneYpZVOkLfllIAKIA3pkzn0edRSOt3aYIumbaAUcN9YFwBMQePsgomJzUE1z+JBaZNzsa8h7PfQ==", "signatures": [{"sig": "MEUCIQD/bFwBr43qIBLco9mD6Ea3Ags2gkI3CdVwA3V3ARw6qwIgWtLAVhZELWWl211dvEDeXVUx1de3HRD+lZ7eroKu+W8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135194}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-hoist-variables": "^8.0.0-alpha.10", "@babel/helper-module-transforms": "^8.0.0-alpha.10", "@babel/helper-validator-identifier": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.10_1717500042730_0.2773645432451517", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "f8012316c5098f6e8dee6ecd58e2bc6f003d0ce7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-GYQE0tW7YoaN13qFh3O1NCY4MPkUiAH3fiF7UcV/I3ajmDKEdG3l+UOcbAm4zUE3gnvUU+Eni7XrVKo9eO9auw==", "signatures": [{"sig": "MEQCICO0hid2LFamAwMPER0XQvObDvh2tuc4GJMLU7ddApBaAiBeOf97KDbW0+AkYO3Q5gmOXK1qXKBYqBFP5RLTULcSPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133996}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-hoist-variables": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/helper-transform-fixture-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.24.7_1717593356119_0.9571038092281774", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "b8ad9cc4522f132a5301a4482155c57d4083a1a1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-8Fv1jcfN2nNFzyOjSOzUDc11+jJnq/00bPXwZaHPQfcecJbWMryQv7+BsLhYcI11/0RF+bCPnWkMhlsF6tlwpw==", "signatures": [{"sig": "MEQCIGB6YLQv8w9w0xpsliM/4Zsio95bnvfS8ic7jL8+ayr5AiB8mdWU2QbAEvz2dUawC6x7pB+2/1KpXFxHJO3oBihvmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135083}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-hoist-variables": "^8.0.0-alpha.11", "@babel/helper-module-transforms": "^8.0.0-alpha.11", "@babel/helper-validator-identifier": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.11_1717751766625_0.11809771925351642", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.25.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "8f46cdc5f9e5af74f3bd019485a6cbe59685ea33", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.0.tgz", "fileCount": 7, "integrity": "sha512-YPJfjQPDXxyQWg/0+jHKj1llnY5f/R6a0p/vP4lPymxLu7Lvl4k2WMitqi08yxwQcCVUUdG9LCUj4TNEgAp3Jw==", "signatures": [{"sig": "MEUCIQDVES0Octo8WoxF7MEtbEFbV6/xhAHEJ0PTG5wLM/IUwAIgEbm184bqOjWOio6bEMTLKct4Z1uVfbv6c09eKN6qMOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131070}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-module-transforms": "^7.25.0", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.24.9", "@babel/helper-plugin-test-runner": "^7.24.7", "@babel/helper-transform-fixture-test-runner": "^7.25.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.25.0_1722013172103_0.07816619894297694", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "056fc21e55c230683c2803a68d4b19bdb61438d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-lDbegy+871x6teWp3L+znVIZw5zUfsbQK9mUJQtEjBK7mbgGaXkvHsTMxUB37GZbXfruLgZviUgxC/4rh6M7WQ==", "signatures": [{"sig": "MEQCIB9F26Tza6gmPVy45tVghJy52JDW6mYN23luNB502amPAiBdTOCQuqgynVBSegM567zpvHfc8e3CWKk1QeS3/dF2RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131759}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-module-transforms": "^8.0.0-alpha.12", "@babel/helper-validator-identifier": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.12_1722015241733_0.8930185566416862", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "8b14d319a177cc9c85ef8b0512afd429d9e2e60b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-t9jZIvBmOXJsiuyOwhrIGs8dVcD6jDyg2icw1VL4A/g+FnWyJKwUfSSU2nwJuMV2Zqui856El9u+ElB+j9fV1g==", "signatures": [{"sig": "MEYCIQCckBUNZKhCShHQ8jN6ihn16pbZB0FUVPQsoZMaM6QIWgIhANoaSjiylKu/zd7SldW5dJXGL2y1MXmu1ZHbcCu+vhb1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139144}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7", "@babel/helper-transform-fixture-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.25.7_1727882131296_0.33693399783191746", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "8bd1b43836269e3d33307151a114bcf3ba6793f8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-hyss7iIlH/zLHaehT+xwiymtPOpsiwIIRlCAOwBB04ta5Tt+lNItADdlXw3jAWZ96VJ2jlhl/c+PNIQPKNfvcA==", "signatures": [{"sig": "MEYCIQCHbMlYacpq1XDPwRww9qZ1ZOc1H5VbgllqCCw35L2wIgIhAPQBba4z8T+MHhjMEXUU3wPdHk2HoBlQWlxpkosERUh3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66605}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-module-transforms": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/helper-transform-fixture-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.25.9_1729610507168_0.549710485549699", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "3187ff354ca71acaf8731b75f6636cee0473842c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-2XnnrHnWukQX/m0gk4amS/DoBNauYypB9ombAVk+gRGCdLT2aVzut+BbrhXZ/N3D9XqQ0PdvgIxt9xYmLiIdLg==", "signatures": [{"sig": "MEUCIB0FalV31fnfdzPBaj137jskY/bbUarQ7aR2rgRbtPrmAiEAvXAiLd4NZNi99PAYp1iMQGxAWVWZ47Ja6V1xj/B9TEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67302}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-module-transforms": "^8.0.0-alpha.13", "@babel/helper-validator-identifier": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.13_1729864487487_0.7850446257862458", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "c459c27462e1139fdbc23d12133e6861a6935970", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-witwgounZPgNgjFKwhbUnF76Lx4HR4o2a6qsRvYo5c3Nb5bHjfb6fMwe3uAdA6QxKfnGReM/j4AHvIeIkHcOrA==", "signatures": [{"sig": "MEUCIQDDd5s6pEMwUssVTQSL3vPeplEOnPVYue9eaN6v1WPgpQIgYHJEtrukMp5qDAnde3FpVdcPywvNMWh1JV/pscCOcIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67302}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-module-transforms": "^8.0.0-alpha.14", "@babel/helper-validator-identifier": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.14_1733504076981_0.6410926790549409", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "58c7a158d6074624cc8ea595b6c17ad677814727", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-uZHKBbDfzRWDmqbei1uiYNnAJvQ2PIeLAlKgHNrrty6VyMmKDVWbrpYep1X2F8D3zfgXmQiuGnssUS9gmtG0nw==", "signatures": [{"sig": "MEUCIQCR4VjOvZwPSE+oKAYnqShauDbwPgw1Pwqjimlx1zlDEAIgCgHP09kiaGYIhbLBtTMqMdxMWM2P+GqVNrjUibCpnFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67302}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-module-transforms": "^8.0.0-alpha.15", "@babel/helper-validator-identifier": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.15_1736529906376_0.35992264915085026", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "da6ef45e829016c072afa8edc817425ea62f1270", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-X3qLA6apyvNUaRFCVov/9RcI4VxfDhWSn79VO7gpNdeSjCMIRiHUmGAyObRU+XuvC4WWbek/7D143fEOq3GB3Q==", "signatures": [{"sig": "MEMCHwcc/wRBMo920dXpnHzYBmv/eijc0d5bzRNkcB5DCkACIA6k3o5OjLJAeiBywsK5M8gUXaOgBPsLjCsuskEwdBV2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67302}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-module-transforms": "^8.0.0-alpha.16", "@babel/helper-validator-identifier": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.16_1739534380568_0.11437541914016913", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "10310548ad8ae253c3f6965fae61a60ecc930355", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-IMPZ5wtcme8ajLWNxkKh+dehpHN31vWn0orext624aG53fcz1fF7jYOmVRX0dlME4Tk5rDB8plWyFTehvwaEvA==", "signatures": [{"sig": "MEUCIQCwaFJIc5PMxF8rSWZsExd9/QnnFSv938NlKFKgWI7bNwIgFplHvmkcZ68y0xwbHMqVxB7OOjPzJHjdDoIxxvFicYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67302}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-module-transforms": "^8.0.0-alpha.17", "@babel/helper-validator-identifier": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17", "@babel/helper-transform-fixture-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-alpha.17_1741717534684_0.978957837406268", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "00e05b61863070d0f3292a00126c16c0e024c4ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-w5N1XzsRbc0PQStASMksmUeqECuzKuTJer7kFagK8AXgpCMkeDMO5S+aaFb7A51ZYDF7XI34qsTX+fkHiIm5yA==", "signatures": [{"sig": "MEYCIQDBLb97hPthNVtHFddgmNHzu54/xfUWy3lZMwXKXoPAyQIhAKtUa84lof9WERyPVavDeB03xd7RvE1LLmvSehEtakkJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66605}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/helper-transform-fixture-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_7.27.1_1746025768982_0.4082717954580348", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "dist": {"shasum": "833b880cc7eeed8af7b8b2a593994beea31fe333", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-DMvPOa223zlq7bAuEF6l3Xy0ZNYKQFMciug3WFjvNqrv+jEiO4T81XHiX6v7mdFIoxcpRJSK4gq9kE0ahJSg0g==", "signatures": [{"sig": "MEQCIFuCWImfvia5bDYkom4P63FVG6ZYiEcaJLWcbyIxTn/PAiB3FlixPvSvf5NUxW0dvUCqhKQ4h00WII/hyetkVnsnkw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67272}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-module-transforms": "^8.0.0-beta.0", "@babel/helper-validator-identifier": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"core-js": "^3.35.0", "@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0", "@babel/helper-transform-fixture-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-beta.0_1748620306441_0.1496225044780055", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-modules-systemjs", "version": "8.0.0-beta.1", "description": "This plugin transforms ES2015 modules to SystemJS", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-transforms": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1", "@babel/helper-validator-identifier": "^8.0.0-beta.1"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1", "@babel/helper-transform-fixture-test-runner": "^8.0.0-beta.1", "core-js": "^3.35.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-modules-systemjs@8.0.0-beta.1", "dist": {"shasum": "a9873f0a16e308dc5af9cb0b966155b03213ac61", "integrity": "sha512-f5Tk28jnPs02mi8kHzSRYGExOT/bIdTC8lW8hpGxNFANfnioTbpN7zyUcBKIQ0H3I4nfIkGfEp/Rawr88ephSw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 67272, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHy9PpT5Vooccscj+ubCBkXnZtlMSURkjJ38MnbTf0ZAAiATpFlZVm/ik8aOD5E7PzSvqZ357RZGVdksQ7IlVUJPKw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-modules-systemjs_8.0.0-beta.1_1751447088815_0.9156435052240195"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:39.235Z", "modified": "2025-07-02T09:04:49.178Z", "7.0.0-beta.4": "2017-10-30T18:35:39.235Z", "7.0.0-beta.5": "2017-10-30T20:57:17.940Z", "7.0.0-beta.31": "2017-11-03T20:04:03.719Z", "7.0.0-beta.32": "2017-11-12T13:33:47.114Z", "7.0.0-beta.33": "2017-12-01T14:29:02.103Z", "7.0.0-beta.34": "2017-12-02T14:39:58.136Z", "7.0.0-beta.35": "2017-12-14T21:48:15.799Z", "7.0.0-beta.36": "2017-12-25T19:05:22.565Z", "7.0.0-beta.37": "2018-01-08T16:03:31.465Z", "7.0.0-beta.38": "2018-01-17T16:32:27.709Z", "7.0.0-beta.39": "2018-01-30T20:27:55.312Z", "7.0.0-beta.40": "2018-02-12T16:42:20.298Z", "7.0.0-beta.41": "2018-03-14T16:26:30.686Z", "7.0.0-beta.42": "2018-03-15T20:51:43.963Z", "7.0.0-beta.43": "2018-04-02T16:48:40.192Z", "7.0.0-beta.44": "2018-04-02T22:20:20.701Z", "7.0.0-beta.45": "2018-04-23T01:57:38.585Z", "7.0.0-beta.46": "2018-04-23T04:32:00.080Z", "7.0.0-beta.47": "2018-05-15T00:09:55.475Z", "7.0.0-beta.48": "2018-05-24T19:23:54.783Z", "7.0.0-beta.49": "2018-05-25T16:03:15.544Z", "7.0.0-beta.50": "2018-06-12T19:47:38.356Z", "7.0.0-beta.51": "2018-06-12T21:20:16.087Z", "7.0.0-beta.52": "2018-07-06T00:59:36.390Z", "7.0.0-beta.53": "2018-07-11T13:40:35.327Z", "7.0.0-beta.54": "2018-07-16T18:00:17.525Z", "7.0.0-beta.55": "2018-07-28T22:07:41.217Z", "7.0.0-beta.56": "2018-08-04T01:07:10.492Z", "7.0.0-rc.0": "2018-08-09T15:59:09.082Z", "7.0.0-rc.1": "2018-08-09T20:08:52.865Z", "7.0.0-rc.2": "2018-08-21T19:25:02.920Z", "7.0.0-rc.3": "2018-08-24T18:08:45.677Z", "7.0.0-rc.4": "2018-08-27T16:45:10.850Z", "7.0.0": "2018-08-27T21:44:02.570Z", "7.1.3": "2018-10-11T15:52:21.408Z", "7.2.0": "2018-12-03T19:01:30.879Z", "7.3.4": "2019-02-25T18:35:22.786Z", "7.4.0": "2019-03-19T20:45:10.006Z", "7.4.4": "2019-04-26T21:04:42.978Z", "7.5.0": "2019-07-04T12:58:01.021Z", "7.7.0": "2019-11-05T10:53:41.798Z", "7.7.4": "2019-11-22T23:33:40.233Z", "7.8.0": "2020-01-12T00:17:34.453Z", "7.8.3": "2020-01-13T21:42:29.290Z", "7.9.0": "2020-03-20T15:39:45.423Z", "7.9.6": "2020-04-29T18:37:56.952Z", "7.10.0": "2020-05-26T21:43:13.920Z", "7.10.1": "2020-05-27T22:08:32.075Z", "7.10.3": "2020-06-19T20:54:33.811Z", "7.10.4": "2020-06-30T13:13:30.846Z", "7.10.5": "2020-07-14T18:18:13.965Z", "7.12.0": "2020-10-14T20:03:32.165Z", "7.12.1": "2020-10-15T22:42:10.913Z", "7.12.13": "2021-02-03T01:12:17.646Z", "7.13.8": "2021-02-26T23:39:00.885Z", "7.14.5": "2021-06-09T23:13:24.367Z", "7.15.4": "2021-09-02T21:40:01.066Z", "7.16.0": "2021-10-29T23:48:00.510Z", "7.16.5": "2021-12-13T22:28:54.235Z", "7.16.7": "2021-12-31T00:23:06.482Z", "7.17.8": "2022-03-18T20:31:09.590Z", "7.17.12": "2022-05-16T19:33:15.843Z", "7.18.0": "2022-05-19T18:16:40.749Z", "7.18.4": "2022-05-29T21:50:11.641Z", "7.18.5": "2022-06-13T06:40:21.242Z", "7.18.6": "2022-06-27T19:50:40.809Z", "7.18.9": "2022-07-18T09:17:43.406Z", "7.19.0": "2022-09-05T19:02:23.412Z", "7.19.6": "2022-10-20T09:03:35.940Z", "7.20.11": "2022-12-23T21:08:08.603Z", "7.21.4-esm": "2023-04-04T14:09:57.767Z", "7.21.4-esm.1": "2023-04-04T14:21:55.538Z", "7.21.4-esm.2": "2023-04-04T14:40:00.688Z", "7.21.4-esm.3": "2023-04-04T14:56:42.793Z", "7.21.4-esm.4": "2023-04-04T15:13:50.859Z", "7.22.0": "2023-05-26T13:45:53.922Z", "7.22.3": "2023-05-27T10:10:56.836Z", "7.22.5": "2023-06-08T18:21:45.813Z", "8.0.0-alpha.0": "2023-07-20T14:00:21.901Z", "8.0.0-alpha.1": "2023-07-24T17:52:55.179Z", "8.0.0-alpha.2": "2023-08-09T15:15:18.232Z", "7.22.11": "2023-08-24T13:08:39.085Z", "7.23.0": "2023-09-25T08:11:32.809Z", "8.0.0-alpha.3": "2023-09-26T14:57:30.938Z", "8.0.0-alpha.4": "2023-10-12T02:06:43.981Z", "7.23.3": "2023-11-09T07:04:13.127Z", "8.0.0-alpha.5": "2023-12-11T15:19:35.937Z", "7.23.9": "2024-01-25T16:57:48.186Z", "8.0.0-alpha.6": "2024-01-26T16:14:36.211Z", "8.0.0-alpha.7": "2024-02-28T14:05:35.131Z", "7.24.1": "2024-03-19T09:48:42.024Z", "8.0.0-alpha.8": "2024-04-04T13:20:14.040Z", "7.24.6": "2024-05-24T12:25:05.361Z", "8.0.0-alpha.9": "2024-06-03T14:05:44.035Z", "8.0.0-alpha.10": "2024-06-04T11:20:42.872Z", "7.24.7": "2024-06-05T13:15:56.305Z", "8.0.0-alpha.11": "2024-06-07T09:16:06.821Z", "7.25.0": "2024-07-26T16:59:32.313Z", "8.0.0-alpha.12": "2024-07-26T17:34:01.965Z", "7.25.7": "2024-10-02T15:15:31.577Z", "7.25.9": "2024-10-22T15:21:47.398Z", "8.0.0-alpha.13": "2024-10-25T13:54:47.677Z", "8.0.0-alpha.14": "2024-12-06T16:54:37.229Z", "8.0.0-alpha.15": "2025-01-10T17:25:06.605Z", "8.0.0-alpha.16": "2025-02-14T11:59:40.721Z", "8.0.0-alpha.17": "2025-03-11T18:25:34.929Z", "7.27.1": "2025-04-30T15:09:29.189Z", "8.0.0-beta.0": "2025-05-30T15:51:46.601Z", "8.0.0-beta.1": "2025-07-02T09:04:48.969Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "description": "This plugin transforms ES2015 modules to SystemJS", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}