{"_id": "emoji-regex", "_rev": "49-9c8e4da494116f86d248d941fb7b053a", "name": "emoji-regex", "dist-tags": {"latest": "10.4.0"}, "versions": {"1.0.0": {"name": "emoji-regex", "version": "1.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@1.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "50698ade6de03754e726f33b51ef609c18b7c449", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-1.0.0.tgz", "integrity": "sha512-DEI01motGwoeyaiAh4+ndb7cyB1RSdIt+9+5m8acTihMdDuMdGVhg3NdBazziLgumWrfNk5az3fwtjvqQUXH3A==", "signatures": [{"sig": "MEUCIQDendTOSOmBhV469cv635pKZBoDFb0MZ+OWYZc8EjuUbAIgCrP3zSHjq2u3cK3BU6xkOqA7ZzV+8Drb/L74H70NUR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "50698ade6de03754e726f33b51ef609c18b7c449", "gitHead": "2915f75504a7ff51d0bb487fe11ee7d35a461d5d", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "devDependencies": {"jsesc": "^0.5.0", "mocha": "^1.21.4", "istanbul": "^0.3.2", "coveralls": "^2.11.1", "unicode-tr51": "^1.0.0", "lodash.template": "^2.4.1", "string.fromcodepoint": "^0.2.1"}}, "1.0.1": {"name": "emoji-regex", "version": "1.0.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@1.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "665d27b543bf8fb66bd127d98c6939a0e723ad3f", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-1.0.1.tgz", "integrity": "sha512-OtS7fAIqlXc81A1aK9vXBoKTmqE3L6CqePgEUCE96v3FA3GOJMevbbehEZLtTTIROEmIXkEnu3EQAC4uJrYUdQ==", "signatures": [{"sig": "MEUCIB1ChAeypIA+wcNYGI6zUZ40rsww76hqCqBW2cVgs/n8AiEAs0yxNJ/zWSz4HGyWCjodOARdx5HQ/6kJLcO05p+Wosk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "665d27b543bf8fb66bd127d98c6939a0e723ad3f", "gitHead": "971ea0284a72ed083572e8dfc7e0096ac76c9eb8", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "devDependencies": {"jsesc": "^0.5.0", "mocha": "^1.21.4", "istanbul": "^0.3.2", "coveralls": "^2.11.1", "regenerate": "^1.0.0", "regex-trie": "^1.0.3", "unicode-tr51": "^1.0.0", "lodash.template": "^2.4.1", "string.fromcodepoint": "^0.2.1"}}, "2.0.0": {"name": "emoji-regex", "version": "2.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@2.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "07c6304da0ec921f5cdcf01c2f1894402eaed137", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-2.0.0.tgz", "integrity": "sha512-QokNpTlvWXnJdOkHlieB9LNECKwQOSW1Yp+otylcg540ejghf33ErARj3zRuOhb/bXbU8iffFlY6bQKzFL+Kvg==", "signatures": [{"sig": "MEUCIQD24qVzxjxemQRMFEgkzcy6XrcrYzgoVQm2lDhAUtDnUQIgWdTSWbLU1XUzdmEmBl9rkzFxvXK+w2YgNDnp8Tzt50c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "07c6304da0ec921f5cdcf01c2f1894402eaed137", "gitHead": "c57ed120f3619431861519fbf2621b12b966c87d", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "0.10.32", "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.0.1", "istanbul": "^0.3.2", "coveralls": "^2.11.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "unicode-tr51": "^2.0.0", "lodash.template": "^2.4.1", "string.fromcodepoint": "^0.2.1"}}, "3.0.0": {"name": "emoji-regex", "version": "3.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@3.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "d89af9ee660ea0780a5d8b09a415872f2a9d3add", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-3.0.0.tgz", "integrity": "sha512-p29WyNdX+v/q7b75S8nInYQx3wZLYdIZ3/HzehylXtMEFutYaYIEnucOWwH+k16NVAF8vIKsZ6E/mAzb0J70Bw==", "signatures": [{"sig": "MEUCICj6TNf+dKzKHzAE5XE+RYHc+yRN7hp62GKauIwyffmrAiEAnbGTm5UP8MfHERFt30ZBLOMe0DbufQpkZ6O8gk/LITI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "d89af9ee660ea0780a5d8b09a415872f2a9d3add", "gitHead": "2322260dd72b062566859fa398f2bd31d43890b5", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "0.10.32", "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.0.1", "istanbul": "^0.3.2", "coveralls": "^2.11.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "unicode-tr51": "^3.0.0", "lodash.template": "^2.4.1", "string.fromcodepoint": "^0.2.1"}}, "4.0.0": {"name": "emoji-regex", "version": "4.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@4.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "f54e520d45f308c9094e15ba401b76ec2d41d839", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-4.0.0.tgz", "integrity": "sha512-yFgtcAL3geDoppx3Xobh0ui7lCdKMdfiOIoW8PRKwbD04K5YbRzxJ2K/3A+OEJtaISgBAijKq2nPAoc49RIk4w==", "signatures": [{"sig": "MEQCIA+l49637GVXNpborqb8BXApXU/OaMKUQRvQS9lPsMmdAiAP2flJFejmrS4tjWZueBAaPOKe7lZdZmS2/xDQoZDOxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "f54e520d45f308c9094e15ba401b76ec2d41d839", "gitHead": "d8d883a9a761156ded461d6b710aa0dce361069f", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "0.10.32", "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.0.1", "istanbul": "^0.3.5", "coveralls": "^2.11.1", "regenerate": "^1.0.1", "regex-trie": "^1.0.4", "unicode-tr51": "^4.0.0", "lodash.template": "^2.4.1", "string.fromcodepoint": "^0.2.1"}}, "5.0.0": {"name": "emoji-regex", "version": "5.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@5.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "dc077afcaa8f67a11170f0339c57d92867bec829", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-5.0.0.tgz", "integrity": "sha512-Ze3ALeZ2efknvbCGXG1YbNYkPtzPRqiB+Co+thwYuN2c5K8YYPV34u2wZb/WADcb3Ft3en4knhVEhmEGxgtACA==", "signatures": [{"sig": "MEQCIELC8d33CqrpiQ0bpCy2DKniVwdqgubVHe+ETJf+UJIqAiAHdi1//qwImCMpQf2wFuA8qde5D2YzDLZFTY2EjfUNzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "dc077afcaa8f67a11170f0339c57d92867bec829", "gitHead": "4657bcb29fe627b16732814394e15ea7f4e75220", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "1.2.0", "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.1.0", "istanbul": "^0.3.5", "coveralls": "^2.11.1", "regenerate": "^1.2.1", "regex-trie": "^1.0.4", "unicode-tr51": "^5.0.0", "lodash.template": "^3.2.0", "string.fromcodepoint": "^0.2.1"}}, "6.0.0": {"name": "emoji-regex", "version": "6.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "df6d704ae667c7d3af89b37dc664b3048996024d", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.0.0.tgz", "integrity": "sha512-wNzOhBSfdzYagAwIaW0mFGLxZL4KF1o5ivxoxvesRhzyoNaQkO7KAm3YqIrZciTct/yNz2I5ju4pMfW00p0tqA==", "signatures": [{"sig": "MEQCIFqgcjuBBWoanndeZdRMMm1R+U1k6P7pUWFwfninKwoPAiBpaF3m5oJpTRC7wilzsacMvW9v6G6UN6MGmwcWMOJOyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "df6d704ae667c7d3af89b37dc664b3048996024d", "gitHead": "3eb1bb312226bec134540902b0679b583e14ff50", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "2.2.1", "devDependencies": {"jsesc": "^0.5.0", "mocha": "^2.1.0", "istanbul": "^0.3.5", "coveralls": "^2.11.1", "regenerate": "^1.2.1", "regex-trie": "^1.0.4", "unicode-tr51": "^6.0.0", "lodash.template": "^3.2.0", "string.fromcodepoint": "^0.2.1"}}, "6.1.0": {"name": "emoji-regex", "version": "6.1.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "d14ef743a7dfa6eaf436882bd1920a4aed84dd94", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.1.0.tgz", "integrity": "sha512-NY6VHzna/d/SQ3ZKLdRBNOrc+hEoD8IkmfsPj5QDyL3HsbIgnzEv3lTO5xURIPHJ2B/wDJaSYsdOswkYnH91gg==", "signatures": [{"sig": "MEQCIDRgAGxRdJc0yDE2+8g0UgM7syUAwUDveWmandDCPlo1AiA+0ITsASK4JNZOWGAp5X3LxmcdT/rfeHMEaIR5gAPpxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js"], "_shasum": "d14ef743a7dfa6eaf436882bd1920a4aed84dd94", "gitHead": "3954a7729054e6f7ff64e168eb509a90ab21e2ff", "scripts": {"test": "mocha tests", "build": "node scripts/generate-regex.js", "coverage": "istanbul cover --report html node_modules/.bin/_mocha scripts/generate-regex.js -- -u exports -R spec"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {"test": "tests"}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^2.1.0", "istanbul": "^0.3.5", "regexgen": "^1.0.0", "coveralls": "^2.11.1", "unicode-tr51": "^6.0.0", "lodash.template": "^3.2.0", "string.fromcodepoint": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.1.0.tgz_1482348417211_0.31900757434777915", "host": "packages-18-east.internal.npmjs.com"}}, "6.1.1": {"name": "emoji-regex", "version": "6.1.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "c6cd0ec1b0642e2a3c67a1137efc5e796da4f88e", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.1.1.tgz", "integrity": "sha512-WfVwM9e+M9B/4Qjh9SRnPX2A74Tom3WlVfWF9QWJ8f2BPa1u+/q4aEp1tizZ3vBKAZTg7B6yxn3t9iMjT+dv4w==", "signatures": [{"sig": "MEUCIQDbSRlxLu/7DRPLR/yklaNYwUH2ySR8KtMPX7TxMcowQgIgHACkUTghW3rgicikunLU9NFqtEfq2Y4toKkhWkl0VO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "dist/index.js"], "_shasum": "c6cd0ec1b0642e2a3c67a1137efc5e796da4f88e", "gitHead": "7d00562619d478b133f442cc6681068438448ba0", "scripts": {"test": "mocha --compilers js:babel-register", "build": "babel src -d dist", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.0.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.1.1.tgz_1489001442680_0.72080272086896", "host": "packages-18-east.internal.npmjs.com"}}, "6.1.3": {"name": "emoji-regex", "version": "6.1.3", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.1.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "ec79a3969b02d2ecf2b72254279bf99bc7a83932", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.1.3.tgz", "integrity": "sha512-73/zxHTjP2N2FQf0J5ngNjxP9LqG2krUshxYaowI8HxZQsiL2pYJc3k9/O93fc5/lCSkZv+bQ5Esk6k6msiSvg==", "signatures": [{"sig": "MEUCICbZkRwFtb99IqXLX+KqSnT+brbwRT4hzeqDyg2FAy7qAiEA5Ov7Z9i0tusGrbU4ct+fVEBSx+tNeaXx5QqGfPURHik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "dist/index.js"], "_shasum": "ec79a3969b02d2ecf2b72254279bf99bc7a83932", "gitHead": "b8a4f39b8e5afdbe6f594f5fd1f0b7a259a1b1e8", "scripts": {"test": "mocha --compilers js:babel-register", "build": "babel src -d dist", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.0.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.1.3.tgz_1489064354443_0.5536381872370839", "host": "packages-12-west.internal.npmjs.com"}}, "6.2.0": {"name": "emoji-regex", "version": "6.2.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "6e2889a6803d85141ce01d189ec6b88022ea2122", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.2.0.tgz", "integrity": "sha512-Cmoz7J4sU0/ACl6qhmp1VeBLkwi1nu0E7LBH2AcVizzdTKVar7Y9szOd8PD5ApjpmAQudwLLFCDcv8TixSdE3w==", "signatures": [{"sig": "MEUCIQDitDsKZxSow1RvuC/9/ERhcJpWx2eo+9HLS9f3yleA7wIgdkxs6EHq7mVEb3BHW5OalxjzoRnJcX2ptCbeJswC2F4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "dist/index.js"], "_shasum": "6e2889a6803d85141ce01d189ec6b88022ea2122", "gitHead": "48431ce1778eef2451da34dfc6c30255f13bcec2", "scripts": {"test": "npm run build && mocha", "build": "babel src -d dist; node script/inject-sequences.js", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.1.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.2.0.tgz_1489581830190_0.2853828819934279", "host": "packages-12-west.internal.npmjs.com"}}, "6.3.0": {"name": "emoji-regex", "version": "6.3.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "8cfd2eba5d469a308329d44ec28ec50cc22cbe50", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.3.0.tgz", "integrity": "sha512-7CVFaOfQvuHtazkTKy50bml9ph18GV6aTb8YoKKAxgrK6+Wfy+VGptSUIiVaGuoZHQNLaWa2361sQ2CRglAFzw==", "signatures": [{"sig": "MEYCIQD7Gy/jq/ymoXZRVAVMrqqBL/OEmtbKf2xNaPyRTfpozgIhAIjzFrd8c5jstAai0HtW26LPZc6mP2UkEHWH/luHdJhp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "dist/index.js", "dist/text.js"], "_shasum": "8cfd2eba5d469a308329d44ec28ec50cc22cbe50", "gitHead": "2bbbc81f5659c69e3630575db226a726ea99d740", "scripts": {"test": "npm run build && mocha", "build": "babel src -d dist; node script/inject-sequences.js", "clean": "rm -rf dist", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.1.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.3.0.tgz_1489607644347_0.6887907045893371", "host": "packages-12-west.internal.npmjs.com"}}, "6.4.0": {"name": "emoji-regex", "version": "6.4.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.4.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "1685ef6ad557ae3f630990348cbe45889e7de9d6", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.4.0.tgz", "integrity": "sha512-Gao1K4r0OWnNMYUjJdwKPGmIMzUVygjtO6EsgmwXQ6bH5q4VPTDd/jS9+DKlvvRRwhmjCdIAm7l6rUwwibpM5g==", "signatures": [{"sig": "MEYCIQC56ziQV6RiuKp4CuRoEpRJBsOx4ym11EMnhL5mvGXhcAIhAMqSrNzBaSNCrCe4fmmNm7Cu3iI2O0jFb/5ptn69mzld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "_shasum": "1685ef6ad557ae3f630990348cbe45889e7de9d6", "gitHead": "2cf425da0f89be32a1a6c2ddd66de09592822657", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -- index.js text.js", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.1.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.4.0.tgz_1489610740180_0.6046377806924284", "host": "packages-12-west.internal.npmjs.com"}}, "6.4.1": {"name": "emoji-regex", "version": "6.4.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.4.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "77486fe9cd45421d260a6238b88d721e2fad2050", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.4.1.tgz", "integrity": "sha512-AvXfY9oj55pWmuSGBihF5oF//GtmR3EZ/3nKVMGQdddawEp1gncpf1KyQvdP5960dqwN+x5iTnUX6iexin2RlA==", "signatures": [{"sig": "MEQCICIid2JAweZ7nFdj4KNc/e758mg1+KTRhhz8F5Xo94+XAiB6TwprAIUez1Q+UqJshskmYh380xgdePTLA/M5sybGOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "_shasum": "77486fe9cd45421d260a6238b88d721e2fad2050", "gitHead": "69b18a5c676998964dda0fc7aa1e402e820900cd", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "regexgen": "github:mathiasbynens/regexgen#sort-alternation", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.1.1", "babel-preset-es2015": "^6.18.0", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.4.1.tgz_1489611909449_0.7590553292538971", "host": "packages-12-west.internal.npmjs.com"}}, "6.4.2": {"name": "emoji-regex", "version": "6.4.2", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.4.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "a30b6fee353d406d96cfb9fa765bdc82897eff6e", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.4.2.tgz", "integrity": "sha512-/Q5UgaKxL7NZNZpZdgODUlJsadqcPCE2tAysGf2rHxW4j67rMozQUFimuj/G3r8KDeNVzNZv+N3NTh4kcAK0qA==", "signatures": [{"sig": "MEYCIQDDw5kPp11oQAGCXV7tfhE6uV9llc5m3r6SnekjrcQYAwIhANwYK+rpqtUDY5F/4GPoZuV8+Zpn0Dnh0xf61Xv+2qtM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "_shasum": "a30b6fee353d406d96cfb9fa765bdc82897eff6e", "gitHead": "9f282109a58a1a1c80c1c5f75d51042a8c038647", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^3.2.0", "regexgen": "^1.2.3", "babel-cli": "^6.23.0", "babel-core": "^6.18.2", "unicode-tr51": "^8.1.1", "babel-preset-env": "^1.2.2", "babel-plugin-transform-unicode-property-regex": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.4.2.tgz_1491478620524_0.9190256621222943", "host": "packages-12-west.internal.npmjs.com"}}, "6.4.3": {"name": "emoji-regex", "version": "6.4.3", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.4.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "6ac2ac58d4b78def5e39b33fcbf395688af3076c", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.4.3.tgz", "integrity": "sha512-VyrhS7duUGY+mBDRobQtGYP+wArX2HU0FHcP/JrGUtVPf824eCDrUTe7ZQ7iWIkzdL772ZWU0NUig/pDaVeIVQ==", "signatures": [{"sig": "MEQCIHL3XvRzcNbZSYzUC+sQ/g79DcVJ9c5tvmpTGDBoGBufAiAc7cDeeEOKDWOnohCRcnRcgY+OlEx4DHG/glfqxN91Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "text.js"], "gitHead": "e784cef2e3c18855d521a37f4a9e8a813870f0a6", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; node script/inject-sequences.js", "clean": "rm -f -- index.js text.js", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "8.0.0", "devDependencies": {"mocha": "^3.4.2", "regexgen": "^1.2.4", "babel-cli": "^6.24.1", "babel-core": "^6.25.0", "unicode-tr51": "^8.1.2", "babel-preset-env": "^1.5.2", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.4.3.tgz_1498985721035_0.18083568243309855", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "emoji-regex", "version": "6.5.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.5.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "c1699e17f34154f7922219ea712ea76a2619c77b", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.5.0.tgz", "integrity": "sha512-Vja85njef5T0kGfRUFkyl0etU9+49L1LNKR5oE41wAGRtJR64/a+JX3I8YCIur/uXj4Kt4cNe5i8bfd58ilgKQ==", "signatures": [{"sig": "MEUCIBqku9KBny9U/lZDpKIjxhv/kXlQqRs1Nk6PmHmq8w7bAiEA4NH8tZfxgwe6q8PbyVhzr5TYmouL0v4PLD5tWqqrb8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "gitHead": "3233d998ef33161dd43dbdefe340767b80ce4712", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "clean": "rm -rf -- index.js text.js es2015", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"mocha": "^3.4.2", "regexgen": "^1.3.0", "babel-cli": "^6.24.1", "babel-core": "^6.25.0", "unicode-tr51": "^8.1.2", "babel-preset-env": "^1.5.2", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.5.0.tgz_1500156603081_0.8509252779185772", "host": "s3://npm-registry-packages"}}, "6.5.1": {"name": "emoji-regex", "version": "6.5.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@6.5.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "9baea929b155565c11ea41c6626eaa65cef992c2", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-6.5.1.tgz", "integrity": "sha512-PAHp6TxrCy7MGMFidro8uikr+zlJJKJ/Q6mm2ExZ7HwkyR9lSVFfE3kt36qcwa24BQL7y0G9axycGjK1A/0uNQ==", "signatures": [{"sig": "MEUCIEt2qi1B9EqT4jbqIgyd5DhERL6Rp3HHmMkGbx7xmwTtAiEAtyaQ5yu8bMkt78l3sYsTUssoqwPBkNSqgjPN/UyhgV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "gitHead": "ebba6b6eb6da0d2b3bb1b4be383e4be6bd53fc3a", "scripts": {"test": "npm run build && mocha", "build": "babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "clean": "rm -rf -- index.js text.js es2015", "prepublish": "npm run clean && npm run build", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"mocha": "^3.4.2", "regexgen": "^1.3.0", "babel-cli": "^6.24.1", "babel-core": "^6.25.0", "unicode-tr51": "^8.1.2", "babel-preset-env": "^1.6.0", "babel-plugin-transform-unicode-property-regex": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex-6.5.1.tgz_1501076526981_0.06240403279662132", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "emoji-regex", "version": "7.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@7.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "7c25d9bb46480f9a5a80ab6a2a12935bdde6cb6c", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-lnvttkzAlYW8WpFPiStPWyd/YdS02cFsYwXwWqnbKY43fMgUeUx+vzW1Zaozu34n4Fm7sxygi8+SEL6dcks/hQ==", "signatures": [{"sig": "MEUCIGVe97ZVcGKuFlYpo1EMobRajo6dTWDE2ZDvu++AyL/5AiEA6R0B+Rg8G7qFdf9g5XrZAcYdX6JUAlQm6/jUClMiYaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGkhvCRA9TVsSAnZWagAATNYP/0ctp+LhCmV8j3BBkET/\nIBgiDSlM8P+kBYVKTV+tANvLjKGxH8TrKXzCw3PdxTjraX7V3B+iRwN9b+AR\nGgFFboR0iXrSXIsMNo/wGlFmGfAddYOfgOstpwMhp8VDQ6T7TnjVB4FMu8fE\nXxrcS92Ik03DluD5HpfRa3ZaOgRPvyVgqK63NWCdpFvkEMwXn8ujg0CwVKMl\nKEVTCk//OZ+8FGQHQVa/82z45KHekq2wCuJZJFQBRbv2Sx/+bswYkGnJyyrY\nllBPspkY17fxvMDND73rP6RQ4X5cx7J7GEUCmbpAg/bENf2yrKKS5fbuUoGN\ngpdfSzjeZqIG7/m27J6UiWDU1UEmqpJlOv+DfiQ3lu4l0NntEjbV9i7XYman\n10OBjdNgurOUVRy3Ld2mcgsj0jjuNnCrsAR2ZaSyQCrrZ8cuxKBIldiWPsik\nGY7sP5BmZPKRRdnQ/7XW+gZuSfQCEtsbySN4iu1zYf6MjJlilLsAEkAVWvA4\nV4JwYdcVNAMsLyU/XP2GM8T8MLI+0wCErk5X6Z02/bt1izEhA4TjE2p0533R\nexd8oy03A+KHdUuXTuBQGZ4lyv+YqjDOeOJ4kKvBUqjo4MvhUVXwHl6E4Aoh\n30P2ao7Qj9ZPl9H6QS9q8vbTciXrQ6XGF3eKAhN9D92tnXfETPUFS/yQKwB3\nZ6oT\r\n=Lnpv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["LICENSE-MIT.txt", "index.js", "text.js", "es2015/index.js", "es2015/text.js"], "gitHead": "7e67d4038143c27bc94bfaf175364d765d641c24", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "8.11.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "regexgen": "^1.3.0", "@babel/cli": "^7.0.0-beta.49", "@babel/core": "^7.0.0-beta.49", "unicode-tr51": "^9.0.1", "unicode-11.0.0": "^0.7.7", "@babel/preset-env": "^7.0.0-beta.49", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_7.0.0_1528449135041_0.5583032630325226", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "emoji-regex", "version": "7.0.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@7.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "5a132b28ebf84a289ba692862f7d4206ebcd32d0", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.1.tgz", "fileCount": 7, "integrity": "sha512-cjx7oFbFIyZMpmWaEBnKeJXWAVzjXwK6yHiz/5X73A2Ww4pnabw+4ZaA/MxLroIQQrB3dL6XzEz8P3aZsSdj8Q==", "signatures": [{"sig": "MEQCIGITO132CsVbdvmcYsNotety0l9XxLfLw8lqfRh16GguAiAD5fetCbmAJ2CYCh5Qdz72Pjs3VMwTycBCXhqebDZ05Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiZXQCRA9TVsSAnZWagAAGVYP/1h27pzg8yiBZ+kf8jXi\ncHHV0XJFwbqXVwj1LqFRgem1PkvywIlRzS9ql314S7dtvoFadQwruIB0D4iJ\nFEUjn2qcsSp4Ilo5kXVLvvhXuPOZWrrwMc6BCQIc9+sSLrP91kQ1MVNLc0dB\nGgRY0tgzG5uaE77ftUNT/DPixUQ9qyJzviVDocVbdN7QZ6scZ9TgClbvaZaN\nn6eloVYEzbOQ7whSLc3mjuEP5TMw4mHbmmiq6mfOJRtui6aV4mWHJy6ehdkq\nRfj+evD32kEWoTxE5P9r0DwoLEmw4tagHDkkyrV/wKWByT8+RzmnEsZPKtq0\nvJs0zY8JVH5SeB5cuGwm/oeboT/SMWWxNCCUVc9BaITzR/z21H6b+EEL5hV/\nIHdWrOTvP9+y9Ldgh/5H770yqyzBvr0AwBl6K9B/fGG+wuWRCnogVosTfKuI\nNzg70X00gaZF9puoqYgCHGPlTGEocAn4av6avmjhi0tBI8eVaAZumo/6jgEC\naU0DF4E4UbiunSBVX0DhTlZG7zAUZ6mwykCF4VEmhTfPzM0iIeJZe+du7Z9t\n3I9G+O/OfjGAezPyEFc5tB5MsZ8ftrgzJRtKaufRcIBc7+I9NbJqPOMBppeh\nc8mpHiKPLvxkeLYAhMNVWCrIqisRO6OmjP1s66UOnaO25755TNtkGMaY5LIK\nPsXR\r\n=2rVD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "cf33819f8c6be57a48518acdb04af9158b910f98", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "8.11.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "regexgen": "^1.3.0", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "unicode-tr51": "^9.0.1", "unicode-11.0.0": "^0.7.7", "@babel/preset-env": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_7.0.1_1535743440114_0.7775245713596617", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "emoji-regex", "version": "7.0.2", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@7.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "7e08c8797b2f7ecf422fe62714b6214f0256be42", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.2.tgz", "fileCount": 7, "integrity": "sha512-I+SKlWNCfHZZakO0+3HOM8RdrfByyKb4HGf+QfpQq2qwyThq2wyEIyZnt9OINAe8ug2PvoqJBXqDM7/ApjuXBw==", "signatures": [{"sig": "MEQCIGCxJHrcibxDrPcpeTG+1n/9U2X5s5FVEII8CpLZs8tuAiB/KygNVWrzrP0DrkdW7Rlf/BVnZovmDIcc4uW1u6D33A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3hLCRA9TVsSAnZWagAAHq4QAKMG229d88GyvvJuUBLz\nLfpK2JtFivQFtVqzn976KonKBZ1YE/3TRhfBkrTLPqidUTOspSPLucEq96KU\n7C4vdc81gRx9xi2QB6eJB4s0yQD0DG0T5ijaBrYDrRH+Ajlhd4mdzsUETq7N\n3qzp929xjtYjuCfkAgY1OCzBFhCEonCEYNels6aORHguPM23mQFMQsGEwRRU\nEm047atV62FoYKPZAjwg8z7S2MK4bUUwovJ2rouq/qZKyzm5Mn0R3htHllZl\nJOTMBOWUzgkA9KJ9mP0f8u2JZvjxat2CFz9SbLr4UKeDZbnd7HYCTtG1alL1\nqnDRHLBkfT47f+AIxSIq3uc9q9h9jboBODMaNC0oC07DxtG4MVoiRH4CZ6RH\n8Sc9e9SpcAAwFMwe0JLhaiwy5GHXo5sbODjt0vc6zsSXvg04zS3DcdEzsAKm\n+jra7LzkWp4ddrYTNXOb4uul34oEDYjKdjRhLupdJ10275vRdIUuFxSuPOdZ\n+uZxBYzyOr7zjsxyVjsM9sF46fQMAhHd6Cf52gFchzOBjHc3iSC3fOG68vTo\n0PBKdnWOR8QeL6LonBzhRzykduq8AvKEE5BrZ6u70lga8rsXW9TXQVKrA9yd\nOjKmqQQ+BSm55tHdxu9SE1W4PbMQdYATDB1Z9dn9eqSYlylWBNsA4uXwjXHe\nPUJ2\r\n=k8LG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./src/index.d.ts", "gitHead": "880a35684329a35296a80c133acb765d351b0f55", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "10.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "regexgen": "^1.3.0", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "unicode-tr51": "^9.0.1", "unicode-11.0.0": "^0.7.7", "@babel/preset-env": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_7.0.2_1545304137084_0.3727673379301799", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "emoji-regex", "version": "7.0.3", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@7.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "933a04052860c85e83c122479c4748a8e4c72156", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz", "fileCount": 8, "integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==", "signatures": [{"sig": "MEUCIBMvbIWyQ4nNNXjh6mV6tPZzdLtWM8zgu2GQZA+nj7eRAiEAjpN0DZoPzEiKy2dPZhfwhOhw2ALd8N8EIYoj+lU/tZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHL/mCRA9TVsSAnZWagAAalkP/iJsyPSIxjYjFQHMXabJ\niQ6yZ4jVzp4QtpAcZlcWl8Nn7PgG0DS0cTQFlWHWOyuHNhJMzUtvRqaT9fqK\nB8M4IyDwWZnyRDMCHxZUi5ZYVVLifGIm+eCVTGKrpjNIb/XOhtvLdpE2QjfK\nASdwKa7YpsS1dbT3Cs4u4SDxEjrE2WpH93ZjZEalIvgMH0/2zH2QNKaqBPJH\n+aPixBEan21Ur60KQEE72UsGm3zjwdAn5pcN2r6mhgEJCCcZMk3J6b6I8w9N\ntF9tzLawZU4ai2dBIan4CxnXABWBReSyMSZnzIOy5a7wqHMnQpG1mjMfsVP7\nkcyvCgdL9msoJpHgDuWH7ZQCEOPO12rzHdDQyglBd0TPUEdS/6UwidE3ZO3U\nbk6yUQjcgGsV/5XUid+xwMLlJW3dAJ1CsCnx6j/Vh38vid5evvhrUSN2JlC2\ndguPh6wabKMhaFXo+9rwncwtn6pZVkRraGeB6RlVj+tBQf9rdc2k7D9ef2l2\nrWrnvYE1I0VHVha3zIGHm4VQEsMaJa3CoZRWmOdxyqvupnvr4pxGcIf6oTPp\nftJ89kWZzIeXT1we3PtVopBmo6iBT7PioqiX8g1Dgl9zQt1hhqZ2w/G8kNZB\ngkhZT2g3Fi1ikzZDNXpjpMmaXfDXk8+IgpwNwh2PUMP3/HIJcg9GgjKM8v61\nvPcb\r\n=CeP6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "3cbaf44e18f4996464ce97f1484ef425bbe8864a", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "10.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "regexgen": "^1.3.0", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "unicode-tr51": "^9.0.1", "unicode-11.0.0": "^0.7.7", "@babel/preset-env": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_7.0.3_1545388005752_0.10144054418270465", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "emoji-regex", "version": "8.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@8.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "e818fd69ce5ccfcb404594f842963bf53164cc37", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "fileCount": 8, "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "signatures": [{"sig": "MEUCIC+waQdufSheUTEjvna3EaQZWs+5GUI/0/r9f5VSiZIAAiEA9mR+WzssFvsrqZPJ7zzRTzWZRyOxn1ZaJlSRNglwMlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfsbPCRA9TVsSAnZWagAAr8oP/1XbIqEdVhX0sTGbaxsq\n2McAMLM6oAIrjkBXrNdMgP9fX5kEUfAE45JTbpZU6RprIM8GnhPAyJKauwmj\nIUFKvwSMdwxUPOsYljPiWYRrjsqn1rDENI/q9vwISwOJuqTg/h+Y6V5PQj8b\nQbSnzFDkuDlAgmp4BPPtNFZdpSQNtaylpC08GkKZneQXfCuV2uOB3PTTelIV\nYnANLh09gEmrKeCEKp2WYhc1wANK1Bae04GEr+8tsoKoDDhiMktEzhuhCs/8\nvl9pQPa2xuNa3SbZF3bZtyiuF8CJUJin9R5sooqjBOHKw58taxfu5A5GwGNx\n9Uss9ysWTfuKKRJTmSG5OaRF49/bmIXPSpt4uwIywFtaWCdPgfeOorBUHeun\n1v4EhksSXggKIW1i1KbNQT6b0OIqEV7GQI5XWWlxhZ5vSFXSjTtasLyf/NtY\nJVOBlb8+fGFygQvzb1QjVDuKtEnnILTGbCVDIYx8yDiTW7z2Qt7Id3Sp+Whr\naRHjVE1mSB3tQrAyBvIw3650stwxMnS++c+qJbCaR/90JKahD3vM40Zt6aAO\nn31zq6N00oUefhd+2YwpUbmDqWQiROZCBKbRcYB1gDiIcu9PaenCeJkvgNId\nVoB6Mb3DYZggjjQcOGtdcS2ZcYaY2UexGuItbYqoMTvdmIcWqjEMq+oOaAjw\n/TqM\r\n=NmDr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "a9f2e514523d4c0931974aff5059052da10c52c5", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "10.14.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.0.2", "regexgen": "^1.3.0", "@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "unicode-12.0.0": "^0.7.9", "@babel/preset-env": "^7.3.4", "@babel/plugin-proposal-unicode-property-regex": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_8.0.0_1551812302894_0.8728129094317747", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "emoji-regex", "version": "9.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@9.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "48a2309cc8a1d2e9d23bc6a67c39b63032e76ea4", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.0.0.tgz", "fileCount": 8, "integrity": "sha512-6p1NII1Vm62wni/VR/cUMauVQoxmLVb9csqQlvLz+hO2gk8U2UYDfXHQSUYIBKmZwAKz867IDqG7B+u0mj+M6w==", "signatures": [{"sig": "MEYCIQDUX8gN8fxLW3fvVKjFVVfaosoa1dB1ih5wmb0o/d0LigIhAL3TBGg5cu57sl4u9725ItYQ94nmofSf6Q6NL7LgsEez", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekD3OCRA9TVsSAnZWagAAoHAP/i3cRh9dr1QX4/4OAsrx\nCFvkSG47uqDlJanm7E9xcqluYEw7ssHX3Fmb+bL1uiUkj14qCp7kHHTk44kl\nGLQnK/BJJmwwLryagKxAtc/BhaEYO1bRw53WyLFg2HrnU0zg+hlBm1oMTMkn\nbuUL9u2WnUuOjqRRKC69wZfZm3ZE69YOMQdKdsE5/fyExEbg2P09cYsnB87u\n0tzCUX6MUQTW514xTEnZUU7Xy2MnL0iSxung1yfcWrGVmMgpAIW5OIZUBmE/\nh2fdyx7b88Ymdxy/gIcQhF2K7IxnI+5h6XfNsd72QK0df+CYooDbdTmq481V\nff1e2CTO6iodZZF7r7crKoU9IiqHEDwDelv4q3ybf0nSxOuVStLMcuMDXyOD\n6XdRvIFyQ8FjOYwQc3fjVyHUNYOo/0BH+3VVManrM+ThkuGU+/5/YKZHxm/4\nFL5+mvIH1dBUZ3uPYM4cvOsoyeZBZu5J+kcQ3JXinAO5/BOVXbL2vnhvT9su\nu/2fDyiuC5qgzaMnIpwQvcoiBtXw5hQwCA1yVINHZbuK4BfaSteTzgVcQFuU\n14LrbRfxR4XuG9TO+HNAY/kNh79HF4fh5gSAi0FFuu3C7FdsTIMPgYxpFUJl\nFsyrtLxVmIX0GOJG/RLH+189CaAkxeK1CUfZxU2dNEx3fzqVpoVMsSyYBiy6\nbEYD\r\n=bYFE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "cf70015c33262fdddf731cf5cda16588bb1583dc", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "regexgen": "^1.3.0", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "unicode-13.0.0": "^0.8.0", "@babel/preset-env": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_9.0.0_1586511309638_0.7386618611323867", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "emoji-regex", "version": "9.1.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@9.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "1d5ffce26d8191e6c3f3a9d27987b1c5bba7d20a", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.1.1.tgz", "fileCount": 8, "integrity": "sha512-AaWyDiNO9rbtMIcGl7tdxMcNu8SOLaDLxmQEFT5JhgKufOJzPPkYmgN2QwqTgw4doWMZZQttC6sUWVQjb+1VdA==", "signatures": [{"sig": "MEQCIHY4fMgjVahDYiiRPOZsv+GaCyitE1ppgrfV7cl99UkQAiBPp266K0eUcy6+ULmXBIIfKV5E43sR3RRxi/H6w6fI1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhanzCRA9TVsSAnZWagAArHcP/AkgkFe6z2lsqPB49GXM\n84UWl+mCyjpKIPrrRxKEXv00WYPTOOuPyCiV1OG0Uoy4ycvAPi3uqAQenUDw\nrBgKNWcLW1ZiHcS51y5O1H7/Bu5ZdEQg9IQOmuDofAEmJ6E+hPBqXNmntQxI\nvgwG5RFvohlxPhIDRqdCpxJuM91XKg5NDyz+Pz7UoXAHimwddT/9+CkAjx00\n+OOy65lLDfG57U8sfZ9T8vzzEUBId+YoBFxHAvFfNFLSr+36Mp+YzlCrc6sW\nL1ZFJKBwGsrZCmu9qJ8RDj4CutDdvoiQTlDtVpByKO79J0xAUA5yYx4z0V0F\n4ZZDKt4VDmwfhR3VTEHO3hbnK6MKs9p+n3ZS/K2Riu7GCATlRvvLJBUi9lNB\nrtwiJGOj2/HqCKqZodQL0TaM5Ki5UBsO9cbThlWUX/FcWaGeIZHV8jOkBGVg\nHKVNfVf74QlpQ7kBoHwd9jP/s0Awhv4EXvukrjBUgaxF9pOMijylZFOSa2mr\n4q+/413YYcUpM85xEI3owx2rK5DRGmvegHHW/Ith/eW6s59AiNrBWxn7/6rM\nkZ/NYSQPR+pGAahXp/ClqdQvy8Vfgsl26nnrUS13heB0wWJB8UgDkc+Wk+iO\nAjcRYCvp6gqFspDo+Gaolm4yL3ASmLneT8iJQv//qy+pivqS0eOU0UV3hgtK\nDq6z\r\n=xDrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "0a00b98dc1532e15d2011638127b8b2c663a3732", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "regexgen": "^1.3.0", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.1", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_9.1.1_1602595315386_0.32293751978809326", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "emoji-regex", "version": "9.2.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@9.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "a26da8e832b16a9753309f25e35e3c0efb9a066a", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.0.tgz", "fileCount": 10, "integrity": "sha512-DNc3KFPK18bPdElMJnf/Pkv5TXhxFU3YFDEuGLDRtPmV4rkmCjBkCSEp22u6rBHdSN9Vlp/GK7k98prmE1Jgug==", "signatures": [{"sig": "MEUCIQDr75dbgqoZK+R2JTOdbjfNyILzfLUQPSndHLqX0DHoMwIgGou9YOZJyNU+1umje+ROw0RFG183yWpiz+wDh+r8DWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjxqJCRA9TVsSAnZWagAA5mEP/1iKvOlbS8EbW3L/wLOo\nrb4Yj2tzYpzFSDDcp9bRyG+kZ+HYS4Tbsk2me8ruRMPgcE9f4Fl6SHS0ouC2\nlUoG/j8rRdEe/Y9Avb+0uxkm5wXK/GWxv9/Xs+vywMce0zaZQ/TJkIXl3+9r\n6TMtEOQSYxFuc+0Es4wRwolUaxDc95EGe1C+uDH2XvohcQWpy5YQ4QaA0AEw\nEnbf2XPd1MWoiZ0OxJ/oQkXyCcLRYuJQCRL30+ddBWPiSqlaAudwHRPB0OJG\nzgFYpeVEVaYgyAMqsJgoDKDWgDyBe6vWsd6J+X80TZlWcb9A1968o+iF4Han\nlQxUVP9Bfhu3hgLH3Zh4Y0SlY+xxuYKd07+qed51jVaYJE16dksaoj/Gn7T9\n76CfHiHPIf8Vtr3DllkpwUJHbqy7cQBbFE1x0WLP13TCvsUcyjVkNCLLzIX8\nObRaKITfPSgInzhi46VXD33f535uXrPjckixL07laW9fkb04RiY61Pe4OEtu\nTzQIpKIDJXze1icv+kbr3axcLRKSnWYgBrckBfBypzl+t8lt2GS71dZVepHn\nF9Il8fSv6A75FgBn0Hwn0/ukd33KP/4myG+r0FJD02K5nEYqsmMeaebqcH/K\nCrCxwUXGY28ogS/BV9goEb2GJqkfNc3pj/7XTq0/zB+SYU9mP+BpigiE/NBk\nVIxX\r\n=+PHs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "acc45a26fdcf85bb9ed03d0ef0a6c5e1c17177e9", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "regexgen": "^1.3.0", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.1", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_9.2.0_1603213960455_0.49571742283457265", "host": "s3://npm-registry-packages"}}, "9.2.1": {"name": "emoji-regex", "version": "9.2.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@9.2.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "c9b25604256bb3428964bead3ab63069d736f7ee", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.1.tgz", "fileCount": 10, "integrity": "sha512-117l1H6U4X3Krn+MrzYrL57d5H7siRHWraBs7s+LjRuFK7Fe7hJqnJ0skWlinqsycVLU5YAo6L8CsEYQ0V5prg==", "signatures": [{"sig": "MEYCIQC40BUgNn3689/lqkZsUgkbj9HPvUEYYMub7LmAS6s7SQIhAJaavn0jKvfA2l9FcdCsLRx95GT/TnahbdLX3/Zxx7Bt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgE9DrCRA9TVsSAnZWagAAy4oP/ikfqONfvODybsCvg9FC\nmVpQBIZj6489gHidF9pABMMEXSJYYAnx5/EEbiCpTUAQLHPdWI85rfQT3lbK\nJ6CenxmrOBgD7BZiCctjiIS6c6GVU8AjBL7Qm2yveQvbD8GHPLVZzWKAD/12\nPYYeudldYL3J2TTdTshLgHbZvFHrT1159ktZnQqA6r69t2UyOHMy/veblJqS\nMGQR+YO2PvKpQOuV7TqMLJloadNLwL8eKqub4V/U4c70qWEocMU7Y9aTkyNh\ncSYbOVpi2ChXvjKoNxgaIK4110HQXlJk7o/zM0iP2dLClGL5tgmFbJByI8Xo\n9r0IlS1XUIe6zHpr/hw8Dcp2Cd8qTamSrO9oPsvpFWY+AuEST/NWlV33hw/w\nEdoXXn0b7XGmANamk80kvllK64jfLkvRv0p2W/1jTLdGg3ORapqYmvPLMMi5\ndJOQGmgbLnnXtiMmZSzaH/8hR208uE63KT0x9XwJ+D5VS7hZeWs2caXNvKV5\nEX/OGb9j7Idm4LtnZefFQyfTKfBdYmykMNP4Avp67mTcx9Tr7snTkZTUykxQ\nEJ7KcN1vwwjyzN/LbfWPDhG1CA8dFcFvWwt1y22S7lllPG+cEjMWxwbBsP2U\n4uGq6CGyvjB8SzCbbUIKGz3q/cKENgWaaB2V4MIV02JptKWIC7sXtclwdwah\ngkEW\r\n=lXB9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "8f0033fc230c4cb7945b5e3cda7e6902e178c747", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; cp src/RGI_Emoji-pattern.txt .; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "regexgen": "^1.3.0", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.3", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_9.2.1_1611911403238_0.23326161744932872", "host": "s3://npm-registry-packages"}}, "9.2.2": {"name": "emoji-regex", "version": "9.2.2", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@9.2.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "840c8803b0d8047f4ff0cf963176b32d4ef3ed72", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "fileCount": 15, "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==", "signatures": [{"sig": "MEUCIHcrGCbwiwzFWONg4AI9JKPLHIGCCljRRyA4x8QCMhWDAiEA22sFAR+MgR75takXx5jHHB1cLnhXUYIoWF5xMrMD5ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPhpBCRA9TVsSAnZWagAA1/YQAJ00Flm0hn1THHoKTG0t\nylutoznvVmXBkTi8RRd7sUoXWfWYTTYYd0kZy1sFqlBdqYSvafkt5gMvednF\nnbhYuFyYM+xhVaLJNiWspc2uMuUHwlivQYrCqH4Sak3YPzhk0hBuj5sgAk4U\nYcGYf4gKOVp/jwcmXVZWWFrn+Rm07/OHZzhIsBnWzBh99UWzFwFom/nGaoCe\nVZxx2OOrWmWFNVDKtubMnJADaHug7oo0o0S4YyMHQU6Rn69flvafdA5f076m\nnn9NVUO/h8He4xrLXoAnUybphaSNJPnFENqK1gN26e5nOzzsAxwRY5DKG9zA\n7czAXBGNAGYkjV8wOC+suYGgwvmfnKqf8h5Teerdo9Gcycn7sHIdsU+qAV2D\n+KqNJ8ql6nI0xrIf8lYtmxZjYjOPhUaKEer/sfxZUUqNzO0LdAkBOpK7Rga3\nFQyB0Pvg3VEFjmL78eDj3/D5GY1VYXogFXJY2lWrd02kPM6IlMjBQH9X9jx6\nwq84cFdGvDmW37dAtJdfuplkPNDd4I3Ivv8ASBphxqPjbSPCgn2vMueKIwHv\nEf8h8QUy9y/qo1QrVjo7kHrdFcjx73zgLZMElrlN9ax1KsgPxXTUmTZoN0K6\ntkC5T6h2LAnyxyi+x2/8XMnFTuzG20ExspSMVWBI4kHQtDWjua9YlCbIccgi\n81rc\r\n=B/lZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "0ffa466d7ab65af304d03dddd3a92a2d8268e7ce", "scripts": {"test": "mocha", "build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src es2015_types -D -d ./es2015; node script/inject-sequences.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "regexgen": "^1.3.0", "@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.3", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_9.2.2_1614682689140_0.39715833161311176", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "emoji-regex", "version": "10.0.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "96559e19f82231b436403e059571241d627c42b8", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.0.0.tgz", "fileCount": 5, "integrity": "sha512-KmJa8l6uHi1HrBI34udwlzZY1jOEuID/ft4d8BSSEdRyap7PwBEt910453PJa5MuGvxkLqlt4Uvhu7tttFHViw==", "signatures": [{"sig": "MEYCIQC0WKA0unPTztegVIqiguqkMsgSOJxwsTI/67j4WGQY8QIhAKDUOm+qs03QZ74wslSAY/LnJgoxsc4xGXYtjcJEPc6b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ppkCRA9TVsSAnZWagAAkJsP/2OKa9JeEY7zXZhs/N77\nKs4D+pj+MLDQRTzorQWutdSaIsW6Ehd4TqGTEFvpBNiSK6q/lVGSWrvRL+Us\n2+pzONm0q4CEXG3NpCQoZgAMEPL2+b9JkeD/x+OLY2gDIQT8z+m6t7sux11H\njs8Dn7+gpeXmTQW2qe+CR0TfMtSSV20a+szySdlW1iRzANaDYhhmFXRPyNlm\n2Tp+wnRDjPptXEPZqG3VGSENUGxxvHoG3eYBXzsOz7p5tuuIxg8inQsJM2Bk\nJeiu/mpNn+08lEfkKu9B0WdgX8m0dosSpUOIt3uniwLA804gf6Jta0iUjoWI\nqfirkwvbvqnnmqweYXislvM8rPx/24/efIyNUMpHs81bHqBC260RVST8hgv3\ngKNqeBYVAz+bnFZj3M33K+oxXXQo7qYeY9431/f/MzoFNFetujZ1iSlUsJbW\n1LhJvunZxitjPH/jVSqRWs8HcDvmNH4Q9uX+7QfuXYqHmlY3kTdUIh1diTOd\n6c/vsg0gsMVYcFou2WHBNUJzg9XWbYAXZtEgQmsLpi5gZnT7li+BtnRVf8co\naO0e3oq8XIuJi9hOlgdncOF1Da3W7ixtv+HBSDwsbNetK4pNik3SehgSgCG9\nf0wxmwFI23QxdFLTJYtQUR9YQ8p2Cz3RaxOUSzD5ZMHh2nm60n1KNjumiza/\n4ZFf\r\n=9KaV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "4b351ff8138215a963fe617d020492bd4766f690", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "14.17.6", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.1.2", "@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.0.0_1632751287205_0.7117707241122113", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "emoji-regex", "version": "10.0.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "77180edb279b99510a21b79b19e1dc283d8f3991", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.0.1.tgz", "fileCount": 5, "integrity": "sha512-cLuZzriSWVE+rllzeq4zpUEoCPfYEbQ6ZVhIq+ed6ynWST7Bw9XnOr+bKWgCup4paq72DI21gw9M3aaFkm4HAw==", "signatures": [{"sig": "MEYCIQDKC7Q3SHdAM4V4DTQD8riNRIPl+ONSe34cpbpSYQ0M/gIhANzwKJ1gmLQDGU1Dxr4HWs3+wQhyaeYVGZDc3a+uKUWy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJd01ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+hw//X2lhQ+YdxYTABgLnV+afUZeDtFru4q/U19EGZc8snCYvO/0m\r\nuJFDIX12nearl08jWJk7o0ZY94AzQ70AmLHkRw2z1MaEVswK3lE8RftlILjp\r\nmVN1ahkRo9I73sxdcnB5B9m4Zvfy8jSxdsDL9NYZQkTzA1JTPnPVuXSSNJEz\r\n61hvT8X1LPmRcRc8n+dF6ECjZWn89xQq/Wy/Izv/I5EqPSQLDoICW1azRkMR\r\neuHOQbbswoN64D9BU43aKzn1WflODhi4mEnRUAL79l3CTBfH7NW4xPKAa8cJ\r\noNyH8UI26/5xcJLZ3Fg4sLpUu251tKvzGnguoxOtZAWxpiamd/aGEidoVt6K\r\nVW122E7RjRBbdKG8htQPkiq19UvTygOuyY+6l6rbT/ZolTiBZAsy4kYMOhaW\r\nBaHukfzxI7g9qTrE0TpuRduHBcffF+iWNO17kmqIaDxTKhTHa/PX5kcNAmsp\r\njnb7ADL/A9FPcLG0nSAMuPJDQOMwHKkR2uacLiX6jnJrlLNuNqdYCMbjlIy8\r\nuGoo0E9CS5Qh4u5wa17ymmYSTqQB1gxve0Den8kzt3oVrlPuGsvWYOEEhmph\r\nY96IqMtg6AfHuALNzJYH1Fg6FQUJcz1KpUvKVcHDFGx98GCRnq7gw6PBL5K1\r\nYBkIjjdK8rboYDcskR5s2sdbDBGDbTvXNnU=\r\n=OHD7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "be333b33e32e21cb085c8a190f855f9037c0bcf2", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.1.2", "@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^1.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.0.1_1646648628924_0.9085361564769059", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "emoji-regex", "version": "10.1.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "d50e383743c0f7a5945c47087295afc112e3cf66", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.1.0.tgz", "fileCount": 5, "integrity": "sha512-xAEnNCT3w2Tg6MA7ly6QqYJvEoY1tm9iIjJ3yMKK9JPlWuRHAMoe5iETwQnx3M9TVbFMfsrBgWKR+IsmswwNjg==", "signatures": [{"sig": "MEUCIE9bKulpuIaGofthOg6rhMtX8n4HYnkrTVdOj05py1TZAiEAkXfB8g5fsXFnJgzIm/DCrGqePZQAG1CkVAOt5b/Qu0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRtgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpS9g//QRVxHimrZXwYOvMrIlc67eFBNsYBWaJR3CMx/TaLHW0G+dmt\r\nzedh0khhO+PgO4EE7TeOr/aI1E43XjECrNwpX0VcxufVFzy42J0kBt7Gh67N\r\nojUxuVIpwaBapN66jYJKoAcUM4cuilIYAbryyIyGLx2fsDiO/T41QMl8UKyo\r\nUYJxKmyo6WOyEwL1NC26FyB1Vo7LS8greqwEw4RRaLbxxY6ACmF2akH3EXvl\r\n+C471Xb3CaLsFrq9BdrdP8MI01JxhRXQvFViZVdhF5+/xYYoP3Xaom9O49hB\r\nkazJbZ69jnNtVTBmKItNbOSJIWKgO2Ax3FB1y+9Xq15AGSGn9PXtGcnwi+W3\r\n3a9ekgcmgyaa0r02jwIs6F60m3yP4TqLTI7oP6pqenEKy2obasTHLDTvA+Ji\r\nBoZ8rxuVFnNDOzvzuSgW0U3R6z4sotibJIMFQcUe9Fkf9kPex57hbU1doXMc\r\ndL7zZV9MZ1MDywMCfmaRKsx7eS7QFoRB/ZSV8RIxrfZTQmXnFjACz/YF0i5e\r\nu2mWcrn5eYkEwdAQgGb/4GjisDV8UhOTIHTCOhA42Ma3E1X67Ssw9Kpik7Qw\r\nWdO5RwVh4XWK/T5EfdATuSsAnB//lxFL4Oqp90dtbimGL/zQj2q23sfZSMdq\r\nGdr3HVs1mjdwUp6QdpcYU65SKuRYV5Gb32E=\r\n=E9Bn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "d342252ff3ff2e8912a686a1e77b0cb672530844", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.2.2", "@unicode/unicode-14.0.0": "^1.2.1", "emoji-test-regex-pattern": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.1.0_1648809994129_0.7557956790845277", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "emoji-regex", "version": "10.2.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "a027bae7f5624a726f41c219783a4401e6a73f4b", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.2.0.tgz", "fileCount": 5, "integrity": "sha512-vum/WOT0ZbG61R3bmfZ+Yw+MsOC7maogn5mjBiY9m4Ga9d/jEEr2hB+DXM+/T/jEpWQAgkB+cfYOrcbRRxltVQ==", "signatures": [{"sig": "MEYCIQCVzK/zNgYK002RdNXrrylzEl/WSf4uXTp7icCcuHbYYAIhAPBRLWcifvAdH8hTSE/LO/aBbZJXSMOKgwce3TjB9u2x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNabbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoWA/+P4r3oxbVKpMdHjxCFjmKyFW1W9qbJVHZl6mwDFpzXlsuMOmW\r\nw+rfOI0AUsLafg5Llf1//2/5kc1aQSPgmnrvDHXV1pDuZstZT6FLsS3CJ6t9\r\nwEkHKu/wIdvBytFnRUWN/u33Sb3BlNerfYQm9M+5gm5kUuu2xNlpOU8eE8qb\r\nDEyxpkLMiP1gvYRwPKCG10MYgAsfFDXqpWxlyAezLoiI8WnM59xmbnezuNnY\r\nfn6RrNWGbuBPTd4FHLC5Zu7Wl1RCk2a7xwChXuP9CQ620dn9HCUor+vxmG3a\r\nZkPDnrR09G8K0Gr4rl+GsaNA2cUA9mjzrxsTZG5r9qSZlOzQateXpeP3jRsU\r\nSDCFs/WwLgLidyCgT4TpbQDgeU7qAk7RakwuW29wHL0sO7a5L34X4JOq6US5\r\nbIXZXl2eUbt9VhC82nenwN4dprFu/AOXjHiRAXc6VFIHKQQCjayzfeSjnyV7\r\n/yRs80q1yrqPykThyeLqw56wxffSWTcfkB58bUJNoLfdaGiLNRbe5uu01bI3\r\nDSQz90ndQJIrcsI6oIl7uF6m/cjnSE9308tdoT5U9T8cJSLDs05tON/AFN+J\r\nyF89ItQA+UXnbQ5/9sjvwM3vU06zd6cYagwENeLRKkvpvDe7wP1RmyCxFvnE\r\nNTiE5s+xksGOaUvmRhtrZAtP4nyx+Cal3Dk=\r\n=8LY+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "module": "index.mjs", "gitHead": "a5207f2484d2ac9ac1975fbcb3d2a1f9e2cd76bc", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "@unicode/unicode-15.0.0": "^1.3.1", "emoji-test-regex-pattern": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.2.0_1664460507693_0.6045529408307782", "host": "s3://npm-registry-packages"}}, "10.2.1": {"name": "emoji-regex", "version": "10.2.1", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.2.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "a41c330d957191efd3d9dfe6e1e8e1e9ab048b3f", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.2.1.tgz", "fileCount": 6, "integrity": "sha512-97g6QgOk8zlDRdgq1WxwgTMgEWGVAQvB5Fdpgc1MkNy56la5SKP9GsMXKDOdqwn90/41a8yPwIGk1Y6WVbeMQA==", "signatures": [{"sig": "MEUCIGqitw9fvjFFGoiIlAH782O+mOLNOmbUlojHExFRXK26AiEAmasLPGr5rFf493M8MxGagYTLgXniN6U9y3TZly3dpa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNeOXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmruiw//VmrdABpnOgWpe+LzUgqyz3Wuf3Jt9wxT8vFgnfBJumqt2Jht\r\ngEuCdJFVgpCfh1e7ju/vmriPHn1LHjOA9A0rFznTpWb6oK0klNpDvRbezSku\r\n94j/A57b7xHCigX6L6gIZBba40u00dVwMzpsH7zJ2hwjZ2lth755SFuSJVlh\r\n+x7WLK+lzBGaemo1qJyt1lmmcrUdpm0hpEkqD9L1mowAPAGvyYEjMhKFY6nj\r\nXejtXzSA7MkiiePsr3gkk96Bzu856TtgSjgIE4k6yHM8b/Ek3ko2+J4ggRWm\r\nF1XlIRQIf35F4mrsqyaWNGGHjgxKmiaJFsNgFBPFNY6rgTyNn0ryhY1BNL/e\r\nTBvh+eJ03zzN9a5ARYR8v6VJ65I8tOc6TQBWQnzljqGvvBt61Vc2WMvzcRyG\r\n+tEtIuY2lZdJ8FqXTD5x+dFgkg9TkLIzon5SCcE1CHwlKU/3GpLfps07jGiz\r\nAnlypXuu1XoXvWmjECeKRbKNO7J4lYuwC9xNT1KF7uIHbIzaTlVHLpo34qTn\r\nasqhYGrz4KaTvIj+HDbm0xaOz44W3rS3Vsf2QZYFLeZ5K8pIoqym+u7JMwz+\r\nAacXIE4cZv/26Foj1hmwI327JQb1Y6NOtWnGrcu3gkH+E+a9ewxSzYh8Tmo1\r\n5X9G4HOCLt2sKwict4E37nW+xCjh6S4ymBU=\r\n=8Wcx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "module": "index.mjs", "gitHead": "6acf03a84f4ceb0728643057fd03ea8f0f970555", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "@unicode/unicode-15.0.0": "^1.3.1", "emoji-test-regex-pattern": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.2.1_1664476055777_0.26730326447221886", "host": "s3://npm-registry-packages"}}, "10.3.0": {"name": "emoji-regex", "version": "10.3.0", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "author": {"url": "https://mathiasbynens.be/", "name": "<PERSON>"}, "license": "MIT", "_id": "emoji-regex@10.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://mths.be/emoji-regex", "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "dist": {"shasum": "76998b9268409eb3dae3de989254d456e70cfe23", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.3.0.tgz", "fileCount": 6, "integrity": "sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw==", "signatures": [{"sig": "MEUCIBcULqmEAytnHuqH+u6WFFDhHHz1+ytqaTFdeibVLeRcAiEAnd/b374LWR/ah+eKZppCpNcbnLtu9tKpBPLWDnO6uTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31536}, "main": "index.js", "types": "index.d.ts", "module": "index.mjs", "gitHead": "3a5f0fbf4448826eb5ac91283b504e1c68fc928e", "scripts": {"test": "mocha", "build": "node script/build.js", "test:watch": "npm run test -- --watch"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mathiasbynens/emoji-regex.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "@unicode/unicode-15.1.0": "^1.5.2", "emoji-test-regex-pattern": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/emoji-regex_10.3.0_1697533349188_0.044973383042357096", "host": "s3://npm-registry-packages"}}, "10.4.0": {"name": "emoji-regex", "version": "10.4.0", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "scripts": {"build": "node script/build.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.0.0", "emoji-test-regex-pattern": "^2.2.0", "mocha": "^10.7.3"}, "_id": "emoji-regex@10.4.0", "gitHead": "8a6871a787a9c9441ed5a341951dcdb1fb3c1d1f", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==", "shasum": "03553afea80b3975749cfcb36f776ca268e413d4", "tarball": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.4.0.tgz", "fileCount": 6, "unpackedSize": 31990, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAM9EoNtjw4nz2NrmpElyoVGH5PkB1fRuQJbUBVH0HmiAiBvlrSf30DraJsF9AZ2Nj6xQQLs5QMm0UaV00DwzMdM+Q=="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/emoji-regex_10.4.0_1724676067217_0.16435619762092735"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-09-28T11:10:47.034Z", "modified": "2024-08-26T12:41:07.513Z", "1.0.0": "2014-09-28T11:10:47.034Z", "1.0.1": "2014-09-29T15:38:43.645Z", "2.0.0": "2014-11-04T06:29:44.152Z", "3.0.0": "2014-11-27T11:49:34.181Z", "4.0.0": "2014-12-17T23:29:54.222Z", "5.0.0": "2015-02-13T22:46:20.711Z", "6.0.0": "2015-07-04T11:57:13.288Z", "6.1.0": "2016-12-21T19:26:57.851Z", "6.1.1": "2017-03-08T19:30:43.274Z", "6.1.3": "2017-03-09T12:59:14.686Z", "6.2.0": "2017-03-15T12:43:50.440Z", "6.3.0": "2017-03-15T19:54:04.590Z", "6.4.0": "2017-03-15T20:45:40.423Z", "6.4.1": "2017-03-15T21:05:09.685Z", "6.4.2": "2017-04-06T11:37:02.494Z", "6.4.3": "2017-07-02T08:55:21.949Z", "6.5.0": "2017-07-15T22:10:04.552Z", "6.5.1": "2017-07-26T13:42:07.910Z", "7.0.0": "2018-06-08T09:12:15.142Z", "7.0.1": "2018-08-31T19:24:00.344Z", "7.0.2": "2018-12-20T11:08:57.200Z", "7.0.3": "2018-12-21T10:26:45.903Z", "8.0.0": "2019-03-05T18:58:23.040Z", "9.0.0": "2020-04-10T09:35:09.769Z", "9.1.1": "2020-10-13T13:21:55.541Z", "9.2.0": "2020-10-20T17:12:40.635Z", "9.2.1": "2021-01-29T09:10:03.378Z", "9.2.2": "2021-03-02T10:58:09.319Z", "10.0.0": "2021-09-27T14:01:27.346Z", "10.0.1": "2022-03-07T10:23:49.050Z", "10.1.0": "2022-04-01T10:46:34.281Z", "10.2.0": "2022-09-29T14:08:27.887Z", "10.2.1": "2022-09-29T18:27:35.963Z", "10.3.0": "2023-10-17T09:02:29.400Z", "10.4.0": "2024-08-26T12:41:07.335Z"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "license": "MIT", "homepage": "https://mths.be/emoji-regex", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "readme": "# emoji-regex [![Build status](https://github.com/mathiasbynens/emoji-regex/actions/workflows/main.yml/badge.svg)](https://github.com/mathiasbynens/emoji-regex/actions/workflows/main.yml) [![emoji-regex on npm](https://img.shields.io/npm/v/emoji-regex)](https://www.npmjs.com/package/emoji-regex)\n\n_emoji-regex_ offers a regular expression to match all emoji symbols and sequences (including textual representations of emoji) as per the Unicode Standard. It’s based on [_emoji-test-regex-pattern_](https://github.com/mathiasbynens/emoji-test-regex-pattern), which generates (at build time) the regular expression pattern based on the Unicode Standard. As a result, _emoji-regex_ can easily be updated whenever new emoji are added to Unicode.\n\nSince each version of _emoji-regex_ is tied to the latest Unicode version at the time of release, results are deterministic. This is important for use cases like image replacement, where you want to guarantee that an image asset is available for every possibly matched emoji. If you don’t need a deterministic regex, a lighter-weight, general emoji pattern is available via the [_emoji-regex-xs_](https://github.com/slevithan/emoji-regex-xs) package that follows the same API.\n\n## Installation\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install emoji-regex\n```\n\nIn [Node.js](https://nodejs.org/):\n\n```js\nconst emojiRegex = require('emoji-regex');\n// Note: because the regular expression has the global flag set, this module\n// exports a function that returns the regex rather than exporting the regular\n// expression itself, to make it impossible to (accidentally) mutate the\n// original regular expression.\n\nconst text = `\n\\u{231A}: ⌚ default emoji presentation character (Emoji_Presentation)\n\\u{2194}\\u{FE0F}: ↔️ default text presentation character rendered as emoji\n\\u{1F469}: 👩 emoji modifier base (Emoji_Modifier_Base)\n\\u{1F469}\\u{1F3FF}: 👩🏿 emoji modifier base followed by a modifier\n`;\n\nconst regex = emojiRegex();\nfor (const match of text.matchAll(regex)) {\n  const emoji = match[0];\n  console.log(`Matched sequence ${ emoji } — code points: ${ [...emoji].length }`);\n}\n```\n\nConsole output:\n\n```\nMatched sequence ⌚ — code points: 1\nMatched sequence ⌚ — code points: 1\nMatched sequence ↔️ — code points: 2\nMatched sequence ↔️ — code points: 2\nMatched sequence 👩 — code points: 1\nMatched sequence 👩 — code points: 1\nMatched sequence 👩🏿 — code points: 2\nMatched sequence 👩🏿 — code points: 2\n```\n\n## For maintainers\n\n### How to update emoji-regex after new Unicode Standard releases\n\n1. [Update _emoji-test-regex-pattern_ as described in its repository](https://github.com/mathiasbynens/emoji-test-regex-pattern#how-to-update-emoji-test-regex-pattern-after-new-uts51-releases).\n\n1. Bump the _emoji-test-regex-pattern_ dependency to the latest version.\n\n1. Update the Unicode data dependency in `package.json` by running the following commands:\n\n     ```sh\n     # Example: updating from Unicode v13 to Unicode v14.\n     npm uninstall @unicode/unicode-13.0.0\n     npm install @unicode/unicode-14.0.0 --save-dev\n     ````\n\n 1. Generate the new output:\n\n     ```sh\n     npm run build\n     ```\n\n 1. Verify that tests still pass:\n\n     ```sh\n     npm test\n     ```\n\n### How to publish a new release\n\n1. On the `main` branch, bump the emoji-regex version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\n_emoji-regex_ is available under the [MIT](https://mths.be/mit) license.\n", "readmeFilename": "README.md", "users": {"rocket0191": true, "zhenguo.zhao": true, "netoperatorwibby": true}}