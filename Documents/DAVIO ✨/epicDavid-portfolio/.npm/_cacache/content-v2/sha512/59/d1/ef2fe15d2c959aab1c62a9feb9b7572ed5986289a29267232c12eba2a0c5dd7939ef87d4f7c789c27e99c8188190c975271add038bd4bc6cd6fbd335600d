{"_id": "@types/hoist-non-react-statics", "_rev": "409-017eed5f347a5b3b377e459d793039e6", "name": "@types/hoist-non-react-statics", "dist-tags": {"ts2.8": "3.3.1", "ts2.9": "3.3.1", "ts3.0": "3.3.1", "ts3.1": "3.3.1", "ts3.2": "3.3.1", "ts3.3": "3.3.1", "ts3.4": "3.3.1", "ts3.5": "3.3.1", "ts3.6": "3.3.1", "ts3.7": "3.3.1", "ts3.8": "3.3.1", "ts3.9": "3.3.1", "ts4.0": "3.3.1", "ts4.1": "3.3.1", "ts4.2": "3.3.1", "ts4.3": "3.3.1", "ts4.4": "3.3.1", "ts4.5": "3.3.5", "ts4.6": "3.3.5", "ts4.7": "3.3.5", "ts4.8": "3.3.5", "ts4.9": "3.3.5", "ts5.2": "3.3.6", "ts5.5": "3.3.6", "ts5.4": "3.3.6", "latest": "3.3.6", "ts5.1": "3.3.6", "ts5.0": "3.3.6", "ts5.3": "3.3.6", "ts5.8": "3.3.6", "ts5.7": "3.3.6", "ts5.6": "3.3.6", "ts5.9": "3.3.6"}, "versions": {"3.0.0": {"name": "@types/hoist-non-react-statics", "version": "3.0.0", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "1c6a0869d9769f189f4932d1a64ec7c7a2c8fdbc", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-Qdgaa9I0wMwR6q5WA0Ulmu95sTQGHscVEpJbOcHn2rqb69RMUw0faBCoU5t92SrXlAZfqJ1Secpa8VzB/Yerrw==", "signatures": [{"sig": "MEYCIQCBOQW+8uF54yTM4tFNTL2NvWNCBUaCzpof87COZkOd5QIhAOeBZbK84MWasItyx7iogtDEBq6u48kpIrjqQ3anACfG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX5z8CRA9TVsSAnZWagAAUDkP/Ro/2dcFBrsl23+OfCX8\n8gOwLj9jGw7A4rGIwqUc2LWyR1LFiweQQKQezKqH/ZYBKQqoac/l8vjCO4+x\n62egm2Ts+fvgO0Q5ArcydB0zwjmhF3fsWKUFqh3I9odtyF145JaXP3wC+FNK\n5tZLo6no6d2RbnlGiIIdDORPM2MI8T9Oxu4EavV+ztGp+DN/NU437FH+8/UP\nN7Ms51OpXpISUBGNRADu4qT0sTb1bO/k66BbTOQ9M7aXoTTKG8jGsRP2ImrP\nDistYCHPuUCKxxce2ADkpXFqIiGMpdcdYpPYZZB8QXOXZVJlbYx7Z2ccZPKI\nvy3VahrsW393mw8t8lCVUIqapGFGRIb6jel7H6dJY6JT575O1pVoFW0/a8I6\niVTZdzWihI4fNaChzLkdl8vvQDE5qnU5DRp2v2ebyVG8a4sKslVIRKrFal06\nWTtQQP7AlK4BOELKMwu/qJfh5YO75zMOChs+eLnk26dKTs2sXGsUPBOKedGF\n68Mgp48p93KYf+7jqZU0rsPoulptEplRCoaqDHpeqICcXXbcP9KZ7k4xqOUU\nuy3Z3V2wU+Y9INolmGIVgePvG4WjPYKAsTEj6q8/AXWowC5nUwrWP4W3L2rl\nox+B6L2AMSqWjZ6niA0jakI1kwcEv9ujJVBz+HkoGXQCvbaysBmTq2h8pXKQ\np1vR\r\n=DTsp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.0.0_1532992763947_0.15533477100439663", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9fcc563f123747ac7e7e08c736548ad3b42926bcdbed398b346091e459ec832f"}, "3.0.1": {"name": "@types/hoist-non-react-statics", "version": "3.0.1", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "dde7c53101912dae8f45a1807f9857a59ddf3919", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-3wTz66vV+WatOAjMST+hKCmo01KYPFgnsu+QeLcn0FuwPCoymX6aj1a4RvFCdVsfh2m0hfTPhE/zTv4M28ho1Q==", "signatures": [{"sig": "MEUCIQCVR29akhAqhKAFOw6RN+xkpAsWiHHf2eUFl5UtUQAriQIgN5rr85xbJIX47dX6DLt3h0wRW2OZNRb2StA+2dbSJzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYmAcCRA9TVsSAnZWagAAO5IP/R+PVRjD3tehUwaGbbhs\nQB3q46oPYporsm1gtKerjWJX89N7tYNlt4Go3huqM9JGTWYByA2sKjVt+mnV\nZ9948N5dafx39bsEVHW/+QRddLyDfdS/SzBevdmIj8I4Xykdf2enu86DLUXl\nYfSbGmu69gJURIJSiv3FjHECezWRb7vy9PYefqUCq35JK9sn4oHzx0cwsgO9\nPsIDUkKo+NotKNciTeUUl4AX0qNJ6KQtT9QEBGuLTUoGU8xvQbRrqvY3x3Fh\nuSR/OylrorhnT5y4oiYcTh+hpnB6Rxr6dGnY8c/T6+teS+slHuZf0qfyeT9Z\niXQTzs7426vQUtDQaPPwWeI+58ACsXLoHFrXfCbDDkyN5KDYcn+XUfE6WPC8\n6eOhMCeL5H0cO1sB/chg3Fz89uNNvYYQ9uGE4ltCZPzSJMC44bwdFZbRwSn6\nUV55MioOMg5pNMi+Rzo0pVOyzU13r+Q+ca25VKBizXuLo4moxcV/5Wwfc1fn\n1R21M6ydNI8jXfSFeS2wKanaWmqc33zU+AuSRiq8DOLT3iD8XWmTmbhd5H6e\ngO9O4G+0RaJv4jbUNiR0gEfplpAo8Ua1zuxKR4jyhdgIptGlD/PMHxEVGhNL\nUHuvQGPcha9XRLGQT4jy7NHa+RZ/F12P9b+LnghxnefjPM0ifd0PFDD1WFM0\nWDtq\r\n=ZTU3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.0.1_1533173788383_0.4767758970664686", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6178f226c44e51550f9d101aa14838771dc27fe597199c9c1052a91d64656f1f"}, "3.3.0": {"name": "@types/hoist-non-react-statics", "version": "3.3.0", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "dist": {"shasum": "a59c0c995cc885bef1b8ec2241b114f9b35b517b", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.0.tgz", "fileCount": 4, "integrity": "sha512-O2OGyW9wlO2bbDmZRH17MecArQfsIa1g//ve2IJk6BnmwEglFz5kdhP1BlgeqjVNH5IHIhsc83DWFo8StCe8+Q==", "signatures": [{"sig": "MEQCICOYrS1V2bUhl5/ypsNbp+ee5dfzbQdb/F9PF7I+wnE6AiBpKEcFPbuCQWyGpxASpCE6W0WxUQlTmfemIg4zgYYAGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfpTUCRA9TVsSAnZWagAAfr0P/2+VgH30ewDjMA2C+WMT\npswYNPEkvandomwLHMdoB6vmX0SnW1YUITboTAOW0ZI6oohvi39qxdrqEKv+\nUUefYYgkFHDbsTmgcIUx8Y5F1z5S4b4K6DySTr1JXHxeH/k/NF9bCw5E17nC\nt/k/hLBcffTQrbbgYG2NvxisPsU1XJV7U586CYflJpetBNcOvkFEXI/wpEIc\niICgVZD4g/HvHjABaMkRJ27LY4LRsX1ETQHVX1pCfdPpv3WKAt79sBo1TOdw\nVhkWkinDeInbjQJLk2tlRC3JhyEcNZWAxq0bnVKQl+GKCwW5lBLGOM/LC4MW\nocAuU7EFIE7wN+zoGIJD3M55Mblxux2vsRsYltEGMz5/oyyifDW7iHR2GFZn\nrxZADOrLqnnu07cA5O/EQ68b0QoB1udWXE7SHEU7bbwe2TFCp6qzvbXLU49r\n6E0zNSEhkIFPLcd7ys5592TzGGxAK5WDLIGCf3Zce71bTms6adhPAYiGbObi\nGA+kU3Yns55AT3TtxB+fYRISJQmAInQMtpOoTsEq991aDgdOEDaocfqtdE/x\n1ZpzNH/CfzDOyBMjBt8aX4pDcofPh3y7KCNeebe7NYDaw/H8OrAtal5xTeLf\neVPRpPoKlqQbLNf4pJI+9x6kRALR4MZXxzUTIgowMhA2RD0YFEAea5kHjCxg\nlWhk\r\n=zZEq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.0_1551799507575_0.11775293468728498", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d074910372938383f7172eca39df8a7a15f3b55a7bc42debe7119fb34a5e7925"}, "3.3.1": {"name": "@types/hoist-non-react-statics", "version": "3.3.1", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "dist": {"shasum": "1124aafe5118cb591977aeb1ceaaed1070eb039f", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz", "fileCount": 4, "integrity": "sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA==", "signatures": [{"sig": "MEYCIQClW9UFCkJs1xJqAg5a1HG21tE8pKHtMgB+YdAY8vHF8wIhALxeuP+DU4xXBUlpvmWZQZwSYvn6xhH5pZlRL48no8VO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcplAVCRA9TVsSAnZWagAAm/AP/2rllqBs9U+cLUU3L1+F\nqSUAk6PSd39CCIFiEnHpOu4EvPiv9kjFvNRJa9BenOKK61+rCukSWLcRp0Tc\nwc90BZsjOvftLU4MihBbPvOo371fqe/+Z1ER+AtC87BF+335WkQUa5edRSmV\neXhKh3oQD9fp3NYe6ja4HcdUhvkAUx81lk2b+A2pY22mLdkh7Ya1jDdUYAQ+\nD9AIjAP6qU9JG55n4dY0v8GI/XFjgB/iw72Ux0zk+WN6PYuJGGBr4qNb9nJF\n5AbYBmk82I+ZtOCNvRVzmlH1WRygJGsvud1ie+GzLOoVR45TaqaFcmZGNMgH\n/q4UJY2HRzI5MHmdgrswosQnsYynsUag1mKhtYXZ4G88Ml9E035fA72ZdM30\nc05QOplDRgVopmraGWjE0Ntzr2/hjOTmtAjcU0j3h5Z8JCB3sz8a7mQR5gxT\nvEh8QAXwbM+Buo/StTe9gXY0YyDcu448+aUMcCyYklKZGPS8VA6+VBqGih6y\n/rovssjBqm9UIRVzOMGCAcQ0nwX5PCJCCOYtM058b86B3WlBqTaiVCc2Cvo6\nmWfAuCJu/+rI85IQjr5kfs1E945TyrVmKsrAAoQ6JYsul9uR8RYuDxVJVbDF\n4I+JKRgoUlGb2H88nonRr/LmvVnjvvSHhJ4D0w6pvp8pHQT5GZbBoZ0/dNWa\nrTzO\r\n=7CBT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.1_1554403348338_0.566346718427164", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "666e47d028495104f280da8e5a0caa7bfd4d6ecc4f113c4b60bec065e6e4c5f2"}, "3.3.2": {"name": "@types/hoist-non-react-statics", "version": "3.3.2", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "dist": {"shasum": "dc1e9ded53375d37603c479cc12c693b0878aa2a", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "fileCount": 5, "integrity": "sha512-YIQtIg4PKr7ZyqNPZObpxfHsHEmuB8dXCxd6qVcGuQVDK2bpsF7bYNnBJ4Nn7giuACZg+WewExgrtAJ3XnA4Xw==", "signatures": [{"sig": "MEYCIQCnfB2/AFRJcwb3y9g2KKii0JZvVCcG1OwVpej7i/ZTegIhAK3GaLHJb9P6jOxNNqAXOfnOmR54jtoHirJhqyG5Fk8a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7033}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.2_1694852202948_0.3611844709237255", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b11019c7ab872577ae5dee51c0bf2a9865c3de38d9b865aba1ed8a9371c016a6"}, "3.3.3": {"name": "@types/hoist-non-react-statics", "version": "3.3.3", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "dist": {"shasum": "8bb41d9a88164f82dd2745ff05e637e655f34d19", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.3.tgz", "fileCount": 5, "integrity": "sha512-Wny3a2UXn5FEA1l7gc6BbpoV5mD1XijZqgkp4TRgDCDL5r3B5ieOFGUX5h3n78Tr1MEG7BfvoM8qeztdvNU0fw==", "signatures": [{"sig": "MEQCIEfWxHPA2kwK7nk4j7US1j/wPOMfyqkYDcKv57FLgrACAiBJFmD5KPrsi2qHqKHbnSXwDqX+CncUJSqOeWs6uLS9QQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7033}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.3_1696966063531_0.7754783617896968", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "86850cd33faf8cb112ddbee932712009d68fb360b4a693b5a1c41c3cdd91e173"}, "3.3.4": {"name": "@types/hoist-non-react-statics", "version": "3.3.4", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "dist": {"shasum": "cc477ce0283bb9d19ea0cbfa2941fe2c8493a1be", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.4.tgz", "fileCount": 5, "integrity": "sha512-ZchYkbieA+7tnxwX/SCBySx9WwvWR8TaP5tb2jRAzwvLb/rWchGw3v0w3pqUbUvj0GCwW2Xz/AVPSk6kUGctXQ==", "signatures": [{"sig": "MEQCIHic+SGbjNqsVY3i2lbVmc3esuWzkjZO04AYRf9htJWQAiA20TFHjjZ3H+F/Use/U8ouwKI+603fKyCk6/gFHjr7YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6445}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.4_1697604455284_0.5423941693742818", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d27e3ef7242ef79e262723c3b7b88e96be8f199bd9097f71f4dc04509e323677"}, "3.3.5": {"name": "@types/hoist-non-react-statics", "version": "3.3.5", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "dist": {"shasum": "dab7867ef789d87e2b4b0003c9d65c49cc44a494", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.5.tgz", "fileCount": 5, "integrity": "sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==", "signatures": [{"sig": "MEYCIQD47NwdTLGa/w53wgMqs4s8v9CNMOjiqR6xq282xfq8KwIhAKbz+ZzY4hyt6xZKrLJon6ILpM5WBv/UPDBwxLjptQy0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6445}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.5_1699342117848_0.7742855745693022", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e7a2e0e3acecd59daaf840349946feec09d36c71528bdf39ac38ab7d566d804b"}, "3.3.6": {"name": "@types/hoist-non-react-statics", "version": "3.3.6", "license": "MIT", "_id": "@types/hoist-non-react-statics@3.3.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "dist": {"shasum": "6bba74383cdab98e8db4e20ce5b4a6b98caed010", "tarball": "https://registry.npmjs.org/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.6.tgz", "fileCount": 5, "integrity": "sha512-lPByRJUer/iN/xa4qpyL0qmL11DqNW81iU/IG1S3uvRUq4oKagz8VCxZjiWkumgt66YT3vOdDgZ0o32sGKtCEw==", "signatures": [{"sig": "MEUCIQCL7ldp1OpWG1Xcz1iCBxFmd+IJSWifyp+SsrTgkDlHvgIgc6icftQS9YpmydRTG3fAtHE1qbacc+4VIJN1J76lW20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6451}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "directories": {}, "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/hoist-non-react-statics_3.3.6_1733427169147_0.04724035446293939", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f828fd090493b00844946121848239611dd37ff529ab0738bdbe126f4576476e"}}, "time": {"created": "2018-07-30T23:19:23.677Z", "modified": "2025-02-23T06:59:24.845Z", "3.0.0": "2018-07-30T23:19:24.029Z", "3.0.1": "2018-08-02T01:36:28.467Z", "3.3.0": "2019-03-05T15:25:07.703Z", "3.3.1": "2019-04-04T18:42:28.500Z", "3.3.2": "2023-09-16T08:16:43.174Z", "3.3.3": "2023-10-10T19:27:43.685Z", "3.3.4": "2023-10-18T04:47:35.521Z", "3.3.5": "2023-11-07T07:28:38.111Z", "3.3.6": "2024-12-05T19:32:49.314Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hoist-non-react-statics", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/hoist-non-react-statics"}, "description": "TypeScript definitions for hoist-non-react-statics", "contributors": [{"url": "https://github.com/JounQin", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jamesreggio", "name": "<PERSON>", "githubUsername": "jamesreggio"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}