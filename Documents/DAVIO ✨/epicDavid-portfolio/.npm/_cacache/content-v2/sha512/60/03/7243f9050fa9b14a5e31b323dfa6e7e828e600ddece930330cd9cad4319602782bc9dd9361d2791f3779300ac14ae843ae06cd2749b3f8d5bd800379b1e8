{"_id": "@babel/plugin-transform-private-property-in-object", "_rev": "34-0e5deae9409a626b3d14ecc943db0cab", "name": "@babel/plugin-transform-private-property-in-object", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.22.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "62d4c769545f105b9438cbfed844df27f3635931", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-P4bP+/4Rq6aQ/IZmAEUX+injSKhuOOMOZkXtB3x++P3k5BtyV8RkTvOtpqIv0mLpHge5ReGk0ijNBFRN0n2xEQ==", "signatures": [{"sig": "MEYCIQDpr79eUd5DL77+torgGhAEuA0GNTkTvFr8sFvnUPcH1QIhAPlWi7wi/v8T1AHDP/7WuBRgdbiEXm+xNGwMZQ8LoyC4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19917}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.0", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.22.0_1685108750581_0.2738398818807577", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.22.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "031621b02c7b7d95389de1a3dba2fe9e8c548e56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-C7MMl4qWLpgVCbXfj3UW8rR1xeCnisQ0cU7YJHV//8oNBS0aCIVg1vFnZXxOckHhEpQyqNNkWmvSEWnMLlc+Vw==", "signatures": [{"sig": "MEUCIQCeZWLjefS2mGRqJtmCsLIm8VjNQ6UqwmvX1Bj9LnhaAQIgYgivqPUg/i4LWboHr4UDyBzbi3zLLQP3PJlwkwFWzAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19925}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.22.1", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.22.3_1685182260562_0.33464769692209084", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "07a77f28cbb251546a43d175a1dda4cf3ef83e32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-/9xnaTTJcVoBtSSmrVyhtSvO3kbqS2ODoh2juEU72c3aYonNF0OMGiaz2gjukyKM2wBBYJP38S4JiE0Wfb5VMQ==", "signatures": [{"sig": "MEQCIHIqJGWNBvVitg+vFnMzP03bKnWu+JDS1Fiss2oT12cpAiBUQSfzs/FcEC8OI86ArWs7yRWF3d7t2I66Csh2Z0id0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19931}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.22.5_1686248509314_0.8857848381143036", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "b41010d31cefe019ac3aff1f882153c65118c346", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-cHVx0j25oRg011XQEd6upMA1xZuUsu/Tdrdaao0O0lAW5yMVEJ89DcyGPN9t23SCJbgse6o32uQ2z7rZT+DhBw==", "signatures": [{"sig": "MEYCIQDPJ9HEv6ST8BTY6HR4Fyc9h3HVyqzU59Cy6OFlJn/NcgIhAOmDSL6piGMevukhnEUfIchcu4Kn0uuff1Lo4V+lEjJI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19876}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.0", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.0", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.0_1689861631873_0.23641280351395255", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "9944152ec27df79c7ecdaa272e076f409eda65cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-CKdD53VE+SjzET8WF+vHRfh4+bScQrZBTMJ2PbGSWnRQiminelhMjtz1zrggmHDE8t4ibl8LWNfcW2erSwZlbA==", "signatures": [{"sig": "MEUCIQDSeVnNQmM4c49C2+hxNoUfOxRNXs+VZU9BYYZqV8UBoQIgabBVK/PNFftcgEzsE9H2cEfcNmNV0jNWo8PCREOEdUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19876}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.1", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.1", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.1_1690221185176_0.9548589604688849", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "0c888f7c17e08f5fbfc2c238f9af1a8b32a3f025", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-W5/uxHDrnoXW9XEk+ZZJtZhRTLBRtGdrjwBNRIsN3ad+KDZO2ESa3v3Vms2zftGh135G4kzZgObIhWJt5PYK+A==", "signatures": [{"sig": "MEYCIQCJGkpNaTuxNY6XErCqSZSqsfKR2hIJssuYkagHOMEYdwIhAKcKZWMHHuwvoXzE+RPZrIsbWtu0zH+/nFgZtEhRzwO+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19769}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.2", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.2_1691594127799_0.6015056813732724", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.22.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "ad45c4fc440e9cb84c718ed0906d96cf40f9a4e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.22.11.tgz", "fileCount": 5, "integrity": "sha512-sSCbqZDBKHetvjSwpyWzhuHkmW5RummxJBVbYLkGkaiTOWGxml7SXt0iWa03bzxFIx7wOj3g/ILRd0RcJKBeSQ==", "signatures": [{"sig": "MEUCIQCNvAFes3XWuVLuH+vY+Pw1kl6d9lm/n+p8Ssc8bQRfdgIgdor6jN8T0Uk7dabTFW4c0f6N2uiq9ePUxE8qwZotvs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19892}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.11", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.11", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.22.11_1692882527376_0.903242944616818", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "93e52dc4b32f0fd6f786d0a3476f26f596988d17", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-uW9xRMDnAN9OiEcwLkjXtozjOmeYWwLFOr8UUCrKBh1+CeKZtt/h7t9FrqRRT8/x6KwGWa4wUEfpRr+b5Jikyw==", "signatures": [{"sig": "MEYCIQCQRPiEIl7069a/5h2M3tSRryQSU+lXJ77F90tWsPS44QIhAJgX8yQK417sdMDBghzI7Nxpfrey7v6aq2RDstb/Ty1h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.3", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.3_1695740259747_0.9860291682534656", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "e7f6797a47dd1710e91032c7c51514dec3078592", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-cJgZdY4B25v2NOY9OR8b8WszpBosEVhioYt6LYhPue8OycNhLfr3l9WMO6GdBJMi5rFVj9pSBENLZ5YJ4vdhBw==", "signatures": [{"sig": "MEUCIQDYip7xGJZ707zQp4Vo7eAMA5skzynmx1IIJ663CQTAcAIgLEmyhP3mBTDfg3rW3d0+uP1I+SKJEjQGdgJQ9zU9dUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19811}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.4", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.4_1697076413599_0.8246729009951166", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "5cd34a2ce6f2d008cc8f91d8dcc29e2c41466da6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-a5m2oLNFyje2e/rGKjVfAELTVI5mbA0FeZpBnkOWWV7eSmKQ+T/XW0Vf+29ScLzSxX+rnsarvU0oie/4m6hkxA==", "signatures": [{"sig": "MEUCIQDadoVz3FXQVwrJmaxYBfyxpwiVdGqw9krn8okRjHyiUgIgKhgiaQ391nEkLrwSn3ah1KEY+mKpnPXAotTTx6FbqDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19970}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.23.3_1699513435475_0.42706245002622056", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.23.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "3ec711d05d6608fd173d9b8de39872d8dbf68bf5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-9G3K1YqTq3F4Vt88Djx1UZ79PDyj+yKRnUy7cZGSMe+a7jkwD259uKKuUzQlPkGam7R+8RJwh5z4xO27fA1o2A==", "signatures": [{"sig": "MEYCIQCH4QVJRzgDW2u8IbYMN4Rwa0b7KnP+VF9cpGsR1O8SbAIhALptkaMX4evBMdY8bO7BP9Z5nl14QFGCF+lKrdQb9Js+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19976}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.22.15", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.23.4_1700490129794_0.9407066308779921", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "680ebbe8a6c7fb0e5e402c8bbe793514f1bc97b9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-u9rAyzWCnWSmYth+i2i0bXXDpS0YdGt+WG0DLuv7UgadBwVnssdGZZJumOe4Sbz/uehGTuye4trfRSqzRPivqg==", "signatures": [{"sig": "MEUCIGEjjS4JiAzEOtajlLJ1wA77/zhE+WKu9PZ7R0BO/ZmeAiEAzSkykOz2a7mT+F/m+djIK/B8l0ra/lqtH2bC0s975Uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19930}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.5", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.5_1702307988026_0.6428772395118292", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "92355eb03836b6143c83453d0d25cc7cff0666d6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-rXJIg3VYfi7OrrIFhstwfvl7lE1pU41+egVw2uJoifums+KX8RDuK27mw9pPuigUtLda65wMOR4k+buN9zKasA==", "signatures": [{"sig": "MEUCIQDQta86/EjHAAeqAWdvaYljjfm1ik7qVCXh/RhBnjf0dQIgcMoKgTbb9scv0yp5oFjIGrXPbyU6uUB/iDj3Jjpr1JA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19930}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.6", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.6_1706285692353_0.008950116266669195", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "e7d90138cb8269d6cddc4aa979f5664ab43a8428", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-mO0tIEJAdOaPQThArqlPGuBL1d0WuDQICD+qN6yvq7n46BPnyLY/q/94NVvGm7fgUwRm+7Zn/3BMEfjlq6TCYg==", "signatures": [{"sig": "MEUCIQCEpF/+DIQMsBR/arZS9R9kB1gZGu8RxRkX6EeIgi6iCgIgd93TksndI4bTSCjnsIvnuyeI5d0m/OWXaolTloBsAGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19930}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.7", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.7_1709129150679_0.7637541298872401", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "756443d400274f8fb7896742962cc1b9f25c1f6a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-pTHxDVa0BpUbvAgX3Gat+7cSciXqUcY9j2VZKTbSB6+VQGpNgNO9ailxTGHSXlqOnX1Hcx1Enme2+yv7VqP9bg==", "signatures": [{"sig": "MEUCIQDxweGByZ5KrpqeEAt9Tx0utWsFco3af6bJapITUI61WAIgcvGQUsgN8PyCaBMIPEyGs9Ii7iNQsbHs37uVJ9MOtiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20037}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.24.1", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.24.1_1710841775275_0.8866973871745518", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "dc680463b4077045f945a0d0a94fe3f102ed300e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-z2PLUiSP/j/0ScKGtJMrvrGwUhCFwBrkZUvWloCAWVCrZaTnaOp+sPmukpid7Y8NNTwC1jEzw1mXW/o+QUBIQg==", "signatures": [{"sig": "MEUCIQCub74lMFlP20r718IsMQVQ+WyqbtKCufuss5aNeRIUHQIgGqzwJXVTLtX8mltWNYmdOssc1Xzi5vb+oeXefaXn5uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19854}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.8", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.8_1712236823341_0.14373463572892264", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.24.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "f5d1fcad36e30c960134cb479f1ca98a5b06eda5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-JM4MHZqnWR04jPMujQDTBVRnqxpLLpx2tkn7iPn+Hmsc0Gnb79yvRWOkvqFOx3Z7P7VxiRIR22c4eGSNj87OBQ==", "signatures": [{"sig": "MEUCIHYVfKv22UUDkguSRkZr5JHscd+vM+nxhQtndbkdhxpbAiEA1BlanW8QUEnpRvyKgMBbhgEQXi1c4uONx2obg8P3f1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87073}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.5", "@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-create-class-features-plugin": "^7.24.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.24.5_1714415668165_0.15078266592454082", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "59ff09a099f62213112cf348e96b6b11957d1f28", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-Qu/ypFxCY5NkAnEhCF86Mvg3NSabKsh/TPpBVswEdkGl7+FbsYHy1ziRqJpwGH4thBdQHh8zx+z7vMYmcJ7iaQ==", "signatures": [{"sig": "MEYCIQCqUTTy/COcGVMELLNyPlYHhEgoUhOBcD4DllDGSYrOgAIhAIRtJVyF8OFZZsBZfrkLfTiXwlbJG3Z8xtK+sZyvAWFP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87228}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-annotate-as-pure": "^7.24.6", "@babel/helper-create-class-features-plugin": "^7.24.6", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.24.6_1716553513773_0.9756320650356995", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "634ca23d481b23134b6a88e0ebf6ca53cbed538b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-u9oils0B8klPEMEyjVGXz6NaRqAzs+sNYfvp2oAxHiPw2UhEbCF3n5qSjF4TVEuo9QUzwJaTdjFOLnHluOr9kQ==", "signatures": [{"sig": "MEUCIQCNopgI++I1rW/ocZOV4uHstRjspDhdFOLckbSPW/JyNgIgbt7k+IrDnI4erjuXQl9lflO8/zm3wEbb+Ba6isY8qV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87297}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.9", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.9_1717423551644_0.20231323581315985", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "f48906eec7964d7eaea34cb22d3a90dde1be01c3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-o5jUuNdJ33hW9apkcU07OFt87owjmVTFqw8S6JLxkb0VJC+/JO0Xq0tM+2NZPiMFIIggnVU8NBTrFSVOM9wAgQ==", "signatures": [{"sig": "MEQCIDgoy84aaZ5Y/80G1CJ/O1fccLEOQuA7AP9T9IRAO8V3AiB7FQ0PSAC7AsmNoG0W5uIeOjxfxcAMd9BnQSnOwOtKRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87306}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.10", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.10_1717500047411_0.06874228810132332", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "4eec6bc701288c1fab5f72e6a4bbc9d67faca061", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-9z76mxwnwFxMyxZWEgdgECQglF2Q7cFLm0kMf8pGwt+GSJsY0cONKj/UuO4bOH0w/uAel3ekS4ra5CEAyJRmDA==", "signatures": [{"sig": "MEUCIDBtAynuuzi3Z74FAcqc+yOhSdxNUQsLnsNUIck/EVXZAiEA8P0o8bxOvf53WjRX/3vtIrynqk51oJHX3icHTD3sbTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87173}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-create-class-features-plugin": "^7.24.7", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.24.7_1717593360107_0.2853827612273556", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "3cf47080d0509873da46b9f083e880d8fd63a125", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-ejFQVncM4t4wSqU9XBss/9FSLk2RMsxQdrSTvyNKfJJJTmm+OU8keR2DvtytEjaQvap7RnGiNRc4G80wKco3Rg==", "signatures": [{"sig": "MEYCIQD4DTpa2lwUzPtbDMS/toAF9x9SzdOaVeGFY8rVX3mmuAIhAOoVcZ3sSGKob8X9qHSqqpd+gaxKghOsvd0Ba3yl4i1Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87195}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.11", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.11_1717751770672_0.8755029136451387", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "03ea9dea09fcf9b2082af4f6726624ef46ead486", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-evH7noMFyHvfLnxzynWMMTlgn3gmB9mG8pLc9O67Nf45g/HNBYuUDFk+2qwkhBcP2AooRl/CXrbgrPUqF8IFjQ==", "signatures": [{"sig": "MEUCIQD4bptVag8sbKUbk7hmWB6nCVMssf1fNzqXKGefONuPKAIgR2a1SmPEN7TxTIJHidXd3fCsfjsFqUTDULRu9+HGx8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83909}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.12", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.12_1722015246127_0.8673733646507973", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "aff877efd05b57c4ad04611d8de97bf155a53369", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-LzA5ESzBy7tqj00Yjey9yWfs3FKy4EmJyKOSWld144OxkTji81WWnUT8nkLUn+imN/zHL8ZQlOu/MTUAhHaX3g==", "signatures": [{"sig": "MEQCIBFPX+57009chFeWLeAsLnoN5v9fMPbnbTJuuMEOmzBPAiAxip777RNifJwAsJOpAhvpjQxjTQMxazI7yl/9vvjEcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91633}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.25.7_1727882136759_0.021250441257596453", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.25.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "1234f856ce85e061f9688764194e51ea7577c434", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.8.tgz", "fileCount": 7, "integrity": "sha512-8Uh966svuB4V8RHHg0QJOB32QK287NBksJOByoKmHMp1TAobNniNalIkI2i5IPj5+S9NYCG4VIjbEuiSN8r+ow==", "signatures": [{"sig": "MEQCIBCjf2As/1ZJ4Tq/7CaY4XEe4RioKUeAx3BdYZZuW3cQAiBXn3niVsGVJzyhGjuSuJJbl1SAl/yc/9EcSfyAuZLk8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91906}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-annotate-as-pure": "^7.25.7", "@babel/helper-create-class-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.8", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.25.8_1728566712862_0.011049904857998616", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "9c8b73e64e6cc3cbb2743633885a7dd2c385fe33", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-Evf3kcMqzXA3xfYJmZ9Pg1OvKdtqsDMSWBDzZOPLvHiTt36E75jLDQo5w1gtRU95Q4E5PDttrTf25Fw8d/uWLw==", "signatures": [{"sig": "MEYCIQCa2OMCxI88bz1o2is/stied7SwoEdcH5CiipylBaXUOwIhAJVBUxwrhNCK1pkjyKh0SuNgHZiCdnS5Fl+zDwTEEo55", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19812}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.25.9_1729610511942_0.19653411796467934", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "5fca1fb7bf21209ff24dad66a113b6807652d7cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-mL2VBqcq9WTMJt8LQg9RlZtB53uycA0CHRZ5o3gQWqifGlASaCx5Mjzm0Ra1B1ZC2FQu1JsliK/f1FZ4oxkhxQ==", "signatures": [{"sig": "MEUCIQDPBywuAjHQiCIVzdEddxgBzjcheDo1Z0JU2LlKxFwjVgIgHyZVbAejmq/97BNWbUlTRZkzC7XuHrzF0Ff+E6DL598=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20098}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.13", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.13_1729864492615_0.7443467378209512", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "117b03f35efd8a8a2681720692691823a97507a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-bF90yK92UKZdE5M7+KHnTO5w72klU1D02eci4PCLcpVemsch3eoK5IsF/JxVgq168owN5r+LvNFSUC9Ub34bAg==", "signatures": [{"sig": "MEUCIBuE6vCOYmFS2W9jzK/4isSD8VbMXdvlLGCwC1PGJRs6AiEAmXTaluTgvSQrIo3dfP2vzHM4Vs+BMDXxyeFk6DlTbHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20098}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.14", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.14_1733504081479_0.3056021245367553", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "d0148fed0c1aab5cf13fc0d2167ee24b6fac4e22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-WUb1wmgiSBIfrzdvu0mgDNKBu7P69Ssi1hBSjz/1VIAOUGLex+5MRDrEIZ9940YngEXPTo2MBuewwPfozbnEuw==", "signatures": [{"sig": "MEUCIFnBNz0ZbLLaRC2BoMHq6XaOzHj4mS79/ZGmzjZw8V+8AiEAgVeOiAQHULcKgrBB2nkADSX5+rFoWCV/DC95cySIQoc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20098}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.15", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.15_1736529911554_0.6766572611409607", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "b24ce2c05611b344dbbda3d4fb6d45e1e3a0aff9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-5LtsjrkuqgwMxUaXB2VZ2yVS7IpqRnV0Xhtb7Ss8VxWOOOKw9AqpkZRlDki0LjnXgSkYQd9YGUtK8HNpqHBRGQ==", "signatures": [{"sig": "MEUCIAoFe/ht+OCg9t/zEdHt2Uh/uQmZeDwOXBZ0NCsw25ILAiEAsXnYXEuPnw3h3//5Giy+Hw2ayRAA6Pney1FynF3OmiY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20098}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.16", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.16_1739534385287_0.14295791041214145", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "28addded54afed70ad04b297b9d253e8dd56e6cf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-RN13OiqEKh4z1ulzv6arhkPKy6tOc1gm39yI2Qvlb9SYycTf7IK3TlddALJbMS5aSoJaDZczPBYxkJy8vLX8PA==", "signatures": [{"sig": "MEYCIQCCqEEIHU6ZpmOgqL9ezWjm/gASl8BfEJr6qWTFh4EbdwIhAJPTgG5nLCK27rmqxPAcsL97sgO+z1NHqweuTzJAVSa1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20098}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-annotate-as-pure": "^8.0.0-alpha.17", "@babel/helper-create-class-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-alpha.17_1741717539694_0.1806746143179625", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-private-property-in-object", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "4dbbef283b5b2f01a21e81e299f76e35f900fb11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-5J+IhqTi1XPa0DXF83jYOaARrX+41gOewWbkPyjMNRDqgOCqdffGh8L3f/Ek5utaEBZExjSAzcyjmV9SSAWObQ==", "signatures": [{"sig": "MEQCIACFhAD9tKHP7TxktkaQgkyyavN5przqI5X7/fcFjnxjAiBvATOIa2fEdsa51d8g2MxZk0KEnMqSGwXaDbv4WaTQfw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 19812}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_7.27.1_1746025774278_0.7655690677795206", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "dist": {"shasum": "da5e52541af3dc81352cd17adb80364d5c083d19", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-fxtyLUW8DeOeZZR9BlTtQYr+Ums3GWHxRc5M8XCIV7F30k3omgvkMhZiiCMVJZ0fU9mPPbsUaPRw8tg4nImQiw==", "signatures": [{"sig": "MEUCIA9L7ltySuAZ8RhaAtnO7sqQXcWWDkTbCYpYLX8nuYRrAiEAh+QT++V2f+NAEz7eyOBGjVgaHtnvtdtmrUWtNFObDrs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 20070}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-annotate-as-pure": "^8.0.0-beta.0", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-beta.0_1748620311228_0.31413583889631", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-private-property-in-object", "version": "8.0.0-beta.1", "description": "This plugin transforms checks for a private property in an object", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^8.0.0-beta.1", "@babel/helper-create-class-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-private-property-in-object@8.0.0-beta.1", "dist": {"shasum": "69f10f6871e75ffad7a3270889d3ac56a6229599", "integrity": "sha512-a16HFLviZm+33MEJwQbiVcA2Ob0JpKCZ+xRthwwcqLxmbmczbNsCxIGs5qJ9AOspuYoceG0ujPnq6r68SjXFVw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 20070, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDphTIx1VNlZKWMs/FYxex+xH9dM/uh+K7yMnK5274QhAIhAMjAmzzfqY5hNnmp51J8IqyBE3xkpdToJfJ9ijkqfh4J"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-private-property-in-object_8.0.0-beta.1_1751447093168_0.09457132454109796"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:50.527Z", "modified": "2025-07-02T09:04:53.611Z", "7.22.0": "2023-05-26T13:45:50.751Z", "7.22.3": "2023-05-27T10:11:00.806Z", "7.22.5": "2023-06-08T18:21:49.451Z", "8.0.0-alpha.0": "2023-07-20T14:00:32.053Z", "8.0.0-alpha.1": "2023-07-24T17:53:05.329Z", "8.0.0-alpha.2": "2023-08-09T15:15:28.018Z", "7.22.11": "2023-08-24T13:08:47.581Z", "8.0.0-alpha.3": "2023-09-26T14:57:40.003Z", "8.0.0-alpha.4": "2023-10-12T02:06:53.746Z", "7.23.3": "2023-11-09T07:03:55.707Z", "7.23.4": "2023-11-20T14:22:10.038Z", "8.0.0-alpha.5": "2023-12-11T15:19:48.251Z", "8.0.0-alpha.6": "2024-01-26T16:14:52.542Z", "8.0.0-alpha.7": "2024-02-28T14:05:50.807Z", "7.24.1": "2024-03-19T09:49:35.457Z", "8.0.0-alpha.8": "2024-04-04T13:20:23.528Z", "7.24.5": "2024-04-29T18:34:28.349Z", "7.24.6": "2024-05-24T12:25:13.970Z", "8.0.0-alpha.9": "2024-06-03T14:05:51.787Z", "8.0.0-alpha.10": "2024-06-04T11:20:47.553Z", "7.24.7": "2024-06-05T13:16:00.316Z", "8.0.0-alpha.11": "2024-06-07T09:16:10.888Z", "8.0.0-alpha.12": "2024-07-26T17:34:06.351Z", "7.25.7": "2024-10-02T15:15:36.939Z", "7.25.8": "2024-10-10T13:25:13.017Z", "7.25.9": "2024-10-22T15:21:52.085Z", "8.0.0-alpha.13": "2024-10-25T13:54:52.807Z", "8.0.0-alpha.14": "2024-12-06T16:54:41.655Z", "8.0.0-alpha.15": "2025-01-10T17:25:11.784Z", "8.0.0-alpha.16": "2025-02-14T11:59:45.514Z", "8.0.0-alpha.17": "2025-03-11T18:25:39.852Z", "7.27.1": "2025-04-30T15:09:34.460Z", "8.0.0-beta.0": "2025-05-30T15:51:51.420Z", "8.0.0-beta.1": "2025-07-02T09:04:53.345Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-private-property-in-object", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-private-property-in-object"}, "description": "This plugin transforms checks for a private property in an object", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}