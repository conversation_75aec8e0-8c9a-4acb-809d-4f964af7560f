{"_id": "anymatch", "_rev": "56-63125188432123337601c051cb40ab72", "name": "anymatch", "dist-tags": {"latest": "3.1.3"}, "versions": {"0.1.0": {"name": "anymatch", "version": "0.1.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "anymatch@0.1.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "d34e745e4f79f7c07e65a5e6f46de0e4489bba05", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.1.0.tgz", "integrity": "sha512-8ioep5XHFKiNPPkkMGFi0k/oDtYKtoK2J8+Lo0ZdJpzkqwIU90qYGObR6Q0hJrCjJWke9yilfga6w+PvWg4RQw==", "signatures": [{"sig": "MEUCIQCBVXNIsmKZDHQ7bQZhMJ43cl+eM8Nd1pEkDms6HDW4GQIgNdZXjdI1O/ADt6o7yL8SOicOOPVNWpljL+Lk9Qvly3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "scripts": {"prepublish": "rm -rf lib && coffee --bare --output lib/ src/"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "**************:es128/anymatch.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "dependencies": {"minimatch": "~0.2.12"}}, "0.1.1": {"name": "anymatch", "version": "0.1.1", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "anymatch@0.1.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "cdb873bd9083a424c2c4e9b6835cc40f35c2910b", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.1.1.tgz", "integrity": "sha512-I8za6r2or0IUj7LEAolVI/Iz4k36rTh6cuiMhRO08MP3KSIj8LhSpX05mY8J7p1zFBaQ5ivVVGZpVrIbVPQxOw==", "signatures": [{"sig": "MEUCIQDsklf6oqzVLEk0Bp3qtALEHVbonF6QJlw+uwr0dWlCeQIgd6H6chL0GdDN38/uG2XPAGWcUMW9tkWG+8Yhn2Ketfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "scripts": {"prepublish": "rm -rf lib && coffee --bare --output lib/ src/"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "**************:es128/anymatch.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "dependencies": {"minimatch": "~0.2.12"}}, "0.2.0": {"name": "anymatch", "version": "0.2.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "anymatch@0.2.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "e919a97cd43373e6e645bbb7b644e2daebeba405", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.2.0.tgz", "integrity": "sha512-+Ga/bgOG8wVyuzBiOoRX9NXMMmNa5uHozMnJgmjoTWAj/8cPQ4Ni+2izK5O1tHATpuagFi38xYkfrQGC4vUgRw==", "signatures": [{"sig": "MEUCIAuxxOEp7tx69nZ4cmOS+k3t+ffxiGQ0JLHNlXGfRGg/AiEA+3APgJtDOFIsEukUcJ+51QSwCf9SMgE910OonEBP4O8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "scripts": {"test": "rm -rf lib && coffee --bare --output lib/ src/ && istanbul test _mocha -- test/test.* --compilers coffee:coffee-script/register", "prepublish": "rm -rf lib && coffee --bare --output lib/ src/"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "**************:es128/anymatch.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "dependencies": {"minimatch": "~0.2.12"}, "devDependencies": {"mocha": "~1.17.1", "istanbul": "~0.2.4", "coffee-script": "~1.7.1"}}, "1.0.0": {"name": "anymatch", "version": "1.0.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.0.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "0aed64d30bc25973afdb3155eb87ae6881e21b1c", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.0.0.tgz", "integrity": "sha512-lLcjwlmCnoGH3xbMVvFWTZiftwrCE6gRA8I0OhQNPbdHJYs92F2RjVwy5Hc+gYtCpDhmMA6UUCe1+6OUjM3beg==", "signatures": [{"sig": "MEQCIE7xq1AoxygQyUtrBFNu9X7jisM00yGLIiLhc1Zs9aGtAiBl1/JIiV9Smm6w1kWQPfaOFGg3rBieRZMCBNJGQ8prdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "0aed64d30bc25973afdb3155eb87ae6881e21b1c", "gitHead": "1ee0628e9572d80f9a1db604e65734fa86b51bdb", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/es128/anymatch", "type": "git"}, "_npmVersion": "1.4.28", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "dependencies": {"minimatch": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}}, "1.1.0": {"name": "anymatch", "version": "1.1.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.1.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "ebc63275cee368a96b300f31623bf9f228d428e3", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.1.0.tgz", "integrity": "sha512-daz8S6od3trUKIvcpAZsuBz+arvdkTFijfnAuT9QRUff77lkdz7kU0qyx+5bfdFbU6zVlMN/HbdDKsKZLxQT6w==", "signatures": [{"sig": "MEUCICAMbGb4sdLwlNxBWONtgx3jKoYEdCh67e/4SVAyfvBdAiEA4Waz7syuopnBd25PnE2QaJLYicMDC4Z/CKwG2vZyA/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "ebc63275cee368a96b300f31623bf9f228d428e3", "gitHead": "11878efb4c829892e38c6b042c54e4da96555740", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/es128/anymatch", "type": "git"}, "_npmVersion": "1.4.28", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "dependencies": {"minimatch": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}}, "1.2.0": {"name": "anymatch", "version": "1.2.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.2.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "f4df38e0f4943bd64cd6fc0ae32e4e191f8db819", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.2.0.tgz", "integrity": "sha512-hQn2PR6ZmJgV0+2vaP0ntDK9FWPBsVyL5nYs3ajt5rubkdjYbR6Y8oCxWPVQz/qd1XEyMf0S/QxFytfej4mGyA==", "signatures": [{"sig": "MEUCIQC/u9VMGmJGz6CxKvFklAZLw1BHMSXaGhv+MGLA8/nC7gIgFU4XyLZKnkVdegUgjuGNx/+zHFmVWUMr0NDz6xvAbOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "f4df38e0f4943bd64cd6fc0ae32e4e191f8db819", "gitHead": "b7b3ec24109404cee5b3b08ccd6964f6ae75522b", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/es128/anymatch", "type": "git"}, "_npmVersion": "2.7.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}}, "1.2.1": {"name": "anymatch", "version": "1.2.1", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.2.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "a7d77e8b62bc27cb5309d5ed905915b8da3f210f", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.2.1.tgz", "integrity": "sha512-bejASqqXzaLoWTdA3XJFn1hsDjD/aTroO2xlGOSNQe9OmYhjJ8vGXvHFDMwK17b767ff4X+4H4KDVABxYArmuA==", "signatures": [{"sig": "MEYCIQDzEXPP/fWAvyqoSHb3U8dLByHP65mP+mlfiWlieEkkwQIhAMnQzku0bCErj7mpQRhvYNhZd2ME7epkuE8km1iWnplD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "a7d77e8b62bc27cb5309d5ed905915b8da3f210f", "gitHead": "4cd0c99e8f3aacd9895b89ae5cf4f2aeb1cf1823", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/es128/anymatch", "type": "git"}, "_npmVersion": "2.7.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}}, "1.3.0": {"name": "anymatch", "version": "1.3.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.3.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "a3e52fa39168c825ff57b0248126ce5a8ff95507", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.0.tgz", "integrity": "sha512-GbRpOH/EMz/3Zq70whK2Q2tkbxbaM5IAU+EZL4zxnEqGtzJWFCJ3leKc6P/w3UmDFIB/GkwfeZJ7ChL7bZMXJw==", "signatures": [{"sig": "MEYCIQCBhixmMqLwvANLxy7fjFSCa/u04crVfGZZgHad8XFQvAIhAMNFYb5xvKZjPPh9K6yqqXEokSRzlQe+BJBhWHee8G69", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a3e52fa39168c825ff57b0248126ce5a8ff95507", "gitHead": "253d2ad42f644ed18557f561312a7f8426daca84", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/es128/anymatch", "type": "git"}, "_npmVersion": "2.7.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.5"}, "devDependencies": {"mocha": "^2.2.4", "istanbul": "^0.3.13", "coveralls": "^2.11.2"}}, "1.3.2": {"name": "anymatch", "version": "1.3.2", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@1.3.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/es128/anymatch", "bugs": {"url": "https://github.com/es128/anymatch/issues"}, "dist": {"shasum": "553dcb8f91e3c889845dfdba34c77721b90b9d7a", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "signatures": [{"sig": "MEQCIG5xLI1LLNylaorA2IWCEIM1HWF3W9jKefbRUG0RMIf9AiBde7dPAETOgWw7fknY5FYD7BJjEC5Qm7wp6Zi07OJRew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "gitHead": "a16f5bd07f1e36c4eef08c1291c6c119d1663639", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/es128/anymatch.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"micromatch": "^2.1.5", "normalize-path": "^2.0.0"}, "devDependencies": {"mocha": "^2.2.4", "istanbul": "^0.3.13", "coveralls": "^2.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch-1.3.2.tgz_1501178625433_0.08029883285053074", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "anymatch", "version": "2.0.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@2.0.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "signatures": [{"sig": "MEYCIQCKIzhRa0to9Wg0MM0aJQvXBnkMfe29dJDkpxLe0oUcyAIhAP4KyblHVv489+g5j3ja+JTSYhRldthEsrBlP3iXceHA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "gitHead": "c906c0234c5fb676426da90e429c909566106028", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "devDependencies": {"mocha": "^3.0.0", "istanbul": "^0.4.5", "coveralls": "^2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch-2.0.0.tgz_1513905286165_0.27702793199568987", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "anymatch", "version": "3.0.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.0.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "53c81b67d20bfe144b3c5977163c6ee53141dda2", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-Y7idCXpN0RtVcBs9OTDPiMq4g4Klqr6ycj9ht4y+jIf5AoRfX5W6KRzp+M1rfWj9C2/r7G5ViiBWtMqjpv1KBA==", "signatures": [{"sig": "MEQCIEJI/zG4AJq024nmwJiC6bo7jk9nbuQtCQk5Wh39l4M/AiB7v976DkI0j4huLsh2kxCQ8aM6oaKzLjwLaqIeQUN3Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrmRyCRA9TVsSAnZWagAA1ccP/1KBgENFXZGXC4d8SqMQ\ntxibc6E1Vfi5iEu+/cZQdUltMT3gsGDbEZw1aTgXzrlTbp6sqDjzv4Uf1Hcj\nQYPDvt5oJvPNoSwgEgHVLtcoc+VzkWib/A2WZ5XxT/rNJkiOtd8jdcYyRZRp\nRxxuNxigLJLLbxZe9Ru60Gil+hwQjgeUlBeGYc/s5lTfTIKbb/JeHGbvSavR\nWtOD02rl64cUXFfFFIb5wZz9nWINA6qHZElcvOMOB9HPyriFJfz1VJWSy1vt\nWk3DL9bioJCkWm/FVPMPvNNInhHGTZgt4EWZY3nWrtbp7fXtILeMX0DEwly+\nrsE0R+aVxpir49WFFULthW++W4H4R5HuM9u1YY8qUUQYmI8n8yGokNxg+af2\nrku07uXC6maXqhhZpwyWBaSUyA3tiFDaUDmTfJOvO3kcH0ZKOUhqdWQnpbjH\n03Cn3J6MOKhjx2XFtkb4+iaji0/d9U34643ueg2Hrkw8YhJ5IMKzE1kf8M+9\nuE8wckTAPXJu/iIG6QupJCllvcnzW/rEQzbwnKk1fFKU4wS6e890IEnnOGPI\ncebBU4ejvNuVIJde++gTZeIdkwOcOWFIaA/Yd2enhuGTPFWDzd09I5iMpB5A\nxfcO7Nodd8Iu9HuN2iTA+7tbeNsa4K6qkug+jRDXElLD/tcc2F9It4XrPTsP\nmMLo\r\n=3aNc\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "d474c820e61a85efcfc90e5b039d8ea14a4b4fae", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "11.13.0", "dependencies": {"picomatch": "^2.0.3", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.0.0_1554932849458_0.45494875225113107", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "anymatch", "version": "3.0.1", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.0.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "a47b8a9e3c3f7f17420276e05ef39746ac1777df", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-WQdpV5fo7XSY76HPN4pqdUl13Q282JsV0gQ8OnIxQsqDEHDZJCBkQ89fL1Mb3tNiPzGQnxMHM5G2iG3k9O6yng==", "signatures": [{"sig": "MEYCIQDgJdIlA+h+GlDyCL2L2Dp7s4GQD8iB4OPdA73Pv5q+SgIhAJ1x7rdeYqjrV7iuv35e8LyHv4oMo2fz/VS6jyAo7q9Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctwZNCRA9TVsSAnZWagAA0oQP/3+N7O7LxqILEuM6lj3p\nIhqfp2Ll1tZUNJ8pWjn7+9T+sjehym7jiyAjVRtu7y0IqUWCFcG3OGWzHc34\nZNZte5V7EOq7qSLtkfLs1YMCFiL/eIF5vMkgv+GXPbwQT4pXX5IWlM9pbqhx\nxQXx+0jKRQhGM/blvNYDz7ZLWqbzzzDY+zWu/aHG17CWP5rQLRABFHU6jWBA\n0tZdv1mpWRFHwfEeNeeTpoiWWV3g7CLvH2ovu0jFyI4WVpzc5mSkI6SDG7EB\n4ASL/cn0611k3a6OzLn76uOvgXZb8owZoB1UiruTfT1zT9J8pJ0XM+0fZoB/\n5z0oIspPkS5p1f8RxU++/5u89DGDlbqH+H3hD/JanId8Qq7PIeTN+ohLLDHu\nHeCtDKKMKzEhHme0Bn60qfK2TBIALADklwhbHF91WOsD/rnAtsoZYYCqA4ET\nrhAiXgepgT+a21R8B0/G8k7OMMl6YM/xWCzOwYWOFp9z/olBe+zOQ4QOKafb\nUt4afg7g2H2boTLaYor8BrsyC//lWA5UibzzzE9JA7BgQSePct1UIobLceXs\n3RN/fbctvm8kjpdNBx7pm2QDuTcWWNSv3BaVRTaEW8AwdrYs84Aj9ct9oGVD\n5IV39zyW/1wIy1tmp+U5kjwGeqoFlNEi7Mby1h06FYz2PM+q442eT6mzSj+t\nzeiP\r\n=QpjV\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "98dabd5d1169b05a0c5c69e71eecd92923f7684a", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.0.1_1555498572807_0.39428219907930706", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "anymatch", "version": "3.0.2", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.0.2", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "ddb3a8495d44875423af7b919aace11e91732a41", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-rUe9SxpRQlVg4EM8It7JMNWWYHAirTPpbTuvaSKybb5IejNgWB3PGBBX9rrPKDx2pM/p3Wh+7+ASaWRyyAbxmQ==", "signatures": [{"sig": "MEQCICyxfT+acPzQVvRpPEq5aAuFrG5uoQ8h+qkzhzIX7IQtAiA9fzTuLAcypNwe32p1sjNDNX0+84/DivF63tst0icX5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2gZJCRA9TVsSAnZWagAAbk8P/3PVMM9Fo0vdbx1PmXe6\n80X34xHFBAyIztkgABwgkT8Jc6BXsvtjftbCg+QCO04QwyTusab96F3v52+8\n2FTrRTIVh1anbOC81SpQAXYekHnf89jtEEeS+214Ey30csg8yHEBXa/YhwCt\nGcgDITv2qKSAeuaq0BgRoyyUm/N1dB7+x8fAnHEJW2ONGMaggdEhJ2Z9COws\nNo0qx3rnl5sJ65626IrV8baQHqHuQkgl0T7Evv9HXzdhUvPd86p0Mk9HDy6I\nd/lhXYbZv4bhPMQuPMzPq4MX75lG5eNI4ljfHh0QUxPiWjl8q4R6P7iYeJsb\nNCfylBRtImCvMe+foqYmKNnd0LZvMbVWw2U3LIHXN8WMmJI4z2QdVNeEYXD4\nkbKKecO1w8XEWW1CwnB61OepT00dssBiYo2QAo9dbac0azZeNaiq5lj8ey1I\nSHMwTnzQ/vuU/+L1XUBelNLd141E2dOaudeCw8WfMEsr9+jKbkAfig/iFSCe\nou4nRD5Adp0aXvxN8s7NT09D/gToouUsY1p6iH2foCLqEQZ7yMi8jsmurKQ2\nhjMJf0P4DdZ7ZjdACbHvCjzeUlTMBE6L3XcITVhtA6AbWUR1Hp5QRxHoMeoM\nAnx9jzlLDEKPzD0aSl0IXEtWEDsNMU93zb+erjSANSomWglwwEdxR6dFR5yH\nTCvV\r\n=iiZn\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "4167e8ad3f0f937dfb8842a4822e5b212c7c0bf0", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.0.2_1557792328596_0.5982534632231673", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "anymatch", "version": "3.0.3", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "http://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.0.3", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "2fb624fe0e84bccab00afee3d0006ed310f22f09", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-c6IvoeBECQlMVuYUjSwimnhmztImpErfxJzWZhIQinIvQWoGOnB0dLIgifbPHQt5heS6mNlaZG16f06H3C8t1g==", "signatures": [{"sig": "MEYCIQC363knsI88/YnBYorI8n7BGUS0lY7hqKw9j1BcSVOEPgIhAPyxg58g9RIcy5wBci07y52N/Ro+Yh0+MYb1RkrxyRbo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIT6LCRA9TVsSAnZWagAAGRoQAKHilWT5oawHndGya7tk\nsRSeHwdT6s3xC9gNNde1m2oiZCnWmDMyjrFD8ptjWQuiR6Zvrr6xdn9p6Oyt\nSKodAN9nX0fIfyI+fWx61aqnm6qVq1OF9efyDLZumCtIXVtSzSz+n1K55h8y\nOWeupQitP3YJQa84uCm4HjWzN4TtRZM5E/JBaoEiOin3PbYNXpsGb+vqJxjV\nOLh1wD2dtb5P2Zqs7MEIKNT8GitevxsL52nM9jQYiLZ9+TPkNo6g8EAvN9U2\n8nXhiLsr233Pix6lxQlDcm1oMx6xow1SYm48FJCK3Z2eyZhlC/Iznh9vc1Wu\naXT5Jj5BJc3aDjBFYgcn0opx7xec2zjgOFxvemvvL2Sg40EIDyOWkcqZM0un\nJluuzfhSqdY6QpzABUBqzU8A160AcpSzjZHMwaImMuFnKH0DFqo8MWJRkMR6\nQj12Eu6+5m5oNPhA6WWFVBVokGVj9dTrcIaGZ5IQfmlMwqIArmN+D1s0VGmK\nyAHKWTtbcS85OqtrsHglrWsHdCSfdzhrQBnsxDOPnCoujyOZ0yUhx36Y/QZJ\nrKXTM4risMNwMK65ABFK7Vm++EM0CwbCmMlaPBp94CcMQW8rowc0EamgmryW\nq+54aygdwNF/2EGX210pHEmTT7i0CvJJXfQyg9uBOHNuvEVvbL+O4qtJVuUW\nmX+K\r\n=qBBz\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "657e80038f5e174358fc4a41159b1dd32a6a60e7", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.0.3_1562459787038_0.5029030204438858", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "anymatch", "version": "3.1.0", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.1.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "e609350e50a9313b472789b2f14ef35808ee14d6", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-Ozz7l4ixzI7Oxj2+cw+p0tVUt27BpaJ+1+q1TCeANWxHpvyn2+Un+YamBdfKu0uh8xLodGhoa1v7595NhKDAuA==", "signatures": [{"sig": "MEUCIQCldXDynb08HBoOW6z/XyxSofb31JKjfSTVHNHra85VjQIgRcwlE1rPtBY6SL1mZZmVCzl7iv/ADcoNdHnFidejSrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaH8dCRA9TVsSAnZWagAAYJ4P/Rde9PG38zsaas4YBbIs\nIceSPgLOSb0Cvnn+mbZzLT9nnykESBBaWjDLknjWnij0E5M9a1LEKyxl5rAf\nPnDT7sFEac95q7OBbLbZxGttT2FhAv+dKULVV2F8n/YygOBu4IXunZlWOKx4\nEXs/CCROrfhgEbpSvCyIb6Rx76VOZbTJ681OGqDdnM7ONnKtlwzDCxRDT5mQ\nn0VzZ6zI0hbp9WzJ2k8lJmGiE7SN3EXV8ZdlO/ofoqPwoKdQLBZ1xNZnIGvQ\ntZeBbWzegLqujDO9bvoXIECIs/HYYqc702CfUJ7kFia1f+WYXuNqZK/Tw2q9\niSBWuu6657WPJnhxytScXlGuUVcrgJ6V7gIb/mJ3SBZLw5iOwissd1vt0/X5\nxnR7b6XzL5beNUx1N5XebiJ4TjpXBSAoIgXlx3+zApWOUF8sl8ViLi4vrnCL\nkatVNebCwHxFrVpDwjg+/yTMBWzdpncJ+xvCpHlBTDiihPqzR3a8LmWvnUft\n9K78EO94OlnUpmR9xei5oHlo6WHW9wb049wWC3Fqmfm5rbQd3GvqotitxpkN\nnj4jbuRBsOPSunmXDyUDh5a19odYNZ+HRo9OT3PvPswHhqy4uOFlXKYAIDPe\nrATPr7dqm00a6T75vV7VYrR+1MsRm+HLc0AezpWPJIRtWMs5CQ5xSOeYUCXX\njZuy\r\n=WO7A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "abfaed634543129e9275e9d56b0938c8997e5b17", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.1.0_1567129371986_0.4832594239025567", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "anymatch", "version": "3.1.1", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.1.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "c55ecf02185e2469259399310c173ce31233b142", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==", "signatures": [{"sig": "MEUCIBCSnnnF1vMIrCJ3541UkIb4rz6m0h9MG226A79N4pOkAiEAlwF73CmCKFw6KGre3j7WU3oAvS0B4ahzrBqCgXmxRsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkrtJCRA9TVsSAnZWagAAAswQAKAWXsp9Lm/3tcBXanuz\nodcqg9m39/XiG6yc1/WD/7bF4YLcWMO6Ra5Sq7ouGtbsvdBAifOX4rsMBfy0\n69UJrWKb8hC/x6w/uMK38rC4nUB3nSmcjTcEQ2+7EITi9oHusQNd6trQ5Y34\nH0K06RcjsrQPwnir2MfeGhsydXqQWgY3NR67DZdosK6hN6jxBq05BMwlQm0c\nwaRJoTA2wFPCi0vy8nqcGoK3qU1/bGId6kpeXOW8TBH5gNJktS0isCl0J9iW\nt9q5Ebg5sj7P8BM+w78684bJfITFKyGk5KyFa8MjV6U5UsUHSNbU4OI6TLMF\nbUqvDd19mt3r+yfWECgmlmZDuwT5q5hDc7Kv96q+uvApXDYIHdZVdihQmQt7\ngPmIdvg8BEM6DoU2Hrft/ijqv74ZQPFZXRFKgtbKCK3IYPJZyqhEm0vYkYdn\nTRpBp2SqYDyXPg/hrC61cyKqB3aEoqxcIw5sITX9voB+FQUXBHOZafOtfx1/\nIB1bveWkdT4AcMaerbJqcjyrSMOoGlX9SiFxjjwsV8euF/GUwH3FL2zS2raJ\nXEY0ftOY07mPSHJLhCKK8LYWsy9V9GQ0D1318EZZWHdZfp+5b/o7Jp6opJaH\noFfksudPT3fXSuZBbjJHxF0WmAzHVnw/W+LJg8RMISGg9utObWZlJpztEDtZ\n3kim\r\n=DCgJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "e0ec3c077d6fd64753b0c9f149614f78c9d54285", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.1.1_1569897288417_0.7130945968950306", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "anymatch", "version": "3.1.2", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.1.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "c0557c096af32f106198f4f4e2a383537e378716", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==", "signatures": [{"sig": "MEYCIQDXiWE4Op4ypi8XwggX31wsPQRX1J6LgzZFVf2X9AXnngIhAMNn4Zl0JUF3kjvxu9vuk/e8s5TX8pZ/0Zh+splHXl58", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbN2hCRA9TVsSAnZWagAAGJYQAJyPOwO3T8jXiUuhUyu5\nQHBRwDjwbwdqi76AZghu7TleqgW4UMEkvfDwx85la5o80LJWwySCO84VG6fG\nNbKN87Z9XZM1yqs/Wf0tFS+SGithE3eCZQIkzrrSmzdKkyTr9ffjl7HR9fcW\nCphEJwN3ffoxSDcqtid/g2Mxqy+i57x8Z/emmx4DJuSFAabL4049BMZzfZdw\nS/ST4mK5EaMrGwyJRP6DpXGdEOgdrID8FwbO2k41HX3x85LAtzBcO0qhdl7L\novL6T/a91OoyLAk+Ugd2vi3wuky5gmclw9QtNVDkAgFmJUhYhtWG1qTcwcK8\nm8zmr9d0coUo/zqXtpZ6/kf6EJC6priCKFvoIlbKbciord4qZWKL077C2TEa\nxDvy052nqUPhq2jiSa0JKXLEhDwKi2wUy9z8c+Hnoa5dfMWbw2N4B6aBqOUa\ncccGVblc/d0Pp7lbqUEdypzCuHu5SJflkELn4MHmGglXZamjVJdzxm54jlzl\nG8wF+g/KGMceL/W/I2ZrmjIKvKDwzkPVYtFnaVQw3+OUGVAUORfuNSLP+KIe\nmFu6CXjnuGe8wJCKxR4JJp10YRX8hO5YnkmD23CxL24IeOfL4V/5AUU4WdyU\nQ3n+ohPtHnxJ/lSEugbxfQfRvABWZ2qjsHKGdKFcuszhkWUnXwSVWdAnB49n\nfT7R\r\n=GvXf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "02e8eed02e67b2fc33059d15c25b9a458c9eec41", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "15.12.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.1.2_1617747361334_0.10702249082012649", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "anymatch", "version": "3.1.3", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "author": {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "_id": "anymatch@3.1.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/anymatch", "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "dist": {"shasum": "790c58b19ba1720a84205b57c618d5ad8524973e", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "signatures": [{"sig": "MEUCIQCb/AbEWxEB0qqOSnPkeWgpv5RGNzN2jbYAbN6bT4n6BAIgJDTSn9WVoppKMohO7pEEOa1eCduipQ3FOe8OsWJPa1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje8GxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoixw/7BaWCNmw3H7ScsUAv7j5Btl3Wg6s0U/5fPxHwBvEs6rYiZi3G\r\nF2WhPTWMB4aioZ8ZX3N6gVrANlHDEnGPs7UhNCGvc/Pej8iwLtpOEgoi/vlQ\r\nxucB2FPnCIFb2avPfaM4xJa2oMpBaV2zBnaIQ2yzV5mWceZs/587gccxqW+t\r\ntWOyU3ftD8TXz9OYfEiVFhbByItor8e7s6jG6KFrOqALimF3sleYV2Ze1zVF\r\nrwhN5jipf4p4ZQQ6Db+m9bP5VXyWLXQOBqvA8fEDMz6ZIpzFusDgCLSP77QS\r\nuxw+bTgSMFt199VObbiRXSEGT4R4rCivXd0FrQWfcuE9APhGvq6xQYDv+XsV\r\nH1Zd4t+SM5xxvEjWGtg+EpEmDjsaTYDMHSUvowOUlgc2x3HuhluTDwKqnbGF\r\nWF14U3k6U7pBfQFhFHokp1c+xfgiID79+7muPZ+OS9v/dzkaiCTAD3sEXXLa\r\nBHiQ4Ek2IKSxhhIweWQzaRkz7Fk4MhqF0pXoJVa+zv4De/X1+xF5udl0QVUL\r\nSO9vO7SLcJtpz59ZvubiB9TErqmqkvUmyfIRQyu8LNcKDrfoSpQybfIFOZLY\r\nDdeco7mDlsytKJQWiPitXmTeoH+uEtjLKR1or14GqMnDyE8iiHl4bBLUQNDu\r\ngWy4MpvD3/FSZdpIgzYmf00lis6zdROYO3A=\r\n=AdxG\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "cbd278e43710eaf325d2061fa11aefd127c509be", "scripts": {"test": "nyc mocha", "mocha": "mocha"}, "_npmUser": {"name": "phated", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/anymatch_3.1.3_1669054897424_0.28372944178346304", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-11-25T16:08:38.502Z", "modified": "2024-09-18T05:27:37.897Z", "0.1.0": "2013-11-25T16:08:40.170Z", "0.1.1": "2013-11-26T21:11:50.083Z", "0.2.0": "2014-02-19T16:21:21.908Z", "1.0.0": "2014-10-25T15:45:33.199Z", "1.1.0": "2014-12-23T17:01:53.196Z", "1.2.0": "2015-03-25T20:28:14.117Z", "1.2.1": "2015-03-27T04:34:02.916Z", "1.3.0": "2015-04-22T22:05:40.612Z", "1.3.2": "2017-07-27T18:03:46.329Z", "2.0.0": "2017-12-22T01:14:46.258Z", "3.0.0": "2019-04-10T21:47:29.674Z", "3.0.1": "2019-04-17T10:56:13.129Z", "3.0.2": "2019-05-14T00:05:28.723Z", "3.0.3": "2019-07-07T00:36:27.223Z", "3.1.0": "2019-08-30T01:42:52.174Z", "3.1.1": "2019-10-01T02:34:48.563Z", "3.1.2": "2021-04-06T22:16:01.465Z", "3.1.3": "2022-11-21T18:21:37.586Z"}, "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "author": {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, "license": "ISC", "homepage": "https://github.com/micromatch/anymatch", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "repository": {"url": "git+https://github.com/micromatch/anymatch.git", "type": "git"}, "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "maintainers": [{"email": "<EMAIL>", "name": "es128"}, {"email": "<EMAIL>", "name": "phated"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "doowb"}], "readme": "anymatch [![Build Status](https://travis-ci.org/micromatch/anymatch.svg?branch=master)](https://travis-ci.org/micromatch/anymatch) [![Coverage Status](https://img.shields.io/coveralls/micromatch/anymatch.svg?branch=master)](https://coveralls.io/r/micromatch/anymatch?branch=master)\n======\nJavascript module to match a string against a regular expression, glob, string,\nor function that takes the string as an argument and returns a truthy or falsy\nvalue. The matcher can also be an array of any or all of these. Useful for\nallowing a very flexible user-defined config to define things like file paths.\n\n__Note: This module has Bash-parity, please be aware that Windows-style backslashes are not supported as separators. See https://github.com/micromatch/micromatch#backslashes for more information.__\n\n\nUsage\n-----\n```sh\nnpm install anymatch\n```\n\n#### anymatch(matchers, testString, [returnIndex], [options])\n* __matchers__: (_Array|String|RegExp|Function_)\nString to be directly matched, string with glob patterns, regular expression\ntest, function that takes the testString as an argument and returns a truthy\nvalue if it should be matched, or an array of any number and mix of these types.\n* __testString__: (_String|Array_) The string to test against the matchers. If\npassed as an array, the first element of the array will be used as the\n`testString` for non-function matchers, while the entire array will be applied\nas the arguments for function matchers.\n* __options__: (_Object_ [optional]_) Any of the [picomatch](https://github.com/micromatch/picomatch#options) options.\n    * __returnIndex__: (_Boolean [optional]_) If true, return the array index of\nthe first matcher that that testString matched, or -1 if no match, instead of a\nboolean result.\n\n```js\nconst anymatch = require('anymatch');\n\nconst matchers = [ 'path/to/file.js', 'path/anyjs/**/*.js', /foo.js$/, string => string.includes('bar') && string.length > 10 ] ;\n\nanymatch(matchers, 'path/to/file.js'); // true\nanymatch(matchers, 'path/anyjs/baz.js'); // true\nanymatch(matchers, 'path/to/foo.js'); // true\nanymatch(matchers, 'path/to/bar.js'); // true\nanymatch(matchers, 'bar.js'); // false\n\n// returnIndex = true\nanymatch(matchers, 'foo.js', {returnIndex: true}); // 2\nanymatch(matchers, 'path/anyjs/foo.js', {returnIndex: true}); // 1\n\n// any picomatc\n\n// using globs to match directories and their children\nanymatch('node_modules', 'node_modules'); // true\nanymatch('node_modules', 'node_modules/somelib/index.js'); // false\nanymatch('node_modules/**', 'node_modules/somelib/index.js'); // true\nanymatch('node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // false\nanymatch('**/node_modules/**', '/absolute/path/to/node_modules/somelib/index.js'); // true\n\nconst matcher = anymatch(matchers);\n['foo.js', 'bar.js'].filter(matcher);  // [ 'foo.js' ]\nanymatch master* ❯\n\n```\n\n#### anymatch(matchers)\nYou can also pass in only your matcher(s) to get a curried function that has\nalready been bound to the provided matching criteria. This can be used as an\n`Array#filter` callback.\n\n```js\nvar matcher = anymatch(matchers);\n\nmatcher('path/to/file.js'); // true\nmatcher('path/anyjs/baz.js', true); // 1\n\n['foo.js', 'bar.js'].filter(matcher); // ['foo.js']\n```\n\nChangelog\n----------\n[See release notes page on GitHub](https://github.com/micromatch/anymatch/releases)\n\n- **v3.0:** Removed `startIndex` and `endIndex` arguments. Node 8.x-only.\n- **v2.0:** [micromatch](https://github.com/jonschlinkert/micromatch) moves away from minimatch-parity and inline with Bash. This includes handling backslashes differently (see https://github.com/micromatch/micromatch#backslashes for more information).\n- **v1.2:** anymatch uses [micromatch](https://github.com/jonschlinkert/micromatch)\nfor glob pattern matching. Issues with glob pattern matching should be\nreported directly to the [micromatch issue tracker](https://github.com/jonschlinkert/micromatch/issues).\n\nLicense\n-------\n[ISC](https://raw.github.com/micromatch/anymatch/master/LICENSE)\n", "readmeFilename": "README.md", "users": {"detj": true, "zeke": true, "laomu": true, "tcrowe": true, "tapmodo": true, "ubenzer": true, "wenbing": true, "heyderpd": true, "xueboren": true, "antixrist": true, "xumakjosh": true, "mysticatea": true, "flumpus-dev": true, "mrmartineau": true, "tommytroylin": true, "shanewholloway": true, "alexandru.vasile": true}}