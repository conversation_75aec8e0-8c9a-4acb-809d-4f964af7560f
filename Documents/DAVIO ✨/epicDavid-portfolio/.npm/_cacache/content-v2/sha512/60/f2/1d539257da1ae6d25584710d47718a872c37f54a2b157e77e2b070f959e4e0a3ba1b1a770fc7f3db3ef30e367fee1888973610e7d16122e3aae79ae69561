{"_id": "lodash.get", "_rev": "22-f0f43daa0b76693e57bc108e8e954f4c", "name": "lodash.get", "dist-tags": {"latest": "4.4.2"}, "versions": {"3.7.0": {"name": "lodash.get", "version": "3.7.0", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@3.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3ce68ae2c91683b281cc5394128303cbf75e691f", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-3.7.0.tgz", "integrity": "sha512-7iD0aRHu/B8gcCDNx53lJi33R4TzpbOB3Mfk4XpIN7WFUt+W5rI+6CtHhpJ52B6zhhRvogtuNSDFZc3xgcbClQ==", "signatures": [{"sig": "MEUCIQC7ry33mEPedc/CCdMvdRn5kjQUg7G2oCiN3RzAjddmyQIgJKbKdkPA4r+PuNSEFgaU+z6ibZ9OR11zOxbXQqmfpkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "3ce68ae2c91683b281cc5394128303cbf75e691f", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.7.6", "description": "The modern build of lodash’s `_.get` as a module.", "_nodeVersion": "0.12.2", "dependencies": {"lodash._topath": "^3.0.0", "lodash._baseget": "^3.0.0"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.0.0": {"name": "lodash.get", "version": "4.0.0", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "faa56272dfdca33553066b5039592cc11b43941a", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.0.0.tgz", "integrity": "sha512-8/sTEr331twrZhi247AjhV52Ue6LhH4Y8NHWYUFjqSGt1K3IgX8v1IhIXhtoc84zkDiZfNh1CqhaO8QJUu0DQA==", "signatures": [{"sig": "MEYCIQC7EWafbfEy5z9ephuXlB6IA0ujITNOOrPm+TrwFMlSNAIhALWqI2xweZKBM+LSh/24EGy08Op4yX9sjbzWm6/fZSA4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "faa56272dfdca33553066b5039592cc11b43941a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.4.0", "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.0.1": {"name": "lodash.get", "version": "4.0.1", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "825f82281af7e36675b1067a0ddd5593ba626070", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.0.1.tgz", "integrity": "sha512-wsRng2n4dWa0YLkKiQrCoPKjDOQPBqNlzsI5m7Ib6ppwjM2wgEE8tAY2sMiJeVwz5iagLP7UQeH22EwJXkBrug==", "signatures": [{"sig": "MEUCIQD4BN/GxnpqPpZ/dv7doTPQJ6tBzC7ZflrS4+9CeODADAIgIMdm7qHdKMmN5mTkmBxKlHdntLOR910LcC6aOZ25POk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "825f82281af7e36675b1067a0ddd5593ba626070", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.4.0", "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.0.2": {"name": "lodash.get", "version": "4.0.2", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "bdac849862f7cd2bdcc2b84e2d6a35463b26e1f6", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.0.2.tgz", "integrity": "sha512-oxtbHslZKUy7MfnD4oV8kW+xVDr9X33Fr+EGyLn1gOkLfLtTq493LzNPUTe7dS6BDjGlPrRw4bASRKkIR6Dxqg==", "signatures": [{"sig": "MEYCIQDeOtP659trSF5BQlyztcKZd29GZuvTxXHCPI8ELyZUOAIhALWzfyQQB7dy3jzcRRH/Sb5kpYlhBSLGlMZdjsunj5UB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "bdac849862f7cd2bdcc2b84e2d6a35463b26e1f6", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.4.0", "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.0.2.tgz_1454484450046_0.03573412774130702", "host": "packages-9-west.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.1.0": {"name": "lodash.get", "version": "4.1.0", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "e110110c32d3225f0b5b66558d91d2d8d7a27e2b", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.1.0.tgz", "integrity": "sha512-NYxA965YBMT2dYeM7hMJ70ty3xAif66IyeFn5u0247Mj8JKtCj9fBYHSDEX1qjDL2UANtIgtxL0j8Z5m8/6g/Q==", "signatures": [{"sig": "MEYCIQCdssJ95lv70YRdYfy2VI7csuUuH854k9mFR/PzE3y8QAIhAPYA709lVm59stNDpR8VLNrXl9WatGyev4GoXIAOchZB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "e110110c32d3225f0b5b66558d91d2d8d7a27e2b", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.5.0", "dependencies": {"lodash._root": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.1.0.tgz_1454898396668_0.48346277163363993", "host": "packages-9-west.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.1.1": {"name": "lodash.get", "version": "4.1.1", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b3f4854ca3a30effd1b84d5bd093c7506dbce3e6", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.1.1.tgz", "integrity": "sha512-Qvv3GKVJErxNWbYrr2G7raENPgF2gUt8cnps4KjtMxs8VId9EnbO1j0S4xgEGKMsf1vlefKfk8KnOy4hzkV3bA==", "signatures": [{"sig": "MEQCIBLrDCuVq30cVoNSMcHSUOeIjZByJQJGsAr/g4k2PQebAiBom4gREKGu3Le0VQbS8A4I/ZZiiCq+VAEHKbXOLx2bnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "b3f4854ca3a30effd1b84d5bd093c7506dbce3e6", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.5.0", "dependencies": {"lodash.tostring": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.1.1.tgz_1455602317197_0.3085003807209432", "host": "packages-5-east.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.1.2": {"name": "lodash.get", "version": "4.1.2", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "e8ae368b95d5bbb6d24aee9651b32d8c552cdd79", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.1.2.tgz", "integrity": "sha512-U2r3ZIt8JgXh/B3l0f7qxDGuOBFsjZNQeLvlrvnQyrzidzkxbZqVPn1O8TaV/lCE4NMuWTFzWEGgyZV263Vq1A==", "signatures": [{"sig": "MEUCIQCdvubuPn5Mpez4QZW61K11diEfhh+hO4zJOwOa/oNaDgIgOlmXvDxiGf77xBSZ4pFRw02Q3C32yBq0CZENzWqUqB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "e8ae368b95d5bbb6d24aee9651b32d8c552cdd79", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.5.0", "dependencies": {"lodash.tostring": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.1.2.tgz_1455615287803_0.8842703467234969", "host": "packages-9-west.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.2.0": {"name": "lodash.get", "version": "4.2.0", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b3edefbc0f2629fb23c2b559cb4d6cc9f60c2a7f", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.2.0.tgz", "integrity": "sha512-65mP3HTknhNd9KbkiuA+mHq+gjc+0ioEqGp4nNFk5ZxSbuTCUR+pCRq5NJj/4vRjr19u//rVNcCS5vfOT2rpgA==", "signatures": [{"sig": "MEYCIQCy2iFJjUbuBIJn2WihxUkWXMiTULn/Uy1y4OC06LUj+AIhAJ1i3lyYNxBrDVydot6ons8JBgJjQVPJeYgDgK6ew072", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "b3edefbc0f2629fb23c2b559cb4d6cc9f60c2a7f", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.2", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.9.1", "dependencies": {"lodash._stringtopath": "~4.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.2.0.tgz_1459655354100_0.8729645137209445", "host": "packages-12-west.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.2.1": {"name": "lodash.get", "version": "4.2.1", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "6e9afc87b8a6c02160667a70f6c8ce1078aeb11b", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.2.1.tgz", "integrity": "sha512-CxrlkwK5QHf60TNDrNpD+3n4o/aIegK6kJWA6hxZSGHHSpttHSGe8ls7/ncsuvbzMCA/Oce3EY2ZnBo0jx0C0A==", "signatures": [{"sig": "MEUCIQD64gw/1XMvpai912T/vnNUtbz1lHXk1wrte8ZskSeUmAIgXBSsm7pvyx+cLe7WqdTGpNB73hCmtJaVvlOuGjzT9gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "6e9afc87b8a6c02160667a70f6c8ce1078aeb11b", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.3", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.5.0", "dependencies": {"lodash._stringtopath": "~4.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.2.1.tgz_1460393674288_0.44501535198651254", "host": "packages-16-east.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.3.0": {"name": "lodash.get", "version": "4.3.0", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "d3765ffbf770d4e3d2bb4f6f40995ceaa223e36f", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.3.0.tgz", "integrity": "sha512-XKw1xicv8gRbEVBiXsB9Q8xO4L52s0ICBbqvY5qYCH7852BC3J3mzQ91FPxo+qjVLNP1eO0tPXHN8j/uzSYh2A==", "signatures": [{"sig": "MEQCIC13JDqIDcXegPi9DnZXnxGK9RP6b3qYkfuPNC68X9ZEAiBaRcY5HL8IpqJFJYQdgvA8prH8JVO3zAmXxoU/Z9U3Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "d3765ffbf770d4e3d2bb4f6f40995ceaa223e36f", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "5.5.0", "dependencies": {"lodash._stringtopath": "~4.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.3.0.tgz_1463062233175_0.7616066564805806", "host": "packages-12-west.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.4.0": {"name": "lodash.get", "version": "4.4.0", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "ed35fb9fe4681abc7eee8c2651120d9015d40a86", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.0.tgz", "integrity": "sha512-cdh39LphqzajNCXCZ56ggS0zgxeohOtByAONuTKrp71gOvXy33NaJ9TmYsoeVpNgoC9k6gR7VnhdtBSSP0YIZg==", "signatures": [{"sig": "MEUCID8STc8+K8m2sVq41YdbxAfsMp8aixDQsBfZHsppUZa2AiEAr6ikFnCV21WUOUfeykFlc4JPK1Kpq4SJqcJbjO+mPfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "ed35fb9fe4681abc7eee8c2651120d9015d40a86", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "4.2.4", "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.4.0.tgz_1469457878230_0.45184128312394023", "host": "packages-16-east.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.4.1": {"name": "lodash.get", "version": "4.4.1", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3cb0acc6a6dc15b5e76197a895a47a2a45ce7f42", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.1.tgz", "integrity": "sha512-IBEZPb7xUbGG2BwE8IODN6yrctOxJlkGhQu0YUfgK+iUyIlbi0vaCwEnoSbk7D7tC1vhdtrq6pM2VRUc4TXNDA==", "signatures": [{"sig": "MEYCIQDsh50+5bEadf8llK1sJwUWJN6W8FD607JrLcVyHdqL2QIhAJb/OgvMM7geRqAsCKqktVPZq3bRHGqkoOYt1uw5jEnS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "3cb0acc6a6dc15b5e76197a895a47a2a45ce7f42", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.8", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "6.0.0", "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.4.1.tgz_1469924577631_0.5892761377617717", "host": "packages-16-east.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "4.4.2": {"name": "lodash.get", "version": "4.4.2", "keywords": ["lodash-modularized", "get"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash.get@4.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "2d177f652fa31e939b4438d5341499dfa3825e99", "tarball": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz", "integrity": "sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==", "signatures": [{"sig": "MEYCIQC10u2i1zbqz9uheOR7U1Bv9bHDWOO4L8ySfsyvo/ExegIhAMYf6tuY3tAAe8swQWJJx6z35T5aKCxyDiz3XgISW5rc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "_from": ".", "_shasum": "2d177f652fa31e939b4438d5341499dfa3825e99", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "The lodash method `_.get` exported as a module.", "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash.get-4.4.2.tgz_1471109964075_0.3515763762407005", "host": "packages-16-east.internal.npmjs.com"}, "directories": {}, "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}}, "time": {"created": "2015-04-16T16:30:47.000Z", "modified": "2025-01-23T20:12:59.749Z", "3.7.0": "2015-04-16T16:30:47.000Z", "4.0.0": "2016-01-13T11:02:34.157Z", "4.0.1": "2016-01-26T08:49:30.896Z", "4.0.2": "2016-02-03T07:27:30.833Z", "4.1.0": "2016-02-08T02:26:37.690Z", "4.1.1": "2016-02-16T05:58:40.746Z", "4.1.2": "2016-02-16T09:34:50.482Z", "4.2.0": "2016-04-03T03:49:14.615Z", "4.2.1": "2016-04-11T16:54:37.455Z", "4.3.0": "2016-05-12T14:10:33.687Z", "4.4.0": "2016-07-25T14:44:41.560Z", "4.4.1": "2016-07-31T00:23:01.462Z", "4.4.2": "2016-08-13T17:39:26.852Z"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "get"], "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "description": "The lodash method `_.get` exported as a module.", "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "readme": "# lodash.get v4.4.2\n\nThe [lodash](https://lodash.com/) method `_.get` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.get\n```\n\nIn Node.js:\n```js\nvar get = require('lodash.get');\n```\n\nSee the [documentation](https://lodash.com/docs#get) or [package source](https://github.com/lodash/lodash/blob/4.4.2-npm-packages/lodash.get) for more details.\n", "readmeFilename": "README.md", "users": {"koulmomo": true}}