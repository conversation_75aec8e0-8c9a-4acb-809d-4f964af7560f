{"_id": "@babel/plugin-transform-unicode-property-regex", "_rev": "30-58600c2045c81647119a676ff7ace551", "name": "@babel/plugin-transform-unicode-property-regex", "dist-tags": {"latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.22.0": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.22.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "5608f53ee68eca2d1e27234f725a46ac8a260b82", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.22.0.tgz", "fileCount": 6, "integrity": "sha512-uQacKjQ46K+yDfrbEyhEGkqqf5Zbn9WTKWgHOioHrTnOSVGYZSITlNNe0cP4fTgt4ZtjvMp85s4Hj86XS3v3uQ==", "signatures": [{"sig": "MEYCIQD9iRfo5odL3fPlHBP2M7dRdJwk/3luRZRSnHhdGUQdIQIhAIfQU07xtq85RKE1Xox7GbyRxgF/94d79w4Q1PJRI+7p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5006}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.22.0_1685108741692_0.967878259882327", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.22.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "597b6a614dc93eaae605ee293e674d79d32eb380", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.22.3.tgz", "fileCount": 5, "integrity": "sha512-5ScJ+OmdX+O6HRuMGW4kv7RL9vIKdtdAj9wuWUKy1wbHY3jaM/UlyIiC1G7J6UJiiyMukjjK0QwL3P0vBd0yYg==", "signatures": [{"sig": "MEUCIQCqgSzMSccDhEu2kRt8Y22cN2AQ8lfFp03lrTmvpJRQ0wIgdWVAgiMQDUWyKiA8gQxFdmOpyQjOXQAZm6079+DP3XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4986}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-create-regexp-features-plugin": "^7.22.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.1", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.22.3_1685182261623_0.6601028036423042", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.22.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "098898f74d5c1e86660dc112057b2d11227f1c81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-HCCIb+CbJIAE6sXn5CjFQXMwkCClcOfPCzTlilJ8cUatfzwHlWQkbtV0zD338u9dZskwvuOYTuuaMaA8J5EI5A==", "signatures": [{"sig": "MEYCIQCT0OzFeEB9zlyMSb3bgDKtbDOGvHRGGn8MmQjFil/oBAIhALkLCTUvlgMv5pLm8zO3w3untifDxmh2GoaXOxPvyEJw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4986}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.22.5_1686248498840_0.9060014911019367", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "ed914005305202bfd8e28cd2cd5fb283f045c3b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-qhZys+qjKpak3mQWFy4b+8uiaGTMSCa2fNbzJhG7brF3fNbe9Ax582TPEWynzIzGi/jFFvNfOzjIXsSLWZepCw==", "signatures": [{"sig": "MEQCIDtiZBuukbh0GN4MnIELw0MaaJpq9XejWryWMgSU4y5hAiAN2H7qV/JnXi0b+1l9g94Rwpy2IpLcKvUpKDeoupc3nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.0_1689861623596_0.8622671426085511", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f4360e95887833c842cb695708d348e27da682d0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-CVNl1NGiJ+2dil3t69Hoo1vXQNgh5r8vjudmTHzZqM+TrcO5msOhP3i0qUHL/kIcg5mb+NnRfOuzF6geXxSOUg==", "signatures": [{"sig": "MEUCIFmN+aRi6riq55kK5qa0pTQOPT2Sy+BRh2yUzsJTd3t5AiEAior2OL7z8e1JevrI+/QXNTcyU5X3/BniEXgIxir4SCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.1_1690221176657_0.06661992389010218", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "da858d5aa0edcb5508d5de52db8005852b536605", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-8rGX21hUqUOc1n10D9IY76wsFDjAz1oYZoFoHjllu3X/v4c/kochVjs8EC+pQzlj6hfu95oKc9dIHJhxvEmT4g==", "signatures": [{"sig": "MEYCIQCoSctarQvkQRwTTFA+fzMced0C4bzXwaAxKC9FPxzXNQIhAPU9kHxqd9N+9ql8AHYdnX1hFfsB0XAK95ME6zv7k2xp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.2_1691594119305_0.09860247420340817", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "45070219ecb5d32d5c4a75fbe5ea67adb3ed947c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-jyjo3U46pBcz5w3FkiYw2YjvVPkrIxDbasMOMs7J2Xoo2FqbzKOZVEm3kyMUWMveDWYvR0Eou1AngMrRTbbRQA==", "signatures": [{"sig": "MEUCIQDZrckhHiXeA1uYdvj+EnEaaxWgybMlyMCnZpvgnrZRGQIgBPhTDabzpn/DAXVgzefPqoO+0nbQEZtGXb7509U7PjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.3_1695740253565_0.37149832220389767", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f3b51189e5a97a94b1bf3f45d2e528320fee1254", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-RsJFo3YeWTnhzGnRoZ86T7w1Pby0LObxqObLKEJ5Y3Fn5zu0VhGg6Ie8oM0Hofzs+jC9k2NegUKAi1rNLY6Tug==", "signatures": [{"sig": "MEUCID1jGvKU7LTrzRvtOWhzDo97ZosUSMq55+W1sqRQzBseAiEAwPCi88eHi3a4ne2LobuuCEaNWeiXPhiWVmy4maoc1OQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.4_1697076406488_0.7416552206769982", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.23.3", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "19e234129e5ffa7205010feec0d94c251083d7ad", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-KcLIm+pDZkWZQAFJ9pdfmh89EwVfmNovFBcXko8szpBeF8z68kWIPeKlmSOkT9BXJxs2C0uk+5LxoxIv62MROA==", "signatures": [{"sig": "MEMCIEZmbvGJgT8BEkffNRYLg43bO3nwXQ+E80U4Eqf0UfRMAh8fMeMNGg4hexmlTqezzDbTINuzQDTCIy/78tQBsJKq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5067}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.23.3_1699513443310_0.9199464474920325", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "2e3aa7735c9ce72ea50112c62831e8bcf7e7cf78", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-yFifbFtGjIAh+mzLvvEfVOxvLwqiyS+M28SHB0ms4C77eyvtq7WGfPxEl/VWQ6AFvoWhMpDDpXFI5SjrWMFx8Q==", "signatures": [{"sig": "MEUCIGchhAynKjyl37pm9wVBqBpZvq2VW1OyIxpasLSfic4NAiEAptrdmIMbzEsWT0bkIV6CcoCKjDuGGq3mJFmixy042VU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.5_1702307978899_0.5727553155833531", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "7ac2bcbd88ee70856ce5819e0fc2fa79f68c16cd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-ae44bOQkeQw5PoZTQS3/dmE+4PtveIUhofqZP0Myy1I9Od6nCGaSR0KY+jY6a++VUFZob4iwpIMhR6WcvpOleQ==", "signatures": [{"sig": "MEYCIQCm8FlFcm1qQOjZsuUBgunoJRE0ej9jSTbmU+ws6/1UBAIhAM1EUMyDriAfmfreBudu4IQKEpsaradKgR0m3RJRpK1N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.6_1706285678449_0.488291012376211", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "874a44ed6440c02473e5e79a829dcb10ea09aa0e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-0SHCHF8+NKqhtadxYUpSZvcFe1hgFBN+79mKjkOifhkZjFSp4wMKOgvmCHFO7qoP8N4/I94RZ0hZaSpOkz3z0A==", "signatures": [{"sig": "MEUCIQDlpCpLuew5HbUU8mHJYsLTtbuG2qRpIgXCWp0S9KHSFQIgEFsSimAbtvLtC+grr5WZvK/hAM051GxiGTfXxUqUHu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.7_1709129139148_0.725468615458587", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.24.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "56704fd4d99da81e5e9f0c0c93cabd91dbc4889e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-Ss4VvlfYV5huWApFsF8/Sq0oXnGO+jB+rijFEFugTd3cwSObUSnUi88djgR5528Csl0uKlrI331kRqe56Ov2Ng==", "signatures": [{"sig": "MEQCIBvIfOj5NBa0CRaMNTuATS/DwD1FnmhA69DjNVa2H7dwAiA+qyU3hK1KOCJVycM7nryp1qB6J1E8h4qNPKGaAH1LKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0", "@babel/helper-create-regexp-features-plugin": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.24.1_1710841748796_0.7936914380801308", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a14469b7812b16d42d743640188e9a702f97f04b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-oaTeVKqA5zzYBnhHhcSmkB4I48A6b00tCZL5tjR7Qh2GpNCqM59jNpwGPSG8jKCTuWYZxqkDlDhTyBEHG6cYkw==", "signatures": [{"sig": "MEUCIF7KoUbH1Tp6Ncq9JwoSJiMD7YCVLBlfnjqdzJg/Ef6EAiEAqwC0zg1S7AVy/U/m4WB98Q/QQvLIco4xrb2l5EY6NqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4873}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.8_1712236816329_0.5390300989761028", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.24.6", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "e66297d5d452db0b0be56515e3d0e10b7d33fb32", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-8EIgImzVUxy15cZiPii9GvLZwsy7Vxc+8meSlR3cXFmBIl5W5Tn9LGBf7CDKkHj4uVfNXCJB8RsVfnmY61iedA==", "signatures": [{"sig": "MEUCIHlSURX8JF7Sjj29RFRzgFA1SB53iUMxlQiV2daMUuSBAiEA6PxX991wYj7wVAROLhV0EdVuwXE2YTmGgVI+0mEgHtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71391}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6", "@babel/helper-create-regexp-features-plugin": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.24.6_1716553506998_0.07927173611191729", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "05f1e682a948b8bfd03d67d6014c994562a5f5cd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-wlXibcDZv1xdyLfnX9gtgAUG6rAi8MexRjLZxt/8paobHXsocnQXoipw/l2dhvilfMlV6UmmxWYRfjOoOx1dGQ==", "signatures": [{"sig": "MEUCIQDpLAiFYZ8idVq0m854HmtpK2REiYFPperc0mw5oHB5SgIgUQWSrJ5rzeccRcqDuQjdf0yX6EDH++sSmEML05A+WZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71579}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.9_1717423492027_0.27034120572660214", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "42dd8f686c489e48b5c9e3074496cdca4c884605", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-7q6aSPUot2EC0cqqgoZKRQlWZDTQ0NwcrGKNnKOOi6xTL0g4cGTHSK7awYTtXAhsXdMqkTpcMOWa7mfrscgbZA==", "signatures": [{"sig": "MEQCICdPJF3edkUbI2UBDMdWuJwMuV+DGmqWN/GCFaMFhJCEAiAktGarJcqmJXI0OO59bna9l0Ytcu1M/v2eh7fp+xNHNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71587}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.10_1717500028458_0.30629106418480645", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.24.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "9073a4cd13b86ea71c3264659590ac086605bbcd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-uH2O4OV5M9FZYQrwc7NdVmMxQJOCCzFeYudlZSzUAHRFeOujQefa92E74TQDVskNHCzOXoigEuoyzHDhaEaK5w==", "signatures": [{"sig": "MEUCIBCYaKLBiOsY9nlWjZXRg8G65ZYbzIhXHrWABDZTx0iWAiEA7fZQCFSZy68sEkheA/hVC+pw5dYWPJ3L3optVCnQH+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71387}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-create-regexp-features-plugin": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.24.7_1717593342019_0.7985570174288741", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "150d049f221cca5343038d94e2e077cc3602dae2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-onFtV++FKFy7zqG+Do1W4qrhNeDJSO9NIaZBOU3XdA8cLdghX/R8hc62QvhLPYNZyUhfAU+ZLRxMbUKNe3e40w==", "signatures": [{"sig": "MEQCIA6cvVa3F87bfu5bj9S7Ne9ZwNSedTCUFWk8kxvDTFNCAiAMayfXAk90H8ofDuBiiWkXyaOVUpY6maHOx/+hJJ0AUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.11_1717751753207_0.23125430906440125", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "660929f05107744d72f1700f50a26e3d04df1d21", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-1NdYB6vrdotJuyIzz2ycPvOlTZX+hH+2ABKGGIVvdH1mhR157lHalRJtpSjcX+q5hYlvTuAHJtLFzoGb1QE5yw==", "signatures": [{"sig": "MEQCIF+F9ZMmXCLV4xEAVW4SYq/wAYVGmz0dC3aXRYzGtz8JAiAkJOJ1JZ5Btpus0SlCKiVsJoKIT55r8yhkiERsQBTGyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68251}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.12_1722015226811_0.877943393296019", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.25.7", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "25349197cce964b1343f74fa7cfdf791a1b1919e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-IWfR89zcEPQGB/iB408uGtSPlQd3Jpq11Im86vUgcmSTcoWAiQMCTOa2K2yNNqFJEBVICKhayctee65Ka8OB0w==", "signatures": [{"sig": "MEYCIQDrtDj+73N9Csswl9r0jDq3wXVkvuqc32fwZtQHuCtJvQIhAPh7MCkO6Cged6rz1OJrVVKk+JNf43aolwgdyiAVPAEe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75908}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-create-regexp-features-plugin": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.25.7_1727882113610_0.37465384385714806", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.25.9", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "a901e96f2c1d071b0d1bb5dc0d3c880ce8f53dd3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-Jt2d8Ga+QwRluxRQ307Vlxa6dMrYEMZCgGxoPR8V52rxPyldHu3hdlHspxaqYmE7oID5+kB+UKUB/eWS+DkkWg==", "signatures": [{"sig": "MEQCIAq/bEVnijwZMsQabSLqnbD6eS1YH3K8uYgLQv8VH73LAiAFjLMuRNGUfPsuvb32nmGV6lIgspvFDIQPr0h8czE4ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5030}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-create-regexp-features-plugin": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.25.9_1729610489567_0.7546047379150747", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "55fb5c42e0442f2f677cf9285039f2466565507a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-uEqNAwkTSB1op1PjRAi7kSEX5/dys0Nuzrkc4M/zCkaQBGN1HIOAaAnDYiYLGEqGQ1LBi0KvSic3KeBVCSqyiQ==", "signatures": [{"sig": "MEQCIE2BhMU3tBUn/hrL/Q19/aHDA7jDGe/XgnAOuJlZ/72YAiABsxdMpQb/4UwUqDPJNj4t92qsZTNHbrb7+ZcyZcRshA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.13_1729864469592_0.5454595246529756", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "73cc037238ae0b28953735d7e8cdc0ae0b195a81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-1BlPTUERyFbQdsaiPcjYbcNaJ0ObarrKIT9F5/cW5A1y7C15DP1v6KIzWxtHE7rKFX6fSXcoavPhvHRmzrOn8Q==", "signatures": [{"sig": "MEUCIEeUkiXBIYhYhFGh55u8b4e6lH+SlMZrQYVgIc8nJTROAiEA2oKikAsG8S5yPJt2wkiF0Rps/P5DtTePYgycnJ2SFIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.14_1733504060002_0.83295598888885", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "df0f3babf4b00ef9e0503e5603fffc1bde06e85f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-QmnKE3glIGVkPeLABLV2QMfpcG35ikui/r06UyNFJduWqDx2u/R0VaonV6y2BW/M3VASZb3ciuA/lsZ62NoFRQ==", "signatures": [{"sig": "MEQCIA57rkgMVakLh6sx5eX2xbxBJTzYBlsDw6b0JJjRkv6pAiBk6G+LaBS5x4w5STttTeMSEs17wVB3VRewVrweJfGQpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.15_1736529887481_0.1247957060596725", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "4f3682a755a9c4bd107a1442898779f10d431de3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-8em/DkqrtrV9eCiGOedHo50iZ2leissPZNgDVn+4pgZZGeZGlL/EjG5jeo3vVrm+iHhcLM09eLGdBesVYDYw0Q==", "signatures": [{"sig": "MEYCIQDfcjdUysGvq6cnA6r0Mm6CFIbY1/7NuYc2Rmhm8hHp6wIhALyaVUjTwdIrT1JJuiizNCldhQcoz/g8SuxRja3nQBXu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.16_1739534363459_0.1845736977631558", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "ac126f6d434a83f9664da433cba24cb0fb48ba08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-2QNQQGoalX6W8F43WMIaFgYG9MMTZya8t78L8csj3Berd3aaYyPiqQV5Iz3d8RUd9/Clgku1ITkH2bHOSRKgng==", "signatures": [{"sig": "MEUCIFTc7UImyH2sm9WNUvQSFKTaJs25+JHnoklLVo/HBIV8AiEAlTQQDDjXTIttIqvgcQ8XG4i4KrSjg49TpptC3v8YIFY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5243}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17", "@babel/helper-create-regexp-features-plugin": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-alpha.17_1741717516825_0.39618126717744007", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "7.27.1", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "bdfe2d3170c78c5691a3c3be934c8c0087525956", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-uW20S39PnaTImxp39O5qFlHLS9LJEmANjMG7SxIhap8rCHqu0Ik+tLEPX5DKmHn6CsWQ7j3lix2tFOa5YtL12Q==", "signatures": [{"sig": "MEYCIQCMVx4KFxONYHSM3OjO92Qz/mXB5WvxfD04YGJdUv+5RQIhAMJsPz+cYzqgBA9s+sYjOu6MOHMSGs1ovv3PmKbVj981", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5030}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-create-regexp-features-plugin": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_7.27.1_1746025752636_0.708286150454597", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-beta.0", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "bugs": "https://github.com/babel/babel/issues", "dist": {"shasum": "f6cb869848f3d23d2d4ccbefab006e7c70e45e69", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-6b9nIUhjVDmgo+jHMv1qgeYQ55VVlURcQoUrO148Cm413fi26I/qPC2f1MgusEMB3VdPSHnhuIlNdUTaN1g8qg==", "signatures": [{"sig": "MEUCIQCXX20GRuoiKsTe2oMhjCAOmymekABFijCJrbnGwiZMMwIgDRvWsaNBV664T5l4C4uCsdqkx9HXtq3BE3xzMXaVVQ8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5217}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0", "@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0", "@babel/helper-plugin-test-runner": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-beta.0_1748620289015_0.9649607724268958", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-transform-unicode-property-regex", "version": "8.0.0-beta.1", "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "engines": {"node": "^20.19.0 || >=22.12.0"}, "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^8.0.0-beta.1", "@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1", "@babel/helper-plugin-test-runner": "^8.0.0-beta.1"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-transform-unicode-property-regex@8.0.0-beta.1", "dist": {"shasum": "3f07e4ee7acacf35de24113808382524c4e5f777", "integrity": "sha512-Kd9ibe6FlhvZ7PNUV2z/dB858eI9WQbt89hjlXVGPrlnknq8cZ9aWftmLVeeRG/EzlEnPB8CQNS8CHin9ijWjg==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 5217, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCuO/e4zxMK2FpexoxoKziN3q9CKwGqmmZ4ojQDO4VRIQIhAIknGRSyzjqMZKCh3GnIxkckOHiuA4lxx3QDLe3CgW/M"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-transform-unicode-property-regex_8.0.0-beta.1_1751447073543_0.5677396275198467"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-26T13:45:41.629Z", "modified": "2025-07-02T09:04:33.975Z", "7.22.0": "2023-05-26T13:45:41.849Z", "7.22.3": "2023-05-27T10:11:01.814Z", "7.22.5": "2023-06-08T18:21:38.994Z", "8.0.0-alpha.0": "2023-07-20T14:00:23.767Z", "8.0.0-alpha.1": "2023-07-24T17:52:56.817Z", "8.0.0-alpha.2": "2023-08-09T15:15:19.471Z", "8.0.0-alpha.3": "2023-09-26T14:57:33.796Z", "8.0.0-alpha.4": "2023-10-12T02:06:46.730Z", "7.23.3": "2023-11-09T07:04:03.468Z", "8.0.0-alpha.5": "2023-12-11T15:19:39.120Z", "8.0.0-alpha.6": "2024-01-26T16:14:38.651Z", "8.0.0-alpha.7": "2024-02-28T14:05:39.304Z", "7.24.1": "2024-03-19T09:49:09.047Z", "8.0.0-alpha.8": "2024-04-04T13:20:16.498Z", "7.24.6": "2024-05-24T12:25:07.153Z", "8.0.0-alpha.9": "2024-06-03T14:04:52.194Z", "8.0.0-alpha.10": "2024-06-04T11:20:28.612Z", "7.24.7": "2024-06-05T13:15:42.219Z", "8.0.0-alpha.11": "2024-06-07T09:15:53.368Z", "8.0.0-alpha.12": "2024-07-26T17:33:47.064Z", "7.25.7": "2024-10-02T15:15:13.824Z", "7.25.9": "2024-10-22T15:21:29.757Z", "8.0.0-alpha.13": "2024-10-25T13:54:29.842Z", "8.0.0-alpha.14": "2024-12-06T16:54:20.244Z", "8.0.0-alpha.15": "2025-01-10T17:24:47.677Z", "8.0.0-alpha.16": "2025-02-14T11:59:23.639Z", "8.0.0-alpha.17": "2025-03-11T18:25:16.998Z", "7.27.1": "2025-04-30T15:09:12.821Z", "8.0.0-beta.0": "2025-05-30T15:51:29.187Z", "8.0.0-beta.1": "2025-07-02T09:04:33.721Z"}, "bugs": "https://github.com/babel/babel/issues", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-unicode-property-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-transform-unicode-property-regex"}, "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}