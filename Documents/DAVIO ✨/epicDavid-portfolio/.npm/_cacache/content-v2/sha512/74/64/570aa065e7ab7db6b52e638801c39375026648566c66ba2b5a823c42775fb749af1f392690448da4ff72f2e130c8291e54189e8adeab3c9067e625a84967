{"_id": "supports-color", "_rev": "102-7d212c0dc3c73e399af85f3d3ab03a74", "name": "supports-color", "dist-tags": {"latest": "10.0.0"}, "versions": {"0.2.0": {"name": "supports-color", "version": "0.2.0", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "d92de2694eb3f67323973d7ae3d8b55b4c22190a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "integrity": "sha512-tdCZ28MnM7k7cJDJc7Eq80A9CsRFAAOZUy41npOZCs++qSjfIy7o5Rh46CBk+Dk5FbKJ33X3Tqg4YrV07N5RaA==", "signatures": [{"sig": "MEUCIFfnh7ytc8FXF2RI1K3mG2VyCrFzFQj2juAzx1/aNMtgAiEAmLfQkFdxmPk7CX5NbRYl8nyU4rnUBb4f5DuCd6D0o14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "d92de2694eb3f67323973d7ae3d8b55b4c22190a", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "1.4.9", "description": "Detect whether a terminal supports color", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "supports-color", "version": "1.0.0", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "5e27d62fddbc2963b160e245c1445f966b0e79d5", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.0.0.tgz", "integrity": "sha512-6IISAinphCey7kgNXTWyaCf0htz3Tmr/l4iJGzukU8iUcnsfSMZ0XlnOIBXqAJ4qcLjx9OBWuCcVtD8pyOowyA==", "signatures": [{"sig": "MEUCIQDQQRcV1Hl5wpT231cP//A1UW+Lu6H+IRZJ0xuX2VKWMgIgGNsE9ExIczzUQsQqGEb3Poid/9M0T2hTotJJPDRUFLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "5e27d62fddbc2963b160e245c1445f966b0e79d5", "engines": {"node": ">=0.10.0"}, "gitHead": "95036ad1622e0c973ce9c09b93af8a6ebda6a56b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "1.4.14", "description": "Detect whether a terminal supports color", "directories": {}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "1.1.0": {"name": "supports-color", "version": "1.1.0", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "fdc4b1a210121071505a2d1ef4d9f5d8fba7ef82", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.1.0.tgz", "integrity": "sha512-ak2IahsZlxhbEQa45UujUllnDZ19YI/rh/qSBO+0+M51DUjOxbrxHC5Dtj3FgMMCROHXGKdLTEa8w/6kjxsudQ==", "signatures": [{"sig": "MEUCIQDgqFAJM+UDRF4wyzR5Yqdz8dgwY3zCW2oZCYGOun2TGgIgZInSS9a7hHzN+YBaVK6X5WlVsXMcxIRgSDEO3FQKpZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "fdc4b1a210121071505a2d1ef4d9f5d8fba7ef82", "engines": {"node": ">=0.10.0"}, "gitHead": "892801bea69ac9658e46378c06d069752aad9b83", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "1.4.23", "description": "Detect whether a terminal supports color", "directories": {}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "1.2.0": {"name": "supports-color", "version": "1.2.0", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "ff1ed1e61169d06b3cf2d588e188b18d8847e17e", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.2.0.tgz", "integrity": "sha512-mS5xsnjTh5b7f2DM6bch6lR582UCOTphzINlZnDsfpIRrwI6r58rb6YSSGsdexkm8qw2bBVO2ID2fnJOTuLiPA==", "signatures": [{"sig": "MEYCIQCCElfuYEmJmlZuNXk3UAne4ft4EkT7iBDjSApsdaKpOwIhAO9YMJwoOUZbWsiJi6w66m1PHoIvj5hEwDGe1guFLBCt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "ff1ed1e61169d06b3cf2d588e188b18d8847e17e", "engines": {"node": ">=0.10.0"}, "gitHead": "e1815a472ebb59612e485096ae31a394e47d3c93", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "2.1.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "1.2.1": {"name": "supports-color", "version": "1.2.1", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "12ee21507086cd98c1058d9ec0f4ac476b7af3b2", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.2.1.tgz", "integrity": "sha512-MNpF4lwJXGrsw6ZnDWZKDAtUqjJXR9QmFshDsbWzLimdzVRuxT3dWxPXnPYRSXnncQsIGTyZGNPDKQ8R8dNYcg==", "signatures": [{"sig": "MEQCIDvg7npxor9fHbG3PXYW5oWWd2R3Eu6GVRGajNxOPoATAiArSmMpq4ZS2HIR6GbiZ92D7APP1E6fr35Qop2bPtwpbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "12ee21507086cd98c1058d9ec0f4ac476b7af3b2", "engines": {"node": ">=0.8.0"}, "gitHead": "ffe5e224bd24dc0410787b94e192d240be025aec", "scripts": {"test": "mocha"}, "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "2.1.16", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.10.35", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "1.3.0": {"name": "supports-color", "version": "1.3.0", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "ca7def134d8bf8163e1c92905a49a2e4439b72a0", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.3.0.tgz", "integrity": "sha512-0aCuBsk0ZZY4+bNS0XYKWrTmCaicmE/jPsbrL8qsNbqW+0d+vkasufiDNF5LezIPHMXAGfKnsPaCaWalbP5TEQ==", "signatures": [{"sig": "MEUCIHJMrHzMTDMCtR/v/ihBtGhLQ2cmlRwQ/qFm2fthg07hAiEA4iZHNrpoxy3B8a/27lIHNVM+1BSZVU8BDPF/Xn/IHaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "ca7def134d8bf8163e1c92905a49a2e4439b72a0", "engines": {"node": ">=0.8.0"}, "gitHead": "6977091035fc1634eace9470a35e21262d84356a", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "2.5.1", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "1.3.1": {"name": "supports-color", "version": "1.3.1", "keywords": ["cli", "bin", "color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@1.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/supports-color", "bugs": {"url": "https://github.com/sindresorhus/supports-color/issues"}, "bin": {"supports-color": "cli.js"}, "dist": {"shasum": "15758df09d8ff3b4acc307539fabe27095e1042d", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-1.3.1.tgz", "integrity": "sha512-OHbMkscHFRcNWEcW80fYhCrzAjheSIBwJChpFaBqA6zEz53nxumqi6ukciRb/UA0/v2nDNMk28ce/uBbYRDsng==", "signatures": [{"sig": "MEYCIQDcYAUpWzHMtozS3H3U2r/ZpjG+61E1d6cLKVNR/JFOLQIhAKYt5kbjsHF6HO1c2G2GTR0EaYPAhRJCWdEybq3rcFcs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "cli.js"], "_shasum": "15758df09d8ff3b4acc307539fabe27095e1042d", "engines": {"node": ">=0.8.0"}, "gitHead": "09f1b4c336cee7269b4c8b3a8880054a23fcb35e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/supports-color", "type": "git"}, "_npmVersion": "2.5.1", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "2.0.0": {"name": "supports-color", "version": "2.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "535d045ce6b6363fa40117084629995e9df324c7", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==", "signatures": [{"sig": "MEYCIQC6/KmRPOKswTeTed6ziGC6GJ334LecaqhUOmZFHRXb2wIhAOzRnbmEP0TI/vol6YYpwNb4M7XM6okvEvhQbeLRHRuf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "535d045ce6b6363fa40117084629995e9df324c7", "engines": {"node": ">=0.8.0"}, "gitHead": "8400d98ade32b2adffd50902c06d9e725a5c6588", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.11.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "3.0.0": {"name": "supports-color", "version": "3.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "15f7652e0a478b18405800d9a3dab21a26e91b65", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.0.0.tgz", "integrity": "sha512-zeEqjojoxHVGxevRk8wJCiBQv48PSegFTBvRsxVnWJGaOHfjjp59doMxnDMpCzZDWLxSSY2zwF2vRzvCIemNSg==", "signatures": [{"sig": "MEUCIQDKnQjsh9g8LeZcYyCZPKFyRB2YV365Yno1hfGb+yjkOAIgWnwRGeaI4J+OfB5v7subfqk/T/DETLXxfBExewLGG9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "15f7652e0a478b18405800d9a3dab21a26e91b65", "engines": {"node": ">=0.8.0"}, "gitHead": "5649000513a10f050abe8740760aabdfa0053d44", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.11.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "3.0.1": {"name": "supports-color", "version": "3.0.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "5044ef794ba979154745dfb77970c53e9b17908a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.0.1.tgz", "integrity": "sha512-Kd13czuqYr/QMIxjhu3xLgQ12ROizbVExgZ8SJVZ+FZBLUOmJ8WzHjbhv4kFvtxYwUsKe4Y1mybxYjFgM/RMaQ==", "signatures": [{"sig": "MEQCIDQbGplkV3FCX3QlLUlOqHNeejRPuWdqAqlFVqxH8QUZAiAk0pmEtoZCZdR2P87EqYgru3M/MAUup3KCN2jiTH/4ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "5044ef794ba979154745dfb77970c53e9b17908a", "engines": {"node": ">=0.8.0"}, "gitHead": "10b2124bf28a5775d82e93f686d4ccae38921045", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.11.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "3.1.0": {"name": "supports-color", "version": "3.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "71b1e2116bf51d24208768c9afa2e5f118d8ef61", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.0.tgz", "integrity": "sha512-8Yt2RgLxfi54eh9OP0/xQVJPIcr8tepUliIZTvfVto2UM5sswGgiBPb+OcQmbYm26zqt/NaxrGNNy+nXR6e1QA==", "signatures": [{"sig": "MEUCIBhL1Ooxi4Y1dYV95MUdLuxwFXhKkY605xe78zGzbnM9AiEAzXii9tHxQKnHOtNRVGjNHURpiy18nSVfKz8BAytWpUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "71b1e2116bf51d24208768c9afa2e5f118d8ef61", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "1f42c42e6f131dcbbd791600ef27a9363064d076", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.11.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"mocha": "*", "require-uncached": "^1.0.2"}}, "3.1.1": {"name": "supports-color", "version": "3.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "10b730ea2c36e9f3790a035f71b007259260ec4b", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.1.tgz", "integrity": "sha512-G9VDK4K194efM0N+Oabk1F6hnKLUKdSgfc9kL22lwRZ2hcv3XfzwLJfrPDLlkBnyi5jIN1dX0JKiF0qFLT0s/g==", "signatures": [{"sig": "MEYCIQDyaZsdG+EqNsWlF8iNqhqSYPz0s7lyrZV2oYZ705VQNgIhAIWrAGuI58OARFbduy7Db42+4kmDlj4urG6Cz98ZPB0u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "10b730ea2c36e9f3790a035f71b007259260ec4b", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "9434c93918301a6b47faa01999482adfbf1b715c", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.11.3", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}}, "3.1.2": {"name": "supports-color", "version": "3.1.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "72a262894d9d408b956ca05ff37b2ed8a6e2a2d5", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.1.2.tgz", "integrity": "sha512-F8dvPrZJtNzvDRX26eNXT4a7AecAvTGljmmnI39xEgSpbHKhQ7N0dO/NTxUExd0wuLHp4zbwYY7lvHq0aKpwrA==", "signatures": [{"sig": "MEYCIQDkKfueGeTBQohTgq8MrmjGQqjSVxl2dsgAM/571PvJfQIhAJql7ppiEopd5mggMfqFfXU/EpgrksFIGUaOwxauLWK0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "72a262894d9d408b956ca05ff37b2ed8a6e2a2d5", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "d9e363732f48ad2bc6b936357246b55e136aa989", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/supports-color", "type": "git"}, "_npmVersion": "2.14.4", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}}, "3.2.0": {"name": "supports-color", "version": "3.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "c4c385da44edcf152136bfa7f3125aee9b0a4f16", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.0.tgz", "integrity": "sha512-LmG2+4D4+QFxwUIoyUeH87m4fg1NTb7WYSPXnVYdMhDvG5uK7gIvFToIiSBJf4HOb+we924jDSZSQNFYp7zmeQ==", "signatures": [{"sig": "MEQCIDqOb4azk9XiBuJvm7Uo+nfOQNWJM8QT7U2J1hNty+UTAiA9oXjMKwUdwA2tbKhWcceH2k2gs3rx/Q0BobMlLtzdpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "c4c385da44edcf152136bfa7f3125aee9b0a4f16", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "4b5e52409dde2286f9c5fc1ea513d700d4172a11", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-3.2.0.tgz_1484524692724_0.9873743725474924", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.1": {"name": "supports-color", "version": "3.2.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.2.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "f15108573f33717782a5eaf7d1b4ce1cd2f2e693", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.1.tgz", "integrity": "sha512-TvLWkPm0DmMpOBYmzq1p7ASuI0a/7Lkf55m/soG84Va0KUCUn8qGBL7MIs9zDnD7dggAww5x9m07xXynTcW0YA==", "signatures": [{"sig": "MEQCIE/xJL7GUlybOysTINGBu3aaXSYWDfpqsF2TjfgSKREdAiAUDXY4v+Gl8fAs0rhc1fzv4+jIJtJ1cdLJA8LCFUI1nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "f15108573f33717782a5eaf7d1b4ce1cd2f2e693", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "4de3ce5a43fdb15615f9b7055124b21c6b59f5a3", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-3.2.1.tgz_1484525436130_0.994121718686074", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.2": {"name": "supports-color", "version": "3.2.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.2.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "c3eb919f3aed5fb659538822e7a42393a78e85b4", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.2.tgz", "integrity": "sha512-C7IdRQhjG8DI4sjCMXQus6RdPYvppnmsP4FkhXFIkJVMX0QCTAcNt0OJaOZUz2f/XayllTFKTbVJ50E5iZBj9Q==", "signatures": [{"sig": "MEUCIQDk0SVYOJ+ImDbJDl4SXGjcvaqaXi8oCAJqNpNjARo5FwIgfBabzkqD7IzNjeIzLL++eZKFR/k1C6CWKJZig1S99iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "c3eb919f3aed5fb659538822e7a42393a78e85b4", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "e2f8b6b7137dedcec6c3c58194d09730df659395", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-3.2.2.tgz_1484525960843_0.01745234290137887", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.3": {"name": "supports-color", "version": "3.2.3", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m", "million"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@3.2.3", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "65ac0504b3954171d8a64946b2ae3cbb8a5f54f6", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-3.2.3.tgz", "integrity": "sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==", "signatures": [{"sig": "MEQCIFhMr9MIiGXr1f7aRCNNf7qivD9Lso87AKxsWsBF6NodAiBgSdJ0XUUqfHdFizKfMLq7fJaJQxB4zCPEWE2gJMeOPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "65ac0504b3954171d8a64946b2ae3cbb8a5f54f6", "browser": "browser.js", "engines": {"node": ">=0.8.0"}, "gitHead": "c2394c1f77a0f64c4b2d0db33a3df3b841e6fc15", "scripts": {"test": "xo && mocha", "travis": "mocha"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"has-flag": "^1.0.0"}, "devDependencies": {"xo": "*", "mocha": "*", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-3.2.3.tgz_1484526472497_0.7697830176912248", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "supports-color", "version": "4.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.0.0", "maintainers": [{"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "33a7c680aa512c9d03ef929cacbb974d203d2790", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.0.0.tgz", "integrity": "sha512-UievrBFhhFqySoXJ0VYASpAKalZJ8TORWWAcL8wI9HPc2NmcVXlT/UosfiafvZrLYy8jIop9MtKXZ62t3l/hcg==", "signatures": [{"sig": "MEYCIQCtOtLAFD9O/GR+n5WG9dJRtylEJxaH+83pGcv41Sk/0gIhAK1fpyHv0bFVEDDU3w0yMxhGdRjHPlPx8iPrIlNsofL1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "33a7c680aa512c9d03ef929cacbb974d203d2790", "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "315d72712d78aff42503949725078354032d0e6b", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "4.8.3", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.0.0.tgz_1497986555161_0.6528980613220483", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "supports-color", "version": "4.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "92cc14bb3dad8928ca5656c33e19a19f20af5c7a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.1.0.tgz", "integrity": "sha512-psI4vjOVwVdPab8uBK1qiEVIOmoGxWqGWqSz0lrCEfdSOGgJ7KhRWlksN2zSybZnRZOoTNtsAr8fWfedsZKicA==", "signatures": [{"sig": "MEUCICYqXxxu8iVZDF1OfSDGF/2dbOQhzNxV9KsiUtnvfbWgAiEAl/JgeQsN6UufUF1fLpMNSbraCKqeSXSXOhSsO4Y0aSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "92cc14bb3dad8928ca5656c33e19a19f20af5c7a", "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "6dd97922ddc812df56faa666d7100521445f7d8c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "6.11.0", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.1.0.tgz_1498849027534_0.3442862711381167", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "supports-color", "version": "4.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "ad986dc7eb2315d009b4d77c8169c2231a684037", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.2.0.tgz", "integrity": "sha512-Ts0Mu/A1S1aZxEJNG88I4Oc9rcZSBFNac5e27yh4j2mqbhZSSzR1Ah79EYwSn9Zuh7lrlGD2cVGzw1RKGzyLSg==", "signatures": [{"sig": "MEYCIQDgvvu2HoKU4zM0jI2k3KB9OU8S8Tp4jEB1WSGBR0cjqwIhAMEODqt/6cA2klLK3iXgeL4K/jv13yGrzyoHX72U8Y/2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "23ac4529ba7a647a7d6410d990682e6d9892d660", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.1.3", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.2.0.tgz_1499397642650_0.3750420247670263", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "supports-color", "version": "4.2.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.2.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "65a4bb2631e90e02420dba5554c375a4754bb836", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.2.1.tgz", "integrity": "sha512-qxzYsob3yv6U+xMzPrv170y8AwGP7i74g+pbixCfD6rgso8BscLT2qXIuz6TpOaiJZ3mFgT5O9lyT9nMU4LfaA==", "signatures": [{"sig": "MEYCIQDZi8cTlbySj/RrA8Z+w0L9odSFlTPGBxX0VtaSLq4cDQIhAJTemUrWenomV6nC+U+v1y7bfcVQFK19Sw/zCRL215PW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "d5261a24bcfb6670a7574b318b6d93bf2f4dce0c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.2.1.tgz_1500721497271_0.5450081420131028", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "supports-color", "version": "4.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.3.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "0fa3755bb961136cf75ff2ee3eb775822c04a31b", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.3.0.tgz", "integrity": "sha512-Yf+8UYPgZlMkps2h2P+248+8iE2hXXWjiZgwcKQc2ncE2s1cYAMdpzyPJ4+ttifNogmi09L5Wr0QxyZN6O9M/w==", "signatures": [{"sig": "MEUCIBaZ5UHYMXXV9pNBveAVGgr3dQWzGLrkoFKsr27vVTT4AiEAqJdIT7aoeekIM6eUxrM3hICqmenWV3R6y9k+5MsR1is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "61008fe6ad344e798a7ca98f0d88509b622414a3", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.3.0.tgz_1504147213885_0.055988931097090244", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "supports-color", "version": "4.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.4.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "883f7ddabc165142b2a61427f3352ded195d1a3e", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.4.0.tgz", "integrity": "sha512-rKC3+DyXWgK0ZLKwmRsrkyHVZAjNkfzeehuFWdGGcqGDTZFH73+RH6S/RDAAxl9GusSjZSUWYLmT9N5pzXFOXQ==", "signatures": [{"sig": "MEQCIC0YnHmVBeoZbCBuqVGTqLFM2nXPcRkCk28x7ZI8F59pAiBiFUITN5S7zQjp9mAsjL2mvk2PrgpRkimIDTKr9A/nLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "d2e32f77030ddf583b95b79bc3f6417241472980", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.4.0.tgz_1504162477210_0.36436482798308134", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "supports-color", "version": "4.5.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@4.5.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "be7a0de484dec5c5cddf8b3d59125044912f635b", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-4.5.0.tgz", "integrity": "sha512-ycQR/UbvI9xIlEdQT1TQqwoXtEldExbCEAJgRo5YXlmSKjv6ThHnP9/vwGa1gr19Gfw+LkFd7KqYMhzrRC5JYw==", "signatures": [{"sig": "MEQCIFKrNLlZguM9UCnG+8PjT6snXllfsRIVFekHIPLT2VlPAiA/JC8xZaFAQDjvpRaVT7f3Dtul5+8PARdROOxhtpN66g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "be7a0de484dec5c5cddf8b3d59125044912f635b", "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "6c024379de76a36bb96feceb02d709eba5fd01c6", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-4.5.0.tgz_1508306144616_0.24811995681375265", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "supports-color", "version": "5.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "1db26229f6ae02f9acdb5410907c36ce2e362b13", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.0.0.tgz", "integrity": "sha512-gLqCIZW9dhpT7N8dWU/5pV6U3OfRfYiY3YEh5vne0c9+qZi+W1R/p45vJGbvQDVj9y3SzA37rQeOtFHRnjJhQQ==", "signatures": [{"sig": "MEUCIQCIcxiqhXqhPSjR0uMUuwHRQNu4+A0jUt5zIB/BrUYBGwIgcLJfGu+Joi+C6XmhcwjCvmzor2+vc8JCOFZfdcAgek0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "browser.js"], "_shasum": "1db26229f6ae02f9acdb5410907c36ce2e362b13", "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "ff840645b8fe1f5c32a3f3befc19e4a8726cdcd0", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-5.0.0.tgz_1508306744766_0.07775744306854904", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "supports-color", "version": "5.0.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.0.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "1c5331f22250c84202805b2f17adf16699f3a39a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.0.1.tgz", "integrity": "sha512-7FQGOlSQ+AQxBNXJpVDj8efTA/FtyB5wcNE1omXXJ0cq6jm1jjDwuROlYDbnzHqdNPqliWFhcioCWSyav+xBnA==", "signatures": [{"sig": "MEQCIDwZOn0sXd1AtQq8SxYUXswgN1cD9aVekjwDkcxFM3MIAiAWT7ai/+UlNywy5DS1F/K2T59Xs+6/aik4t29DDR+cTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "46d2378ef368caa0fbf2506a8690123f55318e2d", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-5.0.1.tgz_1511866497274_0.013081022072583437", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "supports-color", "version": "5.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "058a021d1b619f7ddf3980d712ea3590ce7de3d5", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.1.0.tgz", "integrity": "sha512-Ry0AwkoKjDpVKK4sV4h6o3UJmNRbjYm2uXhwfj3J56lMVdvnUNqzQVRztOOMGQ++w1K/TjNDFvpJk0F/LoeBCQ==", "signatures": [{"sig": "MEUCIQD8f6MO6N9vanOrxdj8wmgeVXifncz8i4ILHHkCVqHmJQIgODKA7BW6kXF0DTUOGBJcgDxPxBQwtR1NYYjlVZpijt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "c6f994cf48ef4a1036c677f9b6853fcb050e05d8", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"has-flag": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color-5.1.0.tgz_1513022197750_0.3715452023316175", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "supports-color", "version": "5.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "b0d5333b1184dd3666cbe5aa0b45c5ac7ac17a4a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.2.0.tgz", "fileCount": 5, "integrity": "sha512-F39vS48la4YvTZUPVeTqsjsFNrvcMwrV3RLZINsmHo+7djCvuUzSIeXOnZ5hmjef4bajL1dNccN+tg5XAliO5Q==", "signatures": [{"sig": "MEYCIQCOKVDKLzVHOTgaj1HVRMWt1Z/NkVL+Pt6h7XsnD5118AIhAOCLQcpF8YQ7mW5N/JbEfzqfYYHO0vu9t7x04/smBkNL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6670}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "1a6f8b2746e8ca3f3c2663d503e1c1a7329f7c47", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_5.2.0_1518353485685_0.8623635956973359", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "supports-color", "version": "5.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.3.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "5b24ac15db80fa927cf5227a4a33fd3c4c7676c0", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.3.0.tgz", "fileCount": 5, "integrity": "sha512-0aP01LLIskjKs3lq52EC0aGBAJhLq7B2Rd8HC/DR/PtNNpcLilNmHC12O+hu0usQpo7wtHNRqtrhBwtDb0+dNg==", "signatures": [{"sig": "MEUCIQDpUS6z43y/5Kjq2+zEkM3GMC6pmfW4RPKllINNQAlGcwIgHawBcfDkHV4DzG9N+G8YPWPdBsSiO3OKtK4ApB0pfKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6779}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "bf4d4c685423906300d4368795a0b1dd0462d5f8", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_5.3.0_1519981679160_0.5273916728314325", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "supports-color", "version": "5.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.4.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "1c6b337402c2137605efe19f10fec390f6faab54", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.4.0.tgz", "fileCount": 5, "integrity": "sha512-zjaXglF5nnWpsq470jSv6P9DwPvgLkuapYmfDm3JWOm0vkNTVF2tI4UrN2r6jH1qM/uc/WtxYY1hYoA2dOKj5w==", "signatures": [{"sig": "MEUCIF7q7yOoK02RdHGiqdquXSl+5tn+lXz4yKxNcwgkUbZiAiEAiFF/Lx2VtTLC+gVpQKrvK6qBE19TdKPzTcYn2C/mkFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1XC3CRA9TVsSAnZWagAAKqEP/1b5ncwgky8BfhFodI/k\nxxuQA/s18s+3RUyXu1KSyFbn+6wbyprD0mlsATEhrc8ILe23ieTpKV9xsy7U\nUjkhV7TW0ibf1ndM8Xr2Y9VqG/FV+ZDcn/sJC1OS+v6cmVRXsMuwUNHzHPmC\nj4dGMR9auksDBY6BMIz/vLIf/nKzKUC84v+Wc2AvWSjx7eii/j2txPlW7eIt\nPxfUinegPSzxVx15ijd7rP7mckXqjAfXHoEiaoJkrfeDZZdwLnJKSgDlGn1B\nQyKxo32z3XBDyf3zIBhrNfEa4yagX1zDLq9q2Kjnt1mWAxAXbScBuMx7NXfW\nZ8IriNLHNnfP+X3lK0uhd8K6nHMX6Vtcl63Ib1jTLXCo+nvotGdydYymsL6I\nqD2HEatdn7bCCBO9rImSOXWIfvVImPP2KIt7n4rnckerpWgMwnS1zeTqfzrm\nUWTAOUgkmrKZEoKdP4GHakdJflUjCFf5bd3pJFgIA2KiSUSYMOP1YPziWLpE\nhq192CiFHEDFmuAjXFqDxH8ewbis8qBhp9P1hg9x5oV/pPABVnsuAFcEEGhR\nlLiUMxTL5sO4ya1ARHtPecOj+Br86kyn5O9TG4iQYzxMQufn/Iyi0La7xs0q\nRHtaokepsuCcZOO8zG5GRVV/WKClK9ePLcjOiYOgyc8OI065zzLT43amvCAZ\n+M/X\r\n=nrD9\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "bcd82c998094535f3462a3e37856852938ccbb37", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_5.4.0_1523937461670_0.5515855155919032", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "supports-color", "version": "5.5.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@5.5.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "e2e69a44ac8772f78a1ec0b35b689df6530efc8f", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "fileCount": 5, "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "signatures": [{"sig": "MEQCIGzpj3WsadR8id5rtQpY1DMlNtw0d2Fh46gPrMaSXZjsAiAdYYPi1u5WZPJz8v4wv63caDr8kfuNj6AXAr6hDdAMzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbekWRCRA9TVsSAnZWagAAA/4QAIlKjuAwiz88EoU76LHY\nEWW+03GolP6yep0Yh9JS7mY6cd6wWooKZ3EmgqbWDmSaWMF0mveNOuh0uzKD\nQF/9rkiyf7z5b6Lh9r7IOJTA/BwfbGPCRzI2yX4G7am7hQ98U7BlPsOZRjo/\n5sbk5YSe5yVn4chc4I5FLH5n5rc+wx+KLq+T3xtAh1ze1+LcjlBcanUpE9ba\nJ/56gSqiM1fZrxmR31KkB4jhlGkUSwOWsycb7AwSORvnAt0E6dkvMj39WZro\nb0YhT5g0z8drvsKgs/pO99CdL1yjjKHhSwieCXSaew12DHHWy/YT445QbCXQ\n/yWBgtL6FkvUvvhs7J9sOwHoBIFTS4zhsn1+mxIi82etWCa+G2uHecJ3zR6x\nCAW4oqLRrwdJFz8CYf34cqaQ9fnkMal4XzDZ/G6FiQmhtUEFfFMq1pILMaq1\nVWqaBjuP3m8zmU2qOURK0aOJN/x3ONfgDm5hH5vukJsMALT5yQRuNslqYWwv\nuB8zQ8OH6+S534IvlbWaQxY03M4Ri/X8ohQDNdjHMhGngAKSyCGCXgbA3Nr9\nAv+8NCfrAEMIJ57QoiPpTsu9BzOUJjtZdmwtpcSHZoZjVdiir9Bc4pl2P/yY\nVP3wy1MGqTLzWICZCbIbwHdASMMGvTkCxw27KZp9bOqHU7ZeFWu9lTfq9/Ex\nq5Yz\r\n=1Pto\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "browser.js"], "browser": "browser.js", "engines": {"node": ">=4"}, "gitHead": "7759fc135b1be07cb7411178d7b1ac33d367fec8", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.20.0", "ava": "^0.25.0", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_5.5.0_1534739857211_0.09573189686372618", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "supports-color", "version": "6.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@6.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "76cfe742cf1f41bb9b1c29ad03068c05b4c0e40a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-on9Kwidc1IUQo+bQdhi8+Tijpo0e1SS6RoGo2guUwn5vdaxw8RXOF9Vb2ws+ihWOmh4JnCJOvaziZWP1VABaLg==", "signatures": [{"sig": "MEYCIQCnubIYxe2p/PJMmypElhxQT6Mee7X3RcZ8ee2nAMQA+wIhAIn1zwbdRJpYRl+r1jFqYyCs9srNOy/Xllf6Ug+gtIsG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHPVCCRA9TVsSAnZWagAA0iYP/jtygc7aPCW/CFObnk2s\nWbvfIWa+uirogBj74mmv1inySOueGaZNv+TuVOJzpO9sXbwuJPRBmyUaL+e7\nhWvf0ZNU+C/fSFLhU1woCfnXUpxSH7ymnOxkXk0CB9bwU0ltrYwldpvyrx2m\nUtpJHiIhNLCaUyonLYpK1aMveq6BGoA67BQBXyxfUDpcqnHqDzCwxUumEXBi\nkE6vOT0YX6udGodyImbyXeWTFUmq8VG1omoUYE+E9KnLzcF8xGUGCcy2Gavp\ndkAWjUiD4wW0klQhigPaJ1LhOjPRmMeVPivxOs40LKCsxxhU5q8wKULg65V4\nieg/3zp5Q5zMxda8grWyELNdxwI/dxAXcB7+TKDfOnRv8lO8JZkDw0XSeaUV\nCITOBYq+70yhsSd+4s/A63teYeYMXUgEkJajisPHgt8RAEz015Aodc55bfNN\n1RDaK6pUx6LNWFbHJevJsbUoyCp09yBL3AXmmQXplGTGe9TE1g7c7+HiSkrw\naV4cZBu3scePDvsgBM/yAuIOLUOd+Yeha72RvLd+xpAH/JdD2I5JnJggV4/m\niTD4chTW0ZXVUwVa0yfV0j841czdfvSNBNHDWzBAfXQEMWZqdD1D0lluq4IJ\nj6APpQMhr0iBuLNcXB2zfsvik8KVoZGjqbFQlh9hSY8lDHwu2DxNFkUHv+p6\nEHPF\r\n=PCpE\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "bfbe6692d549b423230292946756f3fdd79a808e", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_6.0.0_1545401665913_0.7646690775984246", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "supports-color", "version": "6.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@6.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "0764abc69c63d5ac842dd4867e8d025e880df8f3", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-6.1.0.tgz", "fileCount": 5, "integrity": "sha512-qe1jfm1Mg7Nq/NSh6XE24gPXROEVsWHxC1LIx//XNlD9iw7YZQGjZNjYN7xGaEG6iKdA8EtNFW6R0gjnVXp+wQ==", "signatures": [{"sig": "MEUCICBHgLX/Pa0Y1oTiapItURavnjwQCt5nFrTLlP0VHleGAiEAiUrcMuzw9vI2Q3/JNNe3Vpc9mnnYG2BFreBdhYtvLcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOEEuCRA9TVsSAnZWagAA+HgQAJycJqxz6hEUHBSd+sVm\n+DemsSdlzfotf28GEXXz1/Y2lBwj/qrLCJ+qNrsWIEEmdBeXqbnfsQvcEnES\ntbeMtoePtfR18GLPWYWfYSdATrHP1NNchc1ff08pScNod4Pc8N+6jN9isNWU\n3KgtbIhzl0s2pVp0onK6nBtj/BRhb27jSrIt4Ki8lyw/CzT5p76PUoS/28zY\n2aJl2GDDZrv777sfgwIYQmKjFHfXmgEcD8HQKYhmidMj5uW40gEJf87KjsnF\nSjeWjPJjOwboOovy371OtC+0uRd3lVEb0RakdkjQbdJ70fWNzEo2UMYp0AyY\nsQfgCcYzw7eYtvfYvnAMj9N5McPDlbM7iohDWWgpkNslqZ/O+SUBuBAAvHx+\nmD1HJ9dSM000ZuBOw8J1xmJn/E4UPkpMGXDRzKusqe2ZtyOdHDYsm9CjY/IH\n+KBtWHlHw5UKOllX+/yDGU4x5h14KGjtIQNizJAhUXX+pmBnN0Ez7PLiJnr6\n/hTe+1ksTwX88J65mJV4QYFMcImHrqhnR4+gmtNQJAjZk8YIs6akCDqcZPnu\nKGnfh8vr7XneKjk+pZ7UxdBXtmaRnYc1gDrpJJ/T/Cf1AO3l+DZajg7UL4nc\nC3uYXDCPZzAsD90brkkTwOx/q0+5UkWbuDxM3VO9oVLFmDXbzBnmD/UC8aEm\nNaJ1\r\n=sufq\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=6"}, "gitHead": "8c160ebbf4c8fb3c244295b17972b6a1bf80425f", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"has-flag": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0", "import-fresh": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_6.1.0_1547190573796_0.9365106270641614", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "supports-color", "version": "7.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@7.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "f2392c50ab35bb3cae7beebf24d254a19f880c06", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-WRt32iTpYEZWYOpcetGm0NPeSvaebccx7hhS/5M6sAiqnhedtFCHFxkjzZlJvFNCPowiKSFGiZk5USQDFy83vQ==", "signatures": [{"sig": "MEUCIQCAJlRvtrtSsn647Jt2YEWnkix5rsQr5PEFO2DYb1M0lQIgJ6+Kfr8uxteil8RI05iQ8+s2TsAQ+FjR1nOz5TanFbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/+jSCRA9TVsSAnZWagAAf60P/1gxnPQ/uIt4BdrhUjg8\nl8urtW9eDYSf7eD3Yc/gOjO/bUMY2qjzPkkzzGw2QtOx5LHOusDuL92SQ1Hk\njKodfr8Gumhz23snkMZey4f/lZoJunf9sFeBLyjqK3IzaPc46RcZvGjNXKUM\nksXjq/nXUqLrwTkejNAz30J75hKqMICaVaWRbWRW0X8tNM4/omCm0MV0lRCt\njBG95qQSvZtMEkg9sWG6pO1YmPqoi6+bbz4y6XRtVTT5q40EFAgu1qirZ9j5\nPt83PxXlRn6B+n2ub2uPK1lInlJwUPAy6XLAcGpBl1+CnkHzl4AWOBUpwvXl\nMYK41KvS96CbMgXmDK56Wf/wZqRaUZThkwcHvqlufTnFXkCigcYbL+Q3mO6G\ndst9pcP3yelEH/apI7AU7QBEByi1jmqNEr1WNOTpQtuKsEw+B7G8HBzyExVW\nCFItKnYSmxXyS2X/Khlk5dMABgKrWa1PtNv3BdH0AhjjLAgeLzqIDxWWMKvo\nrKa5g8tC0B58PMnm8p+XtqgtaQ5bWzbitx5LwASywWctKDlH+u78RZOgS7By\nSUj9vOiV/BXgyTsHnDhx/ngn8Kum8gHEj+jxO/WC3XDaRkAIdslkMwgTy4u2\nCjC4TXzegK6zt/DnW3qGqNhd4QbsYxPIcJKVsI+h92hj5/9mlADheT8gF4Hq\nXlzt\r\n=j1u3\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "a4140d78478abca47a2015e300d283ea3d1e75ae", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "import-fresh": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_7.0.0_1560275153919_0.12974547935836633", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "supports-color", "version": "7.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@7.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "68e32591df73e25ad1c4b49108a2ec507962bfd1", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-7.1.0.tgz", "fileCount": 5, "integrity": "sha512-oRSIpR8pxT1Wr2FquTNnGet79b3BWljqOuoW/h4oBhxJ/HUbX5nX6JSruTkvXDCFMwDPvsaTTbvMLKZWSy0R5g==", "signatures": [{"sig": "MEUCIC4bu1wOfTRc5G+Klco1dQhZ9d2kKCNPw5rafa7nSHtwAiEA0XcSjGHMHIFtgDrLXmGr45U1CY0O2ojxZkg8HZZSx3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjYrnCRA9TVsSAnZWagAAC1IQAJ8tZscYZ7RIeLC+u0D5\nDAsmAuBW3C3ze3zQ/1U0esnpvyNubRA5AP3VSUSCTf58TT1Y3rVLF6PiSnIt\n1DmPaEwrNQyrN7GQpuJZ5SUlsO1BIANNyAL3q4XpOA4TMFa6ttF/84e9Vter\n9s2lVRtEu3Qh2/gIfodPFtNqrIGaah91W3FKueMjRv+E5KwmbqD9/aRmg4Af\nB8A1ZxDqeTN88ymFDf7X3+JeE5DFkaCf9RnYL5k1wWSfWPcr0tckrLdGR+xp\n7PKAtXCcVwln5dhR4nlr/iVfEGGoZnXKShmN9v5DDHVt2Y71qViMmNQG924A\nfUmDlsPOeNmiFlcyM5KcV5IzO1OGosrdd+VKyjJNfr+eZAa89WflYktkchbl\nT7rI3EE8f5c73+KTMleUJVAnWy/YTlhPIwFVwJVKRtiP2GZYK0TPhxm8MA/R\nYK9zUCZDn+VVT1u7XJl3Iecl6QRcBFpMV4SoGaD9NF7ZxGvJ6o3wXgpadTMK\n8Qlho5DUozKWiaN4VcZyxSHhZoU4l76lK8DfenJGO0CLslqeM43F6TqLobAq\nSiMlUNtqeLDqTQCjCn8xKWJrZkrOGHzM9XR7x3Wtyb+Z2wZ/zDd23EOpl3Rv\nYypdfVR0ubdYL/FKDbn5TJxrzgIV+FvL6RdSkKY2kXdeuZGELVm6w1V15ZDY\niNnX\r\n=0um5\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "8a40054cdbcd3f42b4f68eaefb41c3064835b991", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "import-fresh": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_7.1.0_1569557222639_0.7307909884215651", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "supports-color", "version": "7.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@7.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "1b7dcdcb32b8138801b3e478ba6a51caa89648da", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "fileCount": 5, "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "signatures": [{"sig": "MEQCIE0VQZExbPCN/unr9MvTQXP8sE2Dc3/XaR75ek8UW/PdAiBAcA235EKWMIT6eyE5i6OIMFXIX21HdO+KikcpSHdOdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSOfPCRA9TVsSAnZWagAA1JEQAKKOOmIj0Pn5/XIGvNlg\nBR4gMIGAAlPuSAJAA7lZQukqIbZxtkepHkTYc0hOR8VIW0R8kr9fuHjlqv5n\n/xt6fGCvmOfzty0wcuuudsxtp710zt3XDKKoTTyiFZxowVldF/9B6hGuagXU\nWV96BUo9o72dfQB/70q9ulPAYqqtotOkO0WhP5/Nom0hZ9htDHTRkDkHnUsF\nAaxNAxGxOORNj2yo/KYjKf9H9g18YJcybhk65MWOA4w5M8H6gAb/qbEo0wTI\nxmvtuZClK8OrIEqbkC2y7W8EGS4Tq0vxCe0z8xaT44sm169+8SwMI4NPEz+B\ntCsKmFHtidQ5Gb3svu508fuaBT0Hjspfd3FA7VfiwLa9/pP7I071+KsXxv9W\nkaMIF0PL0LCx6vlwH15IkRiX1ESJcuF39zKMxRV9W2i9x65LRHU0d4G4r4ND\nBv2j2EvbeE0A2Ec83QLN/YCL+mFxrQsRj4cZtzAtzczk2qHSPD8R4mvaVLAQ\nzzxzJPZ6baYBCXsXhNy/dWLwN+C3DnMZiQPJQHirlNMsfNO0ZNtVEEU/E/VU\nMzzoJ97fZx+tkMB3atmxhM3Kcy6yM0EYqlrsQfR4WLmnD0QXogaQBWUjQpDW\ns/x0bBz693U6LoeLaZtQ0XebdwtRnEmLpY2eu0b4mgDYaJPrlmaQH3SNlor6\nwx3o\r\n=bmle\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=8"}, "gitHead": "c5edf46896d1fc1826cb1183a60d61eecb65d749", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "import-fresh": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_7.2.0_1598613454691_0.19388472696485715", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "supports-color", "version": "8.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "51fc88b5f0b8c47a64fcc54941f41a0ff9e695e3", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-of+3NN2wzrj+nTYWrTf4Oc13Lo5gDLVBzKREX8DW1e3IWw8znzOc03NaQwnma27qO3nKPDA8C0HN50SS0Pki7g==", "signatures": [{"sig": "MEYCIQDgy1e7zox9eQ1sSd9IB69DTLo4wnotjn5lU48ggKMVgQIhANBdxWcjd75ghutiVYEYdJsIB+f9OZpi+uR+uGoVPyn1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvMUiCRA9TVsSAnZWagAAAYUP+gOA6Wdt9SfL040SEV1n\nsdEEuYdoaJ6kIXPgZfyKbUFTtGJsk/0yzKijrU3mtkfiTumtUErEaLcjwoIy\ny7SrWpNcfJOrEiW2fWtLdWSuwQ8nfngkiiRaFL8D9UzXa6RyKWLdr/mjbCPK\nPM/O79xhgkQQ7FYhefw32UdmX7n4YJzd/lw8J+V+Qi/roYWmslgueF2B2G6F\nws4E1P1J+BLBpZ2iTBGVYROKoBSu6Uy763abUGRUKRx8me9hwX9ScDdZHaSX\ndcPBxgFQXJvWoBfl24kZ1UrZoSPhQc22kHeJd2sIlaIOtYYvCbrOvXAy3849\nJ/ox3iuTqNmrot/W/s2rXoCDqVWDPw0/yQ8VqgMAYPzKEotXKLFCWvyMroXZ\nU5QkaFt7zo+qOV0jBJsensk8tOAMeYg3yyAtX/659y7yBDmQkDEXfRaLoexQ\nJlD1LHbaFdjMuhpg2B6ekXATEiJuLLwuvVn8YuctzGyVxUNbp9OoH3dWqzmL\nPz8tTH0pQhvt8gpY6bCugHPIJFMpzsXwClpChSbpOtVFQOeM2AdZ8MAmI+vK\n3kBZOY4avzQ63YRyu5d/Tf1q3cZaxT3/i4J/7AIPGY+0i3PGs87jhxq+N344\nME7Odwiz78n/rn2MUBBwx7Qy/LrwSxLpTE4Nd5gF341b6PDttcGbu3VAYb26\nuh0r\r\n=6dN0\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=10"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "b163c2c2615b69da396690f9c6586c0a2dc44511", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "import-fresh": "^3.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_8.0.0_1606206754093_0.7391455025505089", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "supports-color", "version": "8.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@8.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "7f8e6d289ded5a27b7f1b76f6a68cf7ad48cdf0e", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.0.tgz", "fileCount": 5, "integrity": "sha512-7McmmMM5pLe5fDX7vzhZB1dv4a3ZS0POhSoiNINQ/xSonu3oBWxAstLrtgj/rUq0pIGo3AU0ZhLUxy5u20EamA==", "signatures": [{"sig": "MEUCICEeXbqakra+JOEO06zqsd/Qo7OHsI9HXg16w2s2PqOkAiEAvMVh9de38V4bIq8Ld6t1+Q6hDYqR7IaeBKWjDAxRjlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf14/TCRA9TVsSAnZWagAAFAUQAJfgW8gD00rYnBAB28BA\nHnFHrvSrcksIidykJen1I2u9FUJ81opZnKM9OrLUvVRt37+M65HtD2G1hqYu\nGQhwX+dC+quNwOS2eZbWRaF1cbc40esVh4jVJtGIiqi6MEilKERhKCshyJUu\nVfRTm+vKwQqY6n2eqiFidIasA7lZqikw9iP2KwPGNB1AsUk/09H+6oATP9B9\nAf0P3JUl5TF9KQhLtcZfzlw5TjyrfCJxnQq05GPlg1odhDSRBGux9E5tH5vw\nX9fjO979wx3q+bum/W5PSyk/O42h+DeE0kEwiYaP3Rj/IcSbdDrpVR4liFp/\neFNH7qI62wAIYZhlUIWy3r+MpZTCa21bJ1Gbi0+lJsYhNNyRV5J+RHDAiId+\n94ulVg/RIkAGAia7+ZpjTedn5JjqNVTJv2cISDvux1amkakwNGuoMCqol+Oe\nQ/6s3nIJG8oCrCkFkBkGjIaAYqH8YWsjhfiKS6bhP1TIhsIFtszsIigO9z1f\nwTtISByNmtm30O06eGJYpdeekb67wesDnoxJPKygUfOsudNcJctDs5VGtIDO\nzHtNxZLMyQSkLHGUmHgNWu317towoIzX2NN7dBlCvVBHQjkrRZLTo2RP0TeC\ngZrV4flkdxUiZj+AB0/wgFrdIae1yHwMHbq8Fso82UGTMaYuE1znxkPg8FoI\nG6Bm\r\n=sRqg\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=10"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "760ce24f5c087ac14e39c72974c2b7f5d633f48f", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "import-fresh": "^3.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_8.1.0_1607962579102_0.6544583456572908", "host": "s3://npm-registry-packages"}}, "8.1.1": {"name": "supports-color", "version": "8.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@8.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "cd6fc17e28500cff56c1b86c0a7fd4a54a73005c", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "fileCount": 5, "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "signatures": [{"sig": "MEUCIQD3Pt4+oWonkLXTT2YYngrvYIV1ni/3S2qOy6HORKgxpgIgCSrOg3iT7r8k1lwSF6wd239nRXQnAiZtvJa+QDI77lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgC+qgCRA9TVsSAnZWagAA4JEP/16PzgByFE3QIONeNVz1\nMTjt46ilYO6OvdBh3t7tPxRid6muWjtp0M9KxA0s/9n9QmILw8rEQGQ5vSsS\nBdkcAqYk6g/99zftrq3e5uiTqttWdUDD4JYJPeGx/pQmP9Z21tQrFah61los\nhboH3MZ/rNdQviECcWhHY6ni+F2G3Fm/qoRfLxvTVgyEth+W7IaID4RWDBVl\nfam+kHU5tt0B6tdYDJL+Lyj/iU88xWrLM6yXiPICIxGcYh+YzQKJXlPMtJSf\n7GsKlzK5/MwNmpLgyv5FfcDGd1mAMGWRQZnzf/1kSuRiZrCBwhICdjuenSYY\nnP1bNySE6LxgT4GhJEH/+uaESyoO/aeqefb41pNPHIhe+C/k2WxnTpLIovWn\n3iIWZAzVyxcoPT8K1+D6Ih6Lq5DWYYiOjmdy3B5NlzCEdSdmjYWey7ygelKZ\nZXiZqc4M6RG7Ewv1LnJFCVhAY5FvSY1sTXFMnYBwdx9UaQxfZuJM0L7TH+e1\nBDAnPUhE+h3kSWcmGizUlAAlKt9HRs/ExhIeXsykJ49GvTo0E/IMVax+7Ua6\nBGIOkBOGwXjBjNAI8EJtfSCz70dhmSd1ZSSsXHV3WLXCEY6fdWZZKhhEfCVn\nWagovEiNaQDe2j3K7CkkgeyDTQREOYwbstMESXFihZ51MqHDk5RvjH7YSlxA\n6nps\r\n=n9F1\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": "browser.js", "engines": {"node": ">=10"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "****************************************", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"has-flag": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "import-fresh": "^3.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_8.1.1_1611393696226_0.3763411060511064", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "supports-color", "version": "9.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "201a80bcefe03598cd2439f7a2ab9a855ab15570", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.0.0.tgz", "fileCount": 5, "integrity": "sha512-8E5dgZd3FsJaYM+TEbKnwX4VWsP+0gDDsI2CKZQidScJv2QDbt0K8ZM6bHP1nq/pUrH6Nm/ZQvBFdvFVLRuuQQ==", "signatures": [{"sig": "MEUCIDjGbLYeID1nDYSGSvmodjj5xpQ6znzu1JMAQ75HGxLYAiEA9hD84Quyj0gIWKaK4i0nmAD9ibGbmbqDQR4qWGKu45g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8815, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeUeqCRA9TVsSAnZWagAALzIP+QHuofGBv5d5gg6o9MOs\nl4/ti94n0foDc0WoEfBv2n7t/BkHOHQ6I+ZCKtu0feOBkHr4nUKDwtV4wD3+\nUnlahp/AVSdiYm6g71R7yIpLaIgwOubvpVbMv92bHWoSeiTNquAXJzrYxMEr\n6ixoN9z74HBgAfxFguijOufvWmU9tW4spDRpA+QileYwCxX2eepADfx+3SAY\ndLMISmPim6wFXr4hxd3NrFE02jD6DDt1YHKnbNZD3eX2ANijqhgYRLuXzLqL\nNs3S9iA0j3k67FtqWpuP0Qi/YQdoma6Qn/xr5g6dRuyevX2N9iqlnH7hav6X\njTzHuhg0zf2noLp7HMZqtkujEbyJ/S7Rsbk/0YA6eaas5Qr6Scc5/ab/gJAJ\n0FYc3RGzH0drmTrbgiieA6UMQ8uMmvil3PUmClfLiINP2sJxsAP74oVSlOjq\nVWXZLIQbmZ6KMnT9TeLA4hpzlBdMBNcFO0dSSkHBJuQk1Sdq+8skobe/YlwU\nhCz18L7Ui+pi1Vj3q8zS3iJMIQYyoUYlOD2OFqKIiC6kDzWJmzvEXIY/JYZR\n3DmyettTPACAZnEaadiwYYQUXXk7yl+Zea8hJWs1WnOxeXL297XlzyInm6yq\nxKTupzblH/gz9vTnOKciZ1rBRoVGzArDx33Weke45xhY5MCXqqsKa5Pk1TL/\nmT3H\r\n=MS6I\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "42ea2e023331227c1e426e836bf34656abf61d7e", "scripts": {"test": "xo", "//test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"has-flag": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.0.0_1618560938406_0.2886363554639708", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "supports-color", "version": "9.0.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "bcc43ff901cb8088d680144ac0b6553d242abd26", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.0.1.tgz", "fileCount": 5, "integrity": "sha512-e8HnXOAd61fVNyYRcKoqGNpnpceN/+IbDlWCBVjeqfASq/kbH4JwNj1Y4TFrve+w838Ff4eupPKXtY03zhCBKQ==", "signatures": [{"sig": "MEQCIHBGBly14kKVPlQLtnQ3RDXkdQZFXsP0JlFAoZ2AtK6DAiB85M2t5utW2FDeJ72Ypz2ntuS7ULJL4EvtUQRbO92BaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsjw8CRA9TVsSAnZWagAA7jcP/imxPSRBga9Gv8pIFnd/\nazE+HGs9OopzmFWIk/EXM5vLvEqnTvtHRueOxOq0sqMZFQP6Bxp7/dMyJtpV\net1YWMZGNOnQewBH84hsPMmaJfxZrQBKYyeioCifxCHtBMfcTwwsvHuHo+hh\nOBE5Y8dcHWLZ2hheS2M3+88uQVyoELNxrpqZxLLUPJaDNvoxcnmj8eqRasoh\nVFmk06YVehO7wU5S6hevOOqDe/supx286auRpvcjvdldooi44gD/rnJJQ+WW\nwl0AT0kfiLs2TymoG++rkzWIQkXr+ElchQZs9jEYJip8fy2FsZA9a/lX1KbR\ntOzEf33qC2iNCSkOovvAqnLyVu7QZKg22/P6taS+mtHSDPYjZZP4flZmrpwl\ng9QBQKnqhGYsC0FKhgCdTSU+dnKDg4fnj/ud2lBWQfg6CdH/FS3u+paTV9p6\nhF5RU9RGVZ8YytSWEbtZdtLUk3bpLxLqScPzocRuR2fLiP4osMugNUj+Yu7v\nFcbkp7l7pnEuzi6K/4UeUfPbkoW5EA08jUzdSrRxrbF/KZ5GDIfOz48ujyZj\ncuiLks5IQBe0W2gSlW2zCmI2s4CJIPISFMSP9ydM/9zawt2lxkxKcIp4zvEp\n3WX9i4Ys8yyCfhpc3JG1iMn89C0rMOQVQuWowyH73mbd4KKeHBnwNxQ3XAxy\n/Vsz\r\n=U9k+\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "9cc1842f4f7a114099a872eba4437624481b9bcd", "scripts": {"test": "xo", "//test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"has-flag": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.0.1_1622293563820_0.5912679722726126", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "supports-color", "version": "9.0.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "50f082888e4b0a4e2ccd2d0b4f9ef4efcd332485", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.0.2.tgz", "fileCount": 5, "integrity": "sha512-ii6tc8ImGFrgMPYq7RVAMKkhPo9vk8uA+D3oKbJq/3Pk2YSMv1+9dUAesa9UxMbxBTvxwKTQffBahNVNxEvM8Q==", "signatures": [{"sig": "MEYCIQCmc8nW2zzcuOr0l3CubjPY3WH1Z8JEGBt5YwGkkcuijQIhANpQ6EnGSPP0dXTLo4i6QisEzGWws2vQW+JNjIXQQ/b0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9YXBCRA9TVsSAnZWagAAX90P/1XSdHWe6usL2fp9JbWy\nwpcuEDiYZ01/QdA5X3o+BcYyNvd06ZuRfHOPzeIIMjCtlegynUQNDdBrkT50\n1VqF9QZEIbh9aRizPnV9rwJVR6N+rf0lwJ0ghS82v2UPehKhJ58VaprBpAB9\nf2XNZZA5u5NJ5ack33TxAFJvm4rDDLRJSIy72eR2REpxEAFu29GXbhiRK+Hk\nynjghN2Z0iLIP0oPJkD0Z/hfqxkbjWSJtSA8r9227owMwybdzAhccTp5Cvxb\nx41Qyrmjk/WoA6gcZ/ntoiPgaWGFP1JxSL5cGX7d9oUD6D8ihMrvp3vpxxIy\nAAoggHJfvob2ZIGCBbh6gpCPQIhv6mbHncxKi5MNoolhLtdZvaCYFAMmuD59\nEpIswX9RkbwhwUOtwm9mHuHWGKpOvsuOKNcqf2+b0CMdI6sxBdS610KirrOd\n6W84JTFOumz1tAB2HPFG/RIiMV1co7N1939nNv04Cfr4xS0uNDbu7bAdWvgK\nGzlv+hguYwarw43jlTy5DGtQ2B8LbRw2+ZnWzhMaGDXmmqpbqH0Mm1jAEoV0\nYVBv59QEAI4GKx77vx1kaM58noo3e7H9m5t8JvsvLzgr1c/0FYTX9TL8B9J0\nKYz+2KhUyhdmrK4EvuSwFJ9wT4/X7EcWzujk9sNFpYa37jETYbqWW5L5MrMu\ntEP1\r\n=Zn+X\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "d247f9d96b3d49e303deb277ff2206b47ff6b370", "scripts": {"test": "xo", "//test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"has-flag": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.0.2_1626703297109_0.8805003712177977", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "supports-color", "version": "9.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "558963681dafeff41ed68220488cbf438d29f351", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.1.0.tgz", "fileCount": 6, "integrity": "sha512-lOCGOTmBSN54zKAoPWhHkjoqVQ0MqgzPE5iirtoSixhr0ZieR/6l7WZ32V53cvy9+1qghFnIk7k52p991lKd6g==", "signatures": [{"sig": "MEUCIQCjIGNyuZL2QyBljs/4FwCRuzL5z1IZdbVdSoHf4gM7lAIgHRrkK4VQFOpZon/wIoeOtRMdjAG47+I0v/r71e2ol9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlpW2CRA9TVsSAnZWagAAAAQP/1bDFTsYEUAYpAKdBMsy\nFA6Rg42JQGgAljypDklEFOnoM7gdmIKeNca/9FW709/dua8+vT80c0cwhgG7\nEpyPQvY3XSUPA3JJKNBH5m3x6bVBt952LjI/Y7LgqzLEhZkMfQYfEbOb5b9r\n20cmINOXNk023IyFEIq4KvqTrvPaqX+eV4nqGjwIsfppJRX8oka+ojZwMTBc\nj0k2nfRKrvXsAXYPwJTDwwcjm0dQtVM3vIc+Vy+o2MGK+FS3uVw4ut3/I8VB\nh0ioH6L42ft9OnBFt+dgwKkOFz3/ge5Asy9aQK7UZjk5JWWj89V0EIi8CP8H\nEfe3kbboxcdrjZSwovpIpZ+Sui0dtddNOUkDI6jQfFK1kIRwnxuuGRoVOQ3T\n+27dYvwcyHBYOrHNtWXacYXyivnyFsjOfEQhkNHqW31lCKS3Wwec1lEjdrGR\nIgQ6b7b5sp0Oe3aastew1Xbr/Tjd9oB9QwQlgeN66hpEz4U+skogUZ30hLQi\ntXa1M0BkXGqiHzE/d1xBbkJP5Vnnm5bO3jFfAwY7uhU9UtJTu2vs1Uz7Z/KW\ndDF8LXR/7UvNk846aw0I/4AceafGZ0IIh9+sVdSY1jIqEBY2c8Sxvl155sr8\n5ypBtPtyPwDf6y6zVE1EOZfF/AvZ+235AGbB3rT6p/d45/WRUINL9ufcNlVj\nULEU\r\n=i0t+\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "260829c927d68a624bb2dd51d9cda59985bca979", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.1.0_1637258677865_0.5103532076858013", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "supports-color", "version": "9.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "6b52184e8c10166bb004eff6c430112092ef30fa", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.2.0.tgz", "fileCount": 6, "integrity": "sha512-8Zmv7vr4lGiu+99zMvgY6+nxuwoph8zgGXbJy+jgYqCMGcQDFTm5pgNLe3WjHzHxzIML2ymnPeYWMs1t5zFBcw==", "signatures": [{"sig": "MEYCIQDAja4yaym2ED1N9Wp1qXtxaZo9YNCQYXZJqpBiPqPYAQIhAKTWTpyZwPibd00+wo/n4cuYaACdH28z68wlkZfQGSFP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoKVtCRA9TVsSAnZWagAALOUP/3dxR2PDUYHv2Duvue9P\nPCgNDW9KkbXlHBCLTzZFu2kpHcIaYsv9dDAKF/1V4seymGja20qjVTqjyZST\nN5FXBAfdzSZ5cfJL+NFmYMUtBHlTCC3nG/82W6+4NHNkgM3bLWYl4ttLd7pm\nJl43/ZP026Hvv7PwiG8DHNUoVh0sBKSg6d29ruYem5j0pCQVxaJg3a0lZmPB\nGs5iNrRtywUbDq/RVag8vcv5WRpb6+q0+GqXyTt6PFCxaVF+cSyIha7zXP3g\n0hGI13G8YsuO1X/yoGw0EGCZTzLsurUMV4l6E5H5Uj9YwHEZhBDwe63w161Q\nA36JN05vzKy0jazDKJrjYLtmEIa6t2i3DVWlhCWcIZczEBZPbmPj+M/gpxJZ\nuRowBF3eSbghdakMS7H2qErpZTMwfb4mvt8S60rvJttPfXzWYPiPzCRv2nfK\nS2T3FqZ9SgnrVRPnwhj4vPXYZGekpvuopwZgVm5PZpX5FY5zgI+B4y88TtMY\nGbdtdLOp+VIgQF2BqyJJcIkogK7jPNeEtf8sdBZ5RXU1CXV3jEQpIQHEp9y8\nD9mpXaRj20YAPoe+mpb2w92YfCSu1XNkF0638SzHdkrubxYrRkUbwGjN+l07\nCo7eqCl1ViBKOAANk13eJYTv9ZG4v2NlpSEdKOBoyib2Jv5z9NHJXV1hfYyV\nDXN2\r\n=wzX1\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "b20d768ca8d2b91f74c5110cb1168fea5b5b1faa", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.2.0_1637918061848_0.10822172638383698", "host": "s3://npm-registry-packages"}}, "9.2.1": {"name": "supports-color", "version": "9.2.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "599dc9d45acf74c6176e0d880bab1d7d718fe891", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.2.1.tgz", "fileCount": 7, "integrity": "sha512-Obv7ycoCTG51N7y175StI9BlAXrmgZrFhZOb0/PyjHBher/NmsdBgbbQ1Inhq+gIhz6+7Gb+jWF2Vqi7Mf1xnQ==", "signatures": [{"sig": "MEUCIQCo1I24IAWb0ga0i6oB6n88+fJayXwELlsWWCx71ywvsAIgC1mKDTY/GXoNG2FUzp0Yq5++pANI3DR5YXA5E2h7L/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoKnlCRA9TVsSAnZWagAARwgQAJxm06jt+zY9JZVIwEDS\n0nZXRuTD+KT1QQ4Gy7AFPBbplGPPfIyJyc1jxS1jI9HpjY4Yat+ALWhaNmQ3\nzCp6RR5Eq3qdcESOiAMDY+l3qsZozS8dZTbSWipJ41frJ1dIEuV5QM2KKVNx\n7KhCFFdDqgRaZEM4ZF5ENOnz896Eg6PG2FTTqMC9BmHY/Mwz/7xdQuQ37TV9\nSsGtQ9Pfe3j160vz+zKzF1q/maFaVxb18teyNCGsE/P1woJOhoMcowcxJDiZ\nkaqh67qyB0yFhBAPWy72ERnRjBWgWS+ikS7cpNETkOJUl9fXc8Kae0RbBOph\nv/3f2nl3hj+AvJ1v5Yv8kBJueU1ZZX09EvD6JnasG9iUEodwiOAOJbFm/m0z\nNMaazi89VJIG0yzTgnwsUyPde0Xjxb9sqiXdg6GA8pZL4l8B90uC5xTxIGqS\nQURFld5jWKbYbmbXncMh7HuLWQAP37SzbOkEEkxDZhO14TNx36BlcxsFZhTh\nT6VRvChYkKZbKpKjwv0t8rXbdAGlZyBFRF9CQZU67sTgxjwwUdUxodJFwMVQ\nijndBpTDMUwmmNkaFBNFCusAyz4smTz5QbnEEMhEgnBxF9wMRZaa0KwXfdvg\nW5n/vsrzXV3ksRAS3g0X95Ea+qfKS5Bpv8ZmP2eP3pS6YnJ0rpEri8uZFZw6\n2X+G\r\n=GLOV\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "4f459ee311817cfad1332731a2c1060e07b2015e", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.2.1_1637919205211_0.7868146192318419", "host": "s3://npm-registry-packages"}}, "9.2.2": {"name": "supports-color", "version": "9.2.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "502acaf82f2b7ee78eb7c83dcac0f89694e5a7bb", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.2.2.tgz", "fileCount": 7, "integrity": "sha512-XC6g/Kgux+rJXmwokjm9ECpD6k/smUoS5LKlUCcsYr4IY3rW0XyAympon2RmxGrlnZURMpg5T18gWDP9CsHXFA==", "signatures": [{"sig": "MEUCIQCr91jlwGXUgKhu6aJ2waIHJe2Uboo42dZykB6tufBmdQIgQDObiCZb46MV87uT2nLMa5U9F145Q8Z7k+du5qtBaR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQZF1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYtw//TRrBp8x3sL+chVOZLvzL2mWiV0FDp12vVk6g3PYi1O9a4VvN\r\nxDRTcjwlP4UnBsWpqoQOOaCvPWfqBZqUcAcXYZ0OoUOqf/+n1OH8B3X/1Cen\r\nMhrf2e43qXMSzQNDgdICcYCFXCgpGzkpemjDPs2ZkDPvswDhNJlHR3ci/j1+\r\nIpiD8KFDr4zmaRRgaLY3RpsH0UxQfqbcBHZ4XZw/t7qjEP3oibQDDw73aMx2\r\n8t9U/HfCbquf9/26rVLd41oh330VMGIKjrrQEbqnpz3cKzkPfIbkwshEnyZd\r\n+Kott6embQWeArqBOQGPcKfGPwo219ImZVof7e5DLKqHKTzKOHw1+h7EH1+j\r\n9M8VcyEIIzI1pR/BctTXHUtnmc6mzI/D5MzqUw7+Mz+EodQUNVBYhEadIRUF\r\nTopch91SR8zFkY5cHR2ohRRDI6eAyT0jLw8R1QeJWEGXggeoj1iMtALJ2sLz\r\nH1RrMFk3Ccn92BP7d7KEWjsjZ/LFshJZBYHczOfPTJoakYjsEnEGryjAPlbV\r\n5MUWREpn/vva2yyIIl7mEzloNpXM1HaQO7FOcVoPsvUVg4GPiYgC81Xo5jC9\r\nS0C68hSrCfdDMksV+KDYz/AlSa3uDX/AoNfP3hOfnMpzH8+zzAO++ubKL8jU\r\nG1b5HEV1jI5bvCBnZO92qVgGBvtOIJIxVYc=\r\n=YHb6\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "ee88ebcfca649233bb35c9b0db226059883a77b8", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.2.2_1648464245832_0.022669141733941967", "host": "s3://npm-registry-packages"}}, "9.2.3": {"name": "supports-color", "version": "9.2.3", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.2.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "a6e2c97fc20c80abecd69e50aebe4783ff77d45a", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.2.3.tgz", "fileCount": 7, "integrity": "sha512-aszYUX/DVK/ed5rFLb/dDinVJrQjG/vmU433wtqVSD800rYsJNWxh2R3USV90aLSU+UsyQkbNeffVLzc6B6foA==", "signatures": [{"sig": "MEUCIQC/cxvtYP3Gr/+B9VJHyClZQ/29sp4eUkyXUGq5nYVFXAIgONSzP5Rd0D5lVozLtLDRrSCNzpHqvQzKX81K8rNsClI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10409, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGMX/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1Qg/+J1zz1fxbKKm5LREZlIBj3zlV/CWGGsCLfyZ7DMq7bMTlnm4Y\r\nwGw5hM1myrhZIqePWM27FyKQqrG3OJyEeznwRFyaCXka2/VPSsZVnct9O1VM\r\naruSF773oPCgHkO2NGUK+OTJhEUzH3lJv+ED1MCzPsyQG/9Xnrrze4gAMX2j\r\neQWz2x1k3piHkB1cK1uZoZzPql+N7ayjvmGy5D65iWBVWexotINd+tCRdkER\r\nia2Aurh07dUy2VzMi/k5MBH2J9cnL1dNj9AXWkuyH+PFzrfTVeRv3Qt5+YQD\r\nImJZ3Mp8O6AXlCNYb6SUM2V0ZgDrVq/K3kzlLWpeHkKupdP7xTot0DR5bz3W\r\ncbG7muw/Fzzo9R+atySPYUHJJK5cP0IbM5GPMyGlEZgZj47Pagi9sp70QqAH\r\nzwClEbF5J927tPquezDf9FS/jQt30QlanwJv/HSnoWjm9lVXKvcb5iYKoCzB\r\nY6gcsMhF5c+TIO4u4U/fUIVqAr3j09IQfh3keKMGVlIIwNr8CBYdNX+Uq9m0\r\nenYnSENPiiaOgNJHINewHpjPWSzvFsA2sUBezbnMB+1Dj546BRWTskxpS0Sk\r\ntkDqmT5lcPtATCSd8M2SvCalyK256P2mMoHYgrRLPHK0AX1+exIeTriIypRs\r\ntpcbQESTv+qnUpaiw+1kNzkffmP+QWBL0/o=\r\n=KwC3\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "600e5dc8dd973b49c3a712f9d334ca29dc03ce2f", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.2.3_1662567935306_0.17475207619620092", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "supports-color", "version": "9.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "cce566c5ad562eece4dfe5a4502a58efc54986a9", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.3.0.tgz", "fileCount": 7, "integrity": "sha512-hJ6RYjNbcBFkpHi+ykjar+7GgHs+65Kxyw940nBLOSjaWZ13acq4A5f+gWiV5w+xfsg5MbnjDxTigLcUuljerw==", "signatures": [{"sig": "MEYCIQCirSQL9BfUmwjr71lEVJU5lp2dEoS6nustrpnb4WWPuQIhAL+XH+GTSUZ908VLMPY5OZYC2dE7W6vkBwuVfi964EjA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkIpDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqM0g/9G8F6RW9DqNGvQ89RGR9ukXkGAUruIx+afnX4mkaOWnu3WBuC\r\n7yEeZgpr2MqCIDg0ZZx4g7D2Mi+89kpYw6q3Vu8X6iyZH/k5ge9l59sfsut8\r\nUwZZqJSrX8rUV2s9GDmfQkh9t2DIpk3j3QHZKdwcu/tF0jOAQLp5Xi4Fz4SD\r\nsKd7k0BxdRjlqRJwbUTg+MxSQE7Z+BQZfJ+hLDxdQ1VQQS1wBdeiDv9iJxqV\r\ndxPkiRXNEeIQJC3oLNao2/z0j8HL9Eo8+1xYHPmHvOqjjV6tDZEXwb6sMmcn\r\nIXajk395rcK7cdYw4QwdgiCliOiDn5Y3v96a4eknLm8mMznfxBkI6hmnTcle\r\nPKri0IME+3aKOURgADhYPLYkVUKBqaW9OlEpYsNjXw7ZRT+02yOjWEhxqFjT\r\n2ziOKEui341GQVkMUq1qgd0jEEKM1EyaixWuo2PFPX5rmmPxlV2oaMDBBYN+\r\nUTqoTpbiYPln9vkZ70wZ2Oed/jADQSeGLtzIqDL9mzZoRwe/cGdrUoAISaIC\r\nFTD0jsuT95Shv5nVZarfx2F3WCeH1yKvWr9oPfMn7ULEKcABGPSWeY1ts0jD\r\nVVIa6Pu1ZwS6Kp/G8BcHtNGCz2z8zhZGuy1EsshUDB2Zoka2Yj6nKTDB71g6\r\nhElDrwApaJa7T+vN3K9Z3cIgdObVr9PDG/Q=\r\n=6lM0\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "a02ef43a6af71ca20f597a56dd45978a67732bfb", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.21.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.3.0_1670416962766_0.6730221372598995", "host": "s3://npm-registry-packages"}}, "9.3.1": {"name": "supports-color", "version": "9.3.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "34e4ad3c71c9a39dae3254ecc46c9b74e89e15a6", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.3.1.tgz", "fileCount": 7, "integrity": "sha512-knBY82pjmnIzK3NifMo3RxEIRD9E0kIzV4BKcyTZ9+9kWgLMxd4PrsTSMoFQUabgRBbF8KOLRDCyKgNV+iK44Q==", "signatures": [{"sig": "MEUCIEC1VJuOjbR3EJxTieowsJF7/QlReOCkWePBmYNYLo0sAiEAq3eNwWu8pKnjJkVFZzbuzu8xTl23d/Py0+e1zahKAJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmwUEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4Zw/9HtukLZv+2XsN/m17UhW2/LBjQFiWGXKHHZwp8/6Zpr6ncHrH\r\nXdOVyWkQ/gKGcK7jnrNa6YaqZh8i8OlN54a6coOVNXTps8bmQOKB45g66Ibl\r\nsrHY22/xoRVXe5WN9LNN1SXKYdDI8QW1pGZh1s0kMYNXRq317iiEwY313+r2\r\n8xYGES9ln0SwrZMj8+PJUU3zxxI2GWxl1e/6BcgetBkHZWT1jTWkR8zFbptL\r\nv10o1KrXg49SRMim7fXFX17Q4tZJcf2brarootz6QXVLS5uZrlYJy7u50tGs\r\nl4rL5Zg6PS0mEZHtKFCz+GtRHz71Hl9p1vl8pCpWobjMqXr2VSWtbBJEPp/z\r\nhbiRSNfcZrH9M1EQNlN6NgTAt+P47LL4z44BaKbmZNyyB62mkI/2I0kz4oYC\r\nsInrjmc2mwOMB2i2e/MrPMllaisImwnW0hSJNnEmT/QrNvSDPoNS0hbiJiS2\r\nZtif7UJIQWcbId0YsC1RGcXzhy4dNYDviq2yjYnVF6Y4hml4gho1YbCh7eIy\r\n7UMNRCXVUVsdEDuZRl4q4BMx8wN78FWmeFy/qhujmINgxDdbfXa8jpRDU2tZ\r\nEbVNAYl97eNHWl6MBel8nGTQppW3g+yy0mFsGZDGafHGoAKUpaBoP7UrLMKK\r\nTrtEfogHrAM4wVPOXC2M0a+9hsX78LYGG6o=\r\n=S+4l\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "e06f284193939fbc1c75992871cda2b8192557ec", "scripts": {"test": "xo && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "14.21.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.49.0", "ava": "^3.15.0", "tsd": "^0.18.0", "typescript": "^4.4.3", "@types/node": "^16.11.7", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.3.1_1671103748714_0.9074505954504217", "host": "s3://npm-registry-packages"}}, "9.4.0": {"name": "supports-color", "version": "9.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "supports-color@9.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/supports-color#readme", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "dist": {"shasum": "17bfcf686288f531db3dea3215510621ccb55954", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-9.4.0.tgz", "fileCount": 7, "integrity": "sha512-VL+lNrEoIXww1coLPOmiEmK/0sGigko5COxI09KzHc2VJXJsQ37UaQ+8quuxjDeA7+KnLGTWRyOXSLLR2Wb4jw==", "signatures": [{"sig": "MEYCIQDpnV2YVr7bnE37VRdB54fKW/BFOajVuBJFU5gmtvhc1gIhAIsTHDle/uRnBxrbllH4YtAVZ2EYn4wO9j8ehPmhmze8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10764}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": {"node": "./index.js", "default": "./browser.js"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "gitHead": "d4f413efaf8da045c5ab440ed418ef02dbb28bf1", "scripts": {"test": "tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/supports-color.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Detect whether a terminal supports color", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.3.1", "tsd": "^0.18.0", "@types/node": "^20.3.2", "import-fresh": "^3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/supports-color_9.4.0_1687960332195_0.3715605037478784", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "supports-color", "version": "10.0.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "funding": "https://github.com/chalk/supports-color?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "devDependencies": {"@types/node": "^22.10.2", "ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "ava": {"serial": true, "workerThreads": false}, "_id": "supports-color@10.0.0", "gitHead": "ae809ecabd5965d0685e7fc121efe98c47ad8724", "types": "./index.d.ts", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "homepage": "https://github.com/chalk/supports-color#readme", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-HRVVSbCCMbj7/kdWF9Q+bbckjBHLtHMEoJWlkmYzzdwhYMkjkOwubLM6t7NbWKjgKamGDrWL1++KrjUO1t9oAQ==", "shasum": "32000d5e49f1ae70b2645d47701004644a1d7b90", "tarball": "https://registry.npmjs.org/supports-color/-/supports-color-10.0.0.tgz", "fileCount": 7, "unpackedSize": 10617, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApAgIWyg7TmxlH6eH5IdQKxlWCs6MGufiD9WsSuFdBsAiEAs8F/9w3XmYqYVOHY27lDFP4NfDgGl0yIGELNrSZE+Zg="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/supports-color_10.0.0_1734544527422_0.23175929176824273"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-06-14T01:45:57.033Z", "modified": "2024-12-18T17:55:27.773Z", "0.2.0": "2014-06-14T01:45:57.033Z", "1.0.0": "2014-08-13T16:35:11.503Z", "1.1.0": "2014-08-19T23:31:57.814Z", "1.2.0": "2014-10-12T21:36:25.330Z", "1.2.1": "2015-02-22T09:28:35.164Z", "1.3.0": "2015-02-23T06:58:50.877Z", "1.3.1": "2015-03-16T10:58:24.112Z", "2.0.0": "2015-06-30T22:58:34.955Z", "3.0.0": "2015-07-15T23:40:25.242Z", "3.0.1": "2015-07-16T20:43:38.209Z", "3.1.0": "2015-07-18T14:26:04.931Z", "3.1.1": "2015-09-02T19:04:25.188Z", "3.1.2": "2015-10-13T11:20:54.814Z", "3.2.0": "2017-01-15T23:58:12.939Z", "3.2.1": "2017-01-16T00:10:36.346Z", "3.2.2": "2017-01-16T00:19:21.083Z", "3.2.3": "2017-01-16T00:27:52.732Z", "4.0.0": "2017-06-20T19:22:36.244Z", "4.1.0": "2017-06-30T18:57:08.483Z", "4.2.0": "2017-07-07T03:20:42.740Z", "4.2.1": "2017-07-22T11:04:58.322Z", "4.3.0": "2017-08-31T02:40:14.004Z", "4.4.0": "2017-08-31T06:54:37.308Z", "4.5.0": "2017-10-18T05:55:44.764Z", "5.0.0": "2017-10-18T06:05:45.239Z", "5.0.1": "2017-11-28T10:54:57.328Z", "5.1.0": "2017-12-11T19:56:38.670Z", "5.2.0": "2018-02-11T12:51:25.740Z", "5.3.0": "2018-03-02T09:07:59.696Z", "5.4.0": "2018-04-17T03:57:41.760Z", "5.5.0": "2018-08-20T04:37:37.309Z", "6.0.0": "2018-12-21T14:14:26.021Z", "6.1.0": "2019-01-11T07:09:33.987Z", "7.0.0": "2019-06-11T17:45:54.069Z", "7.1.0": "2019-09-27T04:07:02.795Z", "7.2.0": "2020-08-28T11:17:34.870Z", "8.0.0": "2020-11-24T08:32:34.341Z", "8.1.0": "2020-12-14T16:16:19.236Z", "8.1.1": "2021-01-23T09:21:36.393Z", "9.0.0": "2021-04-16T08:15:38.522Z", "9.0.1": "2021-05-29T13:06:03.949Z", "9.0.2": "2021-07-19T14:01:37.227Z", "9.1.0": "2021-11-18T18:04:38.053Z", "9.2.0": "2021-11-26T09:14:21.970Z", "9.2.1": "2021-11-26T09:33:25.398Z", "9.2.2": "2022-03-28T10:44:05.981Z", "9.2.3": "2022-09-07T16:25:35.450Z", "9.3.0": "2022-12-07T12:42:42.978Z", "9.3.1": "2022-12-15T11:29:08.889Z", "9.4.0": "2023-06-28T13:52:12.350Z", "10.0.0": "2024-12-18T17:55:27.599Z"}, "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "description": "Detect whether a terminal supports color", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "readme": "# supports-color\n\n> Detect whether a terminal supports color\n\n## Install\n\n```sh\nnpm install supports-color\n```\n\n## Usage\n\n```js\nimport supportsColor from 'supports-color';\n\nif (supportsColor.stdout) {\n\tconsole.log('Terminal stdout supports color');\n}\n\nif (supportsColor.stdout.has256) {\n\tconsole.log('Terminal stdout supports 256 colors');\n}\n\nif (supportsColor.stderr.has16m) {\n\tconsole.log('Terminal stderr supports 16 million colors (truecolor)');\n}\n```\n\n## API\n\nReturns an `object` with a `stdout` and `stderr` property for testing either streams. Each property is an `Object`, or `false` if color is not supported.\n\nThe `stdout`/`stderr` objects specifies a level of support for color through a `.level` property and a corresponding flag:\n\n- `.level = 1` and `.hasBasic = true`: Basic color support (16 colors)\n- `.level = 2` and `.has256 = true`: 256 color support\n- `.level = 3` and `.has16m = true`: Truecolor support (16 million colors)\n\n### Custom instance\n\nThe package also exposes the named export `createSupportColor` function that takes an arbitrary write stream (for example, `process.stdout`) and an optional options object to (re-)evaluate color support for an arbitrary stream.\n\n```js\nimport {createSupportsColor} from 'supports-color';\n\nconst stdoutSupportsColor = createSupportsColor(process.stdout);\n\nif (stdoutSupportsColor) {\n\tconsole.log('Terminal stdout supports color');\n}\n\n// `stdoutSupportsColor` is the same as `supportsColor.stdout`\n```\n\nThe options object supports a single boolean property `sniffFlags`. By default it is `true`, which instructs the detection to sniff `process.argv` for the multitude of `--color` flags (see _Info_ below). If `false`, then `process.argv` is not considered when determining color support.\n\n## Info\n\nIt obeys the `--color` and `--no-color` CLI flags.\n\nFor situations where using `--color` is not possible, use the environment variable `FORCE_COLOR=1` (level 1), `FORCE_COLOR=2` (level 2), or `FORCE_COLOR=3` (level 3) to forcefully enable color, or `FORCE_COLOR=0` to forcefully disable. The use of `FORCE_COLOR` overrides all other color support checks.\n\nExplicit 256/Truecolor mode can be enabled using the `--color=256` and `--color=16m` flags, respectively.\n\n## Related\n\n- [supports-color-cli](https://github.com/chalk/supports-color-cli) - CLI for this module\n- [chalk](https://github.com/chalk/chalk) - Terminal string styling done right\n- [is-unicode-supported](https://github.com/sindresorhus/is-unicode-supported) - Detect whether the terminal supports Unicode\n- [is-interactive](https://github.com/sindresorhus/is-interactive) - Check if stdout or stderr is interactive\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n", "readmeFilename": "readme.md", "users": {"j3kz": true, "usex": true, "aim97": true, "dyakovk": true, "itonyyo": true, "edwardxyt": true, "jbnicolai": true, "mojaray2k": true, "tomgao365": true, "aleclarson": true, "morogasper": true, "shuoshubao": true, "ahmed-dinar": true, "flumpus-dev": true, "michalskuza": true, "soenkekluth": true, "diegorbaquero": true, "arcticicestudio": true}}