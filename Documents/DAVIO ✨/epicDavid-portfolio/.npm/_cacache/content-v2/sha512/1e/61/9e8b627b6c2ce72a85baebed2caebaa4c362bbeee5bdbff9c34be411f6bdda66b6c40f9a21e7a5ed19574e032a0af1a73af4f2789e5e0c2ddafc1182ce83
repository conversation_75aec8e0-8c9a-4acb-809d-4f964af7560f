{"_id": "object-hash", "_rev": "99-bd558ace55053b69ff0bc4e16108973c", "name": "object-hash", "description": "Generate hashes from javascript objects in node and the browser.", "dist-tags": {"latest": "3.0.0"}, "versions": {"0.0.2": {"name": "object-hash", "version": "0.0.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2"}, "_id": "object-hash@0.0.2", "dist": {"shasum": "bc8455f15e7ad39bc63d865a600e5189d30a6a7a", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.0.2.tgz", "integrity": "sha512-Bf3pDj4Px9fcZxAirCVbSh+zzCKOO3K6P0ugO54fBHtxs/2wVV91+EHxmREdDscviFq1c6tu1UkUuTm3UOxBPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5j6UnHfQ5tdAxoLwRSXJ/k7G02MoO15P83rl9g74ZGAiEAocBHqjXMbzUfTBAE6EpdpWDqhYTZ335FtFH0ngbGpu0="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "object-hash", "version": "0.0.3", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2"}, "_id": "object-hash@0.0.3", "dist": {"shasum": "910979d4c97ad55351560741fdf2df8dd70fe3e4", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.0.3.tgz", "integrity": "sha512-jAj5WMUWMKZhZtI3dETy/9MkwAW1dyLFU+TPKjIda+6T1oYjup2sIlMx/3eyhwM/hEmVl0eZUpa3vEpslaxd8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICffg6/feCr3ceJcmHxWEJF3y7AhiFNnj073/BX/xFNKAiAlwKv/oEiwlHJz46Re/ZLZseLjmr0aQwbGGvQIYYAIfg=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "object-hash", "version": "0.0.4", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "_id": "object-hash@0.0.4", "dist": {"shasum": "7e26df912031cd6a005b081c768966417a3f5bd4", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.0.4.tgz", "integrity": "sha512-hyUv+emzWzCDycYRTpP94qnpcDP3bWSg9vxD0TSCio/9kbR4IbjiSkzJKxgeWU24JXBwM6qNnN13tfjYZQFCfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICTATZPI6mUVe6dNrnB8XTT/NZGct2hoNKsSsjjvWHlGAiB11gndcQgbURGsKkl/5Q45LFnR2uIrRV7BOe0vWe0Jmg=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "object-hash", "version": "0.0.5", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.9.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.0.5", "dist": {"shasum": "e6d0272e3da2b999478aef2e37d63517309a26dc", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.0.5.tgz", "integrity": "sha512-ZumX2/GVnrkwje/ZXmNAupAzc9JAKy7szaZPU+3I9blz3XZfsjT9MSpPwTbLCpowLStN34CQm4ZzGxKWKH23MQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOkjHn2lK4vdNPiVZQxURru0wKRIsZpCtB0W4Z5kKvVAiEAzBWij849UJqvZJn608w7DawbN38zFPKHxUIh1nTNIVI="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "object-hash", "version": "0.1.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.1.0", "dist": {"shasum": "e0bd9c54e2821467cc363c3cc46392ebe8909f3f", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.1.0.tgz", "integrity": "sha512-X7mWc+ExbXhbfDnEZleweeu3tmsFMQPI2HPG2ki/2P2HWVtkYY3YNlw7O9gVno6DBejwynqTcpHCCo0oSXWW3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGhlmr0+91Z2+o1xtjYHdAXsCuQOd+/voCw3va4sarhtAiAQxonp1YOedxsgjOQDeKr+mDODKXIDVu/dR4p9td0foA=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "object-hash", "version": "0.1.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.1.1", "dist": {"shasum": "24a207a6715f45315bedbd34d31e73ae0fcb9ecf", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.1.1.tgz", "integrity": "sha512-0jVwoahFo6vhKrbyaFDYOXskhEneikVmHNrIWK/d5PwMCgN25q4bq+Oe62Qp1JcLGPpviZzAyt7wuCmcfPpEIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBoQnZrF70DjhOCSkzHrmt4sZmRBcZQCBw7TSZsjtb4YAiAPOT81H8XZpUxd0D9qXa1JIrexo/hmWP8V4+ix0ri6fg=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "object-hash", "version": "0.1.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.1.2", "dist": {"shasum": "381ce81090fd1372b3bdc1fc99de2dd350ac9c68", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.1.2.tgz", "integrity": "sha512-MtO3Y/Zjzrdabr0IYYqb4ASnCn4sf8DYdAJIh0mJVAaW/48rP37SntwRwQ/rx4LcflaYyB7cZRMd590SLuoDNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC9fpvTIoV6z3A/D+1HpwBOEDoAHImbV9x49l1+16zLOAiEA47Toln7Ur6URHMTZgfcO1VcM8BUzgVNhxHEJ12SL9b4="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "object-hash", "version": "0.2.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.0", "dist": {"shasum": "e434cab3bc0d07a1edb18e70d02265c27b254e9d", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.0.tgz", "integrity": "sha512-VfM8WQkC5R4SmVuOHjmGYMaj6eySZwS9TDgVG76zPfp6U5fvKmi15H1ZcfDZyovQHfBNpSVtzY91os8D+hbf0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpl2bUPVGZ95CHJ7XxsMHhWIClDM2mhV3+JWsqtSHZcAIhAKTJI/mdSffWpo17N8n64eUHjtn8LMXK3MlpzEc4bmij"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "object-hash", "version": "0.2.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.1", "dist": {"shasum": "52bc8fd5caf7d274b0f3cc08dc0c616e84c2571a", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.1.tgz", "integrity": "sha512-nJfZAmQvLHbLIoN7Q+VVnyH5/zU52Crlevufl4mE5FeOGSgE89pv/S9onVbIZYsaNi/KtPJPEPzJDIaGGptZ6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtenVKR9CcSQXeG0Nq5/zgn0CZq/66+BDtEvkFJuEs4wIhAOxqr5lNbNHpe31OGaJg0WRlJNJm3OCk+kK+ZSC0xlt6"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "object-hash", "version": "0.2.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "dependencies": {"tape": "~2.10.2"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.2", "dist": {"shasum": "0c6b1d665402af1c8ada780fdb4d88f5081cb832", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.2.tgz", "integrity": "sha512-Ozk9NMh/QZYlFSuoMNQ4ZS2yKTEQq1jwAdmO8vQ1j3zkiCxJvcgifTko+Li+BHkFXM3gnjGSq4Z/b765M6PYZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClObGFC/l4khnJZQmghjYKqqxfjHO+VxIj7Q0Gxr8ZdQIhALxFQfECMMJvz0tvLbYz8UBKzOqVdlsbj5TIftlKrLn3"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "object-hash", "version": "0.2.3", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.3", "dist": {"shasum": "a20ad4f169378f0a02791f854ef38e2f35b483ea", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.3.tgz", "integrity": "sha512-CKxgW+ksxN/3QyR0DkhN50MkcCEqVCmsSkb72Z+Cr9dsOOVX7AxfrZNkdodM/AaqmHLvKKOTnWx6VmpATdAL/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE4ziUNjH7hyzgTaxFUGCpI/4HDtiz3Y37U7N5uAb0DpAiEA/uaneTYsNkSsxar1jEvCmuTyOnu30Mu5AEzeMs/cLE8="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "object-hash", "version": "0.2.4", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.4", "dist": {"shasum": "93955c5f8561163b036c5f670e0a1084b921d308", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.4.tgz", "integrity": "sha512-9E6B5G8fTlQfIrGnRJZIavM6b+Kv3S4GMmWjhFobQAluSlr+Y0bb7CIuDhfinJuXu2kbDSL2vPNdTLvOgBWfLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAkDIhyjSaU35beq87nZfxDc/Qcz/xm8rQQyCl3m4IHqAiEAl03YrV7A3S6Ss62URktkjoUpqOk+eV8/Sr84O5bp5EY="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "object-hash", "version": "0.2.5", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.2.5", "dist": {"shasum": "88a555c986109262cc9ae76d9a6153b9fe05800c", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.2.5.tgz", "integrity": "sha512-FbVtUFkacmZwcvWHqK+snnJEuwGfyZINBxpM3Qm8wLkEQXkrbA4DJcN579xgx7x/oEGe2GjpChlPK06JsjbvEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlgQM6GOhmtsuZXLJQsZpYG9aoA0lWLtu/PlYwA1qRXAiEA8mDQMnLFHTb6JftMMW7QbS+XOZd+59EoauQEgYZjRjE="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "object-hash", "version": "0.3.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "object-hash@0.3.0", "dist": {"shasum": "548208e43b36a44e4da30bad6c56ac53b885e744", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.3.0.tgz", "integrity": "sha512-svS23O+dr8NzMMAx90mLwft5LMhqDujSqZ2yHN07Skh2Urdmk5dnoUuqn4/MWrxlD/QvYnY3MRMvxTt7PKc+Wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE0U2f6cN0HXKgQWl5QobMcMlx0r31EJONguiZPruNEnAiBr8QwpzsvUc1YyVIJtm2mB2wZYcFGkCCCPFdu5376S5w=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "object-hash", "version": "0.4.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "f0254a2f1116c7e8911af4397fec7de9f5b2fe7c", "_id": "object-hash@0.4.0", "_shasum": "832084230147913d38ec85d80a9057901853b8cc", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "832084230147913d38ec85d80a9057901853b8cc", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.4.0.tgz", "integrity": "sha512-fz4nejZqH8necxu0Q9mIJtupVYihjCOFJuIpMcMZtfRZKE10LanyWi0EgZuqZUreKryQpSktObnb4QaqftetHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIzU8DW7gHTCbBwwfILneOnzLdM7DeM8BQ1fczDLBCVwIgAKt+l9+7+1GK0P7jtKf3hi1rHKWXP4JMuza5sRdRDbE="}]}, "directories": {}}, "0.5.0": {"name": "object-hash", "version": "0.5.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "5bcda2e849080de1efd4bffc746db81d5fdebc5c", "_id": "object-hash@0.5.0", "_shasum": "e9b784d98b66f416e80de55f2c513710b433fe2d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "e9b784d98b66f416e80de55f2c513710b433fe2d", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.5.0.tgz", "integrity": "sha512-aKwrMfOowu+OVAH+zZrQHnxmgvFtHtlVgX59Tk3pHyh6B1CoPCMb01enCybZmxc2txJ867MPYi/DVyQ7cR8ZEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDL4g7CXdOgdYN8P4NM3yZh32qh25+N9C1e3MfzrLF+0gIhAM8apOgfqE3lktstq88L8NFCf6UotGpb+B9jdVD7b3na"}]}, "directories": {}}, "0.6.0": {"name": "object-hash", "version": "0.6.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "c2611b78b933122b2d4592f5009aac579e531f39", "_id": "object-hash@0.6.0", "_shasum": "ff5fad33e47f2e8c4515869ffb2b14c5fdcf122c", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.10.38", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "ff5fad33e47f2e8c4515869ffb2b14c5fdcf122c", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.6.0.tgz", "integrity": "sha512-MDJxaOjO/Ex+0kdukomTsS5qQBitobo28azNICL+qoedQXgWvImO4fQQ01vBqRAXNSXgUe+MV+VRB1hdWvUJ7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIKgVFHu+PtbyboFMaotTNCWJ99FZfMuHYC1QESu6YawIhAO0UT5jFcCETKEvQwO2LcB7ctQllaGfgDdglSXaBOubq"}]}, "directories": {}}, "0.6.1": {"name": "object-hash", "version": "0.6.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "5ec41673183e2db93ff1d15a9ccf7765484f8ccb", "_id": "object-hash@0.6.1", "_shasum": "45fc036998a1fd09aee9b346663a6c5b87a4959e", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.10.38", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "45fc036998a1fd09aee9b346663a6c5b87a4959e", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.6.1.tgz", "integrity": "sha512-PlDzcuWts59fhHGbUdgmwSRWU/c4bbXEf+/TZXgOh1T/adX971pT4xUGdYycKfpfWXZh5TQchvtRtV/g9EabKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXjkzukNDLrfzsSi1AAKjus9pqyYkBjth+iGnOAqcNvAiEA0/At+vugVcmkvJXfIRFKMh7CPopnWnOvVC7tlVYwWv8="}]}, "directories": {}}, "0.7.0": {"name": "object-hash", "version": "0.7.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "33604becce809133b5ecdea01e392e79f4ee2929", "_id": "object-hash@0.7.0", "_shasum": "5a728ad32b9e989dd67e5fbac97778f6800253ad", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.10.38", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "5a728ad32b9e989dd67e5fbac97778f6800253ad", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.7.0.tgz", "integrity": "sha512-2ZDP7+17sKWkWfbAybtVktuTp4Y6amtAfEZN5sgMguFwThY+R+n0tptwAT0r4gLJ7xcPMc1cYvZdyySxeOKsEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO8BHoownc3hbnACvQjtF0YnixzkkZ1Gt2eIHScNIqfAIhAOtAtVeGUBX8Te4rptWyFMseZ7GYMIYc2Dk22bB0XVcy"}]}, "directories": {}}, "0.8.0": {"name": "object-hash", "version": "0.8.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "68b09cbf947648ea33993ca0605b77fe5ff081a9", "_id": "object-hash@0.8.0", "_shasum": "6ffb61b770c940d91977fda1466c9269cdd59cfc", "_from": ".", "_npmVersion": "2.7.3", "_nodeVersion": "0.10.38", "_npmUser": {"name": "pu<PERSON>s", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}], "dist": {"shasum": "6ffb61b770c940d91977fda1466c9269cdd59cfc", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.8.0.tgz", "integrity": "sha512-OGYQrtxQerFGtKBALcYMDQCs359N8sbDdPATnxtJ0iyMvcrGMUPwUawi0A1HiZxUFjKLYSNikLwGuBN0Y3GJpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnewjqaK55//8AKwtPr119GqCJ+f+v+7cD9CTdEmV1mwIgPeuOBC2XNB0jjsDRcu3aP2WVin9oTG7J04ZKDSIYn8Y="}]}, "directories": {}}, "0.9.0": {"name": "object-hash", "version": "0.9.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "17c29c69528ba9b3149371509078d51c58417030", "_id": "object-hash@0.9.0", "_shasum": "a0f7b6858e54fd2c42cf14aa71e132845f7c3d12", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "a0f7b6858e54fd2c42cf14aa71e132845f7c3d12", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.0.tgz", "integrity": "sha512-i6hC6xZMHkuuyUPHzBTCTJmD8hNSCVfWT5EsJs4/gbwTpqBV9BgmLtvtU36uInl6aVjvO0mITR9ok11t6uNQLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnaL1gi8bBqhG4DxserS61LgwQM4nZJMuzgg/QQmlPNAIgV7ACqjzgn8rWANvyEPZ6PUT3Hy3lHEsok90yGGn5KnI="}]}, "directories": {}}, "0.9.1": {"name": "object-hash", "version": "0.9.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "https://github.com/puleos/object-hash"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "3f0ab3c8e821a246559808d3e488c7b675d60453", "_id": "object-hash@0.9.1", "_shasum": "fb7824033588af0afd10e51a2d90103eef3cd8d1", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "fb7824033588af0afd10e51a2d90103eef3cd8d1", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.1.tgz", "integrity": "sha512-9MgIYHpgHgVsFhWCW0l+nSQw1z8qYnJ4g3oxc61RkC509JqkuhqzWjzsrXhpP/P1GLKcPMNooCYqblWCLSwOtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChXI//OGZquAptvZdk0t1vCUGJB84RelvSSkygAd2XKQIhAIVz1PhDtKzU6prYgxOuQg0YIaPAlhm2RsR5Ytyc4O1C"}]}, "directories": {}}, "0.9.2": {"name": "object-hash", "version": "0.9.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"tape": "~2.10.2", "gulp": "~3.5.5", "gulp-jshint": "~1.5.0", "gulp-browserify": "~0.4.6", "jshint-stylish": "~0.1.5", "gulp-exec": "~1.0.4", "gulp-uglify": "~0.2.1", "gulp-rename": "~1.2.0"}, "engines": {"node": ">= 0.8.0"}, "testling": {"files": "test/*.js", "browsers": ["ie/9..latest", "chrome/22..latest", "firefox/16..latest", "safari/latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "aae67a0741eb2b074f78244eb6a758620946f7a3", "_id": "object-hash@0.9.2", "_shasum": "514291c9f6c8aa1d7240ba015aa9fbef9606edf6", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "514291c9f6c8aa1d7240ba015aa9fbef9606edf6", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.2.tgz", "integrity": "sha512-meAbFNtJreOFXlf40IETTkard+e0bm4kBYfq8ZMfWNbZeS8ycXnlbOxtzme6JDusgWUB626Zd4r2HQEli0KV2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHz745qDeXLWEEhH8o68sj7d6iHCZTdIet0iE1QJLIPiAiA1H5uaA3o4ELHy0sAXeeRNlY64lfMzonPqp+KhSq3Lgg=="}]}, "directories": {}}, "0.9.3": {"name": "object-hash", "version": "0.9.3", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^12.0.1", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "8a2efc81f744a04a927a3a362169b762b3d1cb31", "_id": "object-hash@0.9.3", "_shasum": "499e73b76db1c4d624f8f862645eb8d06410f88b", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "499e73b76db1c4d624f8f862645eb8d06410f88b", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.3.tgz", "integrity": "sha512-y0+n1N6UaUqyVgjt9jpdpqzy77or7qEsjpOXAK9e0ZSBiAvc2HtU1iEX6ic2qAUfV4g7/zZq0Howi9g1iFf2tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+AU9qT/18u8P/55COmcL9zbI9gPmlAgjrWeeY8+iKWAiEAnxosilYWX6byODlLTm4MwjhdvQ+Juiwnufj4XoFmEbA="}]}, "directories": {}}, "0.9.4": {"name": "object-hash", "version": "0.9.4", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^12.0.1", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "a17ee122d927283335d929bc2fa2f3c7316c3fd1", "_id": "object-hash@0.9.4", "_shasum": "6bac7b8d762d4a954533c80b43b5144c202d04c6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "6bac7b8d762d4a954533c80b43b5144c202d04c6", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.4.tgz", "integrity": "sha512-tzlY3jLiYl5tUFZSVhBbO7+KuECvXdXyllWVJ4c2Z9P03w9eqm9ezadbNmqiiapSIioKj1hY5OewyHRG27QTWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpbYwmy5fzHqsWldevwa4SVg8YWKi7iI/Q72v8qvOMKgIgDD3nzJZ9REr2Mc2w/Csu5AfGXNdsWX98LpGUfw+pSWg="}]}, "directories": {}}, "0.9.5": {"name": "object-hash", "version": "0.9.5", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^12.0.1", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "3deb7799092d182be409005b80d6c5f7fc2dfc28", "_id": "object-hash@0.9.5", "_shasum": "8651daeb7be1425d85aa04df714d3360edf52a2c", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "8651daeb7be1425d85aa04df714d3360edf52a2c", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-0.9.5.tgz", "integrity": "sha512-NubTyxdp+zFlKDminGPf37ofxFJDjwcgJz8Xl1ydfQFihLp8P/jaUIMVX25sTbk/J7mhttFnxrDUPNBca7Qgfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNe00Of2owbYvE8742AHaC8v015PMGSdGFZA10nm4aOwIhANUaN5cb585FHmEtMLiTqSi3FPymnntD4UzJqHh2nv1D"}]}, "directories": {}}, "1.0.0": {"name": "object-hash", "version": "1.0.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^12.0.1", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "c8289f24cf99104bea71beefbd3de0d55fda0d41", "_id": "object-hash@1.0.0", "_shasum": "d58e8d97f2671cd6d409209b71e007c275319f66", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "d58e8d97f2671cd6d409209b71e007c275319f66", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.0.0.tgz", "integrity": "sha512-pG+DIL/BSS76kf6UmrGY1eOc/GcHLeUzxZ14mq01xIFu5fGjW0LuEbeOb7lHP4d79gR9LM1Nx6Mz1UCfEpUpnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCewIjyugeLwMm91tsNT9h1jf0X40fCpdgHrWvidUz1tQIhANu1Aws76eStjoJduRg65f3Z4Y8eEKmD6xQnkSYiTqly"}]}, "directories": {}}, "1.1.0": {"name": "object-hash", "version": "1.1.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "73e5e326b511109473cd049f6cb179f37209cd54", "_id": "object-hash@1.1.0", "_shasum": "af789ae6fc4f1436d44e475b6732755293dbb9a0", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "af789ae6fc4f1436d44e475b6732755293dbb9a0", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.0.tgz", "integrity": "sha512-s6pvvW8ajeM780get66qY7FECsBc+ZdPG1qZq8GxwmfDE2bcrVQV3naCKlagi0XAG818Pr2poJBkKjbZjBVisg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSRKOZQaEUwfG8N98OriA8xskwe5zYfNFhyXs7aXeeFwIgU0V0qErVPILnUtFZzOA2Xy6NW2uJNNO83KX84lUjAho="}]}, "directories": {}}, "1.1.1": {"name": "object-hash", "version": "1.1.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "8767aec2213d401d18722ec496afe3b1d1d52c08", "_id": "object-hash@1.1.1", "_shasum": "e2b9894374c5dbb2ddbe804c0cd36fc145d681c0", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "e2b9894374c5dbb2ddbe804c0cd36fc145d681c0", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.1.tgz", "integrity": "sha512-D9xc74T9YofZu78MEUepTZ9/6wjT6AQtYqCMdZkw9tglAo4lkmLJUl1ZOEntRKBsxJMBYTCLptC10TLv/eEspQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSl8QVME2ktAie9T2RhgoERO6cKO/ElZB5eNgm6OowHAiEAnB3siM51ZzLmG0jWgvw5RhXJIXpdgWyCx3oxA4EHL5M="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.1.tgz_1455539366532_0.17967273062095046"}, "directories": {}}, "1.1.2": {"name": "object-hash", "version": "1.1.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "d2179c079541a4302c2f7b2dafd996160468985d", "_id": "object-hash@1.1.2", "_shasum": "2b0493d20c80e129305acc86c40a9159d58c4658", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "6.0.0-pre", "_npmUser": {"name": "sqrt", "email": "<EMAIL>"}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "sqrt", "email": "<EMAIL>"}], "dist": {"shasum": "2b0493d20c80e129305acc86c40a9159d58c4658", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.2.tgz", "integrity": "sha512-U77zyke4WVomXN1MCRw4hEt6pyNh6lMmFlrn0A+BRAg0Can7y+2eHR/1hszDGWj1ocCix6h8m+BVGl6jCGl06g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBSYnB7M00aH/ifgydB1swXe7MqJN00bosD8A4SGrahIAiEAm5X0gVBHWu3WrHyYp9ja4WI2Lu2h1X+uPeO+iDEKB3Q="}]}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.2.tgz_1455550254349_0.05658689793199301"}, "directories": {}}, "1.1.3": {"name": "object-hash", "version": "1.1.3", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "5ad31ab1c73870dde7ddde846cb631784724eec9", "_id": "object-hash@1.1.3", "_shasum": "2c69bb337b7ab1b652f0fee68b3ce7ab3e5f962c", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "7.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "2c69bb337b7ab1b652f0fee68b3ce7ab3e5f962c", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.3.tgz", "integrity": "sha512-YAMxpunfmOHuo4S5KznCqN7diZrT4zHDrawFdkOX898f0XZ1yg3HkYjLm0F3JDWG/D5ebKbGlcpxe6M/V7WZmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX5Vgga05d1X1Smk2Be6HJFdxOwf5mYq5M2oX0DNXhtQIhAISHeyGmJzSEr15olsfLTZIFJFjLFJy8WjSmku9asL3X"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.3.tgz_1468264823029_0.752226869110018"}, "directories": {}}, "1.1.4": {"name": "object-hash", "version": "1.1.4", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "5004fa165e82db05713b5220ed1e42d9bd370d9f", "_id": "object-hash@1.1.4", "_shasum": "136f07f1a48af5f6b906812451ae4e43978c94aa", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "7.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "136f07f1a48af5f6b906812451ae4e43978c94aa", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.4.tgz", "integrity": "sha512-W6muQbHEfHqZusW/fNQT+Z1WAE0FgjfxtGOdSpOfWYVHYBuPu/geEwikhGftiwJwQ7waei/kB2kY4SD70bTR6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFxuBtMbeUlVNOeegmhRQZarZ9RPEZEFn/pPpqysn9tzAiBabEn86EA3XKl+5YopfP+54SxBF4Yp9wfIjn8XBGVdyg=="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.4.tgz_1470069698161_0.6088556589093059"}, "directories": {}}, "1.1.5": {"name": "object-hash", "version": "1.1.5", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "f0974d27d15d74af519c30b123db3a1102bf3de8", "_id": "object-hash@1.1.5", "_shasum": "bdd844e030d0861b692ca175c6cab6868ec233d7", "_from": ".", "_npmVersion": "4.0.1", "_nodeVersion": "7.0.0", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "bdd844e030d0861b692ca175c6cab6868ec233d7", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.5.tgz", "integrity": "sha512-hK8Jrmm4uRKxVho8Xl/3dhzzRzSUSEBBuyledJAmmeQcBnDXCoY08HevN3zEseUereZJmuora6WUL2p7I9n0Mw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH5+SvNDQkf0zR/YLR9HxrfiOICfJB2KC0K4qAr/j1CLAiBEKW9XlW14FmKODcW3B4u4GZoNSg1eHJyNbIwLN5pXdg=="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.5.tgz_1478452439040_0.3592356538865715"}, "directories": {}}, "1.1.6": {"name": "object-hash", "version": "1.1.6", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "main": "dist/object_hash.js", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "gitHead": "53285aff2370602247883b4defb9de85c8cfb85e", "_id": "object-hash@1.1.6", "_shasum": "826fd161372e826bd124a85b7e67710d14146f65", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "826fd161372e826bd124a85b7e67710d14146f65", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.6.tgz", "integrity": "sha512-KTZ/z5abHs89tjE4wVAwcQqD6EehFbFjSPkxmIXG8gox67Ki5r+HBc3viZhpFJnpMkkZyiXx66JeblmJgriMyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGi0i1v+vRBdLWKqCFeiEiT7ur1XuiwqYbvr1x2OqMQ+AiEAk2A4GEB+HFigetTmWkCh3B2dxrn2/SDKeDapvWTWj2w="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.6.tgz_1488310561260_0.3094455455429852"}, "directories": {}}, "1.1.7": {"name": "object-hash", "version": "1.1.7", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "e4061ba0abe0d6a54fc878867439c799f7f42b2c", "_id": "object-hash@1.1.7", "_shasum": "a8d83fdf5d4583a4e2e7ffc18e8915e08482ef52", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "a8d83fdf5d4583a4e2e7ffc18e8915e08482ef52", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.7.tgz", "integrity": "sha512-lhv7ewCsplXdvMuyDArlMN5PspRMinJ7u6b5XpaIzCzkCARO67f150CFmOfzU161rEbBYo89DlyJu1safngyOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1t1tgfRNzk/RdfeVVC6YMZrMdalqi7DO8zF/J5KQ3+gIhAIler6LGkSP+O6oFS2VDXMksKgtgWvv3q3qKitq3C9kM"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.7.tgz_1488479969175_0.6774615540634841"}, "directories": {}}, "1.1.8": {"name": "object-hash", "version": "1.1.8", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "469439a30fdd41e2f35f2a6aeddebb6a8d923f75", "_id": "object-hash@1.1.8", "_shasum": "28a659cf987d96a4dabe7860289f3b5326c4a03c", "_from": ".", "_npmVersion": "4.4.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"shasum": "28a659cf987d96a4dabe7860289f3b5326c4a03c", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.1.8.tgz", "integrity": "sha512-PEGXrjVupekJ/RMT7/vHKhjThBm1dUul3Fhk8TjEPqFmDPyfYURFL6W/gWcvz/yNHeOXosJnUGOm3M2/YfebXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDE5vmiFQboqPlA7+P5QYIJTkSTivm/ABAq8BA+QnLKAIhAOFqFMDJyUNpoJgR9Sl7mN3Z5Hva9ckeyXGew0fWQuxU"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/object-hash-1.1.8.tgz_1491750842080_0.8127299554180354"}, "directories": {}}, "1.2.0": {"name": "object-hash", "version": "1.2.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "e600f09ac0f0132f8472e3d9b92b2dcdfe1732bd", "_id": "object-hash@1.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-smRWXzkvxw72VquyZ0wggySl7PFUtoDhvhpdwgESXxUrH7vVhhp9asfup1+rVLrhsl7L45Ee1Q/l5R2Ul4MwUg==", "shasum": "e96af0e96981996a1d47f88ead8f74f1ebc4422b", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCroWpJv8Ld8eWyFWkDj//6R1HYt/0f3H772WJo14O/wIgDxIeI0pVjaa0pdI5ci6VNoH1JkPPT+ggeRQ0Ueb27ks="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash-1.2.0.tgz_1507768204480_0.36773726250976324"}, "directories": {}}, "1.3.0": {"name": "object-hash", "version": "1.3.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "7813a497bfb53d9efa072ee7637c79ebaab4a296", "_id": "object-hash@1.3.0", "_npmVersion": "5.8.0-next.0", "_nodeVersion": "10.0.0-pre", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-05KzQ70lSeGSrZJQXE5wNDiTkBJDlUT/myi6RX9dVIvz7a7Qh4oH93BQdiPMn27nldYvVQCKMUaM83AfizZlsQ==", "shasum": "76d9ba6ff113cf8efc0d996102851fe6723963e2", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.3.0.tgz", "fileCount": 19, "unpackedSize": 317008, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGyIDjyiYpaWghFURwgh25P97xTVCSFS9FYiaPw85tbwIhAOXcl5eTI7p+g8tvq4RrNJVKyfvdhKCuZtAwHXVeHyNw"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_1.3.0_1521157973301_0.5969662069332622"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "object-hash", "version": "1.3.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^13.0.0", "gulp": "^3.9.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^2.1.2", "gulp-istanbul": "^0.10.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.2.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^0.13.15", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-mocha": "^0.2.1", "karma-phantomjs-launcher": "^0.2.1", "mocha": "^2.3.4", "phantomjs": "^1.9.19"}, "engines": {"node": ">= 0.10.0"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "3fa7790bd255396ee8979ff1a63542affcb37b0f", "_id": "object-hash@1.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OSuu/pU4ENM9kmREg0BdNrUDIl1heYa4mBZacJc+vVWz4GtAwu7jO8s4AIt2aGRUTqxykpWzI3Oqnsm13tTMDA==", "shasum": "fde452098a951cb145f039bb7d455449ddc126df", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-1.3.1.tgz", "fileCount": 19, "unpackedSize": 317359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6c9GCRA9TVsSAnZWagAAQ8EP/0mUkSKOMNJBRVOnrhS6\nZ/AnmnmAt4+VMcfDSmwu8SNcrSkGOq38zM7FhwiZQZgcZ8N0havCrWe3A4u7\nWzIWv50pBiYkmRap90jOh/XHJBFw/ACXduVxcxKtxdQP5ULh5kxtQbvEtArD\nyu8bZfQoB6mM3xvwXVLmY9ttUpyRDv41YsTCExk6wjtwaMWwHgDfz/AGi6uR\nnLhDoYSUvoEZh1Ij7ZM3j2GEegj5ED/3yfnJ5b8IRKAySK6I3lVaebXk4y1T\nozwO/saiiYSfEoDgMnx8aQKTgKpDJUBAZ6qsXVM8wiKSW1hKNADOvfI6TzP5\nFz4FCdTzDU+iSNG0N99nKtEyVjL0dHo2r+xgXUPZQQAiEft/gKi9A/LgBlhF\nYL9KdqdYqzI/M+GrxD7J2J3n6ewGXYGYBrdkeggNHHjFSjnwT2B64Dr2LPk0\net6xgOh2E3lSDGlvYlP9W/O/w1Ra7cw581QNyHN04P80a8nHAvUVByx1XjBt\nCvyBnexWigSclvqA9ZWr5km1P2eQ1IpqKjteR6LT5NbbXqPg+XAPfBkvH5Z3\neZ2OCdHbtlTbdEajw0DxS45304QNd9vChdj4QoED+w7bOnC1FXL5CGNtMXPO\nOeOy+AI7vnzcTSb5pNb7MEIx4qHKEC+lNylGYkFTd2NG8ASWbVDAtP9Sey0Q\n6+6n\r\n=aXEw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHXwhr+fq40WpRtjJpF530WXu+xh0HlsnPdYTw/xK+/zAiEA5gyBcHIWH0yfVZAsnGvxJK+5z7Gjer629foCX6lQXbI="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_1.3.1_1542049605615_0.3664519624945668"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "object-hash", "version": "2.0.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "03f0f7ff417398983293912712e3a8b868ad5b0e", "_id": "object-hash@2.0.0", "_nodeVersion": "13.0.0-pre", "_npmVersion": "6.9.0-next.0", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-I7zGBH0rDKwVGeGZpZoFaDhIwvJa3l1CZE+8VchylXbInNiCj7sxxea9P5dTM4ftKR5//nrqxrdeGSTWL2VpBA==", "shasum": "7c4cc341eb8b53367312a7c546142f00c9e0ea20", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.0.0.tgz", "fileCount": 18, "unpackedSize": 258750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm0DzCRA9TVsSAnZWagAAFuwP/R/nDaC7YOfckRaUN/L3\nhHIOep7awZJA42CD3Dim1bUQx7A7kMQH6GdRxn74cm8W6e572GHL5a2lgvZc\nHiL4KCAELwPxqXdtA9aSTZY6e00XqpGkMCQ+pHJ9diMQfr8az26/wrn51Iv8\n86F+LYEyNrfW2bdOFmZIeyKWD7OjOJuWvXlNTcfJBVV16fEke35/t3HzbE28\nwRXc3G4cook5t9bRx0VNBuN1qGnu6fNViW55DsmbVGPK52TY3wdva5vm7K+U\n2OJNvsybTkyaAEgaDQmNoRYXbHYkwkiYghloGAHZji+kNQMc2ikW4GDeKGxl\nJeFravJL9S8JW53Cn64wP6gyMUA7YhG31/8cde/Vri58v7Vz47XQKuCrl2zh\niyqnJFNYPRHzgReO7vkmtbqVnN66bwl1/g7dM++HLCq5n+/nfo/3GwyZz1aO\npJaypoRR3h6yEAowrQekTuIwWc7J7hgow6aWcAV2rAJSv5t0kOOYEpi1a6sv\nGOneMwYS6WSe67v5xP/57tTk6evDDWRV4MpRr4nxWalBv/GJZfOuE0TQFZ8W\nisvSGd+nOFayQPeriwrLmDO/ZIURakUBByplV1+Qx1OVJsyfQpa3d56zEkvW\nlbNNRP5eoZvu6b6STBz0Ytyv20hU4DoLhm7IZK2jFLAj8CrlLENv0il8pNBp\nsfKV\r\n=z9GB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlLjCH1RITOPC21+rdUHt9gzURj6mD1d8BwEANhLno/AiEAzx2ETW5fNjoVBeZB5oko9RbI9A2A7TXfZvZ5W7V4CnM="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.0.0_1570455794756_0.9387405533589546"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "object-hash", "version": "2.0.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "1e0835cad7d273b521b8a3beee6c7cb8bd778bee", "_id": "object-hash@2.0.1", "_nodeVersion": "14.0.0-pre", "_npmVersion": "6.13.0", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HgcGMooY4JC2PBt9sdUdJ6PMzpin+YtY3r/7wg0uTifP+HJWW8rammseSEHuyt0UeShI183UGssCJqm1bJR7QA==", "shasum": "cef18a0c940cc60aa27965ecf49b782cbf101d96", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.0.1.tgz", "fileCount": 18, "unpackedSize": 258888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4vWrCRA9TVsSAnZWagAAPCoP/1rq9JVyjQeiCQ/nf7XD\niuI5Q7zU/F/87wtg4y4r7G/HRa8SOamTs4H10dxnHZgwLOAh5WeAo62c7ndV\nz4COt/+RaWFj5x0wC0rnfmUVEmQXOrNbwSaLrCAnLCaGAkV1lguc6QMrpORA\nx88W3F4ZN+LUEsQK8z4SuSATCyNxjBj4jx+yFf0VrhhCuoq/xuIy1pOlq4Qc\nT3Dg7LIV/7YiDpeGOnFecwM5IeojFO3dyTnffr2ZLpuWemCXNPTe4sH7jezy\n4/YXMcYba3RLyI/EWnUFt7bQ7IgxUgAx5hoT0vkDnsneGE/BsKy6btQJsJoQ\nZy1/fgF1A93MU5LMTfF2KpepyN5VT1lhsBFRZ7KfNqec4cYYDEZrwGiAa4HH\nataB8RttCidGezkI998zL0jwzUURqa+BFJWfxCyy0pE45ZwbVhzONPruQ7E+\n65uIX9MMXCRfCCKssjMzQKpKkG2X5UdH3rNJ9U4wDKgNJOwS1fc7DCSCQXr0\ncvo1CS1Q0SG18Me3pqlRWi2j7kimW4rKv3f4uREPGxvxHodCiE6KZFHUuG4O\ngPvKlYIwqFNFPeWlwkj0/hKvib8oRmvoLxZxw0jMIeRhBQy5vInMm7//c57o\nRs7j/4E1sHucA5QUfcYPaumtlBleFmcTuhuQPSSRPtVTY4xfE01ZiASRWmCH\nfOGI\r\n=9Luu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6U/MuElL5YqZZ3jkjPcGmri7IZX05OVQRIkOtF0AX4wIhAJpVmukAznS1n0JgPHl+BYPDPuqNtn1Ok2RE8AjPTNdX"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.0.1_1575155115288_0.326411029135119"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "object-hash", "version": "2.0.2", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "e090f7e04fd9d5361a32295417c4f6e1ae9ef5a4", "_id": "object-hash@2.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.1", "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b+2AKjAf6uQlxxv8ChHdM+VT4eeX+ZSwv+pk2xIXZWbo+yxn4/En1iC+GHe/OFYa9on0AhFF2PvuAcFHoiiHaA==", "shasum": "f7b2212dbe07d07e340ccd6004c59504fc4015cf", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.0.2.tgz", "fileCount": 18, "unpackedSize": 259842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQbQJCRA9TVsSAnZWagAAm0AP/0iGdea3HAMMuXys/7/Z\niIxGrIr9lFeQguef75vfpfV5MzHDluHDDdvmgVSGv+M+DLQ/4CJCT1fOYJ/4\nd6KVy0f/Wym3i2dtNC10WrNTXqVdkaU0MA7ZKAS4Mq6S2G+Yp3WbQMic9WvF\nBT7qU9BEMFvOCv9QtwlvtGjGo5llJnbvVMZCeh9jzb8nyuo50hxX2QrysqIk\nncwM3wfvkBV1eYAwvNG8FskuUC8nb9AE5cUd/1EI1WHUihr76o/ob6du/m9h\nntbhCiM10mCaie2Re/K1nI656kNNz8UMMo3Jr2TjvMCcABumjj3rgo6zdlXP\ndjN2k4idMrSpIcafKNXWInxeeLfVO2GrgT1MI66sq7sEhZNHx3mbxkURHQpo\nbThEuQLgnxJhJye7ShcML3j0oVapb+74KZjnH0qXDYfhVeG5LP1001B6zvXc\n4QhMOP7uARvw/b+GtZc5l5uoB/E+wai26HX5cnV3OoigJuX2ywsjwpZvjSv9\nhQB8GVRgKyV4tWDk3lo2EnuSe2zGpxcrAV6S0DVpcIFlrHVaRYydtVtPlQul\nUlPK4JqjELrk0bH5ZjS4futeKvO9ZGCLbNjAbdIgKGSi+AK1vfUlVtBzEkfr\ny2PmnIGaPsXpV4CWvCRupxYzZ9V6EgJ6kwyLo2qn98Ld+O9fhyeWfHQQMuW1\nv4NP\r\n=JLW1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChOS2rR1MkVotvPhmymMLT3XL63q/6xux1FSWc6YtcSgIhAIaFdRJ5PGponm/69k4EKUnxQz41KpE13dE50YohWH5v"}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.0.2_1581364233273_0.8618953652104877"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "object-hash", "version": "2.0.3", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "7a410c7c3aadf387bfa08d919f6bb9faa565e8fe", "_id": "object-hash@2.0.3", "_nodeVersion": "14.0.0-pre", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-JPKn0GMu+Fa3zt3Bmr66JhokJU5BaNBIh4ZeTlaCBzrBsOeXzwcKKAK1tbLiPKgvwmPXsDvvLHoWh5Bm7ofIYg==", "shasum": "d12db044e03cd2ca3d77c0570d87225b02e1e6ea", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.0.3.tgz", "fileCount": 5, "unpackedSize": 58661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSwa5CRA9TVsSAnZWagAADxgP/1dtIuz+8ZqbSFRUrrre\nuGON1rUpjU61VL9ebkD4Yx3Fm6RQMSEw2OMiaw4KVgAM5MxUxQMHhQg+rVy4\nV5iNmBP64y6V9XOvhtnSdhXSlbWlSOMkFi1cEdAm7Qet5DYi8uGI7xveXrBr\npRfS8dRZdI773Gsr21Il1uTmYM69qCTGAU1AcR2aatsAg7Y3jwcNl05dmD4Z\nD9dchY+olomlnNM3FCJvPs5jRsNFIokveYxptlmeqQOKoh1OcvH0f2X5F0UJ\ncl46tAYx+vZm/zR8ET39tLwwHsX/Gul3p78zcOQEeVTRRXwDERNY3IlN7ihb\nGfHh+bTP4XiyeeCF140ywcBDRkRtyfHK2+sktC6PWNm/ggMMgAZhORkDJohk\nWnxuY24uq9SLkXHNW7K8S7vCw1bQLpPkzIeTLQY6HBNxsTuLnHBv7dMoEVLa\nAlN5CjWQgCHsr0hFmsQAymG3bjltGs17e067hBuJ1Fk88ByRSlQzfNUt1U0q\nctX/CSbCaQg0HnWFKh5My/Zw37FoJCWbbjrjXE3ebSpKuEamg0/ekZlzMOmL\njRIIebhh8RiOIPKvbHnk7UZYWTQmIOxYjgmqh0PvxTELWSQcFodTK7hsyMC1\n/790s5JdOHRit9F0CYPnGhehzu13XrMe2zaBB+dVIUPw+hAZj+V3+yRTASiN\nGghD\r\n=WLDD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLyl/VKHb0GPwSXqtmvx93aA8IglPPvAMfo5z3r23gegIgGS24VsPvjXXyKHVRMFvSsLUbFi+1Rh1QppsF26y43dU="}]}, "maintainers": [{"name": "addaleax", "email": "<EMAIL>"}, {"name": "pu<PERSON>s", "email": "<EMAIL>"}], "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.0.3_1581975225063_0.6729135052610222"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "object-hash", "version": "2.1.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "f61b9a5d584158abc3e31c29d2b1fa3d74772677", "_id": "object-hash@2.1.0", "_nodeVersion": "16.0.0-pre", "_npmVersion": "7.3.0", "dist": {"integrity": "sha512-Q7aaYco6F+fQe/hgugbM+Qi3eBCiSo/zhxvJNX2lQSwe+e4BS6cCF9G+qWCV3Sbjm+gcdoVad9Z/jEus5C+JxA==", "shasum": "73df6ebeaa8086e43f65ae20452c1ce4adfcf170", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.1.0.tgz", "fileCount": 5, "unpackedSize": 58800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5jaUCRA9TVsSAnZWagAARYQP/jnUbasvzzFD6I8VHgbF\nHyHVPcfibe5IbL1JJVAv6AwZdKrkCGIwxE3Lqs9yZcvwsfXFdpTPCDCs0CGp\nezskkNTvlKgwY3UY77KVuHoMAZDQB673fwDmeycN0RTfoVDHtiCAvXj+/Bsi\niHvS63QJ0O7urtKRQZd1Ppfb6T5uCUt5K75ADDgBuMcWY9Np2tHu6fzfPzCM\nMw2LrSaM9EgGg1dT/oy/n9ojjdTceLnTfuqP1WGPi8QWu/rOliKyt1AeNTEG\ndTIVdb4mv/CKErXQK2Xqj4mIKMAa/aX5ZxP3LkXfgra9uXYvhqvE8xfXvBAh\nlWgEmvK7ZgexrJ8+UCiicj0m5UenQ80t4xs3Snk1kZd8rZEGpmeOw8MnAhhh\nosUfUgydr9jDmMAK0Q8W8yDL3hk/nbOo158xuQdi32AwUMO35y0v01Xs4pkN\n+NU9rYUXf3sCoYxRZ4gp3txVQGW/d8jbUYYdCywA7d+hqTSgaIzsY3Wcdyoy\nGA5xYJGL7QCodttFcpmXfukBad9NBDCfuZzNHZNE6dGi/2NVY2rT8+ZF9Xq4\nbLVPGFrIrbq2So5bxTpAbMNUmm/4U+oYuweHiNp3cVY3Q+jr5rLHz68a6ZSy\nFmFOkCDxksJmdYOa4GgnKNbc6P58x8Ng9bMvYys6TPNuCjUtCJPy5l2ZAHna\nZNbV\r\n=jq5S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4gUDTRWKGSMT2kCE8BYAgo++BMigCY4SkySpAPQaVtgIhAOs8hqgGDCxopTY1kP1BR+NAJcbQtRXljKt+YCd+xUQ1"}]}, "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "addaleax", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.1.0_1608922771967_0.021420992382469972"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "object-hash", "version": "2.1.1", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "f61b9a5d584158abc3e31c29d2b1fa3d74772677", "_id": "object-hash@2.1.1", "_nodeVersion": "16.0.0-pre", "_npmVersion": "7.3.0", "dist": {"integrity": "sha512-VOJmgmS+7wvXf8CjbQmimtCnEx3IAoLxI3fp2fbWehxrWBcAQFbk+vcwb6vzR0VZv/eNCJ/27j151ZTwqW/JeQ==", "shasum": "9447d0279b4fcf80cff3259bf66a1dc73afabe09", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.1.1.tgz", "fileCount": 5, "unpackedSize": 58747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5jbWCRA9TVsSAnZWagAAfc4P/RIqoctcSGKMGHQxVg6r\nYB7po/819ky3AF6xMpgZ2jJ7Lf6mkQ3/EunW+n8J/oc/uROwl0LOULvAZgIj\nJTudsgqK+sjYXaiO+C0pRD093McjgAPUsQ30kX6qk9lSglXrF4OzckUAuG6O\nJ2Tqut7INMtqVVhngQpcqi5nxxvscJbJdH9vqgo6rU21r1+urvf4N5/HUc9i\n2D9TFaS35f8BI4CDAKzknIAyo5hhIeBHoNVFTToI/a3tcqCpC11ovAKkXI29\nrkU+NPOHQr1jNGFnVNCTg/B9Xk8CFhlqoQVd3unDUvPRTrK3HBdMtnTaQdDo\nWMt0nb4o1yxd1Gfq9MqjMJt6B8utjr3K2IC/O4bPEfafTX7DHNMgFW/d0r+E\nte75TAHBJowcGHVGZylme5/dsDAu5RH4Rm4z8WQyZs/DlMKaxdEbWEO5AJgo\nxeBShAyjZp4FjrePIA/U1/5zP0v69AoCAWzk0RyM+HDPsyx26HR+dYdY03EN\njabWDfXGBh476cOjbYSIjs423DX3bj43InvZ8y1IZN2XgYUPHsmKSfSpItfc\nQQ1ru6UcLxtDizL9dFsQexcC6FOsNJXYUnzm22CM8lYfNB/ebsNiYEqYOT8U\nebV+21O2BUrPEeEp8DAg/NjX2LE6m2wdSNif64bDQE28DG37Jjq4ssW+GivV\n9RfT\r\n=trbg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBWW3Z5IXf8DkEVENygaQvJi2iV2ouLFk1r4P9JdLo2aAiEAwtS8xgMin6T+iavDHEEIPgXjSzGh3CNzdpWvQmcSscE="}]}, "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "addaleax", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.1.1_1608922837701_0.615626317274047"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "object-hash", "version": "2.2.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "f5fa94c47c1cbcc96987772b32427297512cbd3f", "_id": "object-hash@2.2.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw==", "shasum": "5ad518581eefc443bd763472b8ff2e9c2c0d54a5", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-2.2.0.tgz", "fileCount": 5, "unpackedSize": 59029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrgs1CRA9TVsSAnZWagAAu34P/1lrdI0uaK+f6lXoUR9e\nXoHkoz1MYo525RCGejIk1kX1GLF1G+3M9ioySGSn+Kzj7jXKBnyJb+uqFrAt\ns54Yz2HouhRmEzg4agFxDRiQ9GtmiQAN6zaduaqMZE4dkS+yIpTzYs11kNre\nLgGDI+j/v9iqQo4Ta1Uxq/p3Od5oTR7Edko4119i1+wN+UJN2Gs+5IbAwU3c\nOSwdM8PFGhRNDbYlBUTvktKhUQlzii8C+FHLBFmy/hx8dE7TqedJoMZzJYJT\nS0nJIY3qcCLM8IS6ddD/3aWdOV3oLjoHJj4ITFzvCfiIg/zwf09BeXNVBk9S\nTw74NVBoQAR7LxlQuFDYl+l7LL6DnMeLpY0aoByMkJ0aajf+/az8sIbewjHy\ndn5Jb91fmgtNBF0uz1zNIgUok2jZn14R+uJB4zqLmtB9CqJG+QmWTRaBWyvI\n3cUk8Ul98Hi05Sw2vgRTUcnLrQrVUxmbJbOR0IxbbHxOM4SeXXIihE8jQmnT\nqyS5ug1ExjhjZaYF8tMkmJa4/qd6Be1a4J4OybM5qGRjaRxUsiYaVA5s7kAS\nAnkv81ZEvovBpdbLoHmlvtTjlHL3C5+n5w3bo7VXLe2sDJGUgsfHfu/GADTk\nPV7zJhrXn8qVqAYYhrmDTX7oszKWVVXq3gkMJgrioJVEViPFPcs0DOA/69+2\n1chg\r\n=j7n/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDaYfDA5Z7aWUEZEZlvhn8TZfuqP3ldmmFJ6RLBR+5sNAiEAxAYZcndyryGjW/ZR1vM07XzQKdfYytnOPFSsZxvvH90="}]}, "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "addaleax", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_2.2.0_1622018868490_0.7767982220126814"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "object-hash", "version": "3.0.0", "description": "Generate hashes from javascript objects in node and the browser.", "homepage": "https://github.com/puleos/object-hash", "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "keywords": ["object", "hash", "sha1", "md5"], "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "scripts": {"test": "node ./node_modules/.bin/mocha test", "prepublish": "gulp dist"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"browserify": "^16.2.3", "gulp": "^4.0.0", "gulp-browserify": "^0.5.1", "gulp-coveralls": "^0.1.4", "gulp-exec": "^3.0.1", "gulp-istanbul": "^1.1.3", "gulp-jshint": "^2.0.0", "gulp-mocha": "^5.0.0", "gulp-rename": "^1.2.0", "gulp-replace": "^1.0.0", "gulp-uglify": "^3.0.0", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "karma": "^4.2.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0"}, "engines": {"node": ">= 6"}, "main": "./index.js", "browser": "./dist/object_hash.js", "gitHead": "04db18b253f62d9ef88203b64560693b33812868", "_id": "object-hash@3.0.0", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==", "shasum": "73f97f753e7baffc0e2cc9d6e079079744ac82e9", "tarball": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz", "fileCount": 5, "unpackedSize": 58857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD7b3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZjA//eKp9yFXFgYbfrt/wtzPI5MBatg8X3Fjo9oqOmH9wsDSrS+jd\r\nj5MJ+P7FxitPebYfxwdUjVkc5R0D7Mr7Z/gNAaTKED892o5tGy0bJy1PYJP9\r\nQkPlj4pfCN1fBZCDx28xP5xEmnnLCyDkP4xO6fga7eKSpuO2bhVGN65vsNdR\r\npu1kuxruwvgnNSdxffu/BJI0tek/36iok7Lvy+dTNipmhSFk/6WWoEAjaXcj\r\nYBHtGN4UK6ejoZKNwy5ZgI9lULgPwyvB6hZc8sjd5uwGo96Qz1opymatIhRz\r\nyL8RkjUBPdZcZnYelwG4VsdxsU6Sli8xGwgy2CehT3zdWCkdz/pdlLv5lEk0\r\n9lg1mSCCQ6byht5tz5jfguN3m09SiJbrKk6hhbAtwLR0gx4CiX5mHbQP3Yns\r\n6LB38Qjq983gSdt1y6KFLfSdUOH00jEDERvfqiP4uAGfQz5t0Cozn6UXCEOu\r\nOhZoU6SFbvY5jcV7vi1m3bt4ht+kyLHZUlw9OtyqmcTyagE2WksECMsrP7le\r\ncrHDfLxB+EBGRCGwXlnUROx3tlzKDoOuTMhMTu4kSt8ygGDZwO9CCgUv3Onm\r\nHYG7vbIhRHb9tIErGadxGECeTl0LuQaU3CutCvNWh5XvXyBj/fXdgvS4zMIN\r\nufVWH0iDMbw4i7U7LnlK9tr69x4QpkwqKRs=\r\n=W4kp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRuRfqrvXiyuj9TrTAVvaHHkwv8l+TO4tbMaZVLOwK/AiEAy69Lt+jzDvHaGPIHfOo1QhUxYYeuaeza1enxVSTmqd0="}]}, "_npmUser": {"name": "addaleax", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "addaleax", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-hash_3.0.0_1645197046894_0.44201253010542785"}, "_hasShrinkwrap": false}}, "readme": "# object-hash\n\nGenerate hashes from objects and values in node and the browser.  Uses node.js\ncrypto module for hashing.  Supports SHA1 and many others (depending on the platform)\nas well as custom streams (e.g. CRC32).\n\n[![NPM](https://nodei.co/npm/object-hash.png?downloads=true&downloadRank=true)](https://www.npmjs.com/package/object-hash)\n\n[![<PERSON>](https://secure.travis-ci.org/puleos/object-hash.png?branch=master)](https://secure.travis-ci.org/puleos/object-hash?branch=master)\n[![Coverage Status](https://coveralls.io/repos/puleos/object-hash/badge.svg?branch=master&service=github)](https://coveralls.io/github/puleos/object-hash?branch=master)\n\n* Hash values of any type.\n* Supports a keys only option for grouping similar objects with different values.\n\n```js\nvar hash = require('object-hash');\n\nhash({foo: 'bar'}) // => '67b69634f9880a282c14a0f0cb7ba20cf5d677e9'\nhash([1, 2, 2.718, 3.14159]) // => '136b9b88375971dff9f1af09d7356e3e04281951'\n```\n\n## Versioning Disclaimer\n\nStarting with version `1.1.8` (released April 2017), new versions will consider\nthe exact returned hash part of the API contract, i.e. changes that will affect\nhash values will be considered `semver-major`. Previous versions may violate\nthat expectation.\n\nFor more information, see [this discussion](https://github.com/puleos/object-hash/issues/30).\n\n## hash(value, options)\n\nGenerate a hash from any object or type.  Defaults to sha1 with hex encoding.\n\n* `algorithm` hash algo to be used: 'sha1', 'md5', 'passthrough'. default: sha1\n  * This supports the algorithms returned by `crypto.getHashes()`. Note that the default of SHA-1 is not considered secure, and a stronger algorithm should be used if a cryptographical hash is desired.\n  * This also supports the `passthrough` algorith, which will return the information that would otherwise have been hashed.\n* `excludeValues` {true|false} hash object keys, values ignored. default: false\n* `encoding` hash encoding, supports 'buffer', 'hex', 'binary', 'base64'. default: hex\n* `ignoreUnknown` {true|*false} ignore unknown object types. default: false\n* `replacer` optional function that replaces values before hashing. default: accept all values\n* `respectFunctionProperties` {true|false} Whether properties on functions are considered when hashing. default: true\n* `respectFunctionNames` {true|false} consider `name` property of functions for hashing. default: true\n* `respectType` {true|false} Whether special type attributes (`.prototype`, `.__proto__`, `.constructor`)\n   are hashed. default: true\n* `unorderedArrays` {true|false} Sort all arrays before hashing. Note that this affects *all* collections,\n   i.e. including typed arrays, Sets, Maps, etc. default: false\n* `unorderedSets` {true|false} Sort `Set` and `Map` instances before hashing, i.e. make\n  `hash(new Set([1, 2])) == hash(new Set([2, 1]))` return `true`. default: true\n* `unorderedObjects` {true|false} Sort objects before hashing, i.e. make `hash({ x: 1, y: 2 }) === hash({ y: 2, x: 1 })`. default: true\n* `excludeKeys` optional function for excluding specific key(s) from hashing, if true is returned then exclude from hash. default: include all keys\n\n## hash.sha1(value)\n\nHash using the sha1 algorithm.\n\nNote that SHA-1 is not considered secure, and a stronger algorithm should be used if a cryptographical hash is desired.\n\n*Sugar method, equivalent to* `hash(value, {algorithm: 'sha1'})`\n\n## hash.keys(value)\n\nHash object keys using the sha1 algorithm, values ignored.\n\n*Sugar method, equivalent to* `hash(value, {excludeValues: true})`\n\n## hash.MD5(value)\n\nHash using the md5 algorithm.\n\nNote that the MD5 algorithm is not considered secure, and a stronger algorithm should be used if a cryptographical hash is desired.\n\n*Sugar method, equivalent to* `hash(value, {algorithm: 'md5'})`\n\n## hash.keysMD5(value)\n\nHash object keys using the md5 algorithm, values ignored.\n\nNote that the MD5 algorithm is not considered secure, and a stronger algorithm should be used if a cryptographical hash is desired.\n\n*Sugar method, equivalent to* `hash(value, {algorithm: 'md5', excludeValues: true})`\n\n## hash.writeToStream(value, [options,] stream)\n\nWrite the information that would otherwise have been hashed to a stream, e.g.:\n\n```js\nhash.writeToStream({foo: 'bar', a: 42}, {respectType: false}, process.stdout)\n// => e.g. 'object:a:number:42foo:string:bar'\n```\n\n## Installation\n\nnode:\n\n```js\nnpm install object-hash\n```\n\nbrowser: */dist/object_hash.js*\n\n```html\n<script src=\"object_hash.js\" type=\"text/javascript\"></script>\n\n<script>\n  var hash = objectHash.sha1({foo:'bar'});\n\n  console.log(hash); // e003c89cdf35cdf46d8239b4692436364b7259f9\n</script>\n```\n\n## Example usage\n\n```js\nvar hash = require('object-hash');\n\nvar peter = { name: 'Peter', stapler: false, friends: ['Joanna', 'Michael', 'Samir'] };\nvar michael = { name: 'Michael', stapler: false, friends: ['Peter', 'Samir'] };\nvar bob = { name: 'Bob', stapler: true, friends: [] };\n\n/***\n * sha1 hex encoding (default)\n */\nhash(peter);\n// 14fa461bf4b98155e82adc86532938553b4d33a9\nhash(michael);\n// 4b2b30e27699979ce46714253bc2213010db039c\nhash(bob);\n// 38d96106bc8ef3d8bd369b99bb6972702c9826d5\n\n/***\n * hash object keys, values ignored\n */\nhash(peter, { excludeValues: true });\n// 48f370a772c7496f6c9d2e6d92e920c87dd00a5c\nhash(michael, { excludeValues: true });\n// 48f370a772c7496f6c9d2e6d92e920c87dd00a5c\nhash.keys(bob);\n// 48f370a772c7496f6c9d2e6d92e920c87dd00a5c\n\n/***\n * hash object, ignore specific key(s)\n */\nhash(peter, { excludeKeys: function(key) {\n    if ( key === 'friends') {\n      return true;\n    }\n    return false;\n  }\n});\n// 66b7d7e64871aa9fda1bdc8e88a28df797648d80\n\n/***\n * md5 base64 encoding\n */\nhash(peter, { algorithm: 'md5', encoding: 'base64' });\n// 6rkWaaDiG3NynWw4svGH7g==\nhash(michael, { algorithm: 'md5', encoding: 'base64' });\n// djXaWpuWVJeOF8Sb6SFFNg==\nhash(bob, { algorithm: 'md5', encoding: 'base64' });\n// lFzkw/IJ8/12jZI0rQeS3w==\n```\n\n## Legacy Browser Support\n\nIE <= 8 and Opera <= 11 support dropped in version 0.3.0.  If you require\nlegacy browser support you must either use an ES5 shim or use version 0.2.5\nof this module.\n\n## Development\n\n```sh-session\ngit clone https://github.com/puleos/object-hash\n```\n\n## Node Docker Wrapper\n\nIf you want to stand this up in a docker container, you should take at look\nat the [![node-object-hash](https://github.com/bean5/node-object-hash)](https://github.com/bean5/node-object-hash) project.\n\n### gulp tasks\n\n* `gulp watch` (default) watch files, test and lint on change/add\n* `gulp test` unit tests\n* `gulp karma` browser unit tests\n* `gulp lint` jshint\n* `gulp dist` create browser version in /dist\n\n## License\n\nMIT\n\n## Changelog\n\n### v2.0.0\n\nOnly Node.js versions `>= 6.0.0` are being tested in CI now.\nNo other breaking changes were introduced.\n", "maintainers": [{"name": "pu<PERSON>s", "email": "<EMAIL>"}, {"name": "addaleax", "email": "<EMAIL>"}], "time": {"modified": "2023-01-14T04:36:54.132Z", "created": "2014-03-07T16:46:45.130Z", "0.0.2": "2014-03-07T16:46:45.130Z", "0.0.3": "2014-03-07T17:43:09.259Z", "0.0.4": "2014-03-07T19:48:49.499Z", "0.0.5": "2014-03-07T23:48:46.837Z", "0.1.0": "2014-03-15T14:50:35.644Z", "0.1.1": "2014-03-15T15:47:18.536Z", "0.1.2": "2014-03-15T16:07:22.632Z", "0.2.0": "2014-03-16T00:18:37.258Z", "0.2.1": "2014-03-16T17:01:15.343Z", "0.2.2": "2014-03-18T20:19:41.519Z", "0.2.3": "2014-03-20T19:16:08.730Z", "0.2.4": "2014-04-07T18:55:48.631Z", "0.2.5": "2014-04-07T19:02:05.885Z", "0.3.0": "2014-04-08T15:04:46.973Z", "0.4.0": "2014-12-16T21:30:33.463Z", "0.5.0": "2015-01-28T01:07:22.718Z", "0.6.0": "2015-05-04T19:12:18.209Z", "0.6.1": "2015-05-04T19:17:34.787Z", "0.7.0": "2015-05-04T19:20:06.345Z", "0.8.0": "2015-05-06T21:25:53.440Z", "0.9.0": "2015-08-26T12:03:46.886Z", "0.9.1": "2015-08-27T19:40:21.145Z", "0.9.2": "2015-10-17T22:05:25.978Z", "0.9.3": "2015-12-10T06:31:12.515Z", "0.9.4": "2015-12-20T12:32:13.601Z", "0.9.5": "2015-12-21T23:26:02.711Z", "1.0.0": "2015-12-25T23:31:45.460Z", "1.1.0": "2016-01-10T00:15:32.418Z", "1.1.1": "2016-02-15T12:29:30.624Z", "1.1.2": "2016-02-15T15:30:58.542Z", "1.1.3": "2016-07-11T19:20:25.862Z", "1.1.4": "2016-08-01T16:41:40.136Z", "1.1.5": "2016-11-06T17:13:59.570Z", "1.1.6": "2017-02-28T19:36:01.885Z", "1.1.7": "2017-03-02T18:39:31.271Z", "1.1.8": "2017-04-09T15:14:02.974Z", "1.2.0": "2017-10-12T00:30:05.695Z", "1.3.0": "2018-03-15T23:52:53.348Z", "1.3.1": "2018-11-12T19:06:45.792Z", "2.0.0": "2019-10-07T13:43:14.913Z", "2.0.1": "2019-11-30T23:05:15.627Z", "2.0.2": "2020-02-10T19:50:33.395Z", "2.0.3": "2020-02-17T21:33:45.170Z", "2.1.0": "2020-12-25T18:59:32.151Z", "2.1.1": "2020-12-25T19:00:37.860Z", "2.2.0": "2021-05-26T08:47:48.685Z", "3.0.0": "2022-02-18T15:10:47.040Z"}, "homepage": "https://github.com/puleos/object-hash", "keywords": ["object", "hash", "sha1", "md5"], "repository": {"type": "git", "url": "git+https://github.com/puleos/object-hash.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/puleos/object-hash/issues"}, "license": "MIT", "readmeFilename": "readme.markdown", "users": {"diegoperini": true, "pavel.zubkou": true, "hugojosefson": true, "deniscarriere": true, "cmp-cc": true, "djmax": true, "antixrist": true, "sejoker": true, "arielfr": true, "steel1990": true, "artmsilva": true, "shaomingquan": true, "scott.m.sarsfield": true, "mikeyddvl": true, "nogirev": true, "chenzhuoqi": true, "reinerba": true, "martinandersen3d": true, "level9i": true, "monkeymonk": true, "robixxu": true, "ksyrytczyk": true, "acatl": true, "zuojiang": true, "johniexu": true, "jochemstoel": true, "ruyadorno": true}}