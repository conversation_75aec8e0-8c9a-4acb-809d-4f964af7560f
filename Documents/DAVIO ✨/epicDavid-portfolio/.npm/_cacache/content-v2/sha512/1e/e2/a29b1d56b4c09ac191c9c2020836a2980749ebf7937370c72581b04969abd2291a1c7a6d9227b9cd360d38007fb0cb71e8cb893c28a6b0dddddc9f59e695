{"_id": "detect-newline", "_rev": "24-d1c80e6003544d773b68dd89fa3065cf", "name": "detect-newline", "description": "Detect the dominant newline character of a string", "dist-tags": {"latest": "4.0.1"}, "versions": {"0.1.0": {"name": "detect-newline", "version": "0.1.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/detect-newline"}, "bin": {"detect-newline": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@0.1.0", "_shasum": "b86c3a6ff51c9a21619414dd336e0ff93bc34102", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b86c3a6ff51c9a21619414dd336e0ff93bc34102", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-0.1.0.tgz", "integrity": "sha512-cfi71Ct3vgoxDmq+GgfkUQl7q7XV4ornb12q53jelv8gNP96Y34bWK/b+KQvB1C31PmRoneQbF+T4n4grrg8kA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbQimB5NQ7PY8cu4MhLzNIx8hNjG88znc9Lsv5JHsGNAIgH6rYzC4NT16pKuRDuhFem/MbmbPMAnxF/V0fSUpp6Bw="}]}, "directories": {}}, "0.1.1": {"name": "detect-newline", "version": "0.1.1", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/detect-newline"}, "bin": {"detect-newline": "cli.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^0.1.0", "minimist": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@0.1.1", "_shasum": "6c4b26a2ff67293c2e6d368263fdd18eccf2d81e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6c4b26a2ff67293c2e6d368263fdd18eccf2d81e", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-0.1.1.tgz", "integrity": "sha512-j<PERSON>uzmJ6M8g8gULVRNZzeOVX62N1EZ6/zWb1SuBUQhIi7wdt2hV1uWKx04VwAbjazDHarin27xslnt1TKvQtog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLL4IemIfH3B1L3TKf1KBhMTVS1SK2x5OjORTGIvNrFAiAMfylzoApQjSqVS3FIuKGANBER9tUYmR1HPKg4f1z2pA=="}]}, "directories": {}}, "1.0.0": {"name": "detect-newline", "version": "1.0.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/detect-newline"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"detect-newline": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^2.0.0", "minimist": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "76e7e19f7ce404817de91ce304bc453e27e6c9e1", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@1.0.0", "_shasum": "c0e44a7af1d11bd2d7b7af82675735a6a685dd62", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c0e44a7af1d11bd2d7b7af82675735a6a685dd62", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-1.0.0.tgz", "integrity": "sha512-LB4RHTT6r7PuEAj5QUh4zEXh6NTv6U+vFrCwwFxZYQd+UkPXASJHyHOurO+hAG/C9uhZzrERwsldA4qxuqMOcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNVytBju26YvcfkVY3d5QK233gcjXAiQ+13wLYJYxzVwIhANpAmXKSFawjerSXasjcekWv1jZUrPRAXXQYl8yngWCH"}]}, "directories": {}}, "1.0.1": {"name": "detect-newline", "version": "1.0.1", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/detect-newline"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"detect-newline": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^3.0.0", "minimist": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "63586bdc682814db858c4af1c685f21e63b03e4e", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@1.0.1", "_shasum": "436f962ea4814b29e8f4d023568d1ffd3c756ca9", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "436f962ea4814b29e8f4d023568d1ffd3c756ca9", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-1.0.1.tgz", "integrity": "sha512-iapS68DDhBxPqlFz+zT21hkLlk/ldDnvrLwNwtSO6mUZWWCuzsbWZOj3iXRxFOXGzWZm7eOszbqZlr+KL4Lr6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxR1WGc3lM9qtNcCJne3FADrTEGxUPDZBJD2WN7REQPAIhAMcZZ1Ud1Bpp+v21Ocm/p3uZNg+Nz+LYgZZiXjYBHHPt"}]}, "directories": {}}, "1.0.2": {"name": "detect-newline", "version": "1.0.2", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/detect-newline"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"detect-newline": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^3.0.0", "minimist": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "018a1ca8f0070187c0b4f8aa7748e18d65ecec12", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@1.0.2", "_shasum": "db250ee12645b60b5229482b8d42b31cd166cb29", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "db250ee12645b60b5229482b8d42b31cd166cb29", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-1.0.2.tgz", "integrity": "sha512-KjojAdm2yd0dxf/9BSbUNUtsYpuvJutFdsqmVF1reTBTd0Gzx9wEXNOSms6bZS4dgUxk6+6u7Ybv9bOue8zRng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8njKaxWMr08uxuy+HXk881s8LCrbE7yvwmx9EGBvtLgIgIOr9kUIExiRvxOMUf6lZUuflQIJqwJSvCgBuWHUHj0k="}]}, "directories": {}}, "1.0.3": {"name": "detect-newline", "version": "1.0.3", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/detect-newline"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bin": {"detect-newline": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli", "bin", "newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "dependencies": {"get-stdin": "^4.0.1", "minimist": "^1.1.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "4a94cd938a233e304e11a3a6a0a8f855742fdc07", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline", "_id": "detect-newline@1.0.3", "_shasum": "e97b1003877d70c09af1af35bfadff168de4920d", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e97b1003877d70c09af1af35bfadff168de4920d", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-1.0.3.tgz", "integrity": "sha512-g1xZ/Ifp4oihL+E1hh2x/hVU0KBU/O/922wXOkVSBL87amsFCTtatniPMpUWncdbtTGu2MR00VEGd/ZJyIfexg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGchtXFMWiQ7MEac5UXxE48MOLkSD5Fgc0eQHXwE2beIAiEAyoW59MbigFnhImbU4ZSyEv/39HBUycJWsX8UGSqc1UY="}]}, "directories": {}}, "2.0.0": {"name": "detect-newline", "version": "2.0.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bin": {"detect-newline": "cli.js"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "9b1adb4c58c87d9bf2c18d83c186094cf5e45ab5", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@2.0.0", "_shasum": "a72fd0e6b707e8433ead7ff0ae1e3aeabe58fd9e", "_from": ".", "_npmVersion": "3.7.2", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a72fd0e6b707e8433ead7ff0ae1e3aeabe58fd9e", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-2.0.0.tgz", "integrity": "sha512-0JJYs2iT4FOWdOk/AL2zUqWs3b1u/5nt2g8Ry0RVjVAZzCIgZKSKJPWLi/3ctsIszythfT/8tlLAqZPbFX8r8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBRqyZ4+q1mk1/PyVKsb1CDcL8VkXVhHUe9pTuGTgEcFAiBujNqcFPWKsrnci1JbnSYW8zaZ+5POJWT50Q1IUlQu/Q=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/detect-newline-2.0.0.tgz_1455021727687_0.6424719274509698"}, "directories": {}}, "2.0.1": {"name": "detect-newline", "version": "2.0.1", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "54a9b9948dba1e7e6d32534be4f12b3aa64baee5", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@2.0.1", "_shasum": "3f82607365b3495c48126f8f2f2ae07bb47d5da5", "_from": ".", "_npmVersion": "3.7.2", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3f82607365b3495c48126f8f2f2ae07bb47d5da5", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-2.0.1.tgz", "integrity": "sha512-6zEoFWEJecTP+FQTdyfepf3rlkD0bhfev48UKdZElHTbwL79H86WPawfGJpjFZSOQVkhGBMD1BO4aO3zf/kupw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDeXANYswCb3IN6KYwf+MdbpG6XEgmc+qMjDhQRoL/LAiAeZpMiFG6Heud1SA8gQcRH875LM1dHMApf9Soc6wtBhg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/detect-newline-2.0.1.tgz_1455022360606_0.058979029301553965"}, "directories": {}}, "2.1.0": {"name": "detect-newline", "version": "2.1.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "6d96229078f55b65e6252dd2c0a42006e6a81833", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@2.1.0", "_shasum": "f41f1c10be4b00e87b5f13da680759f2c5bfd3e2", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f41f1c10be4b00e87b5f13da680759f2c5bfd3e2", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-2.1.0.tgz", "integrity": "sha512-CwffZFvlJffUg9zZA0uqrjQayUTC8ob94pnr5sFwaVv3IOmkfUHcWH+jXaQK3askE51Cqe8/9Ql/0uXNwqZ8Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzMEh071gLKs844neh9rdSDoAh4zjdWD5Vl6jIliu9bAiEA/KQuJE+UVz0lEoY9qk/uKW8aqpD9ixQ0VM57UhZ6mZ8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/detect-newline-2.1.0.tgz_1455648165578_0.057551848934963346"}, "directories": {}}, "3.0.0": {"name": "detect-newline", "version": "3.0.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "c4f3aba414b8bf8b57125f1954f1f8ea9ce11d24", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JAP22dVPAqvhdRFFxK1G5GViIokyUn0UWXRNW0ztK96fsqi9cuM8w8ESbSk+T2w5OVorcMcL6m7yUg1RrX+2CA==", "shasum": "8ae477c089e51872c264531cd6547719c0b86b2f", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrvn+CRA9TVsSAnZWagAAmUIP/0cl+xK1rXMqw9Il7V04\nw2RTgeajyhQrfvu/O4tiFWcNiznZJLWUiwjcUCRw39DPP0TUm2B6h8aNluom\nRIjQt7uw866zQTlF7nfWYL/KQRmeBVjUudp2MYPivLJYuy9OCoWaVkfpAAtQ\n0t5YHFDVSwlL+X5wIsFP2vrl3rTzOLN6H6ORUmC5+QqWyzun99Bz3IKvoyOz\n9G/OLczLmWe9ZCD2RKjEwdkKPnjf1imArgOS4s+8LK2VuxhOm/mn1DqW9nDu\n63ItoVjPivx97WPFi0T/M+tuqtVO2nZV1y3ZKS79AY8IqFVWKZvx3IhXNEMv\n2OEGYPpl86gmvebqDK62/Z1foUS7KR+YTan7QCVxckwUpFH7mfyN1FT4pvro\nVyHOp9Dnvu3JAMpaAAI856nUcF/xF7gWlvkVn9Nadv60aDSOSk6xv6LWwBFX\niQqa7NFFHQAnE61ibmx0eP6pdIPihXVu4vICkcKIr3YHKRdT7sV00PggbBMt\n7yovf2SS/eVjLSlZIV3iO+apFJBRHhmaFSPanLL067qpsM5QCjCYzZrpY3kk\nwaRtPl+Iu/d+CN2ocVPNHzbDTYYIRnXT3p+XyMRAb5aS7v0tajGOtMFdDxQL\n45tZxcH/5uffSKbeafmEIizK8zo9GxDuugBezp69h/Iccb/VSCbO0iXw1LUz\nDN6B\r\n=uLPi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtYj3gYmzqSwTmGQxj56ats5ZbxaRZ8z45DeNzoEnEpwIhAP5ozEeOPvo/5mG8L+LfW4kt9UphrBrsBVFPPm/W1wlp"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detect-newline_3.0.0_1554971133408_0.5989248008934898"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "detect-newline", "version": "3.1.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character", "char"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "991b31d4ef88471d476f173bdb4e9ab63ea7b85e", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@3.1.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==", "shasum": "576f5dfc63ae1a192ff192d8ad3af6308991b651", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "fileCount": 5, "unpackedSize": 3774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy8IhCRA9TVsSAnZWagAAv1kP/0aidqpQDfU6OqGHe0xj\nsKNS75P844no0CccnDJPBVTDmHCo3hm4AqRDNKqgIGne4C8BdQ4ZbXjQt6Yd\nBoSd6VH0h2QgcP2eCTrl9AhEnW50mKnvwaHYxi3xeZr5alwZ1DjKpK4miUmy\ny2fBPSjXXPAIw5kyyZDmDdw4mPc7auoLq9cPMWOTP7YbehpKGR9wqR6eCAN6\n/IU67koqx4OnbmDw8Clvk7+k0UjmvuYdtz1xvGRnWsveKSw++FAZ031prgZ+\nqzwecLA12jcp524dr0gTqn4oD88EmEBZohouBseq1geOCmSHA+JCbGZDRwBE\nAxbbtndyqWKGfDWMSmwpwEEGIww/UOSdzu/j1nvaitkenalJobbrG9RDDSHF\n1DX1HKrIn9r4HvOGlE8RawefiA2BSAJ5IylIMPp1fsF5mQL86qvyjs1jntAi\nlJ5F8PWPZwpjpLLSMMF1i846kvhFad+r5JK112TIGmUMjSt5UbT0LrAagg8h\nuJ2ZxLZUP+HnWdUzkhn4P+G2RH7I+b/pwje3RC6I/dJKTMTeiA3QHx22L/9e\ng+c/o54eRLeiGl89Ed5zBsKAfNGqDT4eJt7k/T1GptLVP5Q7uqYiXVX6oRvy\n/aLLUUr77r9Vnima1GiRs910LKb66yFLPlxlCjFpwvfSlY0WLYj4syjRP5c2\nA+Di\r\n=deqJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7pYLseog6EW0qLsCtZYN6Q9/qft+7Apou85H9fbC1BAiAGSsGTe6LgcGw26ZeVo6uzPoterJsBpgZiQ7hWNLmXFA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detect-newline_3.1.0_1573634592999_0.9999847296708473"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "detect-newline", "version": "4.0.0", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}, "gitHead": "dd540459b7b095b82a487e7e47642ed8b946ca55", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@4.0.0", "_nodeVersion": "14.17.5", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-1aXUEPdfGdzVPFpzGJJNgq9o81bGg1s09uxTWsqBlo9PI332uyJRQq13+LK/UN4JfxJbFdCXonUFQ9R/p7yCtw==", "shasum": "450ac3f864d5f61112b53a524123b012c59581bc", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2oGpCRA9TVsSAnZWagAADH8P/2SGZNUAISfo3b2R/uA/\ndVLUmjjkSVQ8muVrfyYgGdYn51KLXid+dOzflUwZy+qkRGlhG141rE62o7h+\nQ330BjtKNum1BqV4u5gNKTYNyjJJ7idCRgnVpjxsgDgw+8dTmr+/C3NW/Aq/\nRcIE/n6fkgG2foXl5fPtferDtddBBJ2AWdqA+Z5aieVHtxD0cgt2CZi1/xFQ\nULfgOS5Geqk7OhQUGvmFmMQ21xZzK9x/iMEyotbP2vyZ0IUtw1/joCft19F8\nGNmHNXNU0FmSfXNwTm1Eq9vi18usqkqdnMtzLdhy75K7JdAbcRP3aMjIBRuC\nC58SNJL0mrVyM5jWzE01nlMdkuurAk97UPKLrgiDbkdkoVS5NzBUqK4LO0WQ\nQVTrmKA1+Vxtj5EQ1xdoqKHpjtChDbeqdPtUH4j0YGi9TYlRMZCpFdXClpwo\nH5G13VNYXk5B72cXb5ZeSMuepO90Oxaw4kqVJ0wO1VMKO5HT1l2/GEdAukm5\nFj5/fcm3It3sruJMhmGp2xujL8V3RM+M53awo2I57KXNFBkFqUm5X69vLhuT\na0daEIBVFyE62bIc8/c5jk67UMU8zg/R9YafNLNCRWm+GayuKkDRMp04QGff\nm0C+5dAy60satcJnNMq/9I+cYwYeea/SRyup1x0KWoGICjUtLRkNOAihk2GK\nrmar\r\n=uJrW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDv0N+DkYvpcIwN/QO0mG0IqWltfZtaGI12cxvjlrjv/AIhAOctcb1M81x/18oJSIqXPUBs4VaWDfND37cjc7qZUYMo"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detect-newline_4.0.0_1634199372936_0.5279149495591182"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "detect-newline", "version": "4.0.1", "description": "Detect the dominant newline character of a string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}, "types": "./index.d.ts", "gitHead": "179874905fddf1903d489b3c4a5a0119663106cc", "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "_id": "detect-newline@4.0.1", "_nodeVersion": "18.16.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-qE3Veg1YXzGHQhlA6jzebZN2qVf6NX+A7m7qlhCGG30dJixrAQhYOsJjsnBjJkCSmuOPpCk30145fr8FV0bzog==", "shasum": "fcefdb5713e1fb8cb2839b8b6ee22e6716ab8f23", "tarball": "https://registry.npmjs.org/detect-newline/-/detect-newline-4.0.1.tgz", "fileCount": 5, "unpackedSize": 3957, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPqVoF5gs7wgSEvb3smT4Qy1aS4Y9BRzW29t6kJNvLOAiEA0dx0gOXZxjm7jfFtXXcTCH+Mw5cdFlP1rtusoyFKJnE="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/detect-newline_4.0.1_1695409189426_0.2145286724351847"}, "_hasShrinkwrap": false}}, "readme": "# detect-newline\n\n> Detect the dominant newline character of a string\n\n## Install\n\n```sh\nnpm install detect-newline\n```\n\n## Usage\n\n```js\nimport {detectNewline} from 'detect-newline';\n\ndetectNewline('foo\\nbar\\nbaz\\r\\n');\n//=> '\\n'\n```\n\n## API\n\n### detectNewline(string)\n\nReturns the detected newline or `undefined` when no newline character is found or `\\n` when no dominant newline is present.\n\n### detectNewlineGraceful(unknown)\n\nReturns the detected newline or `\\n` when no newline character is found, no dominant newline is present, or the input is not a string.\n\n## Related\n\n- [detect-newline-cli](https://github.com/sindresorhus/detect-newline-cli) - CLI for this module\n- [detect-indent](https://github.com/sindresorhus/detect-indent) - Detect the indentation of code\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-09-22T18:59:49.793Z", "created": "2014-06-28T14:34:10.819Z", "0.1.0": "2014-06-28T14:34:10.819Z", "0.1.1": "2014-06-28T14:59:05.168Z", "1.0.0": "2014-08-14T18:23:36.536Z", "1.0.1": "2014-08-17T23:43:06.255Z", "1.0.2": "2015-01-24T11:57:35.404Z", "1.0.3": "2015-02-16T17:31:25.668Z", "2.0.0": "2016-02-09T12:42:09.014Z", "2.0.1": "2016-02-09T12:52:41.850Z", "2.1.0": "2016-02-16T18:42:47.534Z", "3.0.0": "2019-04-11T08:25:33.544Z", "3.1.0": "2019-11-13T08:43:13.122Z", "4.0.0": "2021-10-14T08:16:13.106Z", "4.0.1": "2023-09-22T18:59:49.617Z"}, "homepage": "https://github.com/sindresorhus/detect-newline#readme", "keywords": ["newline", "linebreak", "line-break", "line", "lf", "crlf", "eol", "linefeed", "character"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/detect-newline.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/detect-newline/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}