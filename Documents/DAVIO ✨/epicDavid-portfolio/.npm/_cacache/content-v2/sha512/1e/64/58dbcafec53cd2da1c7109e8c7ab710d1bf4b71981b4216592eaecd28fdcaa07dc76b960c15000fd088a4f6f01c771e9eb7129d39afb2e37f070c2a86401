{"_id": "@jest/test-result", "_rev": "142-6e9a037ff5f25efda8f90fe3a235dca6", "name": "@jest/test-result", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.4"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/test-result", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/test-result@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "704c64a3451776e0c6457abfb2c78cad07e5d9f6", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.2.0-alpha.0.tgz", "fileCount": 15, "integrity": "sha512-vFit+BOQGfm2wrC8A7VwS/br8nQhuicDRP6UnfxD8vzcVaHa/nw6jBV6Ux1Mi+hh9A45LWQrzAtq5ZREwa6Jdw==", "signatures": [{"sig": "MEQCIAlBclvbqLVJJGiZk5mrFNoE3kNeyMjObMFxfhIfAS7WAiAdU2fwcFR1rx/v/1PfvYwsfG+vNaRpB5YaKDZ3F8BkqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovnCRA9TVsSAnZWagAAKl4P/2UDZLM8c+4AEuquwlqw\nBMhAWwlh9kPQ1iu+XDZVnwQdg85wK9MEHZm0EiqfzZaedVVuhp3xpHqfFb4E\n4CEcpBjKWoHPVVGcvqLPnVZ23BhINX1ksNRvVeRKGCQP9NgKUDcgv6caSiQT\nsu9sfS7cMwi+HDi74vtalUV/hbxP3YBYhqjgxdXuFSM5qYvS8kHgZtnQqG5i\nRDou0qfHn/5b8JDxu3lvszXhDNk7RQJrbeIeN9ApcTiC3kmMOYgbcq/usmUF\no8sa8UzgMW3ryiJK4w8TO1Ns/UUWQ0cyiB/XG3BSJp4lAOftM6QHl5xyj/6C\nQP6jayH5H1h4DO7xd/r44vS7HnZtcfG/p+imqTQEAS5qj8ckWBYN8BGu3aV8\nfM3uPMV3pSVxsPIAzPtvb4XHgst3qbMbRqCRmYPbESoKnicZBYg5/Yp1TnY/\n67sl7AhUTgq3cpmkHryQ9jXKS0YrL08+dWbAd9IAfPzjWPDbKY9T6qxDNIHx\nMw37X1MAPheRXUtzcUJpFS9jNf7mXoDDYLwk+IwDbGihgxJahQn3I2HZdBya\n70iXx4iBln1vJUnQRMTFs8t1/OYKxJlZwT+hR1S9vXwr+MWc665iDaOUQ4to\nHTvCKjbnuGuQbsQiJuhJddpgE0PCJoijPabiOBwIR69l4PS3VqAZqR7CBpaz\n4Pv3\r\n=Vtsm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@jest/types": "^24.2.0-alpha.0", "@jest/console": "^24.2.0-alpha.0", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.2.0-alpha.0_1551797222900_0.09217638798876115", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/test-result", "version": "24.3.0", "license": "MIT", "_id": "@jest/test-result@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4c0b1c9716212111920f7cf8c4329c69bc81924a", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.3.0.tgz", "fileCount": 15, "integrity": "sha512-j7UZ49T8C4CVipEY99nLttnczVTtLyVzFfN20OiBVn7awOs0U3endXSTq7ouPrLR5y4YjI5GDcbcvDUjgeamzg==", "signatures": [{"sig": "MEYCIQCGcZ9eBEQzgPELxKFK3JYJH239tFmjhhD4GlG8+k1FRQIhALR3beDLYq3yDQkZu6jQgDzTePtAw9CEAMVax/aaKSUa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXDCRA9TVsSAnZWagAAkbQQAIoeYbCmhtxV6W8+A6av\ngD2cnj6Uxk7Y0p4LfOp7UviAhlhHnod27iddDJqGIYDpFTAoZcov02fC7yDG\neDYWjs9voRno7c/iNY9MKDod1PS5vkXGmXX033LVo50bGORtucThTJnujaZ5\nvqPLydQU82Z+LeEvqcXsFEJMg/0pb7qxGw+iXh4U48JlZDXc1ZIHNtGXmiA5\nOGo4/Wl9jRxjZhHgmpTUm/ERygaCa977KavrZxg30tyjyOuiuTpwIQnHWqM8\nrWU8XpNCn+Bq+yvLbO6il02Yxgm7T2MNcixd1sHQiCUkggTSDNcv0cBvagBi\nMiOKRXvY4WZ19y+/tBhOuELIE9lC/XmG2PMeBxdVaLG21pD8DzGq8VOU/CSB\n6ZJZ/50qz2zkLaLflMmkMnXOp49LhtahAFG8Nq1zMBNVyjPEA5A/F3wgdFMV\n5Z+tQCaM1O6R1cCdUimNvFISDT/oRlxsT2fyCW0Nqlr0Po+opIK1QVzCG0yP\nzHP5HmulgqBvmI2r1vFW7i4HmcdDzbtEJfeVLBhMqDHv31sEWFRarrJNeT6+\nU4PNJPcInSzBVh9x95L5ubHysgGlBpiI5Td9WJYLdabGmSX5gaOB5kSnbZYl\nj4/9TAfFzQ/mVtwSRYHwnabQGFIQTVq51f41A80IvguyiTqQ+vHGD1IDu4lc\nLQhs\r\n=D0G8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@jest/types": "^24.3.0", "@jest/console": "^24.3.0", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.3.0_1551963586905_0.8190308906192068", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "@jest/test-result", "version": "24.5.0", "license": "MIT", "_id": "@jest/test-result@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab66fb7741a04af3363443084e72ea84861a53f2", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.5.0.tgz", "fileCount": 15, "integrity": "sha512-u66j2vBfa8Bli1+o3rCaVnVYa9O8CAFZeqiqLVhnarXtreSXG33YQ6vNYBogT7+nYiFNOohTU21BKiHlgmxD5A==", "signatures": [{"sig": "MEQCIF5NrZ2wob2ASRJmuF6k0kvaGaxGDya4H0sHW27QtkTiAiBeYUFNXaQo2J3cscaBd1Dah4Gfal36f/vg/Vq1Za5SMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+ALCRA9TVsSAnZWagAA1wcQAKJWdnfaxcmlVRRTeCkH\nMmI4bWlEX5TxK//ZHa/Za/f2L1CAxCyfY84U7NOACjKbG7T6X6FiyKqCn9Ab\nhkAcKw3QXlvfyf5pjDt7m7Vx41k+w4+u4hfWVTB+6r7XzgMMIKQp6J2Tt9Se\nvFqo8PlXDrTSHz9V9dUTy+NH4j3hFkso35IvoP7clhjZqbpRHnGCLJjHv2Xq\nzMAwY7V2JYvrdaEWBbOn8uIfLuSTPiEufaoRaGlpDNcb8iJiw7f+GUqO3RfC\nfxecCuPLuCocla7ul1N0j8NpBhxvW4gYauz69KsyvtujpGtQmwe2KG8d1Wdb\nM0cQptQkorT2s2KMJBKW6l3yfafT/9sU8kvayxbSlpmuLmrV3pE7iSot8qAK\n55C8gMVEOHRGCUMdSV6hdGcnr8KffVnXUPKQci88/Yn+eFOTtAlwKEcf/BG1\nOAwcSAwuVzQbzwBZwy4GWrLUpMCFzh6I1v8uqun/nG3xVPkKTTbjZ1HwXrmp\nWp4TSjWGDptP0yuDA6ccZIYVfLldqKbN1MRPNof7BJyeNEHbuTBo1AJd0a+i\nLQFSd2jzaZ3GnVtnCAsNzVJPcBz3FFR9Y8rVUp/sKEHJNRJjwnF/TwLE4uxx\nJy84+JOvFszH/j7+kkQcE/Yp7PddGt+W2/iO+PSQAmCk9HGGmi7QKBwxdCpU\ntZPI\r\n=2E3d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@jest/types": "^24.5.0", "@jest/console": "^24.3.0", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.5.0_1552408586707_0.2655677196736814", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/test-result", "version": "24.6.0", "license": "MIT", "_id": "@jest/test-result@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd56c12b031601c282eede8a5ec1317ebe63bd11", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.6.0.tgz", "fileCount": 16, "integrity": "sha512-k6pdgBBJIDbBgQGZgt8IbQC/KrOAC+fsSZrHw62R54FnfoYzuDqnrbB/AfPJS8T4RjDsWvnAHgXLH866yG10Pg==", "signatures": [{"sig": "MEUCIE4Ohtcu7mmVhq4CLClZ1KMX8a9s8bVC0LHNBCEJn4cEAiEAliSYroFeQnDddFTXuVEfQ7r8A0ZVeW9IFOAS2hT8kBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAPCRA9TVsSAnZWagAA+M8P/Re1o/bWHRTRaTJytYBy\n1ko8R+Dr9DOjAJTykczqALfxmzytPmF1JxOrqoxHa4NLSr+j/5r8CIkPK5CY\ngK2NwcnEOdBsjjUi2uyZZuUSzsQKlcO+2+b5PcOTN42MqZFRcsMVNMcc8Kmm\n1GHo526O5tJLJX2+0IEw41Pw+f5pyrf7zHv/emxaDOco/n3D/P0OSecUQIUr\nXQkYpPe4PsQXPeIhu8LKKfUq7wGoYFg2igT3bw5mX82SAzCzSmOXeZEnETm1\nN71YkddGyvmJFub7DiAvj1B35U5uxHmKnHqLIKjtxVdfcvcQ1d60tsh6N0e1\nDTDRF+JNA8Rq/5dUS2XvB9wMl061aTxbX7HRyA7974R0sfEESVQmM/s3dReq\nH/mqADXWPsASDv2TQAlNJv6yYB1aGMFfyc6sZuyRpfwzzpQm1PDgRArdvOHg\nSfuvH25zsNkzNrlIxpHOEzz8tMflX8XibOZge3Rkk2Kho+81PCEFu8i3WC3z\nZeHPrVCXgQ3F5aXpWPqt6l10PAbndQpIf5iAWFaJrDmrokkcieubTsT5WVzg\nxK8RY6unl/WnuCyiXjzuTc266IK5w97og8i9UEMXkpfisJpRte5ITH0yWq0R\n8G9BlXShlF4/9zUbduV1wuoFvT9a55kOZ8A3ImfouR5BjJAOtjDyf+6qZBQ8\nrcil\r\n=ru6p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@jest/types": "^24.6.0", "@jest/console": "^24.6.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.6.0_1554157582467_0.13231157995884057", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "@jest/test-result", "version": "24.7.0", "license": "MIT", "_id": "@jest/test-result@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "062631a3b1727ef4cc6521df152b9142a68f081f", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.7.0.tgz", "fileCount": 16, "integrity": "sha512-bl7HcDnMYEemy/myEmc9AaO9YXxANADNYtXJRC9haolx8btNHY6q78YdL+jb/KC4vBmEEoK+OSgMae90C1tZMQ==", "signatures": [{"sig": "MEQCIBbQwMMQy8QyWJt1uipB1CwaJwNIlTKDLnFu2hg/CLYFAiBwm1sJcgkPMflheBlwuR5sP6XgZB4OnSKncX47I6y9yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC6mCRA9TVsSAnZWagAAvSkP/jxaObsGI4mapbXhbtYy\nAhEUKlH9yJfxCnQA1/Nblj2Ca9hia37oKcAxQWTWRPlli+p/PlzmJdbuDOGz\nUrvwKuWZRIyzBzGI0fzi5DxjS4NvXI1r6ik/rhQxdrlk6t1yctD7NJmk4Nic\n8BdnixGj3FHLqJ273p9AroZDPTvQGdXP4tG6E8etVfkyhLYajr/XQcxNy/WU\nsaS7fiD/HejL49YkKltQwuCPFP94gXSDT3rt/+tmsBeR03E2AEqyBVdPbH+r\n2BLK6tZEeE6R1O2fpRBuc67nGfckY3weqfcPRUZpdhTiGDPGwapMKlALRlYP\nwDg7eq31cZMreP2Uqt4JQ2F2Pcez++0sjaKs2VeHnxIotfb31RB2H//MlvO+\nVPPd0Us/U+KOKyC6be+fcanjKco+dkcei2UFHQODT3T97rmhmT7v8w87i5Lo\nveuYJ8ltuzXqKNskjxTcwKah/PLWJxN5ij9s1jy+Pxxbu9KU9HDB0zC30bG4\nuwlIARAMAZowaoKTR0LWV/xw7F0jqLN2TkhDVHkvYcKNus87O04OxAnM0rNt\nNSvb5lQ1lPNNODV10zRUzHesaPA+yRxrizp03wZbzhFQr9QmSlEg8yj8i0Ib\n8Vq/0dXxL26dRaMLAj/d8K9Z3MkiEeRnNNRI+MJKbJO6vj/F03mCVP07lt5w\nX0dU\r\n=uK+f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@jest/types": "^24.7.0", "@jest/console": "^24.6.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.7.0_1554263717611_0.9662934761004398", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/test-result", "version": "24.7.1", "license": "MIT", "_id": "@jest/test-result@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "19eacdb29a114300aed24db651e5d975f08b6bbe", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.7.1.tgz", "fileCount": 16, "integrity": "sha512-3U7wITxstdEc2HMfBX7Yx3JZgiNBubwDqQMh+BXmZXHa3G13YWF3p6cK+5g0hGkN3iufg/vGPl3hLxQXD74Npg==", "signatures": [{"sig": "MEUCICM6W7lLCPd+1MzCJkE9bNg8YxBNJ3H6qGNJaEJZdnZnAiEAlHovS4M3KAFgNQSx1L1eh5WS+xhxmZzVKMcXBmkfINs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVt3CRA9TVsSAnZWagAAr5YQAI5UQPizGNuXe9TB7LlJ\nU0RNrDBpUdKoiXu7oleHIfr2iOq+9wE0Otnp+b7j+sWVqf/9hcb6NaO7ByxC\nY3Ql8HmZxbV5GBIL+TB2AphH4W4iZ7stOJDnqosjHlEOi1BtC7lkjnqRyAub\n39Mg90LM3OkXKkDEKs3ZNOZHUKssqiT91Y3r9u29Ypcv1iYSA0A0Fo+nZrj1\nMZ03TOpy4dkb6lTzoP6Os2F+ON5zYzGFtZ+I6QPH31jort+owSKM3cWzdYj0\n0mpsGga8yP/U4MSKHkVWJw8+DSqi2mqR7zRNKt/5PupM5LMT/bnzxFSJbBhv\nCu8hdO962/4em2/610+Po0hVeqi8Yc5hByHbtpCSjBIhwU2s4CxdBqh3JKpe\nAJabeendU9hUrkNYEvAMGr0VUi5Rb1dOAP7TQ5g+OZ/knPuGx03iKuOivwSO\nYt9S2JpwpVKfOmYZ3QyeOePhkwwCor3OU6YWTKiWwNOhV4MNbYVJ70sQjYXC\nEvfakHrPxAg0VWjtqTiMUbqn7VTh5RvNV9WsJz0OwbKdT/4RZOQtEa3yLBKJ\n4xQt0FIbo8/7kpAfYLxKIFW9yIzIMt5/d7j3vEPH1QC39q3CDhLJBmjAlO4U\nickRBWCEKxCfn8/sMm7SIJfzV+tuV67QiJYrnlpBb4xYUExKZHUf/BteWBgN\nkG07\r\n=5dWA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@jest/types": "^24.7.0", "@jest/console": "^24.7.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.7.1_1554340726710_0.09528779520133179", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/test-result", "version": "24.8.0", "license": "MIT", "_id": "@jest/test-result@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7675d0aaf9d2484caa65e048d9b467d160f8e9d3", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.8.0.tgz", "fileCount": 16, "integrity": "sha512-+YdLlxwizlfqkFDh7Mc7ONPQAhA4YylU1s529vVM1rsf67vGZH/2GGm5uO8QzPeVyaVMobCQ7FTxl38QrKRlng==", "signatures": [{"sig": "MEYCIQCSWCfI38fh1Fh3S5CkTkQR/JHDfbwhg+zZOHsNIN2uQAIhALE8x/eszADuWl4SrnXFzGXxlijmlZohWpv509f8HsRo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQuCRA9TVsSAnZWagAAO30QAIqkOeA7zhEk2O5h4myW\njcdz0aTmfORNPIRBpR7EL7xVA3PLPVVVcSbScfCrwyKFvVu56Ah7byS5Euym\nYhmjnnBqIIjqGE5H5b7A8Z/yrUcLaEjdiI/y+LLbPsEscajHJz8LcJIxsvSP\n+ltv3DqI5fnYLUEEkCx7i5pqkqNxvSJD0rZ5K3pJHJLMJcX++y3I/76PnwKL\nWvkoxyqCm80Z43KKH3xhOrsKveQ9qkyvIU9W9FWTDEwhSGEconx+l/eboib6\n987vhkQg2p9RCoaQoqI6KvoSkFlxO/kRXIOzn/8RjLGaEyduaeLx5SfwGXTz\nDxPZZMk+aUcO9p285eVwWpSJQ+peGJu7wMAafhtPVFh1iDnR3lvSpSnaVLoe\nps2E32FlP0bZDoArHOSw0qVnvTytDrcTZx3nCAFWVf0Om1rf+4Ae4fxYIvus\nclOaJEaQvYE3+0BT3pfmiv0qceZZMZp21J0evwefWFBTaC1swDQWkV0GRM5H\nweiWJC2ueK1IEzgGFI5QK/9LitnM2GossyTDgZ/TCNGuWGkqHoFZJ9xkw+xG\nn8XDHfn84SkSiK8m4KPvBtXzNf4y9AtxYi/wvFl4iAOmAh9BHePXfXOTkxY5\nagri319XpGkZPzn1qA9ZaNRDYIxB8DsPtqV+22sGn8cLPv6tT9gBO1L76xwV\nRo+l\r\n=fdIn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@jest/types": "^24.8.0", "@jest/console": "^24.7.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.8.0_1557021741691_0.5483931231486627", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/test-result", "version": "24.9.0", "license": "MIT", "_id": "@jest/test-result@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "11796e8aa9dbf88ea025757b3152595ad06ba0ca", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-24.9.0.tgz", "fileCount": 14, "integrity": "sha512-XEFrHbBonBJ8dGp2JmF8kP/nQI/ImPpygKHwQ/SY+es59Z3L5PI4Qb9TQQMAEeYsThG1xF0k6tmG0tIKATNiiA==", "signatures": [{"sig": "MEQCIApqSTinUKv6druisfYPNzBSrSyEnRbkKUUCy/aG9Y1GAiBRswmmSb17uJIx0TOjJCTe8k8X1cLvA9w7gmNU2RShTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVwCRA9TVsSAnZWagAAKAEQAJTglKkKYVocT4cHnbbC\nnviFyR01hjL5tdqFcuuuoj3XeDMCmGEW3Od9Q0S9z8QKNGvf2laYnILwgTfc\nAJit7pOq9YZaPb7SDbOtMLqKthmRINIc/1a3cs/XD+B/JzbjT/kWwxhwdKtE\n4ivjcA2OksUEtp7Bp/NwwAOClVbTu1/lh8BdNqlh7zikaaoLLikSH/BIuzrL\n+Dcpp2ZCjr9QPI9WLi6OIr6KutHRWBIHFERI5/g9wTXd38cP7HLL66tZOHsV\ntsQjOUgVxVtk2rCourgaF+CXwvSnUrRxuy40y2g1Shglc9dXtpqfLWzmr6tf\n73y+QC3Jvqww7WdsXMehNAcSoQBCTMmTt31Tl1/cZ8juIIupF0vbRJIaLYG9\nC+Oc11ONQPGhzaQAEUPkWGNsQnjDB8RVL5OsWXG0o6QEwt/b2jJgPyXJzqs5\nNlBBUtboGTQWl4W8M1kf9b4qlryv3m58pd1/jKyA/OIEKbHZsGPhbL+1YUw+\nzYtf5xZ44Lt5iZSrcxRwNV7sWEHS8WV0fZXCNghDwEzSqxTTm2Fz98dh6xVJ\nlNEajIT6FxfG8O6L40q26LvYIgWodM27zaF/eb03kD/BItmJQTNn2WyAhcrj\nP9uhfrbfre2GAyox59eXDF/z68JUrAnfIQPk4r07Po+BnOrSwTkBm9w82Yra\nKYzm\r\n=lrZh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@jest/types": "^24.9.0", "@jest/console": "^24.9.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_24.9.0_1565934960163_0.8844162959295159", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/test-result", "version": "25.0.0", "license": "MIT", "_id": "@jest/test-result@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87eb968c5c1b22d8b8d428bd387413b676138cdd", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.0.0.tgz", "fileCount": 14, "integrity": "sha512-QhGMvMBPpukW+80H3t6sQ5Slv5v/a1hVojZWisZcdAJagDiqqz+R2KO9qtSekYdqpRTJhLEtkB33FVfiSKy1lA==", "signatures": [{"sig": "MEYCIQCUYMfAy+bD5GMWXKLW2cL+ya94OveYiDKVrzlzrLOsEgIhAMZBSMZb2Lxmn3+09+SRanfuZ2GTV3zzvR041TroifCf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrRCRA9TVsSAnZWagAAmh8P/0yK2ezt88kSqOYP+R1S\nfk4rNru2Q8Jkx7RngvaKXRMZDaAt0nryPwM5hfAfKuvuw4x2RBQklIMg1tWv\nTjE+KAfP/phLaeluJ2QZRDn8p+bR6/7Ds6YfImK/fYVNVwFF0/fWhlcy5VXI\nZBOMiMPFohaJZOZelciLg9gvsRK5MDat0YIOGV77kev3dMguwiVDQni+pzX9\nHD5RQD/NmEtGRVYwBxaDsXMh+PRcBu1bQYwG0KpwVzUG+5pKi7AtGfMeWQ1l\noDq3/tJMaI4Y7sy8DR6XGpAAFIz0fAyXq7y8mwyoiqeJJOCZ5mCIhvY/74wC\nHDyAsmCP58M6cp+7KoZlUHeo849UCgMvflnPY0xCz0l1aD//oRPHOZDB2sVi\nC82PT+S6ZRDQ6PoyW+mzkcLNTM8nykx+cHT6lnGRPXds34OiZBFm5CLTcJ6O\n73sTP69OUL2DdVxGxUeMK6iT2sA7qUSSjNi6YUFV97jvDSpcWRnhBF2vJbtY\nOQcs0TBKNsJZfjI/iNcd8rw89ccT2RbA3SupuEUckJALQkJ8xSrJVBtAkFna\nWz9YgE22GAgKnk5qlQw0PzyWLdcspFiB84f7Jx8nHAl9o22Byq7vI+EBVLHx\nBbqZpX1aJfZpRTM3i2EszdE1sF/dj+1MAcZHQ1F+ndL7Yd5AWJtZbTwCxT4f\nnqQv\r\n=RYWJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@jest/types": "^25.0.0", "@jest/console": "^25.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.0.0_1566444240518_0.7636513668575982", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/test-result", "version": "25.1.0", "license": "MIT", "_id": "@jest/test-result@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "847af2972c1df9822a8200457e64be4ff62821f7", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.1.0.tgz", "fileCount": 14, "integrity": "sha512-FZzSo36h++U93vNWZ0KgvlNuZ9pnDnztvaM7P/UcTx87aPDotG18bXifkf1Ji44B7k/eIatmMzkBapnAzjkJkg==", "signatures": [{"sig": "MEYCIQC1VCMGuxE2R5nxSKCg5JGf0bHlz7XUCjc1GT2UiPUfdAIhAJnd88NxGAJnBmkk7vqns2p/hjxL82PM3DxIbRV//XOt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56YCRA9TVsSAnZWagAA2nAP/39DGRtmBaO2vH9d0yuf\nesH2HrvkjX7XyoL+DMeksabk46j6ZeRghOronQ0zfBtPwXk2pTuQJuNATiEx\nwzIJGRI7gTFEcyx4itLt2jBelZJt76onJxy/BmIIcHCk0DcvWytjUWwr6Xnk\n0f76Im0xUpZB6UnJH2hsdKTBkzclw1xtpb9Zqf178NF+VQcUe8Dogvshuhy1\nIh6CvzYncEstoHjLRJQ9qBtKIQ4fyIGdw4P2SJZbes/1NVwRvvgGjstGMcV9\npxuEzEjNCKjIPvts22Kr6G75mXqnXJve7iw55Pb6APMCNAqHvCUYwQSiirGW\nmqtu4Jf9XK2rHswyvd6c1WAHMEA9oMTA6lSS/PIOnCHxPtbU5whdtIQoLfKk\nqbKv2Bkoehyu5wSp3jAsQfhVFqAOnlS/5K89rPtIHTM0p2F24gp76SQPQZVn\njoeAIPRhpUJvdaNIJ97xNL2tUvN6jBCbXzShOH42qBUX/2n3ykybj0aj/6vx\n1jDvVdf+CmcKrT+nemLTs2Zp6i4dmt8cDiUcxRt5H1gklCWFA7jnHRHqG+l/\nlir7c23drO40GAJ1lhSyWK8q49VvBaBb6nU63fvdpexiKbnBMazD/yWgkipJ\nSdy4Wbv6U6FurfpoaHzOq5hPqwCqQqgCBfrDu75inJ2qCf86dnkfEbOC/x4X\ngjBu\r\n=lEZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"@jest/types": "^25.1.0", "@jest/console": "^25.1.0", "@jest/transform": "^25.1.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.1.0_1579654807658_0.7474794544155912", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/test-result", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/test-result@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b67036655b7f695fcd110f836e5bb985a749ba2", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.0-alpha.86.tgz", "fileCount": 14, "integrity": "sha512-IpoYM95v8fmLV7pnTltvoyN1JHldakskBs6ug20wzBlIvYCnN/THw1TPgSgfUspkHzypRQY2lHitmzIUcbIWXQ==", "signatures": [{"sig": "MEYCIQDTE3teK3LbaoPmLecV067auO/y5R7otS8z/FJZzo6zNQIhALCJWPXSCzvZFvxWldZ9QDGnEf4zFIsSIml8obsr1P4b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5H2CRA9TVsSAnZWagAA4ygP/jSUxtEkJacnw0qNzVRK\nbWHI99wFwHcdabsaESy2F8r5nrzE00raBKKdARJak/oeAxUBB+lbgG9PgS1Z\nzjzXeQzZnuQo3Xl+Ng3msPN7csGA4Aw2ArZNh29gfpjtgeWnbbzUDnoo0NvG\nYFKhsymU07Lj1yAmdsQB1iApleTRW2zL2ktVvhC3TzHEgCSrCKUqtI1FJ9wN\nHT/dQWzAVhbll28DOJmZKHKsuMVpN2Xn3S+wkbpmpfjouEW/9JRHkARqskCM\n/qY9ICwemNp+109lRoAqP2U3EZyT8syZcxRMsFmMXwfv3howPpLYMTNu1eYB\nPqGtp2LfHwFQ5S3mygQKdEkGGydLX5gJPte+8lLr33npY9py2DRColWIwty+\nMxv1EsXKb1I28+stq2PMucfvLmYlg1hfibR97PmD/Npy6KRuXTJhyMrL1E9y\nSoKdiW1DWGIH37vDf7QOKeJQr+eHHitCXgb/DDI8bFa4BXG79xPMKnzYQ2/U\nKuqHSAYssB0THWqusBN8MOJoczH+hR3KnYd765FJav/MRU5Q2K4EVPnMp6T2\nrCL3eVmeJToqbFIpqNy7RVVHTrMuRX5OAQmO50nF+qesd0zfuiBoDFPTtZIC\nxUVS2/uXVviQrRfPzk4UGTOteXOyrRRXdVEzDREtTRdOK0O6N5zxdXJJXTxp\nBhxr\r\n=tUn1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.0-alpha.86+cd98198c9", "@jest/console": "^25.2.0-alpha.86+cd98198c9", "@jest/transform": "^25.2.0-alpha.86+cd98198c9", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.0-alpha.86_1585156598188_0.7019070652264117", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/test-result", "version": "25.2.0", "license": "MIT", "_id": "@jest/test-result@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8c0e50247c4633c92d463c5494a711215f7c1773", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.0.tgz", "fileCount": 14, "integrity": "sha512-FoxHScuV+h2LFFF7I2Me22qSv+Rh1aBBKLvVqWNM0Rkevjil1+wKpri7hQh9NaTk28rAo/iZB1J4n4U75PzGQw==", "signatures": [{"sig": "MEUCIDTN27qxVZJQoUGnI2cwZHnvr2WpSwF4FjwzAViU8m7vAiEAtrXQBBOkRgGBH2G6c7Vreqe3yYtkYs0fwO8k1FUoqWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vGCRA9TVsSAnZWagAAZiwP/iGrNsr0GHh4pcpCazOD\nuPiVXpBHZKoKdLES1mPjyU5ziYUWfPOQngMQjqfWYu//s+ua3Vz46GxFMa6M\nNYQ3zsRFuVf0lzV5y59Ot/MZQyVUk7q6MccCO5C6TF7MFw/M4jNFhScSItEC\nC+7Kghx6Tt/cSfrHcWCv6Jv29ZiB6utHGUnuUxVT1oKINgIPz+1QezkuUBwn\neCQNJJHriENTfRIs1zM5QdgEwPm07d2UoLEKjR8fqzfLHKJh0eroF2l6RhGZ\nnWVNFFYxjM9bReVVocMvTiqoBizTgQ8y82m+rpQnWE6fBLzr9OzXg9titCIH\nSe/8eRNXC/GRWPPIqlATYjgk3jqoFXgnXRMg4Vx1lW2vs+nYTP6AGLtjUb99\nGO5RgDBccR9wwoUfwux7J68TYfJi93Z2rfsGhCu3CMzqzE26Uo0i0HsVW2vF\nzqxgPg8BelMT4huVAxECUr9EEU3zrYlOdjLCHMXqqHVr4OaHZBUdRbVoGRhZ\nJfxnj+PqimV6lv+K1Iz2bSqa2cXglpWTfYaLYdCb+bXc2iQEKFSpkqzM4lFp\njq1i/vsxO06x3/kf3CiXvXq+lLM1RM0WB+ucnVZIZvxqhVobuy5lawORNhFw\nTKyc0LHirFYrSFq6psw4HTDyWsLCbNsI9+tkO3BzkfmukjPVt5iaKG9dij6R\nLWiY\r\n=EfoI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.0", "@jest/console": "^25.2.0", "@jest/transform": "^25.2.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.0_1585159110544_0.9942667378901415", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/test-result", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/test-result@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac7ba0e45e8d53fd8275c6ca342c6c866574a492", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.1-alpha.1.tgz", "fileCount": 18, "integrity": "sha512-he+N4T5QelacUj6p7NRvvKB8ZfO5rSsHk8oyrIbV4ACU/Bqw7aCWIp76TpqyHKL4tQcD0yJP5+CpYUHU/cnEpw==", "signatures": [{"sig": "MEUCIDtJ+hxGx/0HoYpDM2w04wMJSxyL1Ct5Uk1anLSM0nKgAiEA1rFMgVIa+dQ6cbA0D9fUjk3ozv/7kdGkCPB5Q5pgvBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+5CRA9TVsSAnZWagAAanEP/222XC3EcHQAbn14w7aY\nAxo17xZtlwGmAwarlh0uYQX2tSjMYasB3/RjNzB0Cp7qnMtT//q3GotC2RCn\nA2GWjalr7xmB+LBPQBlLcfIq/4m6kt9Eu7mO9YzU5CoP2NfBixadK9EbdUaN\nYjZRrg+E8Y1hob7nLBUfw2NHHY4mjweWpJ/fLUBMXDsZk/IaJHlCa1AlVQBX\nNdKPkRkIlawiPQieB+87fqt/jbUaHpwLl9pHQP2LJ8u4BQwWoSwe1BbkBvXw\nhwNUqMPHD3V5jm3I/c+z2qpWxvzLu7+N8IP4c+9brIpu5iTLAPxCdl0NM5s1\n7P5GEZgCbj2PyhOJBSwgF9tcNoVlsLsDX4CA1/wbwspqQiabvt+gP05n4f93\nG5aPnGwheja9LXBlUSqlY8i+4ItTE+wrK12LbwCWe3RFDr6HVwb0igT8PONH\nZBc0m2tyMyUEMbiTuTGwn3ryU+1ulQZ2A5V6Ovr28S69g8RJqEODlAplc/cQ\n1sQAyGPb1n+1xYMYeusVdivjJDktpzMFOJ7rxqViCVaLUmDHiqVtBo/cxSE2\n96u1p2a7zaU2SHyFEuWM0AvczAen04LbnLpdmgPGZzjoAt2nDMQt8UO/qtkY\nxemJ8B5eMBYVMUtfoKtWhGX92Rw3PcUZKlyNyL9ombWGD2rzF1Kxs6p+BEL5\nrH6d\r\n=d5YZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.1-alpha.1+5cc2ccdac", "@jest/console": "^25.2.1-alpha.1+5cc2ccdac", "@jest/transform": "^25.2.1-alpha.1+5cc2ccdac", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.1-alpha.1_1585209273160_0.1572287342438763", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/test-result", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/test-result@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af7f457da154820f915c12fac6e40c2a1fd09206", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.1-alpha.2.tgz", "fileCount": 26, "integrity": "sha512-akZTG6LCZ/85uZXfZ9QY4u7QJipUBadk/O2PXMJGaIZICSa5NzMK5RET5wctx5gkAmxcEA9gCNW58eKJ8nGq1Q==", "signatures": [{"sig": "MEUCIQDfRaSsDv5bXdX6udqYV4+vR5RYwd2SGerrOmYOyx/IugIgdDM5k+O4hMDO3bJOSpt4izL3KPa+ZYU+15u+NJ0M5oQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGOACRA9TVsSAnZWagAA5BcP/0ge0EVjxC625V7mmSlh\nBLMLqXTWTKPjao6sQbP0LRBWj48OoKUxfo6hTCG4CUKXUbXtLBnB26h4iE+d\nYotEcQSD17KHO/pXWczFLDdOJX/DTkXCFpD7Wbi/BhkxdBkIgY8yjwAMtS/j\nBX0la/F/JBjgxXK0nD2OvX+unWAmVR9nn9ijk8obO+9LEzQnzHmnyBjGJJd8\nIiEKXUdAECnXhUWrg7lxzer9A6Q+pJ2yQVhY//hO3ovSrvLJI0QjhATwkOfn\n8cu2ioKmm5SfbBlY1/sXZt9k6g5yQClPcx831yRnGHA/JnibFNng9G0tfpQK\nccbWZd3noFFnIhOP1SBEfr8F3eeMF8cwYFnZoXJJ3GMRfLu3OWKONqCh+eFy\nWe6GdveKbMEA3FOrCeIH500Z3zxCNbfcAK+ljnVticM79hzaWrxTO9OvdlfJ\nwErEt3TGFgaCjrnDEXsjK77Br8B87pr4bnG9iSklqdyy/1mrDc8sL9E00wq5\nnVQC2f8p/Z6B/eqv1XSwDLwHyO913JDIt5c/2SqqJgED2K9gvYljHMGb61Bv\nJx2xQKrILmEldeJCdEnRcZot9dvZlULU7th6GzgFB5U1rb0z5q1fLcLoPMWt\nsQz/z/tEh6UzV16yTZhdIR02ELZbfx7u+LrP3UqEp39TPRI1KzN+0sETO05H\npCx5\r\n=S0zV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.1-alpha.2+79b7ab67c", "@jest/console": "^25.2.1-alpha.2+79b7ab67c", "@jest/transform": "^25.2.1-alpha.2+79b7ab67c", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.1-alpha.2_1585210240025_0.8307072709657914", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/test-result", "version": "25.2.1", "license": "MIT", "_id": "@jest/test-result@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dc8d26d4329c055733bd5ad6dc4eda190fbacd3b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.1.tgz", "fileCount": 18, "integrity": "sha512-E0tlWh2iOELRLbbPEngs3Dsx88vGBQOs6O3w46YeXfMHlwwqzWrlvoeUq6kRlHRm1O8H+EBr60Wtrwh20C+zWQ==", "signatures": [{"sig": "MEUCIQCaAFeih49RxcEd1FebcvycZv8Mn0x4pxIUTo1RBtKQowIgbduxpDmN8Vvr8pxuhHuDM1lKfY66/H+wB1xm01xoa18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9mCRA9TVsSAnZWagAA10YP+QBK0OT187M5ZRyGZGcP\nucAo2AxRtmA9DNnCmbw7VZ7oUQkNLvhJ/SLztKeZKQEyDeW0S3FikrsR09W0\njGN61eUi/uGjOVOFb5y+TKfnFsxVyc7T8RTtg4bm0CBOniiwj16Wb4Y82cVE\nxHg1P7RQQW02GHfv6ovvVIM73mouRfZgWERA23BbG0VraCuRcoWOGird3+mj\ndCEvg/upTHeN3JJf/AQ8HArMUHRpjm5pF+QCSLQQ/CXT+2gTbGnafz96a12+\nancRbWk5BpCkwNGtJ/0t18w5mPGM31tfoo1HtiYkhhiJ1ewdkdqLFWZ9icwF\nhgtCxxCMpcrW8IlGUXC/tXiB7cTq2z7q0APYKfgI3SJdzjIm5DKySWfzmq4d\n4Nte7JhOOGIyRynIB7n4RSBJPO9yljp0cBlY4dzUmYdiEetcK0CSY+1w50tf\n8QWSf4+xJe2c6sz+PwhrcFjH2iGn/l8WuhtaUcKIEVy0frhMoR85k1/9BtOn\nRUN03E47Uvq5Irw3XRevDMQf6x5aVBzroa21iQeypHbrLVfN2Wx+3t4UZm10\n7iNdqlhNEKVDnOcPOMnWo9ha+VGQVugUdT8XyH+elmgeYp0O1K0Ab4ZpN9HA\nVooA12WGw84COT+6NivJcFYthyaPkbZrjxvgnm0Rz88S9Kh2zhgpvyPlQBBX\nAV1k\r\n=opYj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.1", "@jest/console": "^25.2.1", "@jest/transform": "^25.2.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.1_1585213285771_0.21426121293788047", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/test-result", "version": "25.2.3", "license": "MIT", "_id": "@jest/test-result@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db6028427514702c739dda66528dfbcc7fb8cdf4", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.3.tgz", "fileCount": 18, "integrity": "sha512-cNYidqERTcT+xqZZ5FPSvji7Bd2YYq9M/VJCEUmgTVRFZRPOPSu65crEzQJ4czcDChEJ9ovzZ65r3UBlajnh3w==", "signatures": [{"sig": "MEQCICPitL35WYxUnzkeKKyofxcP9MR1ofx2/eKNuw8QXAL2AiAYRma8WJY9C4J3ChHykoKWqj0TH3shEiEkHLH+FV5HCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+fCRA9TVsSAnZWagAAXBkP/ApKVszfWdd4YGQqT/0h\nI8+6nGas/hgiKQfIscLUaZwh4wiCswZhM9J+eFiEyqv7jaArlEXmK5Hd3j2I\nDif4F2N6XKQQpSqGg6BwyDdogg+LlrgMHiOHcRZ1Vd0YhpOa37eFsiZImrwV\n78/HYrEw+ATNep05aejo/3cMYF+yWrPapGNIVZcqCN3C0OO3Y67N8gjEPUwI\nLsDKYLdjceTT0zQ0N2h1cE2Dr4fJg1qGJmrMNekHZcoeS1Wv7r5QCI0fv9JX\n1VrMWdGlF24q43BtO9vqFbkr7eXCUzJUne3ixByl5ZYPr1b0Zp/Qyi92RBkR\nyMOwOBjkgk06AABmMynwT2R9UF7weOly0S0j0m+qbsuU47Jp5yTORQuxnkoa\ngGpmU/2gwc5mKYmjg+nwOUW82+gkymcCnQ/be3zJupHT0btDOTvjAl4jzsfd\n7qHXq1BzWWyakRq2wW1MAintbFNl2CSRbUErBNnvyRTSqAuNxAJ7R3VqUvGg\nnN1ABG2c30dIzs2UwSnM9dl8pZN2XP2MMC9tvAGRqq3PuS9B6bRw10/LJauh\naUw/2dl3WFwAkL0DBKEzZAyZQE0ZmA5DsbBSKGpAc6ysrxlFTt3lLkdXrqwS\nKuONzwJx847ZaZa/1OpoJ6+SeZq79jUEjbUnr2myyDDTsaApTqg62Ce8lUeT\nfSmf\r\n=/PKQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.3", "@jest/console": "^25.2.3", "@jest/transform": "^25.2.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.3_1585254303044_0.13237055869774084", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "@jest/test-result", "version": "25.2.4", "license": "MIT", "_id": "@jest/test-result@25.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8fc9eac58e82eb2a82e4058e68c3814f98f59cf5", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.4.tgz", "fileCount": 18, "integrity": "sha512-AI7eUy+q2lVhFnaibDFg68NGkrxVWZdD6KBr9Hm6EvN0oAe7GxpEwEavgPfNHQjU2mi6g+NsFn/6QPgTUwM1qg==", "signatures": [{"sig": "MEQCICK/Fw3WqftMxq+bMqVehXxpx7xAUywb5zcUuiiJ87gWAiAEKRvAHFDfL9a/4IqBguX/yxIUJjPi/3otR1EHY1Miwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPk9CRA9TVsSAnZWagAAgMQQAI4FUkb2pTbjuZ1V7jsu\nXMLT0I+j7WdDVOvXx576x9+LOMoYfvX+1dHkJWrppMqoLD2NRHFII605vZuS\n+wMJ2grKHlvCDpZMTF2UGFJpK/vnPVMll7Ce3V8jdueFHBJ5y4w/r1S4idCe\nWwEvCmAMkgpDKEbbRxZRcaepJuvOSFT4fB7dkGCzkpE50rMhySXwqR4Srcjx\n6ESYHRMaa01/c2A5BfH6U7+VD+unBXxDuMW6ovNXxbiCoTemM595guOeBFHx\nbhQ59h2qElJgYW7JMf3DOdMRWACB59jW5LrvxQUtIg3L9mnFTqPy8y2Ir4Bu\nwOGVWPrAP37Cq7wqsJe/5CiEclPieGNEZ5sdR8r+GcS/WsRXk3drNUXuL8jP\nuIJPGvtHS9FQvUJQWyOWK9CpHDWJXBylpR8gERkmWnNQfj3evYxABpY/HDrD\nnDrZID6FsHEw+suS2q3VzGB/1ex4LfHhct+/a6Gzjc3GbOvo8rK3xkA6DJIN\n2YtBjhz4mnw5hKzFXpgARmfJ1vWIVYTqRQzu+FzMbY9EZHel1flUnTAT/8Pg\nBaoMzQahJ9aZLWnEbuqmeTIGB60jNNQc8+i3uwYsAcc6oG74ON9bCS+e27xn\nfXUUNC5dyHOXrL8eyTFKGd/wN+B71kPrwS/tEpG5U/SKKoCfYvpbaV20NjLp\n37JA\r\n=lJue\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.3", "@jest/console": "^25.2.3", "@jest/transform": "^25.2.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.4_1585510716532_0.09316065047699151", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/test-result", "version": "25.2.6", "license": "MIT", "_id": "@jest/test-result@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f6082954955313eb96f6cabf9fb14f8017826916", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.2.6.tgz", "fileCount": 18, "integrity": "sha512-gmGgcF4qz/pkBzyfJuVHo2DA24kIgVQ5Pf/VpW4QbyMLSegi8z+9foSZABfIt5se6k0fFj/3p/vrQXdaOgit0w==", "signatures": [{"sig": "MEYCIQDZnBOObKAsRZeaceABGbQq8wLwzHViztriO8sFIPIAZwIhANx9+L1SZYy/h8Q7+vQosZY795I2dZGca0YLh5AykZJg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6FCRA9TVsSAnZWagAAeIkP/3VBgRqupwk5+eevHWEu\nV6acTOvJB5nQWkCf61/ZtlGfnM91ZACTT+8AdfRlxC6MNjzH+7RYviVYL3uT\nRfu3Ba0itSU7eaS5IydC55qmFsNX0t9A7+1ykK6wxPzJ1LaNtG0cNEcJfHQ7\nOMzrvKNshK6DXz8mamuQYvpt/rwM+cKXeJa/LXoyQGglzsxmYo85l4iHs2Sb\nIAhgFf9JDvCZBIQ4AN/+wz8VvWgT2bKaQqCW5X/V9LAVIgnmSXQBNeUIDG+R\n9XLXQ5py0B4t2Yo6bgn/25OITx9JBj+Zv9faTcoFSnOrhpI7zqjfz3+eXpgV\nKbASiE48XwUA8POdDfDup9lC8lbKHMBxwwyVqsJMX4PjdHgnBuRptUquLL5B\ngkjvTQIcOcnp1fnSmegEuNZPcYaNbs1gCau7G0K5bxGmzuHCpz6tlqZ7ve7P\nGNvUbn8vAQo6WJI3753IYKIzPuUCrZHGAqNUT/R/Pmwxksj6NtutREL4jZSG\ndy6lQFSXJNQnl7E0mgrKcy1hvAg8o2Q+0xGqmQgvuNKF1hCkRinZm/ElogIp\nbmGXyyPEr1dP/Xw4a3IvdGafxRepftkXieBFNDzRJQZx5ft/y4MscxP2A5VH\nbSlcPnlHZ/NGVlbj45fMkkNoBSd+eKzAphhhT4PXEYcjFozd9S85aL4Sm78Z\nxcUY\r\n=1iC9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@jest/types": "^25.2.6", "@jest/console": "^25.2.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.2.6_1585823364610_0.5541229934561629", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/test-result", "version": "25.3.0", "license": "MIT", "_id": "@jest/test-result@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "137fab5e5c6fed36e5d40735d1eb029325e3bf06", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.3.0.tgz", "fileCount": 18, "integrity": "sha512-mqrGuiiPXl1ap09Mydg4O782F3ouDQfsKqtQzIjitpwv3t1cHDwCto21jThw6WRRE+dKcWQvLG70GpyLJICfGw==", "signatures": [{"sig": "MEYCIQCY89TEzdQ+k8+BDt0B2ZUXojadlU/Y9BLzLPM0B2YxQAIhAKJA7jhu4lhCaN/00CWBZVaE2DTGATd+vjziuS6EFpsv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/SCRA9TVsSAnZWagAAMz0P/1vyF61s2fD/XPoJUSfg\nyEfMffgV950X3EM33UuuzMTuTAdR/eaKZiX9Nf9zQ/aEFTFJnM1l/RZxtmvV\nOnO+YBUIkfXIilrKZe2g9jmiVT4ThZ8JBw8jMvBbRPcqdAGClo9osMI+2jbB\nKr2hOWM7hpixcVmgFI7LzrtqTTDFXzZHDmbQG4MMSMgwhELGqGLyvaRzGVr5\nRfIG6jB9JG9e6yxJXgo6kN8Ah9FC+Ug+30tMKesrAKrvPWIW0PF9feLjgb82\nqgN6zKHgS8yCRBCwiPs9CEuW0mXz3y8iPD4h8DBTy+F/z8NVwQNtaJ5dRoSN\nP42oOBIPFQSjkXzQhXaAKuBinuCLXKDXGrOFhhoYHeJvohHFq6l+HDCpRWXX\nIWxHTlILbnR6VSt0wc0aAt0ZcDSgxvcjHDWRQUruI1vu6SX1/lv5bWXoD3cC\nuEO4xu8KX9U6UApHCb6dEdNi9fGoSz4ZjmTU+2MXHQC9Na/o1hvgdBflpuQh\nG8qvs/PMoVb5hnk9pTeBdwTXBYH04Tus4c0dK9jiCeEU2vPI7Jj8BDZM/3lb\nslbMuvZvM/6H+sVeqEM6q8ARnhRl+EZNJ07jF5tm1FWvySXG0f72s9NCYTeU\nK8ZyW5xUEM77DijMmonHuCk/0aMqUQLVTrSFq5Agcrckf3MdHJzc+fuwetvg\n/jfh\r\n=vtTV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@jest/types": "^25.3.0", "@jest/console": "^25.3.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.3.0_1586352082243_0.517020600691317", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/test-result", "version": "25.4.0", "license": "MIT", "_id": "@jest/test-result@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f2ec2c8da9981ef013ad8651c1c6f0cb20c6324", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.4.0.tgz", "fileCount": 14, "integrity": "sha512-8BAKPaMCHlL941eyfqhWbmp3MebtzywlxzV+qtngQ3FH+RBqnoSAhNEPj4MG7d2NVUrMOVfrwuzGpVIK+QnMAA==", "signatures": [{"sig": "MEYCIQC2iHIujVgxiLzfO1sQspuvK6GB6pcrXsp77Czl7CVGgQIhAPPU9TvaZz2bFHja3ToPZCKSKbiIN9t5icpH5BjQK2yQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMepCRA9TVsSAnZWagAAZHEP/1jEwUrn8qC4uyKVGg9M\nO4k4yu01BGK5RrQjhszWprQLbB59bDOt/02+AI1dRvTuXs+JVw4e+MBbCEaB\njgcWqCy+WJaUhMb1vg+USw/MRnJy9denRSkxhABMnwkV6EhOKqyO/7hDJSYj\nlYJ3Q5lIJeL8CLwfQJvhiOxVRIzP6gBij8Q878b6YNGR0ULNfRjGNS+17sk9\nQqxgKfiezntYGNEVPLfmOrxmQL3WVkXk1yQkj3OByi3F2hkiTqA2hTbSXPwg\nSPkg/58VzyKbbix/23MBYNB77a6Rg/foFsHUeJUokETweq/+4zjbv4TAfLYT\nRWksQGCWV+YvTz1ZdF9cLMH5G61tmIpTeQownivGaO2Anz0LcEuZcaUFrsV/\n1+7qb6UMs8K0c9SKhazFRjZb2zl6DnGx9EJ10uNKaw2pftf41yeBj0L5zJMv\nQwvKLlF/JndMd+DT/6gkgoSWmjH5ZDw7QmfGsnqLhbnp124G4cgNvuinkCqj\npunpzy+7QWv39IjNiHoBKIAdSXGcPxJXNEwFhjr5OvFjo0IaDtH92qxL4f91\nbzCLd9OFUvlYWrMHstMOTvHGa2gvim0R830mjLexOLb5BO3paGdUxz50773m\ni8X9kQUi8iUf4V4zc7qVzgsZFdrpneFy2xhhWTegH4e7WtpJgmRS0qo3ivvy\ngkpk\r\n=mt1+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@jest/types": "^25.4.0", "@jest/console": "^25.4.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.4.0_1587333032719_0.2198621182694973", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/test-result", "version": "25.5.0", "license": "MIT", "_id": "@jest/test-result@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "139a043230cdeffe9ba2d8341b27f2efc77ce87c", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-25.5.0.tgz", "fileCount": 14, "integrity": "sha512-oV+hPJgXN7IQf/fHWkcS99y0smKLU2czLBJ9WA0jHITLst58HpQMtzSYxzaBvYc6U5U6jfoMthqsUlUlbRXs0A==", "signatures": [{"sig": "MEUCIDc4AQ3oYvH/qH4MicSHvtVOAQIhS+KXZFKKvf1uLrLnAiEAq5ZRbJ/SFhv0UFiPoHrP29UXnYC3KUZ8nHDDUfyUi3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfdCRA9TVsSAnZWagAAaD4P/3uP+MDYCGUYXkHrk20I\n/cC077qSq+eN35R0wTZk+0bzVSq7GYL3tJipCl9FIvFBi0Y9+OMNymqiTB0O\n/cSn1iLVNgFKO6c4VqdXqYqr7GPFe9asMj2IWT2wMNEs/jB08tWglk3QZRxH\nvYbjKYOdB48G3503wHCOocLodn4c3T5+dep/gKGI7j0iNrgMyo1Fiz894VDs\nbT3COB11KhOvXqRJIG1jC4NxAoMqGTcTe81R2yAHLdWgUUDpAzUQzboOBc+u\nYTK8XgYcoWCayYRpKDlBYitQ3y04KqdVU/fOfvuWmHEA6TF01ySQUzfP4IrH\n3pgQ4R2Wtyz4thet8hJpxGmAeDjfdjxAGv19F9Qx3LA4tAX2Iq45Sfl7zk5R\njHzKayX2VsxvPO8/8Dd1qiN4mCX4bgQRGP+AUQvjYfpMz/9y9a6vATw1FkgH\nx3/SFntPmKHdqX5aeK5c9tU2kqd299kt6aZkcqctR8vKGZ1egJPPGUhUtvO3\nDmxWErExsr12CUsBznP7kul/aVBsAH9JvEqkaG9K+CR0zj2Mw2vRkKcUL4/i\nT//ftoLzqtWr1QKttlDvJfrzFi80Rpklq226fqykQOYt4V8VCGZQkajs5uvn\nP/f1zWbicg66INE6POlY2LVjxTRlbeAWOD7swfxyEMnxO79dZWnh+zz0o3hE\nd57p\r\n=yVRx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@jest/types": "^25.5.0", "@jest/console": "^25.5.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_25.5.0_1588103133140_0.7879197254413386", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/test-result", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-result@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "643058fca0fde152415b9a9be958a7cbd2192992", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-ezsRWcclCC85aCK0ko5rX3sDrCnjgub0PeYYmVgvRKERc882c76qULPILS/fC41FCx6uwoUJdjCZQKRWqMykaQ==", "signatures": [{"sig": "MEYCIQDuvb7y7jg8tYotLred+782dk3KkCpEPSgcwFrpYU/5YgIhAO6g6seCZzV8Y6YUfLBTcYKL6cNhOw9w9N4Byxvw7Av8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPaCRA9TVsSAnZWagAAl8oP/A/+3BtMYnmdKaVn+lni\nZuleb4naaB107/uDLqr4DRQObBvs1oQI5xS1NFliI0aU7xOv3fhA/EFEkNyh\n7Tr9ikKKyMmI8rhFj0x5REUFL1TFj4fD6HJtCNro+UBqktWdZ88Z8Yk4eXt/\nQLVG6kP2ptOYXyXJLA7zXkkjOZKwwG4yUrAR6m50IYsPLvxNsaPYKR2pAwBn\nHWXdnnBglkpbd2sk4MR1zvtbr76py/V7aeQ3B2gFEcOkGgzoGCTBiMAnfHpI\neW15ornkzJNxOiWF4EXXXHR3YL//D2hQtY6j194jg5r1WEqFaddrnQhRX2+/\noCRM3GD/hoGg19fZsU2FAvZN6nKmBvlt6hNQAjyakaDvfJoryJK0WmBklhpo\n+mFZR7VQJI5eMSc+4Qq62kzxuVagObV2/Ann/Ls3uLJ43nKmMkjPvyqKDiuZ\nLEm2AqLDbR3EhAwD5cWa/+/T46Bep17uO3Q4nGU+gVakaLu8PsPRAqMY13Oz\nXWqy0FpVrV3eU2zbEsi4zhhjO89gGwNrTS0zWn73jbRvkaUC0pjbqtgn+5Kb\nnMIHBaIfvo1Gw+zCrl38twaI1zjAHQreKkznSwOy0i4rmtnSoc2Xxq2/JN5e\nVzmU6KwD/u3WreUt59kqjaA6cwVmDaju4no1viqP1DTnDzItlnttcNgAyycS\nDEm0\r\n=Y71m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.0-alpha.0", "@jest/console": "^26.0.0-alpha.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.0-alpha.0_1588421593803_0.10202408381324224", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/test-result", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-result@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c25e3db662cad86a74ebb109e2e84ca8b0f7d946", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-UCESYIh1bHQVflMjJdvG0X3VIrjHptLgYrD/wtluIFPpRwpkDPkwGk5Abg/j5YE0JUwX8Rlgi3+sOpVQLJqJxQ==", "signatures": [{"sig": "MEYCIQCkU+cGKHaUz0cqukOrtOaJqpqqluu0UfJydkBL+TWGLQIhALDbHJsDcXo5UgRfXGk4fmyynj3ZFO6NIWUEln5mjKDC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHtCRA9TVsSAnZWagAA6cYP/j1P0fMQw7ZkdZ73to6N\nPP+GlYUCKfa+rlgeDo3HjS+MC1v+DQYr/NUFD8Kgqds6v9wly+PzUfb5EY7g\nofMUEOz7gU2TCGR96jm2leBPlbNmzVI87GYSPt4uAm4W4/7vyiNxgNHCB6NA\nVNDMJ2oaa5pFXO9BcX1KIIFKX3WadNuoNNyt9RChKy7etyEJLypLHLmdhrcj\nsvqkBeiFsV1RQyo7xoczv/4jcBLBaAOlOU/0GFmSY+EABy/Y6IazRiexEQJc\nSORw7EShriHOY3H7mt9/GiWssjcR0VssG901f9W6BDIh4Ql6rovvQ6hSpASx\ndjRvAvxrcQDfLbaiLjXtgvrLRuTTg/ysbwdkFWoDvnQ3modtDdDCzQqipKVp\nsuHB+9lGKXDwh0bq0o+aRkEQnPTklVd+JpJRfEvyow6CLQ1c6C8KtWqrG9mQ\nTxz4/XbQdFiGVEqYEKpq7OCJPKCHqzyKcsd4XKlQ5pvRjgKALQ93em3QvPQL\nmCz0RsKZSdNNWslghqjI6tZtZl84QvxLbcy3Ba7zeDIDR3skIiEj+hnpO61d\nh9IFnozR/d7If/ULab/uKHs3UeVSU36+gQsNfWO5SiaLv/kQO7z4m99pfgta\nMngl+IBmUv7Zk04Tn9bLzaszwjAPATs4iPGwQBjgiOekjsptzNqdwXD1m7+f\nicqo\r\n=8YJ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.0-alpha.1", "@jest/console": "^26.0.0-alpha.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.0-alpha.1_1588531693348_0.10453901593768111", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/test-result", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-result@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a62ae7f14a861763b1b6039a9ab6d67976a2f2bd", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-0gRX912ZlSn7h6byVQAW7rXo7jCvgfa2Ocwkd2rdlBu1kuztRaquAtcnqrjbYowuqrGeynjl4QpN1CdvIgBUnw==", "signatures": [{"sig": "MEQCIDHiF12ieGMhnjYGbT49ytFKn8bssX3iTwJUhQLWefhKAiA43TBAw87U76HA3SQc0txDloS/KLJeKfqcKogg9MF1TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1aCRA9TVsSAnZWagAATe0P/2ldH92h0DRd/E3uk4kg\nDKn8SGKznZEGovhhyU2reYzYv97rL4APTqFKm6GOLNKIO0qBGlFAwuSC0EHI\nWz1Vj4a5lVLcJI8Mi0mcv7Ng5Uwli/JHxlXOGfoCuuxQ5QCcTzHsEG4pIj8a\ncct8qTZXIb/vfnVrHXwWZRWL2r5K7O1KScfBeCQON722XkRRiLHYUovbTtC1\nVPfijOejNEuO0RSpdopZ9LhQhk9EccysRFBF+AP5lnCj5PHRcYBxy9OMoU5F\nsaS8yJwMbXSmAnCeeHjcEgfE/ZNhYZ4FHTRvSTJyxR38H/bLJyxJOj5g923N\nnlF8zZb1svGRMAnS4NyVfX6f3FCkxd/KgiFvUa4ADRWs1oczbAdtOB3Zf70L\ncY/aj44xg2DPXZr6tYr/8yVVaT5BASlxwTB3cwWKA7CxYr4i9gmKagmbZM7Z\nh0ZYlgdAKJfzhOW6GgPxvQpdDIluWOSWs9lBXUC32yrRwZzK/rP74i7W0kiY\nKvLIAxnQNOMf9SISkjlI9GPx8TxpmodyiFqz4XLB9HrLkxdFvzEtDz9+GC9o\nV+xBnyPyEvnlnbBTAlZIJ1/XVMN4Nn9VqB/haO+o7kamP0UhgDg/suigrDHx\n+5HDzH3UpoqZnG+9L49DbNhP1eYY7kzlWOeEprnLunAV+CEAXafQkXbAba5f\nlfEX\r\n=c1SV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.0-alpha.2", "@jest/console": "^26.0.0-alpha.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.0-alpha.2_1588608345936_0.873043872400892", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/test-result", "version": "26.0.0", "license": "MIT", "_id": "@jest/test-result@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f1ec3afe8e020b791b03c1a931070ee79ac60fc9", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.0.tgz", "fileCount": 10, "integrity": "sha512-pAZa5Cvdu5D9W8JHROESrA2w4HyHNkSoyQcZxFUEMNVb0ioeUQSqJ8w8nfsTr7IRU6TR4akpvbSayE1V2FGNGQ==", "signatures": [{"sig": "MEYCIQC+iydOU4kaUippH9T2W+cCxXGr/aup0+UaDJpKgmPYuQIhAIf/cDkuKjjsXS8JJs7CQ7Bo5gaYuMBci2ONZyEtu/5c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaUCRA9TVsSAnZWagAAIu8QAJLklW8ySgeIUtnWUttW\nncwF8oZ6jN9r58jvPEhs/iQ/MTxDXq4rNawpcT0pf0fChDfsymPN8K2FIx/E\nwx/feC1xEE9tV4FoVpTWahtviv647EfJjuxPQngmXdRY1P1On/7F0QHJ0KZf\nXFVbh/oATjIwRR9WwBxqiFvHOtiC7cU6FmuUEqeZsmM/2SpLMz+pAsi+Vhf8\nnqx8tvWJysxB1FhUjhgZaEwdS88EcDhrxa7KusLXzwUpKq0yjcup1S8mUJdP\nhitnfj7yLRnTYK4xD0X1RU0wqlqU4Dg7MkmZlqecIjLVM2Qxt0V7lRa+MVBY\n6yv76eAlE7CLrANl1YcwdQjOaCzY0BIid+mL+TdNqwFTA8eOzyvRBbMZHCpU\n4ZWUC/jIT2lz7ggTxGUxnI1rblD7O80itUp9PoPBYPa7l6jgSJkPScSO09xX\nOmYsHHqNkFTMuPQQm1bCcRpms9S11x6L4R4E5Lpc+Crr9J1m7M9EtXeCon2x\nES6XVS4hqQsE0/kURSE829inb1edwy4xWdJM9lEr5M5cex8lDe1Cu71N+i4a\n6owugmgOeaAOvTC3w2EcHADRHp2fSF7jPvkq5nnx+Lsu08JNeyjquGujeOTP\naqhCW/n6BnNco0TwFZPzzRnBN9rlsq+3D7TQOpEQs0kwbWyy8FpogOW7N2Cv\nZjXg\r\n=t9fS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.0", "@jest/console": "^26.0.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.0_1588614804575_0.994542056811363", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/test-result", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/test-result@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d2b4ad1f789b134eb8b15688fafdadd513e07fe5", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.1-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-H0nHQXPRtNhJT7Rg5XljRvzcaMOB5goVEca2XqxsGO03ik3HllcXiwtgl0qRD/SOlCK/artgi8aBdGRt0LZpBA==", "signatures": [{"sig": "MEUCIQDlPAWvJvuZnOY3ECK8kfhB4GjXa5gkp/H04AZcr47lvgIgVbcVKT2dSX6BhtGhlSLToQ4ay24gLl3CxtoUXVSYgLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQsCRA9TVsSAnZWagAA0R4P/21I9DI7Sxt1yfhIfF7L\nEMQwj6ikI89qLUKUySR/1VF5OvA0Wm6BpgGLSbfki/vFFj8IfnfjIgPfBTVv\nw2pkEo88TLHivSAX+rLv6UAxkImWL6eHQJ4sbtmhHqiQXow7RvEp+m54rtJ4\nJaDwQyTsX8CWF4xJrYx9CSoq6cO/oohBZce0Aqde+SjkUVMHq2mLmcsE+U5C\npGFa1ZQBV5LTyg5JbDDujbBwSMo2XbG58YNQ/KkZUURH6RcXKyzq8Bx+ver9\n0y5vMiXPf9xkoGubP/enySmWrHy6qh17aNtE4CPQOm8MbOP7gJMMGaKkM6xh\nvnNDQlWLYKcbneKD1uz8Hcokb4XQIz5DVNNOOdv4ERF+uCJ41ux42rwZykiE\nyXEA07hqTi5Str8EHtCkWCjyq1X/83uK0lefk7yjfxo1q3vvbyIEDCWK3lt8\nnMaR+jokWEA9I8ti50TGHjWGh4Cw+QVinQ/6P8hNR/okK3LDTI7eCJM+FXxX\nnrr92HW02/wCuydu+W1QRyMb8d/eY2cuqtGFLPmE3fjOloR78J7qru38Et2i\ngbvj2SAzV9aQA0wCy651CMgop3ua+iqVy9+rZPVgPgIZQSVpHAdUBQCYUL9I\n9BR5ne0Xx7BrTnktdRVRtAnXCLEwnYRLVN0a2lh9xw3jejVsERwnB8EC5ESr\nPfmQ\r\n=2pWk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.1-alpha.0", "@jest/console": "^26.0.1-alpha.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.1-alpha.0_1588630572257_0.6931808745607657", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/test-result", "version": "26.0.1", "license": "MIT", "_id": "@jest/test-result@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1ffdc1ba4bc289919e54b9414b74c9c2f7b2b718", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.0.1.tgz", "fileCount": 10, "integrity": "sha512-oKwHvOI73ICSYRPe8WwyYPTtiuOAkLSbY8/MfWF3qDEd/sa8EDyZzin3BaXTqufir/O/Gzea4E8Zl14XU4Mlyg==", "signatures": [{"sig": "MEUCIBynW9is6QqVq7ej0A0K1MrHy3S+0a2QMWF2+oLfVcVvAiEAhiPNM/Bt8YtOX+FAd3eW0L3kCzh9w431Vc6eb0DrYCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK8CRA9TVsSAnZWagAAJbEP/i7BmMpz9/zh+qREuheM\nQNzhshkUGSQ+QC2TkHRqcYwsMh1pf3K2NGDTUDxbwEdY6tZkHt94/pqA4Q5U\n5hmBBzzFtfsI+og/Zibit7SGgP74oncPjFs2FfcRES3gF2IJ9Xv2KtabjJI9\noa3WaVEglB4qRYQN/RZA8DNahx+IoIkOyR4gKMHYX1lSdKgsNSHUJhc8FnVv\nzrTRw/nR6FPsquN7bdAi8Z1SfWBjJomdCZtfD8hwm86a4IKehNFY/hEGE727\ndHtTb1UWYgFcVFsmt4TZ5W6XBk7jojeTJX51Yet6pCvFKYVgERgJkHw9mK1s\n2e9XpZ4wfcO/W3b5uLjkEd9ofXt6o7HWn1w3qO0aFg7bBhf3jeX8TfWIQJM0\nAXuxrODK3ntI/nRJJSsTW4m38IP+y+MFMsUBVcquZkqJF7Tji7JSeWRCXtvT\nFO2lSO1NkWVLIg7vJMeBm16IMHHhjCdsW4qxCvWzxSy900JQi9UuCoXt58rl\nZOx6QPuslhqTkR6lBUKhwEC6mWODumxYawQtRdMCU4/mjKDqQhK36ZN6ZA6R\nrnNGdVWoCsKjo4UXF73St22g1sxoynLtRpmZ9Js4nG9I5Xdp1o/KUeAg+HVM\n5nTsJphvdvz/pTXwXsrYygBqLs26s90Z/CebmXNjo2g1tw0fBf44hv0g/Ejw\nbmc7\r\n=2kuR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@jest/types": "^26.0.1", "@jest/console": "^26.0.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.0.1_1588675259882_0.023985355731636737", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/test-result", "version": "26.1.0", "license": "MIT", "_id": "@jest/test-result@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a93fa15b21ad3c7ceb21c2b4c35be2e407d8e971", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.1.0.tgz", "fileCount": 10, "integrity": "sha512-Xz44mhXph93EYMA8aYDz+75mFbarTV/d/x0yMdI3tfSRs/vh4CqSxgzVmCps1fPkHDCtn0tU8IH9iCKgGeGpfw==", "signatures": [{"sig": "MEYCIQCzrXgUNbl7m2fLAfGbcClGXeALS+IgUxisjuvxehDEZgIhAKNIIugzQRZ/FJ3/aXIomHDhWTPUvdPzqTwuHnIVcTdz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyOCRA9TVsSAnZWagAAU0MP/jUtJ1/oHAZnbqGrCDU4\nSC8MA3r431Q5AYVxYXSOzcNy7xIq0eqxPicehIBOpApMHh5q+mFmMoihCSB0\nKEvM1nRlhmlUHjM7SpCTghkJKG9VfRPVYDHDfnVKj9OM+f1pKX1FjB4CxIyU\nZqfSU4yoa5s9qPSTSoSk5thQBrLjGs3CxuytfSdN+GfyNcVAx1sPWiQ8oU+M\ntY24vcaSOA5ohxRW+8+8cAnkj+Agz7iq98HTDSFGKhzdW5fkhIO1obfReVeE\ni7FltObBU6T28dksLbcBUNddN5hONwn/gbSSr9h1HobYqrcEE1p/ume5X8Du\nXkcWqlCDfuIQEIdWl0K0M+InaEYZm9HXfsDQfjW9BjuCbiTZ+GD/6mwjUMXS\nRA464XZq8CQaX3mfKpLlWycOsaEwwvBR85oOoDg3Dx6C6ACQS6mAcVDHt/FO\n36yoEqgSAweDEKbSQnb7SzN8GUt+GqKLmp4A7jVFn56w/p1ZMHJU0+WYzHCG\nq50yGBVYdd5ms/YujliyG/6+R5suRDfbh645/aUFK66cAhaSp0y77LCNk29C\nWvmLaXvsPp8LQqS3fdbdEA1wJQYmifiY3XyHGGYx/g2MFoXBvCm2zae4Nw8j\nRp5EvcCd28RxZGMQN/3vhGCmBjwul+QemnFAZ8RwzXxsYpT70uMjalQSohfa\nVh6C\r\n=YHHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@jest/types": "^26.1.0", "@jest/console": "^26.1.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.1.0_1592925325652_0.8801128048558791", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/test-result", "version": "26.2.0", "license": "MIT", "_id": "@jest/test-result@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "51c9b165c8851cfcf7a3466019114785e154f76b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.2.0.tgz", "fileCount": 10, "integrity": "sha512-kgPlmcVafpmfyQEu36HClK+CWI6wIaAWDHNxfQtGuKsgoa2uQAYdlxjMDBEa3CvI40+2U3v36gQF6oZBkoKatw==", "signatures": [{"sig": "MEUCIQDF0dUYS6Sqs0fqi9d347iRk33pfY3z31NP0+LHgBdB/wIgJuKuneLJWbkpUpaTLgYiA+ZY/JuRPBzW8a5hbdlkDRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzrCRA9TVsSAnZWagAAaxEP/1zfBk7aT2KDIxFzuSWw\nC4fuZAiPND+Ymtcwtmpzi4n37TmlDHWse3mCk6Gmnmc2LgVoWzlWOP4zVYiC\nvVpoR+tDSlOxo2+tOAuh9Igl4Mv6ir8DX6lAy1DxYXjKxWMtIj4AnM0z0o8A\ni/DGd/Yv8k4i9nzOTO3ubJEYp2l3M0NWBpDlR0eGm6CulpGvpyDRWEMsbKDT\nz1koqEbAUxrkMxHS8ruGwZYex6LbA3j75ilywzBF0WP4rl6TwdMiyhKm2lbz\nfyP3Dk3r1dAgU3Ja4NgKU5tW1LmbZ58ADILAO7tufCVemv4h+WC/PvyXeVRK\nVuXfvZe04vA6OkA/JIO+xBPCtQG3rCKBneJoUpMCr2sipOBKwG9xC45TaI+Z\nQ62t+pgwz5OA/a+sZjfcSh6aHy7vkYWOD9Z9msl36Y0cDgdUKTuqdtvySjUH\nYhiNVw+ZlqjNuJF4HtkeFKfgHqW29inRhvl6i4DrEWthm718lyWqVVOxroS6\n9A/6iJXKVnJsLO2LgQ9UVe65Oswb4hoMwTuWcIHvMesDiyhcZm52N0FMHmvD\nEcBZIL1F6OSN5OCUD4yYGUC7m6H04x1MahhYooVVUSBmphPVghMAwMZx81Yk\ntGstg6v9oysd93bCX3sQjkHdGXaQdUdwnT2Zp3gAMxfx/4Wtv+GOyBrcTaun\n03nH\r\n=9Yng\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@jest/types": "^26.2.0", "@jest/console": "^26.2.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.2.0_1596103915242_0.05656235936498777", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/test-result", "version": "26.3.0", "license": "MIT", "_id": "@jest/test-result@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46cde01fa10c0aaeb7431bf71e4a20d885bc7fdb", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.3.0.tgz", "fileCount": 10, "integrity": "sha512-a8rbLqzW/q7HWheFVMtghXV79Xk+GWwOK1FrtimpI5n1la2SY0qHri3/b0/1F0Ve0/yJmV8pEhxDfVwiUBGtgg==", "signatures": [{"sig": "MEUCIH51QsD2FF3vs5DlwlnDfTeqS/hOKG4sRC/xNETmzX5TAiEAwWFMJNIO+HvumZh0c+flAEPSsoSEcAezPfW71kncjFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAyCRA9TVsSAnZWagAA4dkQAJu2yqhj1YKOAWbasOzY\nUYYpMCXQu4ntW2kMTIGnoQ8C6vKAyBoraixK7U12cmqPZqrA7zSa5Z9p6nK6\nBzbyq2V/otntz4GKs99/keHlf46kyz6WSQof2Tp2y826tmJH4VpLdGja7d/M\nsTP7U2Bb02TeuITb6djvfU8W1ShT8ZVJcTGYp0ppPceKTKCRWzjOuObVaUqJ\nl8FU2r2xo768pmRT3JO06SPn2+DGRixlowmSf+1wbZGw8/GlCdgaZ2gRRfXm\npIWQ8vKtNJR7IuVQFg9HN963JDZZ7CLkVKith12T+9xv0dnS/oC4nOWTssga\nARnoA+mM51fPj0RB6XKEbnnWX9oLnuOjHPyO3D+agWfqDURWUdlHTw7RCeeN\nPxMFqADj5EozKkYsRq9I4zIDZTI3//ingPRixv7Bh6kUy8OMZGIC3X2550Jv\nHpWeTyXQSizAq7Dc6bQGlIlhhLa7zVKDJ/JOkVhdxmD+Q2H+5TZcoqfRTvw7\nTRqFU2T3ap6IeVmuADohH2GLYQpHpFuJECIskqJeLFwJ8hVJgg6G7/eZDjZl\n+8Nf1zFOTPiBvg6s091YEWxMxigdQqj9qUTK2N8lza85x4psBS8hULv93NwS\n/CDyvnzkOvisivwHipZuflhuOWn35Lc0sIS5NtIfj4ay4A2LFiTnzewNgen+\n9rAr\r\n=stBZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@jest/types": "^26.3.0", "@jest/console": "^26.3.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.3.0_1597059121941_0.34353590986432847", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/test-result", "version": "26.5.0", "license": "MIT", "_id": "@jest/test-result@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d5bdf2eaf12ceddd359c2506fe806afafecc9a9e", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.5.0.tgz", "fileCount": 10, "integrity": "sha512-CaVXxDQi31LPOsz5/+iajNHQlA1Je/jQ8uYH/lCa6Y/UrkO+sDHeEH3x/inbx06PctVDnTwIlCcBvNNbC4FCvQ==", "signatures": [{"sig": "MEUCIQDA6jwbDlJRzSJ+lhR2dlUuytBP1DIdJOosnOmYeLrvLAIgQ9pqRaxH/E5iPTNgKET1AIA4aC4eKTLwFHxBkCD5cz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc9CRA9TVsSAnZWagAAdpQP/RPK9Fd8ZdsKnQxXZllq\njJ0EoizbU430TlvIIIHpodaI+hflDAkAaSINQipqRRZPggehaHYq9+Ca4Ffu\ntY4shEQySahYJZ59Jw+QHHX0O7Cz3JpO92P8aPDf2uYhARCC4GD0Y9oWeW2Y\n4q0nD8STjMO2jsT3ES1TQwgXuf7bNfHQTUVm4PSrUFrhBJyeURtcwFj4BYO7\nAtlDPYxPKOYmAs8SDTkMJOjV/s4rmKi9d3/F+CJnvDI6BMRE7MVuDjy0jcOl\nyMoaoqGNcX743kRZjCCoHBRnI6pNPpFK95trbejcOUkOwW2tg/24MntUBcVq\neb7VpnySWpCcffbLGrRJLrhWQ+KaFUFvSRh8g7oCJMSdyxhEYZtkeZ2fUf6K\n70ZLIchPOHWJrBS3II40F75fLt5BJ6EclcUciT1aYB6lzd+EQnxgNpV6KZKr\nIwxmImkVyFtwsFRN3Xq1C+nim5hqTbWrcNyWmxjZurl3C5YftJw600P/t8uw\n74jf0oRKIcE05nMD62QdwGNrqNYhjT9ovdQy5IhKebOAUcZ2t0aRudHw0FCy\nIoi8GSmu4o2nHXeObZiyhXMIo5+qC40UPYcNz8h0YrVfqD6uxNxPtC7kSlS5\nGNwkT0bu1rVTFNtCf9BkB9ZJFOUJFp6Y/UkQA9zvpLYndRrvhF0o9zFbbWb6\nxJaC\r\n=huI1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@jest/types": "^26.5.0", "@jest/console": "^26.5.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.5.0_1601890109020_0.8391367280493756", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/test-result", "version": "26.5.2", "license": "MIT", "_id": "@jest/test-result@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cc1a44cfd4db2ecee3fb0bc4e9fe087aa54b5230", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.5.2.tgz", "fileCount": 10, "integrity": "sha512-E/Zp6LURJEGSCWpoMGmCFuuEI1OWuI3hmZwmULV0GsgJBh7u0rwqioxhRU95euUuviqBDN8ruX/vP/4bwYolXw==", "signatures": [{"sig": "MEYCIQCKCXl4J8+yXa4Ixk50TYsZUOxz8VVgXpPPzwPK9ISXPAIhAOekJqh4H5Pz+dwjgrmPAiQ7j2+ua3m/BTwKcit0AVE1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyGCRA9TVsSAnZWagAAyN8P/1c/bqrhzJ3WB7MNTfOi\nTBEvUTDsXSGtA9dPdjjhCMUFST8GTBZuulmj0kdCfCoCsh7xhOypgYL86HrQ\nawQZmU+eRJe+4o+c0P7ZJyaYgR3qgX9LNA3EiZ+nsQxhWLuNRX0bim5xr52u\n0k/kBjFH0LgwcunJuVruYkQL/gRAyMayusw81jNWrqI3+rg02GpTr/oqvw4n\nRFFC7+uOQcNC9fIYOk3FiDjQ/sMxvt7IJHrpMcbPKEKYGGDK97uIWSAbTEXi\ngPKlCNFCqBBarx4OaWBQEfN7C9+Osv8OY8Xk9oiA05WrgLKU5Jn7hxRTdy70\nGDO9xn5+p6Ikb/cUPBVN3dPx8LR3GzJsXauHeVq8ENPH8LCHE+jjPuTqv1tK\njRtfUgR/Mau5E2+W0OCwJ3rrx3s+zquhuCfkQdZ8+I9nJSD9wyeomujgFAHj\nADmwV7qk8T8pH43lQHi4jxudftn1OmP2StGYDOGGDe2MiP4Tj1VD0AMfAz84\nSeFfMztn0j12WcvNdnm7EB8PGL0xHKqURLA4t3FoIO7pnvixpTjNuvNpV2dc\n0xIozrNq7jkeBHO6WcVwSV5ltNsxO18PyA+v1eIGOI8p48/tm5ujK9SzjulD\nlkFyJ5HgTDT6rAPfFVXDX8NJreoSIlvpri1dub/9gf14k0/VoGFL8kCaBOJP\nakDn\r\n=R8SZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@jest/types": "^26.5.2", "@jest/console": "^26.5.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.5.2_1601981573877_0.23082674132611203", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/test-result", "version": "26.6.0", "license": "MIT", "_id": "@jest/test-result@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79705c8a57165777af5ef1d45c65dcc4a5965c11", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.0.tgz", "fileCount": 10, "integrity": "sha512-LV6X1ry+sKjseQsIFz3e6XAZYxwidvmeJFnVF08fq98q08dF1mJYI0lDq/LmH/jas+R4s0pwnNGiz1hfC4ZUBw==", "signatures": [{"sig": "MEYCIQDFewEgP5wOsuBWlDA5s+8yLoYo7MaAk8esj1NFvnAcAwIhAPaYRe9/e6Mpzoh9/wKaHGjL/q5SPLT3+5Z2AK0EkaC6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX97CRA9TVsSAnZWagAAOusP/1wp9oqabRCAmxxeflx8\nNj4KC+VqfeUwnjnRQ01uZpthaEWtiNkmLepzRNK2QBxm/hCkLkySOXUT1qWj\nlOcEWiSwqNprVtcL8xMTMXO95SmQJbc03PRi5EgKQGe2PS38SqAixK2LdRDn\nER1obuSkmKdLRc6LURR2iNWuclehZpFTL6UwGHZBPAUA/ESPIjWkW3a5aRdO\nAQUJGtDow/sbPg3f4cGn31m7OqkbpIT8k/HxOrKdKCBVFT4RGHoX/UEsVEyD\nB6IwJrn73hPoWFHemuW3G7E0QgnH4NVabLlfpUHBREFeVAnIeZkTZtULFFXE\nA3gOPTNY5L0YLVR7h5ww0uD40M7bDF+yNNX97JtCDt6y425+mfHsg5cHDFU4\nJDUXz50pNZkELIVYtMd1UOejvSJwIdZaXY9VqfIQJ42FSEYYMhJ7+vl6RO/r\nnU5SAOQ38Myz0pBMlJWjxuRP7DVNI2jubT+OVpiXsXvyhasaNcuyaN2FyRXf\nbGqWsfZBm0ViVSGFvptMqvHMZyobqttzG0ODzdWGOcPHhbYneeHMVkxVktvI\n8VI9qtMVCU8PA6HCQbmeIAg8tPeV8gWnjrbrfN/bjQSvRo2YnfgPYNcHJkGc\ng+x1FlUtFa/sx2rc9MEakvmbC6GSYm4KumAUrm/MlGVdKHbvW2AvYXu3pGrX\nZMQ1\r\n=4Obi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"@jest/types": "^26.6.0", "@jest/console": "^26.6.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.6.0_1603108730810_0.6588705725461923", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/test-result", "version": "26.6.1", "license": "MIT", "_id": "@jest/test-result@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d75698d8a06aa663e8936663778c831512330cc1", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.1.tgz", "fileCount": 10, "integrity": "sha512-wqAgIerIN2gSdT2A8WeA5+AFh9XQBqYGf8etK143yng3qYd0mF0ie2W5PVmgnjw4VDU6ammI9NdXrKgNhreawg==", "signatures": [{"sig": "MEQCICxFKq+oyGVFYVUno+gRFduhOyggW8C0CcnikjobnJ6iAiBV0LgZg1JQ9ev3DHnmSzVQ/pCTpWTc4XAKebm3oFd+ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0VCRA9TVsSAnZWagAAOZUQAJOeWyMtxlVtvRWfj0bq\nQWMRcjm6V7minDYcuJ1ASOTNVxYuxENDIZpV+GGUNaynn1PqNGFeNyjuv5RP\nxFr6I01/A7YmXS4iiIEw3gsCibBTH54bjk6UA4yXXuNyNkCLMK6xkhUKlUMv\n/7pfA8WFKPOEmFJkaU7apThg9xwCxXwTTsQPwL12AB5xMoK6/SgNZb4U7teA\no8dYCsf5FPnwJAwS5cBlot5Mr6YpizLNFbCuUv4Bvf2Ye66amb+iNf2G8oof\nNUJy0FmuJyT9epbf/8vu3I+wF+Ztysd7GtjjR21/HYhkLsXb57D232npyzHV\nONm0oBhMrrNneBsq4PyKC43hQJhXwzInqOt8K3hkXUx0z2hOvyw7XPD2snpo\nj8WdQ7ukNhcC3CMC/XXem2q2OS7AsgjFLUrpYTQQTGeMSmJrNMRZopMe/5CV\nHHSzurwmC/YVEsfKjPzpWtqQAqSssl0wXu+wnVIVRwMyOsOvDoO2ExOP+Ia+\nLbFlcSpZUdojqm1H/8jA000GH+Pk5bnxobFbRQTDNSEJ3+L/rLftZAeTpkOK\nzrgCv3Fq9bSpAuv1fc0r9S56xTESkZ74mT1DmPSuTLWKZqxePk1fGoWR2q4Q\nV+WG7e2lW5L1DX48YbloxSw/wJef0YYoKP8XywW8Mn64M5bEc1404ydIACeU\n3f3M\r\n=2rQj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"@jest/types": "^26.6.1", "@jest/console": "^26.6.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.6.1_1603443988640_0.8423102700725806", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/test-result", "version": "26.6.2", "license": "MIT", "_id": "@jest/test-result@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "55da58b62df134576cc95476efa5f7949e3f5f18", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-26.6.2.tgz", "fileCount": 10, "integrity": "sha512-5O7H5c/7YlojphYNrK02LlDIV2GNPYisKwHm2QTKjNZeEzezCbwYs9swJySv2UfPMyZ0VdsmMv7jIlD/IKYQpQ==", "signatures": [{"sig": "MEUCIFMylw60DOuOI0Yk/XUtIrtLvRHXbNB6UkEBPAJn6i/AAiEA3XFLCUNTQs73/6RgCyo1lM6lqYiLZtJ3M/SDfQVskm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADmCRA9TVsSAnZWagAA3m8P/3ENrBtwd4wxt8+Zc2+Q\ngwZWwtRaKNa1yURYxiq2Oe4LiK15P66KSPJOTLSl6s3+sRbCsRwWCWRz9spN\nWuv3OHbG0PqwANlcfOKiIdB6H98WSx5PCCwRlfJsJzWjlVv2yD+E4C/53r1x\n6yqNn8FJj3i97eWmjHqdKt2S2Ce3CKQslM+JXNaDOwpEILkemSXMBkIQbkGH\naJfgGyBjXcH9NeTP3+IetTEXeYvcs7U8e7ePsgWTO5MVcJgg80tGktpkdM6L\n4QCAskDVr/Og1inZ6s3GDt/5OZtLY+Rddo5kCDs/93R0l+ZUoLBZKCk58N1A\n+jfkCNLBrvtOnWV6fBpYdxKhqXsXgJJ13npkUyZ267W5YZfmd9wOocfMr1/Q\n0LbRyqIoSxniLSXJRDCupO5X0Kd2kJM9XZGHsG3Yr5TSdRqPLZPJISvYRaWM\nwvHEh7DrO8hmrAebWKaLrA7NmMbLTcE32VRqiu17N5nFECjz/s44XmSefChS\nWyRKwvRcKBqHfiv2qVRl/oVmZH7dG8pGnsYWNdfgIImW9YEcnkm9Ey6Dgf33\nC6jjNGPiB1Uo5ebH8/3E8WqcRmnFGtB1BqBEwuaU8vt6WX+Px++aYQ7y5DR7\n7OsMrHJOdGsRIA16fUu3UmYJMnFqH6Jm1wN5iXBw9xs0vMatX32a/zWQM14T\nCJMr\r\n=ZFHV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@jest/types": "^26.6.2", "@jest/console": "^26.6.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_26.6.2_1604321510518_0.24360304593276538", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/test-result", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76d6c6be53ba6dd3f4853b2984944e600f57e969", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.0.tgz", "fileCount": 10, "integrity": "sha512-SIRZMvluwl2Xp8JNhGJHylXeoy46ae0aJKD5D9RSq6JRitKNW8pZXrYZEtIKfmdmL7LWM2mJ5bG7Mv2ovxizYg==", "signatures": [{"sig": "MEYCIQCwP2K674T5Sx2KEjKbYZVc3b1cwNYQVvFNVcWbZcnm8AIhAKGKRKYD/dd2asTYiocUwonaUK7TnHTi+ORFU6Tr4CP6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KRCRA9TVsSAnZWagAAY8UQAJgV1AFueTEeRGOpMbXO\nyeQSgCDBoCARZKdt9lGfWY+3q5xvEQU+3GQnF8yKA0TJ2h/yiH3081O3J3Lw\n16DyzwF2dzLTBAR6udXVVXF/93sA90zj2UNrnzNzSlE0uLlEXc0TgPLbgCd/\nq0dzcbvr8ocfoPiqtHEcYTZ1Fkxt3EvMHmd7WcxGDJxby9rOieOmtrkQPkLe\nILNsJNyXJcbvWyOOJybUQE1/ZAiMHi39BqyW6zJxy9T+qpEmfC6Aobl14ma3\nyNxNXHKhahG8tWVebEcZR8CCaDzBvkproMhIZt5J84SLgrnXiBog/bvSjgN9\nEn9TBrA/Z4duEUzL1zL/E3t69GF59siY+l+XIqhxwb4pE9SKZ1dOs/K68QFr\nQgUzrCXuO5SmDV8hp/T5R8cJ+YmZ0Qg1N73hi81FeKuvLhj12NPYiJbdo+K4\nT/oBuCoK/uVDmsCgKYorw4vLKmu/WiaVF/pOwhHBFAmM6sZru6LpOVBDv6/G\nWyy3kwu7GyaqtIQMwwMJUzoQJhvo0ugFZh+VHGjW5oZ4KxBhW68YfQJ2TA/y\nCnQXR4y5+Z/Ui1Ouk1JkfrI6zvfuKtgUeqvA+u68PwQfdVa7+79l+ZSx8xGp\n/14KDmF4CHKrfx0S6MPavdCLo7lHvlUTK9p/JCBFRbMeqhqU7iUoTDA+4+wV\nZ2bQ\r\n=KB0p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@jest/types": "^27.0.0-next.0", "@jest/console": "^27.0.0-next.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.0_1607189137463_0.14142082715855175", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/test-result", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4dabf1c3d2e0521a36aaf438b6a6ae0618ce1ee", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.1.tgz", "fileCount": 10, "integrity": "sha512-t1rCTmdmgmU3ALbcTUSW+wI7tArm3BNsUhdFWvZQiIQVGCwpqbR84S8Cb8vcM3OKvGoM/vLl5OmqGf8jvQ0jog==", "signatures": [{"sig": "MEQCICcpPl5VQYvpGvmilhed1BRVnlxRdwNzCgifFhPnPr7XAiA3vC8fq0r3SnnjUWrxDnRhr+Qjcv9Rmz99D8lPGu6LeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziN5CRA9TVsSAnZWagAAFM0P/RITOInkOhS6NOhMqM9R\n+gnPvqd93fXNjn7LJwa/il2EPh5P7It74Bv68K25EZFgEbTMzNbT/ITdSGIz\nt09kmkrcvUIq/EPeCbvSMpD8JjStPoxWVVytIa2fhdE7U/Z/lrWKmOpdDg0K\nyMPeMoErpsV8qk7uLGFU2s/roE4FO8V5FqO+Lv8POVed9S8qcsdmPm1LPCAg\ng+VAQvqiWc+GdYFWSBEEC3sHbZTrPsTcQRs7mYlUMRNpica1AOZYZTVllZwf\n17MB/O/gEcu85PpEy7xGN+1iGJ6oHDKK88yET1wzo/9PjHgf8uBQ8rJLqgVT\nKZP9wblMVF0ykw2SedY85j8s2lrhRm9vNKEBm9tgok5zl97aX1COaDwNPGWF\nZKQ8J6j7f+XzSxRJtnvp0AG1my9L501M8WMd/MTVa/Hb1AebXn68cKSxwzX9\ns0TavpZPKWy72VMi3aXzj3zIILd7vEZZKP1497208ZMPxGGJ53CZfaj4xpiy\nfL+Mz3CZz8IFgYGjjfGaqlyWPzwcVgEGQpEpkKm/f3uTt/obgldK8u0DDu5y\ng3wRjunbZLsc1p6TK1aFQcUd2+hvgi+3v3s/rBL6KfbLrv8ZQHRSOKFYjoJ6\nBkoCQYOXDQ7YXyYhGtH0twX1wyqVODefxbTdJ7A+Vw1B1xLfm5XiAg0lXtEM\nuTGd\r\n=03wJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@jest/types": "^27.0.0-next.1", "@jest/console": "^27.0.0-next.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.1_1607345017013_0.833757957666931", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/test-result", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e4fd80288daaafaef63efb70dfe35a35006780f", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.3.tgz", "fileCount": 10, "integrity": "sha512-Y5faXRKrBgKr3RXRPuahpgjrTzPWobpRJnUT3MhAsLf0QfK8FOerAUZUyjxFx4JmCbM3c+WCvNqlXBP1iOrcFQ==", "signatures": [{"sig": "MEUCIQDblKgz9imGoIH5THtQJFIwJOTChsEE8oQApjKwSgt7rQIgQ8u3ZrKJbyKUvZ4dePspo1/S/xHRRVYecaHsd9er+Xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuXCCRA9TVsSAnZWagAAe0oP/1M7rONaO2xmtazJDmVH\nT6Ahc6ouiIhy3H+UjKW0gyA4zut8SGHX8dysKMMQ8j+fCdbidXmtt3g/UTp4\n0hiSx66zl7J7kVJAa5EdzvxhDZyDKyFLCIj8okfjS001l7nUFTC3zf2uvFXG\nEhu85XTNiLmsBc5ofG5+K3ohX9jh+o/7PwE9gk6t7FvReLBrSTNLfD52+7HQ\nVaB+RPbHCVlwKR15onYNYcIEJiDFdjUQJ3df5qQViTr/THiJWQ8cH6psp8q+\npIuuXLZqzuNCAs59A6wF+xb0dkB1mF/ECS8Fkh0c5hvyVdhaDvJgV3NTz0XU\nfJkg9ovcx4pluIsSoTpn6qOeqz2Xclz4Dce6K51xQaqFTzbXDB/T2JrUnsoJ\n0ugeTRLamaP4uRxIrq4T00rAoC1oxaViHlCmzvYQWzvsCxHAFFEWCfEx4/Yr\nQsILkkD6RumTIAENVBg0Msrqo21pq6VpalOBmjmZTX+JB14Jn7oNoVAAPjJA\nQ5i4a1ucvYfvZI3NJDz9QxWWWq7A9kzsPZn0+o8RD9c7HnjfZ7veCRrxgRx0\n7tarap99/VJzd2v+lEu3GS5nvq9TecofwyUxeBxVTDXlp7rQcXu9pnhmaIV+\nbLT3S5jE/bgQhcCJU6svPOuIRjPBuA/2dxk15WwEQW8GZ5FmpK4yeEHsjqLG\n3C+F\r\n=Tu9c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@jest/types": "^27.0.0-next.3", "@jest/console": "^27.0.0-next.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.3_1613686209837_0.6375241241001619", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/test-result", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01aa71cc8f1637c710e8683356ee2f6814969f31", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.5.tgz", "fileCount": 10, "integrity": "sha512-PyNQFOGzmxQFAvnVort2VsQ4Lnynae6iqpKMTKjvmNOt33cT3VeIM9hRlaA/PZdAlvaHnFR98XQhdNi8st2XrQ==", "signatures": [{"sig": "MEUCIQCIUBRrqcodVdAu6oGeIhNRKiydBw7JS38nhcr7p/qIJAIgY3uIMvnz+fQueEocQpXGVDu+lILZM/7doTzzkzQZj+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1soCRA9TVsSAnZWagAA+2AP/iyGBJ4X/rHgNL68Kb5a\n2UCDeLgYOp0ezMJDUQUhZdmo12HvD3qFIRZ9ZNnjdB6mhm47lSkf213Wx270\n3S9hwXOZwtqXMpn7APtYrx6NkWVpf8W/xrJ6cb+Drza5zZXSnmHfhbtA0GH4\nmnIgeCaxWQfYyg9bgIym+YYjkxZsaJm5efrIkSLMdZ3T614y9u9IeO2ZfJUc\nh1w8mVQm44AqawqC0rLvdhT/iOpcf6G4VqOjgcVvN1AtfPwwBJXOv3C806MX\nglm5n0uTAi5CknTsueia1OzybVEpwIey3J+fCzckiUpcX6TeQWhAW2efoXBy\nsGqfWWSNEypDzwTv1g4IeMG1pXjAxrsBWr1uqJI2zOrUD3vT1xPRP6Ba+PgX\n3iDYjwxjjV+nfziwDrVrKpbeDW0mOmJrvhucZSe0n7SgHV8pse749U8Un0/f\no4VKbv3+YsUPBSNvqan+wIYs7b/RJgCBg4MOvnlHogFhj/QU3K+idLtqXNkf\nUNm73UVcgQVGuzWEfz7bQh1pnWNLnC5P7+P+JgkKkPJMYqU7V0871qoKraZ9\ngkQI0neRtl5rcn4y0acpsRlJF4AOhH2QoAkqyDMAuZa+XUk1emlEjGDGWIQ3\nWz31Y5ug5e2rLIAB7fPgHvhUv1Wx4/9VEALmRxX6daXq/AJnAiiL1XqCw8lp\n2suF\r\n=Q1Ge\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@jest/types": "^27.0.0-next.3", "@jest/console": "^27.0.0-next.5", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.5_1615813415745_0.5355734206743021", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/test-result", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "909a458fb1a1e1a5570a8f15a66e895e32b58232", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.6.tgz", "fileCount": 10, "integrity": "sha512-2Q4EFKMLWlX4xnske89V8aevAPKHgtHLt/CrrE14Ueg7Yb7P47JK2qVPDgsONQOpt/+7XAXeHQOOECsNsDHRQA==", "signatures": [{"sig": "MEUCICDjJWe9CWAtLTGjbj1kFE0st0bumwTWWLh8MDDXOdg5AiEAu6ID4d0QGETZTqIJe1FcBzx7PeNwQR+GmLdL4VE8Hr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOceCRA9TVsSAnZWagAAPe0QAJAuB6hOsKp+EYA8NHb3\nSv+WbjfKZhV5/FJ4K6m70WYnAXyHcRkfPsWMALHwh0WLgGHr19I7Ahx5df5p\nfkN3qCxacuSU5bUOracxYS5RIPH5fIXHIVqEmNftLbgAUlkBPgFWB88WN1uj\n8UW0zkI6PL+CvCe2/00rxwds8g3UDzUiq+JseYUj1ekGzvefQXzFkaBO3MIc\nVkK/udardI3QYPB9pqcy7GgvPVb8vD7dx7XJXhkXz7VxuMQoGrdMJQkaRGIz\nOOwm96w5AG5l3YQ2XsOW6zghjOFDTlBUEVa4u7ZK82RzqOCdOSh16yVY9oW7\ncS6uCbNzk18Aa91CgdbTZejc/Wqj/acXp52zoYfEYNLFozcpuzXxZ1xngk21\nmxfFAWDMF7zKwaTD1cuXrycihc/hlqjvvRdn+8pEPIttDDITm5PWFeEdBuA4\nzW65JSiMeIcKLXGbWYXI6ZtEl779VAcH/6T78npl+hDIIkElwcBwU10ve+6l\nqEQul/m86JISggtu0yYvDcKjK/cJzzshlb1mpTNDCXkWObXyzHsASAUHFuc7\ndFqYHJmuEzxISLIE8VtE3lkY4FBqziF8FUbBZpDcCx0nZ3cNjNbRkgvYs2/x\nDf64AellkBVMtQ8OKwvpK6rU+7B1HqdJdt0bRVvDNtujr/bk1hhzUzQYd01+\nSEB6\r\n=uYb1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@jest/types": "^27.0.0-next.3", "@jest/console": "^27.0.0-next.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.6_1616701213796_0.006067551150209516", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/test-result", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27a00b196543eaeaab1e56bd55e99234cf9967b6", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.7.tgz", "fileCount": 10, "integrity": "sha512-IdFDZBo6arrOgwKNJ2sNtTsXCoWo6hsIJS+UphfmB6FaS2pt5a88HxNHbVCdJGYorXyykTrszQiG/WFZ1A++dA==", "signatures": [{"sig": "MEYCIQDwM6vmsina9Y3yiDiRRtQMkXxVpKtWcP4IllPpI+Ed0QIhAK44Wr6Yp/62x8eZXmXavdMXysQbzhBqAV6eZsUQiAyb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCXCRA9TVsSAnZWagAALN4QAJzOkOlJ/joSpspCaM+1\nQ/jfdObpnc1e3nvnA+i4jfozZkn/HY96oKcxXN2gBgvbrO5dytKu5pPihRcA\nrnXEXNQHjle2Uo0oKmagDNo9Ew/ZfOOilCDHjkbsuhc7fVUwJ24rbtIaMVCA\nu5m5ND57DAVZ7pq9I/ZsthWlCfKhz5NPOw+/MUBMPMfj2CDYN0y52Pg0/bXB\nza7ZmYWtw404Lqy2y9o4DSZEyHxZUYlmTNs8mXejF7OfcDh2j2CdzVuem4LC\nrr/OEl4Ne7JJaKG43wowP2to3tgLn2Iu+SpJ23xhjexHFzdsGuy0j/0e+Eos\nToPwlHpmRZVm1vGEjhSAL3n5lOnJdkT97c1p2DDoJUT6bo8/Gs2xDFRx/VwO\nsfzyWmASbTi5JUiyuJVe8CFS4oPge+yEHKW6iz6+0V7sKWkHCxtbqyYxuRUq\nVYp6AuFMHOaoZVO+kLdoY+GZ/20F5JATcl0BYpTRlUs5/FR8tNwh2z8+qCPu\natVMwT9Z2XLH/s9gL4/O1dKshKnz+oCKHH/9Fw3CRI7FmY7oYzzc3Ema1/XR\n7o0+O635pfyr0+XnOmpgK6VDpwTBUU00Y5IqPil8TxtGIeAu/C/5Hd1qux9o\nAa8kFr41SvJ6RfYrMOGv5xnih6bc0Y/4dM++VWQPfE6jMA3kEi5breLWElGs\ndpsM\r\n=PpdG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@jest/types": "^27.0.0-next.7", "@jest/console": "^27.0.0-next.7", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.7_1617371286930_0.973749782161488", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/test-result", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27458e3ca62bf5fe7b5c0f565e39afb58be2c297", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.8.tgz", "fileCount": 10, "integrity": "sha512-D/zJmk7F63dc1P0im5VmzLBsJEs24870128muooj0fBPrd6KqDrJWm+lMxE5+nojOsYAdi5i0gU+aqkPNYB5Nw==", "signatures": [{"sig": "MEYCIQCDNfivur7X0fgI5ZzJs4ubfZbLQ3EWdnE+kyo24ROtKgIhAPUZF7Z8GVPj75ZqUkiXbPTcGM4WpyzHh43Hi8ucE83f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzgCRA9TVsSAnZWagAA8KAP/3yGbjQBNs48GCh/ZPax\nOujGzwx5HmzENltotJ+Pd8k4YOV/GrZtc61yF/ib+1f61XcarbZzygmR+6+l\nqXT3mqfHE2GMZvoptNkZo7QT+AkyOFM4fuzb3sjhA3rzsNIXTaz8XNk5DIpQ\n8yLXAQeAMtn2IC3LbS0eWwW0/8ynTgBd9lDrGP+7R5EtaVybQAe0UTPpaGN2\n17dLUxlcwUiPEl4LSXdTVOa66lHB+2ysLkswBcH9MV+AkdD1YrFUuJoKij2K\nH0+sfBL8z/KKiOxOFacC3WmKwojJykpR1dHq83kl4LfBrOqS3vN4CFlK/9PJ\nFi7BNKEzw8W2QUUNz11dGIlKZ4rOIhBR2AeP9mTwI2vFFcvHEFyJa/uTaQg6\noYDtKeQO78rtYeGC4rUydsiTPSC/bOl7t9pq309K4XeEgqGMsAVGBArq98w4\nuVZil2dxr5HSmKYlGRCaB+yjuaWk3Q/yQcX7SRQURCwcgprV5gnJNONj4S32\nqXC9ROLW5WTge0PcEK3PzdtoxphFOF7iXeNM4rpqftUBXu/DFQgf2Py/dmGt\nr7dbSa5t0cqJvefKRhoyTmO6Xbd1SoSYCmBzX8rO+MCb1qq+hWU0lOEVJQgj\nLDvqRk85HD5971qvagOQQL9Y0Ut3V6hzb3nXCt4wVGds6tbttuJmROfRmvH0\n5MIy\r\n=lspd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@jest/types": "^27.0.0-next.8", "@jest/console": "^27.0.0-next.8", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.8_1618267360158_0.5892152774252426", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/test-result", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77077b54df1e4ab790a62872c788674ffa7152d2", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.9.tgz", "fileCount": 10, "integrity": "sha512-HvqCSgkIIhAShHUOB8n4AYWgaWbA8ENONcwR98UQSUm1Mkeun9alebmsDLCx5wSxaGrzQyQirkJX9hKu1wqHlQ==", "signatures": [{"sig": "MEUCIATXkf+qEiqNIuTfZtklEQK2igRaM3RB3P7xO1PaQXlrAiEAmXHFu2DIkdGQqKRZXN2gFstSl8gkq0sUCd7i2Wv4Wxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjQCRA9TVsSAnZWagAAC14P/2pM26kjmR4utj3JaA5c\nC317vIoz/NnLd/IArL/HuhLMTQwPVEKtmqquGrF/EHit1xmO4EQThoDQzrED\nTjYkBtyxdip9ZYt4NOcGUfIVhOEHzhPvzB6GUMpMONjZ6pKFJol8AXucHz6P\nnWPpyZZMmgGw3LJqJm6RhSNim3XP+BheU07WB7znpvWn2s+P9EaIPmjVS9G3\n/vqtNfMJQ3VRaRMejZ2P+fEE+bb9oDNs0BfYVYR0RJT5Whx/bkrUDI2NcvuM\nz5F1DRGc+HJRhdjcdHdLtcsEyD0WTI0dR5zjO8CVcfaAOZ0by7815X+h1fB6\nd649vn1QMxi4ZGG/xL0MEgT6+DqtY0Txd2Awlu+u7+W02Y61JKxsMjb3UEme\nLcQzxAp6uKTYt9uwwkqdr38pk28azjYEMtqnrr0yjM2UTUyRBvS+rP2NPk/5\n5mnCPLMt6wKEnCo79FSrC99X0vkyRIz/ASnCC9hYCJFXaelRRTSgTTfwi9f1\nJL84yfMm/5IAuKkOZGAvcA6Y9S8U3KuSYiRwy3bQGFyUZ0/B0/08foP7MleF\n8hazf1sp0ihBK280KSotwl7JYLOI0bF02au4Nh7qT4wY3zxZUxwuFqu1VQwU\nb0k09ZQ1WaIQTfByqC3hCP0Vc1m+kzeYn3+S1Eb7h+RuST8UzFTs68/32mDe\nJjrQ\r\n=lGrm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"@jest/types": "^27.0.0-next.8", "@jest/console": "^27.0.0-next.9", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.9_1620109519594_0.40009396622651794", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/test-result", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ea231f314dd85d31f4a196ddfdbf624dbff4aee3", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.10.tgz", "fileCount": 10, "integrity": "sha512-BF8eSVBJnXlM6luk+/aCHkqrnhgNLDVfOrrbk7uNAtvMatvSIcbkkc2+jUQnprmYi++d21Oejv+P6hc242quMA==", "signatures": [{"sig": "MEYCIQCGi+91zmhkjIcsN6EJN2uFN36A6K9CjwwApSa3Q8baAwIhAK3DZtub46jGcF0InXZw9PNKv1PP24sCNcDkOVCECOWr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4TCRA9TVsSAnZWagAAj6sP/iAyLOXwe/hhnrtvdzxk\n+tRX549VCm+SZyFUdsvWdkN8EYwq2joEiVIdVqVWxQuIKVvCOwY1pc/UeJW2\nDm5myroUuVKFMeWrKC1zDD5utMV26tRNZll+aQtkBTLni/atVu76sDImHcj5\nys7K/rCc6cqIoJ9l+H00BT0UZoCyBmrOiC6w4wROBOgPp5M1XhRjmRkNFhSg\nqwVQPG8xuFp4XAjXlvPYuQWUBvfFO76P+OT4uyVNaR6qObulm5iKhAVhNpqc\n9COVtJ/F4vyfmIlc1OaeSv8PFzcEPXtWsDt3BPOTQJXrFvJFt9GCeZ8ZTHFi\npfQP7kDKTVfEAcyAAH/DpWlGOxCgJTJsLWboLGVeZ1MvGkG0iyWbs+gMk/Qx\nFwX2xnO8ktf8ethCH2dgox+8gutiBnluN0FPI46JPTCIyZqRBhZNYbBZCadu\nvo52LE15nhl9D/0Dm5rJbff3EJLYoFwHy3gUnsTaNXIOMsQE1sgqqZfvz8+u\nJEcVQt4L/2mopy7Epp7hU2I4IL44ZSGNHq1P0khJINN4LYu8EVyBwfrErc7j\nZvsTMvjDfuVPKgNRe2CX6APclkC+kEraxvH3K0D/6mYCkcs+aQd8yFx0zMJ4\nsB7ITf4FW6KORuXujG4drawzufx2ax9nV56jeA7mqS7qn5e2meahBGC3CCyg\ng49q\r\n=zJny\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@jest/types": "^27.0.0-next.10", "@jest/console": "^27.0.0-next.10", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.10_1621519891137_0.6721630401879979", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/test-result", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/test-result@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dc760ff6db9fe3dbeda8dccafb96c3b86d4e5c73", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0-next.11.tgz", "fileCount": 10, "integrity": "sha512-zF6vzXqyLWkcl9tO+N0+pPR+xHjrwBLLJZweR6ZC8EQL+X+nDKdYPKPwzDJZwh6TFf+4WWAw10d0EZHwuWchaA==", "signatures": [{"sig": "MEUCIQCnVr+V8TMURgoloAzINnRF5us3bj5Ik0cESDGqMxZiMwIgWv69Pdthp6UNAR0EizX+qpQ8xnl193sbo6fKxbT3u3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKlCRA9TVsSAnZWagAA7lYQAJbztVKp2hzaInl6Q6jv\nuaKoE19ufifeA+a7QcMUyW946CV5rtarggqrn80HET/pNu03iJfwry2NJzOe\n+nMxGuiso3wB3aWFy3Z9b7ZbKwL0GI+yFlDi4NnA9lQ44smlVPgFfCy8FwAh\n7IB+AW4gy4IAjmyH/7Dm3HtmDAdTEMPoS0+/MHJXrSM2Ni8xZmn26XGveojK\nse5T+NBn/lwx+2m2zAm5cCB24Kl6frKDkKnIJJbHRFeS9YHw412Ya9KaRId9\nI2P765nk9QyzHH3PV1eEALW5SVM5QCn7vtYi2jRjN7hEShn/vtUNHnQnFQmG\nj1AFLUt+kAizdoSULbuWkwXymIKX85fKt1X4JR6ih7ouTV7b+eLvEhmdbw3T\nX7GupAWW30u15+3t3ps6WpOb3qpj//etaWDMTNdQqc3tfKrLcOsZHvrJFIJz\n7uEFfCPBqP0wAZHwgxNPhR1cXge2sfcQv5cUcV0rLEhfkEld6nHUj/ZhdvEr\n3ifXHQd54RGREzBspv92KU55VQ2nypTpOTLJv2POBlgqn+4g2F7nc0Qluo6W\nhFHSXUKTFEaIcema2+HPfC51JpoZamN7m+fEbNHhOah7RuzCBuV4Z+mB4cAN\n43e+/FrnmydlA7xIz6UbgJ4Iw5MDu/YC1OQQtgoJgUU1Znoq/DsW+bqhvuDR\ntL2n\r\n=9de/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@jest/types": "^27.0.0-next.10", "@jest/console": "^27.0.0-next.11", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0-next.11_1621549732924_0.20884120147292196", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/test-result", "version": "27.0.0", "license": "MIT", "_id": "@jest/test-result@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3eec95132213e9c66196fc8e7fee5a95a32a194e", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.0.tgz", "fileCount": 10, "integrity": "sha512-sSpb9ZqAD/JBiEKUSngPaEIC2iNkkWC0hn/Ry8/g2tz5/dReUiblgrNq3obDHr/FsgXMX0cYwIk/RsxVshKnTw==", "signatures": [{"sig": "MEUCID9mY9BaLKtwhUsAzQRh+OwckSJvUc6XJWhwA4tCS7SFAiEA8vluiHOxLNhXand5IFejFeZDl8q4R5RPuPIg+XTHPjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIaCRA9TVsSAnZWagAAnT0P/j5aSWY8ASGOxBu2y1aL\nKFyIwFIh2V31otQhg9Ms8+kTM75gt0u7lKmqxHVkjRRuCbHeBA0rxZ3kfAyL\n09dtvj0JOnwHQETulrE92mFNRLJObNi+Jmz4ZVQD44o+G60boxde4tA1MEgY\nmxTy4bWy3X30xa942Eg3pDR0SPbeh0vGMCCrzBrubi8deEFYqAsxYhFMK14V\n5oMPF/8Ly8PbRmGlhekyPee+KLg1FRloSZITy8E12s/RxvMQrmnVPGBxBSNa\nb0Vl7l+IqJyBqy6dWmSZjzBbquQseHbYzFQXLXDpgiF5yMVGVohu43w2OCa2\nbIfj07dqZutIJgbkqFw2F08WGloRAM7ymmkzfa3pqmiLP37DSV+yR+DNDe5R\nFYv35zMCDgh0Ba6vtuscCspeWdp8YrU50asrGkLC6iHX0bEuZdgLzgvzRR0K\nVuMhP2ADg9XcF4oJiwlLwtm7dJlhByjsJiVaF9t58c/k71JRAwTGlI1LmMGo\nj/OD5+Ny6WO8VIwqoiv9OTFOuA2NpKocFSXx/TMbNk600Pwd0ikNvZsInssA\nM92baPjg7DoL77p/9NqGJSllwdeqIF/r2NddWSefZNMOJMYmKm26fQ9e+2+f\nHt0N9mUgjLwOvXKnSczCzZwPRSeC10kv9ALjNvifE1UEqqwER5ePIxi80a8j\nspyU\r\n=J68q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@jest/types": "^27.0.0-next.10", "@jest/console": "^27.0.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.0_1621930522074_0.9639604492563005", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/test-result", "version": "27.0.1", "license": "MIT", "_id": "@jest/test-result@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8fb97214268ea21cf8cfb83edc0f17e558b3466d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.1.tgz", "fileCount": 10, "integrity": "sha512-5aa+ibX2dsGSDLKaQMZb453MqjJU/CRVumebXfaJmuzuGE4qf87yQ2QZ6PEpEtBwVUEgrJCzi3jLCRaUbksSuw==", "signatures": [{"sig": "MEQCIHPzCfNc4G0y82lpbczdtiXLYnsKMrqNdVfsTalwdrirAiAqm6R/COSIsrXWDG46t+qwnnuTXTjYFPGgVtXfzPV7ZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwzCRA9TVsSAnZWagAA0aIP/jCjRVfB66BMrMwW14ow\n/vaDkZRRUn9yuXs6OHggsJfLKHnX80T5ZpRDLWzCGvnULaHG9Gqd/3+M4rOv\nkEiT0aC1072mHDXulTacJvWejlaAnaNBcpZ6LV55qkoKWl59X0HEW/4oaG0N\n+bzOF61SNLoqy4WcPaXCDtbJXgUfrcOo/sR124sIE6Baw/Su21lBXD7JEv3w\not8tENQEYCAhuw2ZryjbDdFgzhukMFALo7e3GU72Qd021NOCWpKezcyeycsO\nBK+UPa8Op5oOHLFdiqdLpwYWpybdpICCKyMw2T9KvoBaWkklBF4WnNj9JWCL\nMLoayHPdu8o7JkMzp0m3bhgCNZzK9QNRz8ppn9tatqu+vrejsQeaK3G6DnhC\nWFMnNi/SiNwkoGuX8Z5o1F0SeIEdbxKSjPnDa9A/mJfmvC9MNvxovMm/ksYj\nwVNC20QOdR4oKVIF8Gts2Hu4dPW4v92EQAW+6RpCv9wllWb/nCRtqjvPlK3A\nd5pORWhv2O/NHKYd/gumNtf7Pu6u8f/6cxOxHglSKE3tq/EvCixDEHGZS3Na\nr4LO+aXnostEvuoMCswKJoEzb/jB+Igifi5F9lVx2nD5JuKbP/YEjQ2LFlZP\nXkx20LVMfpN4IY0m/pLqVIrwwjEavbpgKvwWwZ4W/Fu4uClaGq5dcBDa7mQ9\n+43K\r\n=+SxR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@jest/types": "^27.0.1", "@jest/console": "^27.0.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.1_1621937203568_0.940932271555053", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/test-result", "version": "27.0.2", "license": "MIT", "_id": "@jest/test-result@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0451049e32ceb609b636004ccc27c8fa22263f10", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.2.tgz", "fileCount": 10, "integrity": "sha512-gcdWwL3yP5VaIadzwQtbZyZMgpmes8ryBAJp70tuxghiA8qL4imJyZex+i+USQH2H4jeLVVszhwntgdQ97fccA==", "signatures": [{"sig": "MEYCIQDFAQg8EYVeFPH7Mr3WfGEDIiCsveZCX9y5d1blHtbFygIhAKwwpQj0P+IplkG7yzP/N96zSyb10QQPmVfg/dAd8xs4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi6FCRA9TVsSAnZWagAAoWEP/3M08p3sPyNLqIj/EU9h\ncIdI4Qy5gDG2G6b7BG50Ig7Y6q3FMOt4Nv+VRXE2VHHTYzwI/dk3W9a0Uzae\ntCFzCkuITkyDbFqB1QNhI1SCvZYNUWhJljYq8EagsxpoxD/vdI82VZ/a4aAR\nN+5mIk0ZEGMF36LvqahnfrcEA9/mMj3tX+QmSm9+tSV12yfM7Z+ZEaYgBQbY\nQ3X1bEsjDD7lvPZ4y+QgewvOFpPtZBB5qXXvdBu8sOLt6x9eEWt9wYlKcRLS\nrBgrQEG9JE47SJaUUhEUiveDzhxOAuMNIsVXu9o0ETUvny/Fcn6uQVoZYeMg\n7wrlpz8LeGqBEb+7RADlAgS2prJc8vMiIsLbLQHNpTC7LKqKJBEEQjKKqglU\n8r9wf0PKKPsrgqKDm6AvfFdLPkZJDCOZy5tE6PzwNDFfvkU7R01R1oec/LVa\n4F1hPTgwiM2KFO7JNTvP7ewRlfnsskaOYylMawlLofhx//8cPnFtnuM/0Jrm\n36gqFvgdPdRyPV5QVZoTUptHAh8ONDXef0bCnnr1IahIuJayG6SkVt2IWWLX\nCnNctGgHVFJZf9nhJ+bL7gxc3BdOqLbXDimuRtC0InI7ZFNSd5FlTNmQbHYC\nDmaRSeOMqWcB57wL+dkP3vt31yXxUr1aLwT4yVLHpkdGy99qG4moaaAU/dlY\nfldV\r\n=wTNO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@jest/types": "^27.0.2", "@jest/console": "^27.0.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.2_1622290052969_0.4132199503965108", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/test-result", "version": "27.0.6", "license": "MIT", "_id": "@jest/test-result@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3fa42015a14e4fdede6acd042ce98c7f36627051", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.0.6.tgz", "fileCount": 10, "integrity": "sha512-ja/pBOMTufjX4JLEauLxE3LQBPaI2YjGFtXexRAjt1I/MbfNlMx0sytSX3tn5hSLzQsR3Qy2rd0hc1BWojtj9w==", "signatures": [{"sig": "MEUCICK2CAOEB73VnL7BdJsXeYIk45APFQpRuBBhIj2eP9YhAiEA70q/INlSYPnUQWFiiph3EqooOSJ7Ax7mPAR4A4FWP38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFvCRA9TVsSAnZWagAAiYkP/RdzBgE5FIM8aKHmjQfx\nEzUOLhx8vR4Ab2peEbab9gM1xS0ToY+0s0prZ//iYFnhdYebmbjLqKHQk+Zy\n12s5aQVOsGE+VSwJlSg4YPNFjR1swlqD6kxLLRiNLKNIkonl9UXgPxGuhNfY\nORxe4p6SoMDXpnevjn/RS2GJmj9gD7Fp3E4OhWF94C300SymVyMFtCC0AA0r\naPQLJV/SHUsp2uyoH9xzUVOmkMqPfKFka/nX4uSnFkB7tjIpSiEqHdJFRk2g\n2TdkABjNk60cbDV4Q8nUKRanw+SOcpuKcUAmqVIC0awH9HekO/ritVISHiQ7\n+eEGn1DEbF8e/QxAAvcIfdpddJ88o6fVUoGvQoWz2Xb8wT79tk90y76t25ll\nqeUM+jgvXYJl0NO12kQ/JX2/GoxV+2Vojk+qESYrQb5m2EWFdGmhavJZchQT\notGT3vk8KgSRsHpfbfUmNwtf6fJHm4/7tlfdyGbrDDpNit94pvR4ccs/DV3E\n6tBejNJpdZOWN77chyben2QQBVxydNHH48lhMeBZj7IMHJDGuEsH9Z/Mf6y/\nAc3XsIVZlmRvAvPtgb0NoODsq/nyFeQ+DlFh9z4QipkBaEacKqWypZPYbkPs\n2Vp72GyaMAKoW9m0S61XO1YajaihVNhivLfxWndWm8h6KUoFsV38lz7L2iFh\nkfWu\r\n=1Hns\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"@jest/types": "^27.0.6", "@jest/console": "^27.0.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.0.6_1624899951303_0.6941586123466794", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/test-result", "version": "27.1.0", "license": "MIT", "_id": "@jest/test-result@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9345ae5f97f6a5287af9ebd54716cd84331d42e8", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.1.0.tgz", "fileCount": 10, "integrity": "sha512-Aoz00gpDL528ODLghat3QSy6UBTD5EmmpjrhZZMK/v1Q2/rRRqTGnFxHuEkrD4z/Py96ZdOHxIWkkCKRpmnE1A==", "signatures": [{"sig": "MEUCIFtVzP1RXnMzATXfqDeVRtEJNOfj+lNW2cUuUmcOlfrCAiEAyTYkPa+z2WPbFsPmoyPMUlzaUuc0u7h6ZD1DumINgwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeTCRA9TVsSAnZWagAA+9MP/A6dj5AxJqiPjF3A2MpW\njQRRfxgZR/XWQqlrP29PtWguVebtFJWlv/3CwbYhN029IanbERB7CuvI3a62\npw5bhRp0QUpAxJ/HwlTRoZ8ll5G02HPoB4J+XIBTmapXZ1JzeXUmgMeyg2WG\n0caimLSAW8lcjNT9vTl+qQrBFhPu5deXuYqzYdWow7RT7ngPd6PCbYmZTNo6\n6SDxe5EHtPJEhbfI1xmycD8m/IHbrDR5TcoNy7pS00NhyzaiTDUXTEIp78ql\nb7pe4sATRIt4hDuTyVx/lyA1MW6R1t/HiEi2b8vKo+Sh/lWWe7seghJT222x\nH2OVs0TCwRnpxe83c+czV4SWvkuHNyoxP06cVKkhq+biSZPUGqMv/0q/Bujv\nfvkEaYXMY4BqQ53ef63U/0Ye63xZmlyb9Yp16wbwoPZPbW307rIsxZgAmrZX\ngWRtTz14I7FLPOsUiPCIrbkFIl6hR87yY1Za9s9kMCelmUcMeePp4h+KZ6M7\nscNwt8M4aZ54s0WTL0KDVFmP/RmVQgDoRSdRzPTuDs8N5jZWHoLpR7gNPlEx\nUskTKUdU1RxX2c2kfjFki4HLUIsLVINo5HJwppHULeKte1IiIwXY5extIWsG\nsvlAVbtfSII/J4COnRK8OPbRhoB9IwtKYloy5W/mOWInQSP+XDK0H7tAN1qZ\nb9IB\r\n=5Xgw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@jest/types": "^27.1.0", "@jest/console": "^27.1.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.1.0_1630058386875_0.8759943766021032", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/test-result", "version": "27.1.1", "license": "MIT", "_id": "@jest/test-result@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1086b39af5040b932a55e7f1fa1bc4671bed4781", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.1.1.tgz", "fileCount": 10, "integrity": "sha512-8vy75A0Jtfz9DqXFUkjC5Co/wRla+D7qRFdShUY8SbPqBS3GBx3tpba7sGKFos8mQrdbe39n+c1zgVKtarfy6A==", "signatures": [{"sig": "MEUCIFTfybcWkK1NBVf4Mj6g8Eg8eJ4kMrzkNqf7Eliw4tDbAiEAxmkUXxmigjw2ubBkfpiRo4lqxrnnuC5Ng/5nnUiE0qk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyGCRA9TVsSAnZWagAABSMP/3Qep+ms2vI4ErZ2+QdC\nsODc0IMY5rOFaxhVFdRlaFrnNwyIpH5lQ7F4TfbJ4nBgZwI/bZ6orj/DFQ4G\nG0KYiWNt+s5odBQvQ2ETxCpic0Bqon59pwVsejTGNG68++OqmlviFdfLdvh8\n/+QoHUImdV7oIQcgtagkw/MCvXf1Wxkcp24moHfG9KIYM3IT8q/TjL9x3wR5\nCS/dpCvtMG7ACTppvayi8q8Fp4xO3aQGDQjimTfr6PVbhr5Bza6qQU/grn2G\nqjrGwg07yxSXTsZkWLaZQBAl8DE7ORSgotURFPPzmAc71Y5ynMBHLgqHHxsm\nFrX94oV7bMfBAW/pYLXRPwBefZ5ut50hBJL1hccNIvIQdV3A+lJJgfjNp1Z4\ntmIaMJnNiFswSxpuPs8gwGJfmakfdE9itCzkfxM+EEjIvjYTBNrzb7KOFlls\nGBFfdhYk0ZsaqnqIiQ5D6ghq/W6nmWUuYymGpa2EMdu8KVpCEnr2y8UdQ+G+\nORRDI5p5b4o8l2gQZ0sb+pLmh6lnf5zOSuLzNwgdVnihlczG0d5cCn4/p2I3\ncV7kF2T8Z20TZjGIpSyk3z309luVkzliv1kZH6eIzlDXTzJ0kVSqDDp6TUws\nRZr6aENOYoRmv/MIFRX3hjsPOIAYZPJ4xBeDRNSZV1xKsjKsi2yu2eOfUVvI\n9yyx\r\n=rG5O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@jest/types": "^27.1.1", "@jest/console": "^27.1.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.1.1_1631095941914_0.6794762139176611", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/test-result", "version": "27.2.0", "license": "MIT", "_id": "@jest/test-result@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "377b46a41a6415dd4839fd0bed67b89fecea6b20", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.0.tgz", "fileCount": 10, "integrity": "sha512-JPPqn8h0RGr4HyeY1Km+FivDIjTFzDROU46iAvzVjD42ooGwYoqYO/MQTilhfajdz6jpVnnphFrKZI5OYrBONA==", "signatures": [{"sig": "MEQCIAyCzXY7bj4uV2USHgpp7cqscxV8XAy09tPxt5vDKuvhAiB917X2OfzMZLP3sadvtZfvzHm6kl2xlEZZcVuQy3t04w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwadCRA9TVsSAnZWagAAuf0P/0RygM+9pRfgEFdwu/Qt\no5gM488BVSqo6dSgaqXoMJr2fr5iwOIUt42lknjjqTgnQJmQOisIP87e0ObL\nYB6CvS9SxXcCZo2Kvzyw1bRt86SC86QWJE+5fxXUIISdxRd0D1ZNOeTy1AhO\nzLFtSAPJnd4HruPzFjlHDzwajmfapVGLoYTaVKkMyrmhyMlABHN9u9XsF/48\nqgC9fhoWCRs3MFADLEX5VovtUvWjzPbkNky5oD4wQ+7DsTx0nFUdgBXRWSDc\ngybIi9nJZ3iVuJ4JYSllizo4rWi+KaLQQSyEspTMxqy/5NmmZfHHMvmfEJgY\nVaQraiamn+IYDdOVNsL6C1NnB2h33OW89IG4IG/9a5qF9KJFe8Pd/1oPRzY7\nk/qZExI0nN8N62kHq+sfFNn3y2Mcfze/WAD08Z8JRWaXlFGW54voTKaHyIdb\nyBwz9jAI/p6LPJRJUpNVyIXY/v3WkoX1qh8J/Nklu2U/VkzsQJ1s6ECUHZ/g\nwD7RX2maRgpGY9Fy7+W/He8jadCMuWTsvqNUpMSOqUVyMrJMCx7C7ySozusj\n5YbabSBvXgHsG2ftFEu5HuafeCtAsj/pM6KLRx5esTwgJrPqSZkgmMxEbYNZ\nA2ILr4RA7enTVtkHKkOf6ZA9u9myz4F6Y3GjTD4o8WF38H/kadlqUxJ+SH+C\nqCfD\r\n=e/IF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@jest/types": "^27.1.1", "@jest/console": "^27.2.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.2.0_1631520412810_0.1102402940957874", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/test-result", "version": "27.2.2", "license": "MIT", "_id": "@jest/test-result@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cd4ba1ca9b0521e463bd4b32349ba1842277563b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.2.tgz", "fileCount": 10, "integrity": "sha512-yENoDEoWlEFI7l5z7UYyJb/y5Q8RqbPd4neAVhKr6l+vVaQOPKf8V/IseSMJI9+urDUIxgssA7RGNyCRhGjZvw==", "signatures": [{"sig": "MEQCIESm8OgGDOdQRZ94shd+WiR5GRXx8l03UIKsTa2tB/DKAiB6uWdBSO+lTXMzUPcPQf0N5WmSS2tBhN0qFNN8mMHthQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.1.1", "@jest/console": "^27.2.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.2.2_1632576911646_0.283749880351458", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/test-result", "version": "27.2.3", "license": "MIT", "_id": "@jest/test-result@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7d8f790186c7ec7600edc1d8781656268f038255", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.3.tgz", "fileCount": 10, "integrity": "sha512-+pRxO4xSJyUxoA0ENiTq8wT+5RCFOxK4nlNY2lUes/VF33uB54GBkZeXlljZcZjuzS1Yarz4hZI/a4mBtv9jQA==", "signatures": [{"sig": "MEUCIQCfXeB1ZYqnXZEbhIz794qdl5Lr/Mq3hlG2MsEsTtOkVwIgcyaNHNYG1GYvZrvhEL3zQym1mBHb37JK/0x3Y1/47h4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.2.3", "@jest/console": "^27.2.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.2.3_1632823887652_0.36629529894251234", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/test-result", "version": "27.2.4", "license": "MIT", "_id": "@jest/test-result@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d1ca8298d168f1b0be834bfb543b1ac0294c05d7", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.4.tgz", "fileCount": 10, "integrity": "sha512-eU+PRo0+lIS01b0dTmMdVZ0TtcRSxEaYquZTRFMQz6CvsehGhx9bRzi9Zdw6VROviJyv7rstU+qAMX5pNBmnfQ==", "signatures": [{"sig": "MEYCIQCZDCy3YrAQOpm7g4YQHULs7Vq5NRBqijcZl5I+2c4MfwIhANM9PAeTq3/VlfSdnFZMUIwt8FVClv+7qj1IfwAlC66x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.2.4", "@jest/console": "^27.2.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.2.4_1632924294882_0.21531392273481043", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/test-result", "version": "27.2.5", "license": "MIT", "_id": "@jest/test-result@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e9f73cf6cd5e2cc6eb3105339248dea211f9320e", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.2.5.tgz", "fileCount": 10, "integrity": "sha512-ub7j3BrddxZ0BdSnM5JCF6cRZJ/7j3wgdX0+Dtwhw2Po+HKsELCiXUTvh+mgS4/89mpnU1CPhZxe2mTvuLPJJg==", "signatures": [{"sig": "MEQCIEcdv2+HwwWf3X1R5ufngwYZ483BHLwW+d9ZE5POzGuQAiBdyPCemMDSK08fMZQqRe3t3o8Fe4OX9xQUi7t8hHZDiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.2.5", "@jest/console": "^27.2.5", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.2.5_1633700367453_0.11517540182598074", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/test-result", "version": "27.3.0", "license": "MIT", "_id": "@jest/test-result@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e093c5d9eb34afa1b653cdb550c4bcaeb3096233", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.3.0.tgz", "fileCount": 10, "integrity": "sha512-5+rYZgj562oPKjExQngfboobeIF2FSrgAvoxlkrogEMIbgT7FY+VAMIkp03klVfJtqo3XKzVWkTfsDSmZFI29w==", "signatures": [{"sig": "MEUCIBNj1V5VPevPB7UHvbdRpFWO7MtPBtM8Uv7edZ/s4bDsAiEA/TBUm3XVohTIfuMGN/QNQ/RpiDdMm25KrkL6CjW6iss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.2.5", "@jest/console": "^27.3.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.3.0_1634495689719_0.47687937161549177", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/test-result", "version": "27.3.1", "license": "MIT", "_id": "@jest/test-result@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "89adee8b771877c69b3b8d59f52f29dccc300194", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.3.1.tgz", "fileCount": 10, "integrity": "sha512-mLn6Thm+w2yl0opM8J/QnPTqrfS4FoXsXF2WIWJb2O/GBSyResL71BRuMYbYRsGt7ELwS5JGcEcGb52BNrumgg==", "signatures": [{"sig": "MEUCIQDCh9G2KyNOnKkPk5dUwgX9ws/KYHXZHCy014U0yO6peQIgZfwW6gkzC88wCA2kANDuMlwzV7nhA4rz+mnH+331sQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@jest/types": "^27.2.5", "@jest/console": "^27.3.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.3.1_1634626655281_0.7664522391748179", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/test-result", "version": "27.4.0", "license": "MIT", "_id": "@jest/test-result@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a42153c270e0c3988557c13e72517186a6c7bcb", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.4.0.tgz", "fileCount": 10, "integrity": "sha512-/RiwMUC9pKK1E85CEflPvb4uE4Zo9JK2Iq3RbkbBoj4FkEASb/Zsqta8WGot2J1GxOk3rqdW513tfSDYQQJVpA==", "signatures": [{"sig": "MEUCICLgFR7IbJZiEbJuv3GVuFO4Cymu6gbeh8uoFMzNjcS3AiEA0zQ8eMEGJtOtmN/kSBZWs+K63N7OYEWQx4OCM6P2u24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNedCRA9TVsSAnZWagAApsUQAJ6zhCWSZaXNVbkbhEL2\nfWTFY5pKqomICTtPlOqINnbpHTr+Srmk4qDOpCCeEx2khpjGvIT5zPJJ5cxM\nVnMNFVI2PCc5PlUd9L4KiAQ05gmdL1U5JA7lTG+5B3QLqtc8GpufZw+oukQv\nEcf5DGtri00kMheJhfmhJLsM7GH3MsTHOi7bMG//mkwdmqrAA7AWybMrtJi+\nMQ0ktU8R/DVwqBVCvvz3RJlE5HDyaTjCayAnKwmr89pFocrAxq7OCyqz6K1W\ngYAcTZqqdaxALQa5tHH22eV4peLrmpJNwwkUfgYagkzvqv+F3qwE4ct1nErX\nA4sb3cfAIIOJsLDekNPsU4YDybtXZpnqcWZif2uJQP9wSJLXJAPeDEEtI3P4\nFIqWLUSxLBl7VXPBmUu7BEuadAPAzy7fHYtaIi6MJw7QsGZw6x075XJNURnB\n1BWhoep9njfUDxrym/XE+CFMDFZtEsltoUU0bZVdKdp2cvwt/F9xESWKLB2n\nIbPhqkgeyWuhWeWMRIBcJt3nAm/vq4+e0Vyvz5yEL7clhufgmlnfTZLdD/I/\n6bjZwx/qZSmzGnXDJDQTSxsWp5YhhXrzjom1pwdtIwuEExuZUsup3vPJSVAf\nmil7EhdeXU3qs6CBa+PrUarEJWociJYAQKq2hs+5m0uQFfqZT24dM8oQ2XIh\nsHh2\r\n=oo0x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@jest/types": "^27.4.0", "@jest/console": "^27.4.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.4.0_1638193053508_0.18957623132164958", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/test-result", "version": "27.4.1", "license": "MIT", "_id": "@jest/test-result@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65956407832ce66afd0740d03cd99a8000c1615a", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.4.1.tgz", "fileCount": 10, "integrity": "sha512-vSua8v5V5j4D/YVGRGHXiR4HzT4GrtZSoL0F9TKHDk7PHD/OGliupaL15M+OC661P4OTQcqyQhWaqYIUkBeN8Q==", "signatures": [{"sig": "MEQCIH9LjTuJDa+MrzzR54K4k2PA2AbLu0dHnK5yU+ns6FSSAiASjoxhj54PS3PHgYLx/x2TfDif7NMJObilorAAM6qCIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK+CRA9TVsSAnZWagAAc5QP/2Q2JEDl5CTDCBs26pIG\nk1mV0h9RS4JXG8/CuA/lREtCbJcE296czuLK7X1t8oCJICNtTrtRg1EecAbs\nHsxkTuoA/69yMfHyGT1dNSQqypOQMTIdlXHP9VWyMYRxjQOKojRCoz4P1GYA\nSslbUqnChIkLQYJU1dTgI/HeDM4zDn5wfuIOO5VUqIgLuktNuNNE5Vjp+b7X\nYoZQZFSCrV1LchPn3YoUtQn+HHtRoJD4kQDvppLH6+rmjHcggRkf/JMmh7vQ\nT2gYC4LKopns5BTsXWNBH3x/ftsLxd66cmhAxv7dx0Bc5lkPIqsq4tx4Lxis\n9gW2561DgDwZ4dP/HbMqrq9ZpLYFixmAVdJMrPqzas2bKVuqtZ98dYNyiOt5\n9jPmPwMmVPF7QAoLPkrn+aDu7BC9m0z1P+oqprXSHHLUGJBKndl4FwYTW9I9\nyCTHFY2bdPfk8lnfJym758P3iclcSvXhGCjEGSe17Mtuz2ysUdaoWC8mdWd0\nT7mqLuM2DxM4bx4t20T0BZG/Pe9hU7mMAWY7Aa3s0oBIKtQinMP7s822MsM4\n43evHNCsoXSqWJZMp4d9i6zE6NX1GVVEilqu6GrXLBvZwlNWZ2PEhWBFhMJG\n0l9Wod27hxfwP3OZjK25Hal55as5jmiSpO5yAJwszDzGBoWGwJrZwBb3cXx3\ntgpb\r\n=E+M0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@jest/types": "^27.4.1", "@jest/console": "^27.4.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.4.1_1638261438610_0.3078023196904933", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/test-result", "version": "27.4.2", "license": "MIT", "_id": "@jest/test-result@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05fd4a5466ec502f3eae0b39dff2b93ea4d5d9ec", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.4.2.tgz", "fileCount": 10, "integrity": "sha512-kr+bCrra9jfTgxHXHa2UwoQjxvQk3Am6QbpAiJ5x/50LW8llOYrxILkqY0lZRW/hu8FXesnudbql263+EW9iNA==", "signatures": [{"sig": "MEYCIQCkmtRBG5m4gloaid3VnZg4bKiyz16RgA2QEv05j4WWuQIhAIuNmPU6X9eKoNYaN0KVCVOaf3kxffevfiSfaDeoUUyf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDSCRA9TVsSAnZWagAAkNkP/3JMXClbi0dxa5axwpGk\nb9qxNQZLEC/QWEodAys/wz3Coi3+u0Cp6tqPQLS1huiSFMaMvTFZERU7L70s\nSxuiN2fwwpe6MvwsVFO8d+uDSZugrwKqYaGKBIoGUKNUZssTsZQ9VR/MzfGR\nQYJaGPNxqsQop2Laz9nJpJMU7kgazFJU0g4x7TV3TBDifTBdmbTLiCobjuZ0\njqpTiyAsW/lGh1LpJKhyXNwxapwGQpdFIq8mluqk4+SBH6c17tVKGyz4Wa3P\nv2fPo331DQ4RE2ewFsPqEjCw6i1hFMYWEaMI9nswbrPQRtnd8nYOeU5l6175\nlcEAlDQoCFTl13iRwltT6lErVDwLHUhgXia461kQfRmv3lPOx81BQf1NP/j1\nihoYdnF4clzTbW+NGV8J0EnsJduL7GEpedmBx/uM6TfhPfjPssmw0LU5NbdL\nTAGNi2l21oDsHULeHbeau7ymultMvdP6+mKK6zc/JNu+H/lAjq+adeKp+YIX\nKVvUWNst05l6Ld5MWRQwLXZl2UWwVrX7+5zkUlPQFLkE/mLtJMOxo34ggwwY\nely745GmV8S8Sj0W0tVoSN1fzIGx9D2ubwWnJOl/NWD/5fFUjpcB+gWISETh\nYFIA4+2inTYZaaqgTFfltkZKd0pAZ4Zb2kctxJLfZQayTeLo4ljW23iXTCBB\nN22D\r\n=Xus5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@jest/types": "^27.4.2", "@jest/console": "^27.4.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.4.2_1638273234299_0.8590743103103684", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/test-result", "version": "27.4.6", "license": "MIT", "_id": "@jest/test-result@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b3df94c3d899c040f602cea296979844f61bdf69", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.4.6.tgz", "fileCount": 10, "integrity": "sha512-fi9IGj3fkOrlMmhQqa/t9xum8jaJOOAi/lZlm6JXSc55rJMXKHxNDN1oCP39B0/DhNOa2OMupF9BcKZnNtXMOQ==", "signatures": [{"sig": "MEYCIQCTYGk2K5LWl6Xj/cS61BB3Ju2XH+ce4XeTYMXS9HfFjAIhAIqPkHQTo2Wfl+wERBfvaeOm0Uk3ZOeflVTl1/LmztyN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJQCRA9TVsSAnZWagAANO0QAJ37pAAWcOHcU6lME2/t\n5QcdLNHf8iQ6omK+7i1QxVaG5BgIzyOQSsWlFoceFyFY+Eagc7X054sSzbt4\nlGD3t9Z/FrCvmaLZcXLUHxf9FQeBMzocO/07qPiyCOP72cTG/dlp364kq5Vd\ny6ZwcMXEswW8i+nmxyEDgz29SIF9McCAEnOZMx5Nv8fz+Lq3KYiqIXCzMp8Y\nM9DHmBdRZFKjGf4zC3XZpKNPqLN8s9c5Qg4MzRA9FtSPwxoFkUYmuG/f5CmZ\nHbCB1WdU5W3VIJPOP5sAFsQ6AfENsAqtA+keX4na44LVUbYkzSwefe5fqLX8\nUWXzugsL+JamPw92VU6YGV5zK5oNe3nTtBKSbt/+P4XDor0+EZeCt+0bdVly\nD9RuGR8RGf7cz32KC0wQxuD7y4y7cdYwMoIqSN8eBbIJtL7vUlC8mC7KgF8i\nTjr8LlYy7A83Y8Ra8Z29/+39oYT8pw0oJhW6DfrFUyFEannXIqqNthwJ2Os1\nz3xmDHEr5SILPAvwsolMQkBVTfhJgFdPdxAk2v+tKbekQxfHXXZprMASa/j4\ntlyk66cwUZq5rQEdGyn0buHGqc9+nxvCiZj+5M+2m1EjLXfaJTEkYFlVcHZ3\nQHORig+/BJxtg+9ON2zEJHH3fDZee2KsIHJphoQwbmnaV7OJSgNM5Ma5wTDr\nsIru\r\n=yirC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@jest/types": "^27.4.2", "@jest/console": "^27.4.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.4.6_1641337424451_0.715323855274826", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/test-result", "version": "27.5.0", "license": "MIT", "_id": "@jest/test-result@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "29e0ace33570c9dcbd47c67e954f77a7d7fff98e", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.5.0.tgz", "fileCount": 10, "integrity": "sha512-Lxecvx5mN6WIeynIyW0dWDQm8UPGMHvTwxUPK+OsZaqBDMGaNDSZtw53VoVk7HyT6AcRblMR/pfa0XucmH4hGw==", "signatures": [{"sig": "MEUCIAt6TPKjr9OfUl2tNsxHG/990OAD591m5tRESoZXvB2NAiEAgA/RjwM0SFO42foL/voh8/yLIMoOV8FVqeE4wsNi4sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp/CRA9TVsSAnZWagAA6FsP/2Nzt2UlQfR4OkdYfTim\nAZ1eEXL14OLomzeSrmz4K+ZrUt0dwr9TWdoKWDCXih/qhWb/Z7zqvd080BzG\n0visfdjmYUL4KyAF4UKjNLSVkRCw/iGvM9UlHf3WtYeEsYegtrku7Wld101I\nEkn6ULJwGmeJANjaz4+Wr+YBElbv+k3QB/cMXlM+oEYuQZAZEqAWBplM8T9h\nNdWCS8hD8B/HVsYbG4vy8ZjnHiAbXJ2qD4q34l30f+DdWTBJA1C5EtMIxiMZ\n8DTOF9hLC5MH+eW4JgIRbQwAz0f2psh7vvUuOFURqL+WGqmfyItPUmfCjjlI\nHHfBKrEw46Kk9EQU/V4ziwya+5sbWfce4s0ibIrW3S7+RQe0dCBtfJ6v/ArE\nQm5UIE1N860mHAwWSwXqyPT9UOcttoMe4KzjPffSqnuEaLfiJaYElhT1BkN2\n9g8zv9tByGi2gyrXu7WF9QovY/WfDosrsT/AOGz5kCIFWp8RLcHycrh/3Jgg\nvQIbOtoDA7n0U48Ta9qtV4hn6Ym3ALh9jWyOZmghvOCZGEpvngtV11dUx6ya\n3qATHec5b7ceryO7VhkbLW6ZJM4hsgNAeaiMNSwyiRyh8khmAYAokM5F84ZE\nB/4qLilvI2/hWlt5n3gDJKw7PLxGYs80CNZyZvTwmQMXDWvIJmk96Thyoq9V\nBJTG\r\n=VHWp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@jest/types": "^27.5.0", "@jest/console": "^27.5.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.5.0_1644055167823_0.3925453958208005", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/test-result", "version": "27.5.1", "license": "MIT", "_id": "@jest/test-result@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56a6585fa80f7cdab72b8c5fc2e871d03832f5bb", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.5.1.tgz", "fileCount": 10, "integrity": "sha512-EW35l2RYFUcUQxFJz5Cv5MTOxlJIQs4I7gxzi2zVU7PJhOwfYq1MdC5nhSmYjX1gmMmLPvB3sIaC+BkcHRBfag==", "signatures": [{"sig": "MEQCIFJAYe8yRBtzwylb+c/BHqNcwaRWjjgE7ffXSBW9hWYRAiAgy+dhzXV/GMCYyxnrTYGwq3k77/NOyCVUjBxPrjkzWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktrCRA9TVsSAnZWagAAWUUP/2lpYm3D52iq6viyFlX9\nLbNylQDXYpJZPYQAswV0RpiCw5AshzmhAfUvOMsV5JuZvB0cmBR9Ys04C8Iz\nJM10d3fVknHupBK7sVdE0e4HCnI48txZEFKnSgm88PMOl7xBukcFLKjASMor\n4BnAwIlw+hkXBp+rNwtQlOD7qkXPb+xgSmVHyluZAuTzniPwujJvD4ajhh21\no8fi30Cx/037M//EvqyhslabMCSjENGi9tn0OhsHeIs/ljrxlHEJPIKuJ7A/\nq5ddTQ9OoS/TD1UwrM6hp6VTYFTtyMRluPm3iqlxASZjsmTOGS73Tx2BcOxe\nYhrGfEylqZ0tU5eoH7PoBR36jG5IaPgPXqiCL4lMNbO+6plXriYkxe1LiueG\nyTgbTiOIULdjAd5e+WJGL+HnRt9PZqdJzy2gK98tBeTTfUvJ+zLjaobTl3hZ\nhvt9QiBtdC/Tmn+i/DgR3drZ06tbbv8GKrfDv3+FDmq9ANjWH6N485EopF6O\njf51VzK2yoaxwpGI5yqrZ/RIde2gFEIuLd8KmEb+SgFUDURLNiACIzMbeU6c\nSGyynEaRuYVwxriUKq9G0vpJnsLLnhTFL69OYolQW14ENmjSYPHDqaq5Hqof\nx1ZYRAyS5JltRHRh29EkIqOiAuQ0wDLAguRYR12XB75G5CvPn+ejSSi6/Cpo\n6qxx\r\n=5kN+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@jest/types": "^27.5.1", "@jest/console": "^27.5.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_27.5.1_1644317547129_0.0050382068819245784", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/test-result", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79d6804feba365ec45be0514e14368ff92bb66f0", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-ca3aTq410Bc5UMi5hPq+Wevlbn9RDYcoAajMCmDeb7zJSrabJrYMlLdn3/LLYSRbM30JiDZXoMDN7urJ+JRvtg==", "signatures": [{"sig": "MEUCIQDI4W73tEKNX3i6nX+U4W5xWV3+xo9aS7Pnoul2d9OmCgIgcCFIG7SYXSRyBH4spnx5/4JK6UOMbRRVFh0WraGvoTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbECRA9TVsSAnZWagAA0moP/iuPNFm8dK2rehVPOAxY\nyPQkqelY61p1bhO/5GzzYPhfH5u4Dma5w0VkL/U9W3DjCwkXwemTFyAdY/3f\n6YUoF+22euXtsGu9fPW8BTw8+p1mtoRWbJec+fCVHgTEWUUb8knPyi9dlCxW\nZtThUIYjOOoN5HAHjuhkUZpTuyu9mTAdn1jYBCaIVQ3R3tIl+cBBi/0aTT2o\n6O14g/CgSRWLwxrZLhUxFk0gupHtxO0tO9m4cZlagd2llD8BQc99AKffCLLy\nvrAVv74F2Zla5Ilopy9LIzE8qRKF14LYlYvIvGGy5xGU0REwxI9peDgUY7d0\nOcczujA9sdH3D1pPAuKxPDx1kKy7voNZjHfe50R6imnhnxryj5YQsDcxTPCo\nJ1YFp1RS9EgtIi6/o+Lc337/dwU46lbvY2t29q2cbUb5loRzZ1yu1asBPZmB\nG3+cfsObSmgGvAKTHgRdPkSKnIhAhUIDlr/bDNbxX15PIshxn2PoV+olcWk6\nROk7VME4FQ58tRM3ttEmTbxZdaMpWoxyBuYQFxMBd+4ruAABsT1O1NGAzLYg\n8kxN7My2LveIUJ8Olx7DSccxUxTl9DvG6HKgsb9n3+nGlr9UUxjKo4oYsdmC\nWjxPo/ZEGklQyh0KY/FjJ7NSJRR32OYeWGqEHUnvcWA7gnVm3k7MiYrjU5cG\nNvzA\r\n=glym\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.0", "@jest/console": "^28.0.0-alpha.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.0_1644517060520_0.17784090380962847", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/test-result", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0bcbe997c6f5cad878c0fc981f7d727d2010dc59", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-1eA/184Ogf+Wq5707kJV1ggkUCXo3qUQbw+RPPO7pTHpf0gi1uW+iTUE2JIk9CRrpglzvZvRyyVdC96KJDFQAw==", "signatures": [{"sig": "MEUCIQC1qwXKbsNWJqF7teDtdNtIuyYnjGW9BdtkE79cnOxNhAIgaM0YCjKIrleeVlheW9E/331ZvJBjSK9YmM9U+wB8tx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqpCRA9TVsSAnZWagAAfSkP/2ffP5Psn1E+JNndArOf\nC+aqf9YdhlKAl0DnMZg2+/Zx31Qld5sDJ1kowRMkSs2+cO6EiFkKGwAJ8cP1\nVloxJxJOlYJuoBeuiHsy7EY+eMHCdobcmVUzVVy1mv5OKNnmLUdO6IWYaZdE\n41bX/YluONajiGpJ79uNzuwuuAbnz+/CfU8EfHTIVgxR1NQ+8xfGLw6cKNo0\nrWTr0B8fgwyPEbYmRC3B7wxHJm8FPId9gwmg4JktVAePECkvT9bV273cUGHM\n3XjkPlH2BUXTtDahrIBSHoBlAyBcCPboPsDkI0qWjWbLNfvok6X0VPAqz/4Z\nUjQG1i+zry6vhfHr73qjDA2IaLeHjbJI+uecZCcj9weT42jy1rYlZN7Mucl1\n24DlxGrPMJMcWUSfxyVWe/LmxmplOK+SHnlPK7geIELSHuWPP6BAFKfP7BQz\nLOl6P2l4xFaKlh9IepYE/2HfdarAIHbMQOG30WDe6ed1HnQRiLAAJISX0Zwc\nJyqeUnJHtbGMp27jdQVq5TZa3sXO2R60gJpM9G1T9EUjGjPd0Bea4L1Woe41\nvWswKY0dG0U3U68GrsZ4tpOgd0EqJLhGpeP5AlsjXN7LB6ekVpHH1VfPVJOO\ndIpMbhZGWBkO/YLMZ0ydY2LwZBuhb3VnDl+sHu7/w/l+A4gYN9KIvzPAUh4D\nJFbt\r\n=8rKz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.1", "@jest/console": "^28.0.0-alpha.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.1_1644960425285_0.02134750292641363", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/test-result", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7e36811c8dfdc8539fa3486b0f800673e987fdf", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-d1aQExQfuDmpqddYYXb1B4tSPleOTQic5pRE7fwTIywY1//7djxmh6BKaJPZ10ManBiOOYrIcBS5ptskbZHy3A==", "signatures": [{"sig": "MEQCIGOfrndDVdbpvT2bLoj4wJAWRXElxyD33PeK45+ntW3nAiA34122yQXNGJ5iCX1HGGFbPDUdxnH9+BcUD2ERHKtptA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5/CRA9TVsSAnZWagAAAkIP/16g5yXESarXnq5Y3PsT\nxQEIDLsruzWctoDsKYeHMtlVjKI/V1mOciXtnW8zyjg2ynFXOW+pssPeeQU2\nsT/bjJRfebuJv9fyMjW7sm8aGVLZId4peTBH2EwXpX2rPAo7isccUDrIwmU3\nfyfRxIK1rc1lRmCubTMZ1kkmaTCttuGufHHcUqTx2Xf5irrxTjjXQB6xOSd1\nOdZCZkQ/TuPaPIo9DEeNgqVYnZLN1EpEQWga97QbNcr204wSsxJjwRJmHGcf\n6bb4hR6O0rvzQc/TRoBJmcZr9HOfwpOkPslgJwDYEZrqIzgxT4TvmrYtYrgp\noUnmRX6Rl3zsCvXrMGPoxWOFWmy0esWfkbXvq0LR7Xjoxn5rA9km8wGoa+pz\nrY9hgSjcYRS7ldMUQBKVpFQHBZPhr5TUgrOIcuPKFvVhV3iCYmzQhH48gFPd\nruutziyFvJ4nCDi70QJ4uUEyLnSG/1OXZg0ZRS2RSHrls22C5ILM5MZPDqWq\nOLteqc5jwZX9CGfCtOKlT9npaPfTPmGrW6rm9BnHCNU68C3WmlXcJRJT3tK0\nRAmPBFMpO2dt3Uk/ZnKAjkRpHecWpm8Q45xp1+oI/WlG0EBJMoMXThCr5Rej\nhA183I9TlrZv/De+kBAlrqfpVQWZQvihFiBF6UUENdDV1WNGIWGcfovfKjHQ\n7TbT\r\n=Y/Ye\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.2", "@jest/console": "^28.0.0-alpha.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.2_1645035135354_0.5117558309870103", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/test-result", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65b4ebde97cf31152bebc5a02834f647c6feff85", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-arbfDgaEshsQTfqfcfLJZIiDr2PLXl/qXPT1cSIXxD5/vrYq2U2m96+K3XA35nbX2aNOBKk37QtkTWsRu7RTbA==", "signatures": [{"sig": "MEYCIQCsqrhp6XjpgjAK57YqWq0nM2oJpzmQ2tP7vekOvhFv1wIhANSpdzhyopd3q3lXWNekty1MuPHn/WtqeI58AxEVckLX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDsA/8CbxmUe2TYg94Mzvp61cusG3Ue+4NsijZpDPYMgdLqQfKofuL\r\nUdA8R3QEoJqUmzeDBgHh+T6o0a95DwYhAqh/I96/JcBmO5BcMJfwppfpCVHN\r\nDVUPwvzdcvzcE3hzg5GRei1fKXvBsX2nr4qRP4hlOvY6lRIqmEOoIoecbzna\r\n7XmNeD6qcI8jRoGDqEz9ftxfbu9K85kups89jtAaK3th5INLbFGZu9CaLT5r\r\ny6VJUJpj+eoD0Y2w7E7UlhEtU8CzcdfjSZsXCVTjuDnnS3EE4b1P1wf+TnJx\r\nY85LDxfCvrSmhRQdo9uVf9t4HYn25lA1Z21BsLqxub/pHxFBKXvOdQpNWZUU\r\nTeLKtnucaT4kGrcqnIFSH1Q163G7voGLhv9dOA1lvD+Ht4QEBmnOzIjlJmTz\r\nTxSgBsQ0VJUHiq645IiRw7wtsdnWEjZI/kCzUP/x6LmjnEVnUbqp182Z3Q2T\r\naSlbA+MJkf77XA0nl29blr8mNslqq2tdHepbWr9r92Hf4Ee1u38JkmBiYBMX\r\nlnS/I3YPFe1rliSGca0qlWaKaRwiK0jWK5C1Bas8BPq1VYjVuzJNvc1TqAYS\r\nKRpWbC4xzpYqVt3X3q6gCXjdujRHrIVN9IyFj4RkG++0/0PI45PB0zkELbTw\r\nrdgpF8JXad7LYFBHe5wb5X3AW2nB/00FQpk=\r\n=mdDO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.3", "@jest/console": "^28.0.0-alpha.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.3_1645112545342_0.8003260703588884", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/test-result", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0bdbdf60fc91b7e0583ba2dd7539a514743ddc2f", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-CIQK1rfUwvgD5/k/0+KOgbSIOjc4Ybq+gRi/ZceSsVtuIQlSkxx5+btPw0CsGfPYFGR/36ayERljvKeH0sDpjg==", "signatures": [{"sig": "MEQCIB3+hyfiAPp7GkI1uA8CuHCZ20rcaqTxkYvOPTeqCT+uAiAnk34YYhl1t6y9mTXEPkiju8kkUp+KtpYknK/zhdKQvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGChAAnxqMrkgeMLcS3+j3mtJY2tup+JWwVsiEX8db9lYyJjNSXrCw\r\nelb3QYidjBAT5/PhoExZWT5PH4aOifGpsZhnYeEM8/gvJyZ41/SBtl0MBEEZ\r\nl/eJVYQkCjjGJCSV+VEIfIDtNZuXloTkL2U81yXspy2+aMGTZ8d9UJpNDUWz\r\nx6zbXSXGyNpugEzVPNplUDmHFap5L7aOkOTk9+lReS8e7g6y7q/33P4vMkc7\r\nfrL1YnUy0z77605EvQI9lEJAosnBLGsGxfJFFxr4Wpe9CiGRaH+83fPWI/gj\r\nYXcx39V+Bu/AbFJ/unf4QNqJpJS3Zz15pE+ibBdeOumoMyACuJQxTWlXoQ0j\r\nn/Xm8Tqe2ieBt99+wXvuUUK3JuwmGxrAO8fpUaHoSKrTBZO62rgPVTFs/JjP\r\nn+V7YCktSobHwdw9cY4FxLc8EVFyJVhHpO5MoczXlOz+/JQvy/b+ftseTyJ8\r\nmVI2fbWVWd3h/4coXXc+2R+EsZ48mg+D+P56g8N8pcLKEyuugd9FbxNf7WyB\r\nASy72wS34212pQ6NuoOnVixqXH9ILWvZfWcOGd2D9urVdgAxVejie55dPuFQ\r\nvVov6Z0p7RNfTwAjgBMFG1IRIHPBahaO+vWCc2/vm2WzQN2Wsx4GLpBGHpVB\r\n+onDnjSMYOM1mkzNC/KgXYIgJEl5y9hKe04=\r\n=KGwD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.4", "@jest/console": "^28.0.0-alpha.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.4_1645532037765_0.6537035348130689", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/test-result", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21065cd8d20c5b33a46ba78ceb36a8367c5af068", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.5.tgz", "fileCount": 7, "integrity": "sha512-JtoFz+Ofo0dO8XQAIHG7C2fVoow6CaTlqGMSXReLEy1BIw8oT63a9yyZdDeSCq88Lq9pBJHoLniRb7clJSL3Ew==", "signatures": [{"sig": "MEYCIQCXQkXACwM5aOSY1HOne/4+PAbj9A83nVr5JgMOsvLyjAIhAImIQ4D4Imc6r36LO1EU6Yy+hI3QYEml0RIpnsZiukFH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBFg//eMSEa1aNFBxk2K3K90Z3r0N3QxsXiZQPSZ0g+7XexVHePn4q\r\nMNwHAVB/YL+joD7JmOwfpgpvIUvjCuiSc+VmT8yeqE3+XPSGKmqRgTh7uiUK\r\nU5ZxcmDVuygEwl7YAHLswdST04OSkBevyMhWhYzrLKjf8WyyGkLyytwMC+Xp\r\no3doNP4ZAdiGfyOuoYqHwHaW+qUOC8fxIDeW78h/A72ZDjtcrtvGPy+ATlPT\r\nqoPT7fc3L7bF6KEaeHia01CF+C6+JuI6kQZly88UtdKLoiTcV0A//GupNlcv\r\n6eouuZIjP4Kz9Cca0nzDKeGNHP4XeQPugpfD4Dn4+7lWeScoR8++XW8zfNfD\r\nIQ6Otbs2v1DO0MgX3n+8uaMS8q00szYEWuRz+TaIju+cw4v9iSV5vZXOZow/\r\nn8MDjiNhuTtBM8FB+kFz3Oc6UnTrR8OLlHP2w9oSjmQdKBoPMcNs60sBd/OY\r\namcaIqTCr6dVBvhecbhR8s1spUdW5FnN1CkLe+ZsjkmXvE02oakLUDKtEaDo\r\nKE9KWjJNIXIRMf4OzS8WdA+jBhLTYtLKCfACoLThluR1wc2jantYj+986sdL\r\nfvOxzj1Qd8cFjg2EemYHMoZ4unYitJ9x44+raDRjNDhzgLylfMw44yZAmFxc\r\nLym5tvpfvXSGY37ZP1xKN5yCIut6SGoh6Ms=\r\n=SJKT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.5", "@jest/console": "^28.0.0-alpha.5", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.5_1645736242892_0.25861272119988876", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/test-result", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b93cc9b8908d15412d2a8d2895f9de191a22378", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.6.tgz", "fileCount": 7, "integrity": "sha512-aabVcJgKCBiErGnmCHwTMPLglxo5yQWjxoYDfoCSW7f5ijUXmYmi3Unnv/wigsqraic1pSOpr3C1FjfHL7IdKQ==", "signatures": [{"sig": "MEYCIQDZmukMfk9rkMLhKMAg7JZfzJFL8v6CnQB5TRF487JpQgIhAP9t8IziTVkYTTzH+hNLe0vzb0sjTtoAKFBMAGrf3/sH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdobACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmTQ/7BoUH6QfIgPT9b4VXEV52kM4RLcRaVuX4z1oplgd0R4L8JRJS\r\nG7yMZOrzCqFZRmKuQAYqCczloXCiM8zSGmgQlPWMNN5UmwWTlddQb9TS910Y\r\nkMk55i820X+3waTesWUsGukvatps31TZogPrtsvzW2tBRI7yxB8au/LTcu5A\r\nsUeMUwUV7gY7GdR4dVJKYs9JN+hH1GRvyvIhKCuqrR+lnyYEJtclw9r4j8uG\r\neybclTLLlJqzDpBS/rU59pUuS3gFsaPDzU4kNRN90WvxYc/w9VvQqtYlG5sH\r\nBu3VDHOcl0Fg7msGaNCVx77L8aEA7GWvFkhQZMO0QJeCUNu16dcxJM8L++hv\r\nh3thfSrvhcl/FU1PPxbJk0WVcc4+J7UDrbChkPx1zC5o+IE6Kv+0YlFdwFLa\r\n9P+0mn74LPmF/8HLUyUq8I7lGeXtnWoStbr9BEwuMf3XBdWyFAG+gjoZ9lJK\r\nBZUC3qNynyNvFRVNGL5YG/WyRi4fSwApAVvComFDWEuXHn6q2BNqs1v5w1cn\r\nb/qgdLp2ZhcWFvaPmcoodqKI9gnxt7clNAhJzV7Zs2oz4u3aC2+/nnKXWyHt\r\nb2zLDPkj/xrSsK42vJ1/Z/4lrowvv3IhS0yPqXEF9b3alsvVGfoKdptdf5hf\r\nuaeK7zwswwzBJh5pchxTcXA5FbD0i6nl3Yw=\r\n=m/b8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.6", "@jest/console": "^28.0.0-alpha.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.6_1646123547649_0.46239728386351486", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/test-result", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "10f799a1ad3d4f5319b15e2352aad880a79b8d10", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.7.tgz", "fileCount": 7, "integrity": "sha512-2EHhFCiKhgp+NFj2OMnm0Jpu8c8X07JU+umTiYaEO9rFc5DCJYTP01MshqIaZUbyY4fBVKb8BhZXfPoDtOd9PA==", "signatures": [{"sig": "MEQCIGguGbuNtIWzAk/MiA2Rv2+VH/tdggOKQKu9s09ahxrFAiBwGTq5El2xpp9qqmudMW02bEbzw3M8QREx3tKGoEPh/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpscw/+ObNhhre7hSthlxz/ahOLohS7G4CHTYXxxyVP5oVehyBd3jQc\r\nDJk62fQ0lbtQG8A7IH1PkX6ZLM26SN08uGvehONVToQjFv4iK7ThTphEaHey\r\n/zuW1iYq2WSGjE2k0kbWJY75WOoMb0vpG2XbpJEx/kgq+hLDFUn9pUW6dKm7\r\nwO9QsaSvPxbjNTQQ+DtZies38V+hsWnCqALCkfje3+JWgYYS7pkwmvWtySSX\r\n5vzi8kznufnOmBZYiA4c+PyDbKxfp6IhXv4VK+Dah2+gsXsb9IsTsj8iH3NT\r\nU9zcK+Yp1a2nRfB/RdgDF9ffA2DANEs0lVCESe4J+WOccDzt09wvsgZtzpUT\r\nZ8dQMJtZeT6fYPIvg6vlPQFxpPFvKXE2+qDG6zKK1s4SppbDiZh7CbZHL0cx\r\nLATbZ0BKJ19YYevfmKFZ4hDx35Ssfhnr7A7UUiEzaJxP4Hj5/m1GaTwOhLcU\r\nfgLMCNqCudBrEWdQLuaRjF1sQeWuzNMXljjjCyCsHVxB6R+z5cU58kjtVvwn\r\nO5wbpLfuRFlZB/KXrFzSeOI9c5VSDj1KW4SdGn7AdR7qR7t+fWlcnO5gBy/r\r\nu/dt4nlKCoTBBhTQYTHom84cEYNrUXbNuD961UKx+QKakyfpuWbximcjU9hQ\r\nxWxz6/qbEoG7nb/it+GH6njT/K0MkGaOz8w=\r\n=vPL3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.7", "@jest/console": "^28.0.0-alpha.7", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.7_1646560963773_0.68644186887419", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/test-result", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "92917cf7ba5944d307c0deb93ce03ec880fee55d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.8.tgz", "fileCount": 7, "integrity": "sha512-nzwtKs5y9NxQCxS9T1OKPu95ORT0EuJVMRPq4RV1d9SoOBIIb9RHQ/dc3iciZXV65iKK4Wg1p4hIqDpzqA853w==", "signatures": [{"sig": "MEUCIAR9lC8Qq8Pg+GCYrv5jY0C4kNmmSkuWITHs8NAgwB0uAiEA2se8ea7cEAmuva7VZlzlYvUt9x6iPjh8tiS2529epzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFluACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqb6xAAnMKLce/cAQ04T5zUgn2e8Te01OLzLAgCYJMDervb3oVUWuc5\r\nZm572zsokrOjHcbYy8/f3smv6bt+X26P1T3ZlE5HmJ2SyDIfqoG5XVoPHMkH\r\nNjaCIPKt0Na01Zf/J/vVT9xALxydnGiiXpctDk7RHUiaRUZU4cWa0qENxP4W\r\nTb/y+WPJhym4B4h42s1wA507Qkcpg1X/H59DYCOo5OQXmML17gi1CuVWxgwr\r\nTN0MVvVYl2D4rFA8PECh5fcXoBglZrjdvaExGrwAL4S/8Ng67LED2j/Sf4gZ\r\nR7kC4i9v57pdiol8YOZB0r8Gi7Z6oLho0IB0u0JQcVX3WURwa7c7ze1uQ/rI\r\n99nHkHAiDc44ifnyPv+b3uLoVtMYyBWU0CrkWb93alLPazCMIOoUmq6Iy724\r\nVcdii416E8fnayYBij4uMq+xFphKJ8CH5Zj4U4MpRNbo/CX8I1+oQWR2q+pM\r\nTSS0NlYAujo3yw5XCtlafLyzEHn54lKhroDXVgryx8PU596vCAEQQSOQVxdi\r\nEhEY+RRXmdEFKNxYRXv+DlW07Tg9Y6DgZDDfLlVrdSvD44uOurIRI6lQW9Ts\r\ngbeYpWdVSD8CrOI54wonpXoGLPWWM9RER/pnzH6kYst2l6v+0qnAonTQdkMb\r\nBguPjf1FDEk8pULuTvUpn+1rTTlG1uhjX1E=\r\n=eMKy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.8", "@jest/console": "^28.0.0-alpha.8", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.8_1649170798410_0.7554910681765858", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/test-result", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/test-result@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5be483b43bde16ee0490347c77a55351c49c250b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0-alpha.9.tgz", "fileCount": 7, "integrity": "sha512-1CHE4GQmitbe5A+3A9MQ78nAE5B4ssSwmESRIQlFndWZIFwY95K3G8LK2GPRS63HbPHlOWeu+vSjMMYzS5yJ2g==", "signatures": [{"sig": "MEUCIQD8sNwl/TmiYVUYk9K27ktdNZSL1Ohn1g4sMw3g+MSLjAIgK9nNRBfk1kMJ/cTcSlRPorTDYB51cdAfGH/WZQOOX9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrh5w/9EO0peDchAxcdxftXMeWI64MkOFhyv+zZaqqgX1P/qiDBIUWj\r\nRwlp3hK/Z8MSA7fREsDN8UEcIaqrHEPuervUpv6kNWqkQ78b3VbYBBnVqrx2\r\n0iQaRdIxIyWjh7SFLYLU1PyBoLYGO/xsCunbl5QhO/lNCqFM/ydIhbn4RXdL\r\nTnaHLQSVTuypYAxMHsryhzhHNVUVoItWsjs9+trkava6eKlXAwpz7lNdbb9p\r\nMTr7BlX6BdbucMDGQ8XSf6PCx73aodjFOGPWh9Tr/whEv8try3ZmLsjx07jb\r\n/7y+xF6WdYNuW3KSa5p9FHbiL7Osplf6JNQKE1mvDFkYoXh6YAV9bAdKm+Uz\r\neK84AIxIQp9C/jeFcNPjac6hgXdS49bR/Y/3rvhF7DtnJSsinUtgIP36alCD\r\niMwp3ydZa64l3HA4J/6g9r/l7BcyJECFHvk6v1N9JjwiAWeRIHJxrk+Ch163\r\nhVy012kP3OFPuwZcwYva2xb6XZ6+KKQcUMkxqMF2yiD2NfGGaqG9S/p5ukVk\r\nj/xQKQJDxmFvW0SpzlEyYTw1OwncW+PkypaftgBmXcXUaobFX4V1r5l7g8DX\r\nc9wyebLo4N2InFjeHglshEaLiserGj3dkH3jABPfYEW4hPIjExoXVPLMQWRG\r\nbKiWzRUtwsopc381r2LpcCrA71GB77yAnj4=\r\n=CB6N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.9", "@jest/console": "^28.0.0-alpha.9", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0-alpha.9_1650365957762_0.6823302653820227", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/test-result", "version": "28.0.0", "license": "MIT", "_id": "@jest/test-result@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "98d801b8ece605e127a9b6fdf4440036900ad55b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.0.tgz", "fileCount": 7, "integrity": "sha512-hd6eS08F9gEAY5kt7Pw7zaIzj31ElKRVHml6pyz+i5s0EzHd0LjnaDwaAqBbbFxrD13HoQOJh8Lel6kvgAT3Yg==", "signatures": [{"sig": "MEUCIQCMNPPVlxwqGl4LDsS0F9N0KcFDQ7Y35mUXm3/hM5EW3gIgXXLKjxLVVCi6Ixja78RAjImo95sA0WIMxjoZCN7cemw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4WhAAjNYJCS7RD4baTwz4QmWAvNzkaNtE8ZuPMDMvGx/Yv4u7zPjb\r\nwJA75GsYh8///dgi6PLJ6QwGbYCi0iB8tHQA0fovk4tr/jgl7XK66W91sPe+\r\ni8KHGNuaPf0qzY0gP5Tir9vZbqMjPMQD6dPslRgqR1mU35RZvblciGoZay55\r\nM05fZqrREt+5b9mwrXhWv1FpbeGCfjo7s1j9/PUsYUzFoLsQmcr21LfVBT9g\r\nUno2JKLYLLpJbVSIgmvEacjAGroJhtxUOTPHCL0h+/Bjmh95aD5u4YDhqlIP\r\nB4QR7hkDkY9rmYWNcE1LNl03013FpTuZFI3l0rVs2WyhJssNdsuhw7TT9tqP\r\nPYX82Co1BBzc+owWIigNzVOahtHlYYY1sr8comZsq8m5kgiILgOE79FzQ+TM\r\nHkROaNAu6Tt82AqKZ+UaBtquIJXWQthuaFM2jJe05YI70ITGQvG/fTvtrN3c\r\nvOX6PfC1OqHpiEe1SxLzB3N0p7CRUWbftqDfraFMw+y1yDAEKrR9QfLiIv00\r\nEgPPhCVvplpfuYutDq91XUWFLI7YmGwjFDPLREs7ukUOe1lEzeCqbs6MSX/z\r\ns1WW8n8TjZu/UmNMvmQf41iCPirzz0nZbCB4Fd2SvPoo4aMenyLeuDib2FU1\r\nIZPnBUBiM5go86zu/GNo0cnJqBA6S1Pl6AU=\r\n=JNsN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0", "@jest/console": "^28.0.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.0_1650888493013_0.5950937125265532", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/test-result", "version": "28.0.1", "license": "MIT", "_id": "@jest/test-result@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4c0eb5d7c3947c70f7ae1a119a9bc049b8465fbf", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.1.tgz", "fileCount": 7, "integrity": "sha512-8LhoEbdIkkYK+PZx6JhfRvI1Jw7tfB77OEJUQwp0diBvXJpjPKeFFWfsbpm7djdXuKoKvXKNzSGgjXDOFbxuhg==", "signatures": [{"sig": "MEQCIDZcY9MF9palN3u3rAqUA4LGCa0Qv3Uxp2AunocqXYYiAiA0iUqjIZ26k2tbrMs8wVdjvdPRYxWZTyFLQTLvEZFSEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof1Q/9HwRUnih2rbhtgHkuA1W1lqgNaQICxBhK4uU22zo8Z7lyXaj+\r\naS/l45lkfeeo1QfMTzrNk/DpXM38yUXxvzoGkK3J+Rm/BWDIt9FBfd/rvpBa\r\nqWiqDPEvqHg7vqnYBS3lsdetZGPCWmPufJ5pdoigBGXPQ7AKzyVc1HlqZiRV\r\nqrEyzdFjtH5kedZd8/6d9GVtvf57jy1Q/aYNvY2d6Bev7DjK/CLaMUI9Q2jB\r\nHtVwxl6+O3rdduPh51PzDD8SCC+A6WrPexpMHQvBgX+iZpCCmK3agyhrVGt6\r\njeHxw0HPo6vOoj7o1gJWdPtplTNWn+O5EMlMLgyPbIIFjEb92jLxxQ5vTqYp\r\nUxThJKn3QZHiA7NwizfUJQtgAIVdaYHREVVCnJQTWP7yjcC6qIaI00d7JLI4\r\nT2QGWl701V1KBVszVLnuOusnI0h+prTEA4DghcoFeqTZFkID08mddlTCschi\r\nDei9XVtvt56TcIOLkC1vjANk7L6V8Dwq0K7H3I21zkZXFNz1gXbnfd2jTlLm\r\nuksbL80WbwRsFlYQ1LJH5yImJKESXDuezdgcnvGYw1ZFwMXdd6AS3aUDLPYa\r\nylTPYoBAgoA53/Xg39P05XKVtd96j0+vJzMY/BccutOiZIDP6C3dP0zniXHi\r\nqVXV7yjyTkSmbt4hheblEaU1iEY76c7G5pI=\r\n=17ax\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.1", "@jest/console": "^28.0.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.1_1650967362875_0.19681402970794526", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/test-result", "version": "28.0.2", "license": "MIT", "_id": "@jest/test-result@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bc8e15a95347e3c2149572ae06a5a6fed939c522", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.0.2.tgz", "fileCount": 7, "integrity": "sha512-4EUqgjq9VzyUiVTvZfI9IRJD6t3NYBNP4f+Eq8Zr93+hkJ0RrGU4OBTw8tfNzidKX+bmuYzn8FxqpxOPIGGCMA==", "signatures": [{"sig": "MEUCIFrpGcxMAx4jch3DeZB7kxlXly4B3xp/9Zt8eqjGrkPYAiEA6WTQfuGc7MUq6ztOBzQVeI76L3ZDgURDOIqZu+rDTGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn9Q/+P5Lr1H/+7LCdIY59UcPk084EV23Qh71cIY5E4AvQ39VO/e3J\r\nAIqt6RF/ZoB6sC+ts90PYbm06Se2GGSZFyZfP8EQaBDdWWDZSq3BK9jWiJe+\r\nSvkdXZ34+Hl+PUMc+L8PDgMpeYh95Tqd0UACmQ4uiCvRkLKXKxFBDvUsWeu9\r\nQp139KW1SYMsgoE/q97osSltW2tUMSFb89BBARKk4zpLPARs2gdy0RSPScln\r\nSD4bReM0v0emhKAeZxNerLA+XEOG1hFlduMtuIh3Af/IgkjbhV4SvZPvMAaw\r\neEb6poM7rinI9rmKTlkOw+iKgiCM2gwvOa4RpQyTJTQDhN3UkKQYRnpId72s\r\nU4EKGIiPwcssBV9aSS0+NFSmGUZteXsPAXZrCl+ODYTpcKBF3Fo9Bw584cMX\r\nyTnmbpiTzOIpbao4yYRlov6fU7NI+sGcN/JT2bHqeOiq7ugea+lnL3aWjA45\r\nriDTPsDc1q1pblKcR8FaAJQ/mgjvuht9ydwMj15/RbV9uKAACbh6CMFGlJg4\r\nrwYHJVJDL04j8wMvxmPEmK0BOAJrJiA3aUE2p58UAVh+3R1VzL4mSwmOZfXm\r\n6xg0fl3syJoo3Wb+Yn1Mi/hbUrIhZLM60zTAvN0qzAW1vTrjKw0ZLGa0FKzO\r\nT9HrgzR8/I6rcFBlYi/q6H9Ha+9ZETO79aI=\r\n=1gGh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@jest/types": "^28.0.2", "@jest/console": "^28.0.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.0.2_1651045445723_0.11300905023151953", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/test-result", "version": "28.1.0", "license": "MIT", "_id": "@jest/test-result@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd149dee123510dd2fcadbbf5f0020f98ad7f12c", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.0.tgz", "fileCount": 7, "integrity": "sha512-sBBFIyoPzrZho3N+80P35A5oAkSKlGfsEFfXFWuPGBsW40UAjCkGakZhn4UQK4iQlW2vgCDMRDOob9FGKV8YoQ==", "signatures": [{"sig": "MEUCIQDRJHwn0z9+XmJyJrcrSMyg2oxqIrqWarEzt4sZBuKOQAIgGe5vt2Pln3bVHYtWRElyVBoI3NwtptwR5pdb8uySsWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz9Q/+PdOo4cG0FB2DtQKHTdTyWAZ2UGjYQEcJfgW5gX/5B4qbQ6Xs\r\nLlPXeDtV83nmzz7DR2enEwkvnnp0zr/4CUBWTQLmeCP86t63hrE2VB7Sa/vq\r\nFpbRxCDCYgDPXTVIde8uWmUNxfCdIEm94BGQLK9ymDil8kEnBGvREMKNTxY9\r\nkW/Ie4l3tNcms0ZzkHfxStqmReM61LnFQgABc/TtOSccBIL81aiHRnvhD6YT\r\nWrYrZIAI9v3KSRmfTFm1VKDKfIECPZBxfEu86FRnrXvovw/7fnPlqr1n2ZSs\r\nxFPKPR9Bs0cXsYCNUzP+AB0f4UzDshsIgHD1JO0kcqGT/boXsJBVccnnLNX7\r\nj3Z+a8dBBtBPnTDeWHmAxGbpb2fi7c3h2+lvSZQnDLb/NdaguFYo2bTyFUXQ\r\nknsNZwQ1y4CMHVjkoOKLvIaAej/3NNU1ciswZODAkPzWGkKBfbGEX4gv6xhM\r\nwecb3rcl+oK8dWXFqwrmx2GdI/HfRiQUpXXElzuW1cvFRWmzMB3l5hv4eque\r\nwIsBvsfM3+D8twnVRr5JB+QifZGG0h4JTyUbrThQ4Mlyb31hcwlgh5xk7xTw\r\nWq9JkQHHDvfKQy9EfcKbErOQwmuNdFUQEeGC4HwzH40JmYpV8UDHVg1KMSjF\r\nH83rX4SVHu6HiWB+byunWGBVnExMLuF3Kgs=\r\n=ZZZP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@jest/types": "^28.1.0", "@jest/console": "^28.1.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.1.0_1651834136978_0.61719047410083", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/test-result", "version": "28.1.1", "license": "MIT", "_id": "@jest/test-result@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c6f18d1bbb01aa88925dd687872a75f8414b317a", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.1.tgz", "fileCount": 7, "integrity": "sha512-hPmkugBktqL6rRzwWAtp1JtYT4VHwv8OQ+9lE5Gymj6dHzubI/oJHMUpPOt8NrdVWSrz9S7bHjJUmv2ggFoUNQ==", "signatures": [{"sig": "MEQCIBzJDbro4k45GE3Cq8m79okBnDdQGetPUTmEfk6iNooGAiB/FN7Ppc5g5Q0dO4qcTkvcjL+2eGgEugS9G3Zdt5d+Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuujACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS5w/+LWXDbS9HV9gs0Mj1gLd3okmV090y65rXzPyoClE0uI++cqAw\r\nOH16H4xpyr5ZPcP9WQREE9w7vVWDqfuv1UCvTdY2io23nYMPexNPprDAaimz\r\nDRLmy+ut+QZPZB57/cHqwluwGq3PWFjTKwU7voTaeGkceRK4G5AsX/0KnAyG\r\ny2jLaEDxhCoVeSkB+uhqhT0InSATZqZ3InyR1JLCF4iGCf/V1HYRbGWiRKRF\r\nie+MHX2meD0BCT2OHOnAu8MNNUiUc+/DNKw29IJn/y6en92HmZ4MItWJXgg4\r\ncXjjwln6L9iN5AKfD3c67T7B/5Se1f1l81CNxW73jKl0PuFpPeiCWkKBCCvM\r\nqzMgPkRX2mBZPuPS3ekfxEy7Hj4A0zRDpcxLbwM9/Zrv4FVbRDNSkviv3GAO\r\njwfqOaL0fr0zMR6o2f7BuiA6IgayjYFzxqSPdiN6G/enAFW9nEgXB6J6jJEO\r\nzqY2SEz8IAnXuHb52fDanRd3AVri1A7HAWRE4cHhUtgeqxHjb9vYgieiawvk\r\nacc5Sb6Z3U8Xr3qsvHT4o8SZbud972XAoxRMIuSMle/dfiAssyU+JFSMIDSg\r\n+6wpy18nhg+Mt6ASt+kRXW+mYZLOZU6QyOBhI2+zwcoJpbmWccey+c5/NcHe\r\nJ64Fr5bwrTnwu2EN2klpS08Y7wS2CbdxTm4=\r\n=vxeu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^28.1.1", "@jest/console": "^28.1.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.1.1_1654582179100_0.2249288487813177", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/test-result", "version": "28.1.3", "license": "MIT", "_id": "@jest/test-result@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5eae945fd9f4b8fcfce74d239e6f725b6bf076c5", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.3.tgz", "fileCount": 7, "integrity": "sha512-kZAkxnSE+FqE8YjW8gNuoVkkC9I7S1qmenl8sGcDOLropASP+BkcGKwhXoyqQuGOGeYY0y/ixjrd/iERpEXHNg==", "signatures": [{"sig": "MEUCIQDVhkk/8WueTiukhaI17EbvhbBG+Ch6hivg/x49+GQv1gIgHzB7C9Xv1cYzc3NEyY509zuQO4gNhnXoT6ZAz8mx0qU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbOQ/+PaSvQ6v8XUKjybPd0OekOGqvo0YfPVR5KgwdVSbu3gaCkgAM\r\n9FcxXaMEkXQfzqhkcl8XFv6vj27pJ+xg9jcXZ2/B6jRbgkT0xVFUkgcAzxOk\r\nt6JcnsnSZpfBnPpgJA4u2XcC6hH+vVnixW8Gu+CxlRJ9DW8ow06IUPtJguPs\r\ndpuvfb8GtLN+wB97tN4OiPh03j/bmizzIUJ8vTMC6vZKmQD+7GylHSLbcOlA\r\nb8jsQKwhEI/ppp6Z515wBZ0ROPJzuamOSC2E7oPqYwylZm149YVXZfNnIGtE\r\nbPYHatiWhU4+us/K5ITrQ97eZt5bYQlIek2umLo/LEVpWMIlEEAEKt0xhUzB\r\nPpkxaAuI+A+ld1SLFV4N3/clb53XvUVsKZ0BquyVmWnqwkWpklfzU1IKC6iY\r\nKCSnFh+U+HfghnKZEL4f/CK6IN9yLSl+wOQwZiSMjqRSq0H44tp44wLZBLAh\r\nKb0O2DZp3IHs4HYkNbGG3cUF5yBpvFH0IaeGJPh+MHbEfuWmLm8ORmJYSgaz\r\nt036K/vxx5UNhy4S4nsnSfunzUoHSXfYbRpd7/Bash0zUBRrExjLErkQMz7w\r\nbf0aIWdSeu4VSHA2FTXi9ZvRW83Ve497Tbz0MEReiaI2PxplzFZ+0GKetnuf\r\nHkceeCXgWP+PkekbQz/jhBCmZzFHPSJZBBs=\r\n=757a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^28.1.3", "@jest/console": "^28.1.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_28.1.3_1657721551713_0.30060892285848806", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/test-result", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/test-result@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d1e5153a113279fad2143d2979bbb9e5a49e17d3", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-wDsoxqwc/xNwqi2QKgrnCPh0ehq+t0AAMEhlJoW3tfC9xlF7CpluOwmgio9dXYdmr/Ql05MXKrG4s4aUyqOVKQ==", "signatures": [{"sig": "MEQCIG32V3M09+wXHFfo4eSDxqDxerx3z2VF0OHa2uDuvoOqAiBdAriZwc7DR6dLl26INcsf/6nXr+KQ+sNwhmjIvE/wmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqxiw//RKjgzdPJpr7bpBc7rfBxZppA+1izu9IhVSVVDSempcOCizLC\r\nsliui2EIoU3hBR9tpWzGSCbEV2bbXMSEIr9O1+jAMkV07cTS6hkztd/zLIOc\r\nbE95nT0DsAZy3pPdd9uyMdUTwxYwNkF6vJUKsrlbwWxj5Lk0RLr6qwte1bap\r\nQ8Cot+lSbFyr9oEEo2lU4WCmrnGSiehR96axrFEyxVYhq2IO5FLd+Luwm05g\r\nURCageYLIse1L7hRLR9x1Eu7ux9Sfgot7Ff36+MTZ0t6Zoi5Gt5lBreka54D\r\nblDDrot47szwydzdREjHvbQeSlTwseuaYzGcKwYl8osVmBpkzLYIz+kyMIT7\r\nyOa81B8RTovMNflVWur5VgvLxJjio1N/YFyrxIjeGu+3l1DSe+VxnINPttSa\r\nUlqbHCe7dPvycqz0AVGZCHHP3TwesVvd7vjijuG6iz+uwrJrtpyg0PNwi2lj\r\nb3H0EixVYnMCr5X9tj/AownrkAXo2bOGipMG2WtfnWGHREWOT22LECtRvIct\r\nHrmIRGNrm/WXZnSbkcV/QflCKEcSngbV1ih/HhjGARoFzoVakEqj0VK4QO/h\r\n0/ak02JFBqH5dTpzmZfanl83DJvnFte9CQGavf2Dc7cdbKWOCyluEZag9p5E\r\nYzpd1baTQUtbytgFTxrSoebYN8GSXe/FOj8=\r\n=+wJw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.0", "@jest/console": "^29.0.0-alpha.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0-alpha.0_1658095630462_0.18316295580407949", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/test-result", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-result@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "10314f67b1987bfbace6ddbaa5f0f9fe68808416", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-ji<PERSON><PERSON><PERSON>DPIbtX6aEp0f34msSXU1/yeYxhlQ2WkF4nMSxlFnyEwsWbCRhdfOnpyMkoocAFnIvC8QvThTxRDRqDk2g==", "signatures": [{"sig": "MEYCIQDBfaWCCht6ETJUZX1P48vhAHswxO1bCjzWLDmxytWTPAIhAMHhnmt0JygzI8UDFlegppV2G9ZyhtfRxX3vyQjr2c8H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Jg/9GvOfuxWzP5vmruJgFFVJaRVob+dG6I6Ev68ZMhnYBLxOtl2T\r\nQsNVUYtoiYRUrR4VbHjXfq0QaxH/4Hc5s3n6ZfH+ZS1Ow5cchBieVKZm+zKH\r\nh+3uo5BUVpFOgOiU6KY+YFR7enqHrNorSl4CXXE7h3fYyYOEGnEdD6VB3xLm\r\nZJEAdOpvE49fMY+Ni7R2gLnKKMwLPdZGaqV9ZgIrR+uQ9Js3qt5Kai9tzfzc\r\nOqzpmTm8pJUP4NUQci4VpAtWdNo/FCACZwNcjzuOUp/yOwGE9scpor8RmTm6\r\n2hOKCcsBIptfh115OjMzihF0+hmUDMp2LkWqWkq01xMVK7Rve0+va7wOj3eJ\r\nEPzj1MLucCEPsQstvhx4ob4lv6/JAve4mk94hC24IJdXhGfzpqh6XdkffQ4z\r\n+dbb4Aa8I6yusTTcEh07n0scYnlw6yCzdDORoJWxJ/IxZzBiitO8IS6btVj4\r\nn/zMDC+H5AdqGYNyxw8gdvCRqX4NNzITRH3Ogu1jAEe8PIrukHZ6IvM9bj3K\r\n5ulHSFgSKWU8CBH/Z4AG5uDYflJjJ9LwVOqerPAbSHzILTygrII1yn9eorPm\r\n/4+MoEu6xZELyfyFl+s+2wEazzDZHzszjUHvGmhadHoNSBn5XNwSvcosoQhp\r\ns2Q9ZFEeSYAK+aKNlD+lDd2JYxDPUSYZ/TA=\r\n=Hd9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.0", "@jest/console": "^29.0.0-alpha.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0-alpha.1_1659601412493_0.7457466237041028", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/test-result", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-result@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "100a321df297439d809cfd5233eb882d9be1c567", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-7X<PERSON>++4Ap0v/H4gLf1QVWys1olxNfpnHAYN/36HT22JlIjI2UAq6HHWQs5cgFTPCKYAhuCT7xJX1+KAFrXECuQ==", "signatures": [{"sig": "MEYCIQDLHcTmdv4/zPnmqm9ngthqBDVFDBS2aRZbbHfZioaqLAIhALriGGTRnGc7wPO+HhM43oUuDiJquec7wN9PZYqdUrIh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ESACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Fw/8DSqvBF0JQ1wDaWp6I7H1vNCvgC11ZR0xT83TqT5oaxgVLY06\r\nKLCAdn3afUGSlyIWKNRbpoZTlmd5KEGFMaX4q+nAniRXLGiMwwqIaPzBcXhp\r\nErnbWfjDmiv/jAOt4MTPlAlc2z4is0qEWN5xiubWTHkQ/qgg9xCUNsekoe8P\r\ni71LacntczUW8JZFdSfwReeLMrlGR7m1DXqj0F+D1IHLlTnHLcg4kiN3wlyG\r\nF07kc3V+0HWyGZop1+FCt1gzApMMMsq7g0PpqrRZTNRpo1wGKZHIKhVMcEBS\r\nGO0hvtPQ4N/5pPeHkqjIqUvpqSS1l9OFi9foiCihlR2DvcSrvafq9Oj9tvVM\r\n8MHh9AH/O03Lnijb1qKbIvkltZRgXOh7pDgRP6JkZXWM6l39xXIiOUKDZIWA\r\nHpwzBheZoEn8AEoQmhnvPruNeLNoS2S7A8zzIE0rex9E41ot1s+VYOXbQG7y\r\n64WRpg2GCztCimodTHhXmYa2xJxLmHFSBg9v45k9O87l2Af67wgxqdmW7k3Y\r\nishxPXfSOtNgrGcanMBc2Lq94uJvsZ8qMcEnZ5HhJr/7Ibn/cf0AEAHSyGUP\r\nos0JHs/bjc98TKELh1++5TFlHu70PG53vCYKN+NfSeIvdepUurKKzAHKjWUV\r\nv/S40u+RJaCPDH5LZvVXgq6xOkqb7vQ4zFU=\r\n=6UC1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.3", "@jest/console": "^29.0.0-alpha.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0-alpha.3_1659879698544_0.3015820139545604", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/test-result", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-result@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "403a43048374ffe0a4ea6309e3c265ef426ce8b6", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-PlHp0HoTahXr14Kbj9H40nzLawq9H280PMfsu2Itc7VQElKz6e3suDhb3Gv8wqC+QP3swyTL54fuxOt4BhPiEA==", "signatures": [{"sig": "MEUCIQChCOiSd7qFs51CgGl4UW/snf9B1p5fiDkGpA/ICmL/PwIgALa6571T/Nw/Ig1IKywSvt5c3G54b/cR9zrDCixVkJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QohACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoX1Q//ffnZnBRkkTgDyVHM5VBV1/C76nOErOWI+uBYRcVrYIo1oolX\r\nC9INnZObB4ukjHacrYh8JTVarWS9TkVzSKeruFOP7Tt66L1/ZxgDkBmhJtQs\r\npjn0uU0mM7AlJglvNSvydkOqcxETUcj/cERYPax3Ek4oq+bZPNG3CoIw/+eQ\r\nAfctEfJxsrTk3cnSHVakI4qzr1bE7MHTpTIvY3419FHvgQIzLugIicpJzs0D\r\nSWv4DykMiA60+Sgulq/JglkTxijGHTpXPnYUyi7Y2ZuLbG1uPvWY1czo9ZL7\r\n2Z1cHlr+6Zr2aS8Jav1gyzL0fUtS8+J2MQsuW6SPIGZKIvWln5MVy2kIB+/w\r\nK51WdZS9qmh7+OPVGQnJzDbJ+b5Rn6YsJbcbFhhfe9wrTxFTKMQ/5GAt3Xbx\r\nxK/MibBSX/uBhGr+RQUh0V+mS4me19t7qHxx5OiuH0N8E4790TEGw+NI6BZ+\r\nOTHPU50msxRLPHvydeH64q/RtTgR3c/r5JgXxjvhKHQm+HUIZCynyxee9BtM\r\n8qB+0ZEXFHUHA59N/tHFHhjLDzmaUKkPANE1RvgaV/spRTGA2k3WVCglBtRc\r\n1Ga/B8xxgn7xLWuRY/5+yxTIoA52saVx2dK2qJa2q0VWlGmfx+eTuEx19KCE\r\nntUfvVJ37AcfXRUOJqbrmPu05uRmQqcmqaU=\r\n=F4LT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.4", "@jest/console": "^29.0.0-alpha.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0-alpha.4_1659963937054_0.235381487739436", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/test-result", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-result@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "627a47c42b29d455a2301a84385d8a2f01bf910c", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0-alpha.6.tgz", "fileCount": 10, "integrity": "sha512-+zGDTTWNHe3Sr6rsUdjiyS1fYQrUlHVyL2Iy+7LguA/QCHam2cCnGP9k+f6bUP9ZyiIFgiXMMmwteuS5w7/qLw==", "signatures": [{"sig": "MEQCIG3TBrInUDXH+sMCCHZAisA5IyveJl5eB0Gkrtjp7SaQAiB0g89kWk+p3YmCTmQUoCj4zNgXa1z4R3FlLSalUbehLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNcw//aIWg4c3I08BgHGjgqZSCTbsKIrnwC1RAAmb5vFCBK4nrN/Qm\r\nH3earlfRpzmzX9EYe5gxctGqMyaowaoQn58YxHi5oeP26ASwLf0sNarbqWhx\r\nzKioXjeEQ1lMl6t+tdTVNWdX5JAy/Xwcm9ODtrB5oAA0S7YDB1m8e5roy8Md\r\n4ILhovkVd1TwhoPpeY6i1af/VuzdlCbX+9bVU4B0h2h89DCF6DI9/OrhlK18\r\nffTcQNA595dzDdIFC1Tow+QA/lv75Njyt1MewiVps3nEwxtAXhCwJ4zjYGDz\r\nF8eHi7i0cl+ZIhP7CFcNuy8e/bL6SlVqL3iwiY2AO/mGwSWzDvywYmFXwZlw\r\nYvsTBVYyXWHkXDfZoTXORGS7C4i6DfpaMNF5ZhP+CE+ZlVGnprDHkdt1/l03\r\n6W682VtluKTGLwaOp0bPXvTcR1/AwwfLI1l3uKu/FViqD+rLRo1kolFQcpf7\r\nUM3VO+Xu7ZcKBdW0gFmCLACax/HCuMygLH/Ri67gTi9Fwy/GAvzi5jHMi3e+\r\n4lUfFRMCa2k2z/5kl7TczpFP8H6PdnRjgwD3q2yys8UZs35fWqUYyZqXITC8\r\nXpxZbpk+gCuDNLMWR7pjZ9J0s2PYGz0Iwd4b9bnAj6MWfXU/QmionXKbu86e\r\nIe+G04zIcR5oNzQvbj31ccoRXH0Whf/BOVQ=\r\n=f7pi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.6", "@jest/console": "^29.0.0-alpha.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0-alpha.6_1660917471665_0.482439260163954", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/test-result", "version": "29.0.0", "license": "MIT", "_id": "@jest/test-result@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "592f556b94a9d9aed951dd43c310370239c9cb56", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.0.tgz", "fileCount": 10, "integrity": "sha512-mv76j8ILaqOuZAWBGR1/ZSRinN5Q/eEji7kMcvADjd+gQGfn/Py+91nUrVakJT69idC66bvQ7yF24frQpzFKUg==", "signatures": [{"sig": "MEQCID7H8shpICUTPD+El/TugNS38gwK3QK4fDUz65Pc427aAiAAijb4qWLramfNnE9gMWxFdmiTvanFyqnE5WM3zv8Nig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2waACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjIxAAjGS/dPqycesyQaHDWmc5r+8iyJtoDgt7q0EECgFHr543I2ae\r\nSkQGT5nubmAUSrhgjLYytJo5pxbgm6VKf0PQKfI7rwIWM62ppYKY/PGnS8lG\r\nzb4iq/oPHL49TlciTp2RDKnHt0Yqf+iikLvGpAgmhh/Otww5n50m+Yc+WeIj\r\nHcaqDzW+KD4yRH7jUchWH3A+KFTk4NP6AfK4fCwPrstXJ7vCCKGK3ChzFZXW\r\n/IJ0aC7ponCKJkt+43F3fqKkBtWCYfPAm4+yf7+IrZfgT3PNGNkYFzhVjndp\r\nm4yrV5cgcUQM8y3fOiXYQVd5MCCcr3wuEEqlOzOGkgoGeytLuBoB4ACdcP0a\r\nin++50yTk923pM6rcOmD971fLxeUQTeEt8dpAVzpqwqzwvPCSHJu5x4xrQxa\r\nH4eo+lO4LbTQOZtRbxTiJ+73zKGClbpdCeFou+t1COfUYPmW4Op9zk3IDW5z\r\nz+6dHIXlsvwwU/eyEkbpp9u5n9OWDaOVEtjA2OPdTDB/QgqhsbKQZbOijs4N\r\nTEAg701JI9DoxZu9VZ0LpUtOll/31pnsYN2FhzvOkbl7oUNjFdDVCaLBkODj\r\nTemQPADYUr7e2csHz+PSbo541abO+2wO85gZ0Lu9Qz3Ln2XRhR2iibTr8XoY\r\n5NX5VTyyi0QK3fyu4q1W9wXtpbb+Lsf7FbU=\r\n=SWid\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.0.0", "@jest/console": "^29.0.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.0_1661430810656_0.4867548867782667", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/test-result", "version": "29.0.1", "license": "MIT", "_id": "@jest/test-result@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "97ac334e4c6f7d016c341cdd500aa423a38e4cdd", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.1.tgz", "fileCount": 7, "integrity": "sha512-XCA4whh/igxjBaR/Hg8qwFd/uTsauoD7QAdAYUjV2CSGx0+iunhjoCRRWTwqjQrETRqOJABx6kNfw0+C0vMSgQ==", "signatures": [{"sig": "MEQCIEFMpCq7jWkyMy7co4rA8okliMOnMtdTciLOnfv2OCF7AiAxucF5foPWOMUwNzd+S2EMB/C5mjUYWQ8dO+PR72m6Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo3hAAlRrw7rFKqVv+CzeyAUjm1pVSA8nSoEsiML+BZtqM4/Tr0Bqe\r\nVFq/Coe3pNvubYBgDGFd5RysMo0ADwAfHVAyKQApIulKkjuVPIJog44ZmY/j\r\njSen5DHjdr55OC/mDHhS7nzf29FmPc2StdL1mM4pHH6tdhp+nZNw35+dsDbU\r\nZeY708GsPQCZNX6U7LGQYZIrAYNzLpMMhnLWMs9fKEEXqm3p4MbTbAkMfK4m\r\n9m6hyJhKwqZHhctOv2YLmDZJWeuBK0z7RK4SFyNWmvIIw1PcMA/aQH2OG6aP\r\ncJq5Szy4LB722v3AgchL1maWXuvPQs9ChQkdnjQn3F5N+clvG7kVNV1w3EAZ\r\nq+tWC9+dp/M+5U8gB6y1nESmvup0BCAP5c4qE3ZDSq2DkFtYdSs6zdfDOVOo\r\nNu4CU4F9y54ZMw//pmjOFpHmzaXbMtsTH4waUFYv4J+dXKyY74Exm4Z0+1G9\r\n1MpxjEIX43zCI/1nG9upRpoG1vYCklaJyZIJTgA9syP2PmZJyf61xMb4tcDL\r\n/7E33Fnb0jA0A+dMjyYtRQkbIwOD4y28oZbhjIlng40lRR1u8LbN6J4NR1ml\r\ns4FvRSsZNb7/PuFYaMhlNAZr3I6AKy7RDNgoevwBu2LAt57d1K1SNbOaxJQw\r\nIjnpACSgdEdoMz6DvuTqlSD9Gz4n+oVDejE=\r\n=5d6T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.0.1", "@jest/console": "^29.0.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.1_1661520884288_0.7511549322861328", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/test-result", "version": "29.0.2", "license": "MIT", "_id": "@jest/test-result@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dde4922e6234dd311c85ddf1ec2b7f600a90295d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.2.tgz", "fileCount": 7, "integrity": "sha512-b5rDc0lLL6Kx73LyCx6370k9uZ8o5UKdCpMS6Za3ke7H9y8PtAU305y6TeghpBmf2In8p/qqi3GpftgzijSsNw==", "signatures": [{"sig": "MEUCIQCfZpbdacJCOV89SBdX4EBMLKYckMg0pTf1iL4UdTjcyAIgFy36Q09ItROPz2Zl3+oyqLqZXkuAayr3FTgzGYcqmL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocBg//ZOvLMO7ARw1YC4Sl/6tSlHn2gxRdNFAUxHa43KBggSfsdYbn\r\n2SZAAeWTAh6bm4dvotPeiaHlUZH5cbbeTaoopBwZzC0ctRz0W1bEnGUQhNru\r\nBFVvOZaEVDwvh8F7bD6RlLXSadCbb5oZ8nMnbX6OAczj+H11z7QhtWqV7aVD\r\njdE6sPP6yxxoctYsFoMSYcWgAeNi1bejl51mNuXVX+22NWmoyqGJxvN4Pop7\r\nBH137c+QAiYwW222/4cs2XZBh9oLzSlHpywV56rdSsznN3nKP+lHNPd6MVoX\r\nbzfJvx3jYkjzdC1TnC/0bFMjYiE8mzZMMhE77AlKjWLANQBf2ZcQ5tgQuvdG\r\nTomEi/XSti1Ve14veiOPUXDIVHSVZha/LnpxRGxJ4UsJzTcjXqw/ZMK6FWWo\r\nwGSdF2R6baDoTDW++aLg3DRBh10Sfy29GE6/u0o2rOGdGEK9zbm9amg8AB/h\r\n/NLiNswKUiEvZLZnThtsMw6o/zu5SQAHb4TeIo7grCFHGNge8YR+Ggp/Q0JZ\r\ndW8OEa5/qXwseP9P81pC0CwNb4xniXbrLYDqZvYdZFZceNUeDq6bTY9SX+Cq\r\n0Rxo5677bTWgHAF/POqXdbaP8D0XOVlpDMRyTeW4fB0AVtGVcbAMAM389prC\r\nBxChoH7hOQ88NyuhVhgGr1yaVhBhD4ks1Ds=\r\n=P0LW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.0.2", "@jest/console": "^29.0.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.2_1662202102032_0.12753902292324049", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/test-result", "version": "29.0.3", "license": "MIT", "_id": "@jest/test-result@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b03d8ef4c58be84cd5d5d3b24d4b4c8cabbf2746", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.0.3.tgz", "fileCount": 7, "integrity": "sha512-vViVnQjCgTmbhDKEonKJPtcFe9G/CJO4/Np4XwYJah+lF2oI7KKeRp8t1dFvv44wN2NdbDb/qC6pi++Vpp0Dlg==", "signatures": [{"sig": "MEQCIEClsitIHv/sNvnqANbcgtkEm+qTQUOTj5OM075oj2/nAiBCyzamIJsJHezUgPaDaJaF1RG7T0rtVD7co6UwqoQSMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr48A//XlS7pEAJkyoF89cGhtkw1UKjuh+3QW/bBcgd2suW3AR+3NqX\r\nl5oy5DGYf6oD0iZKS8vdKAa5+JTqPO+6kg2yR3IQngTOxTMedoJUTt7hSnad\r\n7YTRnYEsYbd1BBSZc1rufLI9rfh9s81V5xEWmRRLyHlKQsPcktL40bwFgvKo\r\n3ZUbH+dYJ9OhHSGCmncrDMNatgJAljTTqagK0AgBb1S6LdtiIkTW8kuQCRLC\r\nndT0eR1jhHr6lMd9mBnjGircquvxrb5iaoOiwLwrY9vkdrcOlaws21Bdla++\r\ndIeuvmYNY/l2aAkmGhcvggxCs//63TWIw4RD6/tbKDerI/8dZgrPV+2hQehO\r\nSUi9fwMU2HUsQLDD1BGKUvQq6rbqTL/JWUGRQMRMLgEj44eiXiDGx24iGGlV\r\nOJNF55lAsi1drUa/QwaBqYDxBSl/qko3JgkpI8Y+VPE2qz5sBz8gu4i6dP7N\r\nx/6yeE91xg5ab2oa/rojlJDF9c4d/YS0G0E5tspAcwjm5VaLR5dw14mq1kr1\r\nibKH3EeVkhQBY9HNAqKwuSmMZX5OAiv/hEKtZCutSpXFfNp1VCAFfGHcov0V\r\npCPDvTy+R1uOwVZYKvvsQUQanbtMH8ZNtp4tDKudhWmxzpGmIkBr5woggN4F\r\nzqCUjBFjLZDfDBrsPkn4m7R7BRGcwBUOKXA=\r\n=MvAL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.0.3", "@jest/console": "^29.0.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.0.3_1662820900930_0.22999482684691208", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/test-result", "version": "29.1.0", "license": "MIT", "_id": "@jest/test-result@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da6b0062325a780c9e3c7262b6b89da57fba2dca", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.1.0.tgz", "fileCount": 7, "integrity": "sha512-RMBhPlw1Qfc2bKSf3RFPCyFSN7cfWVSTxRD8JrnvqdqgaDgrq4aGJT1A/V2+5Vq9bqBd187FpaxGTQ4zLrt08g==", "signatures": [{"sig": "MEQCIAinbcyjkuYgHNqMWzn2HjAZ2wBA2ESZBT7K2jcFRonOAiA+RA3sMWtGDiC9ahqC3XR6zhT+iFfcHdHxvMUYQQpxSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoufw//WsafQD1ud/HGP0puXwZMwOobrBdcE4FzzTBj9BeLa9BbSlmj\r\nyc0TqFc0l0Gg4tuRYISZaRxUPWG/1KybXq55mCAOXJSj4lyRy8PBgvnM6se2\r\nzBxo3dVtw7bpaXA62Wh8jbJkh3yrIfk6RhecYA9vyRfTjXjOgdHiyTZVzK3A\r\nhezt1JlwCt4kOzHGAuUZG+PVPg5WY0/Ete9J3XzC1XJQu7C6RisKfc5rmf27\r\nLRW1hSo4w83dEnkC/W2EqcS94ek2TKh4jSekiDhOFxO30Wx8dmcauUEeNETv\r\n4IqHbMmGJptwDfIbx9hVneoHioIvfc45Mvuoiqpz7f0YO2BUT3pBBvogILmF\r\nVgUTQPZKazFnF7RPp+bK8CmX82Xu90b7qqzK5aDgBfWc/IElVtEkdhbGT/hT\r\nn+CfpLgwMsUAq0bgRz6JZ6ejJbcfBTIrRfXvDPT6AHawIOC9P3Snn8Eq5NaL\r\nfPu1JzXvxHX4Zvn94LJtLJlky9ZG+PuRsf/9Nn7Wgyr4tWqMXTBmSXheMcFG\r\nWMwkL26NN0qLRY8MZmOcfh6dC4ySmZCWvyc7WtzfFRePWzd+7pVzmwZEMMcX\r\ntKA1+OA8SWh+twt8v6Gx0D5OsRILfA26zU2TmZza4rFI5BbaSnJ1oLIK9LoW\r\n2N3duTmoAsGqpoWkjLvqSIRSJ5c6fhfOrM0=\r\n=gsA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.1.0", "@jest/console": "^29.1.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.1.0_1664350664312_0.08813428051051497", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/test-result", "version": "29.1.2", "license": "MIT", "_id": "@jest/test-result@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6a8d006eb2b31ce0287d1fc10d12b8ff8504f3c8", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.1.2.tgz", "fileCount": 7, "integrity": "sha512-jjYYjjumCJjH9hHCoMhA8PCl1OxNeGgAoZ7yuGYILRJX9NjgzTN0pCT5qAoYR4jfOP8htIByvAlz9vfNSSBoVg==", "signatures": [{"sig": "MEUCIQC10lAVwrzrVF12H3PDgdyz3sgRDoNOws06DxtxcWXA/QIgCXwUWpOHhlKlVhGoNWw+8zIjY3ltREYBhPZovH0wstE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCrg//XRkfGXP+6tFl+zgUvlxvfjZYbHloOAKYB9FoGDuahbduj90T\r\n1SBYhdRt5GIujbA+0UP+lr72pFMWilGrZtOoV8iCBU8ifutmMhmNoyLMpisP\r\noHFWBbfCDD4IGa0IRbOcORAI+fexnFLaQxqLchpRdGLbVnddMhT6+TCob/Q6\r\nuGcyEo8N9mjQMS+RC4S+EbXZ11OHJPHseaQdvmh5o+Qf1uYW+ksh2ojJNOa+\r\ndmnwXJRoaXgdjt5C6la/G/1IITvmEi9I8D3dbdqhnkYbZajjf8Oq5SSooJfX\r\noF6Xaw0u+6FeOJT0DDh61Bw0VEiytcpnBiBLHD/uSD8e/xkVVNrg+Q55TqwG\r\nPKfSMV4Ek2UVJrQLR7qSo0rzBcDPjfzcEsGScLd/yuM+Q08us+QOSP3/cYrj\r\nVOFb0uujD9LgU6ChFmLTNupdIGYOSXeF4IIqZsRXoK6vj9gwS/1ALTX9bOdf\r\nZKfwClKh+PCXcyCo4GF8xXbusQAvv+ZjSXJSoOhSfmoGNvq0VuyNLrcNpAoK\r\n4nZv/94lHCEu6Sz9bWbTRDNL1cnK2NYNnn4KIa7RRb/8qCC4sUAb/8qZ5n+a\r\ncSW5YMPEeAoeQ5DN4HdO1adEhnHj/dxxW9strQqDtp9OAXK3PPZz5CZSOi1+\r\nyfyEr3/5hA3AL/Cus+Xn3g0zrF9jHtV4V34=\r\n=9u/n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.1.2", "@jest/console": "^29.1.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.1.2_1664522571315_0.8825519306078717", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/test-result", "version": "29.2.0", "license": "MIT", "_id": "@jest/test-result@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3dcc7123b8f0fb5ba1f650ce17af45cce91a0323", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.2.0.tgz", "fileCount": 7, "integrity": "sha512-l76EPJ6QqtzsCLS4aimJqWO53pxZ82o3aE+Brcmo1HJ/phb9+MR7gPhyDdN6VSGaLJCRVJBZgWEhAEz+qON0Fw==", "signatures": [{"sig": "MEQCIGNgwm/y0jRfF2+0XACUNTsIzbeC+EiuRxhI5sZwjstQAiBAljoTuoSJzTE8M5PKdUOIcGirfYmuuxMQvxRciE0QLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNZhAAoOxwLLuv6LJzmqKo1bCdGNxd2U9zSBtA4TalmlesJGa8t1LH\r\nhU66WhUxBOncBRlLXxIa/5W/LVeplsvR7PcCBe0v7ZP5mGwY5O/s/Ij6pPd8\r\nyji5zyethgFTe5leujg3ogRZjCcS0yIKYmoSubJV0+eUYZdzWTc04KCC8179\r\nhmwkAjAKtp0E/TSIrIheZmS/WWr0HH/aTmt76nuea9+RVgQm6kEYmUONeQCv\r\nwLohG+7UibFF0sewRiV93XkempOdlPxUirDPSbg0SqhGcSvWfilCBMIM6YO2\r\nbxDIF2PiPgcDljHtzZsc2AVXzO+DMAj+PYAhRSzDD48AcEXdien9TUUhYJUq\r\niBAp134Xed3SBtUxZlpiZDL5g/IHeE67JknD1y7tdNHFkPQdJw4vUMrMOJZJ\r\n4CY34T3UGmFxT+ltyY+CajGOq+ugaXOK8WIx8le6sxYuF3AaNFU7GtTlvT0U\r\nFa2kYP+0lfhtnVm3d2uQ8TmXQnTt/TjXmmrGILa9sxFfETTOqcOQa6Q5xATX\r\nmnmNwQuaH66SbOnHi4ghGJ1c7JIsaQbeRhfir1+polCDaAxebZa4a4414hv9\r\nRYvjhTFNVEdjf4MBGMiMwi2z6kXWNi4gxc8MPFl2Yl0L3JoewhJS0eJl5VnQ\r\nLahcBH7sLY9VUzglZR0QTHjmOJWYjGi1u/0=\r\n=auyN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.2.0", "@jest/console": "^29.2.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.2.0_1665738833406_0.6343994681901612", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/test-result", "version": "29.2.1", "license": "MIT", "_id": "@jest/test-result@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f42dbf7b9ae465d0a93eee6131473b8bb3bd2edb", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.2.1.tgz", "fileCount": 7, "integrity": "sha512-lS4+H+VkhbX6z64tZP7PAUwPqhwj3kbuEHcaLuaBuB+riyaX7oa1txe0tXgrFj5hRWvZKvqO7LZDlNWeJ7VTPA==", "signatures": [{"sig": "MEUCIQCmcMTAcau3QvbTASbCBc3HfMI4fwiQskShdsIAwv5PSQIgEPhwbZGfxNXeaO/o4HvpmUE0wYdZQvMbx9NtKvPa3rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu0Q//YYg/CMMD9FCjxrQ2clG/xkjnYSqVFh50g8nttjGPGCijgMJ9\r\nQZJpgaXDhFTcvne8teIVNo8ow1EEs8ydtiyMR6xWzbIO91U1LHtnIZz5TZOT\r\n6jHVDc6I6HNawB1boeB4T4pUrufbgVY+c7dIXDymNKSjrbmVyyf8V0IB0dBs\r\njRziSEadWB7R3TZnn0MZY+T/5RRYa3u3gDJc/g1tOKZ2KYiMPebXXpYdJ/hX\r\nyFGMZpMZwYJlf9JyZadrxePTp3fj9BAEr3GlC/GJnUX67goBetpm/xrDJ1OS\r\nTCLctbKGhuWJVG5j2B1CACRaDegDfMr6YGSFcuIwDj+3TFT8fXZ2lTXL7KBI\r\n4O6TfcwZR8v5rLNQWD77VQIcVML5/XG1WkfSEDQT6kPD0FlHErsqyUTO22GS\r\nFUxswecANNJKYMedRFswTO88k+Jq9Z/BxtRFa2VlD0sQe3ZEnirChjwrlAs/\r\nhFNdMWnbnD2s1qkitwAO/omuLY2FRIed5o1FyPOx6Ly+eZjap6zgSo23AnMT\r\n3EkNhYxVAbNLf6vZP5Bh94tZwS+US6wH80Jmi+bTp7urjywOi+GarEPRSMGI\r\nJKVHKUopV2sGKhjSnuN4H1rSV2lrbMvYf285jQNPGkbBJPgayyLcMV4Bh1Jo\r\nN1Aga5zvUMVYec8YzBZIbLi8PADFi1QP61c=\r\n=xUdC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.2.1", "@jest/console": "^29.2.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.2.1_1666108816148_0.43197645695896414", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/test-result", "version": "29.3.1", "license": "MIT", "_id": "@jest/test-result@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "92cd5099aa94be947560a24610aa76606de78f50", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.3.1.tgz", "fileCount": 7, "integrity": "sha512-qeLa6qc0ddB0kuOZyZIhfN5q0e2htngokyTWsGriedsDhItisW7SDYZ7ceOe57Ii03sL988/03wAcBh3TChMGw==", "signatures": [{"sig": "MEUCICHcEQd+/34p+3oda1h3CGeJJyzTuCEkLxksL/+wsRTgAiEA9t0/OkSKDmds+N/87oZ/ez4fscBVnfeV1lR6h4FTQvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFlBAAnFvlVut7fSKxebRy86b3Fa59PcEi0yeqKQ8fLVoP7NI3KIiY\r\n34TC/x1ZBepn73Lrryvh1TUH0y23jcId7BLCNDzkly5deAxvHTUc7rEkuVIe\r\nCaVDLJGyNmMVhpLjgewjgUlT/yrjOcSm/b3X5LzIhDZ/mqQbj6RL9EBSjwc/\r\n7rNjc+1lf64UA8GphmiXHXSKOuMz53OUxe90ctJXzLNWthxNgBlbVuWrBcrP\r\ntLz25rFYn7s39VYE8FzRJ1txOcwtoFHymPXMjCUSBlSQigpabYrN1BtbSe6c\r\nB59ZRCMfO8y/vRFlUwqafvinLul3QSXCJhdIsKMZijP5mshf702c5fWtgyDJ\r\nkTxWJLAqM6EdcOipAxfWCriItmaocaIQh/fipPZijgbLmJvUVCs2wSxaAcVU\r\n3DlqN0F3Z8r2PxTtUcM/WbVEjAsn5eWbY+wPDdbu8+BXYGYK664RIFCYc9mC\r\n0boJHacwgLGs/ecTUzrRoCQWGa7AQHZtdwTKauBphtvToJGwmLtnNWNhxbnk\r\n5WWSL2Gj8DGW/fbMGd6qah2z/GAiU7oghnv/cTqHaiATMrx2MV7P3AX4T2Re\r\nrHmuz9g+4KooznRQu0XmiKspX0TVGIuad9as8qNsl6QVwk8EHxwNzt2E6TK9\r\nke1SE9EBGEDWE6osj0eVvap3s1B21qw09gc=\r\n=7YhL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@jest/types": "^29.3.1", "@jest/console": "^29.3.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.3.1_1667948185643_0.03785641749190782", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/test-result", "version": "29.4.0", "license": "MIT", "_id": "@jest/test-result@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "64f7bd518d6d2a6662c40569e208e030a370f351", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.4.0.tgz", "fileCount": 8, "integrity": "sha512-EtRklzjpddZU/aBVxJqqejfzfOcnehmjNXufs6u6qwd05kkhXpAPhZdt8bLlQd7cA2nD+JqZQ5Dx9NX5Jh6mjA==", "signatures": [{"sig": "MEUCIFiD9aRM+UmXdrFxZpc6kclV9aKe5SxbkKrSCCrkBcAFAiEA2+9+sk4HNzDvnT7leX+9OACQhTsegibdpPUX9AmqysY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7lAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpE9w/9HM5QKeiJyCEvURI2ecqGZtrFMqxcmr91MyAy5hWgZGF4F3P7\r\nWTpTtXHo7bBYGmbqKgT+LdXqtFBLp+uvh1whM5mYne2sowfQQ9dArSRXVRJk\r\niF+ggAkouf+zzM1glARpEfFyzlUTCETz2Uuyh/q5ofXrTd6XEcNF/Uh9GFpw\r\nkOUe3QbNGAfGz6Zc1c46Faim5R8hz35j80V14c9wsZ7okxBbyJF97eJCSsNz\r\n8Iu/NJsjwPgmMU6qGN2MGbPvakZvRb3YSL/NQZ8Nlq/CL0SXjoA+2Zv2/mSS\r\nsO6cbqvZ5fGKYpdh5o8fxs0ZYPG8z9ueNehMnVOx9Xm8OnS5Z0yTHWaaMEGN\r\nWwgj3ofZTfBx6f0S2iM4SUQKzKYibFe0a4E5RA5+1NmYU+XWl17jcyC90NKZ\r\nOqHmIyhpwvm6jnuwAkSiIVdUwSXnyR31AoI1Lfk8SPHSegYuMYqWb5S8gPbZ\r\n3/isTgHQUI57SCHIiTy780qJm2zhixsxCrXK4LFYOM6Z8JWmzyFTRjlqovvQ\r\nv5SnVFUwSBp4BFRbBCLmLZWVg/PliARKLo9Us0aGg3n0X3ij37Zo6Ya6vEFG\r\ntZR2vBOnuquBbwcadzkrX+ygTNRZ+7NIAroccF1/6p63gSU5XYnEi9zGVsza\r\n3DdmchQQ0EKw/CTfLPrHAqC8RoaotQO3X6U=\r\n=1Oe+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@jest/types": "^29.4.0", "@jest/console": "^29.4.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.4.0", "jest-haste-map": "^29.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.4.0_1674557760584_0.6434079243112463", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/test-result", "version": "29.4.1", "license": "MIT", "_id": "@jest/test-result@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "997f19695e13b34779ceb3c288a416bd26c3238d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.4.1.tgz", "fileCount": 8, "integrity": "sha512-WRt29Lwt+hEgfN8QDrXqXGgCTidq1rLyFqmZ4lmJOpVArC8daXrZWkWjiaijQvgd3aOUj2fM8INclKHsQW9YyQ==", "signatures": [{"sig": "MEYCIQCZcZXgfBerid4LIVIKmDW8eZmjfiY4ayld0z8yCy67EgIhAKt5OmIf30DikFgGEI1n8f52F9iMuWoCzdzRAiFLpCV1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17614, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCUhAAjsQrfELJgwe4iCP8Hxad/DKWvrrpuBnzItb+uSAPLOwJga3l\r\nTtqNZovHMBmOqrbwrPRLNf2IXcqpYxop1vahrAC9KqoZBB0WU+U9ilmPbXst\r\n2pYbG0Lnfp1e518ScpLh8ynXh4TASDrpVXv22SkPSoNhqVbuFANS0iuxt90g\r\naDpZt62Kchy9q58lKcd2kp2+Cw4XXCYhlKCtq1QJbm+PaGjihOHCIay4LRKh\r\ngUn4jJ1b+Wytit+i4P5qlc8dvTFWmflufxqcbl0mk4sM0XW9ocGFe5sgkTXn\r\n2Ith6KGiFKZJ3aiMlV4PMP+fog9a+6ZLcrn3DrmZ46KEtMGudIW5u45w6Hac\r\nPfh1MkhMqv/Bd5YUiYII6H6F9uaGKLMkJisYCFGEBP0139ik4UtV7uRNW46h\r\nYaUBWuD7i2OHvkO+Auz6/sQ5Ui/fUys0qvLe8Je1EkK/fcXSpWfRuk3y5mlT\r\nH96WPii3TWzPOtRtQ+oBsbOHa8weP7qSuH9uMiz2yocdpoGzyFzquZMDrmRI\r\nE0pcvRghXJDDOAcI+0IJJ4y+4vB4l1J6rN4jPoq8+guAefPpjb8f/0gECakg\r\nkctVQdpccy3ILI3PiaRDftrcMxtH+LBRTvtCR4za729yKgifpxAV4nh9e/cO\r\nH1L23a1PsvRzK1BxgnsjDpSuFN075mevSrY=\r\n=ao+F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@jest/types": "^29.4.1", "@jest/console": "^29.4.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.4.1", "jest-haste-map": "^29.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.4.1_1674745722383_0.20748351665623277", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/test-result", "version": "29.4.2", "license": "MIT", "_id": "@jest/test-result@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34b0ba069f2e3072261e4884c8fb6bd15ed6fb8d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.4.2.tgz", "fileCount": 7, "integrity": "sha512-HZsC3shhiHVvMtP+i55MGR5bPcc3obCFbA5bzIOb8pCjwBZf11cZliJncCgaVUbC5yoQNuGqCkC0Q3t6EItxZA==", "signatures": [{"sig": "MEYCIQD45sSF+nObNF4e6+35I3dW/Cq5QD6hpNSmGf5NDUh5UQIhALq14TDvizTPJY5kli+9b9UA8b4++8Dk5pyXNKC8wGHQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Jg/9G+mCUITY0j41/oRq4+zppjrM78SuiDlqf6cCwS98B1BY2/Zc\r\nXd/Xbui0ftXF4NmAIQIRJKOC0Np/7JjuWtiAdFt+j2FwdlNrrZhW+DGT47ZX\r\nPcu5G6pT10i3cbz/dMnXVN+8/NDcjjCoEehect67Iqgw63P8yRQkZbVdFeP7\r\nLPWu7SOus6ZECZBa6UFbDxNvlAPk6CQq6PPso1ZRgQpZRoXfe9s+T5aLahDc\r\n78sv9AXVPzvl1CI3nqnTmMtbDYMuLgYO6d5bTeUjtCU15vLWi22XEvEz1NES\r\nlGVVhOEHWoYwPQQn56A3azNWc+8P4xgkqQjLbLdw+L01PjhE0WNIlpessNLc\r\nd5m67NJSOFZLGRydksr5T6R5qPG0n2HCfqTNflsrWwxL5V/43VcCgoDdUHEm\r\nj4UDNliMP6MNF6b/GgwKUtIUV9II8w5QskL0JX012H2ti12sWfxnFx3MM+f0\r\n3yNGwXwtCxjrxhzj66VSEaBq7UbNvD6ltxAb8vNkmFmLGPQM2uydx6wiT8Ud\r\nSCfA908aF3K5W21oibaC9QQZMUbSXDgCeZtO4zjTifIvkZqfEypOhbXGVpCw\r\n6dIgZMB+P9Exaxs7IBKf5VZrnOO/nTWtcWHN1HE3xSS018Nyo04kSguHqigp\r\nAbgBvH1t0w7oFcVHBGdyofOC3BEky4sCRiM=\r\n=EqeV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@jest/types": "^29.4.2", "@jest/console": "^29.4.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.4.2", "jest-haste-map": "^29.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.4.2_1675777542135_0.1665481759207461", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/test-result", "version": "29.4.3", "license": "MIT", "_id": "@jest/test-result@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e13d973d16c8c7cc0c597082d5f3b9e7f796ccb8", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.4.3.tgz", "fileCount": 7, "integrity": "sha512-Oi4u9NfBolMq9MASPwuWTlC5WvmNRwI4S8YrQg5R5Gi47DYlBe3sh7ILTqi/LGrK1XUE4XY9KZcQJTH1WJCLLA==", "signatures": [{"sig": "MEUCIEZesoSoD9fKcxY9SvOX9XgJsUgbFpXhdMXvrHVS1tCTAiEAzCy5J3/fiZZC14jMGhouGEZfr/LT3NmQjpHyB70PU0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MirACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSAA/+KA9fgYKeZjNhfxM9aN8DOPV2U/fvdC8R4CrEswVutic5yL6d\r\nuqe6/8ZYF19+7lQrmMaX/hAtEA0nbzt4hAR88/lTXONSe3gX+3WA8oTAWtTo\r\nnBIQm5QHYp/DgGIJVYEE5nYjE1NR6kVQS/0Eov2sUWr255xIyC9Fsz386Q7A\r\n2EoI3zZj9gcOlON7cZuuZJDR+4OAA/yruEvrc7x036HWUQsN0ezFjRJeKu+9\r\nhtqG2liBKXV+K9I1+SYgmHeLha627AWms7uLDQFuHjYIIBd8dU2XAnoHZ/KB\r\nLLZZLz5fRI/Vk4q7lwh3pjICb/fSLEQYvOnv2Kebiaz87ksJ3NBD8Cb0Z29P\r\n2+5fJPILuywX0WGrqqyb2lGVYSiA8Otn99fbYv8P97LiUiPGYZB7s5ZTYGb/\r\no/c3YmalnYNA7Ruz9KbKZsQgu1PAgqBOdjxEdm9CcDe/6yo2H9uQXiq3Nd0v\r\nP5mYMw09otQO4XXBxj5VtTENBkRRI2vn2n+O+pNWLZBuR0Gn0wFopLaYU/+w\r\n6gbEjX+p3Q/m4Liw7c+4gBx6m5VtOKESMkuXHTMS+McViMuh1/oXlFWe/jir\r\niAbJX/wvZ4okpoAaO8UTW2EyfjTFRzFNqHrKtdxFgT1adp5UOWKgOSHnzV7/\r\nB7F6YZqvHcfN0bS05E6l5kZ14TL7zdJF9+A=\r\n=XyIF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"@jest/types": "^29.4.3", "@jest/console": "^29.4.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.4.3", "jest-haste-map": "^29.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.4.3_1676462251094_0.9528944878075689", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/test-result", "version": "29.5.0", "license": "MIT", "_id": "@jest/test-result@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7c856a6ca84f45cc36926a4e9c6b57f1973f1408", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.5.0.tgz", "fileCount": 7, "integrity": "sha512-fGl4rfitnbfLsrfx1uUpDEESS7zM8JdgZgOCQuxQvL1Sn/I6ijeAVQWGfXI9zb1i9Mzo495cIpVZhA0yr60PkQ==", "signatures": [{"sig": "MEQCIDSriFdGILgyN4hN5lXSs7wnyZvchhnL2XIUaIpJd4g7AiAsTJxMn21PLxNklrLjbAtED/0hQyfrMU8I4+0wPCUo+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeuzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbVA//fsGqMC5t6lZjyGJezQZ1cVv6Q0+f5B+pWEXuBNYeIh2L3Wt4\r\nEpH+NhFu42mCADQ+ZeJGm72P+Scw9REou0iZccAS8hz1JSMaRLHsjpIpn12m\r\nVEPMdL0D90nTb3Qjdi+gDhFOcha+X2agyges5vKgUh1vKG+PePlQWqDWzu1a\r\nm6Lvh/V3f/+BFE+jbBGLYiCePcOEZSZJTKxb/GREfEBQHhQaRFjidmX2IfFs\r\n0XUWY5mkI/EEIk7d4m2Z2vzwM8Ipql3fILHrvGHHOcuiKpf1cIG+x7Ah9Hyc\r\nafkn3dRMS3xuXgSjI9vMZOstrjwvFA8ZUZXBhaeWtZFxzJkwwlrqdwWzOH8E\r\nR0X93Mh/V3euQGk8zAPB4BOQH1c3f5kknt+9RVZHkDOM8QI1VIaHa9B3rQGd\r\nzwPPINeHUuCx/hLgu4bdnBArU0uZNSvXhU+B0YWnHX9wnEGS+D++OvTfXnWp\r\nFDGdR9Vxvnr7UIRYZF1ZriZSvSeXl1RAx/yKoAXf5CBFDdfUkYyjdJaNB2GW\r\n3X4Y1lomJD2melbCnVXN1gNaJ/AccBTIrKALChH+VpytaHe0KpSv9FEYaxiG\r\ni4Tg/b9ZpPDUX5BG8KwPn5zEYuJMysb2hkETYA4Dn76Cp84ZX1dw3wMnZdyt\r\nbDOXzfn/7E4wInbfT60dIf65Zr6LBXisZr4=\r\n=p6hY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"@jest/types": "^29.5.0", "@jest/console": "^29.5.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.5.0", "jest-haste-map": "^29.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.5.0_1678109619280_0.6566260927490555", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/test-result", "version": "29.6.0", "license": "MIT", "_id": "@jest/test-result@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "03bd32d3bb696eff5affecf918468bc633fc32d5", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.6.0.tgz", "fileCount": 7, "integrity": "sha512-9qLb7xITeyWhM4yatn2muqfomuoCTOhv0QV9i7XiIyYi3QLfnvPv5NeJp5u0PZeutAOROMLKakOkmoAisOr3YQ==", "signatures": [{"sig": "MEUCIQDxwXyyxmutQt0vGX7FQvNqAinw6VePhKv9Zc88a9rMLAIgc4XnIdck71a2S7X+AsQoofSA9/OSwDetYLh9b0kSzR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15754}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"@jest/types": "^29.6.0", "@jest/console": "^29.6.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.6.0", "jest-haste-map": "^29.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.6.0_1688484352987_0.4415771697236319", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/test-result", "version": "29.6.1", "license": "MIT", "_id": "@jest/test-result@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "850e565a3f58ee8ca6ec424db00cb0f2d83c36ba", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.6.1.tgz", "fileCount": 7, "integrity": "sha512-Ynr13ZRcpX6INak0TPUukU8GWRfm/vAytE3JbJNGAvINySWYdfE7dGZMbk36oVuK4CigpbhMn8eg1dixZ7ZJOw==", "signatures": [{"sig": "MEQCIHUQQuliDa+1bktdwjeLG6SStURgBi+xeDbzFt/asQg2AiA1HmMFezpc39yPOnjDCaHxGS0flVdAqXCURXStjvdDIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15754}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"@jest/types": "^29.6.1", "@jest/console": "^29.6.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.6.1", "jest-haste-map": "^29.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.6.1_1688653113724_0.322576951408331", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/test-result", "version": "29.6.2", "license": "MIT", "_id": "@jest/test-result@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fdd11583cd1608e4db3114e8f0cce277bf7a32ed", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.6.2.tgz", "fileCount": 7, "integrity": "sha512-3VKFXzcV42EYhMCsJQURptSqnyjqCGbtLuX5Xxb6Pm6gUf1wIRIl+mandIRGJyWKgNKYF9cnstti6Ls5ekduqw==", "signatures": [{"sig": "MEQCIGD51FqAMz6fCjSGDyjin2DASIMNpcycUfJ3RjebUyX3AiA913jYO+TrKsc4O6gODAtw0u320xrMWBA7kq1bffUVsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15754}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"@jest/types": "^29.6.1", "@jest/console": "^29.6.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.6.2", "jest-haste-map": "^29.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.6.2_1690449697763_0.5508955802615987", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/test-result", "version": "29.6.3", "license": "MIT", "_id": "@jest/test-result@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1da4c6749c16a71c108644624d9cd0d17206aa2b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.6.3.tgz", "fileCount": 7, "integrity": "sha512-k7ZZaNvOSMBHPZYiy0kuiaFoyansR5QnTwDux1EjK3kD5iWpRVyJIJ0RAIV39SThafchuW59vra7F8mdy5Hfgw==", "signatures": [{"sig": "MEYCIQDe6tm+SgKR294AktccbM1Yjzb92UtlAgbQb16xx3S8tAIhAOM/itEOEXoC/OGCz4EXQPoD+jtklOHBsy7DmqbNTkEZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15752}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@jest/types": "^29.6.3", "@jest/console": "^29.6.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.6.3", "jest-haste-map": "^29.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.6.3_1692621581669_0.018054995449025224", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/test-result", "version": "29.6.4", "license": "MIT", "_id": "@jest/test-result@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "adf5c79f6e1fb7405ad13d67d9e2b6ff54b54c6b", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.6.4.tgz", "fileCount": 7, "integrity": "sha512-uQ1C0AUEN90/dsyEirgMLlouROgSY+Wc/JanVVk0OiUKa5UFh7sJpMEM3aoUBAz2BRNvUJ8j3d294WFuRxSyOQ==", "signatures": [{"sig": "MEUCIAOU1yG4sH9YomdZ29ynhKHEuskwUjMv2sBgPXhOmSurAiEA+6qChCFmaSpN0SAKO4g/0n6Cid9vBiDQxHujmEIS3Yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15752}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"@jest/types": "^29.6.3", "@jest/console": "^29.6.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.6.4", "jest-haste-map": "^29.6.4"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.6.4_1692875457027_0.3971794883429456", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/test-result", "version": "29.7.0", "license": "MIT", "_id": "@jest/test-result@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8db9a80aa1a097bb2262572686734baed9b1657c", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz", "fileCount": 7, "integrity": "sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==", "signatures": [{"sig": "MEYCIQCzCs5y+Dh/DKgvogOB38VNU67C6Mexk6daXUZXUtlcWAIhAIcdaKC2p0it084IkKplxMFiG6BaQtEoCrj6tttCJ/HI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15752}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@jest/types": "^29.6.3", "@jest/console": "^29.7.0", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "^29.7.0", "jest-haste-map": "^29.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_29.7.0_1694501030798_0.89507234069125", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/test-result", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e53223ae5db6e37635240c47957f886f5ce8e088", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-nS9jcfbFVbJzwqDDrruqjYlptUG6iJ1uD3UVHzQajX3VpGpDQjkj53s7M4l7W+iUD3GqgWow3n5bnQcRF3bnWQ==", "signatures": [{"sig": "MEQCIGeekmhlw7GUUJUVOGOPuwzvPRbNcvzKbKTI0HlK715zAiBfJHqhmqpEj+2OjaPAFH8rIFY4ZZ61PlErv6ibKTij8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18043}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@jest/types": "30.0.0-alpha.1", "@jest/console": "30.0.0-alpha.1", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.1", "jest-haste-map": "30.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.1_1698672800269_0.8551685032647967", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/test-result", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "151d5218abaa192bb31c0fd3bd3ee4919e368440", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-m1pUfckAd6lwB56h92C9hFj8znl88Z1P+Pv4cqj6RMm4L5I0t0dsDKUSj+MYN3jhzW84VLhvwLOLkV4qXG6Tdg==", "signatures": [{"sig": "MEQCIH51NOlwMt8H4IizoNCXTzdwkMMeHUg4/Hn70hHp/orkAiBBEqown30zhxwZk+L+snqtCfhzKVD8VbEqEflM6Wg+SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18363}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@jest/types": "30.0.0-alpha.2", "@jest/console": "30.0.0-alpha.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.2", "jest-haste-map": "30.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.2_1700126918275_0.675699752170341", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/test-result", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "76c951601de1194a1d5afa4ca8412ea1aba9a5f5", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-ZBE3+LAqQ/498FJPqPMJOEaxi0lHTSSxhXYR8LC50WKJZYT2GlR7irPMfHm8pb5m3LgVfqPfgJ9mtm4uwWJgLQ==", "signatures": [{"sig": "MEUCIAjow9rBngd96DVLktBr56FiITZkzCSRs00mwDO7O6zwAiEAy4w4g4GlmA0Hn4ETHHRifgf4Yt3T55lNaOqKD82+dNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18363}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@jest/types": "30.0.0-alpha.3", "@jest/console": "30.0.0-alpha.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.3", "jest-haste-map": "30.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.3_1708427361755_0.2076398789763445", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/test-result", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f82f2e5e5db293326c621e859f7f70ac31690383", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-8b0gjk+95jtqhU+GyEtNCTLPeN1Y5IIGUtPQwb0ep+VAdvpzmtGRvNqQwmxf8Dl6iy5Fz7PqH27Fe2YkDbOzOw==", "signatures": [{"sig": "MEYCIQD2Y2q9ZiSH/c8TA/ydsqQDuOhGbcW5GUjVZJFIcN7kVgIhAJZwLMSbrQbTBeOmcB+JnNbtrHLDJ8aedGH+05F0bHFn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18407}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@jest/types": "30.0.0-alpha.4", "@jest/console": "30.0.0-alpha.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.4", "jest-haste-map": "30.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.4_1715550217083_0.34829217521954003", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/test-result", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "141d004436a68980014d2dc0950abe82eb57300d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-aI9sw/JkUb1BnnH4JWX7vsFRP9pXrN8Yjm+ZLXdjtejY7z/oBPz6imx6+lRt/H4u/OhvqrnaA46cQmYNh2977w==", "signatures": [{"sig": "MEUCIEbiTPCENyc5MgdOL932b0Chv/4zxq7GjTqR93Foxz5EAiEAvP40uiBnbVsMYzzOPiQ0FzNKeW/g9M4KoswHMx0e11Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18407}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@jest/types": "30.0.0-alpha.5", "@jest/console": "30.0.0-alpha.5", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.5", "jest-haste-map": "30.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.5_1717073056989_0.15441738851126208", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/test-result", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "2c125fb4ff966a797f0a75feaa4c7c0f7be6080e", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-Jlg8lCm7VQ6YvQ0eZx2nQEtej/ng+ulV8cXH7Nj5i33hNZq8EZvWM4gQDWDzRe1X7cVE3Bd42On5f6s2rqqIjw==", "signatures": [{"sig": "MEQCID4b5o783A16g4Oi2FbSCvGkuNTjDSHzTINGOCDf1o4+AiBDzsxpzpMx/pOEym6CflBGvpTCxsUrWLv/FPA9eeP/cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18427}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@jest/types": "30.0.0-alpha.6", "@jest/console": "30.0.0-alpha.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.6", "jest-haste-map": "30.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.6_1723102992463_0.7533200939597449", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/test-result", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/test-result@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "b7e101f16bcaca96657867b67b96ef22dbb4929d", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-+GUXXq5LocMv3FXddg9MxXcApaX4Jno6qFAz+pL/m3pxTy7nGPQPuEUXZelEEQevMrLP5ds2PxLGd6OVoV565Q==", "signatures": [{"sig": "MEYCIQCAHDdJ9N+ucT4vKhjLjqDGtRskMVgtjpMvxx2ptW8o2gIhAJUPFPsl5bFnhS3GaCPlvfgZS6UlnCvmpM2Wnyt9yAxw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18428}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"@jest/types": "30.0.0-alpha.7", "@jest/console": "30.0.0-alpha.7", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-alpha.7", "jest-haste-map": "30.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-alpha.7_1738225720699_0.31715800254896087", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/test-result", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a617d752c637038ff6b84ba1d7ad3dd305aac0c1", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-vuOvhMfXrBkx0J5M9eB9r6VQ7wIwqOd2npDVbAcC5Ob9/+ReNEwK9ttMCnTMZEGpznuVXX7kBckFZ2M0BvX+kA==", "signatures": [{"sig": "MEQCIHgbknCP+lxd/mQStUbMKAFi0cBoW6XZGK2DEmcB69jmAiBtdVQjOTu7VbFLwvRPXZpfbvzjineX2Cc0y+d+s1fdpw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18422}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/types": "30.0.0-beta.1", "@jest/console": "30.0.0-beta.2", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.2", "jest-haste-map": "30.0.0-beta.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.2_1748309005426_0.9790593328912762", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/test-result", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "fb476c1546993c48b1705afe0c480f9f23409405", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-tTsVCp0UQw/mrEaGzlml5whyjyZBFTtCmq9wvHSu8lyQo25vlfKb+rUb1GA9cwNgld623moHGkLjzXmYgn9fYw==", "signatures": [{"sig": "MEQCIDoBH6UihyKk+nBB8utGHyALQkuMrol56iJ7xMBMK0pAAiAQvfTg+JZOdVR9JlEl+cxVLg8L7gFqh5QDEKOIIUJEjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18422}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/types": "30.0.0-beta.3", "@jest/console": "30.0.0-beta.3", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.3", "jest-haste-map": "30.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.3_1748309275358_0.6419554883398442", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/test-result", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "6ae29953e04fd7b8da43897112f78fd06b5cdd30", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-AhUj+wje5rXfkuodhBns6e1K4W4YiQhUwhXKpwyUzYUQ/ybSpZbBwS7cEMf9klC6+AHfnTchbL8RXHVcjMYd6Q==", "signatures": [{"sig": "MEYCIQCJOhoJoaGTJHdlMZ/absNyXJcrAnpIdCGPmn19RzqCIAIhAKxfmjUh5Cs/Vu4KDzuHVMbOq5lJms6TXRw+xqIWqiMn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18381}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/types": "30.0.0-beta.3", "@jest/console": "30.0.0-beta.4", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.4", "jest-haste-map": "30.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.4_1748329471980_0.463950367454111", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/test-result", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "3909c0210d70481702b1982dc5867180446922c2", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-zXn0GPpdJGVI5elhqSL2KTtbli7KL4048bgeQmmMBb+PsPOJWx6gxsJAowXl6sbYLp7GOemeZprOf0C6gBqXjA==", "signatures": [{"sig": "MEUCIFEyV1WLTShITIivckmDv+TQ19SLW6ecb8VG0kP+3+/6AiEAqHhaCAGWcLp13P11LV1V0NRdlCRdn8ayD1ohZAA8EWk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18381}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/types": "30.0.0-beta.3", "@jest/console": "30.0.0-beta.5", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.5", "jest-haste-map": "30.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.5_1748478615562_0.013036634634377542", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/test-result", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2054b540da68d77f5f50bbab3ab074b8614b4be1", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-XfWyFhHM9xKTNaNzSLussgxL4ioSqBvq1T0jaIo0e8K/4qrXgyaEEwbMGzeQ+q90UwLet7e/GtSXb4yY/V99BQ==", "signatures": [{"sig": "MEYCIQCE5T4SCr2DabW0qLsQSG48rGOZdAJiVerkTl3s1K5hzgIhAPVNzshklEdIP3IT3A57d63NtZHrDC42aQEgYAjDOxYy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18392}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0-beta.6", "@jest/console": "30.0.0-beta.6", "collect-v8-coverage": "^1.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.6", "jest-haste-map": "30.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.6_1748994657112_0.5808540061949758", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/test-result", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c9de2808452532bcf604c43f8458cac0e8f036e2", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-ejjhXm6Vneypm5doO1HD3jqxVUhK/XR6zZIvt/TxM8QIXw/oxlMsnzpaDkf6huObbnfz8mIhDJztxatvpfFElg==", "signatures": [{"sig": "MEQCIGG03l3DAcM3OTx9GqFQnhii9lBVUnBt3DxzPcthBw/jAiBee3lOrSL49j3qo1VfXv8wnHTIAiX7uPmAA3D69+1n2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18392}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0-beta.7", "@jest/console": "30.0.0-beta.7", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.7", "jest-haste-map": "30.0.0-beta.7"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.7_1749008149608_0.03675165842674666", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/test-result", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "dd45b2f9074de7d735f6b581abc3f97250950e0f", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-Q2k4/FKMf3i0XyJ+7jjuXeLtTcHTde+TahRUGKr2Z8Y0gKDCixx4rHc5d7YG66wIUar/zh5QUK669WP5s+WoHw==", "signatures": [{"sig": "MEUCIDMwi0KeunTiokb9xwb3upv4vmSOCyALpCnyEzIibo83AiEA9NKUPb7Aro8RPwgw/K14cHHKAQt9MCfk4NYvA552Bpk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18392}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0-beta.8", "@jest/console": "30.0.0-beta.8", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.8", "jest-haste-map": "30.0.0-beta.8"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.8_1749023597552_0.7213606251258637", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.9": {"name": "@jest/test-result", "version": "30.0.0-beta.9", "license": "MIT", "_id": "@jest/test-result@30.0.0-beta.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "53e09478d20dc65320068998a9c1815951690d6a", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-beta.9.tgz", "fileCount": 5, "integrity": "sha512-p7Nacz5mxd+1xJfKcOVekgMiuxhKydOKIanQiYA0xUeDQmCCPd4AV8fUSH4S+b501syv26fTvv0IQ56e/hYRAA==", "signatures": [{"sig": "MEUCIEwscOVumIapnAYDMiE8vJMk3dkzql1mGuoK9sf4ksa1AiEA4q1j10UT48uGjnJb4W7wLJMAXEjmJ+bZwxzyxWCCh2s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18392}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2f52a9ed429fb8797a99868860430d55db6d5503", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0-beta.8", "@jest/console": "30.0.0-beta.8", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-beta.9", "jest-haste-map": "30.0.0-beta.8"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-beta.9_1749109234466_0.9371180220321991", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/test-result", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/test-result@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "362f79a8ddb3de420b38a167ef1e625f961a78a6", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-NMFbiw2O1cKQROlLITPZjgCbQxi/YOyA777bPgoQGdDh/Fjt6JOhYlP7qOBlv/nRx5tLqPSdoMI8sr8hWml+5A==", "signatures": [{"sig": "MEQCIDCQxqSKNJWVTT17KNv7Um/8r6miwOVxU5N/j/DxsejGAiBQe7owbciseS00ntYTxJSyR+MO+JShsIUJmDAe3Uz/Rw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18384}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0-beta.8", "@jest/console": "30.0.0-rc.1", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0-rc.1", "jest-haste-map": "30.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0-rc.1_1749430973583_0.7303712282526311", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/test-result", "version": "30.0.0", "license": "MIT", "_id": "@jest/test-result@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "9a06e3b0f2024ace56a2989075c2c8938aae5297", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-685zco9HdgBaaWiB9T4xjLtBuN0Q795wgaQPpmuAeZPHwHZSoKFAUnozUtU+ongfi4l5VCz8AclOE5LAQdyjxQ==", "signatures": [{"sig": "MEQCIBklCTaYLfEw3E2tFC26Gj9ofq/eykW8h974iNDhxCXqAiBWcbYY4aTHeZQFPf6UTwAdxVT8IwPVsqbCy0EAMtoglQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18357}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/types": "30.0.0", "@jest/console": "30.0.0", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.0", "jest-haste-map": "30.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.0_1749521759368_0.1639287888573835", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/test-result", "version": "30.0.1", "license": "MIT", "_id": "@jest/test-result@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "89262d054b51c591ac0834f887233ba443876eca", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-VpPEdwN+NivPsExCb9FCcIfIIP4x6vzGg4xfaH0URYkZcJixwe2E69uRqp9MPq6A4mWUoQRtjPNocFA/kRoiFg==", "signatures": [{"sig": "MEQCIA1QXtA2TdhA9c1u19woB/au/lP8vbFU4mJW6qXtGNSjAiA/tbATQEk5PORV7plRFiNNr/dcyOwuU6O7jqytHf+YXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18357}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"@jest/types": "30.0.1", "@jest/console": "30.0.1", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.1", "jest-haste-map": "30.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.1_1750285896580_0.45980601650041963", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/test-result", "version": "30.0.2", "license": "MIT", "_id": "@jest/test-result@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "786849e33da6060381c508986fa7309ff855a367", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.2.tgz", "fileCount": 5, "integrity": "sha512-KKMuBKkkZYP/GfHMhI+cH2/P3+taMZS3qnqqiPC1UXZTJskkCS+YU/ILCtw5anw1+YsTulDHFpDo70mmCedW8w==", "signatures": [{"sig": "MEUCIQCZwOC3aslZSDTNMTI0FA8yY9F7kMleNM3gO2TGWAkgOwIgWshsHN79Nkk+OlbhJcyPH8367YlVBT1a1h9GIx1mnNw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18357}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-test-result"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"@jest/types": "30.0.1", "@jest/console": "30.0.2", "collect-v8-coverage": "^1.0.2", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-resolve": "30.0.2", "jest-haste-map": "30.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-result_30.0.2_1750329986578_0.6141243954300672", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "@jest/test-result", "version": "30.0.4", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-test-result"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/console": "30.0.4", "@jest/types": "30.0.1", "@types/istanbul-lib-coverage": "^2.0.6", "collect-v8-coverage": "^1.0.2"}, "devDependencies": {"jest-haste-map": "30.0.2", "jest-resolve": "30.0.2"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_nodeVersion": "24.3.0", "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "_id": "@jest/test-result@30.0.4", "dist": {"integrity": "sha512-Mfpv8kjyKTHqsuu9YugB6z1gcdB3TSSOaKlehtVaiNlClMkEHY+5ZqCY2CrEE3ntpBMlstX/ShDAf84HKWsyIw==", "shasum": "0b1c4e8256e3f9ebb9452ede22d4b04b31ea54fe", "tarball": "https://registry.npmjs.org/@jest/test-result/-/test-result-30.0.4.tgz", "fileCount": 6, "unpackedSize": 24423, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHDIuHbdqO4DWQc8sk4byGt5ZcX2HUTT3aRLrQK3di3+AiBIK4G0XyIASQiHpucbj9khtJvbS7066t5Yr5E2AZPe3A=="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/test-result_30.0.4_1751499947738_0.5351593141788036"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T14:47:02.595Z", "modified": "2025-07-02T23:45:48.142Z", "24.2.0-alpha.0": "2019-03-05T14:47:03.006Z", "24.3.0": "2019-03-07T12:59:47.055Z", "24.5.0": "2019-03-12T16:36:26.831Z", "24.6.0": "2019-04-01T22:26:22.633Z", "24.7.0": "2019-04-03T03:55:17.804Z", "24.7.1": "2019-04-04T01:18:46.865Z", "24.8.0": "2019-05-05T02:02:21.842Z", "24.9.0": "2019-08-16T05:56:00.278Z", "25.0.0": "2019-08-22T03:24:00.655Z", "25.1.0": "2020-01-22T01:00:07.789Z", "25.2.0-alpha.86": "2020-03-25T17:16:38.313Z", "25.2.0": "2020-03-25T17:58:30.662Z", "25.2.1-alpha.1": "2020-03-26T07:54:33.317Z", "25.2.1-alpha.2": "2020-03-26T08:10:40.158Z", "25.2.1": "2020-03-26T09:01:25.936Z", "25.2.3": "2020-03-26T20:25:03.288Z", "25.2.4": "2020-03-29T19:38:36.710Z", "25.2.6": "2020-04-02T10:29:24.770Z", "25.3.0": "2020-04-08T13:21:22.439Z", "25.4.0": "2020-04-19T21:50:32.842Z", "25.5.0": "2020-04-28T19:45:33.280Z", "26.0.0-alpha.0": "2020-05-02T12:13:13.935Z", "26.0.0-alpha.1": "2020-05-03T18:48:13.461Z", "26.0.0-alpha.2": "2020-05-04T16:05:46.077Z", "26.0.0": "2020-05-04T17:53:24.689Z", "26.0.1-alpha.0": "2020-05-04T22:16:12.358Z", "26.0.1": "2020-05-05T10:41:00.021Z", "26.1.0": "2020-06-23T15:15:25.873Z", "26.2.0": "2020-07-30T10:11:55.376Z", "26.3.0": "2020-08-10T11:32:02.073Z", "26.5.0": "2020-10-05T09:28:29.122Z", "26.5.2": "2020-10-06T10:52:53.969Z", "26.6.0": "2020-10-19T11:58:50.919Z", "26.6.1": "2020-10-23T09:06:28.748Z", "26.6.2": "2020-11-02T12:51:50.650Z", "27.0.0-next.0": "2020-12-05T17:25:37.650Z", "27.0.0-next.1": "2020-12-07T12:43:37.135Z", "27.0.0-next.3": "2021-02-18T22:10:09.945Z", "27.0.0-next.5": "2021-03-15T13:03:35.890Z", "27.0.0-next.6": "2021-03-25T19:40:13.968Z", "27.0.0-next.7": "2021-04-02T13:48:07.045Z", "27.0.0-next.8": "2021-04-12T22:42:40.291Z", "27.0.0-next.9": "2021-05-04T06:25:19.716Z", "27.0.0-next.10": "2021-05-20T14:11:31.282Z", "27.0.0-next.11": "2021-05-20T22:28:53.060Z", "27.0.0": "2021-05-25T08:15:22.234Z", "27.0.1": "2021-05-25T10:06:43.686Z", "27.0.2": "2021-05-29T12:07:33.074Z", "27.0.6": "2021-06-28T17:05:51.424Z", "27.1.0": "2021-08-27T09:59:47.012Z", "27.1.1": "2021-09-08T10:12:22.037Z", "27.2.0": "2021-09-13T08:06:53.045Z", "27.2.2": "2021-09-25T13:35:11.762Z", "27.2.3": "2021-09-28T10:11:28.028Z", "27.2.4": "2021-09-29T14:04:55.028Z", "27.2.5": "2021-10-08T13:39:27.563Z", "27.3.0": "2021-10-17T18:34:49.859Z", "27.3.1": "2021-10-19T06:57:35.365Z", "27.4.0": "2021-11-29T13:37:33.708Z", "27.4.1": "2021-11-30T08:37:18.750Z", "27.4.2": "2021-11-30T11:53:54.445Z", "27.4.6": "2022-01-04T23:03:44.599Z", "27.5.0": "2022-02-05T09:59:27.936Z", "27.5.1": "2022-02-08T10:52:27.266Z", "28.0.0-alpha.0": "2022-02-10T18:17:40.706Z", "28.0.0-alpha.1": "2022-02-15T21:27:05.473Z", "28.0.0-alpha.2": "2022-02-16T18:12:15.510Z", "28.0.0-alpha.3": "2022-02-17T15:42:25.493Z", "28.0.0-alpha.4": "2022-02-22T12:13:57.898Z", "28.0.0-alpha.5": "2022-02-24T20:57:23.053Z", "28.0.0-alpha.6": "2022-03-01T08:32:27.816Z", "28.0.0-alpha.7": "2022-03-06T10:02:43.963Z", "28.0.0-alpha.8": "2022-04-05T14:59:58.549Z", "28.0.0-alpha.9": "2022-04-19T10:59:17.931Z", "28.0.0": "2022-04-25T12:08:13.142Z", "28.0.1": "2022-04-26T10:02:42.991Z", "28.0.2": "2022-04-27T07:44:05.903Z", "28.1.0": "2022-05-06T10:48:57.146Z", "28.1.1": "2022-06-07T06:09:39.235Z", "28.1.3": "2022-07-13T14:12:31.857Z", "29.0.0-alpha.0": "2022-07-17T22:07:10.598Z", "29.0.0-alpha.1": "2022-08-04T08:23:32.678Z", "29.0.0-alpha.3": "2022-08-07T13:41:38.658Z", "29.0.0-alpha.4": "2022-08-08T13:05:37.204Z", "29.0.0-alpha.6": "2022-08-19T13:57:51.836Z", "29.0.0": "2022-08-25T12:33:30.770Z", "29.0.1": "2022-08-26T13:34:44.440Z", "29.0.2": "2022-09-03T10:48:22.193Z", "29.0.3": "2022-09-10T14:41:41.174Z", "29.1.0": "2022-09-28T07:37:44.460Z", "29.1.2": "2022-09-30T07:22:51.537Z", "29.2.0": "2022-10-14T09:13:53.591Z", "29.2.1": "2022-10-18T16:00:16.364Z", "29.3.1": "2022-11-08T22:56:25.765Z", "29.4.0": "2023-01-24T10:56:00.722Z", "29.4.1": "2023-01-26T15:08:42.544Z", "29.4.2": "2023-02-07T13:45:42.280Z", "29.4.3": "2023-02-15T11:57:31.253Z", "29.5.0": "2023-03-06T13:33:39.455Z", "29.6.0": "2023-07-04T15:25:53.162Z", "29.6.1": "2023-07-06T14:18:33.889Z", "29.6.2": "2023-07-27T09:21:38.004Z", "29.6.3": "2023-08-21T12:39:41.837Z", "29.6.4": "2023-08-24T11:10:57.315Z", "29.7.0": "2023-09-12T06:43:50.942Z", "30.0.0-alpha.1": "2023-10-30T13:33:20.437Z", "30.0.0-alpha.2": "2023-11-16T09:28:38.428Z", "30.0.0-alpha.3": "2024-02-20T11:09:21.913Z", "30.0.0-alpha.4": "2024-05-12T21:43:37.235Z", "30.0.0-alpha.5": "2024-05-30T12:44:17.133Z", "30.0.0-alpha.6": "2024-08-08T07:43:12.615Z", "30.0.0-alpha.7": "2025-01-30T08:28:41.102Z", "30.0.0-beta.2": "2025-05-27T01:23:25.586Z", "30.0.0-beta.3": "2025-05-27T01:27:55.536Z", "30.0.0-beta.4": "2025-05-27T07:04:32.182Z", "30.0.0-beta.5": "2025-05-29T00:30:15.740Z", "30.0.0-beta.6": "2025-06-03T23:50:57.313Z", "30.0.0-beta.7": "2025-06-04T03:35:49.782Z", "30.0.0-beta.8": "2025-06-04T07:53:17.721Z", "30.0.0-beta.9": "2025-06-05T07:40:34.633Z", "30.0.0-rc.1": "2025-06-09T01:02:53.785Z", "30.0.0": "2025-06-10T02:15:59.565Z", "30.0.1": "2025-06-18T22:31:36.751Z", "30.0.2": "2025-06-19T10:46:26.746Z", "30.0.4": "2025-07-02T23:45:47.910Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-test-result"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}