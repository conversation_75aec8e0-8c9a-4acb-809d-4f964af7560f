{"_id": "once", "_rev": "66-24622b86e5ff3cacb541ddb2060a6d09", "name": "once", "description": "Run a function exactly one time", "dist-tags": {"latest": "1.4.0"}, "versions": {"1.1.1": {"name": "once", "version": "1.1.1", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "_id": "once@1.1.1", "dist": {"shasum": "9db574933ccb08c3a7614d154032c09ea6f339e7", "tarball": "https://registry.npmjs.org/once/-/once-1.1.1.tgz", "integrity": "sha512-frdJr++QKEg4+JylTX+NNLgSoO6M2pDNYOOXe4WGIYKKBADBI9nU3oa06y4D4FpAJ3obAsjExeBOnscYJB9Blw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCieyV970jTTntKimuhh/o7Az1lX0tdw5DuM56P5qFKUwIgXLJvOJdZVbbHBrPyEPESrpG4Goe/BNQ5+1B4SLk+pbk="}]}, "_npmVersion": "1.1.48", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.2.0": {"name": "once", "version": "1.2.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "_id": "once@1.2.0", "dist": {"shasum": "de1905c636af874a8fba862d9aabddd1f920461c", "tarball": "https://registry.npmjs.org/once/-/once-1.2.0.tgz", "integrity": "sha512-WBd9yDi3JRrEsysh0s4px+jinLuW/DGRydS+ZGPTHVKu4JrIBmKj3uDC9LfnwEbXHFVLieUuZvunY74wln6arg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOBig6W3FyMp9sh0K4QLICwV9qESYWylncRMBchsK9YAiA2QpvinLj0PKj4dclj/TyLxNF0uGp4Z8gWiG8xDWgQNQ=="}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.3.0": {"name": "once", "version": "1.3.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "_id": "once@1.3.0", "dist": {"shasum": "151af86bfc1f08c4b9f07d06ab250ffcbeb56581", "tarball": "https://registry.npmjs.org/once/-/once-1.3.0.tgz", "integrity": "sha512-A31oqbdEQnnhkjIXJ6QKcgO9eN8Xe+dVAQqlFLAmri0Y5s11pUadCihT2popU2WLd5CbbnD2ZVkbEJsR/8JHvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8RXfVUBPi+ZxXJoQ8mgRZBFokEux5o51yaiVmFl8NmgIhAIh5XtdYRWuYFWt8HGLWQuxA/zNZD+qewa7atQMU6F9o"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.3.1": {"name": "once", "version": "1.3.1", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BSD", "gitHead": "c90ac02a74f433ce47f6938869e68dd6196ffc2c", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once", "_id": "once@1.3.1", "_shasum": "f3f3e4da5b7d27b5c732969ee3e67e729457b31f", "_from": ".", "_npmVersion": "2.0.0", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f3f3e4da5b7d27b5c732969ee3e67e729457b31f", "tarball": "https://registry.npmjs.org/once/-/once-1.3.1.tgz", "integrity": "sha512-NzfbaaoQvz2JC/D/Yj3GZi0FJG1w9i3K9Bp99Ws3p0xriPynC/YfRcpo2zoVuIduvH4b8+6up4ogGxnqajSKhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjEN6vmHO9gyWeVXNZBqftltDNnhMMes7qlk1utWC9tAiAIA5xUJDAr9juUnlIDgNi8ynVRYjOBLFx8BMOjvra2mQ=="}]}}, "1.3.2": {"name": "once", "version": "1.3.2", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "~0.3.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e35eed5a7867574e2bf2260a1ba23970958b22f2", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.3.2", "_shasum": "d8feeca93b039ec1dcdee7741c92bdac5e28081b", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "2.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "d8feeca93b039ec1dcdee7741c92bdac5e28081b", "tarball": "https://registry.npmjs.org/once/-/once-1.3.2.tgz", "integrity": "sha512-tPQxpk4nBjTgu+eHijWhgX2d+tE6HQyMPVnzY5b1qenTUFsxBaKlzEFUF+XVfbToFuVFm8hX+PzV9u3PewDZ4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7IuyMTX7DKRwCdMuhBqt7Y2ievtgM3ro3BtNeG8MH/AiA2VLezEPQWs3rkaSNsZ7Q6Llli+kcR4pOg+EcIKlvMhQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.3.3": {"name": "once", "version": "1.3.3", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2ad558657e17fafd24803217ba854762842e4178", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.3.3", "_shasum": "b2e261557ce4c314ec8304f3fa82663e4297ca20", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b2e261557ce4c314ec8304f3fa82663e4297ca20", "tarball": "https://registry.npmjs.org/once/-/once-1.3.3.tgz", "integrity": "sha512-6vaNInhu+CHxtONf3zw3vq4SP2DOQhjBvIa3rNcG0+P7eKWlYH6Peu7rHizSloRU2EwMz6GraLieis9Ac9+p1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFtJMdkUTYhzUE/ucuz0mZ/hhlHxaRAquKjl6xtrpaLOAiEAjs078yGtzBdWWy8vRBGJZ0gtaY9xvIY3abBMujoXl4E="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.4.0": {"name": "once", "version": "1.4.0", "description": "Run a function exactly one time", "main": "once.js", "directories": {"test": "test"}, "dependencies": {"wrappy": "1"}, "devDependencies": {"tap": "^7.0.1"}, "scripts": {"test": "tap test/*.js"}, "files": ["once.js"], "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "keywords": ["once", "function", "one", "single"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0e614d9f5a7e6f0305c625f6b581f6d80b33b8a6", "bugs": {"url": "https://github.com/isaacs/once/issues"}, "homepage": "https://github.com/isaacs/once#readme", "_id": "once@1.4.0", "_shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "583b1aa775961d4b113ac17d9c50baef9dd76bd1", "tarball": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBoG/U/tJpr0WZlTEuYKTiY+IHcJ8q7ltkuQPJlL9q4ZAiB6nAD2IwFhsf3OOMThg/wNPiKveaMutTIScY9fya/n1g=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/once-1.4.0.tgz_1473196269128_0.537820661207661"}}}, "readme": "# once\n\nOnly call a function once.\n\n## usage\n\n```javascript\nvar once = require('once')\n\nfunction load (file, cb) {\n  cb = once(cb)\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nOr add to the Function.prototype in a responsible way:\n\n```javascript\n// only has to be done once\nrequire('once').proto()\n\nfunction load (file, cb) {\n  cb = cb.once()\n  loader.load('file')\n  loader.once('load', cb)\n  loader.once('error', cb)\n}\n```\n\nIronically, the prototype feature makes this module twice as\ncomplicated as necessary.\n\nTo check whether you function has been called, use `fn.called`. Once the\nfunction is called for the first time the return value of the original\nfunction is saved in `fn.value` and subsequent calls will continue to\nreturn this value.\n\n```javascript\nvar once = require('once')\n\nfunction load (cb) {\n  cb = once(cb)\n  var stream = createStream()\n  stream.once('data', cb)\n  stream.once('end', function () {\n    if (!cb.called) cb(new Error('not found'))\n  })\n}\n```\n\n## `once.strict(func)`\n\nThrow an error if the function is called twice.\n\nSome functions are expected to be called only once. Using `once` for them would\npotentially hide logical errors.\n\nIn the example below, the `greet` function has to call the callback only once:\n\n```javascript\nfunction greet (name, cb) {\n  // return is missing from the if statement\n  // when no name is passed, the callback is called twice\n  if (!name) cb('Hello anonymous')\n  cb('Hello ' + name)\n}\n\nfunction log (msg) {\n  console.log(msg)\n}\n\n// this will print 'Hello anonymous' but the logical error will be missed\ngreet(null, once(msg))\n\n// once.strict will print 'Hello anonymous' and throw an error when the callback will be called the second time\ngreet(null, once.strict(msg))\n```\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:33:12.734Z", "created": "2012-08-14T07:25:56.929Z", "1.1.1": "2012-08-14T07:25:58.262Z", "1.2.0": "2013-08-12T02:55:21.962Z", "1.3.0": "2013-10-24T06:27:26.638Z", "1.3.1": "2014-09-18T23:05:04.887Z", "1.3.2": "2015-05-04T23:09:54.026Z", "1.3.3": "2015-11-20T21:45:14.765Z", "1.4.0": "2016-09-06T21:11:09.367Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "repository": {"type": "git", "url": "git://github.com/isaacs/once.git"}, "users": {"stdarg": true, "yi": true, "abg": true, "chengen": true, "novaleaf": true, "jamescostian": true, "amirmehmood": true, "schnittstabil": true, "donnicky": true, "oleaasen": true, "ergunozyurt": true, "gochomugo": true, "adamlu": true, "saru95": true, "kingtrocki": true, "fill": true, "sjnnr": true, "isik": true, "karthickt": true, "barenko": true, "vwal": true, "milfromoz": true, "quocnguyen": true, "coalesce": true, "loselovegirl": true, "shan": true, "mattmcfarland": true, "codebyren": true, "mojaray2k": true, "dzhou777": true, "garenyondem": true, "jpwilliams": true, "ash": true, "arefm": true, "seangenabe": true, "nichoth": true, "edwingeng": true, "rocket0191": true, "detj": true, "morogasper": true, "knksmith57": true, "flumpus-dev": true}, "homepage": "https://github.com/isaacs/once#readme", "keywords": ["once", "function", "one", "single"], "bugs": {"url": "https://github.com/isaacs/once/issues"}, "license": "ISC", "readmeFilename": "README.md"}